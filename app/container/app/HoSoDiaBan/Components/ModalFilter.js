import {dimensions} from '@app/theme';
import {ButtonLinear, DropdownPicker, Icon, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
const listTrangThai = [
  {label: 'Tất cả', value: ''},
  {
    label: 'Chưa nhận',
    value: 'C',
  },
  {
    label: 'Đang giám định',
    value: 'D',
  },
  {
    label: 'Đã kết thúc',
    value: 'K',
  },
];

const ModalFilterComponent = forwardRef((props, ref) => {
  const {
    listChiNhanh,
    trangThaiSelected,
    setTrangThaiSelected,
    resetFilter,
    onPressTimKiem,
    setNgayBatDau,
    ngayBatDau,
    ngayKetThuc,
    setNgayKetThuc,
    bienXe,
    setBienXe,
    soHoSo,
    setSoHoSo,
    setTenKH,
    tenKH,
    soHopDongGCN,
    setSoHopDongGCN,
    listChiNhanhSelected,
    setListChiNhanhSelected,
  } = props;

  useImperativeHandle(
    ref,
    () => ({
      show: showModal,
      hide: hideModal,
    }),
    [],
  );

  const [isVisible, setIsVisible] = useState(false);

  const [openChiNhanh, setOpenChiNhanh] = useState(false); // DROPDOWN CHI NHÁNH
  const [openTrangThai, setOpenTrangThai] = useState(false); // DROPDOWN Trạng thái

  const [toggleNgayBatDau, setToggleNgayBatDau] = useState(false);
  const [toggleNgayKetThuc, setToggleNgayKetThuc] = useState(false);

  const showModal = () => setIsVisible(true);
  const hideModal = () => setIsVisible(false);

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };
  const getDisplayTextChiNhanh = () => {
    if (listChiNhanhSelected.length === 1 && listChiNhanhSelected[0] === '') return 'Tất cả';
    let displayText = [];
    listChiNhanhSelected.forEach((itemChiNhanhSelected) => {
      listChiNhanh.forEach((itemChiNhanh) => {
        if (itemChiNhanh.ma === itemChiNhanhSelected && itemChiNhanhSelected !== '') displayText.push(itemChiNhanh.ten_hien_thi);
      });
    });
    return displayText.join(', ');
  };
  /* RENDER */
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime)}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
      //   is24Hour={true}
    />
  );
  return (
    <Modal isVisible={isVisible} style={styles.modal} onBackdropPress={hideModal} onBackButtonPress={hideModal}>
      <View style={{flex: 1, justifyContent: 'flex-end'}}>
        <View style={[styles.modalContent]}>
          <View style={styles.headerView}>
            <TouchableOpacity onPress={hideModal}>
              <Icon.AntDesign name="close" size={25} />
            </TouchableOpacity>
            <Text children="Tìm kiếm" style={styles.txtHeader} />
            <TouchableOpacity onPress={resetFilter}>
              <Icon.AntDesign name="reload1" size={25} />
            </TouchableOpacity>
          </View>
          <KeyboardAwareScrollView style={{paddingHorizontal: 10}}>
            <View style={{flexDirection: 'row'}}>
              <View style={{flex: 1, marginRight: 10}}>
                <TextInputOutlined
                  title="Ngày bắt đầu"
                  isTouchableOpacity={true}
                  onPress={() => setToggleNgayBatDau(true)}
                  value={moment(ngayBatDau).format('DD/MM/YYYY')}
                  editable={false}
                  containerStyle={{marginBottom: 0}}
                  isDateTimeField
                />
                {renderDateTimeComp(toggleNgayBatDau, setToggleNgayBatDau, setNgayBatDau, ngayBatDau, 'date', null, new Date(), 0)}
              </View>
              <View style={{flex: 1}}>
                <TextInputOutlined
                  title="Ngày kết thúc"
                  isTouchableOpacity={true}
                  onPress={() => setToggleNgayKetThuc(true)}
                  value={moment(ngayKetThuc).format('DD/MM/YYYY')}
                  editable={false}
                  containerStyle={{marginBottom: 0}}
                  isDateTimeField
                />
                {renderDateTimeComp(toggleNgayKetThuc, setToggleNgayKetThuc, setNgayKetThuc, ngayKetThuc, 'date', null, new Date(), 0)}
              </View>
            </View>
            <DropdownPicker
              title="Chi nhánh"
              zIndex={8000}
              searchable={true}
              isOpen={openChiNhanh}
              setOpen={setOpenChiNhanh}
              onOpen={() => openTrangThai && setOpenTrangThai(false)}
              items={listChiNhanh}
              itemSelected={listChiNhanhSelected}
              setItemSelected={setListChiNhanhSelected}
              maxHeight={dimensions.height / 2}
              containerStyle={{marginBottom: 0}}
              schema={{
                label: 'ten_hien_thi',
                value: 'ma',
              }}
              multiple
              multipleText={getDisplayTextChiNhanh(listChiNhanhSelected)}
              max={100}
            />
            <DropdownPicker
              title="Trạng thái hồ sơ"
              zIndex={7000}
              searchable={false}
              isOpen={openTrangThai}
              setOpen={setOpenTrangThai}
              items={listTrangThai}
              itemSelected={trangThaiSelected}
              setItemSelected={setTrangThaiSelected}
              containerStyle={{marginBottom: 0}}
              onOpen={() => openChiNhanh && setOpenChiNhanh(false)}
            />
            <TextInputOutlined title="Biển xe" placeholder="Biển xe" value={bienXe} onChangeText={setBienXe} containerStyle={{marginBottom: 0}} />
            <TextInputOutlined title="Số hồ sơ" placeholder="Số hồ sơ" value={soHoSo} onChangeText={setSoHoSo} containerStyle={{marginBottom: 0}} />
            <TextInputOutlined title="Số hợp đồng/GCN" placeholder="Số hợp đồng/GCN" value={soHopDongGCN} onChangeText={setSoHopDongGCN} containerStyle={{marginBottom: 0}} />
            <TextInputOutlined title="Tên khách hàng" placeholder="Tên khách hàng" value={tenKH} onChangeText={setTenKH} containerStyle={{marginBottom: 0}} />
            <ButtonLinear
              title="Tìm kiếm"
              onPress={() => {
                hideModal();
                onPressTimKiem();
              }}
              linearStyle={{marginVertical: 20}}
            />
          </KeyboardAwareScrollView>
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalContent: {
    // width: dimensions.width * 0.8,
    height: dimensions.height * 0.9,
    // flex: 1,
    backgroundColor: '#FFF',
    paddingTop: 10,
    borderRadius: 20,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    paddingHorizontal: 10,
    borderColor: 'gray',
  },
  txtHeader: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});
export const ModalFilter = memo(ModalFilterComponent, isEqual);
