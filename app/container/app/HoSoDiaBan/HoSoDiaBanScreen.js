import R from '@app/assets/R';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {BottomTabs, Empty, Icon, ProfileItem, ScreenComponent, Text} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, RefreshControl, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {connect} from 'react-redux';
import {ModalFilter} from './Components';
import styles from './HoSoDiaBanStyle';

const HoSoDiaBanScreenComponent = (props) => {
  console.log('HoSoDiaBanScreenComponent');
  const {chiNhanhBaoHiem} = props;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // DROPDOWN CHI NHÁNH

  const [listChiNhanhSelected, setListChiNhanhSelected] = useState(['']);
  const [listChiNhanh, setListChiNhanh] = useState([{ten_tat: 'Tất cả', ma: ''}]);

  const [trangThaiSelected, setTrangThaiSelected] = useState('');
  const [ngayBatDau, setNgayBatDau] = useState(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  const [ngayKetThuc, setNgayKetThuc] = useState(new Date());
  const [bienXe, setBienXe] = useState('');
  const [soHoSo, setSoHoSo] = useState('');
  const [soHopDongGCN, setSoHopDongGCN] = useState('');
  const [tenKH, setTenKH] = useState('');

  const [listHoSo, setListHoSo] = useState([]);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(20);

  let refModalFilter = useRef(null);

  useEffect(() => {
    let initListChiNhanh = [{ten_tat: 'Tất cả', ma: ''}];
    initListChiNhanh = initListChiNhanh.concat(chiNhanhBaoHiem);
    let parseArr = JSON.parse(JSON.stringify(initListChiNhanh));
    parseArr.map((e) => {
      if (e.ma !== '') e.ten_hien_thi = e.ten_tat + ' - ' + e.ten;
      else e.ten_hien_thi = e.ten_tat;
    });
    setListChiNhanh([...parseArr]);
    layDanhSachHoSo();
  }, []);

  useEffect(() => {
    let chiNhanhLast = listChiNhanhSelected[listChiNhanhSelected.length - 1];
    if (chiNhanhLast === '' && listChiNhanhSelected.length > 1) setListChiNhanhSelected(['']);
    else if (listChiNhanhSelected[0] === '' && listChiNhanhSelected.length > 1) {
      let listChiNhanhSelectedTmp = listChiNhanhSelected;
      listChiNhanhSelectedTmp = listChiNhanhSelectedTmp.slice(1, listChiNhanhSelectedTmp.length);
      setListChiNhanhSelected(listChiNhanhSelectedTmp);
    }
  }, [listChiNhanhSelected]);

  const resetFilter = () => {
    setListChiNhanhSelected(['']);
    setTrangThaiSelected('');
    setNgayBatDau(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
    setNgayKetThuc(new Date());
    setBienXe('');
    setSoHoSo('');
  };
  const onRefresh = () => {
    // setRefreshing(true);
    // let type = route.params.profileTitle == profileTile[0] ? DATA_CONSTANT.PROFILE_NOT_RECEIVED_YET : DATA_CONSTANT.PROFILE_RECEIVED;
    layDanhSachHoSo([]);
    setCurrent(1);
  };

  const layDanhSachHoSo = async (oldData = [], trang = 1, so_dong = 20) => {
    setDialogLoading(true);
    let params = {
      ngay_d: moment(ngayBatDau, 'DD/MM/YYYY').format('YYYYMMDD'),
      ngay_c: moment(ngayKetThuc, 'DD/MM/YYYY').format('YYYYMMDD'),
      bien_xe: bienXe,
      so_hs: soHoSo,
      ma_chi_nhanh_ql: listChiNhanhSelected,
      trang: trang,
      so_dong: so_dong,
      so_hd: soHopDongGCN,
      ten_kh: tenKH,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LIST_HO_SO_THEO_PHAN_QUYEN_QUAN_LY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setTotal(response.data_info.data.tong_so_dong);
      if (response.data_info.data.length > 0) {
        let listHoSoTmp = [...oldData, ...response.data_info.data];
        setListHoSo(listHoSoTmp);
      } else FlashMessageHelper.showFlashMessage('Thông báo', 'Không tìm thấy hồ sơ', 'warning');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setCurrent(current + 1);
      layDanhSachHoSo(listHoSo, current + 1);
    }
  };

  const onPressItem = (item) => {
    if (item.nghiep_vu === 'XE_MAY') {
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY, {
        profileDetail: item,
        prevScreen: SCREEN_ROUTER_APP.HO_SO_DIA_BAN,
      });
    } else {
      NavigationUtil.push(SCREEN_ROUTER_APP.PROFILE_ASSESSMENT, {
        profileDetail: item,
        prevScreen: SCREEN_ROUTER_APP.HO_SO_DIA_BAN,
      });
    }
  };
  /* RENDER */
  const renderProfileItem = (data, index) => {
    let item = data;
    return (
      <TouchableOpacity
        key={index}
        onPress={() =>
          NavigationUtil.push(SCREEN_ROUTER_APP.PROFILE_ASSESSMENT, {
            profileDetail: item,
            prevScreen: SCREEN_ROUTER_APP.HO_SO_DIA_BAN,
          })
        }>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            <Text style={styles.profileTxtHoSo}>{item.so_hs}</Text>
            <View style={styles.profileItemDetail}>
              <View style={styles.profileTimeView}>
                <Image source={R.images.img_clock} style={styles.profileImgClock} />
                <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.ngay_tb}</Text>
              </View>
              <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.doi_tuong}</Text>
            </View>
            <View style={{...styles.profileItemDetail, marginTop: 5}}>
              <View style={styles.profileTimeView}>
                <Icon.Entypo name="info-with-circle" size={15} style={{marginRight: 5, opacity: 0.6}} color={colors.BLUE3} />
                <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.trang_thai_ten}</Text>
              </View>
            </View>
            <View style={{...styles.profileItemDetail, marginTop: 5}}>
              <View style={styles.profileTimeView}>
                <Icon.Entypo name="info-with-circle" size={15} style={{marginRight: 5, opacity: 0.6}} color={colors.BLUE3} />
                <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.nv}</Text>
              </View>
            </View>
          </View>

          <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      header
      renderView={
        <View style={styles.container}>
          <View style={styles.filterView}>
            <Text children="Hồ sơ được phân quyền theo địa bàn" style={styles.titleHoSo} />
            <TouchableOpacity
              hitSlop={{
                bottom: 20,
                left: 20,
                right: 20,
                top: 20,
              }}
              onPress={() => refModalFilter.current.show()}>
              <Icon.FontAwesome name="search" size={25} color={'#5e77f8'} />
            </TouchableOpacity>
          </View>
          <Text children="CHÚ Ý: Tính năng này không dành cho giám định/bồi thường!" style={styles.textWarning} />
          {/* <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} showsVerticalScrollIndicator={true} onMomentumScrollEnd={(event) => handleLoadMore()}>
            <View marginBottom={70}>
              {listHoSo.map((item, index) => {
                return renderProfileItem(item, index);
              })}
            </View>
          </ScrollView> */}
          <FlatList
            scrollEnabled
            data={listHoSo}
            renderItem={(data) => <ProfileItem data={data} onPressItem={() => onPressItem(data.item)} dataType="HS_DIA_BAN" />}
            ListEmptyComponent={<Empty />}
            keyExtractor={(e, i) => i.toString()}
            refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={onRefresh} />}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
          />
          <ModalFilter
            ref={refModalFilter}
            listChiNhanh={listChiNhanh}
            trangThaiSelected={trangThaiSelected}
            setTrangThaiSelected={setTrangThaiSelected}
            resetFilter={resetFilter}
            onPressTimKiem={layDanhSachHoSo}
            ngayBatDau={ngayBatDau}
            setNgayBatDau={setNgayBatDau}
            ngayKetThuc={ngayKetThuc}
            setNgayKetThuc={setNgayKetThuc}
            bienXe={bienXe}
            setBienXe={setBienXe}
            soHoSo={soHoSo}
            setSoHoSo={setSoHoSo}
            setTenKH={setTenKH}
            tenKH={tenKH}
            soHopDongGCN={soHopDongGCN}
            setSoHopDongGCN={setSoHopDongGCN}
            listChiNhanhSelected={listChiNhanhSelected}
            setListChiNhanhSelected={setListChiNhanhSelected}
          />
          <BottomTabs indexActive={3} />
        </View>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  chiNhanhBaoHiem: state.categoryCommon.data.chiNhanhBaoHiem,
});
const mapDispatchToProps = {};
const HoSoDiaBanConnect = connect(mapStateToProps, mapDispatchToProps)(HoSoDiaBanScreenComponent);
export const HoSoDiaBanScreen = memo(HoSoDiaBanConnect, isEqual);
