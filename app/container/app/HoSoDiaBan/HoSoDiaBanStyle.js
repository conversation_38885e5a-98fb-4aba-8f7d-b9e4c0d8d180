import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  titleHoSo: {
    fontSize: 16,
    marginVertical: 3,
    color: colors.BLACK,
    fontWeight: 'bold',
    flex: 1,
  },
  filterView: {
    marginTop: 20,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: spacing.small,
  },
  profileItemView: {
    flexDirection: 'row',
    borderRadius: 10,
    borderWidth: 0.4,
    marginVertical: 5,
    borderColor: colors.GRAY,
    paddingLeft: spacing.small,
    paddingVertical: spacing.tiny,
    marginHorizontal: spacing.small,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: 5,
    paddingRight: 5,
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileTxtHoSo: {
    marginBottom: 4,
    color: colors.BLACK,
    fontSize: 14,
  },
  profileTxtThoiGian: (color) => {
    return {
      fontSize: 12,
      color: color,
      fontWeight: 'bold',
    };
  },
  profileImgClock: {
    marginRight: 5,
    width: 14,
    height: 14,
    opacity: 0.8,
  },
  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  textWarning: {
    color: colors.RED1,
    fontSize: FontSize.size14,
    marginHorizontal: scale(spacing.small),
  },
});
