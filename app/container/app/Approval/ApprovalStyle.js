import {colors} from '@app/commons/Theme';
import {FontSize, dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  contentView: {
    marginTop: spacing.tiny,
    flex: 1,
  },
  profileItemView: {},
  profileItemLinearView: {
    flexDirection: 'row',
    marginVertical: 5,
    paddingLeft: spacing.medium,
    paddingRight: spacing.smaller,
    borderRadius: 10,
    borderColor: colors.GRAY,
    borderWidth: 0.5,
    marginHorizontal: spacing.smaller,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: spacing.tiny,
    paddingRight: spacing.tiny,
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileTxtHoSo: {
    marginBottom: 4,
    color: colors.BLACK,
    fontSize: 14,
  },

  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  txtLoaiTrinh: {
    fontWeight: 'bold',
  },
  txtTrangThaiTrinh: {
    fontWeight: 'bold',
  },
  txtTrangThaiHoSo: {
    fontWeight: 'bold',
  },
  rightIcon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  renderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: vScale(spacing.small),
    paddingHorizontal: scale(spacing.small),
  },
  txtHeaderList: {
    fontWeight: '400',
    color: colors.BLACK_03,
  },
  date: {
    flex: 1,
    color: colors.PRIMARY,
    fontSize: FontSize.size16,
  },
});
