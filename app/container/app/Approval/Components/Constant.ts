import moment from 'moment';

export const startOfYear = moment().startOf('year').format('YYYYMMDD');
export const currentDate = moment().format('YYYYMMDD');

export const DATA_TRANG_THAI = [
  {label: 'Chọn trạng thái phê duyệt', value: ''},
  {label: 'Chờ duyệt', value: 'C'},
  {label: 'Đã duyệt', value: 'D'},
];

export const DATA_NGHIEP_VU = [
  {label: 'Chọn nghiệp vụ', value: ''},
  {label: 'Ô tô', value: 'XE'},
  {label: 'Xe máy', value: 'XE_MAY'},
  {label: 'Người', value: 'NG'},
  {label: 'Hồ sơ giám định', value: 'HSGD'},
  // {label: 'Thanh toán', value: 'THANH_TOAN'},
];
export const titleInput = ['<PERSON><PERSON><PERSON> đầu', '<PERSON><PERSON><PERSON> cuối', '<PERSON>i<PERSON><PERSON> số xe', '<PERSON><PERSON><PERSON><PERSON> chứng nhận', '<PERSON><PERSON><PERSON> khách hàng', '<PERSON><PERSON><PERSON>n thoại', 'Tr<PERSON><PERSON> thái hồ sơ'];

export const DATA_LOAI_TRINH = [
  //Xe Ô tô
  {ma: '', ten: 'Chọn loại trình', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_MO_SO', ten: 'Trình duyệt mở số hồ sơ', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_BAO_CAO_GD', ten: 'Trình duyệt báo cáo giám định', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_DUYET_GIA', ten: 'Trình duyệt duyệt giá', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_BAO_LANH', ten: 'Trình duyệt bảo lãnh', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_BOI_THUONG', ten: 'Trình duyệt bồi thường', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_TU_CHOI', ten: 'Trình duyệt từ chối bồi thường', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_TAM_UNG_BT', ten: 'Trình duyệt tạm ứng bồi thường', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_THU_HOI_VAT_TU', ten: 'Trình duyệt thu hồi vật tư', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_THU_DOI_NTBA', ten: 'Trình duyệt thu đòi người thứ 3', nv: 'XE'},
  {ma: 'XE_TRINH_DUYET_CHI_PHI_CUU_HO', ten: 'Trình duyệt chi phí cứu hộ, cẩu kéo', nv: 'XE'},
  {ma: 'TRINH_GDINH_BTH', ten: 'Trình giám định, bồi thường hộ', nv: 'XE'},
  //Xe máy
  {ma: '', ten: 'Chọn loại trình', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_MO_SO', ten: 'Trình duyệt mở số hồ sơ', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_BAO_CAO_GD', ten: 'Trình duyệt báo cáo giám định xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_DUYET_GIA', ten: 'Trình duyệt duyệt giá xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_BAO_LANH', ten: 'Trình duyệt bảo lãnh xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_BOI_THUONG', ten: 'Trình duyệt bồi thường xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_TU_CHOI', ten: 'Trình duyệt từ chối bồi thường xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_TAM_UNG_BT', ten: 'Trình duyệt tạm ứng bồi thường xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_THU_HOI_VAT_TU', ten: 'Trình duyệt thu hồi vật tư xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_THU_DOI_NTBA', ten: 'Trình duyệt thu đòi người thứ 3 xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_DUYET_CHI_PHI_CUU_HO', ten: 'Trình duyệt chi phí cứu hộ, cẩu kéo xe máy', nv: 'XE_MAY'},
  {ma: 'XE_MAY_TRINH_GDINH_BTH', ten: 'Trình giám định, bồi thường hộ xe máy', nv: 'XE_MAY'},
  //Con người
  {ma: '', ten: 'Chọn loại trình', nv: 'NG'},
  {ma: 'NG_TRINH_DUYET_DUYET_GIA', ten: 'Trình duyệt phương án', nv: 'NG'},
  {ma: 'NG_TRINH_DUYET_BAO_LANH', ten: 'Trình duyệt bảo lãnh', nv: 'NG'},
  {ma: 'NG_TRINH_DUYET_BOI_THUONG', ten: 'Trình duyệt bồi thường', nv: 'NG'},
  {ma: 'NG_TRINH_DUYET_TU_CHOI', ten: 'Trình từ chối bồi thường', nv: 'NG'},
];

export const DATA_DOI_TUONG_DUYET = [
  {ma: '', ten: 'Chọn loại đối tượng duyệt'},
  {ma: '1', ten: 'Phê duyệt chính'},
  {ma: '0', ten: 'Phối hợp / kiểm tra'},
];
