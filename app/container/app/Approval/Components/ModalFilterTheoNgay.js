import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {selectUser} from '@app/redux/slices/UserSlice';
import {dimensions, spacing} from '@app/theme';
import {ButtonLinear, DropdownPicker, ModalChiNhanhTheoDangCay, ModalSelectSimple, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {Keyboard, SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
import {DATA_DOI_TUONG_DUYET, DATA_LOAI_TRINH, DATA_NGHIEP_VU, DATA_TRANG_THAI, currentDate, startOfYear, titleInput} from './Constant';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';

const ModalFilterTheoNgayComponent = forwardRef((props, ref) => {
  const {onBackPress, onPressSearch, loading, chiNhanhBaoHiem, onResetForm} = props;
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const userInfo = useSelector(selectUser);
  const DATA_TRANG_THAI_DUYET = [
    {ma: '', ten: 'Chọn trạng thái duyệt'},
    {ma: userInfo?.nguoi_dung?.nsd, ten: 'Chờ bạn duyệt'},
    {ma: 'NGUOI_KHAC', ten: 'Chờ người khác duyệt'},
  ];

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const initFormInput = {
    ngayd: +startOfYear,
    ngayc: +currentDate,
    trang_thai: '',
    nv: '',
    nsd_duyet: '',
    ma_chi_nhanh: '',
    loai: '',
    so_hs: '',
    doi_tuong: '',
    phe_duyet: '', //1 Phê duyệt chính, 0 Kiểm tra
    tim: '',
  };

  const [formInput, setFormInput] = useState(initFormInput);

  // toggle modal
  const [isVisible, setIsVisible] = useState(false);
  //drdown date
  const [ngayDau, setNgayDau] = useState();
  const [ngayCuoi, setNgayCuoi] = useState();
  const [toggleNgayDau, setToggleNgayDau] = useState(false);
  const [toggleNgayCuoi, setToggleNgayCuoi] = useState(false);
  //drdown trạng thái
  const [openTrangThai, setOpenTrangThai] = useState(false);
  const [trangThaiSelected, setTrangThaiSelected] = useState('');
  //drdown nghiệp vụ
  const [openNghiepVu, setOpenNghiepVu] = useState(false);
  const [nghiepVuSelected, setNghiepVuSelected] = useState(''); //value dùng ở form cho tiện xử lý logic khác
  //drdown loại trình
  const [openLoaiTrinh, setOpenLoaiTrinh] = useState(false);
  const [loaiTrinhSelected, setLoaiTrinhSelected] = useState(''); //value dùng ở form cho tiện xử lý logic khác
  const [dataLoaiTrinh, setDataLoaiTrinh] = useState(DATA_LOAI_TRINH.filter((e) => e.nv === ''));
  //drdown đối tượng duyệt
  const [doiTuongDuyet, setDoiTuongDuyet] = useState('');
  const [openDoiTuongDuyet, setOpenDoiTuongDuyet] = useState(false);
  // Trạng thái người dùng duyệt
  const [nguoiDungDuyetSelected, setNguoiDungDuyetSelected] = useState('');
  const [openNguoiDungDuyet, setOpenNguoiDungDuyet] = useState(false);

  const [listMaDonViXuLySelected, setListMaDonViXuLySelected] = useState([]);

  let refModalDonViXuLy = useRef(null);
  let refModalTrangThai = useRef(null);

  const onChangeText = async (field, value) => {
    setFormInput((prev) => ({...prev, [field]: value}));
    if (field === 'nv') {
      let cloneDataLoaiTrinh = DATA_LOAI_TRINH;
      setDataLoaiTrinh(cloneDataLoaiTrinh.filter((e) => e.nv === value));
    }
  };

  useEffect(() => {
    if (ngayDau) onChangeText('ngayd', Number(moment(ngayDau).format('YYYYMMDD')));
    if (ngayCuoi) onChangeText('ngayc', Number(moment(ngayCuoi).format('YYYYMMDD')));
  }, [ngayDau, ngayCuoi]);

  const onSearchPress = () => {
    onPressSearch && onPressSearch(formInput);
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetField = () => {
    setFormInput(initFormInput);
    setNgayDau(moment(startOfYear).toDate());
    setNgayCuoi(moment(currentDate).toDate());
    setTrangThaiSelected('');
    onResetForm && onResetForm();
    initDonViXuLy();
    onChangeText('nv', '');
  };
  const initDonViXuLy = () => {
    refModalDonViXuLy?.current?.setData(chiNhanhBaoHiem);
  };

  const onOpenDropdown = (type) => {
    Keyboard.dismiss();
    type !== 0 && openNghiepVu && setOpenNghiepVu(false);
    type !== 1 && openTrangThai && setOpenTrangThai(false);
    type !== 2 && openLoaiTrinh && setOpenLoaiTrinh(false);
    type !== 3 && openDoiTuongDuyet && setOpenDoiTuongDuyet(false);
    type !== 4 && openNguoiDungDuyet && setOpenNguoiDungDuyet(false);
    if (type === 0) setLoaiTrinhSelected('');
  };

  const onSelectDonViXuLy = (listMaChiNhanh) => {
    try {
      setListMaDonViXuLySelected(listMaChiNhanh);
      if (listMaChiNhanh) onChangeText('ma_chi_nhanh', listMaChiNhanh.toString());
    } catch (error) {
      console.log(error.message);
    }
  };

  const getTenHienThi = (value, data) => {
    let name = '';
    data.map((e) => {
      if (e.ma_chi_nhanh === value) name = e.ten_chi_nhanh;
    });
    return name;
  };

  /**RENDER  */
  const renderDateTimeComp = (toggle, setToggle, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      date={date}
      mode={mode}
      locale={'vi_VN'}
      display="spinner"
      isVisible={toggle}
      confirmTextIOS="Chọn"
      maximumDate={maxDate}
      minimumDate={minDate}
      cancelTextIOS="Để sau"
      onCancel={() => setToggle(false)}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggle, setDateTime)}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.formInput}>
          {/* Ngày đầu/Ngày cuối */}

          <View style={styles.doubleRowView}>
            <View flex={1}>
              <TextInputOutlined
                isTouchableOpacity
                title={titleInput[0]}
                isRequired
                isDateTimeField
                editable={false}
                placeholder={titleInput[0]}
                onPress={() => setToggleNgayDau(true)}
                value={ngayDau ? moment(ngayDau).format('DD/MM/YYYY') : moment().startOf('year').format('DD/MM/YYYY')}
                inputStyle={{color: colors.BLACK}}
              />
              {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, setNgayDau, ngayDau, 'date', null, new Date(), 0)}
            </View>
            <View flex={1}>
              <TextInputOutlined
                isTouchableOpacity
                title={titleInput[1]}
                isRequired
                editable={false}
                inputStyle={{marginLeft: 8, color: colors.BLACK}}
                placeholder={titleInput[1]}
                value={ngayCuoi ? moment(ngayCuoi).format('DD/MM/YYYY') : moment().format('DD/MM/YYYY')}
                isDateTimeField
                onPress={() => setToggleNgayCuoi(true)}
              />
              {renderDateTimeComp(toggleNgayCuoi, setToggleNgayCuoi, setNgayCuoi, ngayCuoi, 'date', null, new Date(), 0)}
            </View>
          </View>
          <TextInputOutlined
            isTouchableOpacity
            editable={false}
            isDropdown
            title="Đơn vị xử lý"
            value={
              listMaDonViXuLySelected.length === 0
                ? 'Chọn đơn vị xử lý'
                : listMaDonViXuLySelected.length === 1
                ? getTenHienThi(listMaDonViXuLySelected[0], chiNhanhBaoHiemDangCay)
                : `Có ${listMaDonViXuLySelected.length} đơn vị được chọn`
            }
            placeholder="Đơn vị xử lý"
            // onFocus={closeDropdown}
            onPress={() => refModalDonViXuLy.current.show()}
            inputStyle={{color: colors.BLACK}}
          />
          <DropdownPicker
            searchable={false}
            title="Nghiệp vụ"
            zIndex={9000}
            isOpen={openNghiepVu}
            setOpen={setOpenNghiepVu}
            items={DATA_NGHIEP_VU}
            itemSelected={formInput.nv}
            setItemSelected={setNghiepVuSelected}
            onSelectItem={(selected) => onChangeText('nv', selected.value)}
            onOpen={() => onOpenDropdown(0)}
            placeholder="Chọn nghiệp vụ"
          />
          <DropdownPicker
            searchable={false}
            title="Trạng thái phê duyệt"
            placeholder="Chọn trạng thái phê duyệt"
            zIndex={8000}
            isOpen={openTrangThai}
            setOpen={setOpenTrangThai}
            items={DATA_TRANG_THAI}
            itemSelected={trangThaiSelected}
            setItemSelected={setTrangThaiSelected}
            onSelectItem={(selected) => onChangeText('trang_thai', selected.value)}
            onOpen={() => onOpenDropdown(1)}
          />
          <DropdownPicker
            searchable={false}
            title="Loại trình"
            zIndex={7000}
            isOpen={openLoaiTrinh}
            setOpen={setOpenLoaiTrinh}
            items={dataLoaiTrinh}
            itemSelected={formInput.loai}
            setItemSelected={setLoaiTrinhSelected}
            onSelectItem={(selected) => onChangeText('loai', selected.ma)}
            onOpen={() => onOpenDropdown(2)}
            schema={{label: 'ten', value: 'ma'}}
            placeholder="Chọn loại trình"
          />
          <TextInputOutlined title="Số hồ sơ bồi thường" placeholder="Nhập số hồ sơ bồi thường" value={formInput.so_hs} onChangeText={(text) => onChangeText('so_hs', text)} />
          <TextInputOutlined title="Đối tượng bảo hiểm" placeholder="Nhập đối tượng bảo hiểm" value={formInput.doi_tuong} onChangeText={(text) => onChangeText('doi_tuong', text)} />

          <DropdownPicker
            searchable={false}
            title="Đối tượng duyệt"
            zIndex={6000}
            isOpen={openDoiTuongDuyet}
            setOpen={setOpenDoiTuongDuyet}
            items={DATA_DOI_TUONG_DUYET}
            itemSelected={formInput.phe_duyet}
            setItemSelected={setDoiTuongDuyet}
            onSelectItem={(selected) => onChangeText('phe_duyet', selected.ma)}
            onOpen={() => onOpenDropdown(3)}
            schema={{label: 'ten', value: 'ma'}}
            placeholder="Chọn loại đối tượng duyệt"
          />
          <DropdownPicker
            searchable={false}
            title="Trạng thái duyệt"
            zIndex={5000}
            isOpen={openNguoiDungDuyet}
            setOpen={setOpenNguoiDungDuyet}
            items={DATA_TRANG_THAI_DUYET}
            itemSelected={formInput.nsd_duyet}
            setItemSelected={setNguoiDungDuyetSelected}
            onSelectItem={(selected) => onChangeText('nsd_duyet', selected.ma)}
            containerStyle={{marginBottom: 200}}
            onOpen={() => onOpenDropdown(4)}
            schema={{label: 'ten', value: 'ma'}}
            placeholder="Chọn trạng thái duyệt"
          />
        </View>
      </View>
    );
  };

  /* RENDER */

  return (
    <Modal
      onModalWillShow={initDonViXuLy}
      onSwipeComplete={onBackPress}
      // swipeDirection={['right']}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      animationIn="fadeInRight"
      animationOut="fadeOutRight"
      isVisible={isVisible}
      style={styles.modal}>
      <SafeAreaView style={styles.modalView}>
        <HeaderModal title="Tìm kiếm theo ngày trình" onBackPress={onBackPress} />
        <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">{renderFormInput()}</KeyboardAwareScrollView>
        <View style={styles.footer}>
          <ButtonLinear disabled={loading} textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} onPress={resetField} linearStyle={styles.btnLoginView} title="Nhập lại" />
          <ButtonLinear loading={loading} onPress={onSearchPress} linearStyle={styles.btnLoginView} title="Tìm kiếm" />
        </View>
      </SafeAreaView>
      <ModalSelectSimple
        value={formInput.trang_thai_hs_goc}
        title={'Chọn trạng thái'}
        ref={refModalTrangThai}
        baseData={DATA_TRANG_THAI}
        setValue={(val) => setTrangThaiSelected(val.value)}
        onBackPress={() => refModalTrangThai.current.hide()}
      />
      <ModalChiNhanhTheoDangCay ref={refModalDonViXuLy} showCheckCha={true} multiple setListMaDonViXuLySelected={(value) => onSelectDonViXuLy(value)} />
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    alignItems: 'flex-end',
    margin: 0,
  },
  modalView: {
    width: dimensions.width * 0.8,
    flex: 1,
    backgroundColor: '#FFF',
    paddingTop: 10,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  backBtn: {},
  iconBack: {},
  itemHangMucView: {
    paddingVertical: 10,
  },
  stepIndicator: {
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
  },
  headerView: {
    marginVertical: 10,
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  icon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  headerTitleView: {
    marginBottom: 10,
    paddingVertical: 9,
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
  },
  headerTitle: {
    marginBottom: 16,
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  formInput: {
    marginHorizontal: 10,
    // flex: 1,
  },
  btnLoginView: {
    marginHorizontal: 8,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
  },

  subLabel: {
    color: colors.GRAY10,
  },
  contentRow: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  title: {
    fontSize: 18,
    marginLeft: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  containerInput: {
    height: 45,
  },
  doubleRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footer: {
    bottom: 10,
    paddingTop: 10,
    flexDirection: 'row',
    paddingHorizontal: spacing.small,
    backgroundColor: colors.WHITE,
    borderRadius: 20,
  },
  inputRow: {
    flexDirection: 'row',
  },
});

export const ModalFilterTheoNgay = ModalFilterTheoNgayComponent;
