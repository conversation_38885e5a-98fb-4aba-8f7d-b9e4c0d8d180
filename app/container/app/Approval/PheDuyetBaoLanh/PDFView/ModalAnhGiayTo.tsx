import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {ButtonLinear, Icon, ImageComp, Text} from '@component';
import React, {Component} from 'react';
import {FlatList, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';

interface Props {
  scrollViewModalRef;
  setToggleModalAnhGiayTo;
  setScrollOffSet;
  imageUpload;
  openCamera;
  removeImage;
  onPressXemLaiAnh;
  onPressUploadAnh;
  disabledNutGui: boolean;
}

class ModalAnhGiayTo extends Component<Props> {
  constructor(props) {
    super(props);
  }
  renderCameraButton = () => {
    const {openCamera} = this.props;
    return (
      <TouchableOpacity style={styles.cameraView} onPress={openCamera}>
        <Icon.MaterialCommunityIcons name="camera-plus" size={40} color={colors.GRAY11} style={styles.iconCamera} />
      </TouchableOpacity>
    );
  };
  renderImageItem = (imageData) => {
    const {imageUpload, removeImage, onPressXemLaiAnh} = this.props;
    let index = imageData.index;
    if (index == imageUpload.length - 1) return this.renderCameraButton();
    return <ImageComp imageData={imageData} removeImage={removeImage} onPressXemLaiAnh={onPressXemLaiAnh} />;
  };

  render() {
    const {scrollViewModalRef, setToggleModalAnhGiayTo, setScrollOffSet, imageUpload, onPressUploadAnh, disabledNutGui = false} = this.props;
    return (
      <View style={styles.modalView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle}>Ảnh giấy tờ</Text>
          <TouchableOpacity style={styles.closeView} onPress={() => setToggleModalAnhGiayTo(false)}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>
        <ScrollView
          ref={scrollViewModalRef}
          onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={true}
          style={{height: dimensions.height / 2 - 150}}>
          <FlatList scrollEnabled={true} data={imageUpload} renderItem={this.renderImageItem} numColumns={3} horizontal={false} />
          {/* {currentPage == 0 && renderAnhXemLai(anhXemLai)}
          {(currentPage == 1 || currentPage == 2) && (
            <View>
              <FlatList
                data={anhXemLai}
                renderItem={(data) => (
                  <>
                    <Text style={styles.tieuDeDanhMucAnhXemLai}>{data.item.ten}</Text>
                    {renderAnhXemLai(data.item.images)}
                  </>
                )}
                keyExtractor={(item) => item.ma}
              />
            </View>
          )} */}
        </ScrollView>
        {/* <View style={{marginHorizontal: 10}}> */}
        <ButtonLinear title="Gửi" onPress={onPressUploadAnh} linearStyle={{marginHorizontal: 10, marginBottom: 20}} disabled={disabledNutGui} />
        {/* </View> */}
      </View>
    );
  }
}
export default ModalAnhGiayTo;

const styles = StyleSheet.create({
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 10,
    flex: 1,
    marginLeft: 30,
    // borderWidth: 1,
  },
  closeView: {
    marginRight: 15,
    // position: 'absolute',
    // top: ,
    // right: 15,
  },
  tieuDeDanhMucAnhXemLai: {
    marginLeft: 10,
    fontWeight: 'bold',
  },
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 1,
  },
  cameraView: {
    borderWidth: 1,
    borderColor: colors.GRAY11,
    borderStyle: 'dashed',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
  },
  iconCamera: {
    padding: 10,
  },
});
