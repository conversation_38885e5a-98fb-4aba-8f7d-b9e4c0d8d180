import {dimensions} from '@app/theme';
import {StyleSheet} from 'react-native';
import {colors} from '../../../../commons/Theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  btnTop: {
    flexDirection: 'row',
    // flex: 1,
    alignItems: 'stretch',
    width: dimensions.width / 2,
    justifyContent: 'center',
    padding: 7,
    backgroundColor: colors.WHITE4,
    borderColor: colors.GRAY,
    borderBottomWidth: 1,
    borderRightWidth: 1,
  },
  btnActive: {
    backgroundColor: colors.WHITE,
    borderBottomWidth: 0,
  },
  iconBtnTopView: {
    marginRight: 15,
    // borderWidth: 1,
    alignSelf: 'center',
  },
  btnTopView: {
    flexDirection: 'row',
    width: dimensions.width,
  },
  topView: {},
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
    // borderWidth: 1,
    // justifyContent : 'space-between'
    // flex : 1
  },
  txtTitle: {
    marginBottom: 4,
    fontSize: 12,
  },
  txtDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    paddingRight: 10,
  },

  txtAccidentDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    // fontSize: 14,
  },
  joinResolveDetailView: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    paddingLeft: 16,
    paddingVertical: 3,
    // borderColor: colors.GRAY4,
    // borderTopWidth: 0.5,
  },
  joinResolveView: {
    marginTop: 5,
    borderColor: colors.GRAY4,
    // justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    borderBottomWidth: 0.5,
    flexDirection: 'row',
  },

  inforView: {
    flex: 1,
    paddingLeft: 16,
    paddingVertical: 13,
    borderColor: colors.GRAY4,
    borderTopWidth: 0.5,
    // borderBottomWidth: 1,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
  },
  accidentHeader: {
    paddingHorizontal: 16,
    paddingVertical: 13,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtTime: {
    // marginBottom: 8,
    fontSize: 12,
  },
  accidentFooter: {},
  accidentView: {
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
  },

  txtLocation: {
    color: colors.GREEN2,
    fontSize: 12,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  accidentInforView: {
    paddingHorizontal: 16,
    paddingVertical: 13,
  },
  resolveAccidentInforView: {
    paddingHorizontal: 16,
    // paddingVertical: 13,
  },
  resolveAccidentView: {
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
  },
  btnRequestEnd: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    // minHeight: 50,
    flex: 1,
  },
  txtBtnRequestEnd: {
    color: colors.BLACK,
    fontWeight: 'bold',
  },
  footerView: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    flexDirection: 'row',
    width: dimensions.width,
    flex: 1,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  btnsFilterDocumentView: {
    flexDirection: 'row',
    // flex: 1,
    borderBottomColor: colors.GRAY,
    paddingBottom: 8,
    borderBottomWidth: 1,
    marginHorizontal: 10,
    // borderWidth : 1
  },
  btnFilterDocument: {
    borderWidth: 1,
    borderRadius: 20,
    padding: 8,
    borderColor: colors.GRAY,
    marginRight: 8,
  },
  btnFilterDocumentActive: {
    backgroundColor: colors.PRIMARY,
  },
  txtBtnFilterDocumentActive: {
    fontWeight: 'bold',
    color: colors.WHITE,
  },

  txtBtnFilterDocument: {
    color: colors.PRIMARY,
  },
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 10,
  },
  pdfComponent: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
  },
  headerTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 5,
    paddingTop: 20,
    marginLeft: 20,
    textDecorationLine: 'underline',
  },
  headerSubTitle: {
    fontSize: 14,
    marginBottom: 5,
    marginLeft: 20,
  },
  callView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  actionButtonIcon: {
    fontSize: 20,
    height: 22,
    color: 'white',
  },
  shadowStyle: {
    shadowOpacity: 0.35,
    // shadowOffset: {
    //   width: 0,
    //   height: 5,
    // },
    // shadowColor: colors.BLACK,
    // shadowRadius: 3,
    // elevation: 5,
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    borderWidth: 1,
    borderRadius: 35,
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalImageView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: dimensions.height,

    width: dimensions.width,
  },
  successTopView: {
    width: dimensions.width - 50,
    // height: height / 5,
    backgroundColor: colors.WHITE,
    paddingHorizontal: 20,
    // justifyContent: 'center',
    // alignItems: 'center',
    // borderTopLeftRadius: 20,
    // borderTopRightRadius: 20,
    // borderBottomWidth: 5,
    // borderBottomColor: colors.WHITE,
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
  },
  successMessage: {
    fontSize: 14,
    marginTop: 5,
    // fontWeight: 'bold',
    // marginTop: 20,
  },
  successCenterView: {
    width: dimensions.width - 50,
    paddingHorizontal: 20,
    // paddingTop: 5,
    // height: height / 4,
    backgroundColor: colors.WHITE,
    // justifyContent: 'center',
    // alignItems: 'center',
    // borderBottomLeftRadius: 20,
    // borderBottomRightRadius: 20,
    // borderTopWidth: 5,
  },
});
