import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {Icon, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {useRef, useState} from 'react';
import {FlatList, Linking, ScrollView, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';
import styles from './ProfileInformationStyles';

const profileHeaderTitle = [
  'Thông tin chung',
  'Lịch giám định',
  '<PERSON>h sách vụ tổn thất',
  'Quá trình giải quyết',
  'Bên tham gia giám định',
  'File tờ trình',
  'Thông tin giấy chứng nhận',
  'Tình trạng thanh toán',
];

function ProfileInformation(props) {
  const {profileData, categoryCommon} = props;

  let scrollViewModalRef = useRef(null);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState((dimensions.height / 3) * 4);

  const [toggleModal, setToggleModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalData, setModalData] = useState([]);

  const onPressHeader = (headerTitle, modalData) => {
    if (headerTitle == profileHeaderTitle[1]) {
      if (profileData.ho_so.hien_thi_button == 1) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Bạn chưa nhận hồ sơ', 'info');
        return;
      }
      NavigationUtil.push(SCREEN_ROUTER_APP.ASSESSMENT_SCHEDULE, {
        profileData: profileData,
      });
    }
    if (headerTitle == profileHeaderTitle[5]) {
      onPressViewFileDetail();
    } else if (headerTitle == profileHeaderTitle[2] || headerTitle == profileHeaderTitle[3] || headerTitle == profileHeaderTitle[4] || headerTitle == profileHeaderTitle[7]) {
      setModalTitle(headerTitle);
      headerTitle != profileHeaderTitle[7] ? setModalData(modalData) : props.getDataThanhToanPhi();
      setToggleModal(!toggleModal);
    } else if (headerTitle == profileHeaderTitle[6]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAY_CHUNG_NHAN, {
        giayChungNhan: {
          ma_doi_tac: profileData.ho_so.ma_doi_tac,
          ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh_ql,
          nv: 'XE',
          so_id_hdong: profileData.ho_so.so_id_hd,
          so_id_gcn: profileData.ho_so.so_id_dt,
          so_gcn: profileData?.ho_so?.gcn,
        },
      });
    }
  };

  const onPressViewFileDetail = () => {
    NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
      profileData: profileData,
      prevScreen: SCREEN_ROUTER_APP.APPROVAL_PROFILE,
    });
  };

  /* RENDER */

  const renderTinhTrangThanhToanItem = (data) => {
    let noteColor = colors.GREEN; //mặc định là text-success
    if (data.style == 'text-danger') noteColor = colors.RED1;
    else if (data.style == 'text-warning') noteColor = colors.ORANGE1;
    return (
      <View style={styles.thanhToanView}>
        <View style={styles.thanhToanRow}>
          <View style={styles.thanhToanColumn}>
            <Text children="Số tiền : " style={styles.txtAccidentDetail} />
            <NumericFormat value={data.so_tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} />} />
          </View>
        </View>
        <View style={styles.thanhToanRow}>
          <View style={styles.thanhToanColumn}>
            <Text children="Số tiền đã thanh toán : " style={styles.txtAccidentDetail} />
            <NumericFormat value={data.so_tien_da_tt || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} />} />
          </View>
        </View>
        <View style={{flexDirection: 'row', flex: 1}}>
          <Text children="Ghi chú : " style={styles.txtAccidentDetail} />
          <Text children={data.ghi_chu || ''} style={[styles.txtNoteThanhToan, {color: noteColor}]} />
        </View>
      </View>
    );
  };

  // RENDER header các đầu mục
  const renderProfileInformationHeader = (title, data) => {
    let iconName = 'list-ol';
    if (title == profileHeaderTitle[0]) iconName = 'file-text-o';
    else if (title == profileHeaderTitle[1]) iconName = 'calendar-plus-o';
    else if (title == profileHeaderTitle[4]) iconName = 'user-circle-o';
    else if (title == profileHeaderTitle[5]) iconName = 'file-pdf-o';
    else if (title == profileHeaderTitle[6]) iconName = 'info-circle';
    else if (title == profileHeaderTitle[7]) iconName = 'money';

    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => onPressHeader(title, data)}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name={iconName} size={15} style={styles.iconBtnTopLeftView} />
              <Text>
                {title}
                {' ' + dataLength}
              </Text>
            </View>
          </View>
          {title != profileHeaderTitle[0] && <Icon.SimpleLineIcons name={'arrow-right'} size={15} style={styles.iconBtnTopRightView} />}
        </TouchableOpacity>
        {title != profileHeaderTitle[0] && (
          <View style={{backgroundColor: colors.WHITE5}}>
            <View
              style={{
                paddingVertical: 10,
                backgroundColor: colors.WHITE,
                borderTopLeftRadius: 20,
                borderBottomLeftRadius: 20,
              }}
            />
          </View>
        )}
      </View>
    );
  };
  //render ra thông tin hồ sơ
  const renderProfileInformation = () => {
    if (!profileData || !profileData.ho_so) return;
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View style={{flex: 1, backgroundColor: colors.WHITE}}>
          {renderProfileInformationHeader(profileHeaderTitle[0])}
          <View style={{backgroundColor: colors.WHITE5}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số hồ sơ</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.so_hs}</Text>
            </View>
          </View>
          <View style={styles.inforView}>
            <Text style={styles.txtTitle}>Tên chủ xe</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.chu_xe}</Text>
          </View>
          <View style={{flexDirection: 'row', flex: 1}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Người liên hệ</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.nguoi_lhe}</Text>
            </View>
            {profileData.ho_so.dthoai_lhe && (
              <View style={styles.inforView}>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${profileData.ho_so.dthoai_lhe}`)}>
                  <Text style={styles.txtTitle}>Điện thoại</Text>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Icon.Entypo name="old-phone" size={20} style={{marginRight: 5}} color={colors.PRIMARY} />
                    <Text style={styles.txtDetail}>{profileData.ho_so.dthoai_lhe}</Text>
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
          <View style={{flexDirection: 'row', flex: 1}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Biển xe</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.doi_tuong}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Giấy chứng nhận</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.gcn}</Text>
            </View>
          </View>
          <View style={styles.inforView}>
            <Text style={styles.txtTitle}>Số hợp đồng</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.so_hd}</Text>
          </View>
          <View style={styles.inforView}>
            <Text style={styles.txtTitle}>Nội dung trình</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.dia_diem_gd || profileData.ho_so.noi_dung}</Text>
          </View>

          <View
            style={{
              flexDirection: 'row',
              flex: 1,
              backgroundColor: colors.WHITE5,
            }}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Trạng thái</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.trang_thai}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số tiền duyệt</Text>
              <NumericFormat value={profileData.ho_so.tien} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text>{value}</Text>} />
            </View>
          </View>
          <View style={{...styles.inforView, borderBottomWidth: 0}}>
            <Text style={styles.txtTitle}>Cán bộ thụ lý</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.ten_gdvtt || ''}</Text>
          </View>
        </View>
        {/* Chi tiết GCN */}
        {renderProfileInformationHeader(profileHeaderTitle[6])}
        {/* DANH SÁCH CÁC VỤ TỔN THẤT */}
        {renderProfileInformationHeader(profileHeaderTitle[2], profileData.dien_bien)}
        {/* QUÁ TRÌNH GIẢI QUYẾT */}
        {renderProfileInformationHeader(profileHeaderTitle[3], profileData.thong_tin_qtxl)}
        {/* Tình trạng thanh toán */}
        {renderProfileInformationHeader(profileHeaderTitle[7])}

        {/* BÊN THAM GIA GIẢI QUYẾT */}
        {/* {renderProfileInformationHeader(
          profileHeaderTitle[4],
          profileData.nguoi_dd,
        )} */}

        {/* File tờ trình */}
        {/* {renderProfileInformationHeader(profileHeaderTitle[5])} */}
      </View>
    );
  };

  //render Item VỤ TỔN THẤT
  const renderAccidentItem = (item) => {
    return (
      <View style={styles.accidentView}>
        <View style={styles.accidentInforView}>
          <Text style={styles.txtTitle}>Ngày</Text>
          <Text style={styles.txtTime}>{item.ngay_xr}</Text>
        </View>
        <View style={styles.accidentContent}>
          <View style={styles.accidentInforView}>
            <Text style={styles.txtTitle}>Nguyên nhân</Text>
            <Text style={styles.txtAccidentDetail}>{item.nguyen_nhan}</Text>
          </View>
          <View style={styles.accidentInforView}>
            <Text style={styles.txtTitle}>Hậu quả</Text>
            <Text style={styles.txtAccidentDetail}>{item.hau_qua}</Text>
          </View>
        </View>
        <View style={styles.accidentFooter}>
          <View
            style={{
              flexDirection: 'row',
              flex: 1,
            }}>
            <View style={[styles.accidentInforView, {flex: 1}]}>
              <Text style={styles.txtTitle}>Địa điểm</Text>
              <Text style={styles.xtAccidentDetail}>{item.dia_diem}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  //RENDER ITEM QUÁ TRÌNH GIẢI QUYẾT
  const renderResolveItem = (item) => {
    return (
      <View style={styles.resolveAccidentView}>
        <View style={styles.accidentHeader}>
          <Text style={styles.txtTime}>{item.ngay}</Text>
          <Text style={styles.txtLocation}>{item.ten}</Text>
        </View>
        <View style={styles.accidentContent}>
          <View style={styles.resolveAccidentInforView}>
            <Text style={styles.txtAccidentDetail}>Nội dung: {item.nd}</Text>
          </View>
        </View>
      </View>
    );
  };
  //RENDER ITEM CÁC BÊN THAM GIA
  const renderJoinResolveItem = (item) => {
    let daiDien = '';
    for (let i = 0; i < categoryCommon.type2.length; i++) {
      if (item.dai_dien == categoryCommon.type2[i].value) {
        daiDien = categoryCommon.type2[i].label;
        break;
      }
    }
    return (
      <TouchableOpacity
        style={styles.joinResolveView}
        onPress={() => {
          setToggleModal(false);
          NavigationUtil.push(SCREEN_ROUTER_APP.JOIN_RESOLVE, {
            profileData: profileData,
            joinResolveData: item,
          });
        }}>
        <View style={{flex: 1}}>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Đại diện</Text>
            <Text style={styles.txtDetail}>{daiDien}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Họ tên</Text>
            <Text style={styles.txtDetail}>{item.ten}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Điện thoại</Text>
            <Text style={styles.txtDetail}>{item.dien_thoai}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Email</Text>
            <Text style={styles.txtDetail}>{item.email}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Lần giám định</Text>
            <Text style={styles.txtDetail}>{item.lan_gd_hthi}</Text>
          </View>
        </View>
        <View style={{flex: 0}}>
          <Icon.FontAwesome5 name="user-edit" size={20} color={colors.BLACK} />
        </View>
      </TouchableOpacity>
    );
  };
  const renderItemModal = (data) => {
    let item = data.item;
    //Danh sách vụ tổn thất
    if (modalTitle == profileHeaderTitle[2]) {
      return renderAccidentItem(item);
    }
    //quá trình giải quyết
    else if (modalTitle == profileHeaderTitle[3]) return renderResolveItem(item);
    else if (modalTitle == profileHeaderTitle[4]) return renderJoinResolveItem(item);
    else if (modalTitle == profileHeaderTitle[7]) return renderTinhTrangThanhToanItem(item);
  };
  const renderModal = () => {
    return (
      <Modal
        isVisible={toggleModal}
        onSwipeComplete={() => setToggleModal(false)}
        onBackdropPress={() => setToggleModal(false)}
        swipeDirection={['down']}
        scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
        scrollOffset={scrollOffSet}
        scrollOffsetMax={(dimensions.height * 3) / 4 - flatListHeight} // content height - ScrollView height
        propagateSwipe={true}
        style={styles.modal}>
        <View style={styles.modalView}>
          <View style={styles.modalTitleView}>
            <Text style={styles.modalTitle}>{modalTitle}</Text>
            <TouchableOpacity style={styles.closeView} onPress={() => setToggleModal(false)}>
              <Icon.AntDesign name="closecircleo" size={20} />
            </TouchableOpacity>
          </View>

          <ScrollView ref={scrollViewModalRef} onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)} scrollEventThrottle={16}>
            <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>
              {/* nếu là tình trạng thanh toán thì lấy từ props.tinhTrangThanhToan do khi click vào ProfileInformationHeader mới gọi API để lấy data*/}
              <FlatList data={modalTitle != profileHeaderTitle[7] ? modalData : props.tinhTrangThanhToan} renderItem={renderItemModal} />
            </View>
          </ScrollView>
        </View>
        {modalTitle == profileHeaderTitle[4] && (
          <TouchableOpacity
            style={styles.btnAddJoinResolve}
            onPress={() => {
              setToggleModal(false);
              NavigationUtil.push(SCREEN_ROUTER_APP.JOIN_RESOLVE, {
                profileData: profileData,
              });
            }}>
            <Icon.FontAwesome5 name="user-plus" size={20} color={colors.PRIMARY} style={styles.iconAddJoinResolve} />
          </TouchableOpacity>
        )}
      </Modal>
    );
  };

  return (
    <>
      {renderProfileInformation()}

      {renderModal()}
    </>
  );
}

const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ProfileInformation);
