import {StyleSheet, Dimensions} from 'react-native';
import {colors} from '../../../../../commons/Theme';
const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    marginTop: 5,
    marginBottom: 50,
    flex: 1,
  },
  btnTop: {
    flexDirection: 'row',
    // flex: 1,
    alignItems: 'stretch',
    width: width / 2,
    justifyContent: 'center',
    padding: 7,
    backgroundColor: colors.WHITE4,
    borderColor: colors.GRAY,
    borderBottomWidth: 1,
    borderRightWidth: 1,
  },
  btnActive: {
    backgroundColor: colors.WHITE,
    borderBottomWidth: 0,
  },
  iconBtnTopLeftView: {
    marginRight: 15,
    // borderWidth: 1,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    marginHorizontal: 15,
    // borderWidth: 1,
    alignSelf: 'center',
  },
  btnTopView: {
    flexDirection: 'row',
    width: width,
  },
  topView: {},
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
    // borderWidth: 1,
    // justifyContent : 'space-between'
    // flex : 1
  },
  txtTitle: {
    marginBottom: 4,
    fontSize: 12,
  },
  txtDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    paddingRight: 10,
  },

  txtAccidentDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    // fontSize: 14,
  },
  inforView: {
    flex: 1,
    paddingLeft: 16,
    paddingVertical: 13,
    borderColor: colors.GRAY4,
    borderBottomWidth: 0.5,
    backgroundColor: colors.WHITE,
    borderBottomWidth: 1,
    borderTopLeftRadius: 20,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: width - 20,
    // marginBottom: 10,
  },
  accidentHeader: {
    paddingHorizontal: 16,
    paddingVertical: 13,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtTime: {
    // marginBottom: 8,
    fontSize: 12,
  },
  accidentFooter: {},
  accidentView: {
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
    // height: 200,
  },

  txtLocation: {
    color: colors.GREEN2,
    fontSize: 12,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  accidentInforView: {
    paddingHorizontal: 16,
    paddingVertical: 13,
  },
  resolveAccidentInforView: {
    paddingHorizontal: 16,
    // paddingVertical: 13,
  },
  resolveAccidentView: {
    // height: 100,
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
  },
  btnReceivingRecords: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    minHeight: 50,
    borderRadius: 10,
    flex: 1,
  },
  linearBtnView: {
    flex: 1,
    borderRadius: 30,
    marginHorizontal: 10,
    backgroundColor: colors.WHITE,
  },
  btnRequestEnd: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    minHeight: 50,
    flex: 1,
  },
  txtBtnReceivingRecords: {
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  txtBtnRequestEnd: {
    color: colors.BLACK,
    fontWeight: 'bold',
  },
  footerView: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    flexDirection: 'row',
    width: width,
    flex: 1,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  btnsFilterDocumentView: {
    flexDirection: 'row',
    // flex: 1,
    borderBottomColor: colors.GRAY,
    paddingBottom: 8,
    borderBottomWidth: 1,
    marginHorizontal: 10,
    // borderWidth : 1
  },
  btnFilterDocument: {
    borderWidth: 1,
    borderRadius: 20,
    padding: 8,
    borderColor: colors.GRAY,
    marginRight: 8,
  },
  btnFilterDocumentActive: {
    backgroundColor: colors.PRIMARY,
  },
  txtBtnFilterDocumentActive: {
    fontWeight: 'bold',
    color: colors.WHITE,
  },

  txtBtnFilterDocument: {
    color: colors.PRIMARY,
  },
  imageDocument: {
    width: width / 4,
    height: width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 10,
  },
  pdfComponent: {
    flex: 1,
    width: width,
    height: height,
  },
  headerTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 5,
    paddingTop: 20,
    marginLeft: 20,
    textDecorationLine: 'underline',
  },
  headerSubTitle: {
    fontSize: 14,
    marginBottom: 5,
    marginLeft: 20,
  },
  callView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  actionButtonIcon: {
    fontSize: 20,
    height: 22,
    color: 'white',
  },
  shadowStyle: {
    shadowOpacity: 0.35,
    // shadowOffset: {
    //   width: 0,
    //   height: 5,
    // },
    // shadowColor: colors.BLACK,
    // shadowRadius: 3,
    // elevation: 5,
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    borderWidth: 1,
    borderRadius: 35,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: width,
    height: (height * 3) / 4,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  scrollContentView: {
    height: height * 3,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 18,
    // fontWeight: 'bold',
    marginVertical: 10,
  },
  closeView: {
    position: 'absolute',
    top: 15,
    right: 15,
  },

  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalTitleView: {
    height: 50,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  joinResolveDetailView: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    // paddingLeft: 16,
    paddingVertical: 3,
  },
  joinResolveView: {
    marginTop: 5,
    paddingHorizontal: 20,
    borderColor: colors.GRAY4,
    // justifyContent: 'center',
    alignItems: 'center',
    // flex: 1,
    borderBottomWidth: 0.5,
    flexDirection: 'row',
  },
  btnAddJoinResolve: {
    position: 'absolute',
    right: 10,
    bottom: 10,
    borderRadius: 100,
    borderWidth: 3,
    borderColor: colors.PRIMARY,
  },
  iconAddJoinResolve: {
    paddingVertical: 14,
    paddingHorizontal: 12,
  },
  thanhToanView: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderBottomWidth: 0.2,
    marginHorizontal: 10,
  },
  txtNoteThanhToan: {
    flexShrink: 1,
    // marginTop: 5,
  },
  thanhToanRow: {
    marginVertical: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  thanhToanColumn: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    // borderWidth: 1,
  },
});
