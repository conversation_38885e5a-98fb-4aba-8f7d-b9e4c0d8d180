import R from '@app/assets/R';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {useCallback, useEffect, useState} from 'react';
import {Alert, FlatList, Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import Progress from 'react-native-progress/Circle';
import {connect} from 'react-redux';

const extensionsImage = ['.jpg', '.jpeg', '.png', '.gif'];
const extensionsFile = ['.pdf', '.doc', '.docx', '.xml', '.xls', '.xlsx']; //đuôi mở rộng của file

function RenderImage(props) {
  const {title, profileInfo, switchImgView, setData, navigation, setIsLoading} = props;

  const [anhHoSo, setAnhHoSo] = useState([]);
  const [imageData, setImageData] = useState([]);

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    navigation &&
      navigation.addListener('focus', () => {
        getData();
      });
  }, []);

  const onPressToggleCheckAll = (type, images) => {
    let newCheckedValue;
    if (type == 0) newCheckedValue = false;
    else if (type == 1) newCheckedValue = true;
    let imageDataTmp = imageData;
    for (let i = 0; i < imageDataTmp.length; i++) {
      if (imageDataTmp[i].ma == images[0].nhom.ma) {
        imageDataTmp[i].checked = newCheckedValue;
        for (let j = 0; j < imageDataTmp[i].images.length; j++) imageDataTmp[i].images[j].checked = newCheckedValue;
        setImageData([...imageDataTmp]);
        setData && setData([...imageDataTmp]);
        return;
      }
    }
  };

  const groupBy = useCallback((xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  }, []);

  const getData = async () => {
    setIsLoading && setIsLoading(true);
    let params = {
      so_id: profileInfo?.so_id || '',
    };

    try {
      let response = await ESmartClaimEndpoint.getFileThumbnail(axiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let imgsGroup = groupBy(response.data_info, 'ma_file'); //return object
      let menuImage = [];
      for (const property in imgsGroup) {
        menuImage.push({
          checked: false,
          images: imgsGroup[property],
          ma: imgsGroup[property][0].ma_file,
          ten: imgsGroup[property][0].nhom_anh,
          bt: imgsGroup[property][0].bt,
        });
      }
      setImageData(menuImage);
      let imagesTmp = response.data_info.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
        };
        item.nhom = nhom;
        // let newObjData = {
        //   checked: false,
        //   duong_dan: item.duong_dan,
        //   duong_dan_ai: item.duong_dan_ai,
        //   extension: item.extension,
        //   ghi_chu: item.ghi_chu,
        //   loai: item.loai,
        //   ma_chi_nhanh: item.ma_chi_nhanh,
        //   ma_doi_tac: item.ma_doi_tac,
        //   ma_file: item.ma_file,
        //   name: item.ten_file,
        //   ngay: '10/06/2022 09:12',
        //   ngay_dong_bo: null,
        //   nhom_anh: item.nhom_anh,
        //   nhom_hang_muc: item.nhom_hang_muc,
        //   nsd: item.nsd,
        //   path: item.duong_dan,
        //   so_id: item.so_id,
        //   so_id_doi_tuong: item.so_id_doi_tuong,
        //   so_id_dt: item.so_id_dt,
        //   so_luong: 0,
        //   stt: 0,
        //   stt_hang_muc: 0,
        //   ten_file: item.ten_file,
        //   trang_thai: '1',
        //   vu_tt: null,
        //   x: 0,
        //   y: 0,
        //   nhom: nhom,
        // };
        return item;
      });
      setAnhHoSo([...imagesTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressImageCheck = (image) => {
    let imageDataTmp = imageData;
    for (let i = 0; i < imageDataTmp.length; i++) {
      let images = imageDataTmp[i].images;
      for (let j = 0; j < images.length; j++)
        if (images[j].bt == image.bt) {
          images[j].checked = !images[j].checked;
          setImageData([...imageDataTmp]);
          setData([...imageDataTmp]);
          return;
        }
    }
  };

  const onPressOpenImageView = (currentDocumentData) => {
    if (currentDocumentData.item.extension == '.pdf') {
      NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
        profileData: profileInfo,
        prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
        dataPDF: currentDocumentData.item,
      });
    } else if (extensionsImage.includes(currentDocumentData.item.extension)) {
      // if (switchImgView) return;
      NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
        currentImageData: currentDocumentData,
        imagesData: anhHoSo,
      });
    }
  };

  const renderImageItemStep34 = (data) => {
    let item = data.item;
    if (!item.images || item.images.length == 0) return null;
    let checkAll = true;
    let checkAllIsImage = true;
    item.images.map((imgItem) => {
      if (imgItem.checked == false) checkAll = false;
      if (!extensionsImage.includes(imgItem.extension)) checkAllIsImage = false;
    });

    return (
      <>
        <View style={styles.headerView}>
          <Text style={styles.headerTitle}>{item.ten}</Text>
          {props.switchImgView && // đang bật CHỌN ẢNH
            checkAllIsImage && //tất cả file trong hạng mục phải là ảnh
            !props.switchClassifyView && //KHÔNG PHẢI LÀ CHẾ ĐỘ XEm THÔNG TIN PHÂN LOẠI
            item.images.length > 0 && (
              <TouchableOpacity
                onPress={() => {
                  !checkAll
                    ? onPressToggleCheckAll(1, item.images) //1 : chọn tất cả
                    : onPressToggleCheckAll(0, item.images); //0 : bỏ chọn tất cả
                }}>
                <View flexDirection="row" alignItems="center">
                  <Text style={{color: colors.PRIMARY, marginRight: 4}}>{!checkAll ? 'Chọn tất cả' : 'Bỏ chọn'}</Text>
                  <Icon.MaterialCommunityIcons name={!checkAll ? 'check-all' : 'notification-clear-all'} size={20} color={colors.BLACK} />
                </View>
              </TouchableOpacity>
            )}
        </View>
        {!props.switchClassifyView && (
          <FlatList
            numColumns={3}
            data={item.images}
            horizontal={false}
            scrollEnabled={false}
            style={{marginBottom: 10}}
            renderItem={renderImageItemStep12}
            keyExtractor={(item) => item.bt.toString()}
          />
        )}
      </>
    );
  };

  const onRefresh = () => {
    getData();
  };

  const renderImageStep34 = (title, imagesData) => {
    let arrImages = [];
    arrImages = imagesData.filter((itemImages) => itemImages.images?.length > 0);
    return (
      <View marginTop={20}>
        {/* <View style={styles.headerView}>
          <Text style={styles.headerTitle}>{title}</Text>
        </View> */}
        {arrImages.length == 0 ? (
          renderNoData()
        ) : (
          <FlatList data={arrImages} scrollEnabled={false} initialNumToRender={50} style={{marginBottom: 10}} renderItem={(item) => renderImageItemStep34(item, {title: title})} />
        )}
      </View>
    );
  };

  const renderImageItemStep12 = (data) => {
    let item = data.item;
    if (extensionsFile.includes(item.extension)) {
      let iconName = '',
        iconColor = '';
      if (item.extension === extensionsFile[0]) {
        iconName = 'file-pdf-box';
        iconColor = colors.RED2;
      } else if (item.extension == extensionsFile[1] || item.extension == extensionsFile[2]) {
        iconName = 'file-word';
        iconColor = colors.BLUE2;
      } else if (item.extension == extensionsFile[3]) {
        iconName = 'file-code-outline';
        iconColor = colors.ORANGE;
      } else if (item.extension == extensionsFile[4] || item.extension == extensionsFile[5]) {
        iconName = 'file-excel';
        iconColor = colors.GREEN3;
      }

      return (
        <TouchableOpacity onPress={() => onPressOpenImageView(data)}>
          <View style={styles.imageDocument}>
            <Icon.MaterialCommunityIcons name={iconName} color={iconColor} size={dimensions.width / 4} />
          </View>
          {switchImgView && (
            <View style={styles.checkboxImgView}>
              <CheckboxComp value={item.checked} onValueChange={() => onPressImageCheck(item)} />
            </View>
          )}
        </TouchableOpacity>
      );
    }
    return (
      <View>
        <TouchableOpacity onPress={() => onPressOpenImageView(data)}>
          <ImageProcess
            source={{uri: `data:image/gif;base64,${item.duong_dan}`}}
            indicator={Progress.Circle}
            style={styles.imageDocument}
            imageStyle={{borderRadius: 20}}
            indicatorProps={{
              size: 70,
              borderWidth: 0,
              color: colors.PRIMARY,
              unfilledColor: colors.PRIMARY_LIGHT,
            }}
            renderError={() => (
              <View>
                <Image source={R.images.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />
              </View>
            )}
          />
          {/* {imageData.stt_hang_muc != undefined && <Text children={imageData.stt_hang_muc} style={{position: 'absolute', top: 20, left: 25, color: '#FFF', fontSize: 18, fontWeight: 'bold'}} />} */}
        </TouchableOpacity>
        {switchImgView && (
          <View style={styles.checkboxImgView}>
            <CheckboxComp
              value={item.checked}
              onValueChange={() => {
                onPressImageCheck(item);
              }}
            />
          </View>
        )}
      </View>
    );
  };

  return <View style={{flex: 1}}>{renderImageStep34('Ảnh hồ sơ, giấy tờ', imageData)}</View>;
}
const renderNoData = () => (
  <View style={styles.noDataView}>
    <Image source={R.images.img_no_data} style={styles.imageNoData} resizeMode={'contain'} />
    <Text style={{color: colors.GRAY7}}>Chưa có dữ liệu</Text>
  </View>
);

const styles = StyleSheet.create({
  imageDocument: {
    borderRadius: 20,
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginVertical: 15,
    marginHorizontal: 15,
    alignItems: 'center',
    justifyContent: 'center',
    // borderWidth: 1,
  },
  checkboxImgView: {
    right: 15,
    bottom: 15,
    position: 'absolute',
    borderTopLeftRadius: 5,
    backgroundColor: colors.WHITE,
  },
  headerTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    textDecorationLine: 'underline',
  },
  headerView: {
    marginBottom: 10,
    flexDirection: 'row',
    marginHorizontal: 15,
    justifyContent: 'space-between',
  },
  headerSubTitle: {
    flex: 1,
    fontSize: 14,
    marginRight: 5,
  },
  headerSubTitleView: {
    flex: 1,
  },
  noDataView: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageNoData: {
    width: dimensions.width / 5,
    height: dimensions.width / 5,
  },
});
const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(RenderImage);
