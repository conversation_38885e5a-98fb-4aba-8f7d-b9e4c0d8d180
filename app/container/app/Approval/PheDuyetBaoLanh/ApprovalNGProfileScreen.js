import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {ButtonLinear, CustomTabBar, ScreenComponent} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, RefreshControl, SafeAreaView, ScrollView, View} from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {connect} from 'react-redux';
import styles from './ApprovalNGProfileStyles';
import RenderImage from './RenderImage';
import ThongTinChiTietHoSoScreen from './ThongTinChiTietHoSo/ThongTinChiTietHoSoScreen';

const ApprovalNGProfileScreenComponent = (props) => {
  console.log('ApprovalNGProfileScreenComponent');
  const {route, navigation} = props;
  const {profileDetail} = route?.params;
  const scrollViewRef = useRef(null);
  const tabViewRef = useRef(null);

  const [refreshing, setRefreshing] = useState(false);
  const [profileData, setProfileData] = useState(null);

  const [tinhTrangThanhToan, setTinhTrangThanhToan] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    navigation.addListener('focus', () => {
      profileDetail && getApproveProfile(profileDetail);
    });
  }, []);

  /*khi đang ở trong chi tiết phê duyệt - có notification -> hiển thị Alert 
  -> ấn vào nút Xem chi tiết -> cập nhật notificationFirebase -> gọi API để hiển thị hồ sơ vừa ấn xem chi tiết*/
  useEffect(() => {
    let profileDetail = props.notificationFirebase;
    if (profileDetail && profileDetail.thong_tin_chung) getApproveProfile(profileDetail);
  }, [props.notificationFirebase]);

  const onRefresh = () => {
    setRefreshing(true);
    getApproveProfile(profileData);
  };

  //lấy hồ sơ phê duyệt
  const getApproveProfile = async (profileDetail) => {
    let params = {
      ma_doi_tac: profileDetail.thong_tin_chung?.ma_doi_tac || profileDetail.ma_doi_tac,
      bt: profileDetail.thong_tin_chung?.bt || profileDetail.bt,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.APPROVAL_PROFILE_DETAIL, params);
      console.log('🚀 ~ file: ApprovalNGProfileScreen.js:122 ~ getApproveProfile ~ response:', response);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        setRefreshing(false);
        return;
      }
      response.data_info.ho_so = response.data_info.thong_tin_chung;
      setProfileData(response.data_info);
      // store.dispatch(updateNotificationFirebase(null)); //phải cho về null. không mỗi lần nó sẽ gọi lại theo cái data từ notification
      if (!response.data_info.thong_tin_chung) return;
      let paramsThumbnail = {
        so_id: response.data_info.thong_tin_chung.so_id,
      };
      let responseThumbnail = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, paramsThumbnail);
      setRefreshing(false);
      if (!responseThumbnail || !responseThumbnail.state_info || responseThumbnail.state_info.status !== 'OK') return;
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //API xem tình trạng thanh toán phí
  const getDataThanhToanPhi = async () => {
    let params = {
      ma_chi_nhanh_ql: profileData.ho_so.ma_chi_nhanh_ql || '',
      so_id_hd: profileData.ho_so.so_id_hd || '',
      nv: 'XE',
    };
    try {
      let response = await PartnerEndpoint.getPayment(AxiosConfig.ACTION_CODE.XEM_TINH_TRANG_THANH_TOAN_PHI, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setTinhTrangThanhToan(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressViewFileDetail = () => {
    NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
      profileData: profileData,
      prevScreen: SCREEN_ROUTER_APP.APPROVAL_NG_PROFILE,
    });
  };

  /** RENDER */

  //render ra nút của Hồ sơ phê duyệt
  const renderApprovalButton = () => {
    if (!profileData || !profileData.ho_so) return null;
    return <ButtonLinear title="Xem file tờ trình" onPress={onPressViewFileDetail} linearStyle={{marginHorizontal: 10}} />;
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle={'Phê duyệt bảo lãnh'}
      renderView={
        <SafeAreaView style={styles.container}>
          <ScrollableTabView ref={tabViewRef} style={{paddingVertical: 8}} initialPage={0} renderTabBar={() => <CustomTabBar />}>
            <ScrollView
              tabLabel="Thông tin hồ sơ"
              scrollEnabled={true}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
              ref={scrollViewRef}
              showsVerticalScrollIndicator={false}>
              <ThongTinChiTietHoSoScreen profileData={profileData} tinhTrangThanhToan={tinhTrangThanhToan} getDataThanhToanPhi={getDataThanhToanPhi} />
            </ScrollView>
            <View tabLabel="Tài liệu bồi thường">
              <View>
                <ScrollView showsVerticalScrollIndicator={false}>
                  <RenderImage setIsLoading={setIsLoading} navigation={navigation} profileInfo={profileData?.ho_so} />
                </ScrollView>
              </View>
            </View>
          </ScrollableTabView>
          {/* {renderContactToCenter()} */}
          <View style={styles.footerView}>{renderApprovalButton()}</View>
        </SafeAreaView>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  user: state.user.data,
  notificationFirebase: state.notificationFirebase.data,
});

const mapDispatchToProps = {};

const ApprovalNGProfileScreenConnect = connect(mapStateToProps, mapDispatchToProps)(ApprovalNGProfileScreenComponent);
export const ApprovalNGProfileScreen = memo(ApprovalNGProfileScreenConnect, isEqual);
