import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },

  txtTitle: {
    marginBottom: spacing.tiny,
    fontSize: 12,
  },
  txtDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    paddingRight: spacing.smaller,
  },

  inforView: {
    padding: spacing.small,
    borderColor: colors.GRAY4,
    borderTopWidth: 0.5,
  },

  footerView: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    flexDirection: 'row',
    // width: dimensions.width,
    flex: 1,
    paddingVertical: spacing.small,
    backgroundColor: colors.WHITE,
  },

  titleBangView: {
    flexDirection: 'row',
    borderColor: colors.GRAY1,
    paddingRight: spacing.small,
  },
  titleBang: {
    flex: 1,
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 14,
    paddingVertical: spacing.small,
    color: colors.PRIMARY,
  },
  itemThanhToanView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: spacing.small,
    borderRadius: 10,
  },
  txtThanhToan: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.tiny,
    // borderWidth: 1,
  },
  txtTongCong: {
    fontWeight: 'bold',
    fontSize: 14,
    flex: 1,
    textAlign: 'center',
    paddingVertical: spacing.small,
    color: colors.PRIMARY,
    // borderWidth: 1,
  },
  valueTongCong: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 10,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    // borderWidth: 1,
  },
  tongCongView: {
    flexDirection: 'row',
    // borderTopWidth: 1,
    borderColor: colors.GRAY1,
    paddingRight: spacing.small,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE6,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: dimensions.width - 20,
    marginLeft: spacing.small,
  },
  headerCollap: {
    flexDirection: 'row',
    padding: spacing.small,
    // backgroundColor: colors,
    justifyContent: 'center',
  },
  iconBtnTopLeftView: {
    marginRight: spacing.small,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    marginHorizontal: spacing.small,
    // borderWidth: 1,
    alignSelf: 'center',
  },
});
