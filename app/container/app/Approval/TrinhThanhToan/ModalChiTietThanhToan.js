import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {useRef, useState} from 'react';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';

function ModalChiTietThanhToan(props) {
  // console.log('ModalChiTietThanhToan', props);
  let scrollViewModalRef = useRef(null);
  let chiTietThanhToan = props.chiTietThanhToan;
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [contentModalHeight, setContentModalHeight] = useState((dimensions.height / 3) * 4);

  const renderContent = () => {
    return (
      <>
        <View style={styles.row}>
          <Text children="Số hồ sơ" style={styles.rowTitle} />
          <Text children={chiTietThanhToan?.so_hs || ''} style={styles.rowValue} />
        </View>
        <View style={{...styles.row, backgroundColor: colors.WHITE6}}>
          <Text children="Tên người hưởng thụ" style={styles.rowTitle} />
          <Text children={chiTietThanhToan?.ten || ''} style={styles.rowValue} />
        </View>
        <View style={styles.row}>
          <Text children="PTTT" style={styles.rowTitle} />
          <Text children={chiTietThanhToan?.pttt || ''} style={styles.rowValue} />
        </View>
        <View style={{...styles.row, backgroundColor: colors.WHITE6}}>
          <Text children="Số tài khoản, Ngân hàng" style={styles.rowTitle} />
          <Text children={chiTietThanhToan?.tk_cmt + ' ' + chiTietThanhToan?.ten_ngan_hang || ''} style={styles.rowValue} />
        </View>
        <View style={styles.row}>
          <Text children="Số tiền" style={styles.rowTitle} />
          <NumericFormat value={chiTietThanhToan?.tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.rowValue} />} />
        </View>
        <View style={{...styles.row, backgroundColor: colors.WHITE6}}>
          <Text children="Nội dung thanh toán" style={styles.rowTitle} />
          <Text children={chiTietThanhToan?.dien_giai || ''} style={styles.rowValue} />
        </View>
      </>
    );
  };
  return (
    <Modal
      isVisible={props.toggleModal}
      onSwipeComplete={() => props.setToggleModal(false)}
      onBackdropPress={() => props.setToggleModal(false)}
      swipeDirection={['down']}
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
      scrollOffset={scrollOffSet}
      scrollOffsetMax={dimensions.height / 2 - contentModalHeight} // content height - ScrollView height
      propagateSwipe={true}
      style={styles.modal}>
      <View style={styles.modalView}>
        <View style={styles.modalTitleView}>
          <Text font="regular16" style={styles.modalTitle} children="Chi tiết" />
          <TouchableOpacity style={styles.closeView} onPress={() => props.setToggleModal(false)}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>
        <ScrollView
          ref={scrollViewModalRef}
          onScroll={(event) => {
            setScrollOffSet(event.nativeEvent.contentOffset.y);
          }}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}>
          <View onLayout={(event) => setContentModalHeight(event.nativeEvent.layout.height)} style={styles.modalContentView}>
            {renderContent()}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginVertical: 10,
    flex: 1,
    marginLeft: 30,
  },
  closeView: {
    marginRight: 15,
  },
  modalContentView: {
    marginHorizontal: 10,
    marginTop: 10,
  },
  row: {
    flexDirection: 'row',
    // marginVertical: 5,
    paddingVertical: 10,
    // borderBottomWidth: 0.5,
    // textAlign: 'center',
    // height: 60,
    // flex: 1,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  rowTitle: {
    flex: 1,
    fontWeight: 'bold',
    textAlignVertical: 'center',
    paddingHorizontal: 10,
  },
  rowValue: {
    flex: 2,
    // textAlign: 'right',
    textAlignVertical: 'center',
    paddingHorizontal: 10,
  },
});

const mapStateToProps = (state) => ({});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ModalChiTietThanhToan);
