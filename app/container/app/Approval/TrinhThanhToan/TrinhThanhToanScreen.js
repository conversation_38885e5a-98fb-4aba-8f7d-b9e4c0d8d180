import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@component';
import {DATA_CONSTANT, SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, TouchableOpacity, View} from 'react-native';
import prompt from 'react-native-prompt-android';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';
import ModalChiTietThanhToan from './ModalChiTietThanhToan';
import styles from './TrinhThanhToanStyles';

const TrinhThanhToanScreenComponent = ({route, notificationFirebase}) => {
  console.log('TrinhThanhToanScreen', route);
  const [refreshing, setRefreshing] = useState(false);
  const [profileData, setProfileData] = useState(null);

  const [toggleModalChiTietThanhToan, setToggleModalChiTietThanhToan] = useState(false);
  const [chiTietThanhToanSelected, setChiTietThanhToanSelected] = useState(null);

  useEffect(() => {
    route.params.profileDetail && getHoSoTrinhThanhToan(route.params.profileDetail);
  }, []);
  useEffect(() => {
    let profileDetail = notificationFirebase;
  }, [notificationFirebase]);

  const onRefresh = () => {
    setRefreshing(true);
    getHoSoTrinhThanhToan(profileData);
  };

  //lấy hồ sơ phê duyệt
  const getHoSoTrinhThanhToan = async (profileDetail) => {
    let params = {
      ma_doi_tac: profileDetail.thong_tin_chung?.ma_doi_tac || profileDetail.ma_doi_tac,
      bt: profileDetail.thong_tin_chung?.bt || profileDetail.bt,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.APPROVAL_PROFILE_DETAIL, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      response.data_info.ho_so = response.data_info.thong_tin_chung;
      setProfileData(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //nút DUYỆT
  const onPressDuyet = () => {
    prompt(
      'Thông báo',
      'Bạn có muốn duyệt hồ sơ',
      [
        {text: 'Để sau', style: 'cancel'},
        {
          text: 'Đồng ý',
          onPress: async (noi_dung) => {
            if (!noi_dung.trim()) {
              FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập nội dung duyệt', 'info');
              return;
            }
            let params = {
              noi_dung,
              bt: profileData.ho_so.bt,
            };
            try {
              let response = await ESmartClaimEndpoint.approve(AxiosConfig.ACTION_CODE.APPROVAL_PROFILE, params);
              if (!response || !response.state_info || response.state_info.status !== 'OK') return;
              getHoSoTrinhThanhToan(profileData);
              FlashMessageHelper.showFlashMessage('Thông báo', 'Đã thực hiện Duyệt thành công', 'success');
            } catch (error) {
              Alert.alert('Thông báo', error.message);
            }
          },
        },
      ],
      {
        defaultValue: 'Đồng ý',
        placeholder: 'Nội dung duyệt',
      },
    );
  };
  //nút HUỶ DUYỆT
  const onPressHuyDuyet = () => {
    Alert.alert('Thông báo', 'Bạn có muốn huỷ duyệt hồ sơ này', [
      {
        text: 'Để sau',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          let params = {
            so_id: profileData.ho_so.so_id,
            nv: DATA_CONSTANT.APPROVAL_PROFILE_NGHIEP_VU.XE,
            loai: profileData.ho_so.loai,
            bt: profileData.ho_so.bt,
            ma_doi_tac_xl: profileData.ho_so.ma_doi_tac_xl,
          };
          try {
            let response = await ESmartClaimEndpoint.unapprove(AxiosConfig.ACTION_CODE.APPROVAL_CANCEL, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            // getHoSoTrinhThanhToan(profileData);
            FlashMessageHelper.showFlashMessage('Thông báo', 'Đã thực hiện Huỷ duyệt thành công', 'success');
            NavigationUtil.pop();
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  // NÚT TỪ CHỐI DUYỆT
  const onPressTuChoiDuyet = () => {
    prompt(
      'Thông báo',
      'Bạn có muốn từ chối duyệt hồ sơ này',
      [
        {text: 'Để sau', style: 'cancel'},
        {
          text: 'Đồng ý',
          onPress: async (noi_dung) => {
            if (!noi_dung.trim()) {
              FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập nội dung duyệt', 'info');
              return;
            }

            let params = {
              ma_doi_tac_xl: profileData.ho_so.ma_doi_tac_xl,
              so_id: profileData.ho_so.so_id,
              bt: profileData.ho_so.bt,
              nv: profileData.ho_so.nv,
              loai: profileData.ho_so.loai,
              noi_dung,
            };

            try {
              let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.TU_CHOI_DUYET, params);
              if (!response || !response.state_info || response.state_info.status !== 'OK') return;
              FlashMessageHelper.showFlashMessage('Thông báo', 'Từ chối duyệt thành công', 'success');
              NavigationUtil.pop();
            } catch (error) {
              Alert.alert('Thông báo', error.message);
            }
          },
        },
      ],
      {
        placeholder: 'Nội dung duyệt',
      },
    );
  };

  /** RENDER */
  //render ra nút của Hồ sơ phê duyệt
  const renderApprovalButton = () => {
    const {ho_so} = profileData;
    return (
      <>
        {/* NÚT TỪ CHỐI DUYỆT */}
        {ho_so.trang_thai_duyet && ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.CHUA_DUYET && (
          <ButtonLinear title="Từ chối duyệt" onPress={onPressTuChoiDuyet} linearStyle={{marginHorizontal: 10}} />
        )}

        {/* NÚT DUYỆT */}
        {ho_so.trang_thai_duyet && (ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.CHUA_DUYET || ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.TU_CHOI_DUYET) && (
          <ButtonLinear title="Duyệt" onPress={onPressDuyet} linearStyle={{marginHorizontal: 10}} />
        )}
        {/* NÚT TỪ CHỐI */}
        {ho_so.trang_thai_duyet && ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.DA_DUYET && (
          <ButtonLinear title="Huỷ duyệt" onPress={onPressHuyDuyet} linearStyle={{marginHorizontal: spacing.small}} />
        )}
      </>
    );
  };

  const renderItemThanhToan = ({item, index}) => {
    return (
      <TouchableOpacity
        style={{
          ...styles.itemThanhToanView,
          backgroundColor: index % 2 == 0 ? colors.WHITE2 : '#FFF',
        }}
        onPress={() => {
          setChiTietThanhToanSelected(item);
          setToggleModalChiTietThanhToan(true);
        }}>
        <Text children={item.so_hs} style={styles.txtThanhToan} />
        {/* <Text children={item.tk_cmt} style={styles.txtThanhToan} /> */}
        <NumericFormat value={item.tien} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtThanhToan} />} />
        <Icon.Ionicons name="open-outline" size={20} />
      </TouchableOpacity>
    );
  };
  const renderBangDeNghiThanhToan = () => {
    return (
      <>
        <View style={[styles.inforHeaderView]}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name="bank" size={15} style={styles.iconBtnTopLeftView} />
              <Text children="Bảng đề nghị thanh toán bồi thường" />
            </View>
          </View>
        </View>
        <View style={{paddingHorizontal: spacing.small}}>
          <View style={styles.titleBangView}>
            <Text children={'Hồ sơ'} style={styles.titleBang} />
            {/* <Text children={'Tài khoản'} style={styles.titleBang} /> */}
            <Text children={'Số tiền'} style={styles.titleBang} />
            <Icon.Ionicons name="open-outline" size={20} color={'transparent'} />
          </View>
          <FlatList data={profileData.thanh_toan} key={(item) => item.id.toString()} renderItem={renderItemThanhToan} />
          <View style={styles.tongCongView}>
            <Text children={'Tổng cộng'} style={styles.txtTongCong} />
            <NumericFormat value={profileData.thong_tin_chung.tien} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.valueTongCong} />} />
            <Icon.Ionicons name="open-outline" size={20} color={'transparent'} />
          </View>
        </View>
      </>
    );
  };
  const renderXemFileToTrinh = () => {
    return (
      <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {profileData, prevScreen: SCREEN_ROUTER_APP.TRINH_THANH_TOAN})}>
        <View activeOpacity={1} style={[styles.headerCollap]}>
          <View style={{flexDirection: 'row'}}>
            <Icon.FontAwesome name={'file-text-o'} size={15} style={styles.iconBtnTopLeftView} />
            <Text children="Xem file tờ trình" />
          </View>
        </View>
        <Icon.SimpleLineIcons name={'arrow-right'} size={15} style={styles.iconBtnTopRightView} />
      </TouchableOpacity>
    );
  };
  const renderThongTinChung = () => {
    return (
      <View style={{marginVertical: spacing.small}}>
        <View style={[styles.inforHeaderView]}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name="bank" size={15} style={styles.iconBtnTopLeftView} />
              <Text children="Chi tiết thanh toán" />
            </View>
          </View>
        </View>
        <View style={{marginHorizontal: spacing.small}}>
          <View style={styles.inforView}>
            <Text children="Người yêu cầu" style={styles.txtTitle} />
            <Text children={profileData.thong_tin_chung.nsd} style={styles.txtDetail} />
          </View>
          {/* <View style={styles.inforView}>
            <Text children="Tài khoản chi" style={styles.txtTitle} />
            <Text children={profileData.thong_tin_chung.ma_tk} style={styles.txtDetail} />
          </View> */}
          {/* <View style={styles.inforView}>
            <Text children="Nội dung thanh toán" style={styles.txtTitle} />
            <Text children={profileData.thong_tin_chung.nd} style={styles.txtDetail} />
          </View> */}
          <View style={styles.inforView}>
            <Text children="Ngày yêu cầu" style={styles.txtTitle} />
            <Text children={profileData.thong_tin_chung.ngay_yc} style={styles.txtDetail} />
          </View>
        </View>
        {renderXemFileToTrinh()}
      </View>
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle={'Chi tiết phê duyệt'}
      renderView={
        <View style={styles.container}>
          {profileData && (
            <>
              {renderThongTinChung()}
              {renderBangDeNghiThanhToan()}
              <View style={styles.footerView}>{renderApprovalButton()}</View>
            </>
          )}
          <ModalChiTietThanhToan toggleModal={toggleModalChiTietThanhToan} setToggleModal={setToggleModalChiTietThanhToan} chiTietThanhToan={chiTietThanhToanSelected} />
        </View>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  user: state.user.data,
  notificationFirebase: state.notificationFirebase.data,
});

const mapDispatchToProps = {};

const TrinhThanhToanScreenConnect = connect(mapStateToProps, mapDispatchToProps)(TrinhThanhToanScreenComponent);
export const TrinhThanhToanScreen = memo(TrinhThanhToanScreenConnect, isEqual);
