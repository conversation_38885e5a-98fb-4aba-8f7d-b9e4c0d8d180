import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectType2} from '@app/redux/slices/CategoryCommonSlice';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import {NGHIEP_VU, SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Linking, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {ICON_HEADER, profileHeaderTitle} from './Constant';

const TabThongTinHoSoPheDuyetComponent = ({profileData, categoryCommon}) => {
  let scrollViewModalRef = useRef(null);
  const type2 = useSelector(selectType2);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState((dimensions.height / 3) * 4);

  const [toggleModal, setToggleModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalData, setModalData] = useState([]);

  const onPressHeader = (headerTitle, modalData) => {
    if (headerTitle === profileHeaderTitle[5]) onPressViewFileDetail();
    else if (headerTitle === profileHeaderTitle[2] || headerTitle === profileHeaderTitle[4] || headerTitle === profileHeaderTitle[7] || headerTitle === profileHeaderTitle[8]) {
      setModalTitle(headerTitle);
      setModalData(modalData);
      setToggleModal(!toggleModal);
    } else if (headerTitle === profileHeaderTitle[6]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAY_CHUNG_NHAN, {
        giayChungNhan: {
          ma_doi_tac: profileData.ho_so.ma_doi_tac,
          ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh_ql,
          nv: profileData.ho_so.nghiep_vu,
          so_id_hdong: profileData.ho_so.so_id_hd,
          so_id_gcn: profileData.ho_so.so_id_dt,
          so_gcn: profileData?.ho_so?.gcn,
        },
        loaiHinhNghiepVu: profileData.lh_nv,
      });
    } else if (headerTitle === profileHeaderTitle[9]) NavigationUtil.push(SCREEN_ROUTER_APP.LICH_SU_BOI_THUONG_PHE_DUYET, {profileData});
    else if (headerTitle === profileHeaderTitle[10]) NavigationUtil.push(SCREEN_ROUTER_APP.Y_KIEN_TRAO_DOI_PHE_DUYET, {profileData});
    else if (headerTitle === profileHeaderTitle[3]) NavigationUtil.push(SCREEN_ROUTER_APP.QUA_TRINH_XL_HS_PHE_DUYET, {profileData});
  };

  const onPressViewFileDetail = () => {
    NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
      profileData,
      prevScreen: SCREEN_ROUTER_APP.APPROVAL_PROFILE,
    });
  };

  /* RENDER */
  // RENDER header các đầu mục
  const renderProfileInformationHeader = (title, data) => {
    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => onPressHeader(title, data)}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name={ICON_HEADER[title]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>
                {title}
                {' ' + dataLength}
              </Text>
            </View>
          </View>
          {title !== profileHeaderTitle[0] && <Icon.SimpleLineIcons name={'arrow-right'} size={15} style={styles.iconBtnTopRightView} />}
        </TouchableOpacity>
        {title !== profileHeaderTitle[0] && (
          <View style={{backgroundColor: colors.WHITE5}}>
            <View
              style={{
                paddingVertical: spacing.smaller,
                backgroundColor: colors.WHITE,
                borderTopLeftRadius: 20,
                borderBottomLeftRadius: 20,
              }}
            />
          </View>
        )}
      </View>
    );
  };
  //render ra thông tin hồ sơ
  const renderProfileInformation = () => {
    if (!profileData || !profileData.ho_so) return;
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View style={styles.profileContent}>
          {renderProfileInformationHeader(profileHeaderTitle[0])}
          <View style={styles.contentRowView}>
            <View style={styles.inforView}>
              <Text font="regular12" style={styles.txtTitle}>
                Số hồ sơ
              </Text>
              <Text style={styles.txtDetail} selectable>
                {profileData.ho_so.so_hs}
              </Text>
            </View>
          </View>
          <View style={styles.contentRowView}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Tên chủ xe</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.chu_xe}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Biển xe</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.doi_tuong}</Text>
            </View>
          </View>
          <View style={styles.contentRowView}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Người liên hệ</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.nguoi_lhe}</Text>
            </View>
            {profileData.ho_so.dthoai_lhe && (
              <View style={styles.inforView}>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${profileData.ho_so.dthoai_lhe}`)}>
                  <Text style={styles.txtTitle}>Điện thoại</Text>
                  <View style={styles.phoneRow}>
                    <Icon.Entypo name="old-phone" size={20} style={{marginRight: spacing.tiny}} color={colors.PRIMARY} />
                    <Text style={[styles.txtDetail, {color: colors.PRIMARY}]}>{profileData.ho_so.dthoai_lhe}</Text>
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
          <View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số hợp đồng</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.so_hd}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Giấy chứng nhận</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.gcn}</Text>
            </View>
          </View>

          <View style={styles.inforView}>
            <Text style={styles.txtTitle}>Nội dung trình</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.dia_diem_gd || profileData.ho_so.noi_dung}</Text>
          </View>

          <View style={styles.contentRowView}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Trạng thái</Text>
              <Text style={[styles.txtDetail, {color: colors.GREEN}]}>{profileData.ho_so.trang_thai}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số tiền duyệt</Text>
              <NumericFormat value={profileData.ho_so.tien} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.txtDetail}>{value}</Text>} />
            </View>
          </View>
          {/* <View style={{...styles.inforView, borderBottomWidth: 0}}>
            <Text style={styles.txtTitle}>Cán bộ thụ lý</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.ten_gdvtt || ''}</Text>
          </View> */}
        </View>
        {/* Chi tiết GCN */}
        {renderProfileInformationHeader(profileHeaderTitle[6])}
        {/* DANH SÁCH NGƯỜI PHÊ DUYỆT */}
        {renderProfileInformationHeader(profileHeaderTitle[8], profileData.trinh_duyet)}
        {/* DANH SÁCH CÁC VỤ TỔN THẤT */}
        {renderProfileInformationHeader(profileHeaderTitle[2], profileData.dien_bien)}
        {/* QUÁ TRÌNH GIẢI QUYẾT */}
        {renderProfileInformationHeader(profileHeaderTitle[3], profileData.thong_tin_qtxl)}
        {/* Lịch sử tổn thất */}
        {renderProfileInformationHeader(profileHeaderTitle[9])}
        {/* Ý kiến trao đổi */}
        {renderProfileInformationHeader(profileHeaderTitle[10])}

        {/* BÊN THAM GIA GIẢI QUYẾT */}
        {/* {renderProfileInformationHeader(
          profileHeaderTitle[4],
          profileData.nguoi_dd,
        )} */}

        {/* File tờ trình */}
        {/* {renderProfileInformationHeader(profileHeaderTitle[5])} */}
      </View>
    );
  };

  //render Item VỤ TỔN THẤT
  const renderAccidentItem = (item) => {
    return (
      <View style={styles.accidentView}>
        <View style={styles.accidentInforView}>
          <Text style={styles.txtTitle}>Ngày</Text>
          <Text font="regular12" style={styles.txtTime}>
            {item.ngay_xr}
          </Text>
        </View>
        <View style={styles.accidentContent}>
          <View style={styles.accidentInforView}>
            <Text style={styles.txtTitle}>Nguyên nhân</Text>
            <Text style={styles.txtAccidentDetail}>{item.nguyen_nhan}</Text>
          </View>
          <View style={styles.accidentInforView}>
            <Text style={styles.txtTitle}>Hậu quả</Text>
            <Text style={styles.txtAccidentDetail}>{item.hau_qua}</Text>
          </View>
        </View>
        <View style={styles.accidentFooter}>
          <View
            style={{
              flexDirection: 'row',
              flex: 1,
            }}>
            <View style={[styles.accidentInforView, {flex: 1}]}>
              <Text style={styles.txtTitle}>Địa điểm</Text>
              <Text style={styles.txtAccidentDetail}>{item.dia_diem}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  //RENDER ITEM QUÁ TRÌNH GIẢI QUYẾT
  const renderResolveItem = (item) => {
    return (
      <View style={styles.resolveAccidentView}>
        <View style={styles.accidentHeader}>
          <Text style={styles.txtTitle}>{item.ngay}</Text>
          <Text font="regular12" style={styles.txtLocation}>
            {item.ten}
          </Text>
        </View>
        <View style={styles.accidentContent}>
          <View style={styles.resolveAccidentInforView}>
            <Text style={styles.txtAccidentDetail}>Nội dung: {item.nd}</Text>
          </View>
        </View>
      </View>
    );
  };
  //RENDER ITEM CÁC BÊN THAM GIA
  const renderJoinResolveItem = (item) => {
    let daiDien = '';
    for (let i = 0; i < type2.length; i++) {
      if (item.dai_dien === type2[i].value) {
        daiDien = type2[i].label;
        break;
      }
    }
    return (
      <TouchableOpacity
        style={styles.joinResolveView}
        onPress={() => {
          setToggleModal(false);
          NavigationUtil.push(SCREEN_ROUTER_APP.JOIN_RESOLVE, {
            profileData: profileData,
            joinResolveData: item,
          });
        }}>
        <View style={{flex: 1}}>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Đại diện</Text>
            <Text style={styles.txtDetail}>{daiDien}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Họ tên</Text>
            <Text style={styles.txtDetail}>{item.ten}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Điện thoại</Text>
            <Text style={styles.txtDetail}>{item.dien_thoai}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Email</Text>
            <Text style={styles.txtDetail}>{item.email}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Lần giám định</Text>
            <Text style={styles.txtDetail}>{item.lan_gd_hthi}</Text>
          </View>
        </View>
        <View style={{flex: 0}}>
          <Icon.FontAwesome5 name="user-edit" size={20} color={colors.BLACK} />
        </View>
      </TouchableOpacity>
    );
  };

  //RENDER ITEM NGƯỜI PHÊ DUYỆT
  const renderNguoiPheDuyetItem = (item) => {
    return (
      <View style={styles.nguoiPheDuyetItemView}>
        <View style={{flex: 1}}>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Họ tên</Text>
            <Text font="regular12" style={styles.txtHoTenNguoiPheDuyet}>
              {item.ten_nguoi_duyet}
            </Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Ngày duyệt</Text>
            <Text style={styles.txtHoTenNguoiPheDuyet}>{item.ngay_duyet ? item.ngay_duyet : '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Trạng thái</Text>
            <Text font="regular12" style={[styles.txtTrangThaiNguoiPheDuyet, {color: item.trang_thai === 'D' ? colors.GREEN : colors.ORANGE}]}>
              {item.trang_thai_hthi}
            </Text>
          </View>
        </View>
        <View style={styles.iconPheDuyetView}>
          {item.trang_thai === 'D' ? <Icon.AntDesign name="checkcircle" size={25} color={colors.GREEN} /> : <Icon.FontAwesome name="warning" color={colors.ORANGE} size={25} />}
        </View>
      </View>
    );
  };
  const renderItemModal = ({item}) => {
    //Danh sách vụ tổn thất
    if (modalTitle === profileHeaderTitle[2]) return renderAccidentItem(item);
    //bên tham gia giải quyết
    else if (modalTitle === profileHeaderTitle[4]) return renderJoinResolveItem(item);
    //người phê duyệt
    else if (modalTitle === profileHeaderTitle[8]) return renderNguoiPheDuyetItem(item);
  };
  const renderModal = () => {
    return (
      <Modal
        isVisible={toggleModal}
        onSwipeComplete={() => setToggleModal(false)}
        onBackdropPress={() => setToggleModal(false)}
        onBackButtonPress={() => setToggleModal(false)}
        // swipeDirection={['down']} // cho vào thì Flatlist không scroll dc
        scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
        scrollOffset={scrollOffSet}
        scrollOffsetMax={(dimensions.height * 3) / 4 - flatListHeight} // content height - ScrollView height
        // propagateSwipe={true} // cho vào thì Flatlist không scroll dc
        style={styles.modal}>
        <View style={styles.modalView}>
          <View style={styles.modalTitleView}>
            <Text font="regular18" style={styles.modalTitle}>
              {modalTitle}
            </Text>
            <TouchableOpacity style={styles.closeView} onPress={() => setToggleModal(false)}>
              <Icon.AntDesign name="closecircleo" size={20} />
            </TouchableOpacity>
          </View>

          <ScrollView ref={scrollViewModalRef} onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)} scrollEventThrottle={16} showsVerticalScrollIndicator={false}>
            <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>
              <FlatList data={modalData} renderItem={renderItemModal} keyExtractor={(item, index) => index.toString()} showsVerticalScrollIndicator={false} />
            </View>
          </ScrollView>
        </View>
        {modalTitle === profileHeaderTitle[4] && (
          <TouchableOpacity
            style={styles.btnAddJoinResolve}
            onPress={() => {
              setToggleModal(false);
              NavigationUtil.push(SCREEN_ROUTER_APP.JOIN_RESOLVE, {
                profileData: profileData,
              });
            }}>
            <Icon.FontAwesome5 name="user-plus" size={20} color={colors.PRIMARY} style={styles.iconAddJoinResolve} />
          </TouchableOpacity>
        )}
      </Modal>
    );
  };

  const renderHoSoGiamDinh = () => {
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View style={styles.profileContent}>
          {renderProfileInformationHeader(profileHeaderTitle[0])}
          <View style={styles.contentRowView}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số hồ sơ</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.so_hs}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số HSBT</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.so_hs_bt}</Text>
            </View>
          </View>
          <View style={styles.inforView}>
            <Text style={styles.txtTitle}>Nghiệp vụ</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.nv_bt}</Text>
          </View>
          <View style={styles.contentRowView}>
            {profileData.ho_so.dthoai_lhe && (
              <View style={styles.inforView}>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${profileData.ho_so.dthoai_lhe}`)}>
                  <Text style={styles.txtTitle}>Điện thoại</Text>
                  <View style={styles.phoneRow}>
                    <Icon.Entypo name="old-phone" size={20} style={{marginRight: 5}} color={colors.PRIMARY} />
                    <Text style={styles.txtDetail}>{profileData.ho_so.dthoai_lhe}</Text>
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
          <View style={styles.inforView}>
            <Text style={styles.txtTitle}>Nội dung trình</Text>
            <Text style={styles.txtDetail}>{profileData.ho_so.dia_diem_gd || profileData.ho_so.noi_dung}</Text>
          </View>

          <View style={styles.contentRowView}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Trạng thái</Text>
              <Text style={styles.txtDetail}>{profileData.ho_so.trang_thai}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số tiền duyệt</Text>
              <NumericFormat value={profileData.ho_so.tien} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text>{value}</Text>} />
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <>
      {profileData?.ho_so?.nv === NGHIEP_VU.HSGD ? renderHoSoGiamDinh() : renderProfileInformation()}
      {renderModal()}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    marginTop: spacing.tiny,
    flex: 1,
  },
  btnTop: {
    flexDirection: 'row',
    alignItems: 'stretch',
    width: dimensions.width / 2,
    justifyContent: 'center',
    padding: spacing.smaller,
    backgroundColor: colors.WHITE4,
    borderColor: colors.GRAY,
    borderBottomWidth: 1,
    borderRightWidth: 1,
  },
  btnActive: {
    backgroundColor: colors.WHITE,
    borderBottomWidth: 0,
  },
  iconBtnTopLeftView: {
    marginRight: spacing.small,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    marginHorizontal: spacing.small,
    alignSelf: 'center',
  },
  btnTopView: {
    flexDirection: 'row',
    width: dimensions.width,
  },
  topView: {},
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.small,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
  },
  txtTitle: {
    marginBottom: spacing.tiny,
  },
  txtDetail: {
    color: colors.GRAY6,
    paddingRight: spacing.small,
  },

  txtAccidentDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
  },
  inforView: {
    flex: 1,
    borderBottomWidth: 0.3,
    paddingLeft: spacing.small,
    paddingVertical: spacing.smaller,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
    borderTopLeftRadius: 20,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: dimensions.width - 20,
  },
  accidentHeader: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.small,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtTime: {
    color: colors.GRAY6,
  },
  accidentFooter: {},
  accidentView: {
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
  },

  txtLocation: {
    color: colors.GREEN2,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  accidentInforView: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.small,
  },
  resolveAccidentInforView: {
    paddingHorizontal: spacing.small,
  },
  resolveAccidentView: {
    paddingBottom: spacing.small,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: (dimensions.height * 3) / 4,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
  },
  closeView: {
    position: 'absolute',
    top: 15,
    right: 15,
  },

  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalTitleView: {
    height: 50,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    justifyContent: 'center',
  },
  joinResolveDetailView: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: spacing.tiny,
  },
  joinResolveView: {
    marginTop: spacing.tiny,
    paddingHorizontal: spacing.small,
    borderColor: colors.GRAY4,
    alignItems: 'center',
    borderBottomWidth: 0.5,
    flexDirection: 'row',
    flex: 1,
  },
  iconAddJoinResolve: {
    paddingVertical: 14,
    paddingHorizontal: 12,
  },
  nguoiPheDuyetItemView: {
    marginTop: spacing.tiny,
    paddingHorizontal: spacing.small,
    borderColor: colors.GRAY4,
    borderBottomWidth: 0.5,
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  iconPheDuyetView: {
    marginHorizontal: spacing.small,
  },
  txtHoTenNguoiPheDuyet: {
    marginBottom: spacing.tiny,
    fontWeight: 'bold',
  },
  txtTrangThaiNguoiPheDuyet: {
    marginBottom: spacing.tiny,
    fontWeight: 'bold',
  },
  profileContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  contentRowView: {
    flex: 1,
    flexDirection: 'row',
  },
  phoneRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export const TabThongTinHoSoPheDuyet = memo(TabThongTinHoSoPheDuyetComponent, isEqual);
