import R from '@R';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {DropdownPicker, Icon, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, FlatList, Image, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import Progress from 'react-native-progress/Circle';
import {anhTaiLieuDropdown, extensionsFile} from './Constant';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';

const TabTaiLieuBoiThuongPheDuyetComponent = forwardRef((props, ref) => {
  const {
    dataAnhCapDon,
    xemTaiLieuSelected,
    setXemTaiLieuSelected,
    imageDataStep1,
    imageDataStep2,
    imageDataStep3,
    imageDataStep4,
    anhThuHoiVatTu,
    onPressOpenImageView,
    onPressToggleExpandHangMuc,
    listTaiLieuPdf,
    onPressExpandAllHangMuc,
    profileData,
  } = props;

  useImperativeHandle(ref, () => ({
    setViTriHangMucDangTai: (value) => setViTriHangMucDangTai(value),
  }));

  const [openDropdownLoaiTaiLieu, setOpenDropdownLoaiTaiLieu] = useState(false);
  const [hangMucSelected, setHangMucSelected] = useState(null);
  const [viTriHangMucDangTai, setViTriHangMucDangTai] = useState(null);
  const [expandAllHangMuc, setExpandAllHangMuc] = useState(false);

  useEffect(() => {
    let coHangMucExpand = false;
    let imageDataTmp = [];
    if (xemTaiLieuSelected === 'ANH_HO_SO') imageDataTmp = imageDataStep4;
    else if (xemTaiLieuSelected === 'ANH_TON_THAT') imageDataTmp = imageDataStep3;
    imageDataTmp.map((hangMucAnh) => {
      if (hangMucAnh.expanded) coHangMucExpand = true;
    });
    setExpandAllHangMuc(coHangMucExpand);
  }, [xemTaiLieuSelected, imageDataStep4, imageDataStep3]);

  /**RENDER */
  // không có dữ liệu
  const renderNoData = () => (
    <View style={styles.noDataView}>
      <Image
        source={R.images.img_no_data}
        style={{
          width: dimensions.width / 5,
          height: dimensions.width / 5,
        }}
        resizeMode={'contain'}
      />
      <Text style={{color: colors.GRAY7}}>Chưa có dữ liệu</Text>
    </View>
  );

  const renderPdfItem = ({item, index}) => {
    let iconName = '',
      iconColor = '';
    if (item.extension === extensionsFile[0]) {
      iconName = 'file-pdf-box';
      iconColor = colors.RED2;
    } else if (item.extension === extensionsFile[1] || item.extension === extensionsFile[2]) {
      iconName = 'file-word';
      iconColor = colors.BLUE2;
    } else if (item.extension === extensionsFile[3]) {
      iconName = 'file-code-outline';
      iconColor = colors.ORANGE;
    } else if (item.extension === extensionsFile[4] || item.extension === extensionsFile[5]) {
      iconName = 'file-excel';
      iconColor = colors.GREEN3;
    }

    return (
      <View>
        <Text style={[styles.headerSubTitle]} font="medium14" children={item.ten_hang_muc} />
        <TouchableOpacity onPress={() => onPressOpenImageView({item: item})}>
          <View style={styles.imageDocument}>
            <Icon.MaterialCommunityIcons name={iconName} color={iconColor} size={dimensions.width / 4} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderListPdf = () => (
    <FlatList
      scrollEnabled={false}
      data={listTaiLieuPdf}
      renderItem={renderPdfItem}
      keyExtractor={(item, index) => index.toString()}
      style={{marginBottom: spacing.huge, marginLeft: spacing.small}}
      ListEmptyComponent={renderNoData()}
    />
  );

  const renderImageItemStep12 = (data, {listImage}) => {
    let imageData = data.item;
    let source = {uri: `data:image/gif;base64,${imageData.duong_dan}`};
    if (xemTaiLieuSelected === 'ANH_CAP_DON') source = {uri: imageData.pa_att_url};
    return (
      <TouchableOpacity onPress={() => onPressOpenImageView(data, listImage)}>
        <ImageProcess
          source={source}
          indicator={Progress.Circle}
          style={styles.imageDocument}
          imageStyle={{borderRadius: 20}}
          indicatorProps={{
            size: 70,
            borderWidth: 0,
            color: colors.PRIMARY,
            unfilledColor: colors.PRIMARY_LIGHT,
          }}
          renderError={() => (
            <View>
              <Image source={R.images.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />
            </View>
          )}
        />

        {imageData.stt !== undefined && <Text children={imageData.stt} style={{position: 'absolute', top: 20, left: 25, color: '#FFF', fontSize: 18, fontWeight: 'bold'}} />}
        {/* {imageData.stt_hang_muc !== undefined && <Text children={imageData.stt_hang_muc} style={{position: 'absolute', top: 40, left: 25, color: '#FFF', fontSize: 18, fontWeight: 'bold'}} />} */}
      </TouchableOpacity>
    );
  };
  const renderImageStep12 = (title, imageData) => (
    <FlatList
      scrollEnabled={false}
      data={imageData}
      renderItem={(itemImage) => renderImageItemStep12(itemImage, {listImage: imageData})}
      keyExtractor={(_, index) => index.toString()}
      numColumns={3}
      horizontal={false}
      style={{marginBottom: spacing.small}}
      ListEmptyComponent={renderNoData()}
      ListHeaderComponent={
        <View style={styles.headerView}>
          <Text style={[styles.headerSubTitle]} font="medium14" children={title} />
        </View>
      }
    />
  );

  const renderImageItemStep34 = ({item, index}) => {
    item.images = item.images.sort(function (a, b) {
      return a.stt - b.stt;
    });
    let {hang_muc} = item;

    return (
      <View style={{backgroundColor: index % 2 == 0 ? colors.WHITE8 : '#FFF', paddingVertical: spacing.small, borderBottomWidth: 0.5, borderColor: '#CCC'}}>
        <TouchableOpacity
          style={styles.headerItemHangMucView}
          onPress={(event) => {
            onPressToggleExpandHangMuc(index);
            setHangMucSelected(index);
          }}>
          <View style={[styles.headerTitleView]}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <TouchableOpacity
                onPress={() => {
                  if (xemTaiLieuSelected === 'ANH_TOAN_CANH') return;
                  onPressToggleExpandHangMuc(index);
                  setHangMucSelected(index);
                }}
                style={{flex: 1}}>
                <Text style={[styles.headerSubTitle]} font="medium14" color={index === hangMucSelected && item.expanded ? colors.ORANGE : '#000'} children={item.ten} />
              </TouchableOpacity>
            </View>
            {/* CHỈ HIỂN THỊ THÔNG TIN ĐÁNH GIÁ VỚI ẢNH TỔN THẤT */}
            {xemTaiLieuSelected === 'ANH_TON_THAT' && (
              <>
                {hang_muc.muc_do_ten && (
                  <TouchableOpacity
                    style={styles.thongTinDanhGiaView}
                    onPress={() => {
                      onPressToggleExpandHangMuc(index);
                      setHangMucSelected(index);
                    }}>
                    <Text
                      style={{fontStyle: 'italic'}}
                      font="regular12"
                      children={`${hang_muc.muc_do_ten} / ${hang_muc.thay_the_sc === 'S' ? 'Sữa chữa' : 'Thay thế'} / ${hang_muc.thu_hoi === 'K' ? 'Không thu hồi' : 'Có thu hồi'}`}
                    />
                    {/* <NumericFormat
                      value={hang_muc.gia_giam_dinh}
                      displayType={'text'}
                      thousandSeparator={true}
                      renderText={(value) => <Text style={{fontStyle: 'italic'}} font="regular12" children={value + ' VNĐ'} />}
                    /> */}
                  </TouchableOpacity>
                )}
                {/* NẾU CHƯA CÓ MỨC ĐỘ TÊN -> HIỂN THỊ HẠNG MỤC NÀY CHƯA ĐÁNH GIÁ*/}
                {!hang_muc.muc_do_ten && (
                  <View style={styles.thongTinDanhGiaView}>
                    <Icon.Entypo name="warning" size={14} color={colors.ORANGE} />
                    <Text children="Chưa đánh giá" color={colors.ORANGE} style={{fontStyle: 'italic'}} font="regular12" />
                  </View>
                )}
              </>
            )}
          </View>
          {/* HIỂN THỊ NÚT EXPAND/COLPAN VỚI ẢNH TỔN THẤT VÀ ẢNH HỒ SƠ */}
          {(xemTaiLieuSelected === 'ANH_TON_THAT' || xemTaiLieuSelected === 'ANH_HO_SO' || item.ma === 'ANH_NGHIEM_THU') && (
            <>
              {viTriHangMucDangTai === index && <ActivityIndicator size="small" color={colors.PRIMARY} />}
              <TouchableOpacity
                onPress={(event) => {
                  onPressToggleExpandHangMuc(index);
                  setHangMucSelected(index);
                }}
                style={{padding: spacing.small}}>
                <Icon.FontAwesome name={item.expanded ? 'angle-down' : 'angle-up'} size={18} color={colors.BLACK} />
              </TouchableOpacity>
            </>
          )}
        </TouchableOpacity>

        {(item.expanded || (xemTaiLieuSelected !== 'ANH_TON_THAT' && xemTaiLieuSelected !== 'ANH_HO_SO')) && (
          <FlatList
            scrollEnabled={false}
            data={item.images}
            renderItem={(itemImage) => renderImageItemStep12(itemImage, {listImage: item.images})}
            keyExtractor={(item) => item.bt.toString()}
            numColumns={3}
            horizontal={false}
          />
        )}
        {!hang_muc.hang_muc.includes('ANH_KHAC') && xemTaiLieuSelected === 'ANH_TON_THAT' && (
          <TouchableOpacity
            style={styles.traCuuGiaView}
            onPress={() =>
              NavigationUtil.push(SCREEN_ROUTER_APP.TRA_CUU_GIA_HANG_MUC_O_TO, {
                dataHangMuc: {...item, hang_xe: profileData.ho_so.hang_xe, hieu_xe: profileData.ho_so.hieu_xe, nam_sx: profileData.ho_so.nam_sx, nghiep_vu: profileData.ho_so.nghiep_vu},
              })
            }>
            <Text children="Tra cứu giá" font="regilar10" />
          </TouchableOpacity>
        )}
      </View>
    );
  };
  const renderImageStep34 = (title, imagesData) => (
    <FlatList
      scrollEnabled={false}
      data={imagesData}
      renderItem={(item) => renderImageItemStep34(item, {title: title, imagesData})}
      initialNumToRender={50}
      style={{marginBottom: spacing.huge}}
      ListEmptyComponent={renderNoData()}
      ListHeaderComponent={
        title !== 'Ảnh toàn cảnh' ? (
          <TouchableOpacity
            style={styles.headerView}
            onPress={() => {
              setExpandAllHangMuc(!expandAllHangMuc);
              onPressExpandAllHangMuc(!expandAllHangMuc);
            }}>
            <Text style={styles.headerTitle} font="medium16" children={title} />
            <View style={{padding: spacing.small}}>
              <Icon.FontAwesome name={expandAllHangMuc ? 'angle-double-down' : 'angle-double-up'} size={20} color={colors.BLACK} />
            </View>
          </TouchableOpacity>
        ) : null
      }
    />
  );

  return (
    <View>
      <DropdownPicker
        zIndex={8000}
        searchable={false}
        isOpen={openDropdownLoaiTaiLieu}
        setOpen={setOpenDropdownLoaiTaiLieu}
        items={anhTaiLieuDropdown}
        itemSelected={xemTaiLieuSelected}
        setItemSelected={setXemTaiLieuSelected}
        containerStyle={styles.dropdownLoaiAnh}
      />

      <ScrollView showsVerticalScrollIndicator={false}>
        {xemTaiLieuSelected === 'FILE_PDF' && renderListPdf()}
        {xemTaiLieuSelected === 'ANH_CAP_DON' && renderImageStep12('Ảnh cấp đơn', dataAnhCapDon)}
        {xemTaiLieuSelected === 'ANH_HIEN_TRUONG' && renderImageStep12('Ảnh hiện trường', imageDataStep1)}
        {xemTaiLieuSelected === 'ANH_TOAN_CANH' && renderImageStep34('Ảnh toàn cảnh', imageDataStep2)}
        {xemTaiLieuSelected === 'ANH_TON_THAT' && renderImageStep34('Ảnh tổn thất', imageDataStep3)}
        {xemTaiLieuSelected === 'ANH_HO_SO' && renderImageStep34('Ảnh hồ sơ, giấy tờ', imageDataStep4)}
        {xemTaiLieuSelected === 'ANH_THVT' && renderImageStep12('Ảnh thu hồi vật tư', anhThuHoiVatTu)}
      </ScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: spacing.small,
    marginVertical: spacing.small,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
  },
  headerView: {
    paddingHorizontal: spacing.small,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitleView: {
    flex: 1,
    justifyContent: 'space-between',
  },
  headerSubTitle: {
    marginRight: spacing.tiny,
  },
  checkboxImgView: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: colors.WHITE,
    borderTopLeftRadius: 5,
  },
  classifyInfoView: {
    marginVertical: spacing.tiny,
    paddingLeft: spacing.small,
    paddingRight: spacing.small,
    paddingVertical: spacing.tiny,
    borderRadius: 10,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
    marginHorizontal: spacing.small,
  },
  classifyInfoDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  classifyInfoTitle: {
    fontSize: 12,
  },
  classifyInfoValue: {
    fontWeight: 'bold',
    fontSize: 12,
  },
  switchImageViewButton: {
    marginVertical: spacing.tiny,
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: spacing.tiny,
  },
  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageItemStep34View: {
    paddingVertical: spacing.small,
    borderBottomWidth: 0.3,
    borderColor: '#CCC',
  },
  innerCircleStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropdownLoaiAnh: {
    marginBottom: spacing.smaller,
    marginHorizontal: spacing.smaller,
    marginTop: 0,
  },
  switchImageContainerView: {
    flexDirection: 'row',
    marginHorizontal: spacing.smaller,
    marginBottom: spacing.smaller,
  },
  thongTinDanhGiaView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: spacing.smaller,
  },
  headerItemHangMucView: {
    paddingHorizontal: spacing.small,
    flexDirection: 'row',
    alignItems: 'center',
  },
  traCuuGiaView: {
    borderWidth: 0.5,
    borderColor: colors.GRAY,
    flex: 1,
    paddingVertical: spacing.smaller,
    borderRadius: 8,
    marginHorizontal: spacing.default,
    alignItems: 'center',
    marginTop: spacing.default,
    backgroundColor: '#FFF',
  },
});

const TabTaiLieuBoiThuongPheDuyetMemo = memo(TabTaiLieuBoiThuongPheDuyetComponent, isEqual);
export const TabTaiLieuBoiThuongPheDuyet = TabTaiLieuBoiThuongPheDuyetMemo;
