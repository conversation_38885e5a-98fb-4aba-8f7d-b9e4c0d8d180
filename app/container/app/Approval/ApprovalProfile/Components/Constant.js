export const profileHeaderTitle = [
  'Thông tin chung', //0
  'Lị<PERSON> giám định', //1
  '<PERSON><PERSON> sách vụ tổn thất', //2
  'Qu<PERSON> trình giải quyết', //3
  'Bên tham gia giám định', //4
  'File tờ trình', //5
  'Thông tin giấy chứng nhận', //6
  'Tình trạng thanh toán', //7
  'Danh sách người phê duyệt', //8
  'Lịch sử tổn thất', //9
  'Ý kiến trao đổi', //10
];

export const ICON_HEADER = {
  [profileHeaderTitle[0]]: 'file-text-o',
  [profileHeaderTitle[1]]: 'calendar-plus-o',
  [profileHeaderTitle[2]]: 'list-ol',
  [profileHeaderTitle[3]]: 'list-ol',
  [profileHeaderTitle[4]]: 'user-circle-o',
  [profileHeaderTitle[5]]: 'file-pdf-o',
  [profileHeaderTitle[6]]: 'certificate',
  [profileHeaderTitle[7]]: 'money',
  [profileHeaderTitle[8]]: 'user-circle-o',
  [profileHeaderTitle[9]]: 'history',
  [profileHeaderTitle[10]]: 'commenting',
};

export const extensionsFile = ['.pdf', '.doc', '.docx', '.xml', '.xls', '.xlsx']; //đuôi mở rộng của file
export const extensionsImage = ['.jpg', '.jpeg', '.png', '.gif'];
export const anhTaiLieuDropdown = [
  {
    label: 'File tài liệu',
    value: 'FILE_PDF',
  },
  {
    label: 'Ảnh cấp đơn',
    value: 'ANH_CAP_DON',
  },
  {
    label: 'Ảnh hiện trường',
    value: 'ANH_HIEN_TRUONG',
  },
  {
    label: 'Ảnh toàn cảnh',
    value: 'ANH_TOAN_CANH',
  },
  {
    label: 'Ảnh tổn thất',
    value: 'ANH_TON_THAT',
  },
  {
    label: 'Ảnh hồ sơ, giấy tờ',
    value: 'ANH_HO_SO',
  },
  {
    label: 'Ảnh thu hồi vật tư',
    value: 'ANH_THVT',
  },
];
