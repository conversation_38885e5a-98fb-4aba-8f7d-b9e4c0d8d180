import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {ButtonLinear, Icon, ImageComp, Text} from '@component';
import React, {Component} from 'react';
import {FlatList, SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';

interface Props {
  scrollViewModalRef;
  setToggleModalAnhGiayTo;
  setScrollOffSet;
  imageUpload;
  openCamera;
  removeImage;
  onPressXemLaiAnh;
  onPressUploadAnh;
  disabledNutGui: boolean;
}

class ModalAnhGiayTo extends Component<Props> {
  constructor(props) {
    super(props);
  }
  renderCameraButton = () => {
    const {openCamera} = this.props;
    return (
      <TouchableOpacity style={styles.cameraView} onPress={openCamera}>
        <Icon.MaterialCommunityIcons name="camera-plus" size={40} color={colors.GRAY11} style={styles.iconCamera} />
      </TouchableOpacity>
    );
  };
  renderImageItem = (imageData) => {
    const {imageUpload, removeImage, onPressXemLaiAnh} = this.props;
    let index = imageData.index;
    if (index === imageUpload.length - 1) return this.renderCameraButton();
    return <ImageComp imageData={imageData} removeImage={removeImage} onPressXemLaiAnh={onPressXemLaiAnh} />;
  };

  render() {
    const {scrollViewModalRef, setToggleModalAnhGiayTo, setScrollOffSet, imageUpload, onPressUploadAnh, disabledNutGui = false} = this.props;
    return (
      <SafeAreaView style={styles.modalView}>
        <View style={styles.modalTitleView}>
          <Text font="regular16" style={styles.modalTitle}>
            Ảnh giấy tờ
          </Text>
          <TouchableOpacity style={styles.closeView} onPress={() => setToggleModalAnhGiayTo(false)}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>
        <ScrollView
          ref={scrollViewModalRef}
          onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={true}
          style={{height: dimensions.height / 2 - 150}}>
          <FlatList scrollEnabled={true} data={imageUpload} renderItem={this.renderImageItem} numColumns={3} horizontal={false} />
          {/* {currentPage == 0 && renderAnhXemLai(anhXemLai)}
          {(currentPage == 1 || currentPage == 2) && (
            <View>
              <FlatList
                data={anhXemLai}
                renderItem={(data) => (
                  <>
                    <Text style={styles.tieuDeDanhMucAnhXemLai}>{data.item.ten}</Text>
                    {renderAnhXemLai(data.item.images)}
                  </>
                )}
                keyExtractor={(item) => item.ma}
              />
            </View>
          )} */}
        </ScrollView>

        <ButtonLinear title="Gửi" onPress={onPressUploadAnh} linearStyle={{marginHorizontal: scale(spacing.small)}} disabled={disabledNutGui} loading={disabledNutGui} />
      </SafeAreaView>
    );
  }
}
export default ModalAnhGiayTo;

const styles = StyleSheet.create({
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginVertical: scale(spacing.small),
    flex: 1,
    marginLeft: 30,
  },
  closeView: {
    marginRight: scale(spacing.medium),
  },
  tieuDeDanhMucAnhXemLai: {
    marginLeft: scale(spacing.tiny),
    fontWeight: 'bold',
  },
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: scale(spacing.medium),
    marginVertical: vScale(spacing.medium),
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraView: {
    borderWidth: 1,
    borderColor: colors.GRAY11,
    borderStyle: 'dashed',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: scale(spacing.medium),
    marginVertical: vScale(spacing.medium),
  },
  iconCamera: {
    padding: spacing.small,
  },
});
