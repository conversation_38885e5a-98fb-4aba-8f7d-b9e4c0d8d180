import { colors } from '@app/commons/Theme';
import { CustomModal } from '@app/components/CustomModal';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { dimensions, spacing } from '@app/theme';
import { getImageNameFromUriCamera, saveToCameraRoll } from '@app/utils/CameraProvider';
import { getCauHinhHoSoByMa } from '@app/utils/DataProvider';
import { FlashMessageHelper } from '@app/utils/FlashMessageHelper';
import { ButtonLinear, Icon, ModalComp, ScreenComponent, Text } from '@component';
import { DATA_CONSTANT, REGUlAR_EXPRESSION, SCREEN_ROUTER_APP, isIOS } from '@constant';
import React, { memo, useEffect, useRef, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Alert, View } from 'react-native';
import ActionButton from 'react-native-action-button';
import { TouchableOpacity } from 'react-native-gesture-handler';
import ImageCropPicker from 'react-native-image-crop-picker';
import Pdf from 'react-native-pdf';
import Share from 'react-native-share';
import { connect } from 'react-redux';
import { RenderContentModalTraLaiHoSo, RenderContentModalXacNhanDuyet } from './Components';
import ModalAnhGiayTo from './ModalAnhGiayTo';
import ModalMailConfirm from './ModalMailConfirm';
import ModalXemChiTietAnh from './ModalXemChiTietAnh';
import styles from './PDFViewStyles';

const constHeight = dimensions.height / 2;
const mauInGuiMail = ['ESCS_BBGD_HIEN_TRUONG', 'ESCS_BBGD_XAC_DINH_THIET_HAI_XCG'];
const PDFViewScreenComponent = (props) => {
  console.log('PDFViewScreenComponent_PheDuyet');
  const { route } = props;
  const { prevScreen, viTriMauIn, actionSheetMauInData, loaiTrinh } = route.params;
  let categoryImage = route?.params?.profileData?.nhom_hang_muc ? route?.params?.profileData?.nhom_hang_muc : props.categoryImage;
  const [imagesData, setImagesData] = useState([]);
  const [imageUpload, setImageUpload] = useState([]);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState(route.params.profileData);
  const [pdfData, setPDFData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [listMail, setListMail] = useState([]);
  const [inputEmail, setInputEmail] = useState('');
  const [mailData, setMailData] = useState(null);
  const [inputErr, setInputErr] = useState(['']);
  const [toggleModalMailConfirm, setToggleModalMailConfirm] = useState(false);
  const [toggleModalImage, setToggleModalImage] = useState(false);
  const [modalImageSelectedData, setModalImageSelectedData] = useState(null);
  const [toggleModalAnhGiayTo, setToggleModalAnhGiayTo] = useState(false);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState(constHeight);
  const [mauInDuocChon, setMauInDuocChon] = useState(null);
  const [tenMauIn, setTenMauIn] = useState('Tờ trình');
  const [fileType, setFileType] = useState(1);

  let scrollViewModalRef = useRef(null);
  let refModalTraLaiHoSo = useRef(null);
  let refModalPheDuyet = useRef(null);

  /**FUNCTION  */
  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    layMauInDuocChon();
    initImageUpload();
    await getAnhDaChup();
  };
  const layMauInDuocChon = () => {
    if (route.params.dataPDF) {
      //xử lý ảnh cấp đơn OPES là giấy chứng nhận
      if (route.params.dataPDF.pa_att_url?.includes('https://url.opes.com.vn') && route.params.dataPDF.pa_att_url?.includes('.pdf')) {
        return setPDFData({ uri: route.params.dataPDF.pa_att_url });
      }
      getChiTietPDF();
      return;
    }
    if (viTriMauIn == undefined) layMauInPDF();
    //cái này là lấy PDF theo mẫu in
    else {
      let mauInDuocChon = null;
      profileData.mau_in.map((item) => {
        if (item.ten == actionSheetMauInData[viTriMauIn]) mauInDuocChon = item;
      });
      layMauInPDF(mauInDuocChon);
      setMauInDuocChon(mauInDuocChon);
    }
  };

  //lấy hồ sơ phê duyệt
  const getApproveProfile = async (profileDetail) => {
    try {
      let params = {
        ma_doi_tac: profileDetail?.thong_tin_chung?.ma_doi_tac || profileDetail.ma_doi_tac,
        bt: profileDetail?.thong_tin_chung?.bt || profileDetail.bt,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.APPROVAL_PROFILE_DETAIL, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      response.data_info.ho_so = response.data_info.thong_tin_chung;
      setProfileData(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //khởi tạo ảnh step 1
  const initImageUpload = () => {
    setImageUpload([{ path: '' }]);
  };

  const getAnhDaChup = async () => {
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
      };
      let response = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setImagesData(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const onPressSharePDF = () => {
    // if (!pdfData.filePath) {
    //   FlashMessageHelper.showFlashMessage('Thông báo', 'File chưa được tải về máy. Vui lòng thử lại', 'info');
    //   return;
    // }
    let options = {
      url: 'data:application/pdf;base64,' + pdfData.base64,
    };
    Share.open(options)
      .then((res) => {
        // console.log(res);
      })
      .catch((err) => {
        // err && console.log(err);
      });
  };

  //nút HUỶ DUYỆT
  const onPressHuyDuyet = () => {
    // return;
    Alert.alert('Thông báo', 'Bạn có muốn huỷ duyệt hồ sơ này không?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          let params = {
            so_id: profileData.ho_so.so_id,
            nv: profileData.ho_so.nv === 'THANH_TOAN' ? profileData.ho_so.nv : DATA_CONSTANT.APPROVAL_PROFILE_NGHIEP_VU.XE,
            loai: profileData.ho_so.loai,
            bt: profileData.ho_so.bt,
            ma_doi_tac_xl: profileData.ho_so.ma_doi_tac_xl,
            remove_file: profileData.ho_so.remove_file,
            ma_doi_tac: profileData.ho_so.ma_doi_tac,
            so_id_tu: profileData.thanh_toan[0].so_id_tu,
          };
          try {
            let response = await ESmartClaimEndpoint.unapprove(AxiosConfig.ACTION_CODE.APPROVAL_CANCEL, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Đã thực hiện Huỷ duyệt thành công', 'success');
            if (profileData.ho_so.nv === 'THANH_TOAN') NavigationUtil.pop(1);
            else NavigationUtil.pop(2);
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  // nút Từ chối duyệt
  const getListMailData = async () => {
    // console.log('getListMailData params', params);
    try {
      const params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac, //ma_doi_tac
        so_id: profileData.ho_so.so_id ? profileData.ho_so.so_id : profileData.thong_tin_chung.so_id_tt, //so_id_tt
        nv: profileData.ho_so.nghiep_vu,
        loai: mauInDuocChon.ma_mau_in == mauInGuiMail[0] ? 'TEMPLATE_EMAIL_XAC_NHAN_DGHT' : 'TEMPLATE_EMAIL_BBGD',
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.DANH_SACH_MAIL_NGUOI_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.out_value.email_nhan) {
        setMailData(response.out_value);
        // let arrMail = response.data.out_value.email_nhan.split(';');
        // arrMail = arrMail.map((item) => {
        //   let newItem = {
        //     value: item,
        //     checked: false,
        //   };
        //   return newItem;
        // });
        //nếu có danh sách mail nhận thì hiện thị lên modal xác nhận mail
        // setListMail(arrMail);
        setInputEmail(response.out_value.email_nhan || '');
      }
      setToggleModalMailConfirm(true);
      setInputErr(['']);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layMauInPDF = async (mauInDuocChon) => {
    setIsLoading(true);

    let maActionAPI = mauInDuocChon ? mauInDuocChon.ma_action_api : profileData.ho_so.ma_action_api;
    try {
      const params = {
        ma_mau_in: mauInDuocChon ? mauInDuocChon.ma_mau_in : profileData.ho_so.ma_mau_in,
        ma_doi_tac: mauInDuocChon?.ma_doi_tac ? mauInDuocChon.ma_doi_tac : profileData.ho_so.ma_doi_tac, //ma_doi_tac
        so_id: profileData.ho_so.so_id ? profileData.ho_so.so_id : profileData.thong_tin_chung.so_id_tt, //so_id_tt
        url_file: mauInDuocChon ? mauInDuocChon.url_file : profileData.ho_so.url_file || '', // để trống
        loai: profileData?.ho_so?.hanh_dong ? profileData?.ho_so?.hanh_dong : '',
      };
      let response = await ESmartClaimEndpoint.exportPdfBase64(maActionAPI, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let pdfData = {
        base64: response.data_info.base64_string,
        filePath: '',
      };
      setPDFData(pdfData);
      if (mauInDuocChon?.ma_mau_in === 'ESCS_TO_TRINH_PHUONG_AN_SUA_CHUA') {
        setFileType(1);
        setTenMauIn('Tờ trình');
      } else if (mauInDuocChon?.ma_mau_in === 'ESCS_THONG_BAO_DUYET_BAO_LANH') {
        setFileType(0);
        setTenMauIn('Thư bảo lãnh');
      }
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getChiTietPDF = async () => {
    setIsLoading(true);
    try {
      let params = {
        so_id: route.params.dataPDF.so_id,
        bt: route.params.dataPDF.bt,
        ma_file: route.params.dataPDF.ma_file,
        ma_chi_nhanh: route.params.dataPDF.ma_chi_nhanh,
        pm: '',
      };
      let response = await ESmartClaimEndpoint.getFile(AxiosConfig.ACTION_CODE.DOCUMENT_DETAIL, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let pdfData = {
        base64: response.data_info.duong_dan,
        filePath: '',
      };
      setPDFData(pdfData);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const onPressGuiMail = async () => {
    try {
      if (!inputEmail.trim()) {
        setInputErr(['Vui lòng nhập email']);
        return;
      } else if (!REGUlAR_EXPRESSION.REG_EMAIL.test(inputEmail.trim())) {
        setInputErr(['Email không hợp lệ']);
        return;
      }
      let params = {
        ma_doi_tac: mauInDuocChon ? mauInDuocChon.ma_doi_tac : profileData.ho_so.ma_doi_tac, //ma_doi_tac
        so_id: profileData.ho_so.so_id ? profileData.ho_so.so_id : profileData.thong_tin_chung.so_id_tt, //so_id_tt
        nv: 'XE',
        loai: mauInDuocChon.ma_mau_in == mauInGuiMail[0] ? 'TEMPLATE_EMAIL_XAC_NHAN_DGHT' : 'TEMPLATE_EMAIL_BBGD',
        email_nhan: inputEmail,
        create_file: null,
        create_file_sign: mauInDuocChon.ma_mau_in,
        remove_file: mauInDuocChon.ma_mau_in,
      };
      try {
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GUI_EMAIL_XAC_NHAN, params);
        setIsLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        setToggleModalMailConfirm(false);
        FlashMessageHelper.showFlashMessage('Thông báo', 'Gửi email xác nhận thành công', 'success');
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    } catch (error) {
      Alert.alert('Thông báo', JSON.stringify(error));
    }
  };
  const onChangeEmail = (value) => {
    if (!value.trim()) setInputErr(['Vui lòng nhập email']);
    else if (!REGUlAR_EXPRESSION.REG_EMAIL.test(value.trim())) setInputErr(['Email không hợp lệ']);
    else setInputErr(['']);
    setInputEmail(value);
  };
  const addNewMail = () => {
    if (!inputEmail.trim()) return;
    let listMailTmp = listMail;
    listMailTmp.push({
      value: inputEmail,
      checked: false,
    });
    setInputEmail('');
    setListMail([...listMailTmp]);
  };
  const onChangeCheckBox = (value, index) => {
    // console.log('onChangeCheckBox', value, index);
    let listMailTmp = listMail;
    listMailTmp[index].checked = value;
    setListMail([...listMailTmp]);
  };
  const openCamera = (indexOpened, menuImageData, type) => {
    let imgCropOpts = {
      mediaType: 'photo', //mặc định là chụp ảnh từ camera
      cropping: false,
      enableRotationGesture: true,
      compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
      showCropGuidelines: true, //tắt khung 3x3 đi
      compressImageQuality: 0.5,
      useFrontCamera: false,
    };
    // if (type == 1) {
    //   imgCropOpts.cropping = true;
    //   ImageCropPicker.openPicker(imgCropOpts)
    //     .then((data) => {
    //       handleImage(data, menuImageData, indexOpened);
    //     })
    //     .catch((err) => {
    //       console.log('err', err);
    //     });
    //   return;
    // }
    //Open Camera
    ImageCropPicker.openCamera(imgCropOpts)
      .then((data) => {
        handleImage(data, menuImageData, indexOpened);
      })
      .catch((err) => {
        console.log('err', err);
      });
  };

  const handleImage = (imageData, menuImageDataSelected, indexOpened) => {
    let imagesTmp = imageUpload;
    let nhom = {};
    let loai = mauInDuocChon.ma_mau_in == mauInGuiMail[0] ? 'DGHT' : 'BBGD';
    const imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri
    if (profileData && profileData.cau_hinh) {
      let cauHinhLuuAnh = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.LUU_ANH_THU_VIEN, profileData.cau_hinh);
      if (cauHinhLuuAnh?.gia_tri == DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) saveToCameraRoll(imageData.path);
    }
    categoryImage.map((item) => {
      if (loai == 'BBGD' && item.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BIEN_BAN_GIAM_DINH) {
        nhom = item;
        return;
      } else if (loai == 'DGHT' && item.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANH_GIA_HIEN_TRUONG) {
        nhom = item;
        return;
      }
    });
    imagesTmp.unshift({
      path: imageData.path,
      nhom: nhom,
      name: imageName,
    });
    setImageUpload([...imagesTmp]);
  };

  const removeImage = (imageData) => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {
        text: 'Đồng ý',
        onPress: () => {
          let tmp = imageUpload;
          tmp.splice(imageData.index, 1);
          setImageUpload([...tmp]);
        },
      },
      { text: 'Để sau' },
    ]);
  };

  //xem chi tiết ảnh đã chụp
  const onPressXemLaiAnh = (imageData) => {
    if (isIOS) return;
    setModalImageSelectedData({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
    setToggleModalImage(true);
  };
  const onPressUploadAnh = async () => {
    // return;
    let imgsTmp = imageUpload;
    imgsTmp = imgsTmp.filter((item) => item.path);
    if (imgsTmp.length == 0) return;
    let files = [];
    imgsTmp.forEach((e, i) => {
      let file = {
        key_file: 'file' + i,
        nhom: e.nhom.ma,
        // x: currentPosition.coords?.latitude,
        // y: currentPosition.coords?.longitude,
      };
      files.push(file);
    });
    let params = {
      images: imgsTmp,
      so_id: profileData.ho_so.so_id,
      pm: 'GD',
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      files: files,
      ung_dung: 'MOBILE_BT',
    };

    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.uploadFile(AxiosConfig.ACTION_CODE.UPLOAD_FILE, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setToggleModalAnhGiayTo(false);
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
      initImageUpload();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeShowFile = (type) => {
    let dataMauIn = profileData?.mau_in;
    layMauInPDF(dataMauIn[type]);
  };

  /* RENDER */
  const renderPDFView = () => {
    if (!pdfData) return;
    return (
      <Pdf
        source={{
          uri: pdfData.uri ? pdfData.uri : 'data:application/pdf;base64,' + pdfData.base64,
        }}
        onLoadProgress={(percent) => {
          console.log('percent', percent);
        }}
        onLoadComplete={(numberOfPages, filePath) => {
          // let pdfDataTmp = pdfData;
          // if (!pdfDataTmp) return;
          // pdfDataTmp.filePath = filePath;
          // setPDFData(pdfDataTmp);
        }}
        onPageChanged={(page, numberOfPages) => { }}
        onError={(error) => {
          Alert.alert('Thông báo', error);
          // console.log(error);
        }}
        onPressLink={(uri) => {
          // console.log(`Link presse: ${uri}`);
        }}
        style={styles.pdf}
      />
    );
  };

  const renderActionButton = () => {
    if (prevScreen == SCREEN_ROUTER_APP.TRINH_THANH_TOAN || prevScreen == SCREEN_ROUTER_APP.APPROVAL_PROFILE || route.params.prevPrevScreen == SCREEN_ROUTER_APP.HO_SO_DIA_BAN) return;
    if (profileData && profileData?.ho_so?.hien_thi_button == '1') return;
    if (!mauInDuocChon && !route.params.dataPDF) return;
    let maMauIn = mauInDuocChon?.ma_mau_in;
    return (
      <ActionButton
        buttonColor={colors.BLUE3_08}
        renderIcon={() => (
          <View style={{ borderRadius: 20 }}>
            <Icon.SimpleLineIcons name="options-vertical" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
          </View>
        )}
        offsetY={maMauIn == mauInGuiMail[0] || maMauIn == mauInGuiMail[1] ? 80 : 20}
        offsetX={10}
        size={55}
        degrees={0}>
        {(maMauIn == mauInGuiMail[0] || maMauIn == mauInGuiMail[1]) && (
          <ActionButton.Item buttonColor={colors.ORANGE} title="Gửi xác nhận khách hàng" textContainerStyle={styles.actionButtonTextContainer} onPress={() => getListMailData()}>
            <Icon.MaterialCommunityIcons name="email-send-outline" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
          </ActionButton.Item>
        )}

        {/* <ActionButton.Item buttonColor={colors.RED3} title="Chụp ảnh mẫu in" textContainerStyle={styles.actionButtonTextContainer}>
          <Icon.FontAwesome name="camera" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
        </ActionButton.Item> */}
        <ActionButton.Item buttonColor={colors.GREEN} title="Chia sẻ" textContainerStyle={styles.actionButtonTextContainer} onPress={onPressSharePDF}>
          <Icon.FontAwesome name="share-alt" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
        </ActionButton.Item>
      </ActionButton>
    );
  };

  const renderModalConfirmSendMail = () => {
    return (
      <ModalComp
        isVisible={toggleModalMailConfirm}
        swipeDirection={[]}
        onBackdropPress={() => setToggleModalMailConfirm(false)}
        renderContentView={() => (
          <ModalMailConfirm inputEmail={inputEmail} onChangeEmail={onChangeEmail} setToggleModalMailConfirm={setToggleModalMailConfirm} inputErr={inputErr} onPressGuiMail={onPressGuiMail} />
        )}
      />
    );
  };

  const renderModalAnhGiayTo = () => {
    return (
      <ModalComp
        isVisible={toggleModalAnhGiayTo}
        swipeDirection={['down']}
        onSwipeComplete={() => setToggleModalAnhGiayTo(false)}
        onBackdropPress={() => setToggleModalAnhGiayTo(false)}
        // isVisible={true}
        scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
        // onBackdropPress={() => setToggleModalAnhGiayTo(false)}
        renderContentView={() => (
          <ModalAnhGiayTo
            setToggleModalAnhGiayTo={setToggleModalAnhGiayTo}
            scrollViewModalRef={scrollViewModalRef}
            setScrollOffSet={setScrollOffSet}
            imageUpload={imageUpload}
            openCamera={openCamera}
            removeImage={removeImage}
            onPressXemLaiAnh={onPressXemLaiAnh}
            onPressUploadAnh={onPressUploadAnh}
            disabledNutGui={dialogLoading}
          />
        )}
        scrollOffset={scrollOffSet}
        propagateSwipe={true}
        scrollOffsetMax={constHeight - flatListHeight}
      />
    );
  };

  const renderBottomView = () => {
    if (prevScreen === SCREEN_ROUTER_APP.TRINH_THANH_TOAN || route.params.prevPrevScreen === SCREEN_ROUTER_APP.HO_SO_DIA_BAN) return;
    else if (profileData && profileData?.ho_so?.hien_thi_button == '1') return;
    // nút của Hồ sơ Phê Duyệt
    else if (prevScreen === SCREEN_ROUTER_APP.APPROVAL_PROFILE || prevScreen === SCREEN_ROUTER_APP.APPROVAL_NG_PROFILE) {
      if (!pdfData) return;
      return (
        <View style={styles.bottomView}>
          {/* NÚT TỪ CHỐI DUYỆT */}
          {profileData.ho_so.trang_thai_duyet && profileData.ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.CHUA_DUYET && (
            <ButtonLinear title="Trả lại hồ sơ" onPress={() => refModalTraLaiHoSo.current.show()} isSubBtn />
          )}
          {/* NÚT DUYỆT */}
          {profileData.ho_so.trang_thai_duyet &&
            (profileData.ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.CHUA_DUYET || profileData.ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.TU_CHOI_DUYET) && (
              <ButtonLinear title="Duyệt" onPress={() => refModalPheDuyet.current.show()} linearStyle={{ marginLeft: spacing.small }} />
            )}
          {/* NÚT TỪ CHỐI */}
          {profileData.ho_so.trang_thai_duyet && profileData.ho_so.trang_thai_duyet === DATA_CONSTANT.APPROVAL_STATUS.DA_DUYET && <ButtonLinear title="Huỷ duyệt" onPress={onPressHuyDuyet} />}
          {/* <ButtonLinear
            title="Điều chỉnh PA"
            onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.DS_PHUONG_AN_TRINH_DUYET, {profileData})}
            linearStyle={{marginLeft: spacing.small}}
            textStyle={{fontSize: FontSize.size13}}
          /> */}
        </View>
      );
    } else if (mauInDuocChon) {
      let maMauIn = mauInDuocChon.ma_mau_in;
      if (maMauIn != mauInGuiMail[0] && maMauIn != mauInGuiMail[1]) return;
      return (
        <View style={styles.bottomView}>
          <ButtonLinear title="Chụp ảnh" onPress={() => setToggleModalAnhGiayTo(true)} />
        </View>
      );
    }
  };
  const renderModalChiTietAnh = () => {
    return <ModalXemChiTietAnh toggleModalImage={toggleModalImage} setToggleModalImage={setToggleModalImage} modalImageSelectedData={modalImageSelectedData} />;
  };
  return (
    <ScreenComponent
      dialogLoading={isLoading}
      headerBack
      headerTitle="Tài liệu"
      renderView={
        <View style={styles.container}>
          {profileData?.trinh_duyet && profileData?.trinh_duyet.length > 0 && profileData?.trinh_duyet[0].hanh_dong === 'PHUONG_AN_BAO_LANH' && (
            <View style={styles.btnDoiMauInView}>
              <Text style={styles.txtTenMauIn}>{tenMauIn}</Text>
              <View>
                <TouchableOpacity onPress={() => onChangeShowFile(fileType)} style={styles.btnDoiMauIn}>
                  <Text style={styles.txtBtnDoiMauIn}>{fileType === 1 ? 'Xem thư bảo lãnh' : 'Xem tờ trình'}</Text>
                  <Icon.Foundation name="page-export-pdf" size={20} color={colors.PRIMARY} />
                </TouchableOpacity>
              </View>
            </View>
          )}
          {renderPDFView()}
          {renderActionButton()}
          {renderModalConfirmSendMail()}
          {renderModalAnhGiayTo()}
          {renderModalChiTietAnh()}
          {renderBottomView()}

          <CustomModal
            ref={refModalTraLaiHoSo}
            renderContent={() => (
              <RenderContentModalTraLaiHoSo
                profileData={profileData}
                onConfirmed={() => {
                  refModalTraLaiHoSo.current.hide();
                  NavigationUtil.pop(2);
                }}
                onCancel={() => {
                  refModalTraLaiHoSo.current.hide();
                }}
              />
            )}
          />
          <CustomModal
            ref={refModalPheDuyet}
            renderContent={() => (
              <RenderContentModalXacNhanDuyet
                profileData={profileData}
                onConfirmed={() => {
                  refModalPheDuyet.current.hide();
                  getApproveProfile(profileData);
                }}
                onCancel={() => {
                  refModalPheDuyet.current.hide();
                }}
              />
            )}
          />
        </View>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
  categoryImage: state.categoryImage.data,
});

const mapDispatchToProps = {};

const PDFViewScreenConnect = connect(mapStateToProps, mapDispatchToProps)(PDFViewScreenComponent);
export const PDFViewScreen = memo(PDFViewScreenConnect, isEqual);
