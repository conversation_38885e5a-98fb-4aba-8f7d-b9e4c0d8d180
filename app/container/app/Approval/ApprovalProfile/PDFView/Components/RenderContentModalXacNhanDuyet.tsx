import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Text, TextInputOutlined} from '@component';
import React, {forwardRef, memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

interface Props {
  profileData?: any;
  onConfirmed?: () => {};
  onCancel?: () => {};
}

const BORDER_RADIUS = 8;

const RenderContentModalXacNhanDuyetComponent = forwardRef((props: Props) => {
  const {profileData, onConfirmed, onCancel} = props;
  const [isSubmiting, setIsSubmiting] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      chiNhanh: profileData?.ho_so ? profileData.ho_so?.ma_chi_nhanh : '',
      ghiChu: '',
      giamDinhVien: profileData?.ho_so ? profileData.ho_so?.ten_gdvht : '',
      maChiNhanh: '',
    },
    mode: 'onChange',
  });

  const onPressXacNhan = async (data) => {
    Alert.alert('Phê duyệt', 'Bạn có chắc chắn muốn duyệt hồ sơ này không?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            setIsSubmiting(true);
            let params = {
              so_id: profileData.ho_so.so_id,
              nv: profileData.ho_so.nv === 'THANH_TOAN' ? profileData.ho_so.nv : DATA_CONSTANT.APPROVAL_PROFILE_NGHIEP_VU.XE,
              loai: profileData.ho_so.loai,
              bt: profileData.ho_so.bt,
              ma_doi_tac_xl: profileData.ho_so.ma_doi_tac_xl,
              ma_doi_tac: profileData.ho_so.ma_doi_tac,
              noi_dung: data.ghiChu,
              create_file_sign: profileData.ho_so.create_file_sign,
              remove_file: profileData.ho_so.remove_file,
              so_id_tu: profileData.thanh_toan[0].so_id_tu,
              ma_dt_trinh: profileData.trinh_duyet[0].lan,
            };
            let response = await ESmartClaimEndpoint.approve(axiosConfig.ACTION_CODE.APPROVAL_PROFILE, params);
            setIsSubmiting(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Đã thực hiện Duyệt thành công', 'success');
            onConfirmed && onConfirmed();
          } catch (error) {
            setIsSubmiting(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  // const getErrMessage = (inputName, errType) => {
  //   if (errType === 'required') return 'Thông tin bắt buộc';
  // };

  /* RENDER */

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Phê duyệt
        </Text>
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={false}>
        <Controller
          control={control}
          name="ghiChu"
          rules={{
            required: false,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              // isRequired
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              inputStyle={{height: 160}}
              containerStyle={{zIndex: -1}}
              // error={errors.ghiChu && getErrMessage('ghiChu', errors.ghiChu.type)}
            />
          )}
        />
      </KeyboardAwareScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={() => onCancel && onCancel()} title="Để sau" linearStyle={{marginRight: spacing.small}} isSubBtn />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(onPressXacNhan)} title="Xác nhận" />
      </View>
    </View>
  );
});
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.smaller,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalXacNhanDuyet = memo(RenderContentModalXacNhanDuyetComponent, isEqual);
