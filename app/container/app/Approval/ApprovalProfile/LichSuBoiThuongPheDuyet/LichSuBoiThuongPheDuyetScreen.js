import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {Empty, ScreenComponent, Text} from '@component';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import styles from './LichSuBoiThuongPheDuyetStyles';

const LichSuBoiThuongPheDuyetScreenComponent = (props) => {
  console.log('LichSuBoiThuongPheDuyetScreenComponent');
  const {route} = props;
  const {profileData} = route.params;
  /**RENDER  */
  const renderLichSuBoiThuongItem = ({item, index}) => {
    const renderLabel = (label, value) => {
      return (
        <View style={styles.contentRow}>
          <Text style={styles.subLabel}>{label}:</Text>
          {typeof value === 'number' ? (
            <NumericFormat value={value} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={{flex: 1}}>{' ' + val}</Text>} />
          ) : (
            <Text style={styles.content}>{value}</Text>
          )}
        </View>
      );
    };
    return (
      <View style={styles.resolveItemView}>
        <View flexDirection="row" alignItems="center" marginBottom={10}>
          <View style={styles.verticalLineStep}>
            <Text style={styles.countNumber}>{index + 1}</Text>
          </View>
          <View style={styles.titleView}>
            {item?.so_hs.trim() !== '' ? <Text style={styles.title}>{item?.so_hs}</Text> : <Text style={[styles.title, {color: colors.BLACK_03}]}>{'Hồ sơ chưa lấy số'}</Text>}
            <Text style={styles.date}>Thời gian: {item.ngay_ht}</Text>
          </View>
        </View>
        <View style={styles.contentColumn}>
          {renderLabel('Số tiền', item.tien)}
          {renderLabel('Trạng thái', item.trang_thai)}
        </View>
      </View>
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle="Lịch sử bồi thường phê duyệt"
      renderView={
        <View style={styles.container}>
          <FlatList
            style={{paddingTop: spacing.small}}
            data={profileData.lich_su_ton_that}
            renderItem={renderLichSuBoiThuongItem}
            keyExtractor={(item, index) => item.data + index.toString()}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={<Empty />}
          />
        </View>
      }
    />
  );
};

export const LichSuBoiThuongPheDuyetScreen = memo(LichSuBoiThuongPheDuyetScreenComponent, isEqual);
