import {colors} from '@app/commons/Theme';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  resolveItemView: {
    marginHorizontal: scale(spacing.smaller),
  },
  verticalLineStep: {
    width: 22,
    height: 22,
    backgroundColor: colors.PRIMARY,
    borderWidth: 1,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: 'white',
  },
  contentColumn: {
    borderLeftWidth: 1,
    borderColor: colors.GRAY,
    marginLeft: scale(spacing.small),
    marginBottom: vScale(spacing.medium),
  },
  title: {
    marginBottom: 4,
    fontWeight: '500',
    fontSize: FontSize.size14,
    color: colors.PRIMARY,
  },

  date: {
    fontSize: FontSize.size12,
    color: colors.GRAY6,
  },
  subLabel: {
    fontWeight: '400',
    fontSize: FontSize.size14,
    color: colors.GRAY6,
    marginLeft: scale(20),
  },
  content: {
    fontWeight: '400',
    fontSize: FontSize.size14,
    color: colors.PRIMARY,
    marginLeft: scale(spacing.tiny),
  },
  titleView: {
    marginLeft: 10,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: vScale(spacing.tiny),
  },
  countNumber: {
    color: colors.WHITE,
    fontSize: FontSize.size12,
  },
});
