import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {Empty, HeaderModal2, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import Modal from 'react-native-modal';

const ModalHangXeComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, data, value} = props;

  const [isVisible, setIsVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [dsHangXe, setDsHangXe] = useState([]);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    let newData = data;
    newData[index].isCheck = !item.isCheck;
    setDsHangXe([...newData].slice(0, 50));
  };

  const initModalData = () => {
    if (data?.length > 0) {
      let newData = data;
      newData.map((e) => {
        if (e.ma === value) {
          e.isCheck = true;
        } else {
          e.isCheck = false;
        }
      });
      let selected = newData.find((e) => e.ma === value);
      newData.sort(function (x, y) {
        return x == selected ? -1 : y == selected ? 1 : 0;
      });
      setDsHangXe([...newData].slice(0, 50));
    }
    onSearch();
  };

  const onSearch = () => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = data.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDsHangXe(filter);
    } else if (searchText === '') {
      setDsHangXe(data.slice(0, 50));
    }
  };

  useEffect(() => {
    onSearch();
  }, [searchText]);

  /* RENDER */
  const renderItem = ({item, index}) => {
    const isCheck = item.isCheck === true;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK}}>{item.ten}</Text>
        {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['right']}
      animationIn="fadeIn"
      animationOut="fadeOut"
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onModalWillShow={initModalData}
      onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.modalView}>
        <HeaderModal2 title="Hãng xe" onBackPress={() => setIsVisible(false)} />
        <SearchBar value={searchText} onTextChange={setSearchText} />
        <View flex={1}>
          <FlatList
            data={dsHangXe}
            renderItem={renderItem}
            ListEmptyComponent={<Empty />}
            keyExtractor={(e, i) => i.toString()}
            style={{padding: scale(spacing.small)}}
            ListFooterComponent={<View height={70} />}
          />
        </View>
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    flex: 1,
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height * 0.6,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    margin: 16,
    borderWidth: 1,
    paddingLeft: 16,
    borderRadius: 25,
    borderColor: colors.GRAY,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: vScale(spacing.smaller),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  modalContentView: {
    marginTop: 10,
    marginHorizontal: 10,
  },
  container: {
    paddingBottom: 20,
    marginHorizontal: 16,
  },
});

export const ModalHangXe = memo(ModalHangXeComponent, isEqual);
