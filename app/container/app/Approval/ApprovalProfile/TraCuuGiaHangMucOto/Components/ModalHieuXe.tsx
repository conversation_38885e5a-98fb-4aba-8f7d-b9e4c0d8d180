import {colors} from '@app/commons/Theme';
import {dimensions, spacing, vScale} from '@app/theme';
import {Empty, HeaderModal2, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalHieuXeComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, data, value} = props;

  const [isVisible, setIsVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [dsHieuXe, setDsHieuXe] = useState([]);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
  };

  const initModalData = () => {
    if (data?.length > 0) {
      let newData = data;
      newData.map((e) => {
        if (e.ma === value) {
          e.isCheck = true;
        } else {
          e.isCheck = false;
        }
      });
      setDsHieuXe([...newData]);
    }
    onSearch();
  };

  const onSearch = () => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = data.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDsHieuXe(filter);
    } else if (searchText === '') {
      setDsHieuXe(data.slice(0, 50));
    }
  };

  useEffect(() => {
    onSearch();
  }, [searchText]);

  /* RENDER */
  const renderItem = ({item, index}) => {
    const isCheck = item.isCheck;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK}}>{item.ten}</Text>
        {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      animationIn="fadeIn"
      animationOut="fadeOut"
      swipeDirection={['right']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onModalWillShow={initModalData}
      onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.modalView}>
        <HeaderModal2 title="Hiệu xe" onBackPress={() => setIsVisible(false)} />
        <SearchBar value={searchText} onTextChange={setSearchText} />
        <View padding={spacing.small} flex={1}>
          <FlatList showsVerticalScrollIndicator={false} data={dsHieuXe} renderItem={renderItem} keyExtractor={(e, i) => i.toString()} ListEmptyComponent={<Empty />} />
        </View>
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    flex: 1,
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height * 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    margin: 16,
    borderWidth: 1,
    paddingLeft: 16,
    borderRadius: 25,
    borderColor: colors.GRAY,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: vScale(spacing.smaller),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  modalContentView: {
    marginTop: 10,
    marginHorizontal: 10,
  },
  container: {
    paddingBottom: 20,
    marginHorizontal: 16,
  },
});

export const ModalHieuXe = memo(ModalHieuXeComponent, isEqual);
