import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {ButtonLinear, HeaderModal2, Icon, Text, TextInputOutlined} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {ModalHangXe} from './ModalHangXe';
import {ModalHieuXe} from './ModalHieuXe';
import {ModalNamSanXuat} from './ModalNamSanXuat';
import {ModalOptions} from './ModalOptions';

const DATA_THAY_THE_SUA_CHUA = [
  {
    label: 'Sửa chữa',
    value: 'S',
    isCheck: false,
  },
  {
    label: 'Thay thế',
    value: 'T',
    isCheck: false,
  },
];

const DATA_CHINH_HANG = [
  {
    label: 'Chính hãng',
    value: 'C',
    isCheck: false,
  },
  {
    label: 'Không chính hãng',
    value: 'K',
    isCheck: false,
  },
];

const ModalFilterComponent = forwardRef(({dsHangXe, dsHieuXeRoot, dataFilter, resetFilter, onPressTimKiem}, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: showModal,
      hide: hideModal,
    }),
    [],
  );

  const insect = useSafeAreaInsets();
  let refModalHangXe = useRef(null);
  let refModalHieuXe = useRef(null);
  let refModalNamSx = useRef(null);
  let refModalThayThe = useRef(null);
  let refModalChinhHang = useRef(null);

  const [isVisible, setIsVisible] = useState(false);
  const [dsHieuXe, setDsHieuXe] = useState([]);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      hangXe: dataFilter.hang_xe,
      hieuXe: dataFilter.hieu_xe,
      namSx: dataFilter.nam_sx,
      thayTheSuaChua: '',
      chinhHang: '',
      soHS: '',
    },
  });

  const hangXe = watch('hangXe');
  const hieuXe = watch('hieuXe');
  const namSx = watch('namSx');
  const thayTheSuaChua = watch('thayTheSuaChua');
  const chinhHang = watch('chinhHang');

  useEffect(() => {
    if (dsHangXe?.length > 0 && hangXe !== '') {
      let arr = dsHieuXeRoot.filter((e) => e.hang_xe === hangXe);
      setDsHieuXe(arr);
    }
  }, [hangXe, dsHangXe, dsHieuXeRoot]);

  const showModal = () => setIsVisible(true);
  const hideModal = () => setIsVisible(false);

  const getTextHienThi = (val, data) => {
    let text = '';
    data.map((e) => {
      if (e.ma === val) {
        text = e.ten || e.label;
      }
      if (e.value === val) {
        text = e.label;
      }
    });
    return text;
  };
  const onSelectHangXe = (val) => {
    setValue('hangXe', val.ma, {shouldValidate: true});
    setValue('hieuXe', '');
  };
  const onPressSubmit = (data) => {
    hideModal();
    onPressTimKiem(data);
  };
  /* RENDER */

  return (
    <Modal isVisible={isVisible} style={styles.modal} onBackdropPress={hideModal} onBackButtonPress={hideModal}>
      <View style={{flex: 1, justifyContent: 'flex-end'}}>
        <View style={[styles.modalContent, {marginTop: insect.top > 0 ? insect.top : spacing.default}]}>
          <HeaderModal2 title="Tìm kiếm" onBackPress={() => setIsVisible(false)} />
          <KeyboardAwareScrollView style={{paddingHorizontal: spacing.default}}>
            <Controller
              control={control}
              name="soHS"
              // rules={{required: true}}
              render={({field: {onChange, value}}) => <TextInputOutlined title="Số hồ sơ" placeholder="Số hồ sơ" value={value} onChangeText={onChange} containerStyle={{marginBottom: 0}} />}
            />
            <Controller
              control={control}
              name="hangXe"
              // rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  // isRequired
                  isDropdown
                  value={getTextHienThi(value, dsHangXe)}
                  title="Hãng xe"
                  editable={false}
                  isTouchableOpacity
                  placeholder="Chọn hãng xe"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[{flex: 1}]}
                  onPress={() => refModalHangXe.current.show()}
                  // error={errors.hangXe && getErrMessage(errors.hangXe.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="hieuXe"
              // rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  // isRequired
                  isDropdown
                  value={getTextHienThi(value, dsHieuXeRoot)}
                  title="Hiệu xe"
                  editable={false}
                  isTouchableOpacity
                  placeholder="Chọn hiệu xe"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[{flex: 1}]}
                  onPress={() => refModalHieuXe.current.show()}
                  // error={errors.hieuXe && getErrMessage(errors.hieuXe.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="namSx"
              // rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  // isRequired
                  isDropdown
                  value={value}
                  editable={false}
                  isTouchableOpacity
                  title="Năm sản xuất"
                  placeholder="Chọn năm sản xuất"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[{flex: 1}]}
                  onPress={() => refModalNamSx.current.show()}
                  // error={errors.namSx && getErrMessage(errors.namSx.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="thayTheSuaChua"
              // rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  // isRequired
                  isDropdown
                  value={getTextHienThi(value, DATA_THAY_THE_SUA_CHUA)}
                  editable={false}
                  isTouchableOpacity
                  title="Thay thế / Sửa chữa"
                  placeholder="Chọn"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[{flex: 1}]}
                  onPress={() => refModalThayThe.current.show()}
                  // error={errors.namSx && getErrMessage(errors.namSx.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="chinhHang"
              // rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  // isRequired
                  isDropdown
                  value={getTextHienThi(value, DATA_CHINH_HANG)}
                  editable={false}
                  isTouchableOpacity
                  title="Chính hãng / Không chính hãng"
                  placeholder="Chọn"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[{flex: 1}]}
                  onPress={() => refModalChinhHang.current.show()}
                  // error={errors.namSx && getErrMessage(errors.namSx.type)}
                />
              )}
            />

            <ButtonLinear title="Tìm kiếm" onPress={handleSubmit(onPressSubmit)} linearStyle={{marginVertical: 20}} />
          </KeyboardAwareScrollView>
        </View>
      </View>
      <ModalHangXe
        data={dsHangXe}
        ref={refModalHangXe}
        setValue={onSelectHangXe}
        value={hangXe}
        // setLoading={setLoading}
        onBackPress={() => refModalHangXe.current.hide()}
      />

      <ModalHieuXe
        value={hieuXe}
        data={dsHieuXe}
        ref={refModalHieuXe}
        // setLoading={setLoading}
        onBackPress={() => refModalHieuXe.current.hide()}
        setValue={(val) => setValue('hieuXe', val.ma, {shouldValidate: true})}
      />

      <ModalNamSanXuat value={namSx} ref={refModalNamSx} onBackPress={() => refModalNamSx.current.hide()} setValue={(val) => setValue('namSx', val.label, {shouldValidate: true})} />
      <ModalOptions
        modalTitle="Thay thế / Sửa chữa"
        value={thayTheSuaChua}
        data={DATA_THAY_THE_SUA_CHUA}
        ref={refModalThayThe}
        onBackPress={() => refModalThayThe.current.hide()}
        setValue={(val) => setValue('thayTheSuaChua', val.value, {shouldValidate: true})}
        containerStyle={{height: dimensions.height / 4}}
        searchable={false}
      />

      <ModalOptions
        modalTitle="Chính hãng / Không chính hãng"
        value={chinhHang}
        data={DATA_CHINH_HANG}
        ref={refModalChinhHang}
        onBackPress={() => refModalChinhHang.current.hide()}
        setValue={(val) => setValue('chinhHang', val.value, {shouldValidate: true})}
        containerStyle={{height: dimensions.height / 4}}
        searchable={false}
      />
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalContent: {
    width: dimensions.width,
    height: dimensions.height * 0.9,
    flex: 1,
    backgroundColor: '#FFF',
    // paddingTop: 10,
    borderRadius: 20,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    paddingHorizontal: 10,
    borderColor: 'gray',
  },
  txtHeader: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});
export const ModalFilter = memo(ModalFilterComponent, isEqual);
