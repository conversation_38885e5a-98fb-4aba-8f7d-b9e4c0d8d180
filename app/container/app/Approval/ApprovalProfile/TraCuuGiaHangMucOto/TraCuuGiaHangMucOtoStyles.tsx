import {colors} from '@app/commons/Theme';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  rightHeaderView: {
    paddingRight: spacing.default,
  },
  countFilterView: {
    backgroundColor: '#FFF',
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    position: 'absolute',
    right: 5,
    top: -5,
  },
  profileItemView: {
    borderRadius: 4,
    borderWidth: 0.4,
    borderColor: colors.GRAY,
    marginHorizontal: scale(spacing.smaller),
    backgroundColor: colors.WHITE5,
    marginBottom: spacing.default,
    flex: 1,
    borderBottomColor: colors.GRAY4,
  },
  txtSoHS: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  rowContainerView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 0.3,
    borderColor: colors.GRAY,
    paddingVertical: spacing.smaller,
    paddingHorizontal: spacing.default,
    flex: 1,
  },
  detail: {
    flex: 3,
  },
  label: {
    flex: 1,
  },
});
