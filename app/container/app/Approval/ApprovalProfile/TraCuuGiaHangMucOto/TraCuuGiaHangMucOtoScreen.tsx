import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {Empty, Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, SafeAreaView, View} from 'react-native';
import styles from './TraCuuGiaHangMucOtoStyles';
import {TouchableOpacity} from 'react-native';
import {ModalFilter} from './Components';
import {spacing} from '@app/theme';
import {NumericFormat} from 'react-number-format';

const TraCuuGiaHangMucOtoScreenComponent = ({route}) => {
  console.log('TraCuuGiaHangMucOtoScreenComponent');
  const {dataHangMuc} = route.params;

  let refModalFilter = useRef(null);

  const [dialogLoading, setDialogLoading] = useState(false);
  const [dsHangXe, setDsHangXe] = useState([]);
  const [dsHieuXeRoot, setdsHieuXeRoot] = useState([]);

  const [data, setData] = useState([]);

  const [dataFilter, setDataFilter] = useState({
    hang_xe: dataHangMuc?.hang_xe,
    hieu_xe: dataHangMuc?.hieu_xe,
    nam_sx: dataHangMuc?.nam_sx,
    thay_the_sc: '',
    chinh_hang: '',
    so_hs: '',
  });

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    getDataTraCuu();
  }, [dataFilter]);

  const initData = async () => {
    getDataTraCuu();
    await layDsHangXe();
    await layDsHieuXe();
  };

  const getDataTraCuu = async () => {
    try {
      let params = {
        nv: dataHangMuc.nghiep_vu,
        hang_muc: dataHangMuc.hang_muc.hang_muc,
        hang_xe: dataFilter?.hang_xe,
        hieu_xe: dataFilter?.hieu_xe,
        nam_sx: dataFilter?.nam_sx,
        thay_the_sc: dataFilter?.thay_the_sc,
        chinh_hang: dataFilter?.chinh_hang,
        tien_tu: 0,
        tien_toi: 0,
        so_hs: dataFilter?.so_hs,
        so_dong: 10,
        trang: 1,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TRA_CUU_GIA_HANG_MUC_OTO, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setData(response.data_info.data);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDsHangXe = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HANG_XE, {nv: dataHangMuc.nghiep_vu});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDsHangXe(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDsHieuXe = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HIEU_XE, {nv: dataHangMuc.nghiep_vu});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setdsHieuXeRoot(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getCountFilter = () => {
    let count = 0;
    for (const key in dataFilter) {
      if (dataFilter[key]) count += 1;
    }
    return count;
  };
  /**RENDER  */
  const renderRightHeader = () => {
    return (
      <TouchableOpacity style={styles.rightHeaderView} onPress={() => refModalFilter.current?.show()}>
        <Icon.AntDesign name="filter" color="#FFF" size={24} />
        {getCountFilter() > 0 && (
          <View style={styles.countFilterView}>
            <Text children={getCountFilter()} font="regular10" />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderLabel = (title, value, style?) => {
    return (
      <View style={styles.rowContainerView}>
        <Text style={[styles.label]} font="regular12" children={title} color={colors.LABEL_GRAY1} />
        <View style={{flexDirection: 'row', flex: 3}}>
          {typeof value === 'string' ? (
            <Text font="regular14" style={[styles.detail, style]} children={value} color={colors.VALUE_GRAY1} />
          ) : (
            <NumericFormat
              value={value}
              displayType={'text'}
              thousandSeparator={true}
              renderText={(displayValue) => <Text style={[styles.detail, style]} children={displayValue} color={colors.VALUE_GRAY1} />}
            />
          )}
        </View>
      </View>
    );
  };

  const renderItemTraCuuGia = ({item, index}) => {
    return (
      <View style={styles.profileItemView}>
        {renderLabel('Số hồ sơ', item.so_hs, styles.txtSoHS)}
        {renderLabel('Hãng xe', item.hang_xe)}
        {renderLabel('Hiệu xe', item.hieu_xe)}
        {renderLabel('Năm sản xuất', item.nam_sx)}
        {renderLabel('Phương án', item.thay_the_sc_ten)}
        {renderLabel('Sửa chữa', item.chinh_hang_ten)}
        {renderLabel('Giá tiền', item.tien_vtu, {color: colors.GREEN})}
      </View>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      renderRightHeader={renderRightHeader}
      headerBack
      headerTitle="Tra cứu giá hạng mục"
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            style={{marginTop: spacing.default}}
            data={data}
            renderItem={renderItemTraCuuGia}
            keyExtractor={(item, index) => item.data + index.toString()}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={<Empty />}
          />
          <ModalFilter
            ref={refModalFilter}
            dsHangXe={dsHangXe}
            dsHieuXeRoot={dsHieuXeRoot}
            dataFilter={dataFilter}
            onPressTimKiem={(data) => {
              console.log('data', data);
              setDataFilter({
                hang_xe: data?.hangXe,
                hieu_xe: data?.hieuXe,
                nam_sx: data?.namSx,
                thay_the_sc: data?.thayTheSuaChua,
                chinh_hang: data?.chinhHang,
                so_hs: data?.soHS,
              });
            }}
          />
        </SafeAreaView>
      }
    />
  );
};

export const TraCuuGiaHangMucOtoScreen = memo(TraCuuGiaHangMucOtoScreenComponent, isEqual);
