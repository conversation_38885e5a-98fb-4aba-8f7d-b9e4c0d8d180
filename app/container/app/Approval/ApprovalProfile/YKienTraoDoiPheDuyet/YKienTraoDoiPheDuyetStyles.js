import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  inputStyle: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: colors.GRAY,
    marginHorizontal: spacing.small,
    paddingHorizontal: spacing.small,
  },
  item: {
    alignItems: 'flex-start',
    paddingTop: spacing.tiny,
  },
  content: {
    flex: 1,
    marginHorizontal: spacing.small,
  },
  footerView: {
    bottom: 0,
    width: dimensions.width,
    borderTopWidth: 0.2,
    paddingVertical: spacing.small,
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: spacing.small,
  },
  boxChat: {
    margin: spacing.tiny,
    padding: spacing.small,
    borderTopLeftRadius: 0,
    borderRadius: spacing.smaller,
    backgroundColor: colors.WHITE1,
    maxWidth: dimensions.width * 0.7,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtTime: {
    lineHeight: 20,
    fontSize: 10,
    fontStyle: 'italic',
    color: colors.GRAY6,
    marginRight: spacing.smaller,
  },
  txtNoiDung: {
    color: colors.BLACK_03,
  },
  txtTitle: {
    fontSize: 12,
    lineHeight: 20,
    color: colors.BLACK_03,
  },
  isUserBoxChatStyles: {
    borderTopRightRadius: 0,
    backgroundColor: '#E7F3FF',
    borderTopLeftRadius: spacing.smaller,
  },
  txtEmpty: {
    marginVertical: spacing.small,
    textAlign: 'center',
  },
  marginBottom: {
    marginBottom: 70,
  },
});
