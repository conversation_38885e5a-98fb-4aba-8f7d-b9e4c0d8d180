import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectNotificationFirebase} from '@app/redux/slices/NotificationFirebaseSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {ButtonLinear, CustomTabBar, ScreenComponent} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, RefreshControl, ScrollView, View} from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {useSelector} from 'react-redux';
import styles from './ApprovalProfileStyles';
import {TabTaiLieuBoiThuongPheDuyet, TabThongTinHoSoPheDuyet} from './Components';

const extensionsImage = ['.jpg', '.jpeg', '.png', '.gif'];

const ApprovalProfileScreenComponent = (props) => {
  console.log('ApprovalProfileScreenComponent');
  const {route, navigation} = props;
  const {profileDetail} = route.params;
  const scrollViewRef = useRef(null);
  const tabViewRef = useRef(null);
  let refTabTaiLieuBoiThuongPheDuyet = useRef(null);

  const [refreshing, setRefreshing] = useState(false);
  const [profileData, setProfileData] = useState(null);

  const notificationFirebase = useSelector(selectNotificationFirebase);

  const [imageDataStep1, setImageDataStep1] = useState([]); //ẢNH HIỆN TRƯỜNG
  const [imageDataStep2, setImageDataStep2] = useState([]); //ẢNH TOÀN CẢNH
  const [imageDataStep3, setImageDataStep3] = useState([]); //ẢNH TỔN THẤT dữ liệu kiểu : [{//thông tin menu,images}]
  const [imageDataStep4, setImageDataStep4] = useState([]); //ẢNH GIẤY TỜ dữ liệu kiểu : [{//thông tin menu,images}]
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dataAnhCapDon, setDataAnhCapDon] = useState([]);
  const [anhThuHoiVatTu, setAnhThuHoiVatTu] = useState([]); //ẢNH THU HỒI VẬT TƯ
  const [listTaiLieuPdf, setListTaiLieuPdf] = useState([]); //ẢNH XÁC MINH HIỆN TRƯỜNG
  const [xemTaiLieuSelected, setXemTaiLieuSelected] = useState('ANH_TON_THAT');

  const [btnTabActive, setBtnTabActive] = useState(0);

  useEffect(() => {
    navigation.addListener('focus', () => {
      profileDetail && getChiTietHoSoPheDuyet(profileDetail);
    });
  }, []);

  /*khi đang ở trong chi tiết phê duyệt - có notification -> hiển thị Alert 
  -> ấn vào nút Xem chi tiết -> cập nhật notificationFirebase -> gọi API để hiển thị hồ sơ vừa ấn xem chi tiết*/
  useEffect(() => {
    let profileDetail = notificationFirebase;
    if (profileDetail && profileDetail.thong_tin_chung) getChiTietHoSoPheDuyet(profileDetail);
  }, [notificationFirebase]);

  useEffect(() => {
    if (btnTabActive == '1') {
      try {
        if (xemTaiLieuSelected === 'ANH_HIEN_TRUONG') initAnhHienTruong();
        else if (xemTaiLieuSelected === 'ANH_TON_THAT') initAnhTonThat();
        else if (xemTaiLieuSelected === 'ANH_TOAN_CANH') initAnhToanCanh();
        else if (xemTaiLieuSelected === 'ANH_CAP_DON') initAnhCapDon();
        else if (xemTaiLieuSelected === 'ANH_HO_SO') initAnhHoSoGiayTo();
        else if (xemTaiLieuSelected === 'ANH_THVT') initAnhThuHoiVatTu();
        else if (xemTaiLieuSelected === 'FILE_PDF') initTaiLieuPDF();
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    }
  }, [btnTabActive, profileData, xemTaiLieuSelected]);

  const onRefresh = () => {
    getChiTietHoSoPheDuyet(profileData);
  };

  //lấy hồ sơ phê duyệt
  const getChiTietHoSoPheDuyet = async (profileDetail) => {
    setDialogLoading(true);
    try {
      let params = {
        ma_doi_tac: profileDetail?.thong_tin_chung?.ma_doi_tac || profileDetail?.ma_doi_tac,
        bt: profileDetail?.thong_tin_chung?.bt || profileDetail?.bt,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.APPROVAL_PROFILE_DETAIL, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      response.data_info.ho_so = response.data_info.thong_tin_chung;
      setProfileData(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getTaiLieuBoiThuong = async (ma_file) => {
    return new Promise(async (resolve, reject) => {
      try {
        //lấy thông tin của ảnh giám định
        let params = {so_id: profileData.ho_so.so_id, ma_file};
        let responseFileThumbnail = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
        setDialogLoading(false);
        if (!responseFileThumbnail || !responseFileThumbnail.state_info || responseFileThumbnail.state_info.status !== 'OK') return resolve([]);
        resolve(responseFileThumbnail.data_info);
      } catch (error) {
        setDialogLoading(false);
        Alert.alert('Thông báo', error.message);
        resolve([]);
      }
    });
  };

  //KHỞI TẠO ẢNH HIỆN TRƯỜNG
  const initAnhHienTruong = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('HT0001');
    if (response.length > 0) setImageDataStep1([...response]);
  };
  //KHỞI TẠO ẢNH TOÀN CẢNH
  const initAnhToanCanh = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('TC0001');
    let imagesTmp = response.map((item) => {
      item.checked = false;
      item.path = item.duong_dan;
      item.name = item.ten_file;
      let nhom = {
        checked: false,
        ma: item.ma_file,
        ten: item.nhom_anh,
        nhom_hang_muc: item.nhom_hang_muc,
      };
      item.nhom = nhom;
      return item;
    });
    let hangMucMoi = null;
    hangMucMoi = {
      checked: false,
      expanded: false,
      hang_muc: {
        ten_hang_muc: 'Ảnh toàn cảnh',
        loai: 'TC',
        ma_file: 'TC0001',
      },
      images: imagesTmp,
      ma: 'TC0001',
      ten: 'Ảnh toàn cảnh',
    };
    setImageDataStep2([hangMucMoi]);
  };
  //KHỞI TẠO ẢNH TỔN THẤT
  const initAnhTonThat = () => {
    let listHangMuc = [];
    let hangMucTonThat = profileData.hang_muc_chup.filter((item) => item.loai === 'TT');
    // for theo hang_muc
    for (let i = 0; i < hangMucTonThat.length; i++) {
      //tìm hạng mục i trong imageDataStep3
      let hangMucCu = imageDataStep3.find((item) => item.ma === hangMucTonThat[i].hang_muc);
      let hangMucMoi = null; //hạng mục mới để push vào listHangMuc
      //nếu có hạng mục cũ -> chỉ fill mới lại hang_muc
      if (hangMucCu) {
        hangMucMoi = {...hangMucCu};
        hangMucMoi.hang_muc = hangMucTonThat[i];
      }
      //nếu hang_muc[i] chưa có thì tạo mới
      else {
        hangMucMoi = {
          checked: false,
          expanded: false,
          hang_muc: hangMucTonThat[i],
          images: [],
          ma: hangMucTonThat[i].hang_muc,
          ten: hangMucTonThat[i].ten_hang_muc,
        };
      }
      listHangMuc.push(hangMucMoi); //push vào mới
    }
    setImageDataStep3([...listHangMuc]);
  };
  //KHỞI TẠO ẢNH HỒ SƠ GIẤY TỜ
  const initAnhHoSoGiayTo = () => {
    let listHangMuc = [];
    let hangMucGiayTo = profileData.hang_muc_chup.filter(
      (item) => item.loai === 'TL' && item.loai_hang_muc !== 'PDF' && item.hang_muc !== 'ANH_THVT' && item.hang_muc !== 'XMHT' && item.hang_muc !== 'ANH_NGHIEM_THU',
    );
    // for theo hang_muc
    for (let i = 0; i < hangMucGiayTo.length; i++) {
      //tìm hạng mục i trong imageDataStep3
      let hangMucCu = imageDataStep4.find((item) => item.ma === hangMucGiayTo[i].hang_muc);
      let hangMucMoi = null; //hạng mục mới để push vào listHangMuc
      //nếu có hạng mục cũ -> chỉ fill mới lại hang_muc
      if (hangMucCu) {
        hangMucMoi = {...hangMucCu};
        hangMucMoi.hang_muc = hangMucGiayTo[i];
      }
      //nếu hang_muc[i] chưa có thì tạo mới
      else {
        hangMucMoi = {
          checked: false,
          expanded: false,
          hang_muc: hangMucGiayTo[i],
          images: [],
          ma: hangMucGiayTo[i].hang_muc,
          ten: hangMucGiayTo[i].ten_hang_muc,
        };
      }
      listHangMuc.push(hangMucMoi); //push vào mới
    }
    setImageDataStep4([...listHangMuc]);
  };

  const initAnhCapDon = async () => {
    try {
      setDialogLoading(true);
      const params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh_ql,
        so_id: profileData.ho_so.so_id_hd,
        so_id_dt: profileData.ho_so.so_id_dt,
        so_gcn: profileData.ho_so.gcn,
        pm: 'API',
      };
      let response = await PartnerEndpoint.xemAnhCapDon(AxiosConfig.ACTION_CODE.LAY_ANH_CAP_DON, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response?.data_info) setDataAnhCapDon(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  // INIT FILE TÀI LIỆU PDF
  const initTaiLieuPDF = () => {
    try {
      if (listTaiLieuPdf.length > 0) return;
      let listPdf = profileData.hang_muc_chup.filter((item) => item.loai === 'TL' && item.loai_hang_muc === 'PDF');
      listPdf.map((item) => {
        item.extension = '.pdf';
        item.ma_file = item.hang_muc;
      });
      setListTaiLieuPdf([...listPdf]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // INIT ẢNH THU HỒI VẬT TƯ
  const initAnhThuHoiVatTu = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('ANH_THVT');
    setAnhThuHoiVatTu([...response]);
  };

  //nhóm ảnh
  const groupBy = (xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  };
  //click vào 1 ảnh trong Tài liệu bồi thường
  const onPressOpenImageView = (currentImageData, listAnhXem) => {
    try {
      if (currentImageData.item.extension === '.pdf') {
        NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
          profileData,
          prevScreen: SCREEN_ROUTER_APP.APPROVAL_PROFILE,
          dataPDF: currentImageData.item,
        });
      } else if (extensionsImage.includes(currentImageData.item.extension)) {
        //nếu đang ở chế độ CHỌN ẢNH ĐẺ PHÂN LOẠI và đang ở ẢNH TOÀN CẢNH, ẢNH TỔN THẤT, ẢNH HỒ SƠ -> thì k xem dc chi tiết ảnh
        NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
          currentImageData,
          imagesData: listAnhXem,
        });
      } else if (xemTaiLieuSelected === 'ANH_CAP_DON') {
        NavigationUtil.push(SCREEN_ROUTER_APP.XEM_ANH_CAP_DON, {
          imagesData: dataAnhCapDon,
          currentImageData,
        });
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressViewFileDetail = () => {
    NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
      profileData,
      prevScreen: SCREEN_ROUTER_APP.APPROVAL_PROFILE,
    });
  };

  //ĐÓNG / MỞ 1 HẠNG MỤC
  const onPressToggleExpandHangMuc = async (index) => {
    try {
      let imageDataTmp = [];
      let setImageData = null;
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        imageDataTmp = imageDataStep3;
        setImageData = setImageDataStep3;
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        imageDataTmp = imageDataStep4;
        setImageData = setImageDataStep4;
      }
      //nếu có ảnh thì expand ra luôn nhưng vẫn gọi lại API để lấy ảnh
      let daExpand = false; //đã expand rồi thì bên dưới k cần gọi expand nữa
      let newValueExpand = null;
      if (imageDataTmp[index].images.length > 0) {
        daExpand = true;
        setImageData((prevValue) => {
          prevValue[index].expanded = !prevValue[index].expanded; //cập nhật giá trị expand mới
          newValueExpand = prevValue[index].expanded;
          return [...prevValue];
        });
      }
      //nếu là thu lại thì k gọi API
      if (newValueExpand === false) return;
      refTabTaiLieuBoiThuongPheDuyet?.current?.setViTriHangMucDangTai(index);
      let response = await getTaiLieuBoiThuong(imageDataTmp[index].ma);
      refTabTaiLieuBoiThuongPheDuyet?.current?.setViTriHangMucDangTai(null);
      let imagesTmp = response.map((item) => {
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          ma: item.ma_file,
          ten: item.nhom_anh,
          nhom_hang_muc: item.nhom_hang_muc,
        };
        item.nhom = nhom;
        return item;
      });
      setImageData((prevValue) => {
        prevValue[index].images = imagesTmp;
        if (!daExpand) prevValue[index].expanded = !prevValue[index].expanded; //bên trên chưa expand thì mới cần expand ra
        return [...prevValue];
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressExpandAllHangMuc = async (expandAllHangMuc) => {
    try {
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        let imageDataStep3Tmp = [...imageDataStep3];
        for (let i = 0; i < imageDataStep3Tmp.length; i++) {
          let hangMucAnh = imageDataStep3Tmp[i];
          if (expandAllHangMuc === true) {
            if (hangMucAnh.images.length === 0) {
              refTabTaiLieuBoiThuongPheDuyet?.current?.setViTriHangMucDangTai(i);
              let response = await getTaiLieuBoiThuong(hangMucAnh.ma);
              let imagesTmp = response.map((item) => {
                item.checked = false;
                item.path = item.duong_dan;
                item.name = item.ten_file;
                let nhom = {
                  checked: false,
                  ma: item.ma_file,
                  ten: item.nhom_anh,
                  nhom_hang_muc: item.nhom_hang_muc,
                };
                item.nhom = nhom;
                return item;
              });
              imageDataStep3Tmp[i].images = imagesTmp;
            }
            imageDataStep3Tmp[i].expanded = true;
          } else if (expandAllHangMuc === false) imageDataStep3Tmp[i].expanded = false;
        }
        refTabTaiLieuBoiThuongPheDuyet?.current?.setViTriHangMucDangTai(null);
        setImageDataStep3([...imageDataStep3Tmp]);
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        let imageDataStep4Tmp = [...imageDataStep4];
        for (let i = 0; i < imageDataStep4Tmp.length; i++) {
          let hangMucAnh = imageDataStep4Tmp[i];
          if (expandAllHangMuc === true) {
            if (hangMucAnh.images.length === 0) {
              refTabTaiLieuBoiThuongPheDuyet?.current?.setViTriHangMucDangTai(i);
              let response = await getTaiLieuBoiThuong(hangMucAnh.ma);
              let imagesTmp = response.map((item) => {
                item.checked = false;
                item.path = item.duong_dan;
                item.name = item.ten_file;
                let nhom = {
                  checked: false,
                  ma: item.ma_file,
                  ten: item.nhom_anh,
                  nhom_hang_muc: item.nhom_hang_muc,
                };
                item.nhom = nhom;
                return item;
              });
              imageDataStep4Tmp[i].images = imagesTmp;
            }
            imageDataStep4Tmp[i].expanded = true;
          } else if (expandAllHangMuc === false) imageDataStep4Tmp[i].expanded = false;
        }
        refTabTaiLieuBoiThuongPheDuyet?.current?.setViTriHangMucDangTai(null);
        setImageDataStep4([...imageDataStep4Tmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  /** RENDER */
  //render ra nút của Hồ sơ phê duyệt
  const renderApprovalButton = () => {
    if (!profileData || !profileData.ho_so) return null;
    return <ButtonLinear title="Xem file tờ trình" onPress={onPressViewFileDetail} linearStyle={{marginHorizontal: spacing.smaller}} />;
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={'Chi tiết phê duyệt'}
      renderView={
        <View style={styles.container}>
          <ScrollableTabView ref={tabViewRef} initialPage={0} renderTabBar={() => <CustomTabBar />} onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))}>
            <ScrollView
              tabLabel="Thông tin hồ sơ"
              scrollEnabled={true}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
              ref={scrollViewRef}
              showsVerticalScrollIndicator={false}>
              <TabThongTinHoSoPheDuyet profileData={profileData} />
            </ScrollView>
            <View tabLabel="Tài liệu bồi thường" style={styles.centerView}>
              <TabTaiLieuBoiThuongPheDuyet
                ref={refTabTaiLieuBoiThuongPheDuyet}
                profileData={profileData}
                imageDataStep1={imageDataStep1}
                imageDataStep2={imageDataStep2}
                imageDataStep3={imageDataStep3}
                imageDataStep4={imageDataStep4}
                listTaiLieuPdf={listTaiLieuPdf}
                dataAnhCapDon={dataAnhCapDon}
                onPressOpenImageView={onPressOpenImageView}
                // xử lý CHỌN ẢNH
                onPressToggleExpandHangMuc={onPressToggleExpandHangMuc}
                // xử lý hiển thị Thông tin phân loại
                setXemTaiLieuSelected={setXemTaiLieuSelected}
                xemTaiLieuSelected={xemTaiLieuSelected}
                onPressExpandAllHangMuc={onPressExpandAllHangMuc}
                anhThuHoiVatTu={anhThuHoiVatTu}
              />
            </View>
          </ScrollableTabView>
        </View>
      }
      footer={<View style={styles.footerView}>{renderApprovalButton()}</View>}
    />
  );
};

export const ApprovalProfileScreen = memo(ApprovalProfileScreenComponent, isEqual);
