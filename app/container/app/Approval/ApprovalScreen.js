import { colors } from '@app/commons/Theme.js';
import NavigationUtil from '@app/navigation/NavigationUtil.js';
import { selectUser } from '@app/redux/slices/UserSlice';
import { default as AxiosConfig } from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { FlashMessageHelper } from '@app/utils/FlashMessageHelper';
import { BottomTabs, Icon, ScreenComponent, SearchBar, Text } from '@component';
import { DATA_CONSTANT, NGHIEP_VU, SCREEN_ROUTER_APP } from '@constant';
import R from '@R';
import React, { memo, useEffect, useRef, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Alert, Image, Keyboard, RefreshControl, ScrollView, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { NumericFormat } from 'react-number-format';
import { useSelector } from 'react-redux';
import styles from './ApprovalStyle';
import moment from 'moment';
import { ModalFilterTheoNgay } from './Components';
import { selectChiNhanhBaoHiemDangCay } from '@app/redux/slices/CategoryCommonSlice';

const startOfYear = moment().startOf('year').format('YYYYMMDD');
const currentDate = moment().format('YYYYMMDD');
let timer;

const defaultObjParams = {
  ngayd: +startOfYear,
  ngayc: +currentDate,
  trang_thai: '',
  nv: '',
  nsd_duyet: '',
  ma_chi_nhanh: '',
  loai: '',
  so_hs: '',
  doi_tuong: '',
  phe_duyet: '', //1 Phê duyệt chính, 0 Kiểm tra
  tim: '',
};
const ApprovalScreenComponent = ({ noRightIcon, navigation }) => {
  console.log('ApprovalScreenComponent');
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const [approvalData, setApprovalData] = useState([]);
  const [searchInput, setSearchInput] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const userInfo = useSelector(selectUser);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(20);
  const [chiNhanhBaoHiem, setChiNhanhBaoHiem] = useState([]);
  const [objParams, setObjParams] = useState(defaultObjParams);

  let refModalFilterTheoNgay = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDanhSachHoSoPheDuyet(objParams, [], 1, 20);
      setCurrent(1);
      initDonViXuLy();
    });
    return () => {
      if (timer) clearTimeout();
    };
  }, []);

  const initDonViXuLy = () => {
    try {
      // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
      let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay;
      chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
        return {
          ...item,
          listCon: [],
          isExpand: true,
          isCheck: false, //bỏ check or check
          hasChildCheck: false, //list chi nhánh cha, có child checked
          isShow: true,
        };
      });
      let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
      for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
        let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
        chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
      }
      setChiNhanhBaoHiem(chiNhanhBaoHiemCha);
    } catch (error) {
      console.log(error.message);
    }
  };
  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    try {
      let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
      let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
      if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
      if (listConLai.length === 0) return [];
      else {
        for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
        return listConFilter;
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const onRefresh = () => {
    getDanhSachHoSoPheDuyet(objParams, [], 1, 20);
    setCurrent(1);
  };

  const onPressItem = (item) => {
    if (item.nv === NGHIEP_VU.NG) NavigationUtil.push(SCREEN_ROUTER_APP.APPROVAL_NG_PROFILE, { profileDetail: item });
    else if (item.nv === NGHIEP_VU.XE || NGHIEP_VU.HSGD) {
      NavigationUtil.push(item.ma === DATA_CONSTANT.LOAI_HO_SO_PHE_DUYET.TRINH_THANH_TOAN ? SCREEN_ROUTER_APP.TRINH_THANH_TOAN : SCREEN_ROUTER_APP.APPROVAL_PROFILE, {
        profileDetail: item,
      });
    } else if (item.nv === NGHIEP_VU.THANH_TOAN) {
      NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
        profileData: {
          ho_so: {
            ...item,
            ma_mau_in: 'ESCS_TTBT',
            ma_action_api: 'HETBM85XP1TYUN3',
            url_file: userInfo.nguoi_dung.ma_doi_tac + '/MAU_IN/PDF/mau_in_bang_ke_thanh_toan_boi_thuong.xml',
            trang_thai_duyet: item.trang_thai_ma,
            create_file_sign: 'ESCS_TTBT',
            remove_file: 'ESCS_TTBT',
            loai: item.ma,
          },
        },
        prevScreen: SCREEN_ROUTER_APP.APPROVAL_PROFILE,
      });
    }
  };

  const getDanhSachHoSoPheDuyet = async (defaultObj, oldData, trang = 1, so_dong = 20) => {
    setDialogLoading(true);
    try {
      let subObj = {
        trang: trang,
        so_dong: so_dong,
      };
      let params = { ...subObj, ...defaultObj };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.APPROVAL_LIST, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setTotal(response.data_info.tong_so_dong);
      let listPheDuyetTmp = [...oldData, ...response.data_info.data];
      setApprovalData(listPheDuyetTmp);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressSearch = async (defaultObj) => {
    Keyboard.dismiss();
    if (!objParams.tim.trim()) {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập số hồ sơ', 'info');
      return;
    }
    setDialogLoading(true);
    let params = defaultObj;
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.APPROVAL_LIST, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setApprovalData([...response.data_info.data]);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setCurrent(current + 1);
      getDanhSachHoSoPheDuyet(objParams, approvalData, current + 1);
    }
  };

  const onCloseModalFilter = (params) => {
    refModalFilterTheoNgay.current.hide();
    setObjParams(params);

    timer = setTimeout(() => {
      // if (searchInput && searchInput !== '') onPressSearch(params, searchInput);
      // else
      setSearchInput('');
      getDanhSachHoSoPheDuyet(params, [], 1, 20);
    }, 300);
  };

  const onChangeText = (field, value) => {
    setObjParams((prev) => ({ ...prev, [field]: value }));
  };

  /**RENDER  */
  const renderApprovalItem = ({ item, index }) => {
    let linearColors = [];
    let statusColor = colors.BLACK;
    switch (item.trang_thai_ma) {
      case DATA_CONSTANT.APPROVAL_STATUS.CHUA_DUYET:
        linearColors = [colors.ORANGE2, colors.WHITE];
        statusColor = colors.ORANGE;
        break;
      case DATA_CONSTANT.APPROVAL_STATUS.DA_DUYET:
        linearColors = [colors.GREEN1, colors.WHITE];
        statusColor = colors.GREEN;
        break;
      case DATA_CONSTANT.APPROVAL_STATUS.TU_CHOI_DUYET:
        linearColors = [colors.GRAY2, colors.WHITE];
        statusColor = colors.GRAY9;
        break;
      default:
        linearColors = [colors.WHITE, colors.WHITE];
        statusColor = colors.GRAY9;
        break;
    }
    return (
      <TouchableOpacity key={index} onPress={() => onPressItem(item)} style={styles.profileItemView}>
        <LinearGradient colors={linearColors} start={{ x: 0, y: 0 }} end={{ x: 0, y: 1 }} style={styles.profileItemLinearView}>
          <View style={styles.profileItemCenterView}>
            <View style={styles.profileItemDetail}>
              <Text style={styles.profileTxtHoSo}>
                Loại trình:<Text style={styles.txtLoaiTrinh}> {' ' + (item.loai || '')}</Text>
              </Text>
            </View>
            <Text style={styles.profileTxtHoSo}>
              Người duyệt:
              <Text style={{ color: item.duyet_chinh == '0' ? colors.BLACK : colors.PRIMARY }} children={' ' + (item.duyet_chinh_hthi || '')} />
            </Text>
            <Text style={styles.profileTxtHoSo}>
              Người duyệt kế tiếp:
              <Text style={{ color: colors.PRIMARY }} children={' ' + (item.nguoi_duyet_tiep_theo || '')} />
            </Text>
            <Text style={styles.profileTxtHoSo}>
              Trạng thái duyệt:
              <Text style={{ color: colors.PRIMARY }} children={' ' + (item.trang_thai_duyet_ke_tiep || '')} />
            </Text>
            <Text style={styles.profileTxtHoSo}>Nội dung: {item.noi_dung}</Text>
            <Text style={styles.profileTxtHoSo}>Số hồ sơ: {' ' + (item.so_hs || '')}</Text>
            <View style={styles.profileItemDetail}>
              <NumericFormat value={item.tien} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text>{'Số tiền: ' + value}</Text>} />
              <Text style={styles.profileTxtHoSo}>{item.doi_tuong}</Text>
            </View>
            <View style={styles.profileItemDetail}>
              <Text style={[styles.profileTxtHoSo]}>
                Trạng thái trình:<Text style={[styles.txtTrangThaiTrinh, { color: statusColor }]}>{' ' + (item.trang_thai || '')}</Text>
              </Text>
            </View>
            <View style={styles.profileItemDetail}>
              <Text style={[styles.profileTxtHoSo]}>
                Trạng thái HS:<Text style={[styles.txtTrangThaiTrinh, { color: statusColor }]}>{' ' + (item.trang_thai_hs || '')}</Text>
              </Text>
            </View>
          </View>
          <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={styles.rightIcon} color={!noRightIcon ? colors.BLUE1 : colors.WHITE} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };
  // RENDER HỒ SƠ CHƯA TIẾP NHẬN
  const renderApproval = () => {
    return (
      <>
        {approvalData.length > 0 ? (
          <>
            {/* <FlatList
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
              data={approvalData}
              renderItem={renderApprovalItem}
              keyExtractor={(item) => item.so_id.toString()}
              onEndReached={handleLoadMore}
              onMomentumScrollBegin={() => setOnEndReachedCalledDuringMomentum(false)}
              onEndReachedThreshold={0.2}
              // initialNumToRender={3}

              // onScroll={() => setHasScrolled(true)}
              // onScrollEndDrag={() => setHasScrolled(false)}
            /> */}
            {approvalData.map((item, index) => renderApprovalItem({ item: item, index: index }))}
          </>
        ) : (
          <View style={styles.noDataView}>
            <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
            <Text>Chưa có dữ liệu</Text>
          </View>
        )}
      </>
    );
  };

  const rendeHeader = () => {
    return (
      <View style={styles.renderHeader}>
        <Text style={styles.txtHeaderList}>
          Từ ngày: <Text style={styles.date}>{moment(objParams.ngayd, 'YYYYMMDD').format('DD/MM/YYYY')}</Text> đến ngày
          <Text style={styles.date}> {moment(objParams.ngayc, 'YYYYMMDD').format('DD/MM/YYYY')}</Text>
        </Text>
        <TouchableOpacity style={styles.btnSearch} onPress={() => refModalFilterTheoNgay.current.show()}>
          <Icon.AntDesign name="filter" size={26} color={colors.PRIMARY} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      header
      screenTitle="Hồ sơ phê duyệt"
      hightNavbar
      renderView={
        <View style={styles.container}>
          <View style={{ marginTop: -40 }}>
            <SearchBar value={objParams.tim} onPressSearch={() => onPressSearch(objParams)} onTextChange={(value) => onChangeText('tim', value)} placeholder="Tìm kiếm số hồ sơ, biển kiểm soát" />
          </View>
          <View style={styles.contentView}>
            {rendeHeader()}
            <ScrollView showsVerticalScrollIndicator refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} onMomentumScrollEnd={(event) => handleLoadMore()}>
              <View style={{ marginBottom: 60 }}>{renderApproval()}</View>
            </ScrollView>
          </View>
          <BottomTabs indexActive={2} />
          <ModalFilterTheoNgay
            ref={refModalFilterTheoNgay}
            onPressSearch={(params) => onCloseModalFilter(params)}
            onBackPress={() => refModalFilterTheoNgay.current.hide()}
            chiNhanhBaoHiem={chiNhanhBaoHiem}
            onResetForm={initDonViXuLy}
          />
        </View>
      }
    />
  );
};

export const ApprovalScreen = memo(ApprovalScreenComponent, isEqual);
