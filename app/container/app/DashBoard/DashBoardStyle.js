import { colors } from '@app/commons/Theme';
import { spacing } from '@app/theme';
import { StyleSheet } from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  filterView: {
    marginTop: 20,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  chart: {
    flex: 1,
  },
  thongKeTheoTrangThaiView: {
    justifyContent: 'center',
    flex: 1,
    minHeight: 200,
    marginVertical: 10,
    borderRadius: 10,
    borderColor: colors.GRAY,
    borderWidth: 1,
    paddingVertical: 5,
    paddingHorizontal: 10,
  },
  titleThongKe: {
    fontWeight: 'bold',
    fontSize: 16,
    marginVertical: 3,
    color: colors.BLACK,
  },
  statusView: {
    marginVertical: 5,
  },
  txtStatus: {
    fontSize: 12,
    marginLeft: 5,
    fontWeight: 'bold',
    color: colors.GRAY10,
  },
  pieTrangThai: {
    alignSelf: 'center',
    marginBottom: 10,
  },
  processBar: {
    backgroundColor: colors.WHITE13,
    borderRadius: 10,
  },
  barTitleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 3,
  },
  txtStatusCount: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.GRAY10,
  },
  barTitleTotalView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  txtStatusTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.BLACK,
  },
  thongKeChungBoxView: {
    // backgroundColor: colors.PRIMARY,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.GRAY,
    marginBottom: 10,
  },
  thongKeChungDataView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
    marginVertical: 10,
  },
  thongKeChungSoLieuView: {
    marginHorizontal: 20,
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 1,
  },
  thongKeChungItemView: {
    alignItems: 'center',
    borderRightWidth: 1,
    borderColor: colors.WHITE,
    paddingRight: 10,
  },
  thongKeChungItemTitle: {
    textAlign: 'center',
    color: colors.PRIMARY,
  },
  thongKeChungItemLabel: {
    textAlign: 'center',
    color: colors.GREEN,
  },
  thongKeChungItemValue: {
    marginLeft: 5,
    textAlign: 'center',
    color: colors.PRIMARY,
  },
  hoSoTonTitleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
  hoSoTonValueView: {
    marginVertical: 3,
    borderBottomWidth: 0.5,
    borderBottomColor: colors.GRAY,
    paddingVertical: 8,
    flexDirection: 'row',
    color: colors.BLACK_06,
    flex: 1,
  },
  hoSoTonValue: {
    color: colors.BLACK_06,
    flex: 1,
  },
  hoSoTonTitle: {
    flex: 1,
    color: colors.BLACK_06,
    fontWeight: 'bold',
  },
  thongKeTheoNgayView: {
    borderWidth: 1,
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 10,
    borderColor: colors.GRAY,
  },
  content: {
    marginHorizontal: spacing.small,
  },
  detailRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.smaller
  }
});
