import { colors } from '@app/commons/Theme';
import { selectNghiepVuGiamDinh, selectUser } from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { dimensions, spacing } from '@app/theme';
import { BottomTabs, Empty, Icon, ScreenComponent, Text } from '@component';
import moment from 'moment';
import React, { memo, useEffect, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Alert, FlatList, ScrollView, TouchableOpacity, View } from 'react-native';
import * as Progress from 'react-native-progress';
import { NumericFormat } from 'react-number-format';
import { useSelector } from 'react-redux';
import { ModalFilter } from './Compoents';
import styles from './DashBoardStyle';

// const TONG_SO_TRANG_THAI = ['HSBT_DONG_HS', 'DA_GIAI_QUYET', 'TON_BOI_THUONG', 'TON_GIAM_DINH_HT', 'TON_GIAM_DINH'];
// const BIEU_DO_TRANG_THAI = [
//   'HSBT_XE_GD_CHO_LAY_SO',
//   'HSBT_XE_GD_CHO_CHI_DINH',
//   'HSBT_XE_BT_DUYET_GIA',
//   'HSBT_XE_GD_BD',
//   'HSBT_XE_GD_MOI',
//   'HSBT_XE_BT_DUYET',
//   'HSBT_XE_BT_NHAN_HS',
//   'HSBT_XE_GD_YCGD',
//   'HSBT_DONG_HS',
// ];
const BIEU_DO_TRANG_THAI_TON = [
  // {trang_thai_ton: 'TON_CONTACT', ten_trang_thai: 'Tồn contact'},
  { trang_thai_ton: 'TON_GIAM_DINH', ten_trang_thai: 'Tồn giám định' },
  { trang_thai_ton: 'TON_BOI_THUONG', ten_trang_thai: 'Tồn bồi thường' },
  { trang_thai_ton: 'TON_PHE_DUYET', ten_trang_thai: 'Tồn phê duyệt' },
  { trang_thai_ton: 'TON_KE_TOAN', ten_trang_thai: 'Tồn kế toán' },
];

const PIE_COLORS = ['#7cb5e2', '#90ed7d', '#f3a35c', '#8085e9', '#ef5c80', '#e4d354', '#3b918e', '#e84249', '#434348'];
const getBarColorByStatus = (trangThai) => {
  return BIEU_DO_TRANG_THAI_TON.findIndex((item) => item.trang_thai_ton === trangThai);
};

const ngayDauKy = moment('01072024', 'DDMMYYYY').toDate()
const DashBoardScreenComponent = () => {
  console.log('DashBoardScreen');
  const nghiepVuGiamDinh = useSelector(selectNghiepVuGiamDinh)
  const userInfo = useSelector(selectUser)
  const [dialogLoading, setDialogLoading] = useState(false);

  const [thongKeHoSoData, setThongKeHoSoData] = useState(null);
  // const [listChiNhanh, setListChiNhanh] = useState([{label: 'Tất cả', value: '', ten_tat: 'Tất cả'}]);
  // const [listGDV, setListGDV] = useState([{label: 'Tất cả', value: '', ten_tat: 'Tất cả'}]);
  // const [listNam, setListNam] = useState([]);
  const [paramsFilter, setParamsFilter] = useState({
    ma_doi_tac: userInfo?.nguoi_dung?.ma_doi_tac,
    ma_chi_nhanh: [''],
    ma_chi_nhanh_ql: [''],
    // ma_gdv: [''],
    ngay_dau_ky: nghiepVuGiamDinh === 'XCG' ? moment().startOf('year') : ngayDauKy,
    ngay_cuoi_ky: moment(),
  });
  //modal filter data
  const [toggleModalFilter, setToggleModalFiler] = useState(false);

  //PIE Hồ sơ xử lý theo trạng thái
  useEffect(() => {
    // initNam();
    // initChiNhanh();
    // layDuLieuDashboard();
    layDuLieuBangThongKeChung(paramsFilter);
  }, []);

  useEffect(() => {
    layDuLieuBangThongKeChung(paramsFilter);
  }, [nghiepVuGiamDinh])

  // useEffect(() => {
  //   // layDuLieuDashboard();
  //   layDuLieuBangThongKeChung();
  // }, [paramsFilter]);

  // useEffect(() => {
  //   layGiamDinhVienTheoChiNhanh();
  // }, [paramsFilter.ma_chi_nhanh]);

  /*LẤY GDV THEO CHI NHÁNH */
  // const layGiamDinhVienTheoChiNhanh = async () => {
  //   let maChiNhanh = '';
  //   //nếu chọn tất cả -> thì cho tất cả vào
  //   if (paramsFilter.ma_chi_nhanh[0] === '') maChiNhanh = listChiNhanh.map((item) => item.ma).join(',');
  //   //còn không thì chỉ cho những thằng được chọn vào thôi
  //   else maChiNhanh = paramsFilter.ma_chi_nhanh.join(',');
  //   let params = {
  //     ma_doi_tac: paramsFilter.ma_doi_tac,
  //     ma_chi_nhanh: maChiNhanh,
  //   };
  //   try {
  //     let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
  //     if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //     let listGDV = [{ten: 'Tất cả', ma: '', label: 'Tất cả', value: ''}];
  //     listGDV = listGDV.concat(
  //       response.data_info.map((item) => {
  //         item.label = item.ten;
  //         item.value = item.ma;
  //         return item;
  //       }),
  //     );
  //     setListGDV([...listGDV]);
  //   } catch (error) {
  //     Alert.alert('Thông báo', error.message);
  //   }
  //   return;
  // };

  /* LẤY DỮ LIỆU DASHBOARD */
  // const layDuLieuDashboard = async () => {
  //   let params = {...paramsFilter};
  //   params.tu_ngay = moment(params.tu_ngay).format('DD/MM/YYYY');
  //   params.den_ngay = moment(params.den_ngay).format('DD/MM/YYYY');
  //   if (params.ma_gdv[0] === '') params.ma_gdv = listGDV.map((item) => item.ma); //nếu chọn tất cả gdv -> thì truyền vào tất cả mảng
  //   setDialogLoading(true);
  //   try {
  //     console.log('🚀 ~ layDuLieuDashboard ~ params:', params);
  //     let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_THONG_TIN_DASHBOARD, params);
  //     console.log('🚀 ~ layDuLieuDashboard ~ response:', response);
  //     setDialogLoading(false);
  //     if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //     setThongKeHoSoData(response.data_info);
  //   } catch (error) {
  //     setDialogLoading(false);
  //     Alert.alert('Thông báo', error.message);
  //   }
  // };
  const layDuLieuBangThongKeChung = async (params) => {
    const actionCode = nghiepVuGiamDinh && nghiepVuGiamDinh === 'CNG' ? AxiosConfig.ACTION_CODE.LAY_THONG_TIN_DASHBOARD_SK : AxiosConfig.ACTION_CODE.LAY_THONG_TIN_DASHBOARD_V2
    setDialogLoading(true);
    try {
      params.ngay_dau_ky = moment(params.ngay_dau_ky).format('YYYYMMDD');
      params.ngay_cuoi_ky = moment(params.ngay_cuoi_ky).format('YYYYMMDD');
      let response = await ESmartClaimEndpoint.execute(actionCode, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setThongKeHoSoData(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  // const initNam = () => {
  //   let listNam = [];
  //   for (let i = 0; i < 5; i++) {
  //     listNam.push({
  //       label: moment().year() - i + '',
  //       value: moment().year() - i,
  //     });
  //   }
  //   setListNam(listNam);
  // };
  // const initChiNhanh = () => {
  //   let listChiNhanh = [{ten_tat: 'Tất cả', ma: '', label: 'Tất cả', value: ''}];
  //   listChiNhanh = listChiNhanh.concat(chiNhanhBaoHiem);
  //   setListChiNhanh(listChiNhanh);
  // };

  /* RENDER */
  const renderTrangThaiStatus = (data, extraData) => {
    if (thongKeHoSoData?.bo_phan?.length <= 0) return;
    let { item } = data;
    let colorBarIndex = getBarColorByStatus(item.trang_thai_ton);
    const getCountByStatus = (title) => {
      let data = thongKeHoSoData.bo_phan.filter((item) => item.trang_thai_ton === title);
      let sumTongHoSoTon = 0;
      for (let i = 0; i < data.length; i++) {
        sumTongHoSoTon += data[i].sl;
      }
      return sumTongHoSoTon;
    };
    return (
      <View style={[styles.statusView]}>
        <View style={styles.barTitleView}>
          <Text children={item.ten_trang_thai} style={styles.txtStatus} />
          <Text
            children={getCountByStatus(item.trang_thai_ton) + ' (' + Math.round((getCountByStatus(item.trang_thai_ton) / extraData.tongHoSo) * 100).toFixed(2) + '%)'}
            style={styles.txtStatusCount}
          />
        </View>
        <Progress.Bar
          progress={getCountByStatus(item.trang_thai_ton) / extraData.tongHoSo}
          height={10}
          width={dimensions.width * 0.95}
          color={colorBarIndex != -1 ? PIE_COLORS[colorBarIndex] : colors.GRAY10}
          style={styles.processBar}
          borderWidth={0}
        />
      </View>
    );
  };
  const renderThongKeTheoTrangThai = () => {
    if (!thongKeHoSoData) return;
    let tongHoSo = 0;
    thongKeHoSoData.bo_phan?.map((item) => {
      tongHoSo = tongHoSo + item.sl;
    });
    return (
      <View style={styles.thongKeTheoTrangThaiView}>
        <View style={styles.barTitleTotalView}>
          <Text children="Hồ sơ tồn theo bộ phận" style={styles.titleThongKe} />
          {/* <Text children={thongKeHoSoData.nam?.sl} style={styles.txtStatusTotal} /> */}
          <Text children={tongHoSo} style={styles.txtStatusTotal} />
        </View>
        {thongKeHoSoData.bo_phan?.length > 0 ? (
          <FlatList data={BIEU_DO_TRANG_THAI_TON} renderItem={(item) => renderTrangThaiStatus(item, { tongHoSo: thongKeHoSoData.nam?.sl })} />
        ) : (
          <Empty description="Không có dữ liệu" />
        )}
      </View>
    );
  };
  const renderThongKeChung = () => {
    if (!thongKeHoSoData) return;
    // let tongSoTien = 0,
    //   tongSoHoSo = 0,
    //   tongSoTienTon = 0,
    //   tongHoSoTon = 0,
    //   hoSoDaGiaiQuyet = null;
    // thongKeHoSoData.bo_phan?.map((item) => {
    //   tongSoTien = tongSoTien + item.tien;
    //   tongSoHoSo = tongSoHoSo + item.sl;
    //   if (item.trang_thai_ton == 'TON_BOI_THUONG' || item.trang_thai_ton == 'TON_GIAM_DINH_HT' || item.trang_thai_ton == 'TON_GIAM_DINH') {
    //     tongSoTienTon = tongSoTienTon + item.tien;
    //     tongHoSoTon = tongHoSoTon + item.sl;
    //   }
    //   if (item.trang_thai_ton == 'DA_GIAI_QUYET') hoSoDaGiaiQuyet = item;
    // });
    // let pieData = [];
    // pieData.push(
    //   {
    //     label: 'Tổng hồ sơ',
    //     value: tongSoHoSo,
    //   },
    //   {
    //     label: 'Đã giải quyết',
    //     value: thongKeHoSoData.da_giai_quyet.sl,
    //   },
    //   {
    //     label: 'Còn tồn',
    //     value: tongHoSoTon,
    //   },
    // );

    const renderThongKeChiTiet = (title, tong_so_ho_so, tong_so_tien, color, borderWitdh) => {
      let tongSoHoSoFormat = '';
      let tongSoTienFormat = '';
      if (tong_so_ho_so) tongSoHoSoFormat = tong_so_ho_so.toString();
      if (tong_so_tien) tongSoTienFormat = tong_so_tien.toString();
      // if (tong_so_tien > 999999999) tongSoTienFormat = (tong_so_tien / 1000000000).toFixed(3).toString();

      return (
        <View flex={1} borderRightWidth={borderWitdh || 0} borderColor={colors.GRAY}>
          <Text children={title} style={styles.thongKeChungItemTitle} font="medium14" />
          <View>
            <Text children={tongSoHoSoFormat} style={[styles.thongKeChungItemTitle, { color: '#dc3545', marginVertical: spacing.tiny }]} font="bold16" />
            {tongSoTienFormat != '' && (
              <NumericFormat
                value={tongSoTienFormat}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => (
                  <Text font="medium14" style={[styles.thongKeChungItemValue]}>
                    {value.replace('.', ',')}
                  </Text>
                )}
              />
            )}
          </View>
        </View>
      );
    };

    return (
      <View>
        <View style={[styles.thongKeChungBoxView, { paddingVertical: 10 }]}>
          <Text children={'Tổng số hồ sơ phát sinh trong kỳ'} style={[styles.thongKeChungItemLabel, { marginBottom: spacing.smaller }]} font="medium16" />
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View flex={1}>
              <Text children={'Tổng số hồ sơ'} style={[styles.thongKeChungItemTitle, { marginVertical: 4 }]} font="medium14" />
              <Text children={'Tổng số tiền'} style={styles.thongKeChungItemTitle} font="medium14" />
            </View>
            <View flex={1}>
              <Text children={thongKeHoSoData.nam?.sl} style={[styles.thongKeChungItemTitle, { color: '#dc3545', marginVertical: spacing.tiny }]} font="bold16" />
              <NumericFormat
                value={thongKeHoSoData.nam?.tien}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => (
                  <Text font="medium14" style={[styles.thongKeChungItemValue]}>
                    {value.replace('.', ',')}
                  </Text>
                )}
              />
            </View>
          </View>
        </View>
        <View style={[styles.thongKeChungBoxView]}>
          <View style={styles.thongKeChungDataView}>
            {renderThongKeChiTiet('Đã giải quyết', thongKeHoSoData.da_giai_quyet?.sl || '0', thongKeHoSoData.da_giai_quyet?.tien || '0', '#ffc107', 1)}
            {renderThongKeChiTiet('Chưa giải quyết', thongKeHoSoData.chua_giai_quyet?.sl || '0', thongKeHoSoData.chua_giai_quyet?.tien || '0', '#dc3545')}
          </View>
        </View>
        <View style={[styles.thongKeChungBoxView]}>
          <View style={styles.thongKeChungDataView}>
            {renderThongKeChiTiet('Hồ sơ phát sinh ngày ' + thongKeHoSoData.ngay?.thoi_gian, thongKeHoSoData.ngay?.sl || '0', thongKeHoSoData.ngay?.tien || '0', '#17a2b8', 1)}
            {renderThongKeChiTiet('Hồ sơ phát sinh tháng ' + thongKeHoSoData.thang?.thoi_gian, thongKeHoSoData.thang?.sl || '0', thongKeHoSoData.thang?.tien || '0', '#dc3545')}
          </View>
        </View>

        <View style={[styles.thongKeChungBoxView]}>
          <View style={[styles.thongKeChungDataView]}>
            <View flex={1} borderRightWidth={1} borderColor={colors.GRAY}>
              <Text children={'Số tiền dự phòng\nbình quân'} style={styles.thongKeChungItemTitle} font="medium14" />
              <NumericFormat
                value={thongKeHoSoData.tien_dp_tb?.dp_trung_binh}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => (
                  <Text style={[styles.thongKeChungItemTitle, { color: '#dc3545', marginVertical: spacing.tiny }]} font="bold16">
                    {value}
                  </Text>
                )}
              />
            </View>
            {renderThongKeChiTiet('Số ngày giải quyết\nbình quân', thongKeHoSoData.ngay_dp_tb?.tgian_trung_binh || '0', '', colors.PRIMARY)}
          </View>
        </View>
        <View style={[styles.thongKeChungBoxView]}>
          <View style={styles.thongKeChungDataView}>
            {renderThongKeChiTiet('Số vụ tổn thất bình quân/tháng', thongKeHoSoData.so_vu_tb_thang?.so_vu_tb_thang || '0', '', colors.PRIMARY, 1)}
            {renderThongKeChiTiet('Số vụ tổn thất bình quân/ngày', thongKeHoSoData.so_vu_tb_ngay?.so_vu_tb_ngay || '0', '', colors.PRIMARY)}
          </View>
        </View>
      </View>
    );
  };

  // const renderTonNgayItem = (data, extraData) => {
  //   let item = data.item;
  //   return (
  //     <View style={[styles.hoSoTonValueView]}>
  //       <Text children={item.khung} style={styles.hoSoTonValue} />
  //       <Text children={item.sl} style={[styles.hoSoTonValue, {textAlign: 'right'}]} />
  //       <Text children={(item.sl > 0 ? Math.round((item.sl / extraData.tongHoSo) * 100).toFixed(2) : 0) + '%'} style={[styles.hoSoTonValue, {textAlign: 'right'}]} />
  //     </View>
  //   );
  // };
  // const renderThongKeHoSoTonTheoNgay = () => {
  //   if (thongKeHoSoData.length == 0) return;
  //   let tongHoSo = 0;
  //   thongKeHoSoData.ton_ngay?.map((item) => (tongHoSo = tongHoSo + item.sl));
  //   return (
  //     <View style={styles.thongKeTheoNgayView}>
  //       <View style={styles.barTitleTotalView}>
  //         <Text children="Hồ sơ tổn thất theo ngày" style={styles.titleThongKe} />
  //         <Text children={tongHoSo} style={styles.txtStatusTotal} />
  //       </View>
  //       {/* <Text children={'Hồ sơ tổn theo ngày ' + tongHoSo} style={styles.titleThongKe} /> */}
  //       <View style={styles.hoSoTonTitleView}>
  //         <Text children="Số ngày" style={styles.hoSoTonTitle} />
  //         <Text children="Số lượng" style={[styles.hoSoTonTitle, {textAlign: 'right'}]} />
  //         <Text children="Tỷ trọng" style={[styles.hoSoTonTitle, {textAlign: 'right'}]} />
  //       </View>
  //       <FlatList data={thongKeHoSoData.bo_phan} renderItem={(item) => renderTonNgayItem(item, {tongHoSo: tongHoSo})} />
  //     </View>
  //   );
  // };
  const renderFilterView = () => {
    return (
      <View style={styles.filterView}>
        <Text children="Thống kê chung" style={styles.titleThongKe} />
        <TouchableOpacity
          hitSlop={{
            bottom: 20,
            left: 20,
            right: 20,
            top: 20,
          }}
          onPress={() => setToggleModalFiler(true)}>
          <Icon.FontAwesome name="search" size={25} color={'#5e77f8'} />
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      header
      renderView={
        <View style={styles.container}>
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {renderFilterView()}
            {nghiepVuGiamDinh === 'XCG' &&
              <View>
                {renderThongKeChung()}
                {renderThongKeTheoTrangThai()}
              </View>
            }

            {nghiepVuGiamDinh === 'CNG' &&
              <View>
                <RenderSoLuongHoSoSucKhoePhatSinhTheoThoiGian data={thongKeHoSoData} />
                <RenderThongKeTheoTrangThai data={thongKeHoSoData} />
              </View>
            }

            {/* <View marginBottom={100}>{renderThongKeHoSoTonTheoNgay()}</View> */}
          </ScrollView>
          <ModalFilter
            toggleModalFilter={toggleModalFilter}
            closeModal={() => setToggleModalFiler(false)}
            // listChiNhanh={listChiNhanh}
            // listGDV={listGDV}
            // listNam={listNam}
            paramsFilter={paramsFilter}
            setParamsFilter={setParamsFilter}
            dialogLoading={dialogLoading}
            onPressSearch={(param) => layDuLieuBangThongKeChung(param)}
          />
          <BottomTabs indexActive={1} />
        </View>
      }
    />
  );
};
export const DashBoardScreen = memo(DashBoardScreenComponent, isEqual);

const RenderSoLuongHoSoSucKhoePhatSinhTheoThoiGian = (data) => {
  const { nam = null, ngay = null, thang = null } = data.data || {};

  const renderContentTable = (label, value) => {
    return (
      <View style={styles.detailRow}>
        <Text children={label} style={[styles.thongKeChungItemTitle]} font="medium14" />
        {label.includes('HSBT') ? (
          <Text children={value} style={[styles.thongKeChungItemValue, { color: colors.RED1 }]} font="medium14" />
        ) : (
          <NumericFormat
            value={value}
            displayType={'text'}
            thousandSeparator={true}
            renderText={(text) => (
              <Text font="medium14" style={[styles.thongKeChungItemValue]}>
                {text || '--'}
              </Text>
            )}
          />
        )}
      </View>
    )
  }

  return (
    <View>
      <View style={[styles.thongKeChungBoxView, { paddingVertical: 10 }]}>
        <Text children={'Tổng số hồ sơ phát sinh trong năm'} style={[styles.thongKeChungItemLabel, { marginBottom: spacing.smaller }]} font="medium16" />
        {renderContentTable('HSBT phát sinh', nam?.sl)}
        {renderContentTable('Số tiền yêu cầu', nam?.tien_yc)}
        {renderContentTable('HSBT đã giải quyết', nam?.sl_dgq)}
        {renderContentTable('Số tiền duyệt', nam?.tien)}
        {renderContentTable('Số tiền dự phòng', nam?.tien_dp)}
      </View>
      <View style={[styles.thongKeChungBoxView, { paddingVertical: 10 }]}>
        <Text children={'Tổng số hồ sơ phát sinh ngày ' + ngay?.thoi_gian} style={[styles.thongKeChungItemLabel, { marginBottom: spacing.smaller }]} font="medium16" />
        {renderContentTable('HSBT phát sinh', ngay?.sl)}
        {renderContentTable('Số tiền yêu cầu', ngay?.tien_yc)}
        {renderContentTable('HSBT đã giải quyết', ngay?.sl_dgq)}
        {renderContentTable('Số tiền duyệt', ngay?.tien)}
      </View>
      <View style={[styles.thongKeChungBoxView, { paddingVertical: 10 }]}>
        <Text children={'Tổng số hồ sơ phát sinh tháng ' + thang?.thoi_gian_mobile} style={[styles.thongKeChungItemLabel, { marginBottom: spacing.smaller }]} font="medium16" />
        {renderContentTable('HSBT phát sinh', thang?.sl)}
        {renderContentTable('Số tiền yêu cầu', thang?.tien_yc)}
        {renderContentTable('HSBT đã giải quyết', thang?.sl_dgq)}
        {renderContentTable('Số tiền duyệt', thang?.tien)}
        {renderContentTable('HSBT chưa giải quyết', thang?.sl_cgq)}
      </View>
    </View>
  );
}

const RenderThongKeTheoTrangThai = (data) => {

  const BIEU_DO_TRANG_THAI_TON_CNG = [
    { trang_thai_ton: 'TON_TIEP_NHAN', ten_trang_thai: 'Tồn ở bộ phận tiếp nhận' },
    { trang_thai_ton: 'TON_BAO_LANH', ten_trang_thai: 'Tồn ở bộ phận bảo lãnh' },
    { trang_thai_ton: 'TON_BOI_THUONG', ten_trang_thai: 'Tồn ở bộ phận bồi thường' },
    { trang_thai_ton: 'CHO_CHUNG_TU', ten_trang_thai: 'Hồ sơ chờ chứng từ' },
    { trang_thai_ton: 'TON_THANH_TOAN', ten_trang_thai: 'Tồn ở bộ phận thanh toán' },
  ];

  const getBarColorByStatus = (trangThai) => {
    return BIEU_DO_TRANG_THAI_TON_CNG.findIndex((item) => item.trang_thai_ton === trangThai);
  };

  const renderTrangThaiStatus = (data, extraData) => {
    if (thongKeHoSoData?.bo_phan?.length <= 0) return;
    let { item } = data;
    let colorBarIndex = getBarColorByStatus(item.trang_thai_ton);
    const getCountByStatus = (title) => {
      let data = thongKeHoSoData.bo_phan.filter((item) => item.trang_thai_ton === title);
      let sumTongHoSoTon = 0;
      for (let i = 0; i < data.length; i++) {
        sumTongHoSoTon += data[i].sl;
      }
      return sumTongHoSoTon;
    };
    return (
      <View style={[styles.statusView]}>
        <View style={styles.barTitleView}>
          <Text children={item.ten_trang_thai} style={styles.txtStatus} />
          <Text
            children={getCountByStatus(item.trang_thai_ton) + ' (' + Math.round((getCountByStatus(item.trang_thai_ton) / extraData.tongHoSo) * 100).toFixed(2) + '%)'}
            style={styles.txtStatusCount}
          />
        </View>
        <Progress.Bar
          progress={getCountByStatus(item.trang_thai_ton) / extraData.tongHoSo}
          height={10}
          width={dimensions.width * 0.95}
          color={colorBarIndex != -1 ? PIE_COLORS[colorBarIndex] : colors.GRAY10}
          style={styles.processBar}
          borderWidth={0}
        />
      </View>
    );
  };

  const thongKeHoSoData = data.data
  if (!thongKeHoSoData) return;
  let tongHoSo = 0;
  thongKeHoSoData.bo_phan?.map((item) => {
    if (item.trang_thai_ton !== 'TON_CONTACT') tongHoSo = tongHoSo + item.sl;
  });
  return (
    <View style={styles.thongKeTheoTrangThaiView}>
      <View style={styles.barTitleTotalView}>
        <Text children="Hồ sơ tồn theo bộ phận" style={styles.titleThongKe} />
        <Text children={tongHoSo} style={styles.txtStatusTotal} />
      </View>
      {thongKeHoSoData.bo_phan?.length > 0 ? (
        <FlatList data={BIEU_DO_TRANG_THAI_TON_CNG} renderItem={(item) => renderTrangThaiStatus(item, { tongHoSo: thongKeHoSoData.nam?.sl })} />
      ) : (
        <Empty description="Không có dữ liệu" />
      )}
    </View>
  );
};
