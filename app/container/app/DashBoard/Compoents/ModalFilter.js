import {colors} from '@app/commons/Theme';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import {dimensions, spacing} from '@app/theme';
import {ButtonLinear, Icon, ModalChiNhanhTheoDangCay, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
const ModalFilterComponent = (props) => {
  const {toggleModalFilter, closeModal, paramsFilter, setParamsFilter, onPressSearch} = props;
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  // const [chiNhanhSelected, setChiNhanhSelected] = useState(['']);
  // const [gdvSelected, setGDVSelected] = useState(['']);
  // const [namSelected, setNamSelected] = useState(moment().year());
  // const [thangSelected, setThangSelected] = useState(moment().month() + 1);
  const [toggleTuNgay, setToggleTuNgay] = useState(false);
  const [toggleDenNgay, setToggleDenNgay] = useState(false);
  const [listMaDonViXuLySelected, setListMaDonViXuLySelected] = useState([]);
  const [listMaDonViCapDonSelected, setListMaDonViCapDonSelected] = useState([]);

  // const refModalChiNhanh = useRef(null);
  // const refModalGDV = useRef(null);
  // const refModalNam = useRef(null);
  // const refModalThang = useRef(null);
  let refModalDonViXuLy = useRef(null);
  let refModalDonViCapDon = useRef(null);

  const initModalData = () => {
    initDonViCapdon();
    initDonViXuLy();
  };

  // useEffect(() => {
  //   initDonViCapdon(inputType);
  // }, [inputType]);
  // useEffect(() => {
  //   if (listMaDonViXuLySelected.length > 0) onChangeDonViXuLy('', '', listMaDonViXuLySelected);
  //   else setListNguoiXuLy([]);
  // }, [listMaDonViXuLySelected]);

  const initDonViCapdon = () => {
    try {
      let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
      chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
        return {
          ...item,
          listCon: [],
          isExpand: true,
          isCheck: false, //bỏ check or check
          hasChildCheck: false, //list chi nhánh cha, có child checked
          isShow: true,
        };
      });
      let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
      for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
        let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
        chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
      }
      refModalDonViCapDon?.current?.setData(chiNhanhBaoHiemCha);
    } catch (error) {
      console.log(error);
    }
  };
  const initDonViXuLy = () => {
    try {
      // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
      let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay;
      chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
        return {
          ...item,
          listCon: [],
          isExpand: true,
          isCheck: false, //bỏ check or check
          hasChildCheck: false, //list chi nhánh cha, có child checked
          isShow: true,
        };
      });
      let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
      for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
        let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
        chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
      }
      refModalDonViXuLy?.current?.setData(chiNhanhBaoHiemCha);
    } catch (error) {
      console.log(error.message);
    }
  };
  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    try {
      let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
      let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
      if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
      if (listConLai.length === 0) return [];
      else {
        for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
        return listConFilter;
      }
    } catch (error) {
      console.log(error);
    }
  };

  // useEffect(() => {
  //   if (listGDV.length > 0) setGDVSelected([listGDV[0].ma]);
  // }, [listGDV]);

  const resetFilter = () => {
    // setChiNhanhSelected(['']);
    // setGDVSelected(['']);
    // setNamSelected(moment().year());
    // setThangSelected(moment().month() + 1);
    try {
      setParamsFilter((prevValue) => {
        prevValue.ngay_dau_ky = moment().startOf('year');
        prevValue.ngay_cuoi_ky = moment();
        prevValue.ma_chi_nhanh = [''];
        prevValue.ma_chi_nhanh_ql = [''];
        setListMaDonViCapDonSelected([]);
        setListMaDonViXuLySelected([]);
        return {...prevValue};
      });
    } catch (error) {
      console.log(error);
    }
  };

  const onSelectDonViXuLy = (listMaChiNhanh) => {
    try {
      setListMaDonViXuLySelected(listMaChiNhanh);
      setParamsFilter((prevValue) => {
        prevValue.ma_chi_nhanh = listMaChiNhanh;
        return {...prevValue};
      });
    } catch (error) {
      console.log(error);
    }
  };
  const onSelectDonViCapDon = (listMaChiNhanh) => {
    try {
      setListMaDonViCapDonSelected(listMaChiNhanh);
      setParamsFilter((prevValue) => {
        prevValue.ma_chi_nhanh_ql = listMaChiNhanh;
        return {...prevValue};
      });
    } catch (error) {
      console.log(error);
    }
  };

  /* RENDER */
  // const renderDropdownActionSheet = (title, placeholder, value, getTextByValue, onPressShowActionsheet, containerStyle, error, isRequired) => {
  //   return (
  //     <View style={[styles.mucDoTonThatView, containerStyle]}>
  //       <Text style={styles.txtMucDoTitle}>
  //         {title} {isRequired && <Text children="(*)" style={{color: colors.RED1}} />}
  //       </Text>
  //       <View style={[styles.dropdownView, error && {borderColor: colors.RED1}]}>
  //         <TouchableOpacity style={[styles.mucDoTitleView]} onPress={onPressShowActionsheet}>
  //           <Text children={value !== undefined ? getTextByValue(value) : placeholder} style={{flex: 1}} />
  //           <Icon.Entypo name="chevron-small-down" size={20} />
  //         </TouchableOpacity>
  //       </View>
  //       {error !== '' && <Text children={error} style={{color: colors.RED1, marginTop: spacing.tiny}} />}
  //     </View>
  //   );
  // };
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        setToggleDateTime(false);
        setDateTime(dateSelected);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );
  // const renderModalOptions = () => (
  //   <>
  //     {/* MODAL CHI NHÁNH */}
  //     <ModalOptions
  //       ref={refModalChiNhanh}
  //       title="Chi nhánh"
  //       showSearch
  //       onOptionSelected={(value) => {
  //         setChiNhanhSelected((prevValue) => {
  //           //nếu chọn tất cả
  //           if (value === '') prevValue = [''];
  //           else {
  //             //nếu không phải chọn tất cả và option cũ có tất cả -> bỏ cái tất cả đi
  //             if (prevValue.findIndex((maChiNhanh) => maChiNhanh === '') > -1) prevValue.splice(0, 1);
  //             //nếu là bỏ check
  //             if (prevValue.includes(value) && prevValue.length > 1)
  //               prevValue.splice(
  //                 prevValue.findIndex((item) => item === value),
  //                 1,
  //               );
  //             //nếu là check mới
  //             else prevValue.push(value);
  //           }
  //           refModalChiNhanh.current.show(prevValue); //update lại list checked
  //           return [...prevValue];
  //         });
  //       }}
  //       dataRoot={listChiNhanh}
  //       multiple
  //       containerStyle={{height: dimensions.height * 0.4}}
  //       dialogLoading={dialogLoading}
  //     />
  //     {/* MODAL GIÁM ĐỊNH VIÊN */}
  //     <ModalOptions
  //       ref={refModalGDV}
  //       title="Giám định viên"
  //       showSearch
  //       onOptionSelected={(value) => {
  //         setGDVSelected((prevValue) => {
  //           //nếu chọn tất cả
  //           if (value === '') prevValue = [''];
  //           else {
  //             let isTonTai = prevValue.findIndex((maGDV) => maGDV === '') > -1;
  //             //nếu không phải chọn tất cả và option cũ có tất cả -> bỏ cái tất cả đi
  //             if (isTonTai) prevValue.splice(0, 1);
  //             //nếu là bỏ check
  //             if (prevValue.includes(value) && prevValue.length > 1)
  //               prevValue.splice(
  //                 prevValue.findIndex((item) => item === value),
  //                 1,
  //               );
  //             //nếu là check mới
  //             else prevValue.push(value);
  //           }
  //           refModalGDV.current.show(prevValue); //update lại list checked
  //           console.log('prevValue', prevValue);
  //           return [...prevValue];
  //         });
  //       }}
  //       dataRoot={listGDV}
  //       multiple
  //       containerStyle={{height: dimensions.height * 0.4}}
  //       dialogLoading={dialogLoading}
  //     />

  //     {/* MODAL NĂM */}
  //     <ModalOptions
  //       ref={refModalNam}
  //       title="Năm"
  //       onOptionSelected={(value) => {
  //         setNamSelected(value);
  //         setParamsFilter((prevValue) => {
  //           prevValue.tu_ngay = moment(prevValue.tu_ngay).set('year', value).startOf('year');
  //           prevValue.den_ngay = moment(prevValue.den_ngay).set('year', value).endOf('year');
  //           return {...prevValue};
  //         });
  //       }}
  //       dataRoot={listNam}
  //       containerStyle={{height: dimensions.height * 0.4}}
  //     />

  //     {/* MODAL THÁNG */}
  //     <ModalOptions
  //       ref={refModalThang}
  //       title="Tháng"
  //       onOptionSelected={(value) => {
  //         setThangSelected(value);
  //         setParamsFilter((prevValue) => {
  //           prevValue.tu_ngay = moment(prevValue.tu_ngay)
  //             .set('year', namSelected)
  //             .set('month', value - 1)
  //             .startOf('month');
  //           prevValue.den_ngay = moment(prevValue.den_ngay)
  //             .set('year', namSelected)
  //             .set('month', value - 1)
  //             .endOf('month');
  //           return {...prevValue};
  //         });
  //       }}
  //       dataRoot={MONTHS}
  //       containerStyle={{height: dimensions.height * 0.7}}
  //     />
  //   </>
  // );
  return (
    <Modal
      onModalWillShow={initModalData}
      onSwipeComplete={closeModal}
      propagateSwipe={true}
      swipeDirection={['right']}
      isVisible={toggleModalFilter}
      animationIn="fadeInRight"
      animationOut="fadeOutRight"
      onBackdropPress={closeModal}
      onBackButtonPress={closeModal}
      style={styles.modal}>
      <SafeAreaView style={{flex: 1}}>
        <View style={styles.modalContent}>
          <View style={styles.headerView}>
            <TouchableOpacity onPress={closeModal}>
              <Icon.AntDesign name="arrowleft" size={25} />
            </TouchableOpacity>
            <Text children="Tìm kiếm" style={styles.txtHeader} />
            <TouchableOpacity onPress={resetFilter}>
              <Icon.AntDesign name="reload1" size={25} />
            </TouchableOpacity>
          </View>
          <View marginHorizontal={spacing.default}>
            {/* Đv xử lý */}
            <TextInputOutlined
              isTouchableOpacity
              editable={false}
              isDropdown
              title="Đơn vị xử lý"
              value={
                listMaDonViXuLySelected.length === 0
                  ? 'Chọn đơn vị xử lý'
                  : listMaDonViXuLySelected.length === 1
                  ? listMaDonViXuLySelected[0].ten_chi_nhanh
                  : `Có ${listMaDonViXuLySelected.length} đơn vị được chọn`
              }
              placeholder="Đơn vị xử lý"
              // onFocus={closeDropdown}
              onPress={() => refModalDonViXuLy.current.show()}
              inputStyle={{color: colors.BLACK}}
            />
            {/* Đv cấp đơn */}
            <TextInputOutlined
              isTouchableOpacity
              editable={false}
              isDropdown
              title="Đơn vị cấp đơn"
              value={
                listMaDonViCapDonSelected.length === 0
                  ? 'Chọn đơn vị cấp đơn'
                  : listMaDonViCapDonSelected.length === 1
                  ? listMaDonViCapDonSelected[0].ten_chi_nhanh
                  : `Có ${listMaDonViCapDonSelected.length} đơn vị được chọn`
              }
              placeholder="Đơn vị cấp đơn"
              // onFocus={closeDropdown}
              onPress={() => refModalDonViCapDon.current.show()}
              inputStyle={{color: colors.BLACK}}
            />
            {/* {renderDropdownActionSheet(
              'Chi nhánh',
              'Chọn chi nhánh',
              chiNhanhSelected,
              (valueSelected) => {
                let txtDisplay = [];
                listChiNhanh.forEach((itemChiNhanh) => {
                  if (valueSelected.includes(itemChiNhanh.ma)) txtDisplay.push(itemChiNhanh.ten_tat);
                });
                return txtDisplay.join(', ');
              },
              () => refModalChiNhanh.current.show(chiNhanhSelected),
              {marginTop: spacing.small, marginHorizontal: spacing.small},
              '',
              false,
            )} */}
            {/* GIÁM ĐỊNH VIÊN */}
            {/* {renderDropdownActionSheet(
              'Giám định viên',
              'Chọn giám định viên',
              gdvSelected,
              (valueSelected) => {
                let txtDisplay = [];
                listGDV.forEach((itemGdv) => {
                  if (valueSelected.includes(itemGdv.ma)) txtDisplay.push(itemGdv.ten);
                });
                return txtDisplay.join(', ');
              },
              () => refModalGDV.current.show(gdvSelected),
              {marginTop: spacing.small, marginHorizontal: spacing.small},
              '',
              false,
            )} */}
            {/* <View style={{flexDirection: 'row'}}>
              
              {renderDropdownActionSheet(
                'Năm',
                'Chọn năm',
                namSelected,
                (valueSelected) => listNam.find((item) => item.value === valueSelected)?.label || '',
                () => refModalNam.current.show(namSelected),
                {marginTop: spacing.small, marginHorizontal: spacing.small, flex: 1},
                '',
                false,
              )}
              
              {renderDropdownActionSheet(
                'Tháng',
                'Chọn tháng',
                thangSelected,
                (valueSelected) => MONTHS.find((item) => item.value === valueSelected)?.label || '',
                () => refModalThang.current.show(thangSelected),
                {marginTop: spacing.small, marginRight: spacing.small, flex: 1},
                '',
                false,
              )}
            </View> */}

            <View style={{flexDirection: 'row'}}>
              <TextInputOutlined
                title="Ngày đầu kỳ "
                isTouchableOpacity={true}
                onPress={() => setToggleTuNgay(true)}
                value={moment(paramsFilter.ngay_dau_ky).format('DD/MM/YYYY')}
                editable={false}
                isDateTimeField
                containerStyle={{flex: 1, marginRight: 10}}
              />
              {renderDateTimeComp(
                toggleTuNgay,
                setToggleTuNgay,
                (value) => {
                  if (!moment(value).isAfter(paramsFilter.ngay_cuoi_ky))
                    setParamsFilter((prevValue) => {
                      prevValue.ngay_dau_ky = value;
                      return {...prevValue};
                    });
                },
                moment(paramsFilter.ngay_dau_ky).toDate(),
                'date',
                null,
                new Date(),
              )}
              <TextInputOutlined
                title="Ngày cuối kỳ"
                isTouchableOpacity={true}
                onPress={() => setToggleDenNgay(true)}
                value={moment(paramsFilter.ngay_cuoi_ky).format('DD/MM/YYYY')}
                editable={false}
                isDateTimeField
                containerStyle={{flex: 1}}
              />
              {renderDateTimeComp(
                toggleDenNgay,
                setToggleDenNgay,
                (value) => {
                  if (!moment(value).isBefore(paramsFilter.ngay_dau_ky))
                    setParamsFilter((prevValue) => {
                      prevValue.ngay_cuoi_ky = value;
                      return {...prevValue};
                    });
                },
                moment(paramsFilter.ngay_cuoi_ky).toDate(),
                'date',
                null,
                moment().toDate(),
              )}
            </View>
            <ButtonLinear
              title="Tìm kiếm"
              linearStyle={{marginTop: spacing.default}}
              onPress={() => {
                closeModal();
                setTimeout(() => {
                  onPressSearch(paramsFilter);
                }, 300);
              }}
            />
          </View>
        </View>
      </SafeAreaView>
      {/* {renderModalOptions()} */}
      <ModalChiNhanhTheoDangCay ref={refModalDonViXuLy} showCheckCha={true} multiple setListMaDonViXuLySelected={(value) => onSelectDonViXuLy(value)} />
      <ModalChiNhanhTheoDangCay ref={refModalDonViCapDon} showCheckCha={true} multiple setListMaDonViXuLySelected={(value) => onSelectDonViCapDon(value)} />
    </Modal>
  );
};
const styles = StyleSheet.create({
  modal: {
    alignItems: 'flex-end',
    margin: 0,
  },
  modalContent: {
    height: dimensions.height,
    width: dimensions.width * 0.8,
    flex: 1,
    backgroundColor: '#FFF',
    paddingTop: 10,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 0.5,
    paddingBottom: 10,
    paddingHorizontal: 10,
    borderColor: 'gray',
  },
  txtHeader: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  mucDoTonThatView: {
    // marginHorizontal: spacing.small,
    // flex: 1,
  },
  txtMucDoTitle: {
    fontWeight: '700',
    marginBottom: spacing.tiny,
  },
  dropdownView: {
    borderWidth: 1,
    paddingHorizontal: spacing.small,
    borderColor: colors.GRAY,
    borderRadius: 10,
  },
  mucDoTitleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
});

export const ModalFilter = memo(ModalFilterComponent, isEqual);
