import {StyleSheet} from 'react-native';
import {colors} from '../../../commons/Theme';

export default StyleSheet.create({
  container: {flex: 1},
  contentView: {
    marginTop: 10,
    marginBottom: 50,
  },
  profileItemView: {
    flexDirection: 'row',
    marginVertical: 5,
    paddingLeft: 20,
    paddingRight: 10,
    borderRadius: 35,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
    marginHorizontal: 10,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: 5,
    paddingRight: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileTxtHoSo: {
    marginBottom: 4,
    color: colors.BLACK,
    fontSize: 14,
  },
  profileTxtThoiGian: {
    fontSize: 12,
    color: colors.GRAY5,
  },
  profileImgClock: {
    marginRight: 5,
    width: 14,
    height: 14,
    opacity: 0.8,
  },
  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNoData: {
    width: width / 3,
    height: width / 3,
  },
});
