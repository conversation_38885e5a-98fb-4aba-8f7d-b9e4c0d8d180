import {colors} from '@app/commons/Theme.js';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {requestRecordAudioPermissions} from '@app/utils/PermisstionProvider.js';
import {BottomTabs, Icon, ScreenComponent, SearchBar, Text} from '@component';
import R from '@R';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, Linking, RefreshControl, ScrollView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {connect} from 'react-redux';
import styles from './PhoneBookStyle.js';

const PhoneBookScreenComponent = () => {
  console.log('PhoneBookScreen');
  const [contacts, setContacts] = useState([]);
  const [searchInput, setSearchInput] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    getContactData(null);
    // requestRecordAudioPermissions();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    getContactData();
  };

  const getContactData = async (searchInput) => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CONTACTS, {ten: searchInput});
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setContacts(response.data_info.data);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  /**RENDER  */
  const renderContactItem = (data) => {
    let item = data.item;
    return (
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <TouchableOpacity style={styles.profileItemCenterView} onPress={() => item.dthoai && Linking.openURL(`tel:${item.dthoai}`)}>
          <View style={{flex: 1}}>
            <Text style={styles.profileTxtHoSo}>{item.ten}</Text>
            <View style={styles.profileItemDetail}>
              <Text style={styles.profileTxtThoiGian}>{item.chi_nhanh_ten}</Text>
            </View>
            <View style={styles.profileItemDetail}>
              <View>
                <Text style={styles.profileTxtThoiGian}>{item.dthoai}</Text>
              </View>
              <Text style={styles.profileTxtThoiGian}>{item.email}</Text>
            </View>
          </View>
          <View>
            <Icon.Feather name="phone-call" size={20} />
          </View>
        </TouchableOpacity>
      </LinearGradient>
    );
  };
  // RENDER HỒ SƠ CHƯA TIẾP NHẬN
  const renderContact = () => {
    return (
      <View>
        {contacts.length > 0 ? (
          <FlatList data={contacts} renderItem={renderContactItem} keyExtractor={(item) => item.so_id} />
        ) : (
          <View style={styles.noDataView}>
            <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
            <Text>Chưa có dữ liệu</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <ScreenComponent
      header
      screenTitle="Danh bạ"
      hightNavbar
      renderView={
        <View style={styles.container}>
          <View style={{marginTop: -40}}>
            <SearchBar
              value={searchInput}
              onPressSearch={() => getContactData(searchInput)}
              onTextChange={(value) => {
                setSearchInput(value);
                if (!value.trim()) getContactData();
              }}
              onSubmitEditing={() => getContactData(searchInput)}
            />
          </View>
          <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} showsVerticalScrollIndicator={false}>
            <View style={styles.contentView}>{renderContact()}</View>
          </ScrollView>
          <BottomTabs />
        </View>
      }
    />
  );
};

export const PhoneBookScreen = memo(PhoneBookScreenComponent, isEqual);
