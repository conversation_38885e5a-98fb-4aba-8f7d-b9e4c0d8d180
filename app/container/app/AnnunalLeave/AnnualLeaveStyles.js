import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  contentView: {
    marginTop: 10,
    flex: 1,
  },
  profileItemView: {
    flexDirection: 'row',
    marginVertical: 5,
    paddingLeft: 20,
    paddingRight: 10,
    borderRadius: 35,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: 5,
    paddingRight: 5,
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileTxtHoSo: {
    marginBottom: 4,
    color: colors.BLACK,
    fontSize: 14,
  },
  profileTxtThoiGian: {
    fontSize: 12,
    color: colors.GRAY5,
  },
  profileImgClock: {
    marginRight: 5,
    width: 14,
    height: 14,
    opacity: 0.8,
  },
  bottomView: {
    position: 'absolute',
    bottom: 70,
    right: 0,
    left: 0,
    flex: 1,
    marginHorizontal: 10,
    // borderWidth: 1,
  },
  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
});
