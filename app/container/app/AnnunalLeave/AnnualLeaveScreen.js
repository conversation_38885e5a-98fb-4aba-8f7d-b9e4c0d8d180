import {colors} from '@app/commons/Theme.js';
import NavigationUtil from '@app/navigation/NavigationUtil.js';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {BottomTabs, ButtonLinear, Icon, ScreenComponent, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import R from '@R';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, RefreshControl, ScrollView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styles from './AnnualLeaveStyles';

export const AnnualLeaveComponent = (props) => {
  console.log('AnnualLeaveScreen');
  const navigation = props.navigation;
  const [listNghiPhep, setListNghiPhep] = useState([]);
  const [searchInput, setSearchInput] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  const [filterApproval, setFilterApproval] = useState(null);
  const ctrlDropdownApproval = useRef(null);
  const dropdownApprovalItems = [
    {
      label: 'Tất cả',
      value: 'usa',
      icon: () => <Icon.Ionicons name="options-outline" size={18} color={colors.PRIMARY_08} />,
    },
    {
      label: 'Duyệt tiến hình hồ sơ',
      value: 'usa1',
      icon: () => <Icon.Ionicons name="options-outline" size={18} color={colors.PRIMARY_08} />,
    },
    {
      label: 'Duyệt tạm ứng, thanh lý, thu đòi',
      value: 'uk',
      icon: () => <Icon.Ionicons name="options-outline" size={18} color={colors.PRIMARY_08} />,
    },
  ];

  useEffect(() => {
    navigation.addListener('focus', () => {
      getListNghiPhep();
    });
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    getListNghiPhep();
  };

  const getListNghiPhep = async () => {
    try {
      let params = {
        trang: 1, //giám định
        so_dong: 1000, //chưa duyệt
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.DANH_SACH_NGHI_PHEP, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListNghiPhep([...response.data_info.data]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressTaoNghiPhep = () => {
    NavigationUtil.push(SCREEN_ROUTER_APP.ANNUAL_LEAVE_DETAIL);
  };
  /**RENDER  */
  const renderItemNghiPhep = (data) => {
    let item = data.item;
    return (
      <TouchableOpacity
        onPress={() =>
          NavigationUtil.push(SCREEN_ROUTER_APP.ANNUAL_LEAVE_DETAIL, {
            chiTietLichNghi: item,
          })
        }>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            <Text style={styles.profileTxtHoSo}>Ngày bắt đầu: {item.ngay_bd}</Text>
            <Text style={styles.profileTxtHoSo}>Ngày kết thúc: {item.ngay_kt}</Text>
            <Text style={styles.profileTxtHoSo}>Trạng thái: {item.trang_thai_ten}</Text>
          </View>
          <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={!props.noRightIcon ? colors.BLUE1 : colors.WHITE} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };
  const renderBottom = () => (
    <View style={styles.bottomView}>
      <ButtonLinear title="Tạo đề nghị nghỉ phép" onPress={onPressTaoNghiPhep} />
    </View>
  );
  // RENDER HỒ SƠ CHƯA TIẾP NHẬN
  const renderDanhSachNghiPhep = () => {
    return (
      <View>
        <FlatList
          data={listNghiPhep}
          renderItem={renderItemNghiPhep}
          keyExtractor={(item) => item.so_id}
          ListEmptyComponent={
            <View style={styles.noDataView}>
              <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
              <Text>Chưa có dữ liệu</Text>
            </View>
          }
        />
      </View>
    );
  };
  return (
    <ScreenComponent
      header
      screenTitle="Lịch nghỉ phép"
      // hightNavbar
      renderView={
        <View style={styles.container}>
          <View style={styles.contentView}>
            <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} showsVerticalScrollIndicator={false}>
              <View style={styles.contentView}>{renderDanhSachNghiPhep()}</View>
            </ScrollView>
          </View>
          {renderBottom()}
          <BottomTabs />
        </View>
      }
    />
  );
};

export const AnnualLeaveScreen = memo(AnnualLeaveComponent, isEqual);
