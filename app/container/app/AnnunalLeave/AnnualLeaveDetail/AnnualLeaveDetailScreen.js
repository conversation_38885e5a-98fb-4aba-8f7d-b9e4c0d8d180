import NavigationUtil from '@app/navigation/NavigationUtil.js';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, TextInputOutlined} from '@component';
import {DATA_CONSTANT} from '@constant';
import moment from 'moment';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import styles from './AnnualLeaveDetailStyles';

const AnnualLeaveDetailComponent = (props) => {
  console.log('AnnualLeaveDetailComponent');
  let route = props.route;
  const [chiTietLichNghi, setChiTietNghiPhep] = useState(route.params?.chiTietLichNghi || null);
  const [inputErr, setInputErr] = useState(['', '', '', '', '']);
  //NGÀY BẮT ĐẦU
  const [ngayBatDau, setNgayBatDau] = useState(chiTietLichNghi ? new Date(moment(chiTietLichNghi.ngay_bd, 'DD/MM/YYYY')) : new Date());
  const [toggleNgayBatDau, setToggleNgayBatDau] = useState(false);
  //GIỜ BẮT ĐẦU
  const [gioBatDau, setGioBatDau] = useState(new Date());
  const [toggleGioBatDau, setToggleGioBatDau] = useState(false);
  //NGÀY KẾT THÚC
  const [ngayKetThuc, setNgayKetThuc] = useState(chiTietLichNghi ? new Date(moment(chiTietLichNghi.ngay_kt, 'DD/MM/YYYY')) : new Date());
  const [toggleNgayKetThuc, setToggleNgayKetThuc] = useState(false);
  //GIỜ KẾT THÚC
  const [gioKetThuc, setGioKetThuc] = useState(new Date());
  const [toggleGioKetThuc, setToggleGioKetThuc] = useState(false);

  const [inputNoiDung, setInputNoiDung] = useState(chiTietLichNghi?.noi_dung || '');
  const daDuyetLichNghiPhep = chiTietLichNghi?.trang_thai == DATA_CONSTANT.LICH_NGHI_PHEP_STATUS.DA_DUYET ? true : false;

  useEffect(() => {
    // console.log('chiTietLichNghi', chiTietLichNghi);
    if (chiTietLichNghi) {
      setGioBatDau(new Date(moment(route.params.chiTietLichNghi.gio_bd, 'HH:mm')));
      setGioKetThuc(new Date(moment(route.params.chiTietLichNghi.gio_kt, 'HH:mm')));
    }
  }, []);

  const onPressDateConfirm = (date, setToggleDateTime, setDate, type) => {
    // console.log('onPressDateConfirm', type, date, ngayKetThuc, moment(ngayBatDau).isSame(ngayKetThuc));
    setToggleDateTime(false);
    let inputErrTmp = inputErr;
    //nếu là ngày bắt đầu
    if (type == 1) {
      inputErrTmp[1] = '';
      if (
        moment(date).isAfter(moment(ngayKetThuc)) && //isAfter : sau
        !moment(date).isSame(moment(ngayKetThuc)) // 2 ngày không được giống nhau
      ) {
        inputErrTmp[1] = 'Ngày bắt đầu phải trước ngày kết thúc';
        inputErrTmp[0] = '';
        inputErrTmp[2] = '';
      } else {
        inputErrTmp[0] = '';
        inputErrTmp[2] = '';
        inputErrTmp[3] = '';
      }
      //nếu ngày bắt đầu = ngày kết thúc -> check lại thời gian
      if (moment(date).isSame(moment(ngayKetThuc))) {
        if (moment(gioBatDau).isAfter(gioKetThuc) || moment(gioBatDau).isSame(gioKetThuc)) {
          inputErrTmp[0] = 'Giờ bắt đầu phải trước giờ kết thúc';
          inputErrTmp[2] = 'Giờ kết thúc phải sau giờ bắt đầu';
        }
      }
    }
    //nếu là ngày kết thúc
    else if (type == 3) {
      inputErrTmp[3] = '';
      if (
        moment(date).isBefore(moment(ngayBatDau)) && //isBefore : trước
        !moment(date).isSame(moment(ngayBatDau)) // 2 ngày không được giống nhau
      ) {
        inputErrTmp[3] = 'Ngày kết thúc phải sau ngày bắt đầu';
        inputErrTmp[0] = '';
        inputErrTmp[2] = '';
      } else {
        inputErrTmp[0] = '';
        inputErrTmp[1] = '';
        inputErrTmp[2] = '';
      }
      //nếu ngày bắt đầu = ngày kết thúc -> check lại thời gian
      if (moment(date).isSame(moment(ngayBatDau))) {
        if (moment(gioBatDau).isAfter(gioKetThuc) || moment(gioBatDau).isSame(gioKetThuc)) {
          inputErrTmp[0] = 'Giờ bắt đầu phải trước giờ kết thúc';
          inputErrTmp[2] = 'Giờ kết thúc phải sau giờ bắt đầu';
        }
      }
    }
    //nếu là giờ bắt đầu + (Ngày bắt đầu = ngày kết thúc)
    else if (type == 0 && moment(ngayBatDau).isSame(moment(ngayKetThuc))) {
      // console.log('type==0', date, gioKetThuc);
      inputErrTmp[0] = '';
      if (moment(date).isAfter(gioKetThuc) || moment(date).isSame(gioKetThuc)) {
        inputErrTmp[0] = 'Giờ bắt đầu phải trước giờ kết thúc';
      } else {
        inputErrTmp[2] = '';
      }
    }
    //nếu là giờ kết thúc + (Ngày bắt đầu = ngày kết thúc)
    else if (type == 2 && moment(ngayBatDau).isSame(moment(ngayKetThuc))) {
      // console.log('type==2', date, gioBatDau);
      inputErrTmp[2] = '';
      if (moment(date).isBefore(gioBatDau) || moment(date).isSame(gioBatDau)) {
        inputErrTmp[2] = 'Giờ kết thúc phải sau giờ bắt đầu';
      } else {
        inputErrTmp[0] = '';
      }
    }
    // console.log('inputErrTmp', inputErrTmp);
    setInputErr([...inputErrTmp]);
    setDate(date);
  };

  const onChangeInputNoiDung = (value) => {
    let inputErrTmp = inputErr;
    inputErrTmp[4] = '';
    if (!value.trim()) {
      inputErrTmp[4] = 'Vui lòng nhập Nội dung';
    }
    setInputNoiDung(value);
    setInputErr([...inputErrTmp]);
  };

  const onPressCreate = async () => {
    let inputErrTmp = inputErr;
    let haveErr = false;
    if (!inputNoiDung.trim()) {
      inputErrTmp[4] = 'Vui lòng nhập Nội dung';

      haveErr = true;
    }
    //nếu cùng ngày - cùng giờ
    if (moment(gioBatDau).isSame(gioKetThuc) && moment(ngayBatDau).isSame(ngayKetThuc)) {
      inputErrTmp[0] = 'Giờ bắt đầu phải trước giờ kết thúc';
      inputErrTmp[2] = 'Giờ kết thúc phải sau giờ bắt đầu';
      haveErr = true;
    }
    if (haveErr) {
      setInputErr([...inputErrTmp]);
      return;
    }

    let params = {
      so_id: chiTietLichNghi ? chiTietLichNghi.so_id : '',
      gio_phut_bd: moment(gioBatDau).format('HH:mm'),
      ngay_bd: moment(ngayBatDau).format('YYYYMMDD'),
      gio_phut_kt: moment(gioKetThuc).format('HH:mm'),
      ngay_kt: moment(ngayKetThuc).format('YYYYMMDD'),
      noi_dung: inputNoiDung,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.TAO_LICH_NGHI_PHEP, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', (chiTietLichNghi ? 'Cập nhật' : 'Đề nghị') + ' lịch nghỉ phép thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const onPressDelete = () => {
    let params = {
      so_id: chiTietLichNghi.so_id,
    };
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá lịch nghỉ phép này?', [
      {text: 'Từ chối'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_LICH_NGHI_PHEP, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá lịch nghỉ phép thành công', 'success');
            NavigationUtil.pop();
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };
  /**RENDER  */
  const renderBottom = () => {
    if (chiTietLichNghi?.trang_thai == DATA_CONSTANT.LICH_NGHI_PHEP_STATUS.DA_DUYET) return;
    return (
      <View style={styles.bottomView}>
        {!chiTietLichNghi && <ButtonLinear title="Tạo mới" onPress={onPressCreate} />}
        {chiTietLichNghi && (
          <View style={{flexDirection: 'row'}}>
            <ButtonLinear title="Xoá" onPress={onPressDelete} linearStyle={{marginRight: 10}} />
            <ButtonLinear title="Cập nhật" onPress={onPressCreate} />
          </View>
        )}
      </View>
    );
  };

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
      //   is24Hour={true}
    />
  );
  const renderForm = () => {
    return (
      <>
        {/*NGÀY BẮT ĐẦU - GIỜ BẮT ĐÀU */}
        <View style={{flexDirection: 'row'}}>
          <>
            <TextInputOutlined
              title="Giờ bắt đầu"
              isTouchableOpacity={true}
              onPress={() => {
                // console.log('press');
                if (daDuyetLichNghiPhep) return;
                setToggleGioBatDau(true);
              }}
              value={moment(gioBatDau).format('HH:mm')}
              editable={false}
              error={inputErr[0]}
              containerStyle={{flex: 1, marginRight: 10}}
              isRequired={true}
              isDateTimeField
            />
            {renderDateTimeComp(toggleGioBatDau, setToggleGioBatDau, setGioBatDau, gioBatDau, 'time', null, null, 0)}
          </>
          <>
            <TextInputOutlined
              title="Ngày bắt đầu"
              isTouchableOpacity={true}
              onPress={() => {
                if (daDuyetLichNghiPhep) return;
                setToggleNgayBatDau(true);
              }}
              value={moment(ngayBatDau).format('DD/MM/YYYY')}
              editable={false}
              error={inputErr[1]}
              containerStyle={{flex: 1}}
              isRequired={true}
              isDateTimeField
            />
            {renderDateTimeComp(toggleNgayBatDau, setToggleNgayBatDau, setNgayBatDau, ngayBatDau, 'date', new Date(), null, 1)}
          </>
        </View>

        {/*NGÀY KẾT THÚC - GIỜ KẾT THÚC */}
        <View style={{flexDirection: 'row'}}>
          <>
            <TextInputOutlined
              title="Giờ kết thúc"
              isTouchableOpacity={true}
              onPress={() => {
                if (daDuyetLichNghiPhep) return;
                setToggleGioKetThuc(true);
              }}
              value={moment(gioKetThuc).format('HH:mm')}
              editable={false}
              error={inputErr[2]}
              containerStyle={{flex: 1, marginRight: 10}}
              isRequired={true}
              isDateTimeField
            />
            {renderDateTimeComp(toggleGioKetThuc, setToggleGioKetThuc, setGioKetThuc, gioKetThuc, 'time', null, null, 2)}
          </>
          <>
            <TextInputOutlined
              title="Ngày kết thúc"
              isTouchableOpacity={true}
              onPress={() => {
                if (daDuyetLichNghiPhep) return;
                setToggleNgayKetThuc(true);
              }}
              value={moment(ngayKetThuc).format('DD/MM/YYYY')}
              editable={false}
              error={inputErr[3]}
              containerStyle={{flex: 1}}
              isRequired={true}
              isDateTimeField
            />
            {renderDateTimeComp(toggleNgayKetThuc, setToggleNgayKetThuc, setNgayKetThuc, ngayKetThuc, 'date', new Date(), null, 3)}
          </>
        </View>
        <TextInputOutlined
          title="Nội dung"
          value={inputNoiDung}
          numberOfLines={3}
          multiline={true}
          error={inputErr[4]}
          onChangeText={(value) => onChangeInputNoiDung(value)}
          placeholder="Nội dung"
          isRequired={true}
        />
      </>
    );
  };
  return (
    <ScreenComponent
      headerTitle={(chiTietLichNghi ? 'Thông tin' : 'Đề nghị') + ' lịch nghỉ phép'}
      headerBack
      renderView={
        <View style={styles.container}>
          <View style={styles.contentView}>
            <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} resetScrollToCoords={{x: 0, y: 0}} scrollEnabled={true}>
              {renderForm()}
            </KeyboardAwareScrollView>
            {renderBottom()}
          </View>
        </View>
      }
    />
  );
};

export const AnnualLeaveDetailScreen = memo(AnnualLeaveDetailComponent, isEqual);
