import {StyleSheet} from 'react-native';
import {colors} from '../../../../commons/Theme';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  contentView: {
    marginTop: 10,
    flex: 1,
    marginHorizontal: 10,
  },
  bottomView: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    flex: 1,
    marginBottom: 10,
    // borderWidth: 1,
  },
  inputView: {
    marginVertical: 10,
    // marginRight: 10
    // flex: 1,
    // borderWidth: 1
  },
  txtHeaderInput: {
    marginBottom: 8,
    fontWeight: 'bold',
    // color: colors.BLACK
  },
  input: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 5,
    paddingLeft: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
});
