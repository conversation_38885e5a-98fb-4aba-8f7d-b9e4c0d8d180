import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectSuKienBaoHiem} from '@app/redux/slices/CategoryCommonSlice';
import {selectCities} from '@app/redux/slices/CitiesSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {logErrorTryCatch} from '@app/utils';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {geoCodeChuyenToaDoThanhDiaChi, geoFormatLaiDiaChi, requestCurrentLocation} from '@app/utils/LocationProvider';
import {ButtonLinear, DropdownPicker, Icon, ScreenComponent, TextInputOutlined} from '@component';
import {NGAY_CHUYEN_DOI, REGUlAR_EXPRESSION, SCREEN_ROUTER_APP, isRequiredFieldXaPhuong} from '@constant';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {ActivityIndicator, Alert, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
import {ModalChonHangGPLX, ModalChonNhomNguyenNhan, ModalChonNhomSuKienBaoHiem} from './Component';
import {hangGPLX, titleDropdownInput} from './Constant';
import styles from './KhaiBaoVuTonThatStyles';
import {onChangeAlias} from '@app/utils/string';

const KhaiBaoVuTonThatScreenComponent = (props) => {
  console.log('KhaiBaoVuTonThatScreenComponent');
  const {route} = props;
  const citiesData = useSelector(selectCities);
  const danhSachSuKienBaoHiem = useSelector(selectSuKienBaoHiem);
  const {profileData, chiTietVuTonThat, type} = route?.params;
  const hoSoSauNgayChuyenDoi = profileData?.ho_so?.ngay_mo_hs >= NGAY_CHUYEN_DOI && profileData?.ho_so?.ngay_mo_hs >= profileData?.ho_so?.ngay_upd_dvi_hanh_chinh;

  let diaDiemRef = useRef();
  let emailRef = useRef();
  let soGPLXRef = useRef();
  let hauQuaRef = useRef();
  let hoTenLXRef = useRef();
  let phoneRef = useRef();
  let refModal = useRef();
  let soDangKiemRef = useRef();
  let nguyenNhanChiTietRef = useRef();
  let refModalChonNhomNguyenNhan = useRef();
  let refModalChonNhomSuKienBaoHiem = useRef();

  const [openCity, setOpenCity] = useState(false);
  const [districtsDataDropDown, setDistrictsDataDropDown] = useState([]);
  const [openDistrict, setOpenDistrict] = useState(false);
  const [wardsDataDropDown, setWardsDataDropDown] = useState([]);
  const [listHangGPLX, setListHangGPLX] = useState([]);
  const [listSuKienBaoHiem, setListSuKienBaoHiem] = useState([]);
  const [openWard, setOpenWard] = useState(false);
  const [toggleGioXR, setToggleGioXR] = useState(false);
  const [toggleNgayXR, setToggleNgayXR] = useState(false);
  const [toggleNgayCapDangKiem, setToggleNgayCapDangKiem] = useState(false);
  const [toggleNgayHetHanGPLX, setToggleNgayHetHanGPLX] = useState(false);
  const [toggleNgayHetHanDangKiem, setToggleNgayHetHanDangKiem] = useState(false);
  const [toggleNgayCapGPLX, setToggleNgayCapGPLX] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [danhMucSanPhamXe, setDanhMucSanPhamXe] = useState([]);

  const [disableBtnLayDiaChi, setDisableBtnLayDiaChi] = useState(false);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    setError,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: {
      gioXayRa: chiTietVuTonThat && chiTietVuTonThat?.gio_xr ? moment(chiTietVuTonThat?.gio_xr, 'HH:mm').toDate() : '',
      ngayXayRa: chiTietVuTonThat && chiTietVuTonThat?.ngay_xr ? moment(chiTietVuTonThat?.ngay_xr, 'DD/MM/YYYY').toDate() : '',
      tinhThanh: chiTietVuTonThat ? chiTietVuTonThat?.tinh_thanh : '',
      quanHuyen: chiTietVuTonThat ? chiTietVuTonThat?.quan_huyen : '',
      xaPhuong: chiTietVuTonThat ? chiTietVuTonThat?.phuong_xa : '',
      diaDiemXayRa: chiTietVuTonThat ? chiTietVuTonThat?.dia_diem : '',
      nhomNguyenNhan: chiTietVuTonThat ? chiTietVuTonThat?.nhom_nguyen_nhan : '',
      nguyenNhanChiTiet: chiTietVuTonThat ? chiTietVuTonThat?.nguyen_nhan : '',
      hauQua: chiTietVuTonThat ? chiTietVuTonThat?.hau_qua : '',
      hauQuaNguoiThuBa: chiTietVuTonThat ? chiTietVuTonThat?.hau_qua_ntba : '',
      nhomSuKien: chiTietVuTonThat ? chiTietVuTonThat?.nhom_su_kien : '',
      tenLaiXe: chiTietVuTonThat ? chiTietVuTonThat?.ten_lxe : '',
      dienThoaiLaiXe: chiTietVuTonThat ? chiTietVuTonThat?.dthoai_lxe : '',
      emailLaiXe: chiTietVuTonThat ? chiTietVuTonThat?.email_lxe : '',
      soGPLX: chiTietVuTonThat ? chiTietVuTonThat?.gplx_so : '',
      soDangKiem: chiTietVuTonThat ? chiTietVuTonThat?.dangkiem_so : '',
      hangGPLX: chiTietVuTonThat ? chiTietVuTonThat?.gplx_hang : '',
      ngayCapGPLX: chiTietVuTonThat && chiTietVuTonThat?.gplx_hieu_luc ? moment(chiTietVuTonThat?.gplx_hieu_luc, 'DD/MM/YYYY').toDate() : '',
      ngayHetHanGPLX: chiTietVuTonThat && chiTietVuTonThat?.gplx_het_han ? moment(chiTietVuTonThat?.gplx_het_han, 'DD/MM/YYYY').toDate() : '',
      ngayCapDangKiem: chiTietVuTonThat && chiTietVuTonThat?.dangkiem_hieu_luc ? moment(chiTietVuTonThat?.dangkiem_hieu_luc, 'DD/MM/YYYY').toDate() : '',
      ngayHetHanDangKiem: chiTietVuTonThat && chiTietVuTonThat?.dangkiem_het_han ? moment(chiTietVuTonThat?.dangkiem_het_han, 'DD/MM/YYYY').toDate() : '',
    },
    mode: 'onChange',
  });

  const nhomNguyenNhan = watch('nhomNguyenNhan');
  const nhomSuKien = watch('nhomSuKien');
  const tinhThanh = watch('tinhThanh');
  const quanHuyen = watch('quanHuyen');

  useEffect(() => {
    if (listHangGPLX.length > 0) {
      const listTenHang = listHangGPLX.map((item) => {
        return item.ten;
      });
      let tenBenh = JSON.stringify(listTenHang);
      setValue('hangGPLX', tenBenh.replace(/[\[\]'"]+/g, ''));
    } else if (type !== 'EDIT') {
      setValue('hangGPLX', '');
    }
  }, [listHangGPLX]);

  // useEffect(() => {
  //   if (listSuKienBaoHiem.length > 0) {
  //     const maSuKien = listSuKienBaoHiem.map((item) => {
  //       return item.bt;
  //     });
  //     let ma = JSON.stringify(maSuKien);
  //     setValue('nhomSuKien', ma.replace(/[\[\]'"]+/g, ''));
  //   } else if (type !== 'EDIT') {
  //     setValue('nhomSuKien', '');
  //   }
  // }, [listSuKienBaoHiem]);

  const onSetValueSuKienBaoHiem = (arr) => {
    if (arr.length > 0) {
      let filterMaSuKien = arr.filter((e) => e.isChecked);
      const maSuKien = filterMaSuKien.map((item) => {
        return item.bt;
      });
      let ma = JSON.stringify(maSuKien);
      setValue('nhomSuKien', ma.replace(/[\[\]'"]+/g, ''));
    } else {
      setValue('nhomSuKien', '');
    }
    setListSuKienBaoHiem(arr);
  };

  useEffect(() => {
    let newArrData = JSON.parse(JSON.stringify(danhSachSuKienBaoHiem));
    newArrData.map((e, index) => {
      newArrData[index].isChecked = false;
      if (nhomSuKien !== null && nhomSuKien !== '') {
        let arrHm = nhomSuKien.split(',');
        arrHm.map((x) => {
          if (x == e.bt) {
            newArrData[index].isChecked = true;
          }
        });
      }
    });
    setListSuKienBaoHiem([...newArrData]);
    if (chiTietVuTonThat) {
      let tinhThanhGiamDinh = citiesData.find((item) => item.ma === chiTietVuTonThat.tinh_thanh);
      if (tinhThanhGiamDinh) setValue('tinhThanh', tinhThanhGiamDinh.ma);
      //nếu là chọn vụ tổn thất
      for (let i = 0; i < citiesData.length; i++) {
        if (citiesData[i].ma === chiTietVuTonThat.tinh_thanh) {
          let tmpCity = cloneObject(citiesData[i]);
          setValue('tinhThanh', tmpCity.ma);
          //duyệt mảng district trong city
          for (let j = 0; j < tmpCity.district.length; j++) {
            let district = tmpCity.district[j];
            //duyệt mảng ward trong district
            for (let k = 0; k < district.ward.length; k++) {
              let ward = district.ward[k];
              if (ward.ma === chiTietVuTonThat.phuong_xa) {
                setValue('xaPhuong', ward.ma);
              }
            }
            if (district.ma === chiTietVuTonThat.quan_huyen) {
              setValue('quanHuyen', district.ma);
              setDistrictsDataDropDown(tmpCity.district);
              setWardsDataDropDown(district.ward);
            }
            tmpCity.district[j] = district;
          }

          break;
        }
      }
    }
    // setCitiesDataDropDown(cityDropdownTmp);
  }, []);

  useEffect(() => {
    getDanhMucSPXe();
    if (profileData?.ho_so?.moi_qh_lhe === 'QH.0002') {
      setValue('tenLaiXe', profileData?.ho_so?.nguoi_lhe);
      setValue('emailLaiXe', profileData?.ho_so?.email_lhe);
      setValue('dienThoaiLaiXe', profileData?.ho_so?.dthoai_tb);
    } else if (profileData?.ho_so?.moi_qh_tb === 'QH.0002') {
      setValue('tenLaiXe', profileData?.ho_so?.nguoi_tb);
      setValue('emailLaiXe', profileData?.ho_so?.email_tb);
      setValue('dienThoaiLaiXe', profileData?.ho_so?.dthoai_tb);
    }
  }, []);

  const onInputFocus = () => {
    setOpenCity(false);
    setOpenDistrict(false);
    setOpenWard(false);
  };

  const onPressLuu = async (data) => {
    const formatDate = (value) => {
      return +moment(value).format('YYYYMMDD');
    };
    try {
      let soGPLX = onChangeAlias(data.soGPLX, {xoaKyTuDacBiet: false}).toUpperCase();
      let soDangKiem = onChangeAlias(data.soDangKiem, {xoaKyTuDacBiet: false}).toUpperCase();
      const params = {
        so_id: profileData?.ho_so?.so_id, //id hồ sơ
        ngay_xr: formatDate(data.ngayXayRa) || '',
        gio_xr: moment(data.gioXayRa).format('HH:mm'),
        tinh_thanh: data.tinhThanh,
        quan_huyen: data.quanHuyen,
        phuong_xa: data.xaPhuong,
        dia_diem: data.diaDiemXayRa,
        vu_tt: chiTietVuTonThat?.vu_tt || '',
        nhom_nguyen_nhan: data.nhomNguyenNhan,
        nguyen_nhan: data.nguyenNhanChiTiet,
        hau_qua: data.hauQua,
        hau_qua_ntba: data.hauQuaNguoiThuBa,
        ten_lxe: data.tenLaiXe,
        dthoai_lxe: data.dienThoaiLaiXe,
        email_lxe: data.emailLaiXe,
        gplx_so: soGPLX,
        gplx_hang: data.hangGPLX || '',
        gplx_hieu_luc: formatDate(data.ngayCapGPLX) || '',
        gplx_het_han: formatDate(data.ngayHetHanGPLX) || '',
        dangky_so: '', //rỗng
        dangky_hieu_luc: '', //rỗng
        dangky_het_han: '', //rỗng
        dangkiem_so: soDangKiem,
        dangkiem_hieu_luc: formatDate(data.ngayCapDangKiem) || '',
        dangkiem_het_han: formatDate(data.ngayHetHanDangKiem) || '',
        nhom_su_kien: data.nhomSuKien,
      };
      setValue('soGPLX', soGPLX);
      setValue('soDangKiem', soDangKiem);
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.KHAI_BAO_VU_TON_THAT, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật vụ tổn thất thành công!', 'success');
      NavigationUtil.navigate(SCREEN_ROUTER_APP.CHI_TIET_HO_SO_BOI_THUONG_XE, {prevScreen: SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT});
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressXoaVuTonThat = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn xoá thông tin vụ tổn thất', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {text: 'Đồng ý', onPress: () => handleXoaVuTonThat()},
    ]);
  };

  const handleXoaVuTonThat = async () => {
    try {
      const params = {
        so_id: chiTietVuTonThat?.so_id, //id hồ sơ
        vu_tt: chiTietVuTonThat?.vu_tt,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_VU_TON_THAT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá thông tin vụ tổn thất thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const getDanhMucSPXe = async () => {
    const params = {ma_doi_tac: profileData?.ho_so?.ma_doi_tac};
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GET_DANH_MUC_SAN_PHAM_XE, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info;
      data.map((item, index) => {
        data[index].label = item.ten;
        data[index].value = item.ma;
      });
      setDanhMucSanPhamXe(data);
      const filter = data.filter((item) => {
        return item.ma === chiTietVuTonThat?.nhom_nguyen_nhan;
      });
      setValue('nhomNguyenNhan', filter[0]?.ma);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const closeDropdown = (title) => {
    title !== titleDropdownInput[0] && openCity && setOpenCity(false);
    title !== titleDropdownInput[1] && openDistrict && setOpenDistrict(false);
    title !== titleDropdownInput[2] && openWard && setOpenWard(false);
    if (title === titleDropdownInput[1] && !getValues('tinhThanh')) setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    if (title === titleDropdownInput[2]) {
      !getValues('quanHuyen') && setError('quanHuyen', {type: 'required', message: 'Thông tin bắt buộc'});
      !getValues('tinhThanh') && setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    }
  };

  // const onChangeValueDropdown = (items) => {
  //   let itemSelected = items.filter((item) => item.ma == itemValueSelected);
  //   if (itemSelected.length > 0) {
  //     if (title == titleDropdownInput[0]) {
  //       setDistrictsDataDropDown(itemSelected[0].district);
  //       setWardsDataDropDown([]);
  //       // setValue('quanHuyen', '');
  //     } else if (title == titleDropdownInput[1]) {
  //       if (itemSelected.length > 0) {
  //         setWardsDataDropDown(itemSelected[0].ward);
  //         // setValue('xaPhuong', '');
  //       }
  //     }
  //   }
  // };

  const onChangeTinhThanh = (city, prev) => {
    setDistrictsDataDropDown(city.district);
    if (city && city.ma !== prev) {
      setValue('quanHuyen', '');
      setWardsDataDropDown([]);
      setValue('xaPhuong', '');
      return;
    }
  };

  const onChangeQuanHuyen = (district, prev) => {
    setWardsDataDropDown(district.ward);
    if (district && district.ma !== prev) {
      setValue('xaPhuong', '');
      return;
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'dienThoai' || inputName === 'dienThoaiLaiXe') {
      if (errType === 'pattern') return 'Số điện thoại sai định dạng';
    } else if (inputName === 'email' || inputName === 'emailLaiXe') {
      if (errType === 'pattern') return 'Email sai định dạng';
    }
    return '';
  };

  const getTenHienThi = (value, data) => {
    let name = '';
    data.map((e) => {
      if (e.ma === value) name = e.ten;
    });
    return name;
  };

  const getTenHienThiNhomSuKien = (value, data) => {
    let name = '';
    if (value && value !== '') {
      const filterData = data.filter((e) => e.isChecked);
      const listTenSuKien = filterData.map((item) => {
        return item.ten_sk;
      });
      let ten = JSON.stringify(listTenSuKien);
      name = ten.replace(/[\[\]'"]+/g, '');
    }
    return name;
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const onPressLayDiaChiHienTai = () => {
    requestCurrentLocation(
      async (position) => {
        setDisableBtnLayDiaChi(true);
        let response = await geoCodeChuyenToaDoThanhDiaChi({
          lat: position.coords.latitude,
          lon: position.coords.longitude,
          // lat: '20.98943885851548',
          // lon: '105.84141973472079',
          //20.98943885851548, 105.84141973472079
        });
        // console.log('response', response);
        // let response = {
        //   data: {
        //     place_id: 228722580,
        //     licence: 'Data © OpenStreetMap contributors, ODbL 1.0. https://osm.org/copyright',
        //     osm_type: 'way',
        //     osm_id: 224839335,
        //     lat: '10.914053301747073',
        //     lon: '106.71326384510506',
        //     display_name: 'Binh Duong Road, Phường Bình Hòa, Thuận An, Bình Dương Province, 70000, Vietnam',
        //     address: {
        //       road: 'Binh Duong Road',
        //       suburb: 'Phường Bình Hòa',
        //       city: 'Thuận An',
        //       state: 'Bình Dương Province',
        //       'ISO3166-2-lvl4': 'VN-57',
        //       postcode: '70000',
        //       country: 'Vietnam',
        //       country_code: 'vn',
        //     },
        //     boundingbox: ['10.901221', '10.9239548', '106.7117471', '106.7136213'],
        //   },
        // };
        // console.log('response', response);
        setDisableBtnLayDiaChi(false);
        if (response) {
          if (response.data) {
            let diaChiFormat = {
              maTinhThanh: null,
              maQuanHuyen: null,
              maXaPhuong: null,
              diaChiDayDu: null,
            };
            diaChiFormat = geoFormatLaiDiaChi(response.data);
            // LOG RA LỖI NẾU KHÔNG FILL ĐỦ DATA VÀO
            if (response.data.error || diaChiFormat?.maTinhThanh === null || diaChiFormat?.maQuanHuyen === null || diaChiFormat?.maXaPhuong === null) {
              logErrorTryCatch({
                code: 'GEOCODE_KHAI_BAO_TON_THAT',
                message: JSON.stringify(response.data),
              });
            }
            if (diaChiFormat.maTinhThanh) {
              citiesData.forEach((itemTinhThanh) => {
                if (itemTinhThanh.ma === diaChiFormat.maTinhThanh) {
                  setValue('tinhThanh', itemTinhThanh.ma, {shouldValidate: true}); //set Tỉnh thành được chọn
                  setDistrictsDataDropDown([...itemTinhThanh.district]); //set list quận huyện của tỉnh thành
                  //nếu có quận huyện được chọn
                  if (diaChiFormat.maQuanHuyen) {
                    let listQuanHuyen = itemTinhThanh.district;
                    listQuanHuyen.forEach((itemQuanHuyen) => {
                      if (itemQuanHuyen.ma === diaChiFormat.maQuanHuyen) {
                        setValue('quanHuyen', itemQuanHuyen.ma, {shouldValidate: true}); //set quận huyện được chọn
                        setWardsDataDropDown([...itemQuanHuyen.ward]); //set list xã phường của quận huyện
                        if (diaChiFormat.maXaPhuong) setValue('xaPhuong', diaChiFormat.maXaPhuong, {shouldValidate: true});
                      }
                    });
                  }
                }
              });
            }
          } else FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa tồn tại địa chỉ tại địa điểm này. Vui lòng thử lại');
        }
      },
      (error) => logErrorTryCatch(error),
    );
  };

  // RENDER
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderContent = () => {
    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="gioXayRa"
                  render={({field: {onChange, value}}) => {
                    return (
                      <View>
                        <TextInputOutlined
                          isRequired
                          title="Giờ xảy ra"
                          editable={false}
                          isDateTimeField
                          isTouchableOpacity
                          placeholder="Chọn giờ"
                          inputStyle={{color: colors.BLACK}}
                          onPress={() => setToggleGioXR(true)}
                          value={value ? moment(value).format('HH:mm') : ''}
                          error={errors.gioXayRa && getErrMessage('gioXayRa', errors.gioXayRa.type)}
                        />
                        {renderDateTimeComp(toggleGioXR, setToggleGioXR, (val) => setValue('gioXayRa', val, {shouldValidate: true}), value !== '' ? value : new Date(), 'time', null, null, 0)}
                      </View>
                    );
                  }}
                />
              </View>

              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="ngayXayRa"
                  render={({field: {onChange, value}}) => {
                    return (
                      <View>
                        <TextInputOutlined
                          isRequired
                          isDateTimeField
                          editable={false}
                          disabled={false}
                          isTouchableOpacity
                          title="Ngày xảy ra"
                          placeholder="Chọn ngày"
                          inputStyle={{color: colors.BLACK}}
                          onPress={() => setToggleNgayXR(true)}
                          value={value ? moment(value).format('DD/MM/YYYY') : ''}
                          error={errors.ngayXayRa && getErrMessage('ngayXayRa', errors.ngayXayRa.type)}
                        />
                        {renderDateTimeComp(toggleNgayXR, setToggleNgayXR, (val) => setValue('ngayXayRa', val, {shouldValidate: true}), value !== '' ? value : new Date(), 'date', null, new Date(), 0)}
                      </View>
                    );
                  }}
                />
              </View>
            </View>

            <Controller
              control={control}
              name="tinhThanh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <View style={{zIndex: 8000, flexDirection: 'row', flex: 1, alignItems: 'flex-start'}}>
                  <DropdownPicker
                    title={titleDropdownInput[0]}
                    zIndex={8000}
                    items={citiesData.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    isOpen={openCity}
                    setOpen={setOpenCity}
                    placeholder="Chọn Tỉnh thành"
                    onOpen={() => closeDropdown(titleDropdownInput[0])}
                    containerStyle={{marginBottom: openCity ? 300 : spacing.smaller, flex: 1, marginTop: 0}}
                    onSelectItem={(value) => onChangeTinhThanh(value, tinhThanh)}
                    inputErr={errors.tinhThanh && getErrMessage('tinhThanh', errors.tinhThanh.type)}
                    isRequired={true}
                    schema={{
                      label: 'ten',
                      value: 'ma',
                    }}
                  />
                  <TouchableOpacity style={{marginTop: spacing.large, marginLeft: spacing.smaller}} onPress={onPressLayDiaChiHienTai} disabled={disableBtnLayDiaChi}>
                    {!disableBtnLayDiaChi ? <Icon.Entypo name="location" color={colors.PRIMARY} size={30} /> : <ActivityIndicator size="large" color={colors.PRIMARY} />}
                  </TouchableOpacity>
                </View>
              )}
            />
            <Controller
              control={control}
              name="quanHuyen"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  title={hoSoSauNgayChuyenDoi ? titleDropdownInput[2] : titleDropdownInput[1]}
                  zIndex={6000}
                  items={districtsDataDropDown.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  isOpen={openDistrict}
                  setOpen={setOpenDistrict}
                  placeholder={`Chọn ${hoSoSauNgayChuyenDoi ? 'Xã phường' : ''}`}
                  onOpen={() => closeDropdown(hoSoSauNgayChuyenDoi ? titleDropdownInput[2] : titleDropdownInput[1])}
                  onSelectItem={(value) => onChangeQuanHuyen(value, quanHuyen)}
                  inputErr={errors.quanHuyen && getErrMessage('quanHuyen', errors.quanHuyen.type)}
                  isRequired={true}
                  schema={{
                    label: 'ten',
                    value: 'ma',
                  }}
                />
              )}
            />
            {/* <Controller
              control={control}
              name="xaPhuong"
              rules={{
                required: isRequiredFieldXaPhuong,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  isRequired={isRequiredFieldXaPhuong}
                  title={titleDropdownInput[2]}
                  zIndex={5000}
                  items={wardsDataDropDown}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  isOpen={openWard}
                  setOpen={setOpenWard}
                  placeholder="Chọn Xã phường"
                  onOpen={() => closeDropdown(titleDropdownInput[2])}
                  schema={{
                    label: 'ten',
                    value: 'ma',
                  }}
                  // onSelectItem={onChangeValueDropdown}
                  inputErr={errors.xaPhuong && getErrMessage('xaPhuong', errors.xaPhuong.type)}
                />
              )}
            /> */}
            <Controller
              control={control}
              name="diaDiemXayRa"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired={true}
                  blurOnSubmit={false}
                  value={value}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  title="Địa điểm xảy ra cụ thể"
                  placeholder="Địa điểm xảy ra cụ thể"
                  getRef={(ref) => (diaDiemRef = ref)}
                  onChangeText={onChange}
                  onSubmitEditing={() => nguyenNhanChiTietRef?.focus()}
                  error={errors.diaDiemXayRa && getErrMessage('diaDiemXayRa', errors.diaDiemXayRa.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="nhomNguyenNhan"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isDropdown
                  isRequired
                  editable={false}
                  isTouchableOpacity
                  title="Nhóm nguyên nhân"
                  inputStyle={{color: colors.BLACK}}
                  onPress={() => refModalChonNhomNguyenNhan.current.show()}
                  value={getTenHienThi(value, danhMucSanPhamXe)}
                  placeholder="Chọn nhóm nguyên nhân"
                  error={errors.nhomNguyenNhan && getErrMessage('nhomNguyenNhan', errors.nhomNguyenNhan.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="nhomSuKien"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isDropdown
                  editable={false}
                  isTouchableOpacity
                  title="Nhóm sự kiện bảo hiểm"
                  inputStyle={{color: colors.BLACK}}
                  onPress={() => refModalChonNhomSuKienBaoHiem.current.show()}
                  value={getTenHienThiNhomSuKien(value, listSuKienBaoHiem)}
                  placeholder="Chọn nhóm sự kiện"
                />
              )}
            />
            <Controller
              control={control}
              name="nguyenNhanChiTiet"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired={true}
                  value={value}
                  blurOnSubmit={false}
                  onFocus={onInputFocus}
                  title="Nguyên nhân chi tiết"
                  placeholder="Nguyên nhân chi tiết"
                  onChangeText={onChange}
                  // onSubmitEditing={() => hauQuaRef?.focus()}
                  getRef={(ref) => (nguyenNhanChiTietRef = ref)}
                  multiline
                  error={errors.nguyenNhanChiTiet && getErrMessage('nguyenNhanChiTiet', errors.nguyenNhanChiTiet.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="hauQua"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  title="Hậu quả"
                  placeholder="Nhập hậu quả"
                  isRequired={true}
                  blurOnSubmit={false}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  getRef={(ref) => (hauQuaRef = ref)}
                  error={errors.hauQua && getErrMessage('hauQua', errors.hauQua.type)}
                  multiline
                />
              )}
            />

            <Controller
              control={control}
              name="hauQuaNguoiThuBa"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined value={value} title="Hậu quả bên thứ ba" placeholder="Nhập hậu quả bên thứ ba" blurOnSubmit={false} onFocus={onInputFocus} onChangeText={onChange} multiline />
              )}
            />

            <Controller
              control={control}
              name="tenLaiXe"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  title="Họ tên lái xe"
                  placeholder="Họ tên lái xe"
                  blurOnSubmit={false}
                  onFocus={onInputFocus}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  getRef={(ref) => (hoTenLXRef = ref)}
                  onSubmitEditing={() => phoneRef?.focus()}
                  error={errors.tenLaiXe && getErrMessage('tenLaiXe', errors.tenLaiXe.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="dienThoaiLaiXe"
              rules={{
                required: false,
                pattern: REGUlAR_EXPRESSION.REG_PHONE,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  title="Điện thoại"
                  placeholder="Điện thoại"
                  value={value}
                  blurOnSubmit={false}
                  onFocus={onInputFocus}
                  returnKeyType={'next'}
                  keyboardType="phone-pad"
                  onChangeText={onChange}
                  getRef={(ref) => (phoneRef = ref)}
                  onSubmitEditing={() => emailRef?.focus()}
                  error={errors.dienThoaiLaiXe && getErrMessage('dienThoaiLaiXe', errors.dienThoaiLaiXe.type)}
                />
              )}
            />

            <Controller
              control={control}
              name="emailLaiXe"
              rules={{
                required: false,
                pattern: REGUlAR_EXPRESSION.REG_EMAIL,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  title="Email"
                  placeholder="Email lái xe"
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  keyboardType="email-address"
                  getRef={(ref) => (emailRef = ref)}
                  onSubmitEditing={() => soGPLXRef?.focus()}
                  error={errors.emailLaiXe && getErrMessage('emailLaiXe', errors.emailLaiXe.type)}
                />
              )}
            />

            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="soGPLX"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      title="Số GPLX"
                      returnKeyType={'next'}
                      getRef={(ref) => (soGPLXRef = ref)}
                      onSubmitEditing={() => soDangKiemRef?.focus()}
                      value={value}
                      onChangeText={onChange}
                      onFocus={onInputFocus}
                      blurOnSubmit={false}
                      placeholder="Số GPLX"
                      onBlur={() => value !== '' && onChange(onChangeAlias(value, {xoaKyTuDacBiet: false}).toUpperCase())}
                      autoCapitalize={'characters'}
                    />
                  )}
                />
              </View>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="hangGPLX"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isDropdown
                      value={value}
                      editable={false}
                      isTouchableOpacity
                      title="Hạng GPLX"
                      placeholder="Hạng GPLX"
                      inputStyle={{color: colors.BLACK}}
                      onPress={() => refModal.current.show()}
                    />
                  )}
                />
              </View>
            </View>
            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="ngayCapGPLX"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <View>
                      <TextInputOutlined
                        isDateTimeField
                        editable={false}
                        isTouchableOpacity
                        title="Ngày cấp GPLX"
                        placeholder="Ngày cấp GPLX"
                        inputStyle={{color: colors.BLACK}}
                        onPress={() => setToggleNgayCapGPLX(true)}
                        value={value ? moment(value).format('DD/MM/YYYY') : ''}
                      />
                      {renderDateTimeComp(
                        toggleNgayCapGPLX,
                        setToggleNgayCapGPLX,
                        (val) => setValue('ngayCapGPLX', val, {shouldValidate: true}),
                        value !== '' ? value : new Date(),
                        'date',
                        null,
                        new Date(),
                        0,
                      )}
                    </View>
                  )}
                />
              </View>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="ngayHetHanGPLX"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <View>
                      <TextInputOutlined
                        isDateTimeField
                        editable={false}
                        isTouchableOpacity
                        title="Ngày hết hạn"
                        placeholder="Ngày hết hạn"
                        inputStyle={{color: colors.BLACK}}
                        onPress={() => setToggleNgayHetHanGPLX(true)}
                        value={value ? moment(value).format('DD/MM/YYYY') : ''}
                      />
                      {renderDateTimeComp(
                        toggleNgayHetHanGPLX,
                        setToggleNgayHetHanGPLX,
                        (val) => setValue('ngayHetHanGPLX', val, {shouldValidate: true}),
                        value !== '' ? value : new Date(),
                        'date',
                        null,
                        null,
                        0,
                      )}
                    </View>
                  )}
                />
              </View>
            </View>
            <Controller
              control={control}
              name="soDangKiem"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  title="Số đăng kiểm"
                  placeholder="Số đăng kiểm"
                  getRef={(ref) => (soDangKiemRef = ref)}
                  value={value}
                  onChangeText={onChange}
                  onFocus={onInputFocus}
                  blurOnSubmit={false}
                  onBlur={() => value !== '' && onChange(onChangeAlias(value, {xoaKyTuDacBiet: false}).toUpperCase())}
                  autoCapitalize={'characters'}
                />
              )}
            />

            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="ngayCapDangKiem"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <View>
                      <TextInputOutlined
                        isDateTimeField
                        editable={false}
                        isTouchableOpacity
                        title="Ngày cấp từ"
                        placeholder="Ngày cấp từ"
                        inputStyle={{color: colors.BLACK}}
                        onPress={() => setToggleNgayCapDangKiem(true)}
                        value={value ? moment(value).format('DD/MM/YYYY') : ''}
                      />
                      {renderDateTimeComp(
                        toggleNgayCapDangKiem,
                        setToggleNgayCapDangKiem,
                        (value) => setValue('ngayCapDangKiem', value, {shouldValidate: true}),
                        value !== '' ? value : new Date(),
                        'date',
                        null,
                        new Date(),
                        0,
                      )}
                    </View>
                  )}
                />
              </View>

              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="ngayHetHanDangKiem"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <View>
                      <TextInputOutlined
                        isDateTimeField
                        editable={false}
                        title="Đến ngày"
                        isTouchableOpacity
                        placeholder="Đến ngày"
                        inputStyle={{color: colors.BLACK}}
                        onPress={() => setToggleNgayHetHanDangKiem(true)}
                        value={value ? moment(value).format('DD/MM/YYYY') : ''}
                      />
                      {renderDateTimeComp(
                        toggleNgayHetHanDangKiem,
                        setToggleNgayHetHanDangKiem,
                        (value) => setValue('ngayHetHanDangKiem', value, {shouldValidate: true}),
                        value !== '' ? value : new Date(),
                        'date',
                        null,
                        null,
                        0,
                      )}
                    </View>
                  )}
                />
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>
        <ModalChonHangGPLX data={hangGPLX} arrChecked={listHangGPLX} setArrChecked={setListHangGPLX} ref={refModal} onBackPress={() => refModal.current.hide()} />
        {/* <ModalSuKienBaoHiem
          data={suKienBaoHiem}
          arrChecked={listSuKienBaoHiem}
          setArrChecked={setListSuKienBaoHiem}
          ref={refModalChonNhomSuKienBaoHiem}
          onBackPress={() => refModalChonNhomSuKienBaoHiem.current.hide()}
        /> */}
        <ModalChonNhomNguyenNhan
          danhMucSanPhamXe={danhMucSanPhamXe}
          value={nhomNguyenNhan}
          setValue={(value) => setValue('nhomNguyenNhan', value.ma, {shouldValidate: true})}
          ref={refModalChonNhomNguyenNhan}
          onBackPress={() => refModalChonNhomNguyenNhan.current.hide()}
        />
        <ModalChonNhomSuKienBaoHiem
          baseData={listSuKienBaoHiem}
          value={nhomSuKien}
          setValue={onSetValueSuKienBaoHiem}
          ref={refModalChonNhomSuKienBaoHiem}
          onBackPress={() => refModalChonNhomSuKienBaoHiem.current.hide()}
        />
      </SafeAreaView>
    );
  };

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {type === 'EDIT' && (
          <ButtonLinear title="Xoá" onPress={onPressXoaVuTonThat} linearStyle={{marginRight: spacing.small}} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />
        )}
        <ButtonLinear title="Lưu" onPress={handleSubmit(onPressLuu)} />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Khai báo vụ tổn thất"
      renderView={renderContent()}
      footer={renderFooter()}
      //
    />
  );
};

export const KhaiBaoVuTonThatScreen = memo(KhaiBaoVuTonThatScreenComponent, isEqual);
