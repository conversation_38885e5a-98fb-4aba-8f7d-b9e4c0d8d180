import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {CheckboxComp, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Platform, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonNhomSuKienBaoHiemComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, baseData} = props;
  const [data, setData] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const initModalData = () => {
    setData(baseData);
  };

  const onChangeCheckBoxValue = (item, index, val) => {
    let dataUpdate = [...data];
    dataUpdate[index].isChecked = val;
    setData(dataUpdate);
  };

  const onSave = () => {
    setValue && setValue(data);
    onBackPress && onBackPress();
  };

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  /* RENDER */
  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index, !item.isChecked)}>
        <CheckboxComp value={item.isChecked} checkboxStyle={styles.checkbox} disabled />
        <Text style={styles.txtView}>{item.ten_sk}</Text>
      </TouchableOpacity>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <TouchableOpacity style={styles.headerBtnRight} onPress={onSave}>
        <Text children="Xong" style={styles.txtBtnLuu} />
      </TouchableOpacity>
    );
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} onBackButtonPress={onBackPress} onModalWillShow={initModalData}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={onPressBack} title="Thông tin sự kiện bảo hiểm" rightComponent={renderRightHeaderComponent()} />
        <FlatList data={data} extraData={data} renderItem={renderItem} onEndReachedThreshold={0.3} keyExtractor={(item, index) => item + index.toString()} style={styles.flStyles} />
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 8,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingVertical: 10,
    flexDirection: 'row',
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginRight: 5,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Platform.OS === 'android' ? 4 : 2,
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  flStyles: {
    paddingHorizontal: 10,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
});

export const ModalChonNhomSuKienBaoHiem = memo(ModalChonNhomSuKienBaoHiemComponent, isEqual);
