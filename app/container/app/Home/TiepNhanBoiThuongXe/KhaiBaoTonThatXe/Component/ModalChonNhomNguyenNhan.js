import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {CheckboxComp, SearchBar, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {FlatList, Platform, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalChonNhomNguyenNhanComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, danhMucSanPhamXe, value} = props;
  const [data, setData] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  const [searchInput, setSearchInput] = useState('');

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const initModalData = () => {
    let newArr = danhMucSanPhamXe.filter((item) => item.nhom === 'NHOM_NGUYEN_NHAN');
    newArr.map((e, i) => {
      newArr[i].isChecked = false;
      if (e.ma === value) {
        newArr[i].isChecked = true;
      }
    });
    setData([...newArr]);
  };

  const onPressItem = (item, index) => {
    let newArr = danhMucSanPhamXe;
    newArr.map((e, i) => {
      newArr[i].isChecked = false;
      if (e.ma === value) {
        newArr[index].isChecked = true;
      }
    });
    setValue && setValue(item);
    onBackPress && onBackPress();
  };

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  useEffect(() => {
    let newArr = danhMucSanPhamXe.filter((item) => item.nhom === 'NHOM_NGUYEN_NHAN');
    if (searchInput && searchInput !== '') {
      const lowerCaseSearchText = searchInput?.toLowerCase();
      const filter = newArr.filter((item) => item?.ten?.toLowerCase()?.includes(lowerCaseSearchText) || item?.ma?.toLowerCase()?.includes(lowerCaseSearchText));
      setData(filter);
    } else setData(newArr);
  }, [searchInput]);

  const debounced = useDebouncedCallback((val) => {
    setSearchInput(val);
  }, 500);

  /* RENDER */
  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={item.isChecked} checkboxStyle={styles.checkbox} disabled />
        <Text style={styles.txtView}>{item.label}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return <FlatList data={data} extraData={data} renderItem={renderItem} onEndReachedThreshold={0.3} keyExtractor={(item, index) => item + index.toString()} style={styles.flStyles} />;
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} onBackButtonPress={onBackPress} onModalWillShow={initModalData}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={onPressBack} title="Chọn nhóm nguyên nhân" />
        <SearchBar onTextChange={debounced} />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 8,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingVertical: 10,
    flexDirection: 'row',
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginRight: 5,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Platform.OS === 'android' ? 4 : 2,
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  flStyles: {
    paddingHorizontal: 10,
  },
});

export const ModalChonNhomNguyenNhan = ModalChonNhomNguyenNhanComponent;
