import {colors} from '@app/commons/Theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Dimensions, Platform, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
const {width, height} = Dimensions.get('screen');

const ModalChonHangGPLXComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, data, setArrChecked, arrChecked} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [dataGPLX, setDataGPLX] = useState(data);

  useEffect(() => {
    data.forEach((item) => {
      item.isChecked = arrChecked.findIndex((x) => x.ma == item.ma) != -1;
    });
    setDataGPLX(data);
  }, []);

  const onChangeCheckBoxValue = (item, index, value) => {
    let dataUpdate = [...dataGPLX];
    dataUpdate[index].isChecked = value;
    setDataGPLX(dataUpdate);
    const isCheckedDataArr = dataGPLX.filter((item) => item.isChecked === true);
    setArrChecked && setArrChecked(isCheckedDataArr);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Chọn hạng GPLX" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = (item, index) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={(value) => onChangeCheckBoxValue(item, index, !item.isChecked)}>
        <CheckboxComp value={item.isChecked} checkboxStyle={styles.checkbox} onValueChange={(value) => onChangeCheckBoxValue(item, index, value)} />
        <Text style={{color: item.isChecked ? colors.PRIMARY : colors.BLACK_03, marginTop: Platform.OS == 'android' ? 4 : 2}}>{item.ten}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View>{dataGPLX.map((item, index) => renderItem(item, index))}</View>
      </ScrollView>
    );
  };
  return (
    <Modal
      ///onModalShow
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: width,
    height: height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: 16,
    borderColor: colors.GRAY,
    // flex: 1,
    height: 40,
    margin: 16,
    borderRadius: 25,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 8,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
});

// const mapStateToProps = (state) => ({});
// const mapDispatchToProps = {};
// export default connect(mapStateToProps, mapDispatchToProps)(ModalChonHinhThucDieuTri);
export const ModalChonHangGPLX = ModalChonHangGPLXComponent;
