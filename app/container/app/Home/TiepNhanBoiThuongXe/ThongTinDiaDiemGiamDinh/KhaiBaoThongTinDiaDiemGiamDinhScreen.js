import {FORMAT_DATE_TIME, NGAY_CHUYEN_DOI, SCREEN_ROUTER_APP, isRequiredFieldXaPhuong} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Icon, ModalSelectSimple, ScreenComponent, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {connect} from 'react-redux';
// import { ModalChonMoiQuanHeVoiChuXe} from '../KhaiBaoBoiThuongXe/Components';
import styles from './KhaiBaoThongTinDiaDiemGiamDinhStyles';
import {Controller, useForm} from 'react-hook-form';
import {geoCodeChuyenToaDoThanhDiaChi, geoFormatLaiDiaChi, requestCurrentLocation} from '@app/utils/LocationProvider';
import {logErrorTryCatch} from '@app/utils';

const dataHienTruong = [
  {label: 'Xe đang ở hiện trường', value: 'D'},
  {label: 'Xe không ở hiện trường', value: 'K'},
];

const relationship = [
  {Name: 'Chủ xe', ma: 'QH.0001', Value: 'QH.0001', Id: 'QH.0001'},
  {Name: 'Lái xe', ma: 'QH.0002', Value: 'QH.0002', Id: 'QH.0002'},
  {Name: 'Người khác', ma: 'QH.0005', Value: 'QH.0005', Id: 'QH.0005'},
];
const titleDropdownInput = ['Tỉnh thành', 'Quận huyện', 'Xã phường', 'Nhóm nguyên nhân'];

const KhaiBaoThongTinDiaDiemGiamDinhScreenComponent = (props) => {
  console.log('KhaiBaoThongTinDiaDiemGiamDinhScreenComponentTNBT');
  const {route, citiesData} = props;
  const {profileData} = route?.params;
  const hoSoSauNgayChuyenDoi = profileData?.ho_so?.ngay_mo_hs >= NGAY_CHUYEN_DOI && profileData?.ho_so?.ngay_mo_hs >= profileData?.ho_so?.ngay_upd_dvi_hanh_chinh;

  // const [citiesDataDropDown, setCitiesDataDropDown] = useState([]);
  const [openCity, setOpenCity] = useState(false);
  const [districtsDataDropDown, setDistrictsDataDropDown] = useState([]);
  const [openDistrict, setOpenDistrict] = useState(false);
  const [wardsDataDropDown, setWardsDataDropDown] = useState([]);
  const [openWard, setOpenWard] = useState(false);
  const [toggleGioXR, setToggleGioXR] = useState(false);
  const [toggleNgayXR, setToggleNgayXR] = useState(false);
  const [isDisable, setIsDisable] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);

  const [disableBtnLayDiaChi, setDisableBtnLayDiaChi] = useState(false);

  let tieuDeRef = useRef(null);
  let diaDiemRef = useRef(null);
  // let refModalMoiQuanHe = useRef(null);
  let refModalHienTruong = useRef(null);

  const getDefaultFormValues = () => {
    return {
      hienTruong: profileData?.ho_so?.hien_truong || 'K',
      gioHenGiamDinh: moment().add(15, 'm').toDate(),
      ngayHenGiamDinh: new Date(),
      tinhThanh: profileData?.ho_so?.tinh_thanh || '',
      quanHuyen: profileData?.ho_so?.quan_huyen || '',
      xaPhuong: profileData?.ho_so?.phuong_xa || '',
      diaDiemGiamDinh: profileData?.ho_so?.dia_diem || '',
      nguoiLienHe: profileData?.ho_so?.nguoi_lhe || '',
      dthoaiLienHe: profileData?.ho_so?.dthoai_lhe || '',
      emailLienHe: profileData?.ho_so?.email_lhe || '',
      moiQuanHeNgThongBao: '',
    };
  };
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    setError,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: getDefaultFormValues(),
    mode: 'onChange',
  });

  const hienTruong = watch('hienTruong');
  const watchTinhThanh = watch('tinhThanh');

  useEffect(() => {
    let itemSelected = citiesData.filter((item) => item.ma === watchTinhThanh);
    if (itemSelected.length > 0) {
      setDistrictsDataDropDown(itemSelected[0].district);
    }
  }, [watchTinhThanh]);

  useEffect(() => {
    const filter = relationship.filter((item) => item.ma === profileData?.ho_so?.moi_qh_lhe);
    if (filter.length > 0) setValue('moiQuanHeNgThongBao', filter[0].ma);
  }, []);

  useEffect(() => {
    if (profileData?.dien_bien?.length > 0) {
      if (hienTruong === 'D') {
        if (profileData.dien_bien[0].tinh_thanh) {
          setValue('tinhThanh', profileData.dien_bien[0].tinh_thanh, {shouldValidate: true});
          citiesData.forEach((itemTinhThanh) => {
            if (itemTinhThanh.ma === profileData.dien_bien[0].tinh_thanh) {
              setDistrictsDataDropDown([...itemTinhThanh.district]); //set list quận huyện của tỉnh thành
              //nếu có quận huyện được chọn
              if (profileData.dien_bien[0].quan_huyen) {
                setValue('quanHuyen', profileData.dien_bien[0].quan_huyen, {shouldValidate: true});
                let listQuanHuyen = itemTinhThanh.district;
                listQuanHuyen.forEach((itemQuanHuyen) => {
                  if (itemQuanHuyen.ma === profileData.dien_bien[0].quan_huyen) {
                    setWardsDataDropDown([...itemQuanHuyen.ward]); //set list xã phường của quận huyện
                    if (profileData.dien_bien[0].phuong_xa) setValue('xaPhuong', profileData.dien_bien[0].phuong_xa, {shouldValidate: true});
                  }
                });
              }
            }
          });
          setValue('diaDiemGiamDinh', profileData.ho_so?.dia_diem, {shouldValidate: true});
        }
        setIsDisable(true);
      } else {
        if (profileData?.ho_so?.tinh_thanh) {
          setValue('tinhThanh', profileData?.ho_so?.tinh_thanh, {shouldValidate: true});
          citiesData.forEach((itemTinhThanh) => {
            if (itemTinhThanh.ma === profileData?.ho_so?.tinh_thanh) {
              setDistrictsDataDropDown([...itemTinhThanh.district]); //set list quận huyện của tỉnh thành
              //nếu có quận huyện được chọn
              if (profileData.ho_so?.quan_huyen) {
                setValue('quanHuyen', profileData.ho_so?.quan_huyen, {shouldValidate: true});
                let listQuanHuyen = itemTinhThanh.district;
                listQuanHuyen.forEach((itemQuanHuyen) => {
                  if (itemQuanHuyen.ma === profileData.ho_so?.quan_huyen) {
                    setWardsDataDropDown([...itemQuanHuyen.ward]); //set list xã phường của quận huyện
                    if (profileData.ho_so?.phuong_xa) setValue('xaPhuong', profileData.ho_so?.phuong_xa, {shouldValidate: true});
                  }
                });
              }
            }
          });
          setValue('diaDiemGiamDinh', profileData.ho_so?.dia_diem, {shouldValidate: true});
        }
        setIsDisable(false);
      }
    }
  }, [hienTruong]);

  // useEffect(() => {
  //   if (moiQuanHeNgThongBao === 'QH.0001') {
  //     setValue('nguoiLienHe', profileData?.ho_so?.chu_xe);
  //     setValue('emailLienHe', profileData?.ho_so?.email);
  //     setValue('dthoaiLienHe', profileData?.ho_so?.dien_thoai);
  //   }
  //   if (moiQuanHeNgThongBao === 'QH.0002') {
  //     setValue('nguoiLienHe', profileData?.dien_bien[0]?.ten_lxe);
  //     setValue('emailLienHe', profileData?.dien_bien[0]?.email_lxe);
  //     setValue('dthoaiLienHe', profileData?.dien_bien[0]?.dthoai_lxe);
  //   }
  // }, [moiQuanHeNgThongBao]);

  const onInputFocus = () => {
    setOpenCity(false);
    setOpenDistrict(false);
    setOpenWard(false);
  };

  const onPressRegisterSchedule = async (type, data) => {
    setDialogLoading(true);
    try {
      const params = {
        pm: 'TNBT',
        dvi_gdinh: '',
        ma_gdv: '',
        so_id: profileData?.ho_so?.so_id, //id hồ sơ
        hien_truong: data.hienTruong,
        gio_gd: moment(data.gioHenGiamDinh).format('HH:mm'),
        ngay_gd: +moment(data.ngayHenGiamDinh).format(FORMAT_DATE_TIME.API_DATE_FORMAT) || '',
        tinh_thanh: data.tinhThanh,
        quan_huyen: data.quanHuyen,
        phuong_xa: data.xaPhuong,
        dia_diem: data.diaDiemGiamDinh,
        nguoi_lhe: data.nguoiLienHe,
        moi_qh_lhe: data.moiQuanHeNgThongBao,
        dthoai_lhe: data.dthoaiLienHe,
        email_lhe: data.emailLienHe,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.KHAI_BAO_DIA_DIEM_GIAM_DINH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (type === 'NEXT') chuyenGiamDinh();
      if (type === 'SAVE') FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu thông tin giám định thành công', 'success');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const chuyenGiamDinh = async () => {
    setDialogLoading(true);
    try {
      const params = {
        so_id: profileData?.ho_so?.so_id, //id hồ sơ
      };
      let response = await PartnerEndpoint.chuyenGiamDinh(AxiosConfig.ACTION_CODE.CHUYEN_GIAM_DINH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển giám định thành công', 'success');
      // NavigationUtil.pop();
      let navScreen = SCREEN_ROUTER_APP.PROFILE_ASSESSMENT;
      if (profileData?.ho_so?.nghiep_vu === 'XE_MAY') navScreen = SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY;
      NavigationUtil.push(navScreen, {profileDetail: profileData.ho_so, prevScreen: SCREEN_ROUTER_APP.KHAI_BAO_TT_DIA_DIEM_GIAM_DINH});
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const closeDropdown = (title) => {
    title !== titleDropdownInput[0] && openCity && setOpenCity(false);
    title !== titleDropdownInput[1] && openDistrict && setOpenDistrict(false);
    title !== titleDropdownInput[2] && openWard && setOpenWard(false);

    if (title === titleDropdownInput[1] && !getValues('tinhThanh')) setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    if (title === titleDropdownInput[2]) {
      !getValues('quanHuyen') && setError('quanHuyen', {type: 'required', message: 'Thông tin bắt buộc'});
      !getValues('tinhThanh') && setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    }
  };

  const onChangeValueDropdown = (title, items, itemValueSelected) => {
    //itemValueSelected : value của item được chọn
    let itemSelected = items.filter((item) => item.ma == itemValueSelected);
    if (itemSelected.length > 0) {
      if (title == titleDropdownInput[0]) {
        setDistrictsDataDropDown(itemSelected[0].district);
        setWardsDataDropDown([]);
      } else if (title == titleDropdownInput[1]) {
        if (itemSelected.length > 0) {
          setWardsDataDropDown(itemSelected[0].ward);
        }
      }
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate, type) => {
    setToggleDateTime(false);
    setDate(date);
  };

  // const onChangeTextDiaDiem = (value) => {
  //   let errInputTmp = inputErr;
  //   errInputTmp[3] = '';
  //   if (value != null && !value.trim()) {
  //     errInputTmp[3] = 'Vui lòng ghi rõ địa điểm';
  //   }
  //   setAddressInput(value);
  //   setInputErr([...errInputTmp]);
  // };
  // const onChangeMoiQuanHe = (value) => {
  //   let errInputTmp = inputErr;
  //   errInputTmp[4] = '';
  //   if (value != null && !value) {
  //     errInputTmp[4] = 'Vui lòng chọn mối quan hệ';
  //   }
  //   setMoiQuanHeNgThongBao(value);
  //   setInputErr([...errInputTmp]);
  // };

  const getTenHienThi = (value, data) => {
    let name = '';
    data.map((e) => {
      if (value === e.value) name = e.label;
    });
    return name;
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    // if (inputName === 'dienThoai' || inputName === 'dienThoaiLaiXe') {
    //   if (errType === 'pattern') return 'Số điện thoại sai định dạng';
    // } else if (inputName === 'email' || inputName === 'emailLaiXe') {
    //   if (errType === 'pattern') return 'Email sai định dạng';
    // }
    return '';
  };

  const onPressLayDiaChiHienTai = () => {
    requestCurrentLocation(
      async (position) => {
        setDisableBtnLayDiaChi(true);
        let response = await geoCodeChuyenToaDoThanhDiaChi({
          lat: position.coords.latitude,
          lon: position.coords.longitude,
        });

        // console.log('response', response);
        setDisableBtnLayDiaChi(false);
        if (response) {
          if (response.data) {
            let diaChiFormat = {
              maTinhThanh: null,
              maQuanHuyen: null,
              maXaPhuong: null,
              diaChiDayDu: null,
            };
            diaChiFormat = geoFormatLaiDiaChi(response.data);
            // LOG RA LỖI NẾU KHÔNG FILL ĐỦ DATA VÀO
            if (response.data.error || diaChiFormat?.maTinhThanh === null || diaChiFormat?.maQuanHuyen === null || diaChiFormat?.maXaPhuong === null) {
              logErrorTryCatch({
                code: 'GEOCODE_KHAI_BAO_TON_THAT',
                message: JSON.stringify(response.data),
              });
            }
            if (diaChiFormat.maTinhThanh) {
              citiesData.forEach((itemTinhThanh) => {
                if (itemTinhThanh.ma === diaChiFormat.maTinhThanh) {
                  setValue('tinhThanh', itemTinhThanh.ma, {shouldValidate: true}); //set Tỉnh thành được chọn
                  setDistrictsDataDropDown([...itemTinhThanh.district]); //set list quận huyện của tỉnh thành
                  //nếu có quận huyện được chọn
                  if (diaChiFormat.maQuanHuyen) {
                    let listQuanHuyen = itemTinhThanh.district;
                    listQuanHuyen.forEach((itemQuanHuyen) => {
                      if (itemQuanHuyen.ma === diaChiFormat.maQuanHuyen) {
                        setValue('quanHuyen', itemQuanHuyen.ma, {shouldValidate: true}); //set quận huyện được chọn
                        setWardsDataDropDown([...itemQuanHuyen.ward]); //set list xã phường của quận huyện
                        if (diaChiFormat.maXaPhuong) setValue('xaPhuong', diaChiFormat.maXaPhuong, {shouldValidate: true});
                      }
                    });
                  }
                }
              });
            }
          } else FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa tồn tại địa chỉ tại địa điểm này. Vui lòng thử lại');
        }
      },
      (error) => logErrorTryCatch(error),
    );
  };

  // RENDER
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={'Thông tin địa điểm giám định'} // luồng khai báo bồi thường
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView resetScrollToCoords={{x: 0, y: 0}} contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
            <View style={styles.content} marginBottom={openDistrict || openWard ? 300 : 100}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="hienTruong"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    inputStyle={{color: colors.BLACK}}
                    isTouchableOpacity
                    isDropdown
                    editable={false}
                    isRequired
                    value={getTenHienThi(value, dataHienTruong)}
                    title="Hiện trường"
                    placeholder="Hiện trường xe"
                    onPress={() => refModalHienTruong.current.show()}
                  />
                )}
              />
              <View style={styles.doubleInputRowView}>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    rules={{
                      required: true,
                    }}
                    name="gioHenGiamDinh"
                    render={({field: {onChange, value}}) => (
                      <>
                        <TextInputOutlined
                          isRequired
                          inputStyle={{color: colors.BLACK}}
                          isTouchableOpacity
                          isDropdown
                          editable={false}
                          value={value ? moment(value).format('HH:mm') : ''}
                          title="Giờ hẹn giám định"
                          placeholder="Chọn giờ"
                          onPress={() => setToggleGioXR(true)}
                        />
                        {renderDateTimeComp(toggleGioXR, setToggleGioXR, (newValue) => setValue('gioHenGiamDinh', newValue), value ? value : new Date(), 'time', null, null, 0)}
                      </>
                    )}
                  />
                </View>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    rules={{
                      required: true,
                    }}
                    name="ngayHenGiamDinh"
                    render={({field: {onChange, value}}) => (
                      <>
                        <TextInputOutlined
                          isRequired
                          inputStyle={{color: colors.BLACK}}
                          isTouchableOpacity
                          isDropdown
                          editable={false}
                          value={value ? moment(value).format('DD/MM/YYYY') : ''}
                          title="Ngày hẹn giám định"
                          placeholder="Chọn ngày"
                          onPress={() => setToggleNgayXR(true)}
                        />
                        {renderDateTimeComp(toggleNgayXR, setToggleNgayXR, (newValue) => setValue('ngayHenGiamDinh', newValue), value ? value : new Date(), 'date', null, null, 0)}
                      </>
                    )}
                  />
                </View>
              </View>

              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="tinhThanh"
                render={({field: {onChange, value}}) => (
                  <View style={{zIndex: 8000, flexDirection: 'row', alignItems: 'flex-start'}}>
                    <DropdownPicker
                      disabled={isDisable}
                      title="Tỉnh thành"
                      zIndex={8000}
                      items={citiesData.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                      itemSelected={value}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      isOpen={openCity}
                      setOpen={setOpenCity}
                      placeholder="Chọn Tỉnh thành"
                      onOpen={() => closeDropdown(titleDropdownInput[0])}
                      // onChangeValue={onChangeValueDropdown}
                      inputErr={errors.tinhThanh && getErrMessage('tinhThanh', errors.tinhThanh.type)}
                      containerStyle={{marginBottom: openCity ? 300 : spacing.smaller, flex: 1, marginTop: 0}}
                      isRequired={true}
                      schema={{
                        label: 'ten',
                        value: 'ma',
                      }}
                    />
                    {!isDisable && (
                      <TouchableOpacity style={{marginTop: spacing.large, marginLeft: spacing.smaller}} onPress={onPressLayDiaChiHienTai} disabled={disableBtnLayDiaChi}>
                        {!disableBtnLayDiaChi ? <Icon.Entypo name="location" color={colors.PRIMARY} size={30} /> : <ActivityIndicator size="large" color={colors.PRIMARY} />}
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              />

              <Controller
                control={control}
                name="quanHuyen"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    disabled={isDisable}
                    title={hoSoSauNgayChuyenDoi ? titleDropdownInput[2] : titleDropdownInput[1]}
                    zIndex={6000}
                    items={districtsDataDropDown.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    isOpen={openDistrict}
                    setOpen={setOpenDistrict}
                    placeholder={`Chọn ${hoSoSauNgayChuyenDoi ? 'Xã phường' : 'Quận huyện'}`}
                    onOpen={() => closeDropdown(hoSoSauNgayChuyenDoi ? titleDropdownInput[2] : titleDropdownInput[1])}
                    onChangeValue={onChangeValueDropdown}
                    inputErr={errors.quanHuyen && getErrMessage('quanHuyen', errors.quanHuyen.type)}
                    isRequired={true}
                    schema={{
                      label: 'ten',
                      value: 'ma',
                    }}
                  />
                )}
              />

              {/* <Controller
                control={control}
                name="xaPhuong"
                rules={{
                  required: isRequiredFieldXaPhuong,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    isRequired={isRequiredFieldXaPhuong}
                    zIndex={5000}
                    title="Phường xã"
                    isOpen={openWard}
                    itemSelected={value}
                    disabled={isDisable}
                    setOpen={setOpenWard}
                    items={wardsDataDropDown}
                    placeholder="Chọn Xã phường"
                    onChangeValue={onChangeValueDropdown}
                    onOpen={() => closeDropdown(titleDropdownInput[2])}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    inputErr={errors.xaPhuong && getErrMessage('xaPhuong', errors.xaPhuong.type)}
                    schema={{
                      label: 'ten',
                      value: 'ma',
                    }}
                  />
                )}
              /> */}

              <Controller
                control={control}
                name="diaDiemGiamDinh"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    onFocus={onInputFocus}
                    onChangeText={onChange}
                    getRef={(ref) => (tieuDeRef = ref)}
                    title="Địa điểm thực hiện giám định"
                    placeholder="Nhập địa điểm thực hiện giám định"
                    // onSubmitEditing={() => diaDiemRef?.focus()}
                    error={errors.diaDiemGiamDinh && getErrMessage('diaDiemGiamDinh', errors.diaDiemGiamDinh.type)}
                  />
                )}
              />
            </View>
          </KeyboardAwareScrollView>
          <ModalSelectSimple
            baseData={dataHienTruong}
            setValue={(itemSelected) => setValue('hienTruong', itemSelected.value)}
            value={hienTruong}
            ref={refModalHienTruong}
            onBackPress={() => refModalHienTruong.current.hide()}
          />
          {/* <ModalChonMoiQuanHeVoiChuXe data={relationship} setValue={onChangeMoiQuanHe} ref={refModalMoiQuanHe} onBackPress={() => refModalMoiQuanHe.current.hide()} /> */}
        </SafeAreaView>
      }
      footer={
        <View style={styles.footerView}>
          <ButtonLinear title="Lưu" onPress={handleSubmit((data) => onPressRegisterSchedule('SAVE', data))} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />
          <ButtonLinear title="Lưu và chuyển" onPress={handleSubmit((data) => onPressRegisterSchedule('NEXT', data))} linearStyle={{marginLeft: spacing.small}} />
        </View>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  citiesData: state.cities.data,
  userInfo: state.user.data,
});
const mapDispatchToProps = {};
const KhaiBaoThongTinDiaDiemGiamDinhScreenConnect = connect(mapStateToProps, mapDispatchToProps)(KhaiBaoThongTinDiaDiemGiamDinhScreenComponent);
export const KhaiBaoThongTinDiaDiemGiamDinhScreen = memo(KhaiBaoThongTinDiaDiemGiamDinhScreenConnect, isEqual);
