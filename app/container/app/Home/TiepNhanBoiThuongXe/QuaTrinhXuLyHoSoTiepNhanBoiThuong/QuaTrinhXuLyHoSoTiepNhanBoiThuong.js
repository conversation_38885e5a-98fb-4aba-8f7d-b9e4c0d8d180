import {colors} from '@app/commons/Theme';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, View} from 'react-native';

const QuaTrinhXuLyHoSoTiepNhanBoiThuongScreenComponent = (props) => {
  console.log('QuaTrinhXuLyHoSoTiepNhanBoiThuongScreenComponent');
  const {route} = props;
  const {profileData} = route.params;
  const [dataQuaTrinhXuLy, setDataQuaTrinhXuLy] = useState([]);

  useEffect(() => {
    setDataQuaTrinhXuLy(profileData?.qtxl);
  }, [profileData]);

  const renderLichSuBoiThuongItem = ({item, index}) => {
    index = dataQuaTrinhXuLy.length - index;
    return (
      <View style={styles.resolveItemView}>
        <View flexDirection="row">
          <View style={styles.verticalLineStep}>
            <Text style={{color: colors.WHITE, fontSize: FontSize.size11}}>{index}</Text>
          </View>
          <View style={styles.titleView}>
            <Text style={styles.title}>{item.ten}</Text>
            <Text style={styles.date}>{item.ngay}</Text>
          </View>
        </View>
        <View style={styles.contentColumn}>
          <Text style={styles.subLabel}>{item.nd}</Text>
        </View>
      </View>
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle="Quá trình giải quyết"
      renderView={
        <View style={styles.container}>
          <FlatList style={{paddingVertical: spacing.small}} data={dataQuaTrinhXuLy} renderItem={renderLichSuBoiThuongItem} keyExtractor={(e, i) => i.toString()} ListEmptyComponent={<Empty />} />
        </View>
      }
    />
  );
};

export const QuaTrinhXuLyHoSoTiepNhanBoiThuongScreen = memo(QuaTrinhXuLyHoSoTiepNhanBoiThuongScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  resolveItemView: {
    marginHorizontal: scale(spacing.smaller),
  },
  verticalLineStep: {
    width: 22,
    height: 22,
    borderWidth: 1,
    borderRadius: 20,
    alignItems: 'center',
    borderColor: 'white',
    justifyContent: 'center',
    backgroundColor: colors.PRIMARY,
  },
  contentColumn: {
    borderLeftWidth: 1,
    borderColor: colors.GRAY,
    marginLeft: scale(spacing.small),
    marginBottom: vScale(spacing.medium),
  },
  title: {
    marginBottom: 4,
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },

  date: {
    fontWeight: '400',
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
  },
  subLabel: {
    fontWeight: '400',
    color: colors.GRAY6,
    marginLeft: scale(20),
    fontSize: FontSize.size14,
  },

  titleView: {
    flex: 1,
    marginLeft: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: vScale(spacing.small),
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: vScale(spacing.tiny),
  },
});
