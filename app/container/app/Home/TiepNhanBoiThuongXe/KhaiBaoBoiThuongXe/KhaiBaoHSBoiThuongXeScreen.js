import {DATA_NGHIEP_VU_XCG, FORMAT_DATE_TIME, NGHIEP_VU, NGUON_TB, REGUlAR_EXPRESSION, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Icon, ScreenComponent, StepIndicatorComp, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {memo, useEffect, useMemo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {<PERSON><PERSON>, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';
import {BoiThuongXeBuoc1, BoiThuongXeBuoc2} from './Components';
import styles from './KhaiBaoHSBoiThuongXeStyles';

const LOAI_HINH_NGHIEP_VU = [
  {
    label: 'Vật chất xe',
    value: 'XE',
  },
  {
    label: 'Về tài sản',
    value: 'TAI_SAN',
  },
  {
    label: 'Về người',
    value: 'NGUOI',
  },
];

const DATA_LOAI_HINH = [
  {label: 'Tự nguyện', value: 'TN'},
  {label: 'Bắt buộc', value: 'BB'},
];

const KhaiBaoBoiThuongXeBuoc1ScreenComponent = (props) => {
  console.log('KhaiBaoBoiThuongXeBuoc1ScreenComponent');
  const route = useRoute();
  const {page, info, title} = route?.params;
  const userInfo = useSelector(selectUser);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(page || 0);

  const [selectedItemHoSo, setSelectedItemHoSo] = useState(null);

  //step 1
  const [chiTietHS, setChiTietHS] = useState(info);
  const [outValueGCN, setOutValueGCN] = useState(info);
  const [dataHD, setDataHD] = useState([]);
  const [inputErrLienHe, setInputErrLienHe] = useState(['', '', '', '', '', '', '', '', '', '', '']);
  const [userNoticeRelationshipSelected, setUserNoticeRelationshipSelected] = useState({
    label: 'Chủ xe',
    ma: 'QH.0001',
    value: 'QH.0001',
    Id: 'QH.0001',
  }); // Quan hệ với chủ xe
  const [tenNguoiThongBao, setTenNguoiThongBao] = useState('');
  const [dienThoaiNguoiThongBao, setDienThoaiNguoiThongBao] = useState('');
  const [emailNguoiThongBao, setEmailNguoiThongBao] = useState('');

  /* THÔNG TIN NGƯỜI LIÊN HỆ */
  const [checkboxLaNguoiThongBao, setCheckboxLaNguoiThongBao] = useState(true);
  const [defaultNguoiThongBao, setDefaultNguoiThongBao] = useState(false);
  const [tenNguoiLienHe, setTenNguoiLienHe] = useState(''); // Họ tên
  const [userContactRelationshipSelected, setUserContactRelationshipSelected] = useState({
    label: 'Chủ xe',
    ma: 'QH.0001',
    value: 'QH.0001',
    Id: 'QH.0001',
  }); // Quan hệ với chủ xe
  const [dienThoaiNguoiLienHe, setDienThoaiNguoiLienHe] = useState(''); // SDT
  const [emailNguoiLienHe, setEmailNguoiLienHe] = useState(''); // Email

  const [moiQuanHeNgThongBao, setMoiQuanHeNgThongBao] = useState('');
  const currentDate = new Date();

  const [luongXly, setLuongXly] = useState('');
  // const [hienTruong, setHienTruong] = useState({ten: 'Xe không ở hiện trường', ma: 'K'});

  const [loaiHinhSelected, setLoaiHinhSelected] = useState('TN');
  const [dataLHNV, setDataLHNV] = useState([]);
  const [loaiHinhNVSelected, setLoaiHinhNVSelected] = useState('');

  const [bienSoXe, setBienSoXe] = useState('');
  const [tenChuXe, setTenChuXe] = useState('');
  const [showForm, setShowForm] = useState(false);

  // const [dataLuongXly, setDataLuongXly] = useState([]);
  const [dataLuongXuLyTheoGCN, setDataLuongXuLyTheoGCN] = useState([]);
  const [nghiepVuSelected, setNghiepVuSelected] = useState(DATA_NGHIEP_VU_XCG[1].value);

  const [gioThongBao, setGioThongBao] = useState(null);
  const [toggleGioThongBao, setToggleGioThongBao] = useState(false);

  const [ngayThongBao, setNgayThongBao] = useState(null);
  const [toggleNgayThongBao, setToggleNgayThongBao] = useState(false);

  useEffect(() => {
    if (nghiepVuSelected) getLoaiHinhNV({nv: nghiepVuSelected});
  }, [nghiepVuSelected]);

  useEffect(() => {
    setLoaiHinhNVSelected('');
  }, [loaiHinhSelected]);

  const getChiTietGcn = async (i) => {
    return new Promise(async (resolve, reject) => {
      setDialogLoading(true);
      try {
        const params = {
          so_id_hd: selectedItemHoSo?.so_id_hdong,
          so_id_dt: selectedItemHoSo?.so_id_gcn,
          pm: 'GD',
          nv: selectedItemHoSo?.nv ?? selectedItemHoSo.nghiep_vu,
          ma_chi_nhanh: selectedItemHoSo?.ma_chi_nhanh,
        };
        let response = await PartnerEndpoint.xemGCNXeDoiTac(AxiosConfig.ACTION_CODE.LAY_CHI_TIET_GCN_XE, params);
        setDialogLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') {
          resolve(false);
          return;
        }
        setChiTietHS(response.data_info.ho_so);
        setOutValueGCN(response.out_value);
        resolve(true);
      } catch (error) {
        setDialogLoading(false);
        Alert.alert('Thông báo', error.message);
        resolve(false);
      }
    });
  };

  const getLoaiHinhNV = async (params) => {
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_LOAI_HINH_NV, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info;
      data.map((e, index) => {
        data[index].label = e.ten;
        data[index].value = e.ma;
      });
      setDataLHNV(data);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDataLuongXuLy = async () => {
    setDialogLoading(true);
    const params = {};
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DATA_LUONG_XU_LY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let luongXuLyRespone = response.data_info;
      luongXuLyRespone.map((e, i) => {
        luongXuLyRespone[i].label = e.ten;
        luongXuLyRespone[i].value = e.ma;
      });
      setDataLuongXuLyTheoGCN([...luongXuLyRespone]);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDataLuongXuLyTheoGCN = async (params) => {
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DATA_LUONG_XU_LY_THEO_HD, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        return;
      }
      let luongXuLyRespone = response.data_info;
      luongXuLyRespone.map((e, i) => {
        luongXuLyRespone[i].label = e.ten;
        luongXuLyRespone[i].value = e.ma;
      });
      if (luongXuLyRespone.length === 1) setLuongXly(luongXuLyRespone[0].ma);
      setDataLuongXuLyTheoGCN([...luongXuLyRespone]);
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lấy data luồng xử lý thành công ' + luongXuLyRespone.length, 'success');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onCapNhatHoso = async () => {
    try {
      let params = {
        so_id: '', //rỗng
        so_tn: '', //rỗng
        ma_doi_tac: selectedItemHoSo?.ma_doi_tac || userInfo.nguoi_dung.ma_doi_tac,
        nv: selectedItemHoSo ? selectedItemHoSo?.nghiep_vu : showForm ? nghiepVuSelected : '',
        so_id_hd: showForm ? 0 : outValueGCN?.so_id_hd,
        so_id_dt: showForm ? 0 : outValueGCN?.so_id_dt,
        loai: '', //rỗng
        lh_nv: loaiHinhNVSelected,
        ten: showForm ? tenChuXe : '',
        bien_xe: showForm ? bienSoXe : '',
        nv_xly: luongXly,
        nguon_tb: NGUON_TB.APP_MOBILE,
        hien_truong: '',
        gio_tb: moment(gioThongBao).format('HH:mm'),
        ngay_tb: moment(ngayThongBao).format(FORMAT_DATE_TIME.API_DATE_FORMAT),
        nguoi_tb: tenNguoiThongBao,
        moi_qh_tb: moiQuanHeNgThongBao,
        dthoai_tb: dienThoaiNguoiThongBao,
        email_tb: emailNguoiThongBao || '',
        nguoi_lhe: tenNguoiLienHe,
        moi_qh_lhe: '',
        dthoai_lhe: dienThoaiNguoiLienHe,
        email_lhe: emailNguoiLienHe || '',
        noi_dung: '', //rỗng
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.MO_HO_SO_TIEP_NHAN_BOI_THUONG_XE, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Mở hồ sơ tiếp nhận bồi thường thành công', 'success');
      NavigationUtil.navigate(SCREEN_ROUTER_APP.CHI_TIET_HO_SO_BOI_THUONG_XE, {
        id: response.out_value.so_id,
        ma_doi_tac: response.out_value?.ma_doi_tac,
        prevScreen: SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1,
      });
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressNextStep = async () => {
    let haveErr = false;

    let inputErrLienHeTmp = inputErrLienHe;
    if (currentPage === 1) {
      // setDisableKhaiBao(false);
      inputErrLienHeTmp[0] = '';
      if (tenNguoiThongBao !== null && !tenNguoiThongBao?.trim()) {
        inputErrLienHeTmp[0] = 'Vui lòng nhập Họ tên';
        haveErr = true;
      }
      if (!dienThoaiNguoiThongBao || dienThoaiNguoiThongBao === '') {
        inputErrLienHeTmp[1] = 'Vui lòng nhập Số điện thoại';
        haveErr = true;
      } else if (dienThoaiNguoiThongBao !== '' && !REGUlAR_EXPRESSION.REG_PHONE.test(dienThoaiNguoiThongBao)) {
        inputErrLienHeTmp[1] = 'Số điện thoại sai định dạng';
        haveErr = true;
      }
      if (!tenNguoiLienHe?.trim()) {
        inputErrLienHeTmp[3] = 'Vui lòng nhập Họ tên';
        haveErr = true;
      }
      if (!dienThoaiNguoiLienHe?.trim()) {
        inputErrLienHeTmp[4] = 'Vui lòng nhập Số điện thoại';
        haveErr = true;
      } else if (dienThoaiNguoiLienHe !== '' && !REGUlAR_EXPRESSION.REG_PHONE.test(dienThoaiNguoiThongBao)) {
        inputErrLienHeTmp[4] = 'Số điện thoại sai định dạng';
        haveErr = true;
      }
      if (emailNguoiThongBao && !REGUlAR_EXPRESSION.REG_EMAIL.test(emailNguoiThongBao)) {
        inputErrLienHeTmp[2] = 'Email sai định dạng';
        haveErr = true;
      }
      if (emailNguoiLienHe && !REGUlAR_EXPRESSION.REG_EMAIL.test(emailNguoiLienHe)) {
        inputErrLienHeTmp[6] = 'Email sai định dạng';
        haveErr = true;
      }
      if (!luongXly) {
        inputErrLienHeTmp[7] = 'Vui lòng chọn luồng xử ly';
        haveErr = true;
      }
      if (!moiQuanHeNgThongBao) {
        inputErrLienHeTmp[8] = 'Vui lòng chọn mối quan hệ với chủ xe';
        haveErr = true;
      }
      console.log('ngayThongBao', ngayThongBao);
      if (!gioThongBao) {
        inputErrLienHeTmp[9] = 'Thông tin bắt buộc';
        haveErr = true;
      }
      if (!ngayThongBao) {
        inputErrLienHeTmp[10] = 'Thông tin bắt buộc';
        haveErr = true;
      }

      setInputErrLienHe([...inputErrLienHeTmp]);
      if (haveErr) return;
      setDialogLoading(true);
      onCapNhatHoso();
    } else {
      if (!showForm) {
        let response = await getChiTietGcn();
        if (!response) return;
        let params = {
          so_id_hd: selectedItemHoSo.so_id_hdong,
          so_id_gcn: selectedItemHoSo.so_id_gcn,
        };
        await getDataLuongXuLyTheoGCN(params);
        setCurrentPage(currentPage + 1);
      } else {
        await getDataLuongXuLy();
        setCurrentPage(currentPage + 1);
      }
    }
  };
  const onPressBack = () => {
    setCurrentPage(currentPage - 1);
  };

  //Onchange value người thông báo
  //Tên
  const onChangeTenNguoiThongBao = (value) => {
    let inputErrLienHeTmp = inputErrLienHe;
    inputErrLienHeTmp[0] = '';
    if (value != null && !value?.trim()) {
      inputErrLienHeTmp[0] = 'Vui lòng nhập Họ tên';
    }
    setTenNguoiThongBao(value);
    //nếu NGƯỜI THÔNG BÁO LÀ NGƯỜI LIÊN HỆ
    if (checkboxLaNguoiThongBao) onChangeTenNguoiLienHe(value);
    setInputErrLienHe([...inputErrLienHeTmp]);
  };

  //Điện thoại
  const onChangeDienThoaiNguoiThongBao = (value) => {
    let inputErrLienHeTmp = inputErrLienHe;
    inputErrLienHeTmp[1] = '';
    if (value != null && !value?.trim()) {
      inputErrLienHeTmp[1] = 'Vui lòng nhập Số điện thoại';
    } else if (value != null && !REGUlAR_EXPRESSION.REG_PHONE.test(value)) {
      inputErrLienHeTmp[1] = 'Số điện thoại sai định dạng';
    }
    setInputErrLienHe([...inputErrLienHeTmp]);
    //nếu NGƯỜI THÔNG BÁO LÀ NGƯỜI LIÊN HỆ
    if (checkboxLaNguoiThongBao) onChangeDienThoaiNguoiLienHe(value);
    setDienThoaiNguoiThongBao(value);
  };
  //Email
  const onChangeEmailNguoiThongBao = (value) => {
    let inputErrLienHeTmp = inputErrLienHe;
    inputErrLienHeTmp[2] = '';
    if (value != null && !value?.trim()) {
      setInputErrLienHe([...inputErrLienHeTmp]);
    } else if (value != null && !REGUlAR_EXPRESSION.REG_EMAIL.test(value)) {
      inputErrLienHeTmp[2] = 'Email sai định dạng';
    }
    setInputErrLienHe([...inputErrLienHeTmp]);
    //nếu NGƯỜI THÔNG BÁO LÀ NGƯỜI LIÊN HỆ
    if (checkboxLaNguoiThongBao) onChangeEmailNguoiLienHe(value);
    setEmailNguoiThongBao(value);
  };

  // ONCHANGE INPUT NGƯỜI LIÊN HỆ
  // Họ tên
  const onChangeTenNguoiLienHe = (value) => {
    let inputErrLienHeTmp = inputErrLienHe;
    inputErrLienHeTmp[3] = '';
    if (value != null && !value.trim()) {
      inputErrLienHeTmp[3] = 'Vui lòng nhập Họ tên';
    }
    setInputErrLienHe([...inputErrLienHeTmp]);
    setTenNguoiLienHe(value);
  };
  //Điện thoại
  const onChangeDienThoaiNguoiLienHe = (value) => {
    let inputErrLienHeTmp = inputErrLienHe;
    inputErrLienHeTmp[4] = '';
    if (value != null && !value?.trim()) {
      inputErrLienHeTmp[4] = 'Vui lòng nhập Số điện thoại';
    } else if (value != null && !REGUlAR_EXPRESSION.REG_PHONE.test(value)) {
      inputErrLienHeTmp[4] = 'Số điện thoại sai định dạng';
    }
    setInputErrLienHe([...inputErrLienHeTmp]);
    setDienThoaiNguoiLienHe(value);
  };
  //Email
  const onChangeEmailNguoiLienHe = (value) => {
    let inputErrLienHeTmp = inputErrLienHe;
    inputErrLienHeTmp[5] = '';
    if (value != null && !value?.trim()) {
      setInputErrLienHe([...inputErrLienHeTmp]);
    } else if (value != null && !REGUlAR_EXPRESSION.REG_EMAIL.test(value)) {
      inputErrLienHeTmp[5] = 'Email sai định dạng';
    }
    setInputErrLienHe([...inputErrLienHeTmp]);
    setEmailNguoiLienHe(value);
  };

  //onchange luồng xử lý
  const onChangeLuongXuLy = (value) => {
    if (value) {
      let inputErrLienHeTmp = inputErrLienHe;
      inputErrLienHeTmp[7] = '';
      setLuongXly(value);
    }
  };

  //onchange mối quan hệ với chủ sở hữu xe
  const onChangeMoiQuanHeVoiChuXe = (value) => {
    if (value) {
      let inputErrLienHeTmp = inputErrLienHe;
      inputErrLienHeTmp[8] = '';
      setMoiQuanHeNgThongBao(value);
      setInputErrLienHe([...inputErrLienHeTmp]);
      if (value === 'QH.0001') {
        onChangeTenNguoiThongBao(chiTietHS?.ten_chu_xe);
        onChangeDienThoaiNguoiThongBao(chiTietHS?.dthoai);
        onChangeEmailNguoiThongBao(chiTietHS?.email);
        setDefaultNguoiThongBao(true);
      } else setDefaultNguoiThongBao(false);
    }
  };

  //onchange checkbox value
  const onPressCheckbox = () => {
    setCheckboxLaNguoiThongBao(!checkboxLaNguoiThongBao);
    onChangeTenNguoiLienHe(tenNguoiThongBao);
    onChangeDienThoaiNguoiLienHe(dienThoaiNguoiThongBao);
    onChangeEmailNguoiLienHe(emailNguoiThongBao);
    setUserContactRelationshipSelected(userNoticeRelationshipSelected);
  };

  const onSetDataHopDong = (data) => {
    setDataHD(data);
    if (data.length === 1) setSelectedItemHoSo(data[0]);
  };

  const memoDisableBtnNext = useMemo(() => {
    if (currentPage === 0 && !showForm) return selectedItemHoSo === null;
    if (currentPage === 0 && showForm) return loaiHinhSelected === '' || loaiHinhNVSelected === '' || tenChuXe === '' || bienSoXe === '';
  }, [selectedItemHoSo, currentPage, showForm, loaiHinhSelected, loaiHinhNVSelected, tenChuXe, bienSoXe]);

  //render

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={title ? title : 'Khai báo hồ sơ tổn thất'}
      renderView={
        <SafeAreaView style={styles.container}>
          <StepIndicatorComp currentPosition={currentPage} labels={['Thông tin hợp đồng', 'Người liên hệ']} stepCount={2} />
          {currentPage === 0 && (
            <BoiThuongXeBuoc1
              data={dataHD}
              profileInfo={info}
              bienSoXe={bienSoXe}
              tenChuXe={tenChuXe}
              setData={onSetDataHopDong}
              showForm={showForm}
              setShowForm={setShowForm}
              setBienSoXe={setBienSoXe}
              setTenChuXe={setTenChuXe}
              dataLHNV={dataLHNV}
              dataLoaiHinh={DATA_LOAI_HINH}
              loaiHinhSelected={loaiHinhSelected}
              loaiHinhNVSelected={loaiHinhNVSelected}
              selectedItemHoSo={selectedItemHoSo}
              setSelectedItemHoSo={setSelectedItemHoSo}
              setLoaiHinhNVSelected={setLoaiHinhNVSelected}
              setLoaiHinhSelected={setLoaiHinhSelected}
              setNghiepVuSelected={setNghiepVuSelected}
              nghiepVuSelected={nghiepVuSelected}
            />
          )}

          {currentPage === 1 && (
            <BoiThuongXeBuoc2
              showForm={showForm}
              setInputErrLienHe={setInputErrLienHe}
              //giờ thông báo
              gioThongBao={gioThongBao}
              setGioThongBao={setGioThongBao}
              toggleGioThongBao={toggleGioThongBao}
              setToggleGioThongBao={setToggleGioThongBao}
              //ngày thông báo
              ngayThongBao={ngayThongBao}
              setNgayThongBao={setNgayThongBao}
              toggleNgayThongBao={toggleNgayThongBao}
              setToggleNgayThongBao={setToggleNgayThongBao}
              //
              selectedItemHoSo={selectedItemHoSo}
              key={0}
              // Thông tin xe
              inputErrLienHe={inputErrLienHe}
              // Thông tin người thông báo
              userNoticeName={tenNguoiThongBao}
              userNoticePhone={dienThoaiNguoiThongBao}
              userNoticeEmail={emailNguoiThongBao}
              setUserNoticeName={onChangeTenNguoiThongBao}
              checkboxIsUserNotice={checkboxLaNguoiThongBao}
              setUserNoticePhone={onChangeDienThoaiNguoiThongBao}
              setUserNoticeEmail={onChangeEmailNguoiThongBao}
              userNoticeRelationshipSelected={userNoticeRelationshipSelected}
              setUserNoticeRelationshipSelected={setUserNoticeRelationshipSelected}
              //Thông tin người liên hệ
              luongXly={luongXly}
              chiTietHS={chiTietHS}
              // hienTruong={hienTruong}
              // dataLuongXly={dataLuongXly}

              dataLuongXuLyTheoGCN={dataLuongXuLyTheoGCN.length > 0 ? dataLuongXuLyTheoGCN : LOAI_HINH_NGHIEP_VU}
              // dataLuongXuLyTheoGCN={LOAI_HINH_NGHIEP_VU}

              // setHienTruong={setHienTruong}
              setLuongXly={onChangeLuongXuLy}
              onPressCheckbox={onPressCheckbox}
              userContactName={tenNguoiLienHe}
              userContactPhone={dienThoaiNguoiLienHe}
              userContactEmail={emailNguoiLienHe}
              defaultUserNotice={defaultNguoiThongBao}
              moiQuanHeNgThongBao={moiQuanHeNgThongBao}
              setUserContactName={onChangeTenNguoiLienHe}
              setUserContactEmail={onChangeEmailNguoiLienHe}
              setUserContactPhone={onChangeDienThoaiNguoiLienHe}
              setMoiQuanHeNgThongBao={onChangeMoiQuanHeVoiChuXe}
              userContactRelationshipSelected={userContactRelationshipSelected}
              setUserContactRelationshipSelected={setUserContactRelationshipSelected}

              // onSelectedPickerModal={onSelectedPickerModal}
            />
          )}

          {title !== undefined && page !== 0 ? (
            <ButtonLinear title="Lưu" onPress={onCapNhatHoso} linearStyle={styles.saveBtn} />
          ) : (
            <View style={styles.footerView}>
              <TouchableOpacity activeOpacity={0.5} onPress={onPressBack} style={styles.btnBack}>
                {currentPage > 0 && (
                  <>
                    <View style={styles.iconLeftBtnView}>
                      <Icon.Ionicons name="arrow-back" size={25} color={colors.WHITE} style={styles.iconLeftBtn} />
                    </View>
                    <Text style={styles.txtBtnBottom}>Trước</Text>
                  </>
                )}
              </TouchableOpacity>
              {/* NÚT TIẾP TỤC - HOÀN THÀNH   */}

              <TouchableOpacity
                disabled={memoDisableBtnNext}
                activeOpacity={0.5}
                onPress={onPressNextStep}
                style={[styles.btnNext, {backgroundColor: memoDisableBtnNext ? colors.GRAY3 : colors.PRIMARY_08}]}>
                {<Text style={styles.txtBtnBottom}>{currentPage == 1 ? 'Khai báo' : 'Tiếp'}</Text>}
                <View style={[styles.iconRightBtnView, {backgroundColor: memoDisableBtnNext ? colors.GRAY10 : colors.PRIMARY_DARK_08}]}>
                  <Icon.Ionicons name={currentPage == 1 ? 'checkmark-sharp' : 'arrow-forward'} size={25} style={styles.iconRightBtn} color={colors.WHITE} />
                </View>
              </TouchableOpacity>
            </View>
          )}
        </SafeAreaView>
      }
    />
  );
};

export const KhaiBaoBoiThuongXeBuoc1Screen = memo(KhaiBaoBoiThuongXeBuoc1ScreenComponent, isEqual);
