import {colors} from '@app/commons/Theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  stepIndicator: {
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
  },

  btnNext: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: colors.PRIMARY_08,
    flexDirection: 'row',
    borderRadius: 30,
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  txtBtnBottom: {
    flex: 1,
    textAlign: 'center',
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  footerView: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY2,
    justifyContent: 'space-around',
  },
  btnBack: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: colors.PRIMARY_08,
    flexDirection: 'row',
    borderRadius: 30,
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconLeftBtn: {
    padding: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconLeftBtnView: {
    backgroundColor: colors.PRIMARY_DARK_08,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconRightBtn: {
    padding: 10,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconRightBtnView: {
    backgroundColor: colors.PRIMARY_DARK_08,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  saveBtn: {
    paddingTop: 10,
    marginHorizontal: 16,
  },
});
