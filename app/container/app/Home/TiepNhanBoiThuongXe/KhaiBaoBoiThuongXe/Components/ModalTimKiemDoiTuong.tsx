import {DATA_NGHIEP_VU_XCG} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {BenThuBaEndPoint, PartnerEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon, DialogLoading, Icon, ModalQuetQR, ModalSelectSimple, Text} from '@component';
import moment from 'moment';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Keyboard, SafeAreaView, StyleProp, StyleSheet, TouchableOpacity, View, ViewStyle} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
import {titleInput} from './Constant';
import {ModalChiNhanh} from './ModalChiNhanh';
import {ChiNhanHBaoHiemProps, ListChiNhanHBaoHiemProps, ModalTimKiemDoiTuongProps} from './ModalTimKiemDoiTuong.props';
import {replaceAllISO8859} from '@app/utils/string';
import {logErrorTryCatch} from '@app/utils';

const MA_CHI_NHANH_KHONG_AUTO_FILL = ['TCT', 'KV01', 'KV02'];
const ModalTimKiemDoiTuongComponent = forwardRef(({onBackPress, setData}: ModalTimKiemDoiTuongProps, ref) => {
  const userInfo = useSelector(selectUser);
  // const insect = useSafeAreaInsets();
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [disalogLoading, setDialogLoading] = useState<boolean>(false);
  const chiNhanhBaoHiemDangCay = useSelector<ChiNhanHBaoHiemProps[]>(selectChiNhanhBaoHiemDangCay);

  // toggle modal
  const [toggleNgayDau, setToggleNgayDau] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [isValid, setIsValid] = useState<boolean>(false);
  const [listChiNhanh, setListChiNhanh] = useState<ListChiNhanHBaoHiemProps[]>([]);
  const userKhongCanCheckDieuKien = MA_CHI_NHANH_KHONG_AUTO_FILL.includes(userInfo?.nguoi_dung?.ma_chi_nhanh);

  let refModalNghiepVu = useRef(null);
  let refModalChiNhanh = useRef(null);
  let refSoGCN = useRef(null);
  let refBienSoXe = useRef(null);
  let refSoKhung = useRef(null);
  let refSoMay = useRef(null);
  let refModalQuetQR = useRef(null);

  useImperativeHandle(
    ref,
    () => ({
      show: (): void => setIsVisible(true),
      hide: (): void => setIsVisible(false),
    }),
    [],
  );

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    reset,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      ngay_xr: moment().toDate(),
      so_hdong: '',
      so_gcn: '',
      so_may: '',
      bien_so_xe: __DEV__ ? '' : '',
      so_khung: '',
      ten_kh: '',
      cmt_kh: '',
      mst_kh: '',
      nv: 'XE',
      ma_chi_nhanh: !MA_CHI_NHANH_KHONG_AUTO_FILL.includes(userInfo?.nguoi_dung?.ma_chi_nhanh) ? userInfo?.nguoi_dung?.ma_chi_nhanh : '',
    },
    mode: 'onChange',
  });

  const maChiNhanh = watch('ma_chi_nhanh');

  const getData = async (params) => {
    if (!params.so_hdong && !params.so_gcn && !params.so_may && !params.bien_so_xe && !params.so_khung) return Alert.alert('Thông báo', 'Nhập ít nhất 1 trường thông tin xe để tìm kiếm', 'warning');
    if (params.ngay_xr) params.ngay_xr = moment(params.ngay_xr).format('DD/MM/YYYY');
    params.ma_doi_tac = userInfo.nguoi_dung.ma_doi_tac;
    setLoading(true);
    try {
      let response = await PartnerEndpoint.searchGCN(axiosConfig.ACTION_CODE.TIM_KIEM_GCN_XE_DOI_TAC, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.hd;
      setData(data);
      if (data.length <= 0) return Alert.alert('Thông báo', 'Không tìm thấy kết quả phù hợp!');
      else onBackPress && onBackPress();
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const initChiNhanhBaoHiem = (): void => {
    if (listChiNhanh.length > 0) return;
    let chiNhanhBaoHiemQuanLySorted: ChiNhanHBaoHiemProps[] = [...chiNhanhBaoHiemDangCay];
    chiNhanhBaoHiemQuanLySorted = chiNhanhBaoHiemQuanLySorted.sort((a, b) => a.stt - b.stt);
    let listChiNhanhMap = chiNhanhBaoHiemQuanLySorted.map((item) => {
      return {label: item.ten_chi_nhanh, value: item.ma_chi_nhanh};
    });
    setListChiNhanh(listChiNhanhMap);
  };

  const resetField = (): void => reset({}, {keepDefaultValues: true, keepDirty: false});

  const getTenHienThi = (value: string, data: ListChiNhanHBaoHiemProps[]): string => data.find((e) => value === e.value)?.label || '';

  const getDieuKienKhongBatBuocNhap2TruongDuLieu = (ma_chi_nhanh = userInfo?.nguoi_dung?.ma_chi_nhanh) => {
    let value = false;
    if (userKhongCanCheckDieuKien) value = true;
    else if (!userKhongCanCheckDieuKien && userInfo?.nguoi_dung?.ma_chi_nhanh === ma_chi_nhanh) value = true;
    else value = false;
    return value;
  };

  const getValidValue = (ma_chi_nhanh) => {
    let totalCount = 0;
    if (getValues('bien_so_xe') !== '') totalCount += 1;
    if (getValues('so_gcn') !== '') totalCount += 1;
    if (getValues('so_khung') !== '') totalCount += 1;
    if (getValues('so_may') !== '') totalCount += 1;
    if (getValues('so_hdong') !== '') totalCount += 1;
    if ((!getDieuKienKhongBatBuocNhap2TruongDuLieu(ma_chi_nhanh) && totalCount >= 2) || (getDieuKienKhongBatBuocNhap2TruongDuLieu(ma_chi_nhanh) && totalCount >= 1)) setIsValid(true);
    else setIsValid(false);
  };

  const onChangeText = async (field, value) => {
    setValue(field, value);
    getValidValue(maChiNhanh);
  };

  const handleQRData = async (data) => {
    try {
      if (!data.includes('https://hotro.pjico.com.vn/')) {
        return setTimeout(() => {
          Alert.alert('Thông báo', 'Mã QR không phù hợp với Giấy chứng nhận');
        }, 350);
      }
      setDialogLoading(true);
      let response = await BenThuBaEndPoint.readUrlQrGcnPJICO(data);
      setDialogLoading(false);
      let arrInputResult = response.split('input-result">');
      let arrLabel = response.split('control-label">');
      arrInputResult = arrInputResult.map((item) => item.split('</label>')[0]);
      arrLabel = arrLabel.map((item) => item.split('</label>')[0]).filter((item) => item !== 'Tình trạng');
      if (arrInputResult.length <= arrLabel.length) {
        for (let i = 0; i < arrInputResult.length; i++) {
          if (arrLabel[i] === 'Số đơn BH') {
            let arrSoGCN = arrInputResult[i].split('/');
            setValue('ma_chi_nhanh', arrSoGCN[1]);
            let soGCN = '';
            /*xử lý trường hợp nối dài số GCN + số Sửa đổi bổ sung : P-23/HPH/XCG/5106/001316E-23/HPH/XCG/5106/001316-01
            số GCN dúng chỉ đến P-23/HPH/XCG/5106/001316
            */
            if (arrSoGCN.length > 5) {
              let indexSlice = 0;
              for (let j = 0; j < arrSoGCN[4].length; j++) {
                if (arrSoGCN[4][j] > '9') {
                  indexSlice = j;
                  break;
                }
              }
              soGCN = arrSoGCN.slice(0, 4).join('/') + '/' + arrSoGCN[4].slice(0, indexSlice);
            } else soGCN = arrInputResult[i];
            onChangeText('so_gcn', soGCN);
          } else if (arrLabel[i] === 'Tên khách hàng') onChangeText('ten_kh', replaceAllISO8859(arrInputResult[i]).toUpperCase());
          else if (arrLabel[i] === 'Biển kiểm soát') onChangeText('bien_so_xe', arrInputResult[i].length > 2 ? arrInputResult[i] : '');
          else if (arrLabel[i] === 'Số khung') onChangeText('so_khung', arrInputResult[i].length > 2 ? arrInputResult[i] : '');
          else if (arrLabel[i] === 'Số máy') onChangeText('so_may', arrInputResult[i].length > 2 ? arrInputResult[i] : '');
        }
      }
    } catch (error) {
      logErrorTryCatch({code: 'QUET_QR_GCN_ERROR', message: data});
      setTimeout(() => {
        Alert.alert('Thông báo', error.message);
      }, 350);
    }
  };

  /**RENDER  */
  const renderDateTimeComp = (
    toggleDateTime: boolean,
    setToggleDateTime: (boolean) => {},
    setDateTime: (newDate: Date) => {},
    date: Date,
    mode: 'date' | 'time' | 'datetime',
    minDate: Date,
    maxDate: Date,
    type,
  ) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        setToggleDateTime(false);
        setDateTime(dateSelected);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderFormInput = () => {
    return (
      <View style={styles.formInput}>
        {/* Ngày đầu/Ngày cuối */}
        <Controller
          control={control}
          name="ngay_xr"
          render={({field: {onChange, value}}) => (
            <>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                isDateTimeField
                placeholder={titleInput[0]}
                styleContainer={styles.containerInput as StyleProp<ViewStyle>}
                onPressInput={() => {
                  Keyboard.dismiss();
                  setToggleNgayDau(true);
                }}
                value={value ? moment(value).format('DD/MM/YYYY') : ''}
              />
              {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, (newValue) => onChange(newValue), value ? value : new Date(), 'date', null, new Date(), 0)}
            </>
          )}
        />

        <Controller
          control={control}
          name="ma_chi_nhanh"
          render={({field: {value}}) => (
            <CommonOutlinedTextFieldWithIcon
              isPickerModal={true}
              placeholder="Chi nhánh"
              value={listChiNhanh.length > 0 ? getTenHienThi(value, listChiNhanh) : ''}
              onPressInput={() => {
                Keyboard.dismiss();
                refModalChiNhanh.current?.show();
              }}
              styleContainer={[styles.containerInput as StyleProp<ViewStyle>]}
            />
          )}
        />

        <Controller
          control={control}
          name="nv"
          render={({field: {value}}) => (
            <CommonOutlinedTextFieldWithIcon
              isRequired
              isPickerModal={true}
              placeholder="Nghiệp vụ"
              value={getTenHienThi(value, DATA_NGHIEP_VU_XCG)}
              onPressInput={() => {
                Keyboard.dismiss();
                refModalNghiepVu.current?.show();
              }}
              styleContainer={[styles.containerInput as StyleProp<ViewStyle>]}
            />
          )}
        />
        <Controller
          control={control}
          name="so_hdong"
          render={({field: {onChange, value, ref}}) => (
            <CommonOutlinedTextFieldWithIcon
              getRef={(refInput) => (ref = refInput)}
              value={value}
              placeholder={titleInput[1]}
              styleContainer={[styles.containerInput]}
              onChangeText={(text) => onChangeText('so_hdong', text)}
              returnKeyType="next"
              onSubmit={() => refSoGCN.current?.focus()}
              blurOnSubmit={false}
              onBlur={() => value !== '' && onChange(value.toUpperCase())}
              autoCapitalize="characters"
              clear
              clearTextInput={() => onChange('')}
            />
          )}
        />
        <Controller
          control={control}
          name="so_gcn"
          render={({field: {onChange, value}}) => (
            <CommonOutlinedTextFieldWithIcon
              refInput={refSoGCN}
              value={value}
              placeholder={titleInput[2]}
              styleContainer={[styles.containerInput]}
              onChangeText={(text) => onChangeText('so_gcn', text)}
              returnKeyType="next"
              onSubmit={() => refBienSoXe.current?.focus()}
              blurOnSubmit={false}
              onBlur={() => value !== '' && onChange(value.toUpperCase())}
              autoCapitalize="characters"
              clear
              clearTextInput={() => onChange('')}
            />
          )}
        />
        <View flexDirection="row">
          <Controller
            control={control}
            name="bien_so_xe"
            // rules={{
            //   minLength: 4,
            // }}
            render={({field: {onChange, value}}) => (
              <CommonOutlinedTextFieldWithIcon
                refInput={refBienSoXe}
                value={value}
                placeholder={titleInput[3]}
                onChangeText={(text) => onChangeText('bien_so_xe', text)}
                // error={errors.bien_so_xe && getErrMessage('bien_so_xe', errors.bien_so_xe.type)}
                styleContainer={[styles.containerInput] as StyleProp<ViewStyle>}
                returnKeyType="next"
                onSubmit={() => refSoKhung.current?.focus()}
                blurOnSubmit={false}
                onBlur={() => value !== '' && onChange(value.toUpperCase())}
                autoCapitalize="characters"
                clear
                clearTextInput={() => onChange('')}
              />
            )}
          />
        </View>
        <View>
          <Controller
            control={control}
            name="so_khung"
            render={({field: {onChange, value}}) => (
              <CommonOutlinedTextFieldWithIcon
                refInput={refSoKhung}
                value={value}
                placeholder={titleInput[4]}
                onChangeText={(text) => onChangeText('so_khung', text)}
                returnKeyType="next"
                onSubmit={() => refSoMay.current?.focus()}
                blurOnSubmit={false}
                styleContainer={[styles.containerInput]}
                onBlur={() => value !== '' && onChange(value.toUpperCase())}
                autoCapitalize="characters"
                clear
                clearTextInput={() => onChange('')}
              />
            )}
          />
          <Controller
            control={control}
            name="so_may"
            render={({field: {onChange, value}}) => (
              <CommonOutlinedTextFieldWithIcon
                refInput={refSoMay}
                placeholder={titleInput[5]}
                value={value}
                styleContainer={[styles.containerInput]}
                onChangeText={(text) => onChangeText('so_may', text)}
                onBlur={() => value !== '' && onChange(value.toUpperCase())}
                autoCapitalize="characters"
                clear
                clearTextInput={() => onChange('')}
              />
            )}
          />
        </View>
        {/* <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Icon.Ionicons name="warning" color={isValid ? colors.GREEN : colors.ORANGE} size={20} />
          <Text children="Nhập ít nhất 1 trường thông tin xe để tìm kiếm" color={isValid ? colors.GREEN : colors.ORANGE} />
        </View> */}
        {getDieuKienKhongBatBuocNhap2TruongDuLieu(maChiNhanh) ? (
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Icon.Ionicons name="warning" color={isValid ? colors.GREEN : colors.ORANGE} size={20} />
            <Text flex={1} children="Nhập ít nhất 1 trường trong các trường thông tin (Biển số xe, Số GCN bảo hiểm, Số khung, Số máy)" color={isValid ? colors.GREEN : colors.ORANGE} />
          </View>
        ) : (
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Icon.Ionicons name="warning" color={isValid ? colors.GREEN : colors.ORANGE} size={20} />
            <Text flex={1} children="Nhập ít nhất 2 trường trong các trường thông tin (Biển số xe, Số GCN bảo hiểm, Số khung, Số máy)" color={isValid ? colors.GREEN : colors.ORANGE} />
          </View>
        )}
      </View>
    );
  };
  /* RENDER */
  return (
    <Modal
      // statusBarTranslucent={true}
      onBackButtonPress={onBackPress}
      animationIn="fadeInRight"
      animationOut="fadeOutRight"
      isVisible={isVisible}
      onModalWillShow={() => {
        resetField();
        initChiNhanhBaoHiem();
      }}
      style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal
          title="Tìm kiếm đối tượng"
          onBackPress={onBackPress}
          rightComponent={
            <TouchableOpacity onPress={() => refModalQuetQR.current?.show()} style={styles.qrGCNView}>
              <Text children="GCN" color={colors.PRIMARY} style={styles.txtGCN} />
              <Icon.Ionicons name="qr-code" size={20} color={colors.PRIMARY} />
            </TouchableOpacity>
          }
        />
        <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">{renderFormInput()}</KeyboardAwareScrollView>
        <View style={[styles.footer]}>
          <ButtonLinear
            textStyle={{color: colors.BLACK_03}}
            linearColors={[colors.GRAY2, colors.GRAY2]}
            onPress={() => {
              resetField();
              setIsValid(false);
            }}
            title="Nhập lại"
          />
          <ButtonLinear onPress={handleSubmit(getData)} loading={loading} linearStyle={styles.btnLoginView} title="Tìm kiếm" disabled={!isValid || loading} />
        </View>
        <ModalSelectSimple
          title={'Chọn nghiệp vụ'}
          baseData={DATA_NGHIEP_VU_XCG}
          value={getValues('nv')}
          ref={refModalNghiepVu}
          setValue={(val) => setValue('nv', val.value, {shouldValidate: true})}
          onBackPress={() => refModalNghiepVu.current?.hide()}
        />
        <ModalChiNhanh
          baseData={listChiNhanh}
          value={getValues('ma_chi_nhanh')}
          ref={refModalChiNhanh}
          setValue={(val) => {
            getValidValue(val.value);
            setValue('ma_chi_nhanh', val.value, {shouldValidate: true});
          }}
          onBackPress={() => refModalChiNhanh.current?.hide()}
        />
        <ModalQuetQR ref={refModalQuetQR} handleData={handleQRData} />
      </SafeAreaView>
      {disalogLoading && <DialogLoading />}
    </Modal>
  );
});

export const ModalTimKiemDoiTuong = memo(ModalTimKiemDoiTuongComponent, isEqual);

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    // marginTop: !isIOS ? getStatusBarHeight() : 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  itemHangMucView: {
    paddingVertical: 10,
  },
  stepIndicator: {
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
  },
  headerView: {
    marginVertical: 10,
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  icon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  headerTitleView: {
    marginBottom: 10,
    paddingVertical: 9,
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
  },
  headerTitle: {
    marginBottom: 16,
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  formInput: {
    marginHorizontal: spacing.small,
    marginVertical: spacing.small,
  },
  btnLoginView: {
    marginLeft: spacing.small,
  },
  btnNhapLaiView: {
    marginHorizontal: spacing.tiny,
    borderWidth: 1,
    borderColor: colors.GRAY2,
    borderRadius: 10,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
  },

  subLabel: {
    color: colors.GRAY10,
  },
  contentRow: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  title: {
    fontSize: 18,
    marginLeft: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  containerInput: {
    flex: 1,
    marginBottom: spacing.small,
    marginTop: 0,
  },
  doubleRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footer: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    paddingHorizontal: 15,
    borderTopColor: colors.GRAY2,
    backgroundColor: colors.WHITE,
  },
  qrGCNView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    paddingVertical: 2,
    paddingHorizontal: spacing.smaller,
    borderRadius: 8,
    borderColor: colors.PRIMARY,
  },
  txtGCN: {
    marginRight: spacing.tiny,
  },
});
