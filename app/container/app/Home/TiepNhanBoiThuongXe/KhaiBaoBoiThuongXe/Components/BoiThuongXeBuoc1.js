import R from '@app/assets/R';
import {DATA_NGHIEP_VU_XCG, isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, DropdownPicker, Text, TextInputOutlined} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Image, Keyboard, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import LinearGradient from 'react-native-linear-gradient';
import {ModalTimKiemDoiTuong} from './ModalTimKiemDoiTuong';

let timer;

const BoiThuongXeBuoc1Component = (props) => {
  const {
    setData,
    data,
    dataLoaiHinh,
    loaiHinhSelected,
    setLoaiHinhSelected,
    dataLHNV,
    loaiHinhNVSelected,
    setLoaiHinhNVSelected,
    bienSoXe,
    setBienSoXe,
    tenChuXe,
    setTenChuXe,
    showForm,
    setShowForm,
    setNghiepVuSelected,
    nghiepVuSelected,
    selectedItemHoSo,
  } = props;
  // const [selectedItem, setSelectedItem] = useState(-1);
  const [isVisibleModal, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  // const [showForm, setShowForm] = useState(false);
  const [openLoaiHinh, setOpenLoaiHinh] = useState(false);
  const [openLoaiHinhNV, setOpenLoaiHinhNV] = useState(false);
  const [inputErr, setInputErr] = useState(new Array(4).fill(''));
  const [openNghiepVu, setOpenNghiepVu] = useState(false);

  let refModalTimKiemHopDongXe = useRef(null);
  let tenChuXeRef = useRef(null);
  let bsXeRef = useRef(null);

  const onPressSearch = () => {
    setShowForm(false);
    refModalTimKiemHopDongXe.current.show();
  };
  const onPressKhongXDDT = () => {
    setShowForm(true);
  };

  useEffect(() => {
    if (data.length <= 0) {
      timer = setTimeout(() => {
        setIsVisible(true);
      }, 500);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [data]);

  const onPressItem = (i) => props.setSelectedItemHoSo(i);

  useEffect(() => {
    // navigation.addListener('focus', () => {
    //   getData();
    // });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  /**RENDER  */

  const renderForm = () => {
    return (
      <KeyboardAwareScrollView keyboardShouldPersistTaps="handled" scrollEnabled={isIOS ? true : !openLoaiHinhNV}>
        <View marginHorizontal={spacing.small} flex={1} minHeight={openLoaiHinhNV ? dimensions.height : dimensions.height / 2}>
          <DropdownPicker
            isRequired
            zIndex={7000}
            title="Nghiệp vụ"
            searchable={false}
            items={DATA_NGHIEP_VU_XCG}
            isOpen={openNghiepVu}
            inputErr={inputErr[0]}
            setOpen={(value) => {
              Keyboard.dismiss();
              setOpenNghiepVu(value);
            }}
            placeholder="Chọn nghiệp vụ"
            itemSelected={nghiepVuSelected}
            setItemSelected={setNghiepVuSelected}
          />

          <DropdownPicker
            isRequired
            zIndex={6000}
            title="Loại hình"
            searchable={false}
            items={dataLoaiHinh}
            isOpen={openLoaiHinh}
            inputErr={inputErr[0]}
            setOpen={(value) => {
              Keyboard.dismiss();
              setOpenLoaiHinh(value);
            }}
            placeholder="Chọn loại hình"
            itemSelected={loaiHinhSelected}
            setItemSelected={setLoaiHinhSelected}
            onOpen={() => setOpenLoaiHinhNV(false)}
            // onChangeValue={onChangeValueDropdown}
          />

          <DropdownPicker
            isRequired
            zIndex={5000}
            items={dataLHNV.filter((item) => item.loai === loaiHinhSelected)}
            searchable={false}
            inputErr={inputErr[1]}
            isOpen={openLoaiHinhNV}
            setOpen={(value) => {
              Keyboard.dismiss();
              setOpenLoaiHinhNV(value);
            }}
            title="Loại hình nghiệp vụ"
            onOpen={() => setOpenLoaiHinh()}
            itemSelected={loaiHinhNVSelected}
            placeholder="Chọn loại hình nghiệp vụ"
            setItemSelected={setLoaiHinhNVSelected}
            maxHeight={200}
            // onChangeValue={onChangeValueDropdown}
          />

          <View flexDirection="row">
            <TextInputOutlined
              isRequired
              value={tenChuXe}
              title="Tên chủ xe"
              placeholder="Tên chủ xe"
              error={inputErr[2]}
              blurOnSubmit={false}
              returnKeyType={'next'}
              onChangeText={setTenChuXe}
              getRef={(ref) => (tenChuXeRef = ref)}
              onSubmitEditing={() => bsXeRef?.focus()}
              containerStyle={{flex: 1, marginRight: 10}}
            />
            <TextInputOutlined
              isRequired
              value={bienSoXe}
              title="Biển số xe"
              placeholder="Biển số xe"
              error={inputErr[3]}
              blurOnSubmit={false}
              onChangeText={setBienSoXe}
              containerStyle={{flex: 1}}
              getRef={(ref) => (bsXeRef = ref)}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    );
  };

  const renderProfileItem = ({item, index}) => {
    let linearColors = [];
    if (selectedItemHoSo === item) {
      linearColors = [colors.WHITE1, colors.WHITE1];
    } else linearColors = [colors.WHITE, colors.WHITE];
    return (
      <TouchableOpacity key={index} onPress={() => onPressItem(item)}>
        <LinearGradient colors={linearColors} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.profileItemView, {borderColor: selectedItemHoSo === item ? colors.PRIMARY : colors.GRAY}]}>
          {/* <Text style={styles.profileTxtHoSo(colors.BLACK)}>{item.so_hs}</Text> */}
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số hợp đồng: <Text style={styles.content}>{item.so_hdong}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Biển số xe: <Text style={styles.content}>{item.bien_so_xe}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số giấy chứng nhận: <Text style={styles.content}>{item.so_gcn}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Nghiệp vụ: <Text style={styles.content}>{item.ten_loai_gcn}</Text>{' '}
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Hiệu lực bh: <Text style={styles.content} children={item.ngay_hl_bh} /> -
              <Text style={styles.content} children={item.ngay_kt_bh} />
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Tên chủ xe: <Text style={styles.content}>{item.ten_chu_xe}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Đơn vị cấp đơn:<Text style={styles.content}>{item.ten_chi_nhanh}</Text>
            </Text>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View flexDirection="row" marginHorizontal={spacing.small}>
        <ButtonLinear
          onPress={onPressSearch}
          title="Tìm kiếm đối tượng"
          linearStyle={styles.btnSearch}
          textStyle={{color: showForm ? colors.BLACK_03 : colors.WHITE}}
          linearColors={showForm ? [colors.GRAY2, colors.GRAY2] : [colors.PRIMARY, colors.PRIMARY]}
        />
        <ButtonLinear
          title="Không x/đ đ.tượng"
          onPress={onPressKhongXDDT}
          linearStyle={styles.btnSearch}
          textStyle={{color: !showForm ? colors.BLACK_03 : colors.WHITE}}
          linearColors={!showForm ? [colors.GRAY2, colors.GRAY2] : [colors.PRIMARY, colors.PRIMARY]}
        />
      </View>
      {showForm ? (
        renderForm()
      ) : (
        <FlatList
          data={data || []}
          removeClippedSubviews={true}
          refreshControl={<RefreshControl refreshing={loading} />}
          keyExtractor={(item, index) => index.toString()}
          renderItem={renderProfileItem}
          ListHeaderComponent={data.length > 1 && <Text style={styles.getStylesTxtCanhBao}>Vui lòng chọn hợp đồng phù hợp để tiếp tục</Text>}
          ListEmptyComponent={
            <View style={styles.noDataView}>
              <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
              <Text>Chưa có dữ liệu</Text>
            </View>
          }
        />
      )}
      <ModalTimKiemDoiTuong ref={refModalTimKiemHopDongXe} setData={setData} onBackPress={() => refModalTimKiemHopDongXe.current.hide()} />
    </SafeAreaView>
  );
};

export const BoiThuongXeBuoc1 = memo(BoiThuongXeBuoc1Component, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  profileItemView: {
    padding: 8,
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: 5,
    marginHorizontal: 10,
    borderColor: colors.GRAY,
  },
  subLabel: {
    color: colors.GRAY10,
  },
  contentRow: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  content: {
    flex: 1,
    color: colors.PRIMARY,
  },
  btnSearch: {
    marginVertical: 8,
    marginHorizontal: 5,
  },
  imageNoData: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  noDataView: {
    alignItems: 'center',
  },
  getStylesTxtCanhBao: {
    color: colors.ORANGE,
    margin: spacing.small,
  },
});
