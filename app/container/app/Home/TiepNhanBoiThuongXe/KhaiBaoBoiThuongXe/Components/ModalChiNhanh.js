import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Empty, HeaderModal, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalChiNhanhComponent = forwardRef((props, ref) => {
  const [isVisible, setIsVisible] = useState(false);
  const {onBackPress, baseData, setValue, value} = props;

  const [searchInput, setSearchInput] = useState('');
  const [dataChiNhanh, setDataChiNhanh] = useState([]);

  let refContainer = useRef();

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const initModalData = () => {
    let newArr = baseData;
    newArr.map((e, idx) => {
      newArr[idx].isChecked = false;
      if (e.value === value) newArr[idx].isChecked = true;
    });
    setDataChiNhanh([...newArr]);
  };

  const onSelectItem = (item) => {
    setValue && setValue(item);
    onBackPress && onBackPress();
  };

  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 500);

  useEffect(() => {
    if (searchInput) {
      const lowerCaseSearchText = searchInput?.toLowerCase();
      const filter = baseData.filter((item) => item?.label.toLowerCase()?.includes(lowerCaseSearchText) || item?.value.toLowerCase()?.includes(lowerCaseSearchText));
      setDataChiNhanh(filter);
    } else if (searchInput === '') {
      setDataChiNhanh(baseData);
    }
  }, [searchInput]);

  /* RENDER */
  const renderItemHangMuc = ({item, index}) => (
    <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onSelectItem(item)}>
      <Text style={styles.getLabelStyles(item.isChecked)} children={item.label} />
      {item.isChecked && <Icon.Ionicons name={'checkmark-sharp'} size={25} style={styles.iconRightBtn} color={colors.PRIMARY} />}
    </TouchableOpacity>
  );

  return (
    <Modal animationIn={'fadeIn'} animationOut={'fadeOut'} onModalWillShow={initModalData} isVisible={isVisible} style={styles.modal}>
      <SafeAreaView style={{flex: 1}}>
        <HeaderModal title="Chọn chi nhánh" onBackPress={onBackPress} />
        <SearchBar placeholder="Tìm kiếm chi nhánh" onTextChange={debounced} />
        <FlatList
          data={dataChiNhanh}
          ref={refContainer}
          scrollEnabled={true}
          style={styles.flStyles}
          onEndReachedThreshold={0.3}
          renderItem={renderItemHangMuc}
          keyExtractor={(item, index) => item + index.toString()}
          ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
        />
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },

  searchInput: {
    flex: 1,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    flexDirection: 'row',
    paddingVertical: 10,
    marginHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 18,
    marginLeft: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  searchBar: {
    height: 45,
    borderRadius: 8,
  },
  getLabelStyles: (value) => {
    return {
      color: value ? colors.PRIMARY : colors.BLACK_03,
      marginTop: !isIOS ? 4 : 2,
    };
  },
});

export const ModalChiNhanh = memo(ModalChiNhanhComponent, isEqual);
