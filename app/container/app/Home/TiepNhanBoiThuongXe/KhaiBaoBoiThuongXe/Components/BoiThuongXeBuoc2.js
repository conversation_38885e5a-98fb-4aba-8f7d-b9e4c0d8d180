import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {CommonOutlinedTextFieldWithIcon, ModalSelectSimple, Text} from '@component';
import moment from 'moment';
import React, {memo, useRef} from 'react';
import isEqual from 'react-fast-compare';
import {Keyboard, StyleSheet, View} from 'react-native';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {MOI_QUAN_HE_DATA} from './Constant';

const BoiThuongXeBuoc2Component = (props) => {
  const {
    selectedItemHoSo,
    gioThongBao,
    setGioThongBao,
    toggleGioThongBao,
    setToggleGioThongBao,
    ngayThongBao,
    setNgayThongBao,
    toggleNgayThong<PERSON>ao,
    setToggleNgayThongBao,
    luongXly,
    moiQuanHeNgThongBao,
    chiTietHS,
    dataLuongXuLyTheoGCN,
    showForm,
    setInputErrLienHe,
  } = props;

  let refModalMoiQuanHe = useRef(null);
  let refModalLuongXuly = useRef(null);
  // let refModalHienTruong = useRef(null);

  const getTenHienThi = (value, data) => {
    let name = '';
    data.map((e) => {
      if (value === e.value) name = e.label;
    });
    return name;
  };

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        setToggleDateTime(false);
        setDateTime(dateSelected);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderContactInfo = () => {
    return (
      <View style={[styles.contentDetail]}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin người thông báo</Text>
        </View>
        <View marginHorizontal={10}>
          <CommonOutlinedTextFieldWithIcon
            isPickerModal
            editable={false}
            value={getTenHienThi(moiQuanHeNgThongBao, MOI_QUAN_HE_DATA)}
            placeholder={'Mối quan hệ với chủ xe'}
            onPressInput={() => {
              Keyboard.dismiss();
              refModalMoiQuanHe.current.show();
            }}
            error={props.inputErrLienHe[8]}
          />
          <CommonOutlinedTextFieldWithIcon
            isRequired
            value={props.userNoticeName}
            onChangeText={(value) => {
              props.setUserNoticeName(value);
              if (props.checkboxIsUserNotice) props.setUserContactName(value);
            }}
            placeholder="Họ tên"
            error={props.inputErrLienHe[0]}
            editable={true}
          />
          <CommonOutlinedTextFieldWithIcon
            isRequired
            value={props.userNoticePhone}
            placeholder="Điện thoại"
            keyboardType="numeric"
            onChangeText={(value) => {
              props.setUserNoticePhone(value);
              if (props.checkboxIsUserNotice) props.setUserContactPhone(value);
            }}
            error={props.inputErrLienHe[1]}
            editable={true}
          />
          <CommonOutlinedTextFieldWithIcon
            value={props.userNoticeEmail}
            placeholder="Email"
            keyboardType="email-address"
            onChangeText={(value) => {
              props.setUserNoticeEmail(value);
              if (props.checkboxIsUserNotice) props.setUserContactEmail(value);
            }}
            error={props.inputErrLienHe[2]}
            editable={true}
          />
        </View>
      </View>
    );
  };
  /**THÔNG TIN NGƯỜI LIÊN HỆ */
  const renderContactUser = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin người liên hệ</Text>
        </View>
        <View marginHorizontal={10}>
          <View style={styles.descRow}>
            <Text style={styles.txtSubTitle}>Tôi là người thông báo và là người liên hệ</Text>
            <View>
              <BouncyCheckbox
                iconStyle={{borderColor: colors.PRIMARY}}
                fillColor={colors.PRIMARY}
                isChecked={props.checkboxIsUserNotice}
                onPress={() => {
                  props.onPressCheckbox();
                }}
              />
            </View>
          </View>
          <CommonOutlinedTextFieldWithIcon
            isRequired
            value={props.userContactName}
            onChangeText={(value) => props.setUserContactName(value)}
            placeholder="Họ tên"
            disabled={props.checkboxIsUserNotice}
            editable={!props.checkboxIsUserNotice}
            error={props.inputErrLienHe[3]}
          />
          <CommonOutlinedTextFieldWithIcon
            isRequired
            value={props.userContactPhone}
            placeholder="Điện thoại"
            keyboardType="numeric"
            onChangeText={(value) => props.setUserContactPhone(value)}
            disabled={props.checkboxIsUserNotice}
            editable={!props.checkboxIsUserNotice}
            error={props.inputErrLienHe[4]}
          />
          <CommonOutlinedTextFieldWithIcon
            value={props.userContactEmail}
            placeholder="Email"
            keyboardType="email-address"
            onChangeText={(value) => props.setUserContactEmail(value)}
            disabled={props.checkboxIsUserNotice}
            editable={!props.checkboxIsUserNotice}
            error={props.inputErrLienHe[5]}
          />
        </View>
      </View>
    );
  };

  //thông tin xe
  const renderCarInfo = () => {
    const renderLabel = (title, value) => {
      return (
        <View style={styles.viewRowGCN}>
          <Text style={styles.label}>{title}:</Text>
          <Text style={styles.detail}>{value}</Text>
        </View>
      );
    };
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin xe</Text>
        </View>
        <View marginHorizontal={10}>
          {renderLabel('Biển số', chiTietHS?.bien_xe)}
          {renderLabel('Chủ xe', chiTietHS?.ten_chu_xe)}
          {renderLabel('Điện thoại', chiTietHS?.dthoai)}
          {renderLabel('Email', chiTietHS?.email)}
        </View>
      </View>
    );
  };
  // luồng xử lý
  const renderLuongXuly = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Chọn luồng xử lý</Text>
        </View>
        <View marginHorizontal={10}>
          <CommonOutlinedTextFieldWithIcon
            isRequired
            error={props.inputErrLienHe[7]}
            value={getTenHienThi(luongXly, dataLuongXuLyTheoGCN)}
            editable={false}
            placeholder="Chọn luồng xử lý"
            isPickerModal
            onPressInput={() => {
              Keyboard.dismiss();
              refModalLuongXuly.current.show();
            }}
          />
          <View style={styles.doubleInputRowView}>
            <View style={styles.doubleInputRow}>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                isDateTimeField
                editable={false}
                placeholder="Giờ thông báo"
                onPressInput={() => {
                  Keyboard.dismiss();
                  setToggleGioThongBao(true);
                }}
                value={gioThongBao ? moment(gioThongBao).format('HH:mm') : ''}
                error={props.inputErrLienHe[9]}
              />
              {renderDateTimeComp(
                toggleGioThongBao,
                setToggleGioThongBao,
                (value) => {
                  setGioThongBao(value);
                  setInputErrLienHe((prev) => {
                    let newValue = prev;
                    newValue[9] = '';
                    return [...newValue];
                  });
                },
                gioThongBao || new Date(),
                'time',
                null,
                null,
              )}
            </View>
            <View style={styles.doubleInputRow}>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                isDateTimeField
                editable={false}
                placeholder="Ngày thông báo"
                onPressInput={() => {
                  Keyboard.dismiss();
                  setToggleNgayThongBao(true);
                }}
                value={ngayThongBao ? moment(ngayThongBao).format('DD/MM/YYYY') : ''}
                error={props.inputErrLienHe[10]}
              />
              {renderDateTimeComp(
                toggleNgayThongBao,
                setToggleNgayThongBao,
                (value) => {
                  setNgayThongBao(value);
                  setInputErrLienHe((prev) => {
                    let newValue = prev;
                    newValue[10] = '';
                    return [...newValue];
                  });
                },
                ngayThongBao || new Date(),
                'date',
                null,
                new Date(),
              )}
            </View>
          </View>
          {/* <CommonOutlinedTextFieldWithIcon value={'hienTruong.ten'} editable={false} placeholder="Hiện trường*" isPickerModal onPressInput={() => refModalHienTruong.current.show()} /> */}
        </View>
      </View>
    );
  };

  const renderThongTinGCN = () => {
    const renderLabel = (title, value) => {
      return (
        <View style={styles.viewRowGCN}>
          <Text style={styles.label}>{title}:</Text>
          <Text style={styles.detail}>{value}</Text>
        </View>
      );
    };
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông giấy chứng nhận</Text>
        </View>
        <View marginHorizontal={10}>
          {renderLabel('Tên khách hàng', selectedItemHoSo?.ten_chu_xe || '')}
          {renderLabel('Số hợp đồng', selectedItemHoSo?.so_hdong || '')}
          {renderLabel('Số giấy chứng nhận', selectedItemHoSo?.so_gcn || '')}
          {renderLabel('Loại hình', selectedItemHoSo?.ten_loai_gcn || '')}
          {renderLabel('Hiệu lực', selectedItemHoSo?.ngay_hl_bh + ' - ' + selectedItemHoSo?.ngay_kt_bh || '')}
        </View>
      </View>
    );
  };
  return (
    <KeyboardAwareScrollView keyboardShouldPersistTaps="handled" style={styles.contentView}>
      {/* Thông tin xe */}
      {!showForm && (
        <View>
          {renderThongTinGCN()}
          {renderCarInfo()}
        </View>
      )}

      {renderLuongXuly()}
      {/* THÔNG TIN NGƯỜI THÔNG BÁO */}
      {renderContactInfo()}
      {/* Thông tin người liên hệ */}
      {renderContactUser()}
      <ModalSelectSimple
        title={'Mối quan hệ với chủ xe'}
        baseData={MOI_QUAN_HE_DATA}
        value={moiQuanHeNgThongBao}
        setValue={(selected) => props.setMoiQuanHeNgThongBao(selected.value)}
        ref={refModalMoiQuanHe}
        onBackPress={() => refModalMoiQuanHe.current.hide()}
      />
      <ModalSelectSimple
        title={'Chọn luồng xử lý'}
        value={luongXly}
        baseData={dataLuongXuLyTheoGCN}
        setValue={(selected) => props.setLuongXly(selected.value)}
        ref={refModalLuongXuly}
        onBackPress={() => refModalLuongXuly.current.hide()}
      />
      {/* <ModalChonHienTruongXe data={dataHienTruong} setValue={props.setHienTruong} ref={refModalHienTruong} onBackPress={() => refModalHienTruong.current.hide()} /> */}
    </KeyboardAwareScrollView>
  );
};

export const BoiThuongXeBuoc2 = memo(BoiThuongXeBuoc2Component, isEqual);

const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    width: dimensions.width,
  },
  btnLoginView: {
    flexDirection: 'row',
    backgroundColor: colors.PRIMARY,
    marginHorizontal: 10,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerView: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    backgroundColor: colors.WHITE,
  },
  stepIndicator: {
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingRight: 5,
    color: colors.WHITE,
    paddingVertical: 10,
    textTransform: 'uppercase',
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.BLUE3,
  },
  contentDetail: {
    marginBottom: spacing.smaller,
    backgroundColor: colors.WHITE,
    borderRadius: 10,
  },
  modalSelectorView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    marginRight: 15,
  },
  dropDownSelectedTxt: {
    flex: 1,
    fontSize: 14,
    paddingRight: 5,
    paddingLeft: 10,
    paddingVertical: 10,
  },
  headerTitleView: {
    marginBottom: 10,
    paddingVertical: 9,
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
  },
  errTxt: {
    fontSize: 12,
    marginLeft: 12,
    color: colors.RED1,
  },
  descRow: {
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewRowGCN: {
    marginVertical: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: {
    color: colors.GRAY6,
  },
  detail: {
    flex: 1,
    textAlign: 'right',
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  doubleInputRow: {
    width: (dimensions.width - 30) / 2,
  },
  doubleInputRowView: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtSubTitle: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
