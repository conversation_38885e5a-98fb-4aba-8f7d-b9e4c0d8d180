export interface ModalTimKiemDoiTuongProps {
  onBackPress: () => {};
  setData: (data) => {};
}
export interface ChiNhanHBaoHiemProps {
  loai: string;
  ma_cap_tren: string;
  ma_chi_nhanh: string;
  ma_goc: string;
  quan_ly: number;
  stt: number;
  ten_chi_nhanh: string;
}
export interface ListChiNhanHBaoHiemProps {
  label: string;
  value: string;
}
export type ModalTimKiemHandle = {
  show: () => void;
  hide: () => void;
};
