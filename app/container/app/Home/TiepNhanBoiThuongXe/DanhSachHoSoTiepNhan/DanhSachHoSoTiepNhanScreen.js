import R from '@app/assets/R';
import {NGHIEP_VU, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useSelector} from 'react-redux';
import {ModalTimKiemHoSoTiepNhan} from './Compontents';
import styles from './DanhSachHoSoTiepNhanStyles';
import {spacing} from '@app/theme';
import ListProfilesHeader from '@app/components/ListProfilesHeader';

let timer;
const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment(new Date()).format('YYYYMMDD');

const DanhSachHoSoTiepNhanScreenComponent = ({navigation}) => {
  console.log('DanhSachHoSoTiepNhanScreenComponent');
  const userInfo = useSelector(selectUser);
  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const user = userInfo?.nguoi_dung;

  let refModalTimKiemHoSo = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const [objParams, setObjParams] = useState({
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    bien_xe: '',
    ma_doi_tac: user?.ma_doi_tac,
    ma_chi_nhanh: user?.ma_chi_nhanh,
    gcn: '',
    dien_thoai: '',
    ten_kh: '',
    trang_thai: '', // D, C, H đã chuyển / chưa chuyển / huỷ
  });

  const [data, setData] = useState([]);
  const [total, setTotal] = useState(20);
  const [current, setCurrent] = useState(1);

  useEffect(() => {
    timer = setTimeout(() => {
      getData();
    }, 500);
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [objParams]);

  const getData = async (oldData = [], trang = 1, so_dong = 20) => {
    setDialogLoading(true);
    let subObj = {
      trang: trang,
      so_dong: so_dong,
    };
    let params = {...subObj, ...objParams};
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HS_BOI_THUONG, params);
      setRefreshing(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) return;
      setTotal(response.data_info.tong_so_dong);
      let arrData = response.data_info.data;
      let mergeData = [...oldData, ...arrData];
      setData(mergeData);
    } catch (error) {
      setRefreshing(false);
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onRefresh = () => {
    setDialogLoading(true);
    setCurrent(1);
    getData();
  };

  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setCurrent(current + 1);
      getData(data, current + 1);
    }
  };

  const renderProfileItem = ({item}) => {
    const icColor = item.nghiep_vu === NGHIEP_VU.XE_MAY ? colors.VIOLET1 : colors.PRIMARY;
    const icName = item.nghiep_vu === NGHIEP_VU.XE_MAY ? 'motorcycle' : 'automobile';
    const icSize = item.nghiep_vu === NGHIEP_VU.XE_MAY ? 16 : 15;
    const statusColorText = item.trang_thai_ten === 'Đã chuyển' ? colors.GREEN : item.trang_thai_ten === 'Đang xử lý' ? colors.PRIMARY : item.trang_thai_ten === 'Đã hủy' ? colors.RED1 : null;
    const renderLabel = (title, value, style) => {
      return (
        <Text style={[styles.label]}>
          {title}: <Text style={[styles.detail, style]} children={value} />
        </Text>
      );
    };
    return (
      <TouchableOpacity onPress={() => NavigationUtil.navigate(SCREEN_ROUTER_APP.CHI_TIET_HO_SO_BOI_THUONG_XE, {profileDetail: item, prevScreen: SCREEN_ROUTER_APP.DS_HO_SO_KHAI_BAO_TON_THAT})}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            {renderLabel('Số tiếp nhận', item.so_tn, styles.txtSoHS)}
            {renderLabel('Ngày mở', item.ngay)}
            {renderLabel('Trạng thái', item.trang_thai_ten, {color: statusColorText})}
            <View flexDirection="row" alignItems="center" marginBottom={spacing.tiny}>
              <Icon.FontAwesome name={icName} size={icSize} color={icColor} />
              <Text style={[styles.detail, {marginLeft: spacing.smaller}]} children={item.doi_tuong} />
            </View>
            {renderLabel('Bồi thường viên', item.gdvtt)}
            {renderLabel('Gđv hiện trường', item.gdvht)}
            {renderLabel('Giấy chứng nhận', item.gcn)}
          </View>
          <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  // footer

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Hồ sơ tiếp nhận bồi thường"
      renderView={
        <SafeAreaView style={styles.container}>
          <ListProfilesHeader ngayDau={objParams.ngay_d} ngayCuoi={objParams.ngay_c} onPressSearch={() => refModalTimKiemHoSo.current.show()} />
          <FlatList
            data={data}
            renderItem={renderProfileItem}
            keyExtractor={(_, index) => index.toString()}
            onEndReachedThreshold={0.5}
            onEndReached={handleLoadMore}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            ListEmptyComponent={
              <View style={styles.noDataView}>
                <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
                <Text>Chưa có dữ liệu</Text>
              </View>
            }
          />
          <ModalTimKiemHoSoTiepNhan ref={refModalTimKiemHoSo} userInfo={userInfo} initFormInput={objParams} setValue={setObjParams} onBackPress={() => refModalTimKiemHoSo.current.hide()} />
        </SafeAreaView>
      }
      footer={<ButtonLinear title="Mở hồ sơ khai báo tổn thất" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1, {page: 0, info: {}})} />}
    />
  );
};

export const DanhSachHoSoTiepNhanScreen = memo(DanhSachHoSoTiepNhanScreenComponent, isEqual);
