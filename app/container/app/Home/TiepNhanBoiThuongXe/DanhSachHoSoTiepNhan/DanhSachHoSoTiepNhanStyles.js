import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },
  radioView: {
    marginLeft: 5,
    marginTop: 10,
    // marginBottom: 15,
  },
  dropDownTitle: {
    marginTop: 5,
    marginLeft: 10,
    fontWeight: 'bold',
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  profileItemView: {
    paddingLeft: 10,
    borderRadius: 10,
    borderWidth: 0.4,
    marginVertical: 5,
    flexDirection: 'row',
    marginHorizontal: spacing.small,
    borderColor: colors.GRAY,
  },
  profileItemCenterView: {
    flex: 1,
    paddingRight: 5,
    paddingVertical: 5,
    borderBottomColor: colors.GRAY4,
  },
  profileItemRightView: {
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: colors.GRAY4,
  },
  profileItemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'space-between',
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  profileTxtHoSo: {
    fontSize: 14,
    marginBottom: 4,
    color: colors.BLACK_03,
  },
  profileImgClock: {
    width: 14,
    height: 14,
    opacity: 0.8,
    marginRight: 5,
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
    fontWeight: '500',
    color: colors.GRAY6,
    // textDecorationLine: textStyle,
  },
  noDataView: {
    // flexDirection: 'row',
    alignItems: 'center',
  },
  btnSearch: {
    borderRadius: 5,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
  },
  txtTimKiem: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
    textTransform: 'uppercase',
  },
  txtHeaderList: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.BLACK_03,
  },
  date: {
    flex: 1,
    color: colors.PRIMARY,
  },
  txtSoHS: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  renderHeader: {
    marginBottom: 5,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    borderBottomWidth: 0.2,
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  detail: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.BLACK_03,
  },
});
