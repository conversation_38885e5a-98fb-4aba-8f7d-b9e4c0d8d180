import {DATA_NGHIEP_VU_XCG} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {BenThuBaEndPoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {logErrorTryCatch} from '@app/utils';
import {replaceAllISO8859} from '@app/utils/string';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon, DialogLoading, Icon, ModalQuetQR, ModalSelectSimple, Text} from '@component';
import moment from 'moment';
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {Alert, Keyboard, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {ModalTrangThaiHoSo} from './ModalTrangThaiHoSo';

const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment().format('YYYYMMDD');

const ModalTimKiemHoSoTiepNhanComponent = forwardRef((props, ref) => {
  const {setValue, onBackPress} = props;
  let refModalTrangThaiHoSo = useRef(null);
  let refModalNghiepVu = useRef(null);
  let refModalQuetQR = useRef(null);
  const [disalogLoading, setDialogLoading] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const dataTrangThaiHSGoc = [
    {label: 'Chưa chuyển', value: 'C'},
    {label: 'Đã chuyển', value: 'D'},
    {label: 'Đã huỷ', value: 'H'},
  ];
  const [valueTrangThaiHoSo, setValueTrangThaiHoSo] = useState({});

  const initFormInput = {
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    bien_xe: '',
    ma_doi_tac: '',
    ma_chi_nhanh: '',
    gcn: '',
    dien_thoai: '',
    ten_kh: '',
    nv: 'XE',
    trang_thai: '', // D, C, H đã chuyển / chưa chuyển / huỷ
  };
  const titleInput = ['Ngày đầu', 'Ngày cuối', 'Biển số xe', 'Giấy chứng nhận', 'Tên khách hàng', 'Điện thoại', 'Trạng thái hồ sơ'];
  const [formInput, setFormInput] = useState(initFormInput);

  // modal value
  const [ngayDau, setNgayDau] = useState();
  const [ngayCuoi, setNgayCuoi] = useState();
  // toggle modal
  const [toggleNgayDau, setToggleNgayDau] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [toggleNgayCuoi, setToggleNgayCuoi] = useState(false);

  const onChangeText = async (field, value) => {
    setFormInput((prev) => ({...prev, [field]: value}));
  };

  useEffect(() => {
    if (ngayDau) {
      onChangeText('ngay_d', Number(moment(ngayDau).format('YYYYMMDD')));
    }
    if (ngayCuoi) {
      onChangeText('ngay_c', Number(moment(ngayCuoi).format('YYYYMMDD')));
    }
    onChangeText('trang_thai', valueTrangThaiHoSo.value);
  }, [ngayDau, ngayCuoi, valueTrangThaiHoSo]);

  const onSearchPress = () => {
    setValue && setValue(formInput);
    onBackPress && onBackPress();
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetField = () => {
    setFormInput(initFormInput);
    setValueTrangThaiHoSo({});
    setNgayDau(moment(startOfMonth).toDate());
    setNgayCuoi(moment(currentDate).toDate());
  };

  const getTenHienThi = (value, data) => {
    let name = '';
    data.map((e) => {
      if (value === e.value) name = e.label;
    });
    return name;
  };

  const handleQRData = async (data) => {
    try {
      if (!data.includes('https://hotro.pjico.com.vn/')) {
        return setTimeout(() => {
          Alert.alert('Thông báo', 'Mã QR không phù hợp với Giấy chứng nhận');
        }, 350);
      }
      setDialogLoading(true);
      let response = await BenThuBaEndPoint.readUrlQrGcnPJICO(data);
      setDialogLoading(false);
      let arrInputResult = response.split('input-result">');
      let arrLabel = response.split('control-label">');
      arrInputResult = arrInputResult.map((item) => item.split('</label>')[0]);
      arrLabel = arrLabel.map((item) => item.split('</label>')[0]).filter((item) => item !== 'Tình trạng');
      if (arrInputResult.length <= arrLabel.length) {
        for (let i = 0; i < arrInputResult.length; i++) {
          if (arrLabel[i] === 'Số đơn BH') {
            let arrSoGCN = arrInputResult[i].split('/');
            let soGCN = '';
            /*xử lý trường hợp nối dài số GCN + số Sửa đổi bổ sung : P-23/HPH/XCG/5106/001316E-23/HPH/XCG/5106/001316-01
            số GCN dúng chỉ đến P-23/HPH/XCG/5106/001316
            */
            if (arrSoGCN.length > 5) {
              let indexSlice = 0;
              for (let j = 0; j < arrSoGCN[4].length; j++) {
                if (arrSoGCN[4][j] > '9') {
                  indexSlice = j;
                  break;
                }
              }
              soGCN = arrSoGCN.slice(0, 4).join('/') + '/' + arrSoGCN[4].slice(0, indexSlice);
            } else soGCN = arrInputResult[i];
            onChangeText('gcn', soGCN);
          } else if (arrLabel[i] === 'Tên khách hàng') onChangeText('ten_kh', replaceAllISO8859(arrInputResult[i]).toUpperCase());
          else if (arrLabel[i] === 'Biển kiểm soát') onChangeText('bien_xe', arrInputResult[i].length > 2 ? arrInputResult[i] : '');
        }
      }
    } catch (error) {
      logErrorTryCatch({code: 'QUET_QR_GCN_ERROR', message: data});
      setTimeout(() => {
        Alert.alert('Thông báo', error.message);
      }, 350);
    }
  };

  /**RENDER  */
  const renderDateTimeComp = (toggle, setToggle, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      date={date}
      mode={mode}
      locale={'vi_VN'}
      display="spinner"
      isVisible={toggle}
      confirmTextIOS="Chọn"
      maximumDate={maxDate}
      minimumDate={minDate}
      cancelTextIOS="Để sau"
      onCancel={() => setToggle(false)}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggle, setDateTime)}
      themeVariant="light"
      isDarkModeEnabled={false}
      //   is24Hour={true}
    />
  );

  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.formInput}>
          {/* Ngày đầu/Ngày cuối */}
          <View style={styles.doubleRowView}>
            <View flex={1} marginRight={12}>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                isDateTimeField
                editable={true}
                disabled={false}
                placeholder={titleInput[0]}
                styleContainer={styles.containerInput}
                onPressInput={() => {
                  Keyboard.dismiss();
                  setToggleNgayDau(true);
                }}
                value={ngayDau ? moment(ngayDau).format('DD/MM/YYYY') : moment().startOf('month').format('DD/MM/YYYY')}
              />
              {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, setNgayDau, ngayDau, 'date', null, new Date(), 0)}
            </View>
            <View flex={1}>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                editable={true}
                disabled={false}
                placeholder={titleInput[1]}
                styleContainer={styles.containerInput}
                value={ngayCuoi ? moment(ngayCuoi).format('DD/MM/YYYY') : moment().format('DD/MM/YYYY')}
                isDateTimeField
                onPressInput={() => {
                  Keyboard.dismiss();
                  setToggleNgayCuoi(true);
                }}
              />
              {renderDateTimeComp(toggleNgayCuoi, setToggleNgayCuoi, setNgayCuoi, ngayCuoi, 'date', null, new Date(), 0)}
            </View>
          </View>
          <CommonOutlinedTextFieldWithIcon
            isPickerModal
            editable={true}
            disabled={false}
            value={getTenHienThi(formInput.nv, DATA_NGHIEP_VU_XCG)}
            placeholder={'Nghiệp vụ'}
            styleContainer={styles.containerInput}
            onPressInput={() => {
              Keyboard.dismiss();
              refModalNghiepVu.current.show();
            }}
            onChangeText={(text) => onChangeText('nv', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.bien_xe}
            placeholder={titleInput[2]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('bien_xe', text)}
            clear
            clearTextInput={() => onChangeText('bien_xe', '')}
          />
          <CommonOutlinedTextFieldWithIcon
            value={formInput.gcn}
            placeholder={titleInput[3]}
            onChangeText={(text) => onChangeText('gcn', text)}
            disabled={false}
            editable={true}
            styleContainer={styles.containerInput}
            clear
            clearTextInput={() => onChangeText('gcn', '')}
          />
          <CommonOutlinedTextFieldWithIcon
            value={formInput.ten_kh}
            placeholder={titleInput[4]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('ten_kh', text)}
            disabled={false}
            editable={true}
            clear
            clearTextInput={() => onChangeText('ten_kh', '')}
          />
          <CommonOutlinedTextFieldWithIcon
            value={formInput.dien_thoai}
            keyboardType="phone-pad"
            placeholder={titleInput[5]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('dien_thoai', text)}
            disabled={false}
            editable={true}
            clear
            clearTextInput={() => onChangeText('dien_thoai', '')}
          />
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            isPickerModal={true}
            placeholder={titleInput[6]}
            styleContainer={styles.containerInput}
            value={valueTrangThaiHoSo.label || ''}
            onPressInput={() => {
              Keyboard.dismiss();
              refModalTrangThaiHoSo.current.show();
            }}
          />
        </View>
      </View>
    );
  };

  /* RENDER */
  const renderContent = () => {
    return <View style={{flex: 1, marginTop: 10}}>{renderFormInput()}</View>;
  };
  return (
    <Modal onBackButtonPress={onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal
          title="Tìm kiếm hồ sơ"
          onBackPress={onBackPress}
          rightComponent={
            <TouchableOpacity onPress={() => refModalQuetQR.current?.show()} style={styles.qrGCNView}>
              <Text children="GCN" color={colors.PRIMARY} style={styles.txtGCN} />
              <Icon.Ionicons name="qr-code" size={20} color={colors.PRIMARY} />
            </TouchableOpacity>
          }
        />
        <KeyboardAwareScrollView keyboardShouldPersistTaps="handled">{renderContent()}</KeyboardAwareScrollView>
        <View style={styles.footer}>
          <ButtonLinear textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} onPress={resetField} linearStyle={styles.btnLoginView} title="Nhập lại" />
          <ButtonLinear onPress={onSearchPress} linearStyle={styles.btnLoginView} title="Tìm kiếm" />
        </View>
      </SafeAreaView>
      <ModalTrangThaiHoSo ref={refModalTrangThaiHoSo} dataBLV={dataTrangThaiHSGoc} setValue={setValueTrangThaiHoSo} onBackPress={() => refModalTrangThaiHoSo.current.hide()} />
      <ModalSelectSimple
        title={'Chọn nghiệp vụ'}
        baseData={DATA_NGHIEP_VU_XCG}
        ref={refModalNghiepVu}
        setValue={(val) => onChangeText('nv', val.value)}
        value={formInput.nv}
        onBackPress={() => refModalNghiepVu.current.hide()}
      />
      <ModalQuetQR ref={refModalQuetQR} handleData={handleQRData} />
      {disalogLoading && <DialogLoading />}
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  modalView: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  backBtn: {},
  iconBack: {},
  itemHangMucView: {
    paddingVertical: 10,
  },
  stepIndicator: {
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
  },
  headerView: {
    marginVertical: 10,
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  icon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  headerTitleView: {
    marginBottom: 10,
    paddingVertical: 9,
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
  },
  headerTitle: {
    marginBottom: 16,
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  formInput: {
    marginHorizontal: 10,
  },
  btnLoginView: {
    marginHorizontal: 8,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
  },

  subLabel: {
    color: colors.GRAY10,
  },
  contentRow: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  title: {
    fontSize: 18,
    marginLeft: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  containerInput: {
    height: 45,
  },
  doubleRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footer: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    paddingHorizontal: 15,
    borderTopColor: colors.GRAY2,
    backgroundColor: colors.WHITE,
  },
  qrGCNView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    paddingVertical: 2,
    paddingHorizontal: spacing.smaller,
    borderRadius: 8,
    borderColor: colors.PRIMARY,
  },
  txtGCN: {
    marginRight: spacing.tiny,
  },
});

export const ModalTimKiemHoSoTiepNhan = ModalTimKiemHoSoTiepNhanComponent;
