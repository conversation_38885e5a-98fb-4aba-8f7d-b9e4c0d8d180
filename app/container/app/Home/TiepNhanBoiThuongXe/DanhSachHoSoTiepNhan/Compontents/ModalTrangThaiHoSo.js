import {colors} from '@app/commons/Theme';
import {Icon, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useRef, useState} from 'react';
import {Dimensions, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
const {width, height} = Dimensions.get('screen');

const ModalTrangThaiHSComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, dataBLV} = props;

  let scrollViewModalRef = useRef(null);

  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState(height);
  const [selectedItemIndex, setSelectedItemIndex] = useState(-1);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setSelectedItemIndex(index);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn trạng thái hồ sơ" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderItem = (item, index) => {
    const isCheck = selectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK}}>{item.label}</Text>
        {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView
        style={styles.container}
        ref={scrollViewModalRef}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}>
        <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>{dataBLV.map((item, index) => renderItem(item, index))}</View>
      </ScrollView>
    );
  };
  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      scrollOffset={scrollOffSet}
      swipeDirection={['down']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      scrollOffsetMax={height - flatListHeight} // content height - ScrollView height
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: width,
    height: height / 3,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    margin: 16,
    borderWidth: 1,
    paddingLeft: 16,
    borderRadius: 25,
    borderColor: colors.GRAY,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  modalContentView: {
    marginTop: 10,
    marginHorizontal: 10,
  },
  container: {
    paddingBottom: 20,
    marginHorizontal: 16,
  },
});

export const ModalTrangThaiHoSo = ModalTrangThaiHSComponent;
