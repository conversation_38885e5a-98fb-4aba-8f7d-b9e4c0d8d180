import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CustomTabBar, Empty, Icon, ScreenComponent, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import moment from 'moment';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, RefreshControl, ScrollView, TouchableOpacity, View} from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {useSelector} from 'react-redux';
import styles from './ChiTietHoSoTiepNhanStyles';
import {ChiTietHoSo} from './components';

const ChiTietHoSoTiepNhanScreenComponent = (props) => {
  console.log('ChiTietHoSoTiepNhanScreenComponent');
  const {route, navigation} = props;
  const userInfo = useSelector(selectUser);
  const {id, ma_doi_tac, prevScreen} = route?.params;

  const scrollViewRef = useRef(null);
  const tabViewRef = useRef(null);

  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [dataCacVuTonThat, setdataCacVuTonThat] = useState([]);

  const [btnTabActive, setBtnTabActive] = useState(0);

  const validateBtnHuy = [
    +moment(profileData?.ho_so?.ngay_huy).format('YYYYMMDD') === null,
    +moment(profileData?.ho_so?.ngay_huy).format('YYYYMMDD') === 30000101,
    +moment(profileData?.ho_so?.ngay_huy).format('YYYYMMDD')?.toString().trim() === '',
  ];
  const validateAllBtn = [
    +moment(profileData?.ho_so?.ngay_chuyen).format('YYYYMMDD') === null,
    +moment(profileData?.ho_so?.ngay_chuyen).format('YYYYMMDD') === 30000101,
    +moment(profileData?.ho_so?.ngay_chuyen).format('YYYYMMDD')?.toString().trim() === '',
  ];

  useEffect(() => {
    navigation.addListener('focus', () => {
      initData();
    });
  }, []);
  const initData = async () => {
    await getProfileData(route.params.profileDetail);
  };
  useEffect(() => {
    if (dataCacVuTonThat?.length > 0 && prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT) tabViewRef.current.goToPage(1);
  }, [dataCacVuTonThat]);

  const onRefresh = useCallback(() => {
    getProfileData(profileData);
  }, [profileData]);

  const getProfileData = async (profileDetail, choGoiAPI) => {
    //lấy chi tiết hồ sơ
    try {
      let paramsProfileDetail = {
        ma_doi_tac: ma_doi_tac || profileDetail?.ho_so?.ma_doi_tac || profileDetail?.ma_doi_tac,
        so_id: id || profileDetail?.ho_so?.so_id || profileDetail?.so_id,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GET_PROFILE_DATA, paramsProfileDetail);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setProfileData(response.data_info);
      setdataCacVuTonThat(response.data_info.dien_bien);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onPressHuyHoSo = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn huỷ hồ sơ tiếp nhận', [
      {
        text: 'Huỷ',
        style: 'cancel',
      },
      {text: 'Đồng ý', onPress: () => handleHuyHoSo()},
    ]);
  };
  const onPressGoHuyHoSo = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn gỡ huỷ hồ sơ tiếp nhận', [
      {
        text: 'Huỷ',
        style: 'cancel',
      },
      {text: 'Đồng ý', onPress: () => handleGoHuyHoSo()},
    ]);
  };

  const handleHuyHoSo = async () => {
    try {
      const params = {
        so_id: profileData?.ho_so?.so_id, //id hồ sơ
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_HO_SO_TIEP_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ hồ sơ tiếp nhận thành công', 'success');
      if (prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1) NavigationUtil.pop(2);
      else NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const handleGoHuyHoSo = async () => {
    try {
      const params = {
        so_id: profileData?.ho_so?.so_id, //id hồ sơ
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GO_HUY_HO_SO_TIEP_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Gỡ huỷ hồ sơ tiếp nhận thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  /** RENDER */
  const renderCacVuTonThat = () => {
    return (
      <ScrollView>
        {dataCacVuTonThat.map((item, index) => {
          return (
            <TouchableOpacity
              key={index}
              style={[styles.profileItemView]}
              onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT, {chiTietVuTonThat: item, type: 'EDIT', profileData: profileData})}>
              <View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={[styles.labelItem, {color: colors.PRIMARY}]}>Ngày giờ xảy ra: </Text>
                    <Text style={styles.detailContent}>
                      {item.gio_xr} - <Text style={styles.detailContent}>{item.ngay_xr}</Text>
                    </Text>
                  </View>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Tên lái xe: </Text>
                    <Text style={styles.detailContent}>{item.ten_lxe}</Text>
                  </View>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Điện thoại: </Text>
                    <Text style={styles.detailContent}>{item.dthoai_lxe}</Text>
                  </View>
                </View>
                <View style={styles.contentRow}>
                  <View style={[styles.contentCol, {flex: 1}]}>
                    <Text style={styles.labelItem}>
                      Địa điểm xảy ra: <Text style={[styles.detailContent, , {color: colors.PRIMARY_08}]}>{item.dia_diem_ton_that}</Text>
                    </Text>
                  </View>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>
                      Nguyên nhân: <Text style={styles.detailContent}>{item.nguyen_nhan}</Text>
                    </Text>
                  </View>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>
                      Hậu quả: <Text style={styles.detailContent}>{item.hau_qua}</Text>
                    </Text>
                  </View>
                </View>
              </View>
              <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={styles.rightIcon} color={colors.BLUE1} />
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    );
  };

  const switchButton = () => {
    if (validateBtnHuy.indexOf(true) === -1) {
      return <ButtonLinear title="Gỡ huỷ hồ sơ" onPress={onPressGoHuyHoSo} linearStyle={styles.footerBtn} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />;
    } else {
      return <ButtonLinear title="Huỷ hồ sơ" onPress={onPressHuyHoSo} linearStyle={styles.footerBtn} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />;
    }
  };

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {btnTabActive == 0 && (
          <View style={styles.doubleBtn}>
            {switchButton()}
            {/* <ButtonLinear title="Huỷ hồ sơ" onPress={onPressHuyHoSo} linearStyle={styles.footerBtn} linearColors={[colors.GRAY, colors.GRAY]} textStyle={{color: colors.BLACK_03}} /> */}
            {validateBtnHuy.indexOf(true) !== -1 ? (
              <ButtonLinear title="Khai báo sự vụ" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT, {profileData: profileData})} linearStyle={styles.footerBtn} />
            ) : null}
          </View>
        )}
        {btnTabActive == 1 && validateBtnHuy.indexOf(true) !== -1 ? (
          <View style={styles.doubleBtn}>
            <ButtonLinear title="Khai báo sự vụ" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT, {profileData: profileData})} linearStyle={styles.footerBtn} />
            {dataCacVuTonThat?.length > 0 && (
              <ButtonLinear title="Tiếp theo" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_TT_DIA_DIEM_GIAM_DINH, {profileData: profileData})} linearStyle={[styles.footerBtn]} />
            )}
          </View>
        ) : null}
      </View>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      onPressBack={prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1 ? () => NavigationUtil.pop(2) : () => NavigationUtil.pop()}
      headerTitle={'Chi tiết hồ sơ tiếp nhận'}
      renderView={
        <View style={styles.container}>
          <ScrollableTabView ref={tabViewRef} style={styles.centerView} initialPage={0} onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))} renderTabBar={() => <CustomTabBar />}>
            <ScrollView
              tabLabel="Thông tin hồ sơ"
              scrollEnabled={true}
              refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}
              ref={scrollViewRef}
              showsVerticalScrollIndicator={false}>
              <ChiTietHoSo profileData={profileData} />
            </ScrollView>
            <View tabLabel="Các vụ tổn thất" style={styles.centerView}>
              {dataCacVuTonThat?.length > 0 ? (
                renderCacVuTonThat()
              ) : (
                <View style={styles.emptyView}>
                  <Empty description="Chưa có thông tin" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />
                </View>
              )}
            </View>
          </ScrollableTabView>
          {validateAllBtn.indexOf(true) === -1 ? null : renderFooter()}
        </View>
      }
    />
  );
};

export const ChiTietHoSoTiepNhanScreen = memo(ChiTietHoSoTiepNhanScreenComponent, isEqual);
