import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {Linking, StyleSheet, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import {HEADER_TITLE, ICON_HEADER} from './Constant';

const ChiTietHoSoComponent = (props) => {
  const {profileData} = props;

  const onPressHeader = (headerTitle) => {
    const {ho_so} = profileData;
    if (headerTitle === HEADER_TITLE[13]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAY_CHUNG_NHAN, {
        giayChungNhan: {
          ma_doi_tac: ho_so.ma_doi_tac,
          ma_chi_nhanh: ho_so.ma_chi_nhanh_ql,
          nv: 'XE',
          so_id_hdong: ho_so.so_id_hd,
          so_id_gcn: ho_so.so_id_dt,
          so_gcn: ho_so?.gcn,
        },
        loaiHinhNghiepVu: profileData.lh_nv,
      });
    } else if (headerTitle === HEADER_TITLE[4]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.QUA_TRINH_XL_HS_TIEP_NHAN_BT, {
        profileData,
      });
    }
  };

  /* RENDER */
  // RENDER header các đầu mục
  const renderProfileInformationHeader = (title, data) => {
    let indexIcon = HEADER_TITLE.findIndex((item) => item === title);
    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => onPressHeader(title, data)}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View flexDirection="row">
              <Icon.FontAwesome name={ICON_HEADER[indexIcon]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>{title + ' ' + dataLength}</Text>
            </View>
          </View>
          {title !== HEADER_TITLE[0] && <Icon.SimpleLineIcons name={'arrow-right'} size={15} style={styles.iconBtnTopRightView} />}
        </TouchableOpacity>
        {title !== HEADER_TITLE[0] && (
          <View style={{backgroundColor: colors.WHITE5}}>
            <View style={styles.spacing} />
          </View>
        )}
      </View>
    );
  };
  const renderThongTinChungChidren = (title, data, containerStyle, subValue, style) => (
    <View style={[styles.inforView, containerStyle]}>
      <Text style={styles.txtTitle} children={title} />
      {typeof data === 'number' ? (
        <NumericFormat
          value={data}
          displayType={'text'}
          thousandSeparator={true}
          renderText={(value) => <Text style={styles.txtDetail} selectable children={value + (subValue ? ' (' + subValue + '%)' : '')} />}
        />
      ) : (
        <Text style={[styles.txtDetail, style]} selectable children={data} />
      )}
    </View>
  );
  //render ra thông tin hồ sơ
  const renderProfileInformation = () => {
    if (!profileData) return;
    const {ho_so, dien_bien} = profileData;
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View flex={1}>
          {renderProfileInformationHeader(HEADER_TITLE[0])}
          <View flexDirection="row">
            {renderThongTinChungChidren('Số tiếp nhận', ho_so.so_hs || '', {borderTopLeftRadius: 20, backgroundColor: colors.WHITE})}
            {renderThongTinChungChidren('Số hợp đồng', ho_so.so_hd)}
          </View>
          {renderThongTinChungChidren('Tên chủ xe', ho_so.chu_xe || '')}
          <View flexDirection="row">
            {renderThongTinChungChidren('Người liên hệ', ho_so.nguoi_lhe)}
            {ho_so.dthoai_lhe && (
              <View style={styles.inforView}>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${ho_so.dthoai_lhe}`)}>
                  <Text style={styles.txtTitle}>Điện thoại</Text>
                  <View flexDirection="row" alignItems="center">
                    <Icon.Entypo name="old-phone" size={20} color={colors.PRIMARY} />
                    <Text style={[styles.txtDetail, styles.actionTxt]}>{ho_so.dthoai_lhe || ''}</Text>
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Biển xe', ho_so.doi_tuong || '')}
            {renderThongTinChungChidren('Giấy chứng nhận', ho_so.gcn || '')}
          </View>

          {renderThongTinChungChidren('Thời gian, địa điểm dự kiến giám định', ho_so.dia_diem_gd || ho_so.noi_dung)}
          <View flexDirection="row">
            {renderThongTinChungChidren('Hiện trường', ho_so.hien_truong === 'D' ? 'Xe đang ở hiện trường' : 'Xe không ở hiện trường')}
            <View style={styles.inforView}>
              <TouchableOpacity onPress={() => onPressHeader(HEADER_TITLE[15])}>
                <Text style={styles.txtTitle}>Link chụp ảnh</Text>
                <View flexDirection="row" alignItems="center">
                  <Icon.FontAwesome name="share-alt" size={20} color={colors.PRIMARY} />
                  <Text style={[styles.txtDetail, styles.actionTxt]}>Chia sẻ </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Tên lái xe', dien_bien[0]?.ten_lxe || '')}
            <View style={styles.inforView}>
              <View>
                <Text style={styles.txtTitle}>Điện thoại lái xe</Text>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${dien_bien[0]?.dthoai_lxe}`)} style={styles.phoneRow}>
                  <Icon.Entypo name="old-phone" size={20} color={colors.PRIMARY} />
                  <Text style={[styles.txtDetail, styles.actionTxt]}>{dien_bien[0]?.dthoai_lxe || ''}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Trạng thái', ho_so.trang_thai || '', null, null, {color: colors.GREEN})}
            {renderThongTinChungChidren('Ngày thông báo', ho_so.ngay_tb || '')}
          </View>
        </View>
        <View flexDirection="row">
          {renderThongTinChungChidren('Bồi thường viên', ho_so.btv || '')}
          {renderThongTinChungChidren('Giám định viên', ho_so.ma_gdvht || '')}
        </View>
        {renderThongTinChungChidren('Đơn vị cấp đơn', ho_so.dvi_cap_don || '', {backgroundColor: colors.WHITE, borderBottomLeftRadius: 20, borderBottomWidth: 0})}
        <View flexDirection="row">
          {renderThongTinChungChidren('Giá trị xe', ho_so.gia_tri_xe || '')}
          {renderThongTinChungChidren('Số tiền bảo hiểm vật chất', ho_so.so_tien_bh_vcx || '', {}, ho_so.tl_tham_gia_bh || '')}
        </View>
        {/* GIẤY CHỨNG NHẬN */}
        {renderProfileInformationHeader(HEADER_TITLE[13])}

        {/* QUÁ TRÌNH GIẢI QUYẾT */}
        {renderProfileInformationHeader(HEADER_TITLE[4], profileData.qtxl)}
      </View>
    );
  };

  return <>{renderProfileInformation()}</>;
};

export const ChiTietHoSo = memo(ChiTietHoSoComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    marginTop: 5,
    marginBottom: 60,
    flex: 1,
  },
  iconBtnTopLeftView: {
    marginRight: 15,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    marginHorizontal: 15,
    alignSelf: 'center',
  },
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
  },
  txtTitle: {
    marginBottom: 4,
    fontSize: 12,
  },
  txtDetail: {
    color: colors.GRAY6,
    paddingRight: 10,
    flexShrink: 1,
  },
  inforView: {
    flex: 1,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.smaller,
    borderColor: colors.GRAY4,
    borderBottomWidth: 0.5,
    backgroundColor: colors.WHITE,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: dimensions.width - 20,
  },
  headerTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 5,
    paddingTop: 20,
    marginLeft: 20,
    textDecorationLine: 'underline',
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionTxt: {
    marginLeft: 8,
    color: colors.PRIMARY,
  },
  spacing: {
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
});
