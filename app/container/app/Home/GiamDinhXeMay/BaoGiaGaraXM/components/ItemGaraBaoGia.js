import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {CheckboxComp, Icon, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {spacing} from '@app/theme';
import moment from 'moment';
import React, {forwardRef, memo} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, StyleSheet, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';

const ItemGaraBaoGiaComponent = forwardRef((props, ref) => {
  const {data, profileData, onPressEditGara, onPressRemoveGara, handleCheck, btTrinh} = props;
  const {item, index} = data;
  const bgDaKetThuc = item?.ngay_dong_bg !== null && +moment(item?.ngay_dong_bg, 'DDMMYYYY').format('YYYYMMDD') < 30000101;

  const _handleCheck = (value, it) => {
    if (btTrinh > 0) {
      return Alert.alert('Thông báo', 'Không sửa xoá dữ liệu khi hồ sơ đang TRÌNH hoặc ĐÃ DUYỆT phương án khắc phục!');
    } else if (!bgDaKetThuc) {
      return Alert.alert('Thông báo', 'Báo giá chưa kết thúc!');
    } else {
      handleCheck && handleCheck(value, it);
    }
  };

  /* RENDER */

  return (
    <View
      style={styles.container}
      //  onPress={() => onPressItem(item, index)}
    >
      <View style={styles.titleRow}>
        <TouchableOpacity style={styles.titleRow} onPress={() => _handleCheck(!item.checked, item)}>
          <CheckboxComp
            disabled={btTrinh > 0 || !bgDaKetThuc}
            checkboxStyle={{marginBottom: spacing.none, marginRight: spacing.smaller}}
            value={item.checked}
            onValueChange={(value) => _handleCheck(value, item)}
          />
          <Text children={item?.ten || item.gara.label} style={styles.txtGaraName} />
        </TouchableOpacity>
        {bgDaKetThuc ? <Text children="Đã kết thúc" style={{color: colors.GREEN}} /> : <Text children="Chưa kết thúc" style={{color: colors.RED1}} />}
      </View>
      <View style={styles.garaDetailView}>
        <View style={{flex: 1}}>
          <View style={styles.rowData}>
            <Text children="Đối tượng:" style={[styles.txtLabel]} />
            <Text children={item.ten_doi_tuong} style={[styles.txtValue, {color: colors.PRIMARY, fontWeight: '600'}]} />
          </View>
          <View style={styles.rowData}>
            <Text children="Ngày báo giá:" style={[styles.txtLabel]} />
            <Text children={item.ngay_gio_bg || moment(item.ngayBaoGia).format('DD/MM/YYYY')} style={[styles.txtValue]} />
          </View>
          <View style={styles.rowData}>
            <Text children="Tổng tiền:" style={styles.txtLabel} />
            <NumericFormat value={item.tong_tien || item.tongTien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
          </View>
          <View style={styles.rowData}>
            <Text children="Tổng duyệt:" style={styles.txtLabel} />
            <NumericFormat value={item.tong_duyet || item.tongDuyet || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
          </View>
        </View>
        <View>
          <TouchableOpacity style={{marginBottom: spacing.smaller}} onPress={() => onPressEditGara(item, index)}>
            <Icon.FontAwesome name="edit" color={colors.PRIMARY} size={20} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onPressRemoveGara(item, index)}>
            <Icon.AntDesign name="closesquareo" color={colors.RED1} size={20} />
          </TouchableOpacity>
        </View>
      </View>
      <View style={bgDaKetThuc ? styles.bottomBtnRow : styles.alignSelf}>
        {bgDaKetThuc && (
          <TouchableOpacity
            style={styles.themHangMucView}
            onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_BOI_THUONG_XM, {garaBaoGia: item, indexGara: index, profileData: profileData})}>
            <Text children={'Tính toán bồi thường'} style={styles.txtThemHangMuc} />
            <Icon.AntDesign name="doubleright" size={15} color={colors.PRIMARY} />
          </TouchableOpacity>
        )}
        <TouchableOpacity style={styles.themHangMucView} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.BAO_GIA_HANG_MUC_XM, {garaBaoGia: item, indexGara: index, profileData: profileData})}>
          <Text children={'Chi tiết báo giá'} style={styles.txtThemHangMuc} />
          <Icon.AntDesign name="doubleright" size={15} color={colors.PRIMARY} />
        </TouchableOpacity>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.smaller,
    paddingVertical: spacing.smaller,
    marginBottom: spacing.smaller,
    backgroundColor: '#FFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  themHangMucView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: spacing.smaller,
  },
  txtThemHangMuc: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.PRIMARY,
    paddingRight: spacing.smaller,
  },
  garaDetailView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowData: {
    flexDirection: 'row',
    paddingVertical: spacing.tiny,
  },
  txtLabel: {
    flex: 1,
    color: colors.GRAY6,
  },
  txtValue: {
    flex: 1,
  },
  txtGaraName: {
    flex: 1,
    fontSize: 16,
    color: colors.PRIMARY,
  },
  titleRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bottomBtnRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  alignSelf: {
    alignSelf: 'flex-end',
  },
});
export const ItemGaraBaoGia = memo(ItemGaraBaoGiaComponent, isEqual);
