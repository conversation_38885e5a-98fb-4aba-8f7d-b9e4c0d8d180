import React, {memo, useImperativeHandle, useState, forwardRef} from 'react';
import {StatusBar, StyleSheet, View} from 'react-native';
import isEqual from 'react-fast-compare';
import Modal from 'react-native-modal';
import {dimensions, spacing, vScale} from '@app/theme';
import {ButtonLinear, DropdownPicker, TextInputOutlined} from '@app/components';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import {colors} from '@app/commons/Theme';
import {useDebouncedCallback} from 'use-debounce';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {isIOS} from '@app/commons/Constant';

const ModalGaraComponent = forwardRef(({listGara, onPressChonGara, profileData, onChangeSearchText}, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  const [openChonGara, setOpenChonGara] = useState(false);
  const [openChonDoiTuong, setOpenChonDoiTuong] = useState(false);
  const [garaSelected, setGaraSelected] = useState(null);
  const [doiTuongSelected, setDoiTuongSelected] = useState(null);
  const [errGara, setErrGara] = useState('');
  const [errDropdown, setErrDropdown] = useState(['', '']);

  const [ngayBaoGia, setNgayBaoGia] = useState(new Date());
  const [toggleNgayBaoGia, setToggleNgayBaoGia] = useState(false);

  const [gioBaoGia, setGioBaoGia] = useState(new Date());
  const [toggleGioBaoGia, setToggleGioBaoGia] = useState(false);
  const [dsDoiTuong, setDsDoiTuong] = useState([]);

  const [indexUpdated, setIndexUpdated] = useState(-1); //nếu index > 0 -> là gara dc update

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
    // updateGata: (garaUpdate, index) => {
    //   setIsVisible(true);
    //   setIndexUpdated(index);
    //   setGaraSelected(garaUpdate.gara.value);
    //   setNgayBaoGia(garaUpdate.ngayBaoGia);
    //   setGioBaoGia(garaUpdate.gioBaoGia);
    // },
  }));

  const initDsDoiTuong = () => {
    if (profileData?.ds_doi_tuong?.length > 0) {
      let filter = profileData?.ds_doi_tuong.filter((item) => item.nhom === 'XE' || (item.nhom === 'TAI_SAN' && item.loai === 'XE') || (item.nhom === 'TAI_SAN' && item.loai === 'XE_MAY'));
      filter.map((item, index) => {
        filter[index].label = item.ten_doi_tuong;
        filter[index].value = item.so_id_doi_tuong;
      });
      setDsDoiTuong(filter);
      if (profileData?.ds_doi_tuong?.length == 1) {
        setDoiTuongSelected(filter[0].value);
      }
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetModalData = () => {
    setNgayBaoGia(new Date());
    setGioBaoGia(new Date());
    setGaraSelected(null);
    setOpenChonGara(false);
    setToggleGioBaoGia(false);
    setErrGara('');
    setIndexUpdated(-1);
  };

  const onChangeValueDoiTuong = () => {
    let errDropdownTmp = errDropdown;
    errDropdownTmp[0] = '';
    setErrDropdown([...errDropdownTmp]);
  };
  const onChangeValueGara = () => {
    let errDropdownTmp = errDropdown;
    errDropdownTmp[1] = '';
    setErrDropdown([...errDropdownTmp]);
  };

  const onPressLuu = () => {
    let haveErr = false;
    let errDropdownTmp = errDropdown;
    if (!doiTuongSelected) {
      errDropdownTmp[0] = 'Vui lòng chọn đối tượng';
      haveErr = true;
    }
    if (!garaSelected) {
      errDropdownTmp[1] = 'Vui lòng chọn Gara';
      haveErr = true;
    }
    setErrDropdown([...errDropdownTmp]);
    if (haveErr) return;
    let garaSelectedData = null;
    listGara.map((gara) => {
      if (gara.value == garaSelected) {
        garaSelectedData = gara;
        return;
      }
    });
    let garaBaoGia = {
      doiTuong: doiTuongSelected,
      gara: garaSelectedData,
      ngayBaoGia: ngayBaoGia,
      gioBaoGia: gioBaoGia,
    };
    // console.log('garaBaoGia', garaBaoGia, indexUpdated);
    onPressChonGara(garaBaoGia, indexUpdated >= 0 ? indexUpdated : undefined);
    setIsVisible(false);
    resetModalData();
  };

  const debounced = useDebouncedCallback((value) => {
    onChangeSearchText(value);
  }, 500);

  /* RENDER */
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime)}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );
  return (
    <Modal
      onModalShow={initDsDoiTuong}
      isVisible={isVisible}
      onSwipeComplete={() => {
        setIsVisible(false);
        resetModalData();
      }}
      onBackdropPress={() => {
        setIsVisible(false);
        resetModalData();
      }}
      swipeDirection={['down']}
      propagateSwipe={true}
      style={styles.modal}
      avoidKeyboard={true}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <View style={styles.headerRow} />
        </View>
        <DropdownPicker
          searchable={false}
          title="Chọn đối tượng tổn thất"
          zIndex={9001}
          isOpen={openChonDoiTuong}
          setOpen={setOpenChonDoiTuong}
          items={dsDoiTuong}
          itemSelected={doiTuongSelected}
          setItemSelected={setDoiTuongSelected}
          onChangeValue={onChangeValueDoiTuong}
          placeholder="Chọn đối tượng tổn thất"
          containerStyle={{marginBottom: spacing.tiny}}
          isRequired={true}
          inputErr={errDropdown[0]}
          maxHeight={dimensions.height / 4}
        />
        <DropdownPicker
          title="Gara báo giá"
          zIndex={9000}
          isOpen={openChonGara}
          setOpen={setOpenChonGara}
          items={listGara}
          itemSelected={garaSelected}
          setItemSelected={setGaraSelected}
          onChangeValue={onChangeValueGara}
          placeholder="Chọn gara"
          containerStyle={{marginBottom: spacing.tiny}}
          isRequired={true}
          inputErr={errDropdown[1]}
          maxHeight={dimensions.height / 4}
          onChangeSearchText={debounced}
        />

        <View style={styles.dateTimeRow}>
          <View flex={1}>
            <TextInputOutlined
              title="Giờ báo giá"
              isTouchableOpacity={true}
              onPress={() => setToggleGioBaoGia(true)}
              value={moment(gioBaoGia).format('HH:mm')}
              editable={false}
              isRequired={true}
              isDateTimeField
              inputStyle={{color: colors.BLACK}}
            />
            {renderDateTimeComp(toggleGioBaoGia, setToggleGioBaoGia, setGioBaoGia, gioBaoGia, 'time', null, null)}
          </View>
          <View flex={1}>
            <TextInputOutlined
              title="Ngày bắt đầu"
              isTouchableOpacity={true}
              onPress={() => setToggleNgayBaoGia(true)}
              value={moment(ngayBaoGia).format('DD/MM/YYYY')}
              editable={false}
              isRequired={true}
              containerStyle={{marginLeft: spacing.smaller}}
              isDateTimeField
              inputStyle={{color: colors.BLACK}}
            />
            {renderDateTimeComp(toggleNgayBaoGia, setToggleNgayBaoGia, setNgayBaoGia, ngayBaoGia, 'date', null, null)}
          </View>
        </View>
        <View style={styles.btnView}>
          <ButtonLinear
            title="Đóng"
            linearStyle={styles.btnLuu}
            onPress={() => {
              setIsVisible(false);
              resetModalData();
            }}
            isSubBtn
          />
          <ButtonLinear title={indexUpdated < 0 ? 'Lưu' : 'Cập nhật'} linearStyle={[styles.btnLuu, {marginLeft: spacing.smaller}]} onPress={onPressLuu} />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    margin: 0,
  },
  btnView: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: vScale(30),
    left: 12,
    right: 12,
  },
  modalContent: {
    height: dimensions.height * 0.9,
    backgroundColor: '#FFF',
    borderWidth: 1,
    width: dimensions.width,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: spacing.small,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    width: dimensions.width / 5,
    height: 7,
    backgroundColor: colors.GRAY,
    borderRadius: 20,
  },
});
export const ModalGara = memo(ModalGaraComponent, isEqual);
