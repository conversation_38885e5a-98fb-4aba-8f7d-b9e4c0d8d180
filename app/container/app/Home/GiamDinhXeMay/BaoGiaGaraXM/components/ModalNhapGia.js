import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, TextInputOutlined, Text} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import Modal from 'react-native-modal';
import {Switch} from 'react-native-switch';

const ModalNhapGiaComponent = forwardRef(({listGara, onBackPress, hangMucSelected, index, updateData}, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  // const [giaHopTacGara, setGiaHopTacGara] = useState('');
  const [tongTienVatTuGara, setTongTienVatTuGara] = useState('');
  const [nhanCongGara, setNhanCongGara] = useState('');
  const [tienSonGara, setTienSonGara] = useState('');

  const [tienVatTuDeXuat, setTienVatTuDeXuat] = useState('');
  const [nhanCongDeXuat, setNhanCongDeXuat] = useState('');
  const [tienSonDeXuat, setTienSonDeXuat] = useState('');

  const [phuongAnKhacPhucSelected, setPhuongAnKhacPhucSelected] = useState('S');
  const [errInput, setErrInput] = useState(['', '', '']);
  // const [err, setErr] = useState([false, false, false]);
  const [autoFill, setAutoFill] = useState(true);
  const [disabledBtnLuu, setDisabledBtnLuu] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
    // updateGata: (garaUpdate, index) => {
    //   setIsVisible(true);
    //   setIndexUpdated(index);
    //   setGaraSelected(garaUpdate.gara.value);
    //   setNgayBaoGia(garaUpdate.ngayBaoGia);
    //   setGioBaoGia(garaUpdate.gioBaoGia);
    // },
  }));

  const initModalData = () => {
    if (hangMucSelected) {
      // console.log('route.params.hangMucBaoGia', route.params.hangMucBaoGia);
      // setGiaHopTacGara(hangMucSelected.gia_giam_dinh);
      setPhuongAnKhacPhucSelected(hangMucSelected.thay_the_sc);

      // setTongTienVatTuGara(hangMucSelected.tien_vtu);
      // setNhanCongGara(hangMucSelected.tien_nhan_cong);
      // setTienSonGara(hangMucSelected.tien_khac);

      // setTongTienVatTuDeXuat(hangMucSelected.tien_vtu_dx);
      // setNhanCongDeXuat(hangMucSelected.tien_nhan_cong_dx);
      // setTienSonDeXuat(hangMucSelected.tien_khac_dx);

      setTongTienVatTuGara(hangMucSelected.tien_vtu > 0 ? hangMucSelected.tien_vtu : '');
      setNhanCongGara(hangMucSelected.tien_nhan_cong > 0 ? hangMucSelected.tien_nhan_cong : '');
      setTienSonGara(hangMucSelected.tien_khac > 0 ? hangMucSelected.tien_khac : '');

      setTienVatTuDeXuat(hangMucSelected.tien_vtu_dx > 0 ? hangMucSelected.tien_vtu_dx : '');
      setNhanCongDeXuat(hangMucSelected.tien_nhan_cong_dx > 0 ? hangMucSelected.tien_nhan_cong_dx : '');
      setTienSonDeXuat(hangMucSelected.tien_khac_dx > 0 ? hangMucSelected.tien_khac_dx : '');
    }
  };

  useEffect(() => {
    // let errTmp = err;
    // let errInputTmp = errInput;

    // if (+tongTienVatTuDeXuat > 0 && +tongTienVatTuDeXuat > +tongTienVatTuGara) {
    //   errInputTmp[0] = ' ';
    //   errTmp[0] = true;
    // } else {
    //   errInputTmp[0] = '';
    //   errTmp[0] = false;
    // }
    // if (+nhanCongDeXuat > 0 && +nhanCongDeXuat > +nhanCongGara) {
    //   errInputTmp[1] = ' ';
    //   errTmp[1] = true;
    // } else {
    //   errInputTmp[1] = '';
    //   errTmp[1] = false;
    // }
    // if (+tienSonDeXuat > 0 && +tienSonDeXuat > +tienSonGara) {
    //   errInputTmp[2] = ' ';
    //   errTmp[2] = true;
    // } else {
    //   errInputTmp[2] = '';
    //   errTmp[2] = false;
    // }
    // setErr([...errTmp]);
    // setErrInput([...errInputTmp]);
    let tongTienGaraBaoGia = +tongTienVatTuGara + +nhanCongGara + +tienSonGara;
    let tongTienDeXuat = +tienVatTuDeXuat + +nhanCongDeXuat + +tienSonDeXuat;
    if (tongTienDeXuat > tongTienGaraBaoGia) setDisabledBtnLuu(true);
    else setDisabledBtnLuu(false);
  }, [tienSonDeXuat, nhanCongDeXuat, tienVatTuDeXuat, tienSonGara, nhanCongGara, tongTienVatTuGara]);

  const onPressLuu = () => {
    if (disabledBtnLuu) return;
    let hangMucBaoGia = hangMucSelected;

    hangMucBaoGia.tien_vtu = +tongTienVatTuGara || 0;
    hangMucBaoGia.tien_nhan_cong = +nhanCongGara || 0;
    hangMucBaoGia.tien_khac = +tienSonGara || 0;
    hangMucBaoGia.tong_tien_gara_bg = +tongTienVatTuGara + +nhanCongGara + +tienSonGara || 0;

    hangMucBaoGia.tien_dx = +tienVatTuDeXuat + +nhanCongDeXuat + +tienSonDeXuat || 0;
    hangMucBaoGia.tien_vtu_dx = +tienVatTuDeXuat || 0;
    hangMucBaoGia.tien_nhan_cong_dx = +nhanCongDeXuat || 0;
    hangMucBaoGia.tien_khac_dx = +tienSonDeXuat || 0;

    updateData && updateData(hangMucBaoGia, index);
    setIsVisible(false);
  };

  // onChangeText GARA

  const onChangeTongTienVatTuGara = (value) => {
    if (value < 0) setTongTienVatTuGara(0);
    else {
      setTongTienVatTuGara(value);
      if (autoFill) {
        setTienVatTuDeXuat(value);
      }
    }
  };
  const onChangeNhanCongGara = (value) => {
    if (value < 0) setNhanCongGara(0);
    else {
      setNhanCongGara(value);
      if (autoFill) {
        setNhanCongDeXuat(value);
      }
    }
  };
  const onChangeTienSonGara = (value) => {
    if (value < 0) setTienSonGara(0);
    else {
      setTienSonGara(value);
      if (autoFill) {
        setTienSonDeXuat(value);
      }
    }
  };
  // onChangeText Đề Xuất
  const onChangeTongTienVatTuDeXuat = (value) => {
    if (value < 0) setTienVatTuDeXuat(0);
    else setTienVatTuDeXuat(value);
  };
  const onChangeNhanCongDeXuat = (value) => {
    if (value < 0) setNhanCongDeXuat(0);
    else setNhanCongDeXuat(value);
  };
  const onChangeTienSonDeXuat = (value) => {
    if (value < 0) setTienSonDeXuat(0);
    else setTienSonDeXuat(value);
  };

  /* RENDER */

  const textAlignRight = {textAlign: 'right'};

  const renderTienBaoGiaGara = () => (
    <View style={styles.blockSubForm}>
      {/* <View flexDirection="row" marginBottom={10}>
        <TextInputOutlined keyboardType="numeric" title="Giá hợp tác" containerStyle={styles.inputContainer} value={giaHopTacGara} disabled={true} editable={false} placeholder="0" />
      </View> */}
      <Text children="Tiền gara báo giá" style={styles.blockTitle} />
      <View flexDirection="row">
        <TextInputOutlined
          keyboardType="numeric"
          title="Tiền vật tư"
          onChangeText={onChangeTongTienVatTuGara}
          containerStyle={[styles.inputContainer]}
          value={tongTienVatTuGara?.toString()}
          disabled={phuongAnKhacPhucSelected === 'S' ? true : false}
          editable={phuongAnKhacPhucSelected === 'S' ? false : true}
          // onBlur={() => !tongTienVatTuGara.trim() && setTongTienVatTuGara(0)}
          placeholder="0"
          inputStyle={textAlignRight}
        />
        <TextInputOutlined
          keyboardType="numeric"
          title="Nhân công"
          placeholder="0"
          onChangeText={onChangeNhanCongGara}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          value={nhanCongGara}
          inputStyle={textAlignRight}
          // onBlur={() => !nhanCongGara.trim() && setNhanCongGara(0)}
        />
        <TextInputOutlined
          keyboardType="numeric"
          title="Tiền sơn"
          placeholder="0"
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          value={tienSonGara}
          onChangeText={onChangeTienSonGara}
          inputStyle={textAlignRight}
          // onBlur={() => !tienSonGara.trim() && setTienSonGara(0)}
        />
      </View>
    </View>
  );
  const renderTienDeXuat = () => (
    <View style={styles.blockSubForm}>
      {/* <Text children="Tiền đề xuất duyệt" style={styles.blockTitle} /> */}
      <View style={styles.titleRow}>
        <Text children="Tiền đề xuất duyệt" style={styles.blockTitle} />
        <View style={styles.titleRow} marginTop={10}>
          <Text style={{marginRight: 5, color: colors.GRAY6}} children="Tự động điền" />
          <Switch
            value={autoFill}
            onValueChange={setAutoFill}
            disabled={false}
            activeText={''}
            inActiveText={''}
            circleSize={16}
            barHeight={24}
            circleBorderWidth={0}
            // màu khi active
            circleActiveColor={colors.WHITE}
            backgroundActive={colors.PRIMARY}
            //màu khi InActive
            circleInActiveColor={colors.WHITE}
            backgroundInactive={colors.GRAY}
            // renderInsideCircle={() => <CustomComponent />} // custom component to render inside the Switch circle (Text, Image, etc.)
            changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
            innerCircleStyle={{alignItems: 'center', justifyContent: 'center'}} // style for inner animated circle for what you (may) be rendering inside the circle
            outerCircleStyle={{}} // style for outer animated circle
            renderActiveText={true}
            renderInActiveText={true}
            switchLeftPx={2} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
            switchRightPx={2} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
            switchWidthMultiplier={3} // multipled by the `circleSize` prop to calculate total width of the Switch
            switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
          />
        </View>
      </View>
      <View flexDirection="row">
        <TextInputOutlined
          keyboardType="numeric"
          title="Tiền vật tư"
          placeholder="0"
          error={errInput[0]}
          containerStyle={[styles.inputContainer]}
          value={tienVatTuDeXuat}
          onChangeText={onChangeTongTienVatTuDeXuat}
          disabled={phuongAnKhacPhucSelected === 'S' ? true : false}
          editable={phuongAnKhacPhucSelected === 'S' ? false : true}
          inputStyle={textAlignRight}
          // onBlur={() => !tongTienVatTuDeXuat.trim() && setTongTienVatTuDeXuat(0)}
        />
        <TextInputOutlined
          keyboardType="numeric"
          title="Nhân công"
          placeholder="0"
          error={errInput[1]}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          value={nhanCongDeXuat}
          onChangeText={onChangeNhanCongDeXuat}
          inputStyle={textAlignRight}
          // onBlur={() => !nhanCongDeXuat.trim() && setNhanCongDeXuat(0)}
        />
        <TextInputOutlined
          keyboardType="numeric"
          title="Tiền sơn"
          placeholder="0"
          error={errInput[2]}
          onChangeText={onChangeTienSonDeXuat}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          value={tienSonDeXuat}
          inputStyle={textAlignRight}
          // onBlur={() => !tienSonDeXuat.trim() && setTienSonDeXuat('')}
        />
      </View>
      {disabledBtnLuu && <Text children="Tổng số tiền đề xuất duyệt không được lớn hơn tổng số tiền gara báo giá" style={styles.errText} />}
    </View>
  );

  return (
    <Modal
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onModalWillShow={initModalData}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      style={styles.modal}
      avoidKeyboard={true}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <View style={styles.headerRow} />
        </View>
        <View marginTop={10}>
          <Text style={styles.title}>{hangMucSelected.ten_hang_muc}</Text>
          {renderTienBaoGiaGara()}
          {renderTienDeXuat()}
        </View>
        <View flexDirection="row" marginTop={30}>
          <ButtonLinear
            title="Đóng"
            linearStyle={styles.btnLuu}
            onPress={() => {
              setIsVisible(false);
            }}
            linearColors={[colors.GRAY, colors.GRAY]}
            textStyle={{color: colors.BLACK_03}}
          />
          <ButtonLinear title={'Lưu'} linearStyle={[styles.btnLuu, {marginLeft: spacing.smaller}]} onPress={onPressLuu} />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    margin: 0,
  },
  btnView: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 30,
    left: 10,
    right: 10,
  },
  modalContent: {
    // flex: 1,
    borderWidth: 1,
    backgroundColor: '#FFF',
    width: dimensions.width,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: dimensions.height * 0.9,
    paddingHorizontal: spacing.small,
    marginTop: isIOS ? getStatusBarHeight() : 0,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    width: dimensions.width / 5,
    height: 7,
    // borderWidth: 1,
    backgroundColor: colors.GRAY,
    borderRadius: 20,
  },
  btnLuu: {
    // marginTop: spacing.smaller,
  },
  inputContainer: {
    flex: 1,
    marginBottom: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
    // paddingBottom :
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.RED1,
    marginVertical: 10,
  },
  errText: {
    color: colors.RED1,
    marginBottom: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
export const ModalNhapGia = memo(ModalNhapGiaComponent, isEqual);
