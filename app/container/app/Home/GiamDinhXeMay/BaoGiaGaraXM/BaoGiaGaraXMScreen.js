import {FORMAT_DATE_TIME, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@component';
import moment from 'moment';
import React, {createRef, memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import ActionSheet from 'react-native-actionsheet';
import {useSelector} from 'react-redux';
import styles from './BaoGiaGaraXMStyle';
import {ItemGaraBaoGia, ModalGara} from './components';

let timer;

const BaoGiaGaraXMScreenComponent = (props) => {
  console.log('BaoGiaGaraXMScreenComponent');
  const {route, navigation} = props;
  const {profileData, listHangMuc} = route?.params;
  const userInfo = useSelector(selectUser);

  const [btGara, setBtGara] = useState('');
  const [btTrinh, setBtTrinh] = useState('');
  const [arrName, setArrName] = useState([]);
  const [listGara, setListGara] = useState([]);
  const [garaBaoGia, setGaraBaoGia] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [indexSelected, setIndexSelected] = useState(-1);
  const [listGaraBaoGia, setListGaraBaoGia] = useState([]);

  const showBtnHuy = btTrinh > 0;

  let actionSheetRef = createRef();
  let refModalGara = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      initData();
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const initData = async () => {
    await getDanhSachGaraBaoGia();
    getListGara();
  };

  const onPressOCRBaoGia = () => {
    if (listGaraBaoGia.length <= 0) return FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa có Gara báo giá');
    // if(profileData?.ho_so?.ma_trang_thai === MA_TRANG_THAI.HSBT_XE_BT_TRINH_GIA)
    actionSheetRef.show();
  };

  const getDanhSachGaraBaoGia = async () => {
    setIsLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DANH_SACH_GARA_BAO_GIA_XM, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info.gara;
      data.map((item, index) => {
        if (item.chon_pa_trinh === 'C') {
          data[index].checked = false;
        } else data[index].checked = true;
      });
      let filter = data.filter((item) => item.chon_pa_trinh === 'D');
      const mapName = data.map((item) => {
        return item.ten;
      });
      setArrName(mapName);
      setListGaraBaoGia([...data]);
      setBtTrinh(filter[0]?.bt_trinh_pa);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  useEffect(() => {
    if (listHangMuc && listGaraBaoGia.length > 0) {
      let listGaraBaoGiaTmp = listGaraBaoGia;
      listGaraBaoGiaTmp.forEach((item, index) => {
        let garaBaoGia = listGaraBaoGiaTmp[index];
        garaBaoGia.listHangMuc = listHangMuc;
        garaBaoGia.tongTien = 0;
        garaBaoGia.tongDuyet = 0;
        garaBaoGia.listHangMuc.map((item) => {
          garaBaoGia.tongTien += +item.tongTienVatTuGara + +item.nhanCongGara + +item.tienSonGara;
          garaBaoGia.tongDuyet += +item.tongTienVatTuDeXuat + +item.nhanCongDeXuat + +item.tienSonDeXuat;
        });
        listGaraBaoGiaTmp[route.params.indexGara] = garaBaoGia;
        setListGaraBaoGia([...listGaraBaoGiaTmp]);
      });
    }
  }, [listGaraBaoGia.length]);

  // Remove Gara
  const onPressRemoveGara = (item, index) => {
    Alert.alert('Xoá Gara báo giá', 'Bạn có chắc chắn muốn xoá Gara báo giá này không?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: () => {
          onXoaGaraBaoGia(item);
        },
      },
    ]);
  };

  const onPressEditGara = (item, index) => {
    refModalGara.current.show();
    setBtGara(item.bt_gara);
  };

  const onSave = (garaSelected) => {
    timer = setTimeout(() => {
      onThemGaraBaoGia(garaSelected);
    }, 500);
  };

  const onThemGaraBaoGia = async (garaSelected, type) => {
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        so_id_doi_tuong: garaSelected?.doiTuong,
        bt_gara: btGara || '',
        gara: garaSelected?.gara?.value,
        gio_bg: moment(garaSelected.gioBaoGia).format('HH:mm'),
        ngay_bg: +moment(garaSelected.ngayBaoGia).format(FORMAT_DATE_TIME.API_DATE_FORMAT),
        pm: 'BT',
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.THEM_GARA_BAO_GIA_XM, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      getDanhSachGaraBaoGia();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  const onXoaGaraBaoGia = async (item) => {
    let params = {
      so_id: profileData?.ho_so?.so_id,
      so_id_doi_tuong: item?.so_id_doi_tuong,
      bt_gara: item?.bt_gara,
      gara: item?.gara,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_GARA_BAO_GIA_XM, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      getDanhSachGaraBaoGia();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onPressAddGara = () => {
    refModalGara.current.show();
    setBtGara('');
  };

  const onPressItemGara = (item) => {
    setGaraBaoGia(item);
    // console.log('🚀 ~ file: BaoGiaGaraScreen.js ~ line 172 ~ onPressItemGara ~ item', item);
  };

  // const onSubmitHuyTrinh = () => {
  //   Alert.alert('Thông báo', 'Bạn có chắc chắn HUỶ TRÌNH phương án báo giá này?', [
  //     {text: 'Huỷ'},
  //     {
  //       text: 'Đồng ý',
  //       onPress: async () => {
  //         let params = {
  //           ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
  //           so_id: profileData?.ho_so?.so_id,
  //           bt: btTrinh,
  //           ma_dt_trinh: null,
  //           remove_file: MA_MAU_IN.TRINH_PHUONG_AN,
  //         };
  //         try {
  //           let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_TRINH_BAO_LANH, params);
  //           if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //           FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ trình phương án báo giá thành công', 'success');
  //           getDanhSachGaraBaoGia();
  //         } catch (error) {
  //           Alert.alert('Thông báo', error.message);
  //         }
  //         return;
  //       },
  //     },
  //   ]);
  // };

  const handleCheck = async (value, item) => {
    let isCheck = '';
    if (value) isCheck = 'D';
    else isCheck = 'C';
    let params = {
      so_id: profileData?.ho_so?.so_id,
      ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      so_id_pa: item.so_id_pa,
      chon: isCheck,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHON_PA_BAO_GIA_XM, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK')
        Alert.alert('Thông báo', JSON.stringify(response.state_info.message_body), [{text: 'OK', onPress: getDanhSachGaraBaoGia}]);
      else if (response.state_info?.status === AxiosConfig.SERVER_RESPONSE_STATUS.OK) getDanhSachGaraBaoGia();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  //lấy dữ liệu gara
  const getListGara = async (searchInput, hopTac) => {
    let params = {
      hop_tac: hopTac || '',
      ten: searchInput || '',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LIST_GARA, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let listGara = [];
      // const listGaraFilter = response.data_info.filter((e) => e.nv === 'XE_MAY');
      const listGaraFilter = response.data_info;
      listGara = listGaraFilter.map((item) => {
        let gara = {
          label: item.ten || item.ten_tat || '',
          value: item.ma,
        };
        return gara;
      });
      setListGara(listGara.slice(0, 20));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  /** RENDER */
  const renderListGaraBaoGia = () => (
    <FlatList
      data={listGaraBaoGia}
      keyExtractor={(item, index) => index.toString()}
      ListEmptyComponent={<Empty description="Danh sách Gara báo giá trống" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
      renderItem={(data) => (
        <ItemGaraBaoGia
          data={data}
          btTrinh={btTrinh}
          handleCheck={handleCheck}
          profileData={profileData}
          indexSelected={indexSelected}
          onPressItemGara={onPressItemGara}
          setIndexSelected={setIndexSelected}
          onPressRemoveGara={(item) => onPressRemoveGara(item)}
          onPressEditGara={(item, index) => onPressEditGara(item, index)}
        />
      )}
      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={getDanhSachGaraBaoGia} />}
    />
  );

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {/* {showBtnHuy ? (
          <ButtonLinear
            loading={false}
            title="Huỷ trình phương án"
            onPress={onSubmitHuyTrinh}
            // linearColors={[colors.GRAY2, colors.GRAY2]}
            linearStyle={styles.footerBtn}
            // textStyle={{color: colors.BLACK_03}}
          />
        ) : (
          <View flexDirection="row" flex={1}>
            <ButtonLinear loading={false} title="OCR báo giá" onPress={() => actionSheetRef.show()} linearStyle={styles.footerBtn} />
            <ButtonLinear loading={false} title="Thêm Gara báo giá" onPress={onPressAddGara} linearStyle={styles.footerBtn} />
            {listGaraBaoGia.length > 0 ? (
              <ButtonLinear
                loading={false}
                title="Trình phương án"
                onPress={() =>
                  NavigationUtil.push(SCREEN_ROUTER_APP.TRINH_PHUONG_AN, {
                    profileInfo: profileData.ho_so,
                    maMauIn: MA_MAU_IN.TRINH_PHUONG_AN,
                    loaiTrinh: 'XE_TRINH_DUYET_DUYET_GIA',
                    garaBaoGia: garaBaoGia,
                  })
                }
                linearStyle={styles.footerBtn}
              />
            ) : (
              <ButtonLinear loading={false} title="Thêm Gara báo giá" onPress={onPressAddGara} linearStyle={styles.footerBtn} />
            )}
          </View>
        )} */}
        <ButtonLinear loading={false} title="OCR báo giá" onPress={onPressOCRBaoGia} linearStyle={{marginRight: spacing.small}} />
        <ButtonLinear loading={false} title="Thêm Gara báo giá" onPress={onPressAddGara} />
      </View>
    );
  };

  const onActionPress = (index) => {
    if (index === listGaraBaoGia.length) return;
    let gara = listGaraBaoGia[index];
    NavigationUtil.push(SCREEN_ROUTER_APP.OCR_BAO_GIA_XM, {
      profileInfo: profileData?.ho_so,
      listGara: listGaraBaoGia,
      gara: gara,
      listHangMuc: listHangMuc,
    });
  };

  const renderActionSheetGiamDinh = () => (
    <ActionSheet
      ref={(o) => (actionSheetRef = o)}
      title={'Chọn Gara báo giá'}
      cancelButtonIndex={listGaraBaoGia.length}
      destructiveButtonIndex={listGaraBaoGia.length}
      onPress={(index) => onActionPress(index)}
      options={[...arrName, 'Để sau']}
    />
  );

  return (
    <ScreenComponent
      dialogLoading={isLoading}
      headerBack
      headerTitle="Báo giá gara xe máy"
      renderView={
        <SafeAreaView style={styles.container}>
          <View style={styles.headerTitleView}>
            <Text style={styles.subLabel}>Danh sách Gara báo giá</Text>
            <TouchableOpacity style={styles.btnAddGara} onPress={onPressAddGara}>
              <Text children="Thêm Gara báo giá" style={styles.txtAddGara} />
              <Icon.FontAwesome name="angle-right" size={20} color={colors.PRIMARY} />
            </TouchableOpacity>
          </View>
          {renderListGaraBaoGia()}
          {renderActionSheetGiamDinh()}
          <ModalGara ref={refModalGara} profileData={profileData} listGara={listGara} onPressChonGara={onSave} onChangeSearchText={(val) => getListGara(val)} />
        </SafeAreaView>
      }
      footer={renderFooter()}
    />
  );
};
export const BaoGiaGaraXMScreen = memo(BaoGiaGaraXMScreenComponent, isEqual);
