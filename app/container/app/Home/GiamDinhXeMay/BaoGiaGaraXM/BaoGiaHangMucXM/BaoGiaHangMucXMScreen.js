import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Empty, ScreenComponent, Text} from '@component';
import moment from 'moment';
import React, {createRef, memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, SafeAreaView, View} from 'react-native';
import ActionSheet from 'react-native-actionsheet';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {ModalShowNote} from '../../../BaoLanhVienPhi/ThongTinBaoLanh/Components/ModalShowNote';
import {ModalNhapGia} from '../components/ModalNhapGia';
import styles from './BaoGiaHangMucXMStyle';
import {ItemHangMuc} from './components';

const BaoGiaHangMucXMScreenComponent = ({route}) => {
  console.log('BaoGiaHangMucXMScreenComponent');
  const userInfo = useSelector(selectUser);
  const {garaBaoGia, profileData} = route?.params;

  const [listHangMuc, setListHangMuc] = useState([]);
  const [note, setNote] = useState('');
  const [item, setItem] = useState({});
  const [index, setIndex] = useState(-1);
  const [loading, setLoading] = useState(false);
  const [tongTienDuyet, setTongTienDuyet] = useState(0);
  const [tongTienGaraBaoGia, setTongTienGaraBaoGia] = useState(0);
  const [isChangedData, setIsChangedData] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);

  let actionSheetRef = createRef();
  let refModalNhapGia = useRef(null);
  let refModalShowNote = useRef(null);

  const showBtnHuy = garaBaoGia?.ngay_dong_bg !== null && +moment(garaBaoGia?.ngay_dong_bg, 'DDMMYYYY').format('YYYYMMDD') < 30000101;

  useEffect(() => {
    garaBaoGia?.listHangMuc && setListHangMuc(garaBaoGia.listHangMuc);
    xemGaraBaoGiaChiTiet();
  }, []);

  const xemGaraBaoGiaChiTiet = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        gara: garaBaoGia?.gara || '',
        bt_gara: garaBaoGia?.bt_gara || '',
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
        so_id_doi_tuong: garaBaoGia?.so_id_doi_tuong || '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XEM_CHI_TIET_BAO_GIA_GARA_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListHangMuc(response.data_info.gara_ct);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  useEffect(() => {
    // console.log('BaoGiaHangMucScreen route', route.params);
    updateListData();
  }, [route.params]);

  const updateListData = () => {
    if (route.params?.hangMucBaoGia) {
      let hangMucBaoGia = route.params?.hangMucBaoGia;
      //xử lý để đồng khi 1 giảm giá được checked thì các hạng mục khác cũng có giảm giá bằng cái được checked
      let listHangMucTmp = listHangMuc;
      listHangMucTmp.map((hangMuc) => {
        if (hangMucBaoGia.checkGiamGiaVatTu == true) {
          hangMuc.checkGiamGiaVatTu = true;
          hangMuc.vatTuGiamGia = hangMucBaoGia.vatTuGiamGia;
        } else hangMuc.checkGiamGiaVatTu = false;
        if (hangMucBaoGia.checkGiamGiaNhanCong == true) {
          hangMuc.checkGiamGiaNhanCong = true;
          hangMuc.nhanCongGiamGia = hangMucBaoGia.nhanCongGiamGia;
        } else hangMuc.checkGiamGiaNhanCong = false;
        if (hangMucBaoGia.checkGiamGiaSon == true) {
          hangMuc.checkGiamGiaNhanCong = true;
          hangMuc.tienSonGiamGia = hangMucBaoGia.tienSonGiamGia;
        } else hangMucBaoGia.checkGiamGiaSon = false;
        return hangMuc;
      });
      //nếu k có vị trí -> là thêm mới
      if (route.params?.index == undefined) listHangMucTmp.unshift(hangMucBaoGia);
      //nếu có vị trí thì là cập nhật
      else if (route.params?.index != undefined) listHangMucTmp[route.params.index] = hangMucBaoGia;
      setListHangMuc([...listHangMucTmp]);
      setIsChangedData(true);
    }
  };

  const onModalUpdateData = (item, index) => {
    let listHangMucTmp = listHangMuc;
    listHangMucTmp[index] = item;
    setListHangMuc([...listHangMucTmp]);
    setIsChangedData(true);
  };

  useEffect(() => {
    handleSum();
  }, [listHangMuc]);

  // const onPressRemoveHangMuc = (index) => {
  //   let tmp = listHangMuc;
  //   tmp.splice(index, 1);
  //   setListHangMuc([...tmp]);
  // };
  const onPressXacNhan = (type) => {
    // let routes = navigation.getState().routes;
    // NavigationUtil.updateParams(routes[routes.length - 2].key, {listHangMuc: listHangMuc, indexGara: route.params.indexGara}); //cập nhật tham số cho màn Chụp ảnh
    // NavigationUtil.pop(1);
    if (type == 'ket_thuc') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn muốn KẾT THÚC báo giá với Gara này không?', [
        {
          text: 'Huỷ',
        },
        {text: 'Đồng ý', onPress: () => ketThucBaoGia()},
      ]);
    } else {
      Alert.alert('Thông báo', 'Bạn có chắc chắn muốn LƯU và KẾT THÚC báo giá với Gara này không?', [
        {
          text: 'Huỷ',
        },
        {text: 'Đồng ý', onPress: () => handleLuu(type)},
      ]);
    }
  };

  const handleSum = () => {
    let tongTien = 0;
    let tongDuyet = 0;
    listHangMuc.map((item) => {
      let sumTienDuyet = item?.tien_dx;
      let sumTienGaraBaoGia = item?.tien_vtu + item.tien_nhan_cong + item.tien_khac;
      tongTien += sumTienGaraBaoGia;
      tongDuyet += sumTienDuyet;
    });
    setTongTienDuyet(tongDuyet);
    setTongTienGaraBaoGia(tongTien);
  };

  const handleLuu = async (type) => {
    setLoading(true);
    let arr = [];
    listHangMuc.map((e) => {
      let json = {
        hang_muc: e.hang_muc,
        muc_do: e.muc_do,
        thay_the_sc: e.thay_the_sc,
        loai_hang_muc: e.loai_hang_muc,
        tien_ht_gara: e.gia_giam_dinh,
        so_luong: e.so_luong,
        //tiền GARA
        tien_vtu: e.tien_vtu,
        tien_nhan_cong: e.tien_nhan_cong,
        tien_khac: e.tien_khac,
        //tiền đề xuất
        tien_vtu_dx: e.tien_vtu_dx,
        tien_nhan_cong_dx: e.tien_nhan_cong_dx,
        tien_khac_dx: e.tien_khac_dx,
        tien_dx: e.tien_dx,
        //tiền duyệt = tiền đề xuất
        tien_vtu_duyet: e.tien_vtu_dx,
        tien_nhan_cong_duyet: e.tien_nhan_cong_dx,
        tien_khac_duyet: e.tien_khac_dx,
        tien_duyet: e.tien_dx,
        //phần trăm giảm giá
        tl_giam_gia_vtu: e.tl_giam_gia_vtu,
        tl_giam_gia_nhan_cong: e.tl_giam_gia_nhan_cong,
        tl_giam_gia_khac: e.tl_giam_gia_khac,
        //
        ghi_chu: e.ghi_chu,
        bt: '',
      };
      arr.push(json);
    });

    try {
      let params = {
        so_id: profileData?.ho_so?.so_id || '',
        lh_nv: profileData?.lh_nv[0]?.ma || '',
        gara: garaBaoGia?.gara || '',
        bt_gara: garaBaoGia?.bt_gara || '',
        so_id_doi_tuong: garaBaoGia?.so_id_doi_tuong,
        arr: arr,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_BAO_GIA_GARA_XM, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (type === 'luu_ket_thuc') return ketThucBaoGia();
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu chi tiết báo giá thành công', 'success');
      setIsChangedData(false);
      if (type === 'act_back') {
        return setTimeout(() => {
          NavigationUtil.pop();
        }, 200);
      }
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const ketThucBaoGia = async () => {
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id || '',
        so_id_doi_tuong: garaBaoGia?.so_id_doi_tuong,
        gara: garaBaoGia?.gara,
        tao_pa: 'C',
        so_lieu_tu_dong: 'C',
        pm: 'GD',
        bt_gara: garaBaoGia?.bt_gara,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.KT_BAO_GIA_GARA_XM, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Kết thúc báo giá thành công', 'success');
      return NavigationUtil.pop();
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressHuyKTBaoGia = () => {
    Alert.alert('Thông báo', 'Huỷ kết thúc báo giá sẽ huỷ phương án báo giá với Gara này.Bạn có chắc chắn HUỶ KẾT THÚC báo giá này không?', [
      {
        text: 'Huỷ',
      },
      {text: 'Đồng ý', onPress: () => huyKetThucBaoGia()},
    ]);
  };

  const huyKetThucBaoGia = async () => {
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id || '',
        so_id_doi_tuong: garaBaoGia?.so_id_doi_tuong,
        gara: garaBaoGia?.gara,
        bt_gara: garaBaoGia?.bt_gara,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.HUY_KT_BAO_GIA_GARA_XM, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ kết thúc báo giá thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onOpenModalNhapGia = (item, index) => {
    if (showBtnHuy) return Alert.alert('Thông báo', 'Không sửa/xoá hồ sơ đã kết thúc báo giá');
    refModalNhapGia.current.show();
    setItem(item);
    setIndex(index);
  };

  const handleBackPress = () => {
    if (isChangedData) {
      Alert.alert('Thông báo', 'Bạn có muốn lưu thay đổi hay không?', [
        {
          text: 'Không',
          style: 'destructive',
          onPress: () =>
            setTimeout(() => {
              NavigationUtil.pop();
            }, 200),
        },
        {
          text: 'Có',
          onPress: () => {
            handleLuu('act_back');
          },
        },
      ]);
    } else NavigationUtil.pop();
  };

  /** RENDER */
  const renderListHangMuc = () => (
    <FlatList
      data={listHangMuc}
      keyExtractor={(item, index) => index.toString()}
      ListEmptyComponent={<Empty description="Danh sách Hạng mục trống" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
      renderItem={(data) => (
        <ItemHangMuc
          data={data}
          setNote={setNote}
          showBtnHuy={showBtnHuy}
          listHangMuc={listHangMuc}
          onPressEditPrice={onOpenModalNhapGia}
          setIsVisibleModal={() => refModalShowNote.current.show()}
          garaBaoGia={garaBaoGia}
          // onPressRemoveHangMuc={onPressRemoveHangMuc}
        />
      )}
      ListFooterComponent={
        <View flexDirection="row" alignItems="center" marginLeft={8} marginTop={10} marginBottom={spacing.massive + 10}>
          <View style={[styles.totalCol, {width: dimensions.width / 4}]}>
            <Text style={styles.txtGaraName} children="Tổng cộng" />
          </View>
          <View style={styles.totalCol}>
            <NumericFormat value={tongTienGaraBaoGia} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.txtValue]} />} />
          </View>
          <View style={[styles.totalCol, {width: dimensions.width / 3 + 10}]}>
            <NumericFormat value={tongTienDuyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.txtValue, {marginLeft: 10}]} />} />
          </View>
        </View>
      }
      showsVerticalScrollIndicator={false}
    />
  );

  const onActionPress = (index) => {
    if (index === 0) {
      handleLuu();
    }
    if (index === 1) {
      onPressXacNhan('luu_ket_thuc');
    } else if (index === 2) {
      onPressXacNhan('ket_thuc');
    }
  };

  const renderActionSheetGiamDinh = () => (
    <ActionSheet
      ref={(o) => (actionSheetRef = o)}
      title={'Lưu báo giá'}
      cancelButtonIndex={3}
      destructiveButtonIndex={3}
      onPress={(index) => onActionPress(index)}
      options={['Lưu', 'Lưu và kết thúc', 'Kết thúc', 'Để sau']}
    />
  );

  const renderFooter = () => {
    if (showBtnHuy) {
      return (
        <View style={styles.footerView}>
          <ButtonLinear loading={loading} title="Huỷ kết thúc báo giá" onPress={onPressHuyKTBaoGia} textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} />
          {/* <ButtonLinear
            loading={loading}
            title="Trình phương án"
            onPress={() =>
              NavigationUtil.push(SCREEN_ROUTER_APP.TRINH_PHUONG_AN, {
                profileInfo: profileData.ho_so,
                maMauIn: MA_MAU_IN.TRINH_PHUONG_AN,
                loaiTrinh: 'XE_TRINH_DUYET_DUYET_GIA',
                garaBaoGia: garaBaoGia,
              })
            }
            linearStyle={styles.footerBtn}
          /> */}
        </View>
      );
    } else {
      return (
        <View style={styles.footerView}>
          <ButtonLinear loading={loading} title="Lưu / Kết thúc" onPress={() => actionSheetRef.show()} />
          <ButtonLinear
            title="Bổ sung hạng mục"
            onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_HANG_MUC_BAO_GIA_XM, {listHangMuc: listHangMuc})}
            linearStyle={{marginLeft: spacing.small}}
          />
        </View>
      );
    }
  };

  return (
    <ScreenComponent
      onPressBack={() => handleBackPress()}
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Chi tiết báo giá"
      renderView={
        <SafeAreaView style={styles.container}>
          {renderListHangMuc()}
          {renderActionSheetGiamDinh()}
          <ModalShowNote title="Ghi chú" ref={refModalShowNote} detail={note} onBackPress={() => refModalShowNote.current.hide(false)} />
          <ModalNhapGia updateData={onModalUpdateData} ref={refModalNhapGia} hangMucSelected={item} index={index} onBackPress={() => refModalNhapGia.current.hide()} />
        </SafeAreaView>
      }
      footer={renderFooter()}
    />
  );
};

export const BaoGiaHangMucXMScreen = memo(BaoGiaHangMucXMScreenComponent, isEqual);
