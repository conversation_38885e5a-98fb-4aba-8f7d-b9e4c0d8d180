import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  footerView: {
    flex: 1,
    flexDirection: 'row',
  },
  footerBtn: {
    marginHorizontal: spacing.smaller,
  },
  txtGaraName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.RED1,
  },
  txtValue: {
    fontWeight: '600',
    textAlign: 'right',
    color: colors.RED1,
  },
  totalCol: {
    width: width / 3,
  },
});
