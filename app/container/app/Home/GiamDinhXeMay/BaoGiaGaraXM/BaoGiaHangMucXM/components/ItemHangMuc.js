import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Icon, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';

const ItemHangMucComponent = forwardRef((props, ref) => {
  // console.log('ItemHangMucComponent', mucDoTonThat);
  const {data, mucDoTonThat, listHangMuc, setNote, setIsVisibleModal, onPressEditPrice, garaBaoGia} = props;
  const {item, index} = data;
  useImperativeHandle(ref, () => ({
    // show: () => setIsVisible(true),
  }));

  // const getTenMucDoTonThatByMa = (ma) => {
  //   let mucDo = null;
  //   mucDoTonThat.map((item) => {
  //     if (item.ma === ma) mucDo = item;
  //   });
  //   if (mucDo) return mucDo.ten;
  //   return '';
  // };
  // const getTenPhuongAnByMa = (ma) => {
  //   if (ma === 'S') return 'Sửa chữa';
  //   return 'Thay thế';
  // };

  const onPressShowNote = () => {
    setNote && setNote(item.ghi_chu);
    setIsVisibleModal(true);
  };

  /* RENDER */

  return (
    <View style={styles.container}>
      <Text style={styles.txtGaraName}>
        {index + 1 + '. '}
        {item?.ten_hang_muc}
        <Text style={styles.subText} children={' - '} />
        <Text style={styles.subText} children={item.muc_do_ten + '/'} />
        <Text style={styles.subText} children={item.thay_the_sc_ten} />
        {/* <Text style={styles.subText} children={item.so_luong} /> */}
      </Text>
      {/* <View flexDirection="row" justifyContent="space-between" paddingBottom={spacing.smaller} marginHorizontal={10}>
        <View flexDirection="row">
          <Text children="Mức độ: " />
          <Text numberOfLines={1} ellipsizeMode="tail" style={[styles.subText, {width: width / 4}]} children={item.muc_do_ten} />
        </View>
        <View flexDirection="row">
          <Text children="Ph/án: " />
          <Text style={styles.subText} children={item.thay_the_sc_ten} />
        </View>
        <View flexDirection="row">
          <Text children="S/lượng: " />
          <Text style={styles.subText} children={item.so_luong} />
        </View>
      </View> */}
      <View style={styles.garaDetailView}>
        <View style={{flex: 1}}>
          {/* <View style={styles.rowData}>
            <Text children="Mức độ" style={[styles.txtLabel]} />
            <Text children={item.muc_do_ten} style={[styles.txtValue]} />
          </View>
          <View style={styles.rowData}>
            <Text children="Phương án" style={styles.txtLabel} />
            <Text children={item.thay_the_sc_ten} style={[styles.txtValue]} />
          </View>
          <View style={styles.rowData}>
            <Text children="Số lượng" style={styles.txtLabel} />
            <NumericFormat value={item.so_luong} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
          </View> */}

          <View style={[styles.rowStyles, {backgroundColor: 'rgba(51,	85,	180,0.2)'}]}>
            <View style={[styles.frame, {width: dimensions.width / 4}]} />
            <View style={[styles.frame, {width: dimensions.width / 3 + 15}]}>
              <Text children="Gara báo giá" style={styles.txtGroup} />
            </View>
            <View style={{width: dimensions.width / 3}}>
              <Text children="Đề xuất duyệt" style={styles.txtGroup} />
            </View>
          </View>

          <TouchableOpacity onPress={() => onPressEditPrice(item, index)} style={[styles.rowStyles, {borderTopWidth: 0}]}>
            <View style={[styles.frame, {width: dimensions.width / 4}]}>
              <Text children="Tiền vật tư" style={styles.txtLabel} />
            </View>
            <View style={[styles.frame, {width: dimensions.width / 3 + 15}]}>
              <NumericFormat value={item.tien_vtu} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.txtValue, {marginRight: 10}]} />} />
            </View>
            <View style={{width: dimensions.width / 3}} flexDirection="row" alignItems="center">
              <NumericFormat value={item.tien_vtu_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
              {/* <Icon.MaterialCommunityIcons name="pencil-outline" size={18} color={colors.GRAY3} /> */}
            </View>
          </TouchableOpacity>

          <TouchableOpacity onPress={() => onPressEditPrice(item, index)} style={[styles.rowStyles, {borderTopWidth: 0}]}>
            <View style={[styles.frame, {width: dimensions.width / 4}]}>
              <Text children="Nhân công" style={styles.txtLabel} />
            </View>
            <View style={[styles.frame, {width: dimensions.width / 3 + 15}]}>
              <NumericFormat value={item.tien_nhan_cong} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.txtValue, {marginRight: 10}]} />} />
            </View>
            <View style={{width: dimensions.width / 3}}>
              <NumericFormat value={item.tien_nhan_cong_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onPressEditPrice(item, index)} style={[styles.rowStyles, {borderTopWidth: 0}]}>
            <View style={[styles.frame, {width: dimensions.width / 4}]}>
              <Text children="Tiền sơn" style={styles.txtLabel} />
            </View>
            <View style={[styles.frame, {width: dimensions.width / 3 + 15}]}>
              <NumericFormat value={item.tien_khac} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.txtValue, {marginRight: 10}]} />} />
            </View>
            <View style={{width: dimensions.width / 3}}>
              <NumericFormat value={item.tien_khac_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
            </View>
          </TouchableOpacity>

          <View style={{flexDirection: 'row'}}>
            {/* <View style={{width: width / 4}}>
              <Text children=" " style={styles.txtGroup} />
              <Text children="Tiền vật tư" style={styles.txtLabel} numberOfLines={1} ellipsizeMode="tail" />
              <Text children="Nhân công" style={[styles.txtLabel, {backgroundColor: 'rgba(51,	85,	180,0.2)'}]} numberOfLines={1} ellipsizeMode="tail" />
              <Text children="Tiền sơn" style={styles.txtLabel} numberOfLines={1} ellipsizeMode="tail" />
            </View>
            <View style={{flex: 1}}>
              <Text children="Gara báo giá" style={styles.txtGroup} numberOfLines={1} ellipsizeMode="tail" />
              <TouchableOpacity onPress={() => onPressEditPrice(item, index)}>
                <NumericFormat value={item.tien_vtu} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
                <NumericFormat
                  value={item.tien_nhan_cong}
                  displayType={'text'}
                  thousandSeparator={true}
                  renderText={(value) => <Text children={value} style={[styles.txtValue, {backgroundColor: 'rgba(51,	85,	180,0.2)'}]} />}
                />
                <NumericFormat value={item.tien_khac} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
              </TouchableOpacity>
            </View>
            <View style={{flex: 1}}>
              <Text children="Đề xuất duyệt" style={styles.txtGroup} numberOfLines={1} ellipsizeMode="tail" />
              <TouchableOpacity onPress={() => onPressEditPrice(item, index)}>
                <NumericFormat value={item.tien_vtu_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
                <NumericFormat
                  value={item.tien_nhan_cong_dx}
                  displayType={'text'}
                  thousandSeparator={true}
                  renderText={(value) => <Text children={value} style={[styles.txtValue, {backgroundColor: 'rgba(51,	85,	180,0.2)'}]} />}
                />
                <NumericFormat value={item.tien_khac_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
              </TouchableOpacity>
            </View> */}
            {/* <View style={{width: width / 5}}>
              <Text children="Giảm(%)" style={styles.txtGroup} numberOfLines={1} ellipsizeMode="tail" />
              <NumericFormat value={item.tl_giam_gia_vtu} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
              <NumericFormat
                value={item.tl_giam_gia_nhan_cong}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => <Text children={value} style={[styles.txtValue, {backgroundColor: 'rgba(51,	85,	180,0.2)'}]} />}
              />
              <NumericFormat value={item.tl_giam_gia_khac} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
            </View> */}
          </View>

          <View style={styles.bottomRow}>
            <TouchableOpacity style={{flexDirection: 'row'}} onPress={() => onPressShowNote(item)}>
              <Text children={'Ghi chú: '} />
              <Icon.Feather name="file-text" size={18} color={item.ghi_chu === '' || item.ghi_chu === null ? colors.GRAY3 : colors.PRIMARY} />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_HANG_MUC_BAO_GIA_XM, {garaBaoGia: garaBaoGia, hangMucBaoGia: item, index: index, listHangMuc: listHangMuc, type: 'EDIT'})}>
              <Icon.FontAwesome name="edit" color={colors.PRIMARY} size={20} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <View style={{alignSelf: 'flex-end'}}>
        {/* <TouchableOpacity style={styles.themHangMucView} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.BAO_GIA_HANG_MUC)}>
          <Text children="Chi tiết báo giá" style={styles.txtThemHangMuc} />
          <Icon.AntDesign name="doubleright" size={15} color={colors.PRIMARY} />
        </TouchableOpacity> */}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 2,
    // paddingVertical: spacing.smaller,
    // marginBottom: spacing.smaller,

    backgroundColor: '#FFF',

    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.25,
    // shadowRadius: 3.84,

    // elevation: 5,
  },

  themHangMucView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: spacing.smaller,
    // borderWidth: 1,
  },
  txtThemHangMuc: {
    color: colors.PRIMARY,
    fontWeight: '700',
    paddingRight: spacing.smaller,
    fontSize: 16,
  },
  garaDetailView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    // flex: 1,
  },
  rowData: {
    flexDirection: 'row',
    // paddingVertical: spacing.tiny,
  },
  txtLabel: {
    flex: 1,
    paddingVertical: 2,
    textAlign: 'left',
    marginLeft: spacing.tiny,
    // borderWidth: 1
  },
  txtValue: {
    flex: 1,
    // paddingBottom: spacing.smaller,
    textAlign: 'right',
    paddingVertical: spacing.tiny,
    // borderWidth: 1
  },
  txtGaraName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingBottom: spacing.smaller,
    marginHorizontal: 10,
    marginTop: 16,
    // textDecorationLine: 'underline',
  },
  txtGroup: {
    color: colors.PRIMARY,
    paddingVertical: spacing.tiny,
    // backgroundColor: 'rgba(51,	85,	180,0.2)',
    textAlign: 'center',
    // fontWeight : 'bold'
    // borderWidth: 1,
  },
  subText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.BLACK_03,
    fontStyle: 'italic',
  },
  rowStyles: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderColor: colors.GRAY,
  },
  frame: {
    borderRightWidth: 0.5,
    borderColor: colors.GRAY,
  },
  bottomRow: {
    paddingLeft: 4,
    borderWidth: 0.5,
    paddingRight: 10,
    borderTopWidth: 0,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
});
const mapStateToProps = (state) => ({
  mucDoTonThat: state.categoryCommon.data.levelLost,
});
const ItemHangMucMemo = memo(ItemHangMucComponent, isEqual);
export const ItemHangMuc = connect(mapStateToProps, {})(ItemHangMucMemo);
