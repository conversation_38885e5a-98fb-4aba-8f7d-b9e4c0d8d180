import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  headerTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
    marginBottom: vScale(spacing.tiny),
    paddingVertical: vScale(spacing.tiny),
    paddingHorizontal: scale(spacing.tiny),
  },
  subLabel: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.PRIMARY,
    marginVertical: vScale(spacing.smaller),
  },
  btnAddGara: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: scale(spacing.smaller),
  },
  txtAddGara: {
    fontWeight: '700',
    color: colors.PRIMARY,
    paddingRight: scale(spacing.smaller),
  },

  footerView: {
    flex: 1,
    flexDirection: 'row',
  },
});
