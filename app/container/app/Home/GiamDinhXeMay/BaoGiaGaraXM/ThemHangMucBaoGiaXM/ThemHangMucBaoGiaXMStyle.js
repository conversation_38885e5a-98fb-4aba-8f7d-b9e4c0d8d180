import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ECECEC',
  },
  btnView: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFF',
  },
  formContainer: {
    // marginHorizontal: spacing.smaller,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingHorizontal: spacing.smaller,
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
  },
  blockTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    color: colors.PRIMARY,
    marginTop: 10,
  },
  inputContainer: {
    flex: 1,
  },
  btnConfirm: {
    marginBottom: spacing.medium,
    paddingHorizontal: spacing.smaller,
    paddingTop: spacing.smaller,
  },
  checkView: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  footerView: {
    borderTopWidth: 0.2,
    paddingVertical: 10,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: 16,
  },
  errText: {
    color: colors.RED1,
    marginBottom: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textAlignRight: {
    textAlign: 'right',
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  switchBtnView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
