import {NGHIEP_VU} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {View, Alert} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Switch} from 'react-native-switch';
import {connect} from 'react-redux';
import {PHUONG_AN_KHAC_PHUC} from './Constans';
import styles from './ThemHangMucBaoGiaXMStyle';
import {getPercentageValue} from '@app/utils/string';

const ThemHangMucBaoGiaXMScreenComponent = (props) => {
  console.log('ThemHangMucBaoGiaXMScreenComponent');
  const {route, navigation, mucDoTonThat, categoryCommon} = props;

  const {listHangMuc, type, garaBaoGia} = route?.params;

  const [dsHangMuc, setDsHangMuc] = useState([]); //CÓ API, HỎI A THANH
  const [dsHangMucRoot, setDsHangMucRoot] = useState([]); //CÓ API, HỎI A THANH

  const [openChonHangMuc, setOpenChonHangMuc] = useState(false);
  const [hangMucSelected, setHangMucSelected] = useState(null);

  const [openMucDoTonThat, setOpenMucDoTonThat] = useState(false);
  const [mucDoTonThatSelected, setMucDoTonThatSelected] = useState(null);
  const [mucDoTonThatTen, setMucDoTonThatTen] = useState('');

  const [openPhuongAnKhacPhuc, setOpenPhuongAnKhacPhuc] = useState(false);
  const [phuongAnKhacPhucSelected, setPhuongAnKhacPhucSelected] = useState('S');

  const [soLuong, setSoLuong] = useState(0);

  // const [giaHopTacGara, setGiaHopTacGara] = useState('');
  const [tongTienVatTuGara, setTongTienVatTuGara] = useState('');
  const [nhanCongGara, setNhanCongGara] = useState('');
  const [tienSonGara, setTienSonGara] = useState('');

  const [tienVatTuDeXuat, setTienVatTuDeXuat] = useState('');
  const [nhanCongDeXuat, setNhanCongDeXuat] = useState('');
  const [tienSonDeXuat, setTienSonDeXuat] = useState('');

  const [vatTuGiamGia, setVatTuGiamGia] = useState('');
  const [nhanCongGiamGia, setNhanCongGiamGia] = useState('');
  const [tienSonGiamGia, setTienSonGiamGia] = useState('');
  const [checkVatTu, setCheckVatTu] = useState(true);
  const [checkNhanCong, setCheckNhanCong] = useState(true);
  const [checkSon, setCheckSon] = useState(true);
  const [hangMuc, setHangMuc] = useState({});
  const [searchInput, setSearchInput] = useState('');
  const [giaGiamDinh, setGiaGiamDinh] = useState('');

  const [ghiChu, setGhiChu] = useState('');
  const [actionType, setActionType] = useState('');
  const [errInput, setErrInput] = useState(['', '', '']);
  const [err, setErr] = useState([false, false, false]);
  const [isCheckAll, setIsCheckAll] = useState(true);
  const [autoFill, setAutoFill] = useState(true);

  const [disabledBtnXacNhan, setDisabledBtnXacNhan] = useState(false);

  // useEffect(() => {
  //   if (listHangMuc?.length > 0) {
  //     listHangMuc.map((item, index) => {
  //       listHangMuc[index].label = item.ten_hang_muc;
  //       listHangMuc[index].value = item.hang_muc;
  //       return item;
  //     });
  //     setDsHangMuc(listHangMuc);
  //   }
  // }, [listHangMuc]);

  useEffect(() => {
    const lowerCaseSearchText = searchInput?.toLowerCase();
    const filter = dsHangMucRoot.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
    setDsHangMuc(filter.slice(0, 20));
  }, [searchInput]);

  useEffect(() => {
    if (hangMucSelected && dsHangMucRoot && type === 'EDIT') {
      const filter = dsHangMucRoot.filter((item) => item.ma === hangMucSelected);
      setHangMuc(filter);
      setDsHangMuc(filter.slice(0, 20));
    } else {
      const filter = dsHangMucRoot.filter((item) => item.ma === hangMucSelected);
      setHangMuc(filter);
    }
  }, [actionType, hangMucSelected]);

  useEffect(() => {
    const filter = mucDoTonThat.filter((item) => item.value === mucDoTonThatSelected);
    if (filter.length > 0) return setMucDoTonThatTen(filter[0].ten);
  }, [mucDoTonThatSelected]);

  useEffect(() => {
    let listHangMucGoc = [];
    const isDoiTuongTaiSanXeMay = garaBaoGia?.nhom === 'TAI_SAN' && garaBaoGia?.loai_doi_tuong === 'XE_MAY';
    if (isDoiTuongTaiSanXeMay) listHangMucGoc = categoryCommon?.listHangMucXeMay;
    else listHangMucGoc = categoryCommon?.type1;

    let data = JSON.parse(JSON.stringify(listHangMucGoc));
    let dataFilter = [];
    if (type === 'EDIT') dataFilter = data.filter((e) => e.loai === 'CHINH' || (e.loai === 'PHU' && route.params.hangMucBaoGia?.loai_hang_muc === 'B'));
    else dataFilter = data.filter((e) => e.loai === 'PHU');
    setDsHangMuc(!isDoiTuongTaiSanXeMay ? dataFilter.slice(0, 20) : dataFilter);
    setDsHangMucRoot(dataFilter);
    setActionType(type);
  }, []);

  useEffect(() => {
    let tongTienGaraBaoGia = +tongTienVatTuGara + +nhanCongGara + +tienSonGara;
    let tongTienDeXuat = +tienVatTuDeXuat + +nhanCongDeXuat + +tienSonDeXuat;
    if (tongTienDeXuat > tongTienGaraBaoGia) setDisabledBtnXacNhan(true);
    else setDisabledBtnXacNhan(false);
  }, [tienSonDeXuat, nhanCongDeXuat, tienVatTuDeXuat, tienSonGara, nhanCongGara, tongTienVatTuGara]);

  useEffect(() => {
    if (route.params?.hangMucBaoGia) {
      let hangMucBaoGia = route.params.hangMucBaoGia;

      setHangMucSelected(hangMucBaoGia.hang_muc);
      setMucDoTonThatSelected(hangMucBaoGia.muc_do);
      setMucDoTonThatTen(hangMucBaoGia.muc_do_ten);
      setPhuongAnKhacPhucSelected(hangMucBaoGia.thay_the_sc);
      setSoLuong(hangMucBaoGia.so_luong);
      setGiaGiamDinh(hangMucBaoGia.gia_giam_dinh);

      setTongTienVatTuGara(hangMucBaoGia.tien_vtu > 0 ? hangMucBaoGia.tien_vtu : '');
      setNhanCongGara(hangMucBaoGia.tien_nhan_cong > 0 ? hangMucBaoGia.tien_nhan_cong : '');
      setTienSonGara(hangMucBaoGia.tien_khac > 0 ? hangMucBaoGia.tien_khac : '');

      setTienVatTuDeXuat(hangMucBaoGia.tien_vtu_dx > 0 ? hangMucBaoGia.tien_vtu_dx : '');
      setNhanCongDeXuat(hangMucBaoGia.tien_nhan_cong_dx > 0 ? hangMucBaoGia.tien_nhan_cong_dx : '');
      setTienSonDeXuat(hangMucBaoGia.tien_khac_dx > 0 ? hangMucBaoGia.tien_khac_dx : '');

      setVatTuGiamGia(hangMucBaoGia.tl_giam_gia_vtu > 0 ? hangMucBaoGia.tl_giam_gia_vtu : '');
      setNhanCongGiamGia(hangMucBaoGia.tl_giam_gia_nhan_cong > 0 ? hangMucBaoGia.tl_giam_gia_nhan_cong : '');
      setTienSonGiamGia(hangMucBaoGia.tl_giam_gia_khac > 0 ? hangMucBaoGia.tl_giam_gia_khac : '');

      hangMucBaoGia.checkGiamGiaVatTu === false && setCheckVatTu(hangMucBaoGia.checkGiamGiaVatTu);
      hangMucBaoGia.checkGiamGiaNhanCong === false && setCheckNhanCong(hangMucBaoGia.checkGiamGiaNhanCong);
      hangMucBaoGia.checkGiamGiaSon === false && setCheckSon(hangMucBaoGia.checkGiamGiaSon);

      if (hangMucBaoGia.ghiChu) setGhiChu(hangMucBaoGia.ghiChu);
    }
  }, []);

  const onCheckAll = (value) => {
    setIsCheckAll(value);
    setCheckVatTu(value);
    setCheckSon(value);
    setCheckNhanCong(value);
  };

  const onSelectItem = (hangMucSelected) => {
    let daTonTai = listHangMuc.filter((item) => item.hang_muc === hangMucSelected.ma).length > 0;
    if (daTonTai) return Alert.alert('Thông báo', `Hạng mục "${hangMucSelected.ten}" đã tồn tại!`);
    else setHangMucSelected(hangMucSelected.ma);
  };

  const onPressXacNhan = () => {
    try {
      if (!hangMucSelected) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn Hạng mục báo giá', 'warning');
      if (!mucDoTonThatSelected) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn Mức độ tổn thất', 'warning');
      if (disabledBtnXacNhan) return;
      let hangMucBaoGia =
        type === 'EDIT'
          ? route.params?.hangMucBaoGia
          : {
              hang_muc: '',
              ten_hang_muc: '',
              loai_hang_muc: 'B',
              muc_do: '',
              muc_do_ten: '',
              thay_the_sc: '',
              thay_the_sc_ten: '',
              so_luong: 0,
              gia_giam_dinh: 0,

              tien_vtu: 0,
              tien_nhan_cong: 0,
              tien_khac: 0,

              tien_vtu_dx: 0,
              tien_nhan_cong_dx: 0,
              tien_khac_dx: 0,
              tien_dx: 0,

              tl_giam_gia_vtu: 0,
              tl_giam_gia_nhan_cong: 0,
              tl_giam_gia_khac: 0,
              checkGiamGiaVatTu: true,
              checkGiamGiaNhanCong: true,
              checkGiamGiaSon: true,

              ghi_chu: '',
            };
      hangMucBaoGia.hang_muc = hangMucSelected;
      hangMucBaoGia.ten_hang_muc = hangMuc[0].ten;
      hangMucBaoGia.hang_muc = hangMucSelected;
      hangMucBaoGia.muc_do = mucDoTonThatSelected;
      hangMucBaoGia.muc_do_ten = mucDoTonThatTen;
      hangMucBaoGia.thay_the_sc = phuongAnKhacPhucSelected;
      hangMucBaoGia.thay_the_sc_ten = phuongAnKhacPhucSelected === 'S' ? 'Sửa chữa' : 'Thay thế';
      hangMucBaoGia.so_luong = +soLuong || 0;
      hangMucBaoGia.gia_giam_dinh = +giaGiamDinh || 0;

      hangMucBaoGia.tien_vtu = +tongTienVatTuGara || 0;
      hangMucBaoGia.tien_nhan_cong = +nhanCongGara || 0;
      hangMucBaoGia.tien_khac = +tienSonGara || 0;
      hangMucBaoGia.tong_tien_gara_bg = +tongTienVatTuGara + +nhanCongGara + +tienSonGara || 0;

      hangMucBaoGia.tien_dx = +tienVatTuDeXuat + +nhanCongDeXuat + +tienSonDeXuat || 0;
      hangMucBaoGia.tien_vtu_dx = +tienVatTuDeXuat || 0;
      hangMucBaoGia.tien_nhan_cong_dx = +nhanCongDeXuat || 0;
      hangMucBaoGia.tien_khac_dx = +tienSonDeXuat || 0;

      hangMucBaoGia.tl_giam_gia_vtu = +vatTuGiamGia || 0;
      hangMucBaoGia.tl_giam_gia_nhan_cong = +nhanCongGiamGia || 0;
      hangMucBaoGia.tl_giam_gia_khac = +tienSonGiamGia || 0;
      hangMucBaoGia.checkGiamGiaVatTu = checkVatTu;
      hangMucBaoGia.checkGiamGiaNhanCong = checkNhanCong;
      hangMucBaoGia.checkGiamGiaSon = checkSon;
      hangMucBaoGia.ghi_chu = ghiChu;
      let routes = navigation.getState().routes;
      let paramsUpload = {hangMucBaoGia, index: route.params?.index !== undefined ? route.params.index : undefined};
      paramsUpload[new Date().getTime()] = new Date().getTime();
      NavigationUtil.updateParams(routes[routes.length - 2].key, paramsUpload); //cập nhật tham số cho màn Chụp ảnh
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeSoLuong = (value) => {
    if (value < 0) setSoLuong(0);
    else setSoLuong(value);
  };
  // onChangeText GARA
  const onChangeTongTienVatTuGara = (value) => {
    let errInputTmp = errInput;
    if (value < 0) setTongTienVatTuGara(0);
    else {
      setTongTienVatTuGara(value);
      if (autoFill) {
        setTienVatTuDeXuat(value);
      }
    }
  };
  const onChangeNhanCongGara = (value) => {
    if (value < 0) setNhanCongGara(0);
    else {
      setNhanCongGara(value);
      if (autoFill) {
        setNhanCongDeXuat(value);
      }
    }
  };
  const onChangeTienSonGara = (value) => {
    if (value < 0) setTienSonGara(0);
    else {
      setTienSonGara(value);
      if (autoFill) {
        setTienSonDeXuat(value);
      }
    }
  };
  // onChangeText Đề Xuất
  const onChangeTongTienVatTuDeXuat = (value) => {
    if (value < 0) setTienVatTuDeXuat(0);
    else setTienVatTuDeXuat(value);
  };
  const onChangeNhanCongDeXuat = (value) => {
    if (value < 0) setNhanCongDeXuat(0);
    else setNhanCongDeXuat(value);
  };
  const onChangeTienSonDeXuat = (value) => {
    if (value < 0) setTienSonDeXuat(0);
    else setTienSonDeXuat(value);
  };
  // onChange Giảm Giá
  const onChangeVatTuGiamGia = (value) => {
    if (value < 0) setVatTuGiamGia(0);
    else setVatTuGiamGia(value);
    if (checkVatTu) {
      if (checkNhanCong) {
        setNhanCongGiamGia(value);
      }
      if (checkSon) {
        setTienSonGiamGia(value);
      }
    }
  };
  const onChangeNhanCongGiamGia = (value) => {
    if (value < 0) setNhanCongGiamGia(0);
    else setNhanCongGiamGia(value);
    if (checkNhanCong) {
      if (checkVatTu) {
        setVatTuGiamGia(value);
      }
      if (checkSon) {
        setTienSonGiamGia(value);
      }
    }
  };
  const onChangeSonGiamGia = (value) => {
    if (value < 0) setTienSonGiamGia(0);
    else setTienSonGiamGia(value);
    if (checkSon) {
      if (checkVatTu) {
        setVatTuGiamGia(value);
      }
      if (checkNhanCong) {
        setNhanCongGiamGia(value);
      }
    }
  };

  /** RENDER */
  // const renderThongTinChung = () => (
  //   <View style={[styles.blockSubForm, {zIndex: 9000}]}>
  //     <Text children="Thông tin hạng mục" style={styles.blockTitle} />
  //     {/* Hạng mục */}
  //     <DropdownPicker
  //       title="Hạng mục báo giá"
  //       zIndex={10000}
  //       isOpen={openChonHangMuc}
  //       setOpen={setOpenChonHangMuc}
  //       items={dsHangMuc}
  //       itemSelected={hangMucSelected}
  //       setItemSelected={setHangMucSelected}
  //       onOpen={() => {
  //         setOpenMucDoTonThat(false);
  //         setOpenPhuongAnKhacPhuc(false);
  //       }}
  //       // onChangeValue={() => setErrGara('')}
  //       placeholder="Chọn hạng mục dsss"
  //       containerStyle={{marginBottom: spacing.tiny}}
  //       isRequired={true}
  //       onChangeSearchText={(t) => setSearchInput(t)}
  //       searchable={true}
  //       // inputErr={errGara}
  //     />
  //     <View style={{flexDirection: 'row', zIndex: 9000}}>
  //       <DropdownPicker
  //         title="Mức độ tổn thất"
  //         zIndex={9000}
  //         isOpen={openMucDoTonThat}
  //         setOpen={setOpenMucDoTonThat}
  //         items={mucDoTonThat}
  //         itemSelected={mucDoTonThatSelected}
  //         setItemSelected={setMucDoTonThatSelected}
  //         // onChangeValue={() => setErrGara('')}
  //         placeholder="Chọn mức độ tổn thất"
  //         containerStyle={{marginBottom: spacing.tiny, flex: 1}}
  //         isRequired={true}
  //         searchable={false}
  //         // inputErr={errGara}
  //       />
  //       <DropdownPicker
  //         title="Phương án khắc phục"
  //         zIndex={9000}
  //         isOpen={openPhuongAnKhacPhuc}
  //         setOpen={setOpenPhuongAnKhacPhuc}
  //         items={PHUONG_AN_KHAC_PHUC}
  //         itemSelected={phuongAnKhacPhucSelected}
  //         setItemSelected={setPhuongAnKhacPhucSelected}
  //         onChangeValue={(title, items, value) => {
  //           if (value == 'S') setSoLuong(0);
  //           else setSoLuong(1);
  //         }}
  //         placeholder="Chọn phương án khắc phục"
  //         containerStyle={{marginBottom: spacing.tiny, flex: 1, marginLeft: spacing.smaller}}
  //         isRequired={true}
  //         searchable={false}
  //         // inputErr={errGara}
  //       />
  //     </View>
  //     <TextInputOutlined
  //       disabled={phuongAnKhacPhucSelected === 'S' ? true : false}
  //       editable={phuongAnKhacPhucSelected === 'S' ? false : true}
  //       keyboardType="numeric"
  //       title="Số lượng"
  //       onChangeText={onChangeSoLuong}
  //       containerStyle={[styles.inputContainer]}
  //       onBlur={() => !soLuong?.trim() && setSoLuong('1')}
  //       value={soLuong?.toString()}
  //       placeholder="Số lượng"
  //     />
  //   </View>
  // );

  const renderTienBaoGiaGara = () => (
    <View style={styles.blockSubForm}>
      <Text children="Tiền gara báo giá" style={styles.blockTitle} />
      <View flexDirection="row">
        <TextInputOutlined
          keyboardType="numeric"
          title="Tiền vật tư"
          onChangeText={onChangeTongTienVatTuGara}
          containerStyle={[styles.inputContainer]}
          value={tongTienVatTuGara?.toString()}
          disabled={phuongAnKhacPhucSelected === 'S' ? true : false}
          editable={phuongAnKhacPhucSelected === 'S' ? false : true}
          inputStyle={styles.textAlignRight}
          placeholder="0"
        />
        <TextInputOutlined
          keyboardType="numeric"
          title="Nhân công"
          placeholder="0"
          onChangeText={onChangeNhanCongGara}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          value={nhanCongGara}
          inputStyle={styles.textAlignRight}
        />
        <TextInputOutlined
          title="Tiền sơn"
          placeholder="0"
          value={tienSonGara}
          keyboardType="numeric"
          inputStyle={styles.textAlignRight}
          onChangeText={onChangeTienSonGara}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
        />
      </View>
    </View>
  );
  const renderTienDeXuat = () => (
    <View style={styles.blockSubForm}>
      <View style={styles.titleRow}>
        <Text children="Tiền đề xuất duyệt" style={styles.blockTitle} />
        <RenderSwitchButton value={autoFill} setValue={setAutoFill} title="Tự động điền" />
      </View>
      <View style={styles.inputRow}>
        <TextInputOutlined
          placeholder="0"
          title="Tiền vật tư"
          error={errInput[0]}
          keyboardType="numeric"
          value={tienVatTuDeXuat}
          inputStyle={styles.textAlignRight}
          containerStyle={[styles.inputContainer]}
          onChangeText={onChangeTongTienVatTuDeXuat}
          disabled={phuongAnKhacPhucSelected === 'S' ? true : false}
          editable={phuongAnKhacPhucSelected === 'S' ? false : true}
          // onBlur={() => !tongTienVatTuDeXuat.trim() && setTongTienVatTuDeXuat(0)}
        />
        <TextInputOutlined
          placeholder="0"
          title="Nhân công"
          error={errInput[1]}
          keyboardType="numeric"
          value={nhanCongDeXuat}
          inputStyle={styles.textAlignRight}
          onChangeText={onChangeNhanCongDeXuat}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          // onBlur={() => !nhanCongDeXuat.trim() && setNhanCongDeXuat(0)}
        />
        <TextInputOutlined
          title="Tiền sơn"
          placeholder="0"
          error={errInput[2]}
          value={tienSonDeXuat}
          keyboardType="numeric"
          inputStyle={styles.textAlignRight}
          onChangeText={onChangeTienSonDeXuat}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          // onBlur={() => !tienSonDeXuat.trim() && setTienSonDeXuat('')}
        />
      </View>
      {disabledBtnXacNhan && <Text children="Tổng số tiền đề xuất duyệt không được lớn hơn tổng số tiền gara báo giá" style={styles.errText} />}
    </View>
  );

  const renderTienGiamGia = () => (
    <View style={styles.blockSubForm}>
      <View style={styles.switchBtnView}>
        <Text children="Giảm giá (%)" style={[styles.blockTitle]} />
        <RenderSwitchButton value={isCheckAll} setValue={onCheckAll} title="Áp dụng tất cả" />
      </View>
      <View style={styles.inputRow}>
        <TextInputOutlined
          keyboardType="numeric"
          title="Vật tư (%)"
          placeholder="0"
          inputStyle={styles.textAlignRight}
          onChangeText={onChangeVatTuGiamGia}
          containerStyle={[styles.inputContainer]}
          value={getPercentageValue(vatTuGiamGia)}
          maxLength={3}
        />
        <TextInputOutlined
          keyboardType="numeric"
          title="Nhân công (%)"
          placeholder="0"
          maxLength={3}
          inputStyle={styles.textAlignRight}
          onChangeText={onChangeNhanCongGiamGia}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          value={getPercentageValue(nhanCongGiamGia)}
          // onBlur={() => !nhanCongGiamGia?.trim() && setNhanCongGiamGia(0)}
        />
        <TextInputOutlined
          keyboardType="numeric"
          title="Sơn (%)"
          placeholder="0"
          maxLength={3}
          inputStyle={styles.textAlignRight}
          onChangeText={onChangeSonGiamGia}
          containerStyle={[styles.inputContainer, {marginLeft: spacing.smaller}]}
          value={getPercentageValue(tienSonGiamGia)}
        />
      </View>
    </View>
  );
  const renderGhiChu = () => (
    <View style={styles.blockSubForm}>
      <Text children="Ghi chú" style={styles.blockTitle} />
      <TextInputOutlined value={ghiChu} numberOfLines={3} multiline={true} onChangeText={setGhiChu} placeholder="Nội dung" isRequired={true} />
    </View>
  );
  return (
    <ScreenComponent
      headerBack
      headerTitle="Hạng mục báo giá XM"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView style={{flex: 1}} showsVerticalScrollIndicator={false} enableResetScrollToCoords={false}>
            <View style={[styles.blockSubForm]}>
              <Text children="Thông tin hạng mục" style={styles.blockTitle} />
              {/* Hạng mục */}
              <DropdownPicker
                title="Hạng mục báo giá"
                zIndex={10000}
                items={dsHangMuc}
                isOpen={openChonHangMuc}
                setOpen={setOpenChonHangMuc}
                itemSelected={hangMucSelected}
                // setItemSelected={setHangMucSelected}
                onSelectItem={onSelectItem}
                onOpen={() => {
                  setOpenMucDoTonThat(false);
                  setOpenPhuongAnKhacPhuc(false);
                }}
                // onChangeValue={() => setErrGara('')}
                placeholder="Chọn hạng mục"
                isRequired={true}
                onChangeSearchText={setSearchInput}
                disableLocalSearch={true}
                disabled={type === 'EDIT' ? true : false}
                schema={{
                  label: 'ten',
                  value: 'ma',
                }}
                // inputErr={errGara}
              />
              <DropdownPicker
                title="Mức độ tổn thất"
                zIndex={9000}
                isOpen={openMucDoTonThat}
                setOpen={setOpenMucDoTonThat}
                items={mucDoTonThat.filter((e) => e.nhom === NGHIEP_VU.XE)}
                itemSelected={mucDoTonThatSelected}
                setItemSelected={setMucDoTonThatSelected}
                // onChangeValue={() => setErrGara('')}
                placeholder="Chọn mức độ tổn thất"
                isRequired={true}
                searchable={false}
                maxHeight={150}
                onOpen={() => {
                  setOpenChonHangMuc(false);
                  setOpenPhuongAnKhacPhuc(false);
                }}
                // inputErr={errGara}
              />
              <DropdownPicker
                title="Phương án khắc phục"
                zIndex={8000}
                isOpen={openPhuongAnKhacPhuc}
                setOpen={setOpenPhuongAnKhacPhuc}
                items={PHUONG_AN_KHAC_PHUC}
                itemSelected={phuongAnKhacPhucSelected}
                setItemSelected={setPhuongAnKhacPhucSelected}
                onChangeValue={(title, items, value) => {
                  if (value == 'S') setSoLuong(0);
                  else setSoLuong(1);
                }}
                placeholder="Chọn phương án khắc phục"
                isRequired={true}
                searchable={false}
                onOpen={() => {
                  setOpenChonHangMuc(false);
                  setOpenMucDoTonThat(false);
                }}
              />
              <TextInputOutlined
                disabled={phuongAnKhacPhucSelected === 'S' ? true : false}
                editable={phuongAnKhacPhucSelected === 'S' ? false : true}
                keyboardType="numeric"
                title="Số lượng"
                onChangeText={onChangeSoLuong}
                containerStyle={[styles.inputContainer]}
                // onBlur={() => !soLuong?.trim() && setSoLuong('1')}
                value={soLuong?.toString()}
                placeholder="Số lượng"
              />
            </View>

            {/* </View> */}
            <View style={styles.formContainer}>
              {/* {renderThongTinChung()} */}
              {renderTienBaoGiaGara()}
              {renderTienDeXuat()}
              {renderTienGiamGia()}
              {renderGhiChu()}
            </View>
          </KeyboardAwareScrollView>
        </View>
      }
      footer={<ButtonLinear title="Xác nhận" onPress={onPressXacNhan} />}
    />
  );
};
const mapStateToProps = (state) => ({
  mucDoTonThat: state.categoryCommon.data.levelLost,
  categoryCommon: state.categoryCommon.data,
});
const mapDispatchToProps = {};
const ThemHangMucBaoGiaXMScreenConnect = connect(mapStateToProps, mapDispatchToProps)(ThemHangMucBaoGiaXMScreenComponent);
export const ThemHangMucBaoGiaXMScreen = memo(ThemHangMucBaoGiaXMScreenConnect, isEqual);

const RenderSwitchButton = ({value, setValue, title}) => {
  return (
    <View style={styles.titleRow} marginTop={10}>
      <Text style={{marginRight: 5, color: colors.GRAY6}} children={title} />
      <Switch
        value={value}
        onValueChange={setValue}
        disabled={false}
        activeText={''}
        inActiveText={''}
        circleSize={16}
        barHeight={24}
        circleBorderWidth={0}
        // màu khi active
        circleActiveColor={colors.WHITE}
        backgroundActive={colors.PRIMARY}
        //màu khi InActive
        circleInActiveColor={colors.WHITE}
        backgroundInactive={colors.GRAY}
        // renderInsideCircle={() => <CustomComponent />} // custom component to render inside the Switch circle (Text, Image, etc.)
        changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
        innerCircleStyle={{alignItems: 'center', justifyContent: 'center'}} // style for inner animated circle for what you (may) be rendering inside the circle
        outerCircleStyle={{}} // style for outer animated circle
        renderActiveText={true}
        renderInActiveText={true}
        switchLeftPx={2} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
        switchRightPx={2} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
        switchWidthMultiplier={3} // multipled by the `circleSize` prop to calculate total width of the Switch
        switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
      />
    </View>
  );
};
