/* eslint-disable radix */
import {NGHIEP_VU, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, Empty, Icon, ScreenComponent, TextInputOutlined, Text, CustomTabBar} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {ModalChonHangMuc, ModalInput} from './Components';
import RenderImage from './Components/RenderImage';
import styles from './OCRBaoGiaXMStyle';

let timer;

const LOAI_TIEN = {
  TIEN_VAT_TU: 'TIEN_VAT_TU',
  TIEN_NHAN_CONG: 'TIEN_NHAN_CONG',
  TIEN_SON: 'TIEN_SON',
};

const OCRBaoGiaXMScreenComponent = (props) => {
  console.log('OCRBaoGiaXMScreenComponent');
  const {route, navigation} = props;
  const {profileInfo, gara, listHangMuc} = route?.params;
  const userInfo = useSelector(selectUser);
  const [currentPage, setCurrentPage] = useState(0);

  const [duLieuBaoGia, setDuLieuBaoGia] = useState([]);
  const [itemsSelected, setItemsSelected] = useState([]);
  const [listHangMucOCR, setListHangMucOCR] = useState([]);
  const [listHangMucOCRBase, setListHangMucOCRBase] = useState([]);

  const [itemIndex, setItemIndex] = useState(-1);
  const [searchText, setSearchText] = useState('');
  const [field, setField] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [tongTien, setTongTien] = useState(0);
  const [tongDuyet, setTongDuyet] = useState(0);

  let refItemIndex = useRef(null);
  let refErrMess = useRef(null);

  let tabViewRef = useRef(null);
  let scrollViewRef = useRef(null);
  let refModal = useRef(null);
  let refModalInput = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDuLieuOCR();
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    if (itemsSelected.length > 0) {
      getDuLieuOCR();
    }
  }, [itemsSelected]);

  useEffect(() => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = listHangMucOCRBase.filter((item) => item?.ten_hang_muc.toLowerCase()?.includes(lowerCaseSearchText));
      setListHangMucOCR(filter);
    } else setListHangMucOCR(listHangMucOCRBase);
  }, [searchText]);

  useEffect(() => {
    initData();
  }, [duLieuBaoGia]);

  useEffect(() => {
    let tt = 0;
    let td = 0;
    if (listHangMucOCR.length > 0) {
      listHangMucOCR.map((item) => {
        let sum = item.thanh_tien;
        let sumTd = item.tien_duyet;
        tt += sum;
        td += sumTd;
      });
      setTongTien(tt);
      setTongDuyet(td);
    }
  }, [listHangMucOCR]);

  const initData = () => {
    if (duLieuBaoGia.length > 0) {
      let datas = duLieuBaoGia;
      datas.map((item, index) => {
        let checkVt = item.loai_tien === LOAI_TIEN.TIEN_VAT_TU;
        let checkNC = item.loai_tien === LOAI_TIEN.TIEN_NHAN_CONG;
        let checkSon = item.loai_tien === LOAI_TIEN.TIEN_SON;
        datas[index].bt = item.bt || '';
        datas[index].ten_hang_muc = item.ten_hang_muc || '';
        datas[index].don_gia = +item.don_gia || 0;
        datas[index].tien_duyet = +item.tien_duyet || 0;
        datas[index].so_luong = +item.so_luong || 0;
        datas[index].thanh_tien = +item.thanh_tien || 0;
        datas[index].loai_tien = item.loai_tien || '';
        datas[index].check_vt = checkVt;
        datas[index].check_nc = checkNC;
        datas[index].check_s = checkSon;
      });
      setListHangMucOCR([...datas]);
      setListHangMucOCRBase([...datas]);
    }
  };

  const onReadOCR = async () => {
    setIsLoading(true);
    if (itemsSelected.length > 0) {
      let mapBt = itemsSelected.map((item) => {
        return item.bt;
      });
      try {
        let params = {
          so_id: profileInfo?.so_id,
          bt: mapBt,
          gara: gara.gara,
          nv: 'XE_MAY',
        };
        let response = await ESmartClaimEndpoint.docBaoGiaOCR(AxiosConfig.ACTION_CODE.OCR_DOC_BAO_GIA, params);
        setIsLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        FlashMessageHelper.showFlashMessage('Thông báo', 'Đọc OCR thành công', 'success');
        getDuLieuOCR();
      } catch (error) {
        Alert.alert('Thông báo', error.message);
        setIsLoading(false);
      }
      return;
    }
  };

  const onLuuBaoGia = async () => {
    // kiểm tra checkbox
    let haveErr = false;
    listHangMucOCR.map((e, index) => {
      if (e.loai_tien === '') {
        haveErr = true;
        refItemIndex.current = index;
        refErrMess.current = 'Không xác định được hạng mục';
      }
      return e.loai_tien;
    });

    //check item chưa chọn hang mục hệ thống
    listHangMucOCR.map((e, index) => {
      if (e.mapping == 0) {
        haveErr = true;
        refItemIndex.current = index;
        refErrMess.current = 'Chưa chọn hạng mục hệ thống';
      }
      return e.loai_tien;
    });

    // check tiền trong 1 item
    listHangMucOCR.map((e, index) => {
      if (e.tien_duyet > e.thanh_tien) {
        haveErr = true;
        refItemIndex.current = index;
        refErrMess.current = 'Tiền duyệt không được lớn hơn tiền số tiền báo giá';
      }
    });

    if (haveErr) return FlashMessageHelper.showFlashMessage('Thông báo', `${refErrMess.current} dòng ${refItemIndex.current + 1}`, 'danger');

    let arr = [];
    let data = [];
    listHangMucOCR.forEach((item) => {
      var json = {
        hang_muc: item.ma_hang_muc_he_thong,
        ten_hang_muc: item.ten_hang_muc,
        ten_hang_muc_he_thong: item.ten_hang_muc_he_thong,
        so_luong: item.so_luong,
        tien_vat_tu: 0,
        tien_nhan_cong: 0,
        tien_khac: 0,
        bt: item.bt,
        tl_khop: item.tl_khop,
        tien_vtu_dx: 0,
        tien_nhan_cong_dx: 0,
        tien_khac_dx: 0,
        loai_tien: item.loai_tien,
        ma_hang_muc_he_thong: item.ma_hang_muc_he_thong,
        tien_duyet: item.tien_duyet,
      };

      let obj = json;

      if (item.loai_tien == LOAI_TIEN.TIEN_VAT_TU) {
        obj.tien_vat_tu = +item.thanh_tien;
        obj.tien_vtu_dx = +item.tien_duyet;
      }
      if (item.loai_tien == LOAI_TIEN.TIEN_NHAN_CONG) {
        obj.tien_nhan_cong = +item.thanh_tien;
        obj.tien_nhan_cong_dx = +item.tien_duyet;
      }
      if (item.loai_tien == LOAI_TIEN.TIEN_SON) {
        obj.tien_khac = +item.thanh_tien;
        obj.tien_khac_dx = +item.tien_duyet;
      }

      var item_check = arr.filter((n) => n.hang_muc == obj.hang_muc)[0];
      if (item_check != undefined && item_check != null) {
        if (item.loai_tien == LOAI_TIEN.TIEN_VAT_TU) {
          item_check.tien_vat_tu = +(json.tien_vat_tu + item_check.tien_vat_tu);
          item_check.tien_vtu_dx = +(json.tien_vtu_dx + item_check.tien_vtu_dx);
        }
        if (item.loai_tien == LOAI_TIEN.TIEN_NHAN_CONG) {
          item_check.tien_nhan_cong = +(json.tien_nhan_cong + item_check.tien_nhan_cong);
          item_check.tien_nhan_cong_dx = +(json.tien_nhan_cong_dx + item_check.tien_nhan_cong_dx);
        }
        if (item.loai_tien == LOAI_TIEN.TIEN_SON) {
          item_check.tien_khac = +(json.tien_khac + item_check.tien_khac);
          item_check.tien_khac_dx = +(json.tien_khac_dx + item_check.tien_khac_dx);
        }
      } else {
        item_check = obj;
        arr.push(item_check);
      }
      data.push(obj);
    });
    if (arr.length < listHangMuc.length) {
      return FlashMessageHelper.showFlashMessage('Thông báo', 'Tồn tại hạng mục giám định chưa phân loại theo báo giá sửa chữa', 'danger');
    }
    try {
      let params = {
        so_id: profileInfo.so_id,
        so_id_doi_tuong: gara.so_id_doi_tuong,
        gara: gara.gara,
        bt_gara: gara.bt_gara,
        arr: arr,
        data: data,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LUU_OCR_BAO_GIA_XM, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu báo giá thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const getDuLieuOCR = async () => {
    setIsLoading(true);
    if (itemsSelected.length > 0) {
      let mapBt = itemsSelected.map((item) => {
        return item.bt;
      });
      try {
        let params = {
          so_id: profileInfo?.so_id,
          bt: mapBt,
          gara: gara.gara,
          ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
          nv: profileInfo.nghiep_vu,
        };
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DU_LIEU_OCR, params);
        setIsLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        const data = response.data_info;
        setDuLieuBaoGia(data);
        if (data.length > 0) tabViewRef.current.goToPage(1);
        else FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa có dữ liệu OCR');
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
      return;
    }
  };

  const onSetValueInput = (value, field) => {
    if (field === 'Đơn giá') {
      onChangeInputDonGia(value, itemIndex);
    }
    if (field === 'Số lượng') {
      onChangeInputSoLuong(value, itemIndex);
    }
    if (field === 'Tiền duyệt') {
      onChangeInputTienDuyet(value, itemIndex);
    }
  };

  const onChangeInputDonGia = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].don_gia = value;
    if (datas[index].so_luong >= 1) {
      datas[index].tien_duyet = datas[index].don_gia * datas[index].so_luong;
      datas[index].thanh_tien = datas[index].don_gia * datas[index].so_luong;
    } else {
      datas[index].tien_duyet = datas[index].don_gia * 1;
      datas[index].thanh_tien = datas[index].don_gia * 1;
    }
    setListHangMucOCR([...datas]);
    setDuLieuBaoGia([...datas]);
  };
  const onChangeInputSoLuong = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].so_luong = value;
    if (datas[index].so_luong >= 1) {
      datas[index].tien_duyet = datas[index].don_gia * datas[index].so_luong;
      datas[index].thanh_tien = datas[index].don_gia * datas[index].so_luong;
    } else {
      datas[index].tien_duyet = datas[index].don_gia * 1;
      datas[index].thanh_tien = datas[index].don_gia * 1;
    }
    setDuLieuBaoGia([...datas]);
    setListHangMucOCR([...datas]);
  };
  const onChangeInputTienDuyet = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].tien_duyet = value;
    setDuLieuBaoGia([...datas]);
    setListHangMucOCR([...datas]);
  };
  const onSelectHangMuc = (item) => {
    let datas = duLieuBaoGia;
    datas[itemIndex].ten_hang_muc_he_thong = item.ten_hang_muc;
    datas[itemIndex].ma_hang_muc_he_thong = item.hang_muc;
    datas[itemIndex].mapping = '1';
    setDuLieuBaoGia([...datas]);
    setListHangMucOCR([...datas]);
  };

  const onSetCheckVatTu = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].check_vt = value;
    if (value) {
      datas[index].check_nc = !value;
      datas[index].check_s = !value;
      datas[index].loai_tien = LOAI_TIEN.TIEN_VAT_TU;
    } else {
      datas[index].loai_tien = '';
    }
    setDuLieuBaoGia([...datas]);
    setListHangMucOCR([...datas]);
  };
  const onSetCheckNhanCong = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].check_nc = value;
    if (value) {
      datas[index].check_vt = !value;
      datas[index].check_s = !value;
      datas[index].loai_tien = LOAI_TIEN.TIEN_NHAN_CONG;
    } else {
      datas[index].loai_tien = '';
    }
    setDuLieuBaoGia([...datas]);
    setListHangMucOCR([...datas]);
  };
  const onSetCheckSon = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].check_s = value;
    if (value) {
      datas[index].check_vt = !value;
      datas[index].check_nc = !value;
      datas[index].loai_tien = LOAI_TIEN.TIEN_SON;
    } else {
      datas[index].loai_tien = '';
    }
    setDuLieuBaoGia([...datas]);
    setListHangMucOCR([...datas]);
  };

  const onOpenModalHangMuc = (index) => {
    setItemIndex(index);
    refModal.current.show();
  };
  const onOpenModalInput = (index, field) => {
    setField(field);
    setItemIndex(index);
    refModalInput.current.show();
  };

  const onRemoveItem = (idx) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn xoá dòng dữ liệu này không?', [{text: 'Huỷ'}, {text: 'Đồng ý', onPress: () => confirmRemove(idx)}]);
  };

  const confirmRemove = (idx) => {
    let newArr = duLieuBaoGia;
    newArr.splice(idx, 1);
    setListHangMucOCR([...newArr]);
    setDuLieuBaoGia([...newArr]);
  };

  const onPressBoSungBaoGia = () => {
    NavigationUtil.push(SCREEN_ROUTER_APP.BO_SUNG_TAI_LIEU, {profileData: profileInfo, prevScreen: SCREEN_ROUTER_APP.OCR_BAO_GIA, nv: profileInfo.nghiep_vu});
  };

  /** RENDER */
  const listHeader = (item) => {
    return (
      <View margin={10}>
        <TextInputOutlined inputStyle={{borderRadius: 5}} value={searchText} onChangeText={setSearchText} placeholder="Tìm hạng mục" />
      </View>
    );
  };

  const renderItem = ({item, index}) => {
    return (
      <View style={styles.itemView}>
        <View>
          <View backgroundColor={'#E7F3FF'} padding={10}>
            <View flexDirection="row">
              <Text style={styles.txtTitle} children="Hạng mục OCR: " />
              <Text style={styles.txtTenHangMuc}>
                {item.ten_hang_muc}
                <Text style={{color: colors.GRAY10, fontStyle: 'italic', fontSize: 10}} children={' (' + (item.tl_khop * 100).toFixed() + '%)'} />
              </Text>
              <TouchableOpacity onPress={() => onRemoveItem(index)}>
                <Icon.FontAwesome name="trash-o" size={22} color={colors.PRIMARY} />
              </TouchableOpacity>
            </View>
            <View flexDirection="row" alignItems="center">
              <View flexDirection="row" alignItems="center" width={dimensions.width / 3} marginRight={20}>
                <Text children="Số lượng:" />
                <TouchableOpacity style={styles.btnNhapGia} onPress={() => onOpenModalInput(index, 'Số lượng')}>
                  <NumericFormat value={item.so_luong} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.value} children={value} />} />
                </TouchableOpacity>
              </View>
              <View flex={1} flexDirection="row" alignItems="center" marginLeft={10}>
                <Text children="Đơn giá:" />
                <TouchableOpacity style={styles.btnNhapGia} onPress={() => onOpenModalInput(index, 'Đơn giá')}>
                  <NumericFormat value={item.don_gia} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.value} children={value} />} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View style={{height: 0.5, backgroundColor: colors.GRAY}} />
          <View paddingTop={10} paddingBottom={4} paddingHorizontal={10} backgroundColor={colors.WHITE1}>
            <View flexDirection="row">
              <Text style={styles.txtTitle} children="Hạng mục GĐ: " />
              <TouchableOpacity style={{flexDirection: 'row', flex: 1}} onPress={() => onOpenModalHangMuc(index)}>
                <Text style={[styles.txtTenHangMuc, {color: item.mapping == '0' ? colors.BLACK_03 : colors.PRIMARY}]} children={item.ten_hang_muc_he_thong} />
                {item.mapping == '0' && <Text style={styles.note} children="(Chưa xác định)" />}
              </TouchableOpacity>
            </View>
            <View style={styles.checkView}>
              <View style={styles.itemCheck}>
                <Text children="Vật tư" />
                <CheckboxComp checkboxStyle={{marginLeft: spacing.smaller}} value={item.check_vt} onValueChange={(value) => onSetCheckVatTu(value, index)} />
              </View>
              <View style={styles.itemCheck}>
                <Text children="Nhân công" />
                <CheckboxComp checkboxStyle={{marginLeft: spacing.smaller}} value={item.check_nc} onValueChange={(value) => onSetCheckNhanCong(value, index)} />
              </View>
              <View style={styles.itemCheck}>
                <Text children="Sơn" />
                <CheckboxComp checkboxStyle={{marginLeft: spacing.smaller}} value={item.check_s} onValueChange={(value) => onSetCheckSon(value, index)} />
              </View>
            </View>
            <View flexDirection="row">
              <View flex={1} flexDirection="row" alignItems="center" marginRight={16}>
                <Text children="Thành tiền:" />
                <TextInputOutlined
                  value={item.thanh_tien}
                  inputStyle={styles.inputStyle}
                  containerStyle={styles.input}
                  onChangeText={(val) => onChangeInputTienDuyet(val, index)}
                  placeholder="0"
                  keyboardType="numeric"
                  editable={false}
                />
              </View>
              <View flex={1} flexDirection="row" alignItems="center" justifyContent="flex-end">
                <Text children="Tiền duyệt:" />
                <TouchableOpacity style={styles.btnNhapGia} onPress={() => onOpenModalInput(index, 'Tiền duyệt')}>
                  <NumericFormat value={item.tien_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.value} children={value} />} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  // Footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {currentPage === 0 && itemsSelected.length > 0 && <ButtonLinear loading={false} title="Đọc OCR báo giá" onPress={onReadOCR} linearStyle={styles.footerBtn} />}
        {currentPage === 1 && <ButtonLinear onPress={onLuuBaoGia} loading={false} title="Lưu báo giá" linearStyle={styles.footerBtn} />}
      </View>
    );
  };

  const renderListFooter = () => {
    return (
      <View flexDirection="row" justifyContent="space-between" marginTop={10} marginBottom={30} marginHorizontal={10}>
        <View flexDirection="row">
          <Text style={styles.txtTotalValue} children="Tổng tiền: " />
          <NumericFormat value={tongTien} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={[styles.txtTotalValue, {color: colors.BLACK_03}]} children={value} />} />
        </View>
        <View flexDirection="row">
          <Text style={styles.txtTotalValue} children="Tổng duyệt: " />
          <NumericFormat value={tongDuyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={[styles.txtTotalValue, {color: colors.BLACK_03}]} children={value} />} />
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle="OCR báo giá"
      renderView={
        <SafeAreaView style={styles.container}>
          <ScrollableTabView ref={tabViewRef} initialPage={0} onChangeTab={(value) => setCurrentPage(value.i)} renderTabBar={() => <CustomTabBar />}>
            <ScrollView tabLabel="Chụp báo giá">
              <View margin={10}>
                <Text style={styles.label}>
                  Gara sửa chữa:
                  <Text style={styles.txtGaraName} children={' ' + gara?.ten} />
                </Text>
              </View>
              <RenderImage loading={isLoading} setIsLoading={setIsLoading} navigation={navigation} profileInfo={profileInfo} setData={setItemsSelected} />
              {/* <ButtonLinear title="Chụp ảnh báo giá" onPress={onPressBoSungBaoGia} textStyle={styles.txtBtnChupAnh} linearColors={[colors.WHITE1, colors.WHITE1]} linearStyle={styles.btnChupAnh} /> */}
              <TouchableOpacity style={styles.btnChupAnh} onPress={onPressBoSungBaoGia}>
                <Icon.Entypo name={'camera'} size={20} color={colors.PRIMARY} />
                <Text style={styles.txtBtnChupAnh}>Chụp ảnh báo giá</Text>
              </TouchableOpacity>
            </ScrollView>
            <ScrollView
              tabLabel="Dữ liệu OCR báo giá"
              scrollEnabled={true}
              refreshControl={<RefreshControl refreshing={false} onRefresh={() => {}} />}
              ref={scrollViewRef}
              showsVerticalScrollIndicator={false}>
              {/* <RenderDuLieuOCR listHangMuc={listHangMuc} duLieuBaoGia={duLieuBaoGia} setDuLieuBaoGia={setDuLieuBaoGia} /> */}
              <View style={styles.container}>
                {listHeader()}
                <KeyboardAwareScrollView>
                  <FlatList
                    style={{marginBottom: 40}}
                    data={listHangMucOCR}
                    renderItem={renderItem}
                    removeClippedSubviews={true}
                    keyExtractor={(item, index) => index.toString()}
                    ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
                    ListFooterComponent={listHangMucOCR.length > 0 && renderListFooter()}
                  />
                </KeyboardAwareScrollView>
                <ModalChonHangMuc listHangMuc={listHangMuc} setValue={onSelectHangMuc} ref={refModal} onBackPress={() => refModal.current.hide()} />
                <ModalInput setValue={onSetValueInput} field={field} ref={refModalInput} onBackPress={() => refModalInput.current.hide()} />
              </View>
            </ScrollView>
          </ScrollableTabView>
          {renderFooter()}
        </SafeAreaView>
      }
    />
  );
};

export const OCRBaoGiaXMScreen = memo(OCRBaoGiaXMScreenComponent, isEqual);
