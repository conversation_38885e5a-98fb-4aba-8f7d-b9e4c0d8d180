import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  headerTitleView: {
    marginBottom: spacing.tiny,
    paddingVertical: spacing.tiny,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: spacing.tiny,
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
  },
  subLabel: {
    fontSize: 14,
    marginVertical: spacing.smaller,
    fontWeight: '700',
    color: colors.PRIMARY,
  },
  btnAddGara: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    // borderLeftWidth: 1,
    // borderWidth: 1,
    paddingLeft: spacing.smaller,
  },
  txtAddGara: {
    color: colors.PRIMARY,
    fontWeight: '700',
    paddingRight: spacing.smaller,
  },

  footerView: {
    width: width,
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
  },
  footerBtn: {
    width: width / 2,
    marginHorizontal: 10,
  },
  label: {
    fontWeight: '600',
    color: colors.RED1,
  },
  txtGaraName: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  txtTitle: {
    fontWeight: '600',
    color: colors.RED1,
  },
  titleRow: {
    flexDirection: 'row',
    marginBottom: spacing.smaller,
    justifyContent: 'space-between',
  },
  input: {
    flex: 1,
    padding: 0,
    marginLeft: 10,
    textAlign: 'right',
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
  },
  inputStyle: {
    borderWidth: 0,
    paddingLeft: 0,
    paddingRight: 4,
    borderRadius: 2,
    textAlign: 'right',
    alignItems: 'center',
    paddingVertical: spacing.tiny,
    backgroundColor: colors.WHITE1,
  },
  checkView: {
    marginVertical: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  itemView: {
    // marginBottom: 10,
    // paddingVertical: 10,
    borderBottomWidth: 8,
    borderColor: colors.GRAY2,
  },
  txtStt: {
    fontSize: 16,
    marginBottom: 10,
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  btnNhapGia: {
    flex: 1,
    marginLeft: 10,
    marginVertical: 10,
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
  },
  value: {
    marginBottom: 2,
    textAlign: 'right',
  },
  txtTenHangMuc: {
    flex: 1,
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  txtTotalValue: {
    marginVertical: 4,
    fontWeight: 'bold',
    color: colors.RED1,
  },
  itemCheck: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  note: {
    fontSize: 10,
    color: colors.RED1,
    fontStyle: 'italic',
  },
  btnChupAnh: {
    borderWidth: 1,
    borderRadius: 8,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: colors.PRIMARY,
    marginVertical: spacing.small,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.smaller,
  },
  txtBtnChupAnh: {
    fontSize: 15,
    marginHorizontal: 12,
    color: colors.PRIMARY,
    textTransform: 'uppercase',
  },
});
