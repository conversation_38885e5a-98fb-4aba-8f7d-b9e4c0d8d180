import R from '@app/assets/R';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {CheckboxComp, Icon, Text} from '@component';
import React, {useCallback, useEffect, useState} from 'react';
import {ActivityIndicator, Alert, FlatList, Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import Progress from 'react-native-progress/Circle';
import {connect} from 'react-redux';

const extensionsImage = ['.jpg', '.jpeg', '.png', '.gif'];
const extensionsFile = ['.pdf', '.doc', '.docx', '.xml', '.xls', '.xlsx']; //đuôi mở rộng của

function RenderImage(props) {
  const {title, profileInfo, switchImgView, setData, navigation, setIsLoading, loading} = props;

  const [anhHoSo, setAnhHoSo] = useState([]);
  const [imageData, setImageData] = useState([]);

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    navigation &&
      navigation.addListener('focus', () => {
        getData();
      });
  }, []);

  const onPressToggleCheckAll = (type, images) => {
    let newCheckedValue;
    if (type == 0) newCheckedValue = false;
    else if (type == 1) newCheckedValue = true;
    let imageDataTmp = imageData;
    for (let i = 0; i < imageDataTmp.length; i++) {
      if (imageDataTmp[i].ma == images[0].nhom.ma) {
        imageDataTmp[i].checked = newCheckedValue;
        for (let j = 0; j < imageDataTmp[i].images.length; j++) imageDataTmp[i].images[j].checked = newCheckedValue;
        setImageData([...imageDataTmp]);
        setData && setData([...imageDataTmp]);
        return;
      }
    }
  };

  const groupBy = useCallback((xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  }, []);

  const getData = async () => {
    setIsLoading && setIsLoading(true);
    let params = {
      so_id: profileInfo?.so_id || '',
    };

    try {
      let response = await ESmartClaimEndpoint.getFileThumbnail(axiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const filter = response.data_info.filter((item) => {
        return item.ma_file === 'BAOGIA';
      });
      let imgsGroup = groupBy(filter, 'ma_file'); //return object
      let menuImage = [];
      for (const property in imgsGroup) {
        menuImage.push({
          checked: false,
          images: imgsGroup[property],
          ma: imgsGroup[property][0].ma_file,
          ten: imgsGroup[property][0].nhom_anh,
          bt: imgsGroup[property][0].bt,
        });
      }
      setImageData(menuImage);
      let imagesTmp = filter.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
        };
        item.nhom = nhom;
        return item;
      });
      setAnhHoSo([...imagesTmp]);
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressImageCheck = (image) => {
    let imageDataTmp = imageData;
    for (let i = 0; i < imageDataTmp.length; i++) {
      let images = imageDataTmp[i].images;
      for (let j = 0; j < images.length; j++)
        if (images[j].bt == image.bt) {
          images[j].checked = !images[j].checked;
          setImageData([...imageDataTmp]);
          // setData([...imageDataTmp]);
          // return;
        }
    }
    let newArr = [];
    if (image.checked) {
      newArr.push(image);
    }
    setData([...newArr]);
  };

  const onPressOpenImageView = (currentDocumentData) => {
    if (currentDocumentData.item.extension == '.pdf') {
      NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
        profileData: profileInfo,
        prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
        dataPDF: currentDocumentData.item,
      });
    } else if (extensionsImage.includes(currentDocumentData.item.extension)) {
      // if (switchImgView) return;
      NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
        currentImageData: currentDocumentData,
        imagesData: anhHoSo,
      });
    }
  };

  const renderImageItemStep34 = (data) => {
    let item = data.item;
    if (!item.images || item.images.length == 0) return null;
    let checkAll = true;
    let checkAllIsImage = true;
    item.images.map((imgItem) => {
      if (imgItem.checked == false) checkAll = false;
      if (!extensionsImage.includes(imgItem.extension)) checkAllIsImage = false;
    });

    return (
      <>
        <View style={styles.headerView}>
          <Text style={styles.headerTitle}>{item.ten}</Text>
        </View>
        <FlatList
          numColumns={2}
          data={item.images}
          horizontal={false}
          scrollEnabled={false}
          style={{marginBottom: 10}}
          renderItem={renderImageItemStep12}
          keyExtractor={(item) => item.bt.toString()}
        />
      </>
    );
  };

  const onRefresh = () => {
    getData();
  };

  const renderImageStep34 = (title, imagesData) => {
    let arrImages = [];
    arrImages = imagesData.filter((itemImages) => itemImages.images?.length > 0);
    return (
      <View marginTop={20}>
        {/* <View style={styles.headerView}>
          <Text style={styles.headerTitle}>{title}</Text>
        </View> */}
        {arrImages.length == 0 ? (
          renderNoData()
        ) : (
          <FlatList data={arrImages} scrollEnabled={false} initialNumToRender={50} style={{marginBottom: 10}} renderItem={(item) => renderImageItemStep34(item, {title: title})} />
        )}
      </View>
    );
  };

  const renderImageItemStep12 = (data) => {
    let item = data.item;
    if (extensionsFile.includes(item.extension)) {
      let iconName = '',
        iconColor = '';
      if (item.extension === extensionsFile[0]) {
        iconName = 'file-pdf-box';
        iconColor = colors.RED2;
      } else if (item.extension == extensionsFile[1] || item.extension == extensionsFile[2]) {
        iconName = 'file-word';
        iconColor = colors.BLUE2;
      } else if (item.extension == extensionsFile[3]) {
        iconName = 'file-code-outline';
        iconColor = colors.ORANGE;
      } else if (item.extension == extensionsFile[4] || item.extension == extensionsFile[5]) {
        iconName = 'file-excel';
        iconColor = colors.GREEN3;
      }

      return (
        <TouchableOpacity onPress={() => onPressOpenImageView(data)}>
          <View style={styles.imageDocument}>
            <Icon.MaterialCommunityIcons name={iconName} color={iconColor} size={width / 4} />
          </View>
          <View style={styles.checkboxImgView}>
            <CheckboxComp value={item.checked} onValueChange={() => onPressImageCheck(item)} />
          </View>
        </TouchableOpacity>
      );
    }
    return (
      <View style={styles.imageDocument}>
        <TouchableOpacity onPress={() => onPressOpenImageView(data)}>
          {loading && <ActivityIndicator style={styles.indicator} color={colors.PRIMARY} size="large" />}
          <ImageProcess
            source={{uri: `data:image/gif;base64,${item.duong_dan}`}}
            indicator={Progress.Circle}
            style={styles.imageDocument}
            imageStyle={{borderRadius: 12}}
            indicatorProps={{
              size: 70,
              borderWidth: 0,
              color: colors.PRIMARY,
              unfilledColor: colors.PRIMARY_LIGHT,
            }}
            renderError={() => (
              <View>
                <Image source={R.images.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />
              </View>
            )}
          />
          {/* {imageData.stt_hang_muc != undefined && <Text children={imageData.stt_hang_muc} style={{position: 'absolute', top: 20, left: 25, color: '#FFF', fontSize: 18, fontWeight: 'bold'}} />} */}
        </TouchableOpacity>
        <View style={styles.checkboxImgView}>
          <CheckboxComp
            value={item.checked}
            onValueChange={() => {
              onPressImageCheck(item);
            }}
          />
        </View>
      </View>
    );
  };

  return <View style={{flex: 1}}>{renderImageStep34('Ảnh hồ sơ, giấy tờ', imageData)}</View>;
}
const renderNoData = () => (
  <View style={styles.noDataView}>
    <Image source={R.images.img_no_data} style={styles.imageNoData} resizeMode={'contain'} />
    <Text style={{color: colors.GRAY7}}>Chưa có dữ liệu</Text>
  </View>
);

const styles = StyleSheet.create({
  imageDocument: {
    width: width / 2 - 16,
    height: width / 2 - 16,
    margin: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxImgView: {
    right: 0,
    bottom: 0,
    position: 'absolute',
    backgroundColor: colors.WHITE1,
  },
  headerTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerView: {
    flexDirection: 'row',
    marginHorizontal: 15,
    justifyContent: 'space-between',
  },
  headerSubTitle: {
    flex: 1,
    fontSize: 14,
    marginRight: 5,
  },
  headerSubTitleView: {
    flex: 1,
  },
  noDataView: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageNoData: {
    width: width / 5,
    height: width / 5,
  },
  indicator: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
    zIndex: 2,
  },
});
const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(RenderImage);
