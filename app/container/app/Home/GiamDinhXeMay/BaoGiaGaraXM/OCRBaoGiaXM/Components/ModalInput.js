import {colors} from '@app/commons/Theme';
import {ButtonLinear, TextInputOutlined} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalInputComponent = forwardRef(({field, onBackPress, setValue, value}, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  const [text, setText] = useState('');

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
    // updateGata: (garaUpdate, index) => {
    //   setIsVisible(true);
    //   setIndexUpdated(index);
    //   setGaraSelected(garaUpdate.gara.value);
    //   setNgayBaoGia(garaUpdate.ngayBaoGia);
    //   setGioBaoGia(garaUpdate.gioBaoGia);
    // },
  }));

  const onPressLuu = () => {
    setValue(text, field);
    onBackPress && onBackPress();
  };

  /* RENDER */

  return (
    <Modal
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onModalShow={() => setText('')}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      style={styles.modal}
      avoidKeyboard={true}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <View style={styles.headerRow} />
        </View>
        <View flex={1} marginTop={10}>
          {/* <Text style={styles.title}>Nhập {field}</Text> */}
          {/* <TextInputOutlined
            keyboardType="numeric"
            onChangeText={setText}
            containerStyle={[styles.inputContainer, {marginRight: 5}]}
            value={text}
            title="Số lượng"
            // onBlur={() => !tongTienVatTuGara.trim() && setTongTienVatTuGara(0)}
            placeholder="0"
            inputStyle={{textAlign: 'right'}}
          /> */}
          <TextInputOutlined
            keyboardType="numeric"
            onChangeText={setText}
            containerStyle={[styles.inputContainer]}
            value={text}
            title={field}
            // onBlur={() => !tongTienVatTuGara.trim() && setTongTienVatTuGara(0)}
            placeholder="0"
            inputStyle={{textAlign: 'right'}}
          />
        </View>
        <View style={styles.btnView}>
          <ButtonLinear
            title="Đóng"
            linearStyle={styles.btnLuu}
            onPress={() => {
              setIsVisible(false);
            }}
            linearColors={[colors.GRAY, colors.GRAY]}
            textStyle={{color: colors.BLACK_03}}
          />
          <ButtonLinear title={'Lưu'} linearStyle={[styles.btnLuu, {marginLeft: spacing.smaller}]} onPress={onPressLuu} />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    margin: 0,
  },
  btnView: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 30,
    left: 10,
    right: 10,
  },
  modalContent: {
    height: dimensions.height * 0.3,
    backgroundColor: '#FFF',
    borderWidth: 1,
    width: dimensions.width,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: spacing.small,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    width: dimensions.width / 5,
    height: 7,
    // borderWidth: 1,
    backgroundColor: colors.GRAY,
    borderRadius: 20,
  },
  btnLuu: {
    // marginTop: spacing.smaller,
  },
  inputContainer: {
    flex: 1,
    marginBottom: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
    // paddingBottom :
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.RED1,
    marginVertical: 10,
  },
  errText: {
    color: colors.RED1,
    marginBottom: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
export const ModalInput = memo(ModalInputComponent, isEqual);
