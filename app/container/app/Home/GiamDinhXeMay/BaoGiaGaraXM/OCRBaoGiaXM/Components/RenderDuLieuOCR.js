import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import {CheckboxComp, Empty, TextInputOutlined, Text} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalChonHangMuc} from './ModalChonHangMuc';

const PRIMARY_LABEL = ['Mã hồ sơ', 'Tổng phí tạm tính', '<PERSON>h sách người được bảo hiểm', R.strings().tong_phi_bh];

// const data = [
//   {
//     bt: 1,
//     bt_anh: 20220830003996,
//     don_gia: 5500000,
//     giam_gia: 0,
//     loai_tien: '',
//     ma_doi_tac: 'CTYBHABC',
//     ma_gara: 'GR0061',
//     ma_hang_muc_he_thong: '00357',
//     ngay_bao_gia: '25/08/2022',
//     pt_giam_gia: 0,
//     so_id: 20220830002220,
//     so_luong: 1,
//     ten_gara: 'Công ty TNHH Thương Mại và Dịch Vụ Cơ Khí Ô Tô Việt Đăng',
//     ten_hang_muc: 'Thay đèn hậu1',
//     ten_hang_muc_he_thong: 'Bóng đèn hậu',
//     ten_hang_muc_ocr: 'THAYDENHAU',
//     thanh_tien: 5700000,
//     tien_duyet: 5700000,
//     tl_khop: 0.8,
//     tl_thue: 0,
//   },
//   {
//     bt: 1,
//     bt_anh: 20220830003996,
//     don_gia: 5500000,
//     giam_gia: 0,
//     loai_tien: '',
//     ma_doi_tac: 'CTYBHABC',
//     ma_gara: 'GR0061',
//     ma_hang_muc_he_thong: '00357',
//     ngay_bao_gia: '25/08/2022',
//     pt_giam_gia: 0,
//     so_id: 20220830002220,
//     so_luong: 1,
//     ten_gara: 'Công ty TNHH Thương Mại và Dịch Vụ Cơ Khí Ô Tô Việt Đăng',
//     ten_hang_muc: 'Thay đèn hậu2',
//     ten_hang_muc_he_thong: 'Bóng đèn hậu',
//     ten_hang_muc_ocr: 'THAYDENHAU',
//     thanh_tien: 5700000,
//     tien_duyet: 5700000,
//     tl_khop: 0.8,
//     tl_thue: 0,
//   },
// ];

const RenderDuLieuOCRComponent = forwardRef((props, ref) => {
  const {duLieuBaoGia, listHangMuc, setDuLieuBaoGia} = props;

  const [searchText, setSearchText] = useState('');
  const [ListHangMucOCR, setListHangMucOCR] = useState([]);
  const [ListHangMucOCRBase, setListHangMucOCRBase] = useState([]);

  const [itemIndex, setItemIndex] = useState(-1);

  let refModal = useRef(null);

  const listHeader = (item) => {
    return (
      <View margin={10}>
        <TextInputOutlined inputStyle={{borderRadius: 5}} value={searchText} onChangeText={setSearchText} placeholder="Tìm hạng mục" />
      </View>
    );
  };

  // const debounced = useDebouncedCallback((value) => {
  //   setSearchText(value);
  // }, 500);

  useEffect(() => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = ListHangMucOCRBase.filter((item) => item?.ten_hang_muc.toLowerCase()?.includes(lowerCaseSearchText));
      setListHangMucOCR(filter);
    } else setListHangMucOCR(ListHangMucOCRBase);
  }, [searchText]);

  useEffect(() => {
    initData();
  }, [duLieuBaoGia]);

  const initData = () => {
    if (duLieuBaoGia.length > 0) {
      let datas = duLieuBaoGia;
      datas.map((item, index) => {
        datas[index].bt = item.bt || '';
        datas[index].ten_hang_muc = item.ten_hang_muc || '';
        datas[index].don_gia = item.don_gia || '';
        datas[index].tien_duyet = item.tien_duyet || '';
        datas[index].so_luong = item.so_luong || '';
        datas[index].check_vt = false;
        datas[index].check_nc = false;
        datas[index].check_s = false;
      });
      setListHangMucOCR([...datas]);
      setListHangMucOCRBase([...datas]);
    }
  };

  // const onChangeInputValue = (value, index) => {
  //   let datas = duLieuBaoGia;
  //   datas[index].bt = value;
  //   datas[index].ten_hang_muc = value;
  //   datas[index].don_gia = value;
  //   datas[index].tien_duyet = value;
  //   datas[index].so_luong = value;
  //   setListHangMucOCR([...datas]);
  // };

  const onChangeInputDonGia = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].don_gia = value;
    setListHangMucOCR([...datas]);
  };
  const onChangeInputSoLuong = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].so_luong = value;
    setListHangMucOCR([...datas]);
  };
  const onChangeInputTienDuyet = (value, index) => {
    let datas = duLieuBaoGia;
    datas[index].tien_duyet = value;
    setListHangMucOCR([...datas]);
  };
  const onSelectHangMuc = (item) => {
    let datas = duLieuBaoGia;
    datas[itemIndex].ten_hang_muc_he_thong = item.ten_hang_muc;
    setListHangMucOCR([...datas]);
  };

  const onSetCheckVatTu = (value, idx) => {
    let datas = duLieuBaoGia;
    datas[idx].check_vt = value;
    if (value) {
      datas[idx].check_nc = !value;
      datas[idx].check_s = !value;
    }
    setListHangMucOCR([...datas]);
  };
  const onSetCheckNhanCong = (value, idx) => {
    let datas = duLieuBaoGia;
    datas[idx].check_nc = value;
    if (value) {
      datas[idx].check_vt = !value;
      datas[idx].check_s = !value;
    }
    setListHangMucOCR([...datas]);
  };
  const onSetCheckSon = (value, idx) => {
    let datas = duLieuBaoGia;
    datas[idx].check_s = value;
    if (value) {
      datas[idx].check_vt = !value;
      datas[idx].check_nc = !value;
    }
    setListHangMucOCR([...datas]);
  };

  const onOpenModalHangMuc = (index) => {
    setItemIndex(index);
    refModal.current.show();
  };

  const onSubmit = (data) => console.log(data);

  const renderItem = ({item, index}) => {
    return (
      <View style={styles.itemView}>
        <View marginHorizontal={10}>
          <Text style={{marginBottom: 10, color: colors.PRIMARY, fontSize: 16, fontWeight: '600'}} children={index + 1} />
          <View style={styles.titleRow}>
            <Text style={styles.txtTitle} children="Dữ liệu OCR" />
            <Text style={{color: colors.BLUE1, fontWeight: '600'}} children={' (Tỉ lệ khớp ' + (item.tl_khop * 100).toFixed() + '%)'} />
          </View>
          <View flexDirection="row">
            <Text style={{color: colors.PRIMARY}} children={item.ten_hang_muc} />
          </View>
          <View flexDirection="row" alignItems="center">
            <View flexDirection="row" alignItems="center" width={dimensions.width / 3}>
              <Text children="Số lượng:" />
              <TextInputOutlined
                value={item.so_luong}
                inputStyle={styles.inputStyle}
                containerStyle={styles.input}
                onChangeText={(val) => onChangeInputSoLuong(val, index)}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
            <View flex={1} flexDirection="row" alignItems="center" marginLeft={10}>
              <Text children="Đơn giá:" />
              <TextInputOutlined
                inputStyle={styles.inputStyle}
                containerStyle={styles.input}
                onChangeText={(val) => onChangeInputDonGia(val, index)}
                value={item.don_gia}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
          </View>
          <View style={styles.titleRow}>
            <Text style={styles.txtTitle} children="Hạng mục hệ thống" />
          </View>
          <View flexDirection="row" justifyContent="space-between">
            <TouchableOpacity onPress={() => onOpenModalHangMuc(index)}>
              <Text style={{color: colors.PRIMARY}} children={item.ten_hang_muc_he_thong} />
            </TouchableOpacity>
            <View style={styles.checkView}>
              <View alignItems="center">
                <Text children="Vật tư" />
                <CheckboxComp checkboxStyle={{margin: spacing.tiny}} value={item.check_vt} onValueChange={(value) => onSetCheckVatTu(value, index)} />
              </View>
              <View marginHorizontal={10} alignItems="center">
                <Text children="Nhân công" />
                <CheckboxComp checkboxStyle={{margin: spacing.tiny}} value={item.check_nc} onValueChange={(value) => onSetCheckNhanCong(value, index)} />
              </View>
              <View alignItems="center">
                <Text children="Sơn" />
                <CheckboxComp checkboxStyle={{margin: spacing.tiny}} value={item.check_s} onValueChange={(value) => onSetCheckSon(value, index)} />
              </View>
            </View>
          </View>
          <View flexDirection="row" alignItems="center" marginBottom={10}>
            <View flexDirection="row" alignItems="center" width={dimensions.width / 3}>
              <Text children="Số lượng:" />
              <TextInputOutlined editable={false} disabled value={item.so_luong} inputStyle={styles.inputStyle} containerStyle={styles.input} placeholder="0" keyboardType="numeric" />
            </View>
            <View flex={1} flexDirection="row" alignItems="center" marginLeft={10}>
              <Text children="Tiền duyệt:" />
              <TextInputOutlined
                value={item.tien_duyet}
                inputStyle={styles.inputStyle}
                containerStyle={styles.input}
                onChangeText={(val) => onChangeInputTienDuyet(val, index)}
                placeholder="0"
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {listHeader()}
      <KeyboardAwareScrollView>
        <FlatList removeClippedSubviews={true} data={ListHangMucOCR} renderItem={renderItem} keyExtractor={(item, index) => index.toString()} ListEmptyComponent={<Empty />} />
      </KeyboardAwareScrollView>

      <ModalChonHangMuc listHangMuc={listHangMuc} setValue={onSelectHangMuc} ref={refModal} onBackPress={() => refModal.current.hide()} />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  txtTitle: {
    fontWeight: '600',
    color: colors.RED1,
  },
  titleRow: {
    flexDirection: 'row',
    marginBottom: spacing.smaller,
    justifyContent: 'space-between',
  },
  input: {
    flex: 1,
    padding: 0,
    marginLeft: 10,
    textAlign: 'right',
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
  },
  inputStyle: {
    borderWidth: 0,
    paddingLeft: 0,
    paddingRight: 4,
    textAlign: 'right',
    alignItems: 'center',
    paddingVertical: spacing.tiny,
    borderRadius: 2,
  },
  checkView: {
    marginTop: -10,
    alignItems: 'center',
    flexDirection: 'row',
  },
  itemView: {
    marginBottom: 10,
    borderBottomWidth: 8,
    borderColor: colors.GRAY2,
  },
});

export const RenderDuLieuOCR = memo(RenderDuLieuOCRComponent, isEqual);
