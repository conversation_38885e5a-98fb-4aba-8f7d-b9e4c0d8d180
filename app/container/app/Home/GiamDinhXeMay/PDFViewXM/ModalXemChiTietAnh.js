import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

function ModalXemChiTietAnh(props) {
  return (
    <Modal
      isVisible={props.toggleModalImage}
      onSwipeComplete={() => props.setToggleModalImage(false)}
      onBackdropPress={() => props.setToggleModalImage(false)}
      swipeDirection={['down']}
      propagateSwipe={true}
      style={styles.modal}>
      <View style={styles.modalImageView}>
        <View style={styles.modalImageContentView}>
          <View style={styles.modalTitleView}>
            <Text style={styles.modalTitle}>{props.modalImageSelectedData?.title}</Text>
            <TouchableOpacity style={styles.closeView} onPress={() => props.setToggleModalImage(false)}>
              <Icon.AntDesign
                name="closecircle"
                color="red"
                size={25}
                style={{
                  backgroundColor: colors.WHITE,
                  borderRadius: 20,
                }}
              />
            </TouchableOpacity>
          </View>
          <Image
            source={
              props.modalImageSelectedData?.base64
                ? {
                    uri: `data:image/gif;base64,${props.modalImageSelectedData.imageData}`,
                  }
                : props.modalImageSelectedData?.imageData
            }
            style={styles.imageModal}
            resizeMode={'contain'}
          />
        </View>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalImageView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: dimensions.height,
    width: dimensions.width,
  },
  modalImageContentView: {
    backgroundColor: colors.WHITE,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  closeView: {
    marginRight: 15,
  },
  imageModal: {
    width: dimensions.width - 30,
    height: dimensions.width - 30,
    borderRadius: 20,
    margin: 10,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 10,
    flex: 1,
    marginLeft: 30,
    // borderWidth: 1,
  },
});

const mapStateToProps = (state) => ({});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ModalXemChiTietAnh);
