import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {Component} from 'react';
import {Image, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';

interface Props {
  setToggleModalMailConfirm;
  inputEmail: string;
  onChangeEmail;
  inputErr;
  onPressGuiMail;
}

class ModalMailConfirm extends Component<Props> {
  constructor(props) {
    super(props);
  }

  render() {
    const {setToggleModalMailConfirm, inputEmail, onChangeEmail, inputErr, onPressGuiMail} = this.props;
    return (
      <View style={styles.modalSuccessView}>
        <View style={styles.successTopView}>
          <Image source={R.images.img_mail_user} style={styles.imgMailUser} resizeMode={'contain'} />
          <Text style={styles.successTitle}>Xác nhận người nhận mail</Text>
        </View>
        <View style={styles.successCenterView}>
          {/* <ScrollView
            ref={scrollViewModalRef}
            onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}
            scrollEventThrottle={16}
            showsVerticalScrollIndicator={false}
            style={{minHeight: 20, width: width - 30, maxHeight: 150}}>
            <View
              onLayout={(event) => {
                setFlatListHeight(event.nativeEvent.layout.height);
                scrollViewModalRef.current.scrollToEnd({animated: true});
              }}>
              {listMail.map((item, index) => (
                <View style={{flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 20, marginVertical: 3}}>
                  <Text children={item.value} />
                  <CheckboxComp value={item.checked} onValueChange={(value) => onChangeCheckBox(value, index)} />
                </View>
              ))}
            </View>
          </ScrollView> */}
          <View style={styles.addMailView}>
            <TextInput style={styles.inputMail} keyboardType="email-address" value={inputEmail} onChangeText={onChangeEmail} placeholderTextColor="#CCC" />
            {/* <TouchableOpacity onPress={addNewMail} style={styles.iconAddMail}>
              <Icon.MaterialCommunityIcons name="email-plus" size={20} />
            </TouchableOpacity> */}
          </View>
          {inputErr[0] != '' && <Text children={inputErr[0]} style={{color: colors.RED1, alignSelf: 'flex-start', marginLeft: 20}} />}

          <View style={{flexDirection: 'row', marginTop: 10}}>
            <TouchableOpacity
              style={{
                ...styles.btnSuccessView,
                backgroundColor: colors.PRIMARY_08,
              }}
              onPress={() => setToggleModalMailConfirm(false)}>
              <View style={styles.buttonModalView}>
                <Icon.AntDesign name="close" color={colors.WHITE} size={20} />
                <Text style={styles.txtSuccess} children="Đóng" />
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                ...styles.btnSuccessView,
                backgroundColor: 'green',
              }}
              onPress={onPressGuiMail}>
              <View style={styles.buttonModalView}>
                <Icon.MaterialCommunityIcons name="email-send-outline" color={colors.WHITE} size={20} />
                <Text style={styles.txtSuccess} children="Gửi mail" />
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }
}
export default ModalMailConfirm;

const styles = StyleSheet.create({
  modalSuccessView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  successTopView: {
    width: dimensions.width - 30,
    height: dimensions.height / 6,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // borderBottomWidth: 5,
    // borderBottomColor: colors.WHITE,
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    // marginTop: 20,
  },
  successCenterView: {
    width: dimensions.width - 30,
    // height: 60,
    paddingBottom: 10,
    backgroundColor: colors.WHITE,
    justifyContent: 'space-around',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    // flexDirection: 'row',
    // borderWidth: 1,
    // flex: 1,
    // borderTopWidth: 5,
  },
  addMailView: {
    flexDirection: 'row',
    // alignItems: 'center',
    // height: 70,
    justifyContent: 'center',
    width: dimensions.width - 60,
    // borderWidth: 1,
  },
  inputMail: {
    borderBottomWidth: 0.5,
    paddingLeft: 20,
    borderWidth: 0.5,
    flex: 1,
    borderRadius: 30,
    height: 50,
  },
  iconAddMail: {
    borderRadius: 10,
    borderWidth: 1,
    padding: 10,
  },
  btnSuccessView: {
    height: 40,
    width: dimensions.width / 2 - 40,
    // borderWidth: 1,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 10,
    // flexDirection: 'row',
    // justifyContent: 'center',
  },
  buttonModalView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtSuccess: {
    textAlign: 'center',
    paddingHorizontal: 10,
    color: colors.WHITE,
    fontSize: 16,
  },
  imgMailUser: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});
