import {StyleSheet, Dimensions, Platform} from 'react-native';
const {width, height} = Dimensions.get('screen');
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {colors} from '../../../../../commons/Theme';
export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingTop: Platform.OS == 'ios' ? getStatusBarHeight() : 0,
    // alignItems: 'center',
    // marginTop: 25,
  },
  pdf: {
    flex: 1,
    width: width,
    height: height,
  },
  shareView: {
    position: 'absolute',
    right: 10,
    bottom: 10,
    borderRadius: 100,
    borderWidth: 3,
    borderColor: colors.PRIMARY,
    backgroundColor: colors.WHITE,
  },
  shareIcon: {
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    borderWidth: 1,
    borderRadius: 35,
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  modalSuccessView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  imgMailUser: {
    width: width / 4,
    height: width / 4,
  },
  successTopView: {
    width: width - 30,
    height: height / 6,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // borderBottomWidth: 5,
    // borderBottomColor: colors.WHITE,
  },
  btnSuccessView: {
    height: 40,
    width: width / 2 - 40,
    // borderWidth: 1,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 10,
    // flexDirection: 'row',
    // justifyContent: 'center',
  },
  successCenterView: {
    width: width - 30,
    // height: 60,
    paddingBottom: 10,
    backgroundColor: colors.WHITE,
    justifyContent: 'space-around',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    // flexDirection: 'row',
    // borderWidth: 1,
    // flex: 1,
    // borderTopWidth: 5,
  },
  txtSuccess: {
    textAlign: 'center',
    paddingHorizontal: 10,
    color: colors.WHITE,
    fontSize: 16,
  },
  buttonModalView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    // marginTop: 20,
  },
  addMailView: {
    flexDirection: 'row',
    // alignItems: 'center',
    // height: 70,
    justifyContent: 'center',
    width: width - 60,
    // borderWidth: 1,
  },
  inputMail: {
    borderBottomWidth: 0.5,
    paddingLeft: 20,
    borderWidth: 0.5,
    flex: 1,
    borderRadius: 30,
    height: 50,
  },
  iconAddMail: {
    borderRadius: 10,
    borderWidth: 1,
    padding: 10,
  },
  bottomView: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flex: 1,
    paddingVertical: 10,
    paddingBottom: 20,
    backgroundColor: colors.WHITE,
    paddingHorizontal: 10,
  },
});
