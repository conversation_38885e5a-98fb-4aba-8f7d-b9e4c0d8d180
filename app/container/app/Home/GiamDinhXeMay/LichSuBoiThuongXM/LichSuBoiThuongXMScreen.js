import {colors, dimension} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, StyleSheet, View} from 'react-native';
import {NumericFormat} from 'react-number-format';

const LichSuBoiThuongXMScreenComponent = (props) => {
  console.log('LichSuBoiThuongXMScreenComponent');
  const {route} = props;
  const {profileData} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [lichSuBoiThuong, setLichSuBoiThuong] = useState([]);

  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    let params = {
      so_id: profileData.ho_so.so_id,
      so_id_hd: profileData.ho_so.so_id_hd,
      so_id_dt: profileData.ho_so.so_id_dt,
      nv: 'XE_MAY',
    };
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_LICH_SU_BOI_THUONG_OTO, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setLichSuBoiThuong(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  /**RENDER  */
  const renderLichSuBoiThuongItem = ({item, index}) => {
    index = lichSuBoiThuong.length - index;
    const renderLabel = (label, value) => {
      return (
        <View style={styles.contentRow}>
          <Text style={styles.subLabel}>{label}:</Text>
          {typeof value === 'number' ? (
            <NumericFormat value={item.tien} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={{flex: 1}}>{' ' + val}</Text>} />
          ) : (
            <Text style={styles.content}>{value}</Text>
          )}
        </View>
      );
    };
    return (
      <View style={styles.resolveItemView}>
        <View flexDirection="row" alignItems="center">
          <View style={styles.verticalLineStep}>
            <Text style={{color: colors.WHITE, fontSize: FontSize.size11}}>{index}</Text>
          </View>
          <View style={styles.titleView}>
            {item?.so_hs.trim() !== '' ? <Text style={styles.title}>{item?.so_hs}</Text> : <Text style={[styles.title, {color: colors.BLACK_03}]}>{'Hồ sơ chưa lấy số'}</Text>}
            <Text style={styles.date}>Thời gian: {item.ngay_ht}</Text>
          </View>
        </View>
        <View style={styles.contentColumn}>
          {renderLabel('Số tiền', item.tien)}
          {renderLabel('Trạng thái', item.trang_thai)}
        </View>
      </View>
    );
  };
  return (
    <ScreenComponent
      // renderRightHeader={() => <Icon.FontAwesome name="motorcycle" size={20} color={colors.WHITE} />}
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Lịch sử bồi thường"
      renderView={
        <View style={styles.container}>
          <FlatList
            style={{paddingVertical: spacing.small}}
            data={lichSuBoiThuong}
            showsVerticalScrollIndicator={false}
            renderItem={renderLichSuBoiThuongItem}
            keyExtractor={(item, index) => item.data + index.toString()}
            ListEmptyComponent={<Empty imageStyle={{width: dimension.width / 4, height: dimension.width / 4}} />}
          />
        </View>
      }
    />
  );
};

export const LichSuBoiThuongXMScreen = memo(LichSuBoiThuongXMScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  resolveItemView: {
    marginHorizontal: scale(spacing.smaller),
  },
  verticalLineStep: {
    width: 22,
    height: 22,
    backgroundColor: colors.PRIMARY,
    borderWidth: 1,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: 'white',
  },
  contentColumn: {
    borderLeftWidth: 1,
    borderColor: colors.GRAY,
    marginLeft: scale(spacing.small),
    marginBottom: vScale(spacing.medium),
  },
  title: {
    marginBottom: 4,
    fontWeight: '500',
    fontSize: FontSize.size14,
    color: colors.PRIMARY,
  },

  date: {
    fontSize: FontSize.size12,
    color: colors.GRAY6,
  },
  subLabel: {
    fontWeight: '400',
    fontSize: FontSize.size14,
    color: colors.GRAY6,
    marginLeft: scale(20),
  },
  content: {
    fontWeight: '400',
    fontSize: FontSize.size14,
    color: colors.PRIMARY,
    marginLeft: scale(spacing.tiny),
  },
  titleView: {
    marginLeft: 10,
    marginBottom: vScale(spacing.small),
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: vScale(spacing.tiny),
  },
});
