import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  itemContainer: {
    paddingHorizontal: spacing.smaller,
    paddingVertical: spacing.smaller,
    marginVertical: spacing.tiny,
    backgroundColor: '#FFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  themHangMucView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: spacing.smaller,
  },
  txtThemHangMuc: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.PRIMARY,
    paddingRight: spacing.smaller,
  },
  garaDetailView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // flex: 1,
  },
  rowData: {
    flexDirection: 'row',
    paddingVertical: spacing.tiny,
  },
  txtLabel: {
    color: colors.GRAY6,
  },
  txtValue: {
    flex: 1,
    color: colors.PRIMARY,
  },
  txtGaraName: {
    fontSize: 16,
    color: colors.PRIMARY,
  },
  footerView: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
    paddingHorizontal: spacing.small,
  },
  subLabel: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.PRIMARY,
    marginTop: spacing.medium,
    textTransform: 'uppercase',
    paddingBottom: spacing.tiny,
    marginHorizontal: spacing.small,
  },
  searchBtn: {
    marginBottom: 20,
    width: width / 3,
    marginHorizontal: 0,
  },
  txtSum: {
    fontWeight: '500',
    fontStyle: 'italic',
    color: colors.BLACK_03,
    textTransform: 'lowercase',
  },
});
