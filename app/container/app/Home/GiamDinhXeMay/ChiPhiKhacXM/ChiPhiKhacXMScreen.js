import {colors} from '@app/commons/Theme';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import styles from './ChiPhiKhacXmStyles';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';

let timer;

const dataLoaiChiPhi = [
  {label: 'Chọn loại chi phí', value: ''},
  {label: 'Chi phí giám định với đơn vị giám định ngoài', value: 'CP_GIAM_DINH'},
  {label: 'Chi phí cẩu / kéo', value: 'CP_CAU_KEO'},
  {label: 'Chi phí khác', value: 'KHAC'},
];

const ChiPhiKhacXMScreenComponent = (props) => {
  console.log('ChiPhiKhacXMScreenComponent');
  const {route, navigation} = props;
  const {profileData} = route?.params;
  const [dsChiPhi, setDsChiPhi] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tongChiPhi, setTongChiPhi] = useState(0);
  const [type, setType] = useState();
  // const [selectedItem, setSelectedItem] = useState({});

  const refItemSelected = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDsChiPhi();
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getChiTietChiPhi = async (item, action) => {
    try {
      setLoading(true);
      let params = {
        so_id: item?.so_id || '',
        ma_chi_phi: item?.ma_chi_phi || '', // note lại báo API chỉ search được cẩu || kéo
        bt: item?.bt || '',
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_CHI_TIET_CHI_PHI_XM, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response?.data_info;
      // setSelectedItem(data);
      refItemSelected.current = data?.nhom;
      if (action === 'edit') return onPressThemChiPhi(type, data);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }

    return;
  };
  const getDsChiPhi = async () => {
    setLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_chi_phi: '', // note lại báo API chỉ search được cẩu || kéo
        pm: 'BT',
        tim: '',
        trang: 1,
        so_dong: 100,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_CHI_PHI_KHAC_XM, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response?.data_info.data;
      let tongCong = 0;
      data.map((item) => {
        let sum = +item.tong_cong;
        tongCong += sum;
      });
      setTongChiPhi(tongCong);
      setDsChiPhi(data);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }

    return;
  };

  const onRefresh = () => {
    getDsChiPhi();
  };

  const onPressThemChiPhi = (action, item = null) => {
    setType(action);
    // refModalThemChiPhiKhac.current.show();
    let routeparams = {
      profileData: profileData,
      selectedItem: item,
      type: action,
      dataLoaiChiPhi: dataLoaiChiPhi,
    };
    NavigationUtil.push(SCREEN_ROUTER_APP.THEM_CHI_PHI_CAU_KEO_KHAC_XM, routeparams);
  };

  const onPressRemoveChiPhi = (item) => {
    getChiTietChiPhi(item);
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn xoá chi phí này?', [
      {text: 'Để sau', style: 'destructive'},
      {text: 'Đồng ý', onPress: () => onRemoveChiPhi(item)},
    ]);
  };

  const onRemoveChiPhi = async (item) => {
    setLoading(true);
    try {
      let params = {
        bt: item.bt,
        so_id: profileData?.ho_so?.so_id,
        nhom: refItemSelected?.current || '',
        pm: 'GD',
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_CHI_PHI_KHAC_XM, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      getDsChiPhi();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }

    return;
  };

  const renderItem = ({item, index}) => {
    const lastItem = index === dsChiPhi.length - 1;
    return (
      <View style={[styles.itemContainer, {marginBottom: lastItem ? 60 : spacing.tiny}]}>
        <View style={styles.garaDetailView}>
          <View style={{flex: 1}}>
            <View style={styles.rowData}>
              <Text children="Loại chi phí: " style={[styles.txtLabel]} />
              <Text children={item.ten_loai_chi_phi} style={[styles.txtValue]} />
            </View>
            <View style={styles.rowData}>
              <Text children="Tên chi phí: " style={styles.txtLabel} />
              <Text children={item.ten_chi_phi} style={[styles.txtValue]} />
            </View>
            <View style={styles.rowData}>
              <Text children="Đối tác: " style={styles.txtLabel} />
              <Text children={item.ten_dvi_gd} style={[styles.txtValue]} />
            </View>
            <View style={styles.rowData}>
              <Text children="Tổng số tiền: " style={styles.txtLabel} />
              <NumericFormat
                value={item.tong_cong || 0}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => <Text children={value} style={[styles.txtValue, , {color: colors.BLACK_03}]} />}
              />
            </View>
          </View>
          <View>
            <TouchableOpacity style={{marginBottom: spacing.smaller}} onPress={() => getChiTietChiPhi(item, 'edit')}>
              <Icon.FontAwesome name="edit" color={colors.PRIMARY} size={20} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => onPressRemoveChiPhi(item)}>
              <Icon.AntDesign name="closesquareo" color={colors.RED1} size={20} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  /** RENDER */
  const renderDsChiPhi = () => (
    <FlatList
      data={dsChiPhi}
      renderItem={renderItem}
      refreshControl={<RefreshControl refreshing={loading} onRefresh={onRefresh} />}
      style={{paddingBottom: spacing.mediumPlush}}
      keyExtractor={(item, index) => index.toString()}
      ListEmptyComponent={<Empty description="Danh sách trống" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
    />
  );

  return (
    <ScreenComponent
      dialogLoading={loading}
      headerBack
      headerTitle="Chi phí khác"
      renderView={
        <SafeAreaView style={styles.container}>
          {/* <View marginHorizontal={10}>
            <DropdownPicker
              zIndex={9000}
              title="Loại chi phí"
              isOpen={openDropdown}
              items={dataLoaiChiPhi}
              setOpen={setOpenDropdown}
              itemSelected={itemSelected}
              placeholder="Chọn loại chi phí"
              setItemSelected={setItemSelected}
              // isRequired={true}
              searchable={false}
              // maxHeight={150}
              // inputErr={errGara}
            />
            <TextInputOutlined
              editable={true}
              disabled={false}
              value={tenDoiTac}
              keyboardType="default"
              title="Thông tin tìm kiếm"
              onChangeText={setTenDoiTac}
              placeholder="Nhập tên đối tác"
              containerStyle={{marginBottom: 0}}
            />
            <View marginTop={10} marginBottom={20} alignItems="center">
              <ButtonLinear onPress={getDsChiPhi} loading={false} title="Tìm kiếm" linearStyle={styles.searchBtn} />
            </View>
          </View> */}
          <Text style={styles.subLabel}>
            Danh sách chi phí{' '}
            <NumericFormat value={tongChiPhi || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.txtSum} children={'(Tổng cộng: ' + value + ' đ)'} />} />
          </Text>
          {renderDsChiPhi()}
        </SafeAreaView>
      }
      footer={<ButtonLinear onPress={() => onPressThemChiPhi('new', null)} title="Thêm chi phí" />}
    />
  );
};

export const ChiPhiKhacXMScreen = memo(ChiPhiKhacXMScreenComponent, isEqual);
