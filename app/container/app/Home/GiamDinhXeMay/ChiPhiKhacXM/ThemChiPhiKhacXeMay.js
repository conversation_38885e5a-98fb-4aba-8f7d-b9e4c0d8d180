import {colors} from '@app/commons/Theme';
import {CustomModal} from '@app/components/CustomModal';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {getPercentageValue} from '@app/utils/string';
import {ButtonLinear, DropdownPicker, Icon, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Dimensions, FlatList, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

const {width} = Dimensions.get('window');
const dataTLThue = [
  {label: '0 %', value: 0},
  {label: '5 %', value: 5},
  {label: '8 %', value: 8},
  {label: '10 %', value: 10},
];

const ThemChiPhiKhacXeMayScreenComponent = (props) => {
  console.log('ThemChiPhiKhacXeMayScreenComponent');
  const {route} = props;
  const {type, selectedItem, profileData, dataLoaiChiPhi} = route.params;
  //data
  const [dsDoiTuong, setDsDoiTuong] = useState([]);
  const [lhnv, setLhnv] = useState([]);
  // const [dataDvi, setDataDVi] = useState([]);
  //toggle
  const [openDropdown, setOpenDropdown] = useState(false);
  const [openDropdownLhnv, setOpenDropdownLhnv] = useState(false);
  const [openChonDoiTuong, setOpenChonDoiTuong] = useState(false);
  // const [showDropdownDonVi, setShowDropdownDonVi] = useState(true);
  // const [openDropdownDonVi, setOpenDropdownDonVi] = useState(false);
  const [showRowCauXe, setShowRowCauXe] = useState(true);
  const [showRowKeoXe, setShowRowKeoXe] = useState(true);
  const [showTenChiPhi, setShowTenChiPhi] = useState(true);
  const [showRowNhapTienChuaFilter, setShowRowNhapTienChuaFilter] = useState(true);
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [modalType, setModalType] = useState('');
  const [tyLeThueSelected, setTyLeThueSelected] = useState('');

  let refModal = useRef(null);

  const getDefaultFormValue = () => {
    return {
      tienKhac: selectedItem?.chi_phi_khac || 0,
      tienCauXe: selectedItem?.chi_phi_cau || 0,
      tlThueCau: selectedItem?.tl_thue_chi_phi_cau || 0,
      tienThueCau: selectedItem?.tien_thue_chi_phi_cau || 0,
      tienKeoXe: selectedItem?.chi_phi_keo || 0,
      tlThueKeo: selectedItem?.tl_thue_chi_phi_keo || 0,
      tienThueKeo: selectedItem?.tien_thue_chi_phi_keo || 0,
      tenChiPhi: selectedItem?.ten_chi_phi || '',
      tlThueKhac: selectedItem?.tl_thue_chi_phi_khac || 0,
      tienThueKhac: selectedItem?.tien_thue_chi_phi_khac || 0,
      doiTuongTonThat: selectedItem?.so_id_doi_tuong || '',
      loaiHinhNghiepVu: selectedItem?.lh_nv || '',
      loaiChiPhi: selectedItem?.ma_chi_phi || '',
      donVi: selectedItem?.ma || '',
      ptGiamTruKhac: selectedItem?.pt_giam_tru_khac || 0,
      ptTrachNhiemKhac: selectedItem?.pt_bao_hiem_khac || 0,
      ptBaoHiemCauXe: selectedItem?.pt_bao_hiem_cau || 0,
      ptGiamTruCauXe: selectedItem?.pt_giam_tru_cau || 0,
      ptBaoHiemKeoXe: selectedItem?.pt_bao_hiem_keo || 0,
      ptGiamTruKeoXe: selectedItem?.pt_giam_tru_keo || 0,
      tienBaoGiaCau: selectedItem?.tien_bgia_cau || 0,
      tienBaoGiaKeo: selectedItem?.tien_bgia_keo || 0,
      tienBaoGiaKhac: selectedItem?.tien_bgia_khac || 0,
    };
  };

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  useEffect(() => {
    initData();
  }, []);

  //init Data
  const initData = () => {
    // resetModalData();
    initInputFieldValue();
    initDsDoiTuong();
    initLoaiHinhNv();
  };
  const tienCauXe = watch('tienCauXe');
  const tlThueCau = watch('tlThueCau');
  const tienKeoXe = watch('tienKeoXe');
  const tlThueKeo = watch('tlThueKeo');
  const tlThueKhac = watch('tlThueKhac');
  const tienKhac = watch('tienKhac');

  const loaiChiPhi = watch('loaiChiPhi');
  // const donVi = watch('donVi');

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const initDsDoiTuong = () => {
    if (profileData?.ds_doi_tuong?.length > 0) {
      let filter = profileData?.ds_doi_tuong; //.filter((item) => item.nhom === 'XE' || item.loai === 'XE' || item.loai === 'XE_MAY');
      filter.map((item, index) => {
        filter[index].label = item.ten_doi_tuong;
        filter[index].value = item.so_id_doi_tuong;
      });
      setDsDoiTuong(filter);
      //   if (filter.length === 1 && type === 'new') {
      //     setValue('doiTuongTonThat', filter[0].so_id_doi_tuong);
      //   }
    }
  };

  const initLoaiHinhNv = () => {
    if (profileData?.lh_nv?.length > 0) {
      let filter = profileData.lh_nv;
      filter.map((item, index) => {
        filter[index].label = item.ten;
        filter[index].value = item.ma;
      });
      setLhnv(filter);
      //   if (filter.length === 1 && type === 'new') {
      //     setValue('loaiHinhNghiepVu', filter[0].ma);
      //   }
    }
  };

  //init form sửa chi phí
  const initInputFieldValue = () => {
    if (selectedItem === null) return;
    if (selectedItem?.ma_chi_phi !== 'CP_GIAM_DINH' && selectedItem?.ma_chi_phi !== 'KHAC') {
      setValue('loaiChiPhi', 'CP_CAU_KEO');
    } else setValue('loaiChiPhi', selectedItem?.ma_chi_phi);
  };

  //show-hide input
  useEffect(() => {
    if (loaiChiPhi === 'KHAC' || loaiChiPhi === '') {
      setShowRowCauXe(false);
      setShowRowKeoXe(false);
      setShowTenChiPhi(true);
      // setShowDropdownDonVi(false);
      setShowRowNhapTienChuaFilter(true);
      return;
    } else if (loaiChiPhi === 'CP_CAU_KEO' || loaiChiPhi === 'CP_CAU' || loaiChiPhi === 'CP_KEO') {
      setShowRowCauXe(true);
      setShowRowKeoXe(true);
      // setShowDropdownDonVi(true);
      setShowTenChiPhi(false);
      setShowRowNhapTienChuaFilter(false);
      // getDataDVi();
      return;
    } else if (loaiChiPhi === 'CP_GIAM_DINH') {
      setShowRowCauXe(false);
      setShowRowKeoXe(false);
      // setShowDropdownDonVi(true);
      setShowTenChiPhi(true);
      setShowRowNhapTienChuaFilter(true);
      setValue('tenChiPhi', 'Chi phí giám định với đơn vị giám định ngoài');
      // getDataDVi();
      return;
    }
    // getDataDVi();
    // setShowDropdownDonVi(true);
  }, [loaiChiPhi]);

  //show-hide, set defaul input
  // useEffect(() => {
  //   if (donVi === 'DVI_CAU_KEO') {
  //     setValue('tienKhac', 0);
  //     setShowRowCauXe(true);
  //     setShowRowKeoXe(true);
  //     setValue('tenChiPhi', 'Chi phí cẩu và kéo');
  //     return;
  //   } else if (donVi === 'DVI_CAU') {
  //     setShowRowCauXe(true);
  //     setShowRowKeoXe(false);
  //     setValue('tenChiPhi', 'Chi phí cẩu');
  //     setValue('tienKeoXe', 0);
  //   } else if (donVi === 'DVI_KEO') {
  //     setShowRowCauXe(false);
  //     setShowRowKeoXe(true);
  //     setValue('tienCauXe', 0);
  //     setValue('tenChiPhi', 'Chi phí kéo');
  //   }
  // }, [donVi]);

  //  Tính toán tiền thuế
  useEffect(() => {
    let calc = (tienKhac * tlThueKhac) / 100;
    if (calc >= 1) {
      setValue('tienThueKhac', calc);
    } else setValue('tienThueKhac', 0);
  }, [tlThueKhac, tienKhac]);

  useEffect(() => {
    let calc = (tienCauXe * tlThueCau) / 100;
    if (calc >= 1) {
      setValue('tienThueCau', calc);
    } else setValue('tienThueCau', 0);
  }, [tlThueCau, tienCauXe]);

  useEffect(() => {
    let calc = (tienKeoXe * tlThueKeo) / 100;
    if (calc >= 1) {
      setValue('tienThueKeo', calc);
    } else setValue('tienThueKeo', 0);
  }, [tlThueKeo, tienKeoXe]);

  // const getDataDVi = async () => {
  //   try {
  //     let params = {
  //       ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
  //       ma_chi_phi: selectedItem?.nhom === 'CAU_KEO' ? 'CP_CAU_KEO' : loaiChiPhi,
  //       nhom: selectedItem?.nhom || '',
  //       ma: '',
  //       ma_gara: '',
  //     };
  //     let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_DON_VI, params);
  //     console.log('🚀 ~ file: ThongTinChiPhiKhac.js:240 ~ getDataDVi ~ response:', response);
  //     if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //     let rsData = response?.data_info.lke_dvi;
  //     rsData.map((item, index) => {
  //       rsData[index].label = item.ten;
  //       rsData[index].value = item.ma;
  //     });
  //     setDataDVi(response?.data_info.lke_dvi);
  //   } catch (error) {
  //     Alert.alert('Thông báo', error.message);
  //   }
  // };

  //bấm lưu
  const onPressLuu = async (data) => {
    // setIsSubmiting(true);
    try {
      let params = {
        bt: selectedItem?.bt || '',
        so_id: profileData?.ho_so?.so_id || '',
        so_id_doi_tuong: data.doiTuongTonThat || '',
        pm: 'GD',
        ma_chi_phi: data.loaiChiPhi || '',
        ten_chi_phi: data.tenChiPhi || '',
        dvi_tham_gia: data.donVi || '',
        chi_phi_cau: +data.tienCauXe || 0, //tiền
        chi_phi_keo: +data.tienKeoXe || 0,
        chi_phi_khac: +data.tienKhac || 0,

        pt_bao_hiem: null,
        tien_bao_hiem: null,
        pt_giam_tru: null,
        tien_giam_tru: null,
        nguyen_nhan_giam_tru: null,
        dkbs: null,
        ngan_hang: null,
        stk: null,
        chu_tk: null,
        noi_dung: null,
        ma_gara: null,
        tinh_thanh: null,
        quan_huyen: null,
        xa_phuong: null,
        dia_chi: null,
        khoang_cach_km: null,
        tl_thue_chi_phi_cau: +data.tlThueCau || 0,
        tien_thue_chi_phi_cau: +data.tienThueCau || 0,
        tl_thue_chi_phi_keo: +data.tlThueKeo || 0,
        tien_thue_chi_phi_keo: +data.tienThueKeo || 0,
        tl_thue_chi_phi_khac: +data.tlThueKhac || 0,
        tien_thue_chi_phi_khac: +data.tienThueKhac || 0,
        pt_bao_hiem_cau: +data.ptBaoHiemCauXe || 0,
        pt_giam_tru_cau: +data.ptGiamTruCauXe || 0,
        pt_bao_hiem_keo: +data.ptBaoHiemKeoXe || 0,
        pt_giam_tru_keo: +data.ptGiamTruKeoXe || 0,
        pt_bao_hiem_khac: +data.ptTrachNhiemKhac || 0,
        pt_giam_tru_khac: +data.ptGiamTruKhac || 0,
        lh_nv: data.loaiHinhNghiepVu || '',
        tien_bgia_cau: +data.tienBaoGiaCau,
        tien_bgia_keo: +data.tienBaoGiaKeo,
        tien_bgia_khac: +data.tienBaoGiaKhac,
        nguon: 'MOBILE',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.THEM_CHI_PHI_KHAC_XM, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật chi phí thành công', 'success');
      NavigationUtil.pop();
      resetModalData();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  //reset modal data
  const resetModalData = () => {
    setValue('tienCauXe', 0);
    setValue('tienKeoXe', 0);
    setValue('tienKhac', 0);
    setValue('tlThueCau', 0);
    setValue('tlThueKeo', 0);
    setValue('tlThueKhac', 0);
    // setValue('donVi', '');
    setValue('loaiHinhNghiepVu', '');
    setValue('tenChiPhi', '');
    setValue('doiTuongTonThat', '');
    // setValue('loaiChiPhi', '');
    setValue('tienBaoGiaCau', 0);
    setValue('tienBaoGiaKeo', 0);
    setValue('tienBaoGiaKhac', 0);
    setValue('ptBaoHiemCauXe', 0);
    setValue('ptBaoHiemKeoXe', 0);
    setValue('ptTrachNhiemKhac', 0);
    setValue('ptGiamTruCauXe', 0);
    setValue('ptGiamTruKeoXe', 0);
    setValue('ptGiamTruKhac', 0);
  };

  //reset err

  const onSelectTlThue = (item) => {
    if (modalType == 'tlt_cau') {
      setValue('tlThueCau', item.value, {shouldValidate: true});
    }
    if (modalType == 'tlt_keo') {
      setValue('tlThueKeo', item.value, {shouldValidate: true});
    }
    if (modalType == 'tlt') {
      setValue('tlThueKhac', item.value, {shouldValidate: true});
    }
    refModal.current.hide();
  };

  const onOpenTlThue = (type, value) => {
    setModalType(type);
    refModal.current.show();
    setTyLeThueSelected(value);
  };

  // const onInputFocus = (field, value) => {
  //   if (+value === 0) {
  //     setValue(field, '');
  //   }
  // };
  // const onInputBlur = (field, value) => {
  //   if (value === '') {
  //     setValue(field, '0');
  //   }
  // };

  //RENDER
  const renderModalItem = ({item}) => {
    const tlThueSelected = item.value === tyLeThueSelected;
    return (
      <View style={[styles.modalItem]}>
        <TouchableOpacity onPress={() => onSelectTlThue(item)} style={{paddingHorizontal: spacing.mediumPlush}}>
          <View flexDirection="row" alignItems="center">
            {tlThueSelected && <Icon.Feather name="check" size={24} color={colors.PRIMARY} style={styles.checkIcon} />}
            <Text style={tlThueSelected && {color: colors.PRIMARY}}>{item.label}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderContentModal = () => {
    return (
      <View style={styles.customModal}>
        <TouchableOpacity onPress={() => refModal.current.hide()}>
          <Icon.AntDesign name="closecircle" size={20} color={colors.GRAY} style={{textAlign: 'right'}} />
        </TouchableOpacity>
        <FlatList data={dataTLThue} renderItem={renderModalItem} keyExtractor={(_, index) => index.toString()} />
      </View>
    );
  };

  const renderFooter = () => {
    return (
      <View style={styles.btnView}>
        {type !== 'edit' && (
          <ButtonLinear title="Nhập lại" linearStyle={[styles.marginRight]} onPress={resetModalData} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />
        )}
        <ButtonLinear title="Lưu" onPress={handleSubmit(onPressLuu)} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={isSubmiting}
      title="Thông tin chi phí khác"
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView>
            <View marginHorizontal={spacing.small}>
              <Controller
                control={control}
                name="doiTuongTonThat"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    zIndex={9001}
                    isRequired={true}
                    searchable={false}
                    items={dsDoiTuong.filter((item) => item.kieu_dt === 'TT')}
                    itemSelected={value}
                    isOpen={openChonDoiTuong}
                    setOpen={setOpenChonDoiTuong}
                    title="Chọn đối tượng tổn thất"
                    maxHeight={dimensions.height / 4}
                    placeholder="Chọn đối tượng tổn thất"
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    inputErr={errors.doiTuongTonThat && getErrMessage('doiTuongTonThat', errors.doiTuongTonThat.type)}
                  />
                )}
              />
              <Controller
                control={control}
                name="loaiHinhNghiepVu"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    items={lhnv}
                    zIndex={9000}
                    isRequired={true}
                    searchable={false}
                    itemSelected={value}
                    placeholder="Chọn nghiệp vụ"
                    isOpen={openDropdownLhnv}
                    title="Loại hình nghiệp vụ"
                    setOpen={setOpenDropdownLhnv}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    inputErr={errors.loaiHinhNghiepVu && getErrMessage('loaiHinhNghiepVu', errors.loaiHinhNghiepVu.type)}
                  />
                )}
              />

              <Controller
                control={control}
                name="loaiChiPhi"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    zIndex={8000}
                    isRequired={true}
                    searchable={false}
                    itemSelected={value}
                    title="Loại chi phí"
                    isOpen={openDropdown}
                    items={dataLoaiChiPhi}
                    setOpen={setOpenDropdown}
                    placeholder="Chọn loại chi phí"
                    disabled={type == 'edit' ? true : false}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    inputErr={errors.loaiChiPhi && getErrMessage('loaiChiPhi', errors.loaiChiPhi.type)}
                  />
                )}
              />

              {/* {showDropdownDonVi && (
                <Controller
                  control={control}
                  name="donVi"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title="Đối tác (đơn vị cẩu/kéo/khác)"
                      zIndex={7000}
                      isOpen={openDropdownDonVi}
                      setOpen={setOpenDropdownDonVi}
                      items={dataDvi}
                      itemSelected={value}
                      placeholder="Chọn đơn vị"
                      isRequired={true}
                      searchable={false}
                      disabled={type == 'edit' ? true : false}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      inputErr={errors.donVi && getErrMessage('donVi', errors.donVi.type)}
                    />
                  )}
                />
              )} */}

              {showTenChiPhi && (
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="tenChiPhi"
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isRequired
                      value={value}
                      editable={true}
                      title="Tên chi phí"
                      keyboardType="default"
                      onChangeText={onChange}
                      placeholder="Tên loại chi phí"
                      error={errors.tenChiPhi && getErrMessage('tenChiPhi', errors.tenChiPhi.type)}
                    />
                  )}
                />
              )}

              {showRowNhapTienChuaFilter && (
                <>
                  <View flexDirection="row">
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienBaoGiaKhac"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={value}
                          title="Tiền báo giá"
                          editable={true}
                          placeholder="0"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('tienKhac', value)}
                          // onFocus={() => onInputFocus('tienKhac', value)}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          error={errors.tienBaoGiaKhac && getErrMessage('tienBaoGiaKhac', errors.tienBaoGiaKhac.type)}
                        />
                      )}
                    />
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienKhac"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={value}
                          title="Số tiền"
                          editable={true}
                          placeholder="0"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('tienKhac', value)}
                          // onFocus={() => onInputFocus('tienKhac', value)}
                          containerStyle={[styles.inputContainer]}
                          error={errors.tienKhac && getErrMessage('tienKhac', errors.tienKhac.type)}
                        />
                      )}
                    />
                  </View>
                  <View flexDirection="row">
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="ptTrachNhiemKhac"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          maxLength={3}
                          value={getPercentageValue(value)}
                          placeholder="0"
                          editable={true}
                          returnKeyType="next"
                          title="% Trách nhiệm"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          // onBlur={() => onInputBlur('ptTrachNhiemKhac', value)}
                          // onFocus={() => onInputFocus('ptTrachNhiemKhac', value)}
                          error={errors.ptTrachNhiemKhac && getErrMessage('ptTrachNhiemKhac', errors.ptTrachNhiemKhac.type)}
                        />
                      )}
                    />
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="ptGiamTruKhac"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={getPercentageValue(value)}
                          maxLength={3}
                          placeholder="0"
                          title="% Giảm trừ"
                          keyboardType="numeric"
                          returnKeyType={'next'}
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('ptGiamTruKhac', value)}
                          // onFocus={() => onInputFocus('ptGiamTruKhac', value)}
                          containerStyle={[styles.inputContainer]}
                          error={errors.ptGiamTruKhac && getErrMessage('ptGiamTruKhac', errors.ptGiamTruKhac.type)}
                        />
                      )}
                    />
                  </View>
                  <View flexDirection="row">
                    {/* <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tlThueKhac"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isLeftDropdown
                          isRequired
                          value={value}
                          title="% Thuế"
                          editable={false}
                          isTouchableOpacity
                          keyboardType="numeric"
                          placeholder="Tl thuế"
                          inputStyle={styles.textInput}
                          onPress={() => onOpenTlThue('tlt', value)}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          error={errors.tlThueKhac && getErrMessage('tlThueKhac', errors.tlThueKhac.type)}
                        />
                      )}
                    /> */}
                    {/* <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienThueKhac"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          disabled
                          isRequired
                          value={value}
                          placeholder="0"
                          editable={false}
                          title="Tiền thuế"
                          keyboardType="numeric"
                          inputStyle={styles.textInput}
                          containerStyle={[styles.inputContainer, {flex: 1}]}
                          error={errors.tienThueKhac && getErrMessage('tienThueKhac', errors.tienThueKhac.type)}
                        />
                      )}
                    /> */}
                  </View>
                </>
              )}

              {showRowCauXe && (
                <>
                  <Text style={styles.txtBlocktitle}>Chi phí cẩu</Text>
                  <View flexDirection="row">
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienBaoGiaCau"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={value}
                          title="Tiền báo giá"
                          editable={true}
                          placeholder="0"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('tienKhac', value)}
                          // onFocus={() => onInputFocus('tienKhac', value)}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          error={errors.tienBaoGiaCau && getErrMessage('tienBaoGiaCau', errors.tienBaoGiaCau.type)}
                        />
                      )}
                    />
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienCauXe"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={value}
                          title="Số tiền"
                          placeholder="0"
                          keyboardType="numeric"
                          returnKeyType={'next'}
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('tienCauXe', value)}
                          // onFocus={() => onInputFocus('tienCauXe', value)}
                          containerStyle={[styles.inputContainer]}
                          error={errors.tienCauXe && getErrMessage('tienCauXe', errors.tienCauXe.type)}
                        />
                      )}
                    />
                  </View>
                  <View flexDirection="row">
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="ptBaoHiemCauXe"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          maxLength={3}
                          value={getPercentageValue(value)}
                          placeholder="0"
                          editable={true}
                          returnKeyType="next"
                          title="% Trách nhiệm"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          // onBlur={() => onInputBlur('ptBaoHiemCauXe', value)}
                          // onFocus={() => onInputFocus('ptBaoHiemCauXe', value)}
                          error={errors.ptBaoHiemCauXe && getErrMessage('ptBaoHiemCauXe', errors.ptBaoHiemCauXe.type)}
                        />
                      )}
                    />
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="ptGiamTruCauXe"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={getPercentageValue(value)}
                          maxLength={3}
                          placeholder="0"
                          title="% Giảm trừ"
                          keyboardType="numeric"
                          returnKeyType={'next'}
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('ptGiamTruCauXe', value)}
                          // onFocus={() => onInputFocus('ptGiamTruCauXe', value)}
                          containerStyle={[styles.inputContainer]}
                          error={errors.ptGiamTruCauXe && getErrMessage('ptGiamTruCauXe', errors.ptGiamTruCauXe.type)}
                        />
                      )}
                    />
                  </View>
                  <View flexDirection="row">
                    {/* <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tlThueCau"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isLeftDropdown
                          isRequired
                          value={value}
                          title="% Thuế"
                          editable={false}
                          isTouchableOpacity
                          keyboardType="numeric"
                          placeholder="Tỷ lệ thuế"
                          inputStyle={styles.textInput}
                          onPress={() => onOpenTlThue('tlt_cau', value)}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          error={errors.tlThueCau && getErrMessage('tlThueCau', errors.tlThueCau.type)}
                        />
                      )}
                    /> */}
                    {/* <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienThueCau"
                      render={({field: {value}}) => (
                        <TextInputOutlined
                          disabled
                          isRequired
                          value={value}
                          placeholder="0"
                          editable={false}
                          title="Tiền thuế"
                          keyboardType="numeric"
                          inputStyle={styles.textInput}
                          containerStyle={[styles.inputContainer]}
                          error={errors.tienThueCau && getErrMessage('tienThueCau', errors.tienThueCau.type)}
                        />
                      )}
                    /> */}
                  </View>
                </>
              )}
              {showRowCauXe && showRowKeoXe && <View style={styles.divider} />}
              {showRowKeoXe && (
                <>
                  <Text style={styles.txtBlocktitle}>Chi phí kéo</Text>
                  <View flexDirection="row">
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienBaoGiaKeo"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={value}
                          title="Tiền báo giá"
                          editable={true}
                          placeholder="0"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('tienKhac', value)}
                          // onFocus={() => onInputFocus('tienKhac', value)}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          error={errors.tienBaoGiaKeo && getErrMessage('tienBaoGiaKeo', errors.tienBaoGiaKeo.type)}
                        />
                      )}
                    />
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienKeoXe"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={value}
                          placeholder="0"
                          editable={true}
                          title="Số tiền"
                          keyboardType="numeric"
                          clearText
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('tienKeoXe', value)}
                          // onFocus={() => onInputFocus('tienKeoXe', value)}
                          containerStyle={[styles.inputContainer]}
                          error={errors.tienKeoXe && getErrMessage('tienKeoXe', errors.tienKeoXe.type)}
                        />
                      )}
                    />
                  </View>
                  <View flexDirection="row">
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="ptBaoHiemKeoXe"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={getPercentageValue(value)}
                          maxLength={3}
                          placeholder="0"
                          editable={true}
                          returnKeyType="next"
                          title="% Trách nhiệm"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          // onBlur={() => onInputBlur('ptBaoHiemKeoXe', value)}
                          // onFocus={() => onInputFocus('ptBaoHiemKeoXe', value)}
                          error={errors.ptBaoHiemKeoXe && getErrMessage('ptBaoHiemKeoXe', errors.ptBaoHiemKeoXe.type)}
                        />
                      )}
                    />
                    <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="ptGiamTruKeoXe"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          isRequired
                          value={getPercentageValue(value)}
                          maxLength={3}
                          placeholder="0"
                          editable={true}
                          title="% Giảm trừ"
                          keyboardType="numeric"
                          onChangeText={onChange}
                          inputStyle={styles.textInput}
                          // onBlur={() => onInputBlur('ptGiamTruKeoXe', value)}
                          // onFocus={() => onInputFocus('ptGiamTruKeoXe', value)}
                          containerStyle={[styles.inputContainer]}
                          error={errors.ptGiamTruKeoXe && getErrMessage('ptGiamTruKeoXe', errors.ptGiamTruKeoXe.type)}
                        />
                      )}
                    />
                  </View>
                  <View flexDirection="row">
                    {/* <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tlThueKeo"
                      render={({field: {value}}) => (
                        <TextInputOutlined
                          isLeftDropdown
                          isRequired
                          value={value}
                          title="% Thuế"
                          editable={false}
                          isTouchableOpacity
                          keyboardType="numeric"
                          placeholder="Tỷ lệ thuế"
                          inputStyle={styles.textInput}
                          containerStyle={[styles.inputContainer, styles.marginRight]}
                          onPress={() => onOpenTlThue('tlt_keo', value)}
                          error={errors.tlThueKeo && getErrMessage('tlThueKeo', errors.tlThueKeo.type)}
                        />
                      )}
                    /> */}
                    {/* <Controller
                      control={control}
                      rules={{
                        required: true,
                      }}
                      name="tienThueKeo"
                      render={({field: {onChange, value}}) => (
                        <TextInputOutlined
                          disabled
                          isRequired
                          value={value}
                          placeholder="0"
                          editable={false}
                          title="Tiền thuế"
                          keyboardType="numeric"
                          inputStyle={styles.textInput}
                          containerStyle={styles.inputContainer}
                        />
                      )}
                    /> */}
                  </View>
                </>
              )}
            </View>
          </KeyboardAwareScrollView>
          <CustomModal ref={refModal} renderContent={renderContentModal} onBackPress={() => refModal.current.hide()} />
        </SafeAreaView>
      }
      footer={renderFooter()}
    />
  );
};

export const ThemChiPhiKhacXeMayScreen = memo(ThemChiPhiKhacXeMayScreenComponent, isEqual);
const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
  },
  btnView: {
    flex: 1,
    flexDirection: 'row',
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },
  inputContainer: {
    flex: 1,
  },
  customModal: {
    width: width / 2,
    borderRadius: 10,
    padding: scale(10),
    backgroundColor: colors.WHITE,
  },
  modalItem: {
    alignItems: 'center',
    marginVertical: vScale(5),
  },
  textInput: {
    height: 45,
    textAlign: 'right',
    color: colors.BLACK,
  },
  txtBlocktitle: {
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size16,
    textTransform: 'uppercase',
    marginTop: vScale(spacing.small),
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    backgroundColor: colors.GRAY2,
    paddingVertical: scale(spacing.tiny),
    paddingHorizontal: scale(spacing.small),
  },
  marginRight: {
    marginRight: scale(spacing.small),
  },
  divider: {
    height: 5,
    backgroundColor: colors.GRAY2,
    marginTop: vScale(spacing.smaller),
  },
  selectedValue: {
    borderWidth: 0.5,
    borderRadius: 4,
    borderColor: colors.PRIMARY,
    paddingVertical: spacing.tiny,
  },
  checkIcon: {
    position: 'absolute',
    right: spacing.large,
  },
});
