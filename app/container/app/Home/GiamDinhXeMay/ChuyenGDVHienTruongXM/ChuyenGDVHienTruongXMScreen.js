import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ModalChiNhanhTheoDangCay, ScreenComponent, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';
import {ModalNguoiXuLy} from './Components';

const ChuyenGDVHienTruongScreenXMComponent = (props) => {
  console.log('ChuyenGDVHienTruongScreenXMComponent');
  const {route} = props;
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState(route.params.profileData);
  const [listMaDonViXuLySeleted, setListMaDonViXuLySelected] = useState([]); //list mã đơn vị xử lý được chọn
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);
  const titleInput = ['Đơn vị xử lý', 'Người xử lý', 'Lý do'];

  let refModalChiNhanhTheoDangCay = useRef(null);
  let refModalNguoiXuLy = useRef(null);

  const getDefaultFormValues = () => {
    let donViGiamDinh = [];
    if (profileData?.ho_so) donViGiamDinh = chiNhanhBaoHiemDangCay.filter((item) => item.ma_chi_nhanh === profileData?.ho_so?.dvi_gdinh);
    return {
      lyDo: '',
      donViGiamDinh: donViGiamDinh,
      giamDinhVien: profileData?.ho_so ? profileData?.ho_so?.ma_gdv : '',
    };
  };

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: getDefaultFormValues(),
    mode: 'onChange',
  });

  const giamDinhVien = watch('giamDinhVien');

  useEffect(() => {
    setProfileData(route.params.profileData);
    initChiNhanhBaoHiemDangCay();
  }, [route.params]);

  useEffect(() => {
    if (listMaDonViXuLySeleted.length > 0) onChangeDonViXuLy('', '', listMaDonViXuLySeleted);
    else setListNguoiXuLy([]);
  }, [listMaDonViXuLySeleted]);

  const initChiNhanhBaoHiemDangCay = () => {
    let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay;
    // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
    chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
      return {
        ...item,
        listCon: [],
        isExpand: true,
        isCheck: false, //bỏ check or check
        hasChildCheck: false, //list chi nhánh cha, có child checked
        isShow: true,
      };
    });
    let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
    for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
      let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
      chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
    }
    refModalChiNhanhTheoDangCay.current.setData(chiNhanhBaoHiemCha);
  };

  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
    let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
    if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
    if (listConLai.length === 0) return [];
    else {
      for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
      return listConFilter;
    }
  };

  const onPressSave = async (data) => {
    // const lanGiamDinh = profileData?.lan_gd[profileData?.lan_gd.length - 1].lan_gd;
    // return console.log('params', params);
    try {
      let nguoiXuLy = listNguoiXuLy.find((item) => item.ma === data.giamDinhVien);
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        nsd_moi: data.giamDinhVien,
        ma_chi_nhanh_moi: nguoiXuLy ? nguoiXuLy.ma_chi_nhanh : '',
        ly_do: data.lyDo,
        hanh_dong: route.params.prevScreen === 'KHAI_BAO_TT_DIA_DIEM_GIAM_DINH_HS_TIEP_NHAN' ? 'CCCT_CHUYEN_GDVHT' : 'CHUYEN_GDVHT',
        lan_gd: profileData?.lan_gd?.lan_gd ? profileData.lan_gd.lan_gd : profileData?.lan_gd[profileData?.lan_gd.length - 1].lan_gd,
        nv: profileData.ho_so.nghiep_vu,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHUYEN_GDV_HIEN_TRUONG, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển giám định viên thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeDonViXuLy = async (title, items, itemValueSelected) => {
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        ma_chi_nhanh: itemValueSelected.join(','),
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListNguoiXuLy(response.data_info);
      if (response.data_info.length <= 0) setValue('giamDinhVien', '');
      else {
        // if (profileData?.ho_so?.dvi_gdinh === itemValueSelected) setValue('giamDinhVien', profileData?.ho_so?.ma_gdv);
        // else setValue('giamDinhVien', '');
        // setValue('giamDinhVien', '');
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const getTenHienThi = (value, data = []) => {
    let name = '';
    data.map((e) => {
      if (e.ma === value) name = e.ten;
    });
    return name;
  };

  /**RENDER  */
  const renderContent = () => {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView scrollEnabled={true}>
          <View style={styles.contentView}>
            <Controller
              control={control}
              name="donViGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {value, onChange}}) => (
                <>
                  <TextInputOutlined
                    isRequired
                    isTouchableOpacity
                    editable={false}
                    isDropdown
                    title="Đơn vị xử lý"
                    value={value.length === 0 ? 'Chọn đơn vị xử lý' : value.length === 1 ? value[0].ten_chi_nhanh : `Có ${value.length} đơn vị được chọn`}
                    error={errors.donViGiamDinh && getErrMessage('donViGiamDinh', errors.donViGiamDinh.type)}
                    placeholder="Chọn đơn vị xử lý"
                    onPress={() => refModalChiNhanhTheoDangCay.current.show()}
                    inputStyle={{color: colors.BLACK}}
                  />
                </>
              )}
            />
            <Controller
              control={control}
              name="giamDinhVien"
              rules={{
                required: true,
              }}
              render={({field: {value, onChange}}) => (
                <TextInputOutlined
                  isRequired
                  isDropdown
                  isTouchableOpacity
                  editable={false}
                  title="Giám định viên"
                  placeholder="Chọn giám định viên"
                  inputStyle={{color: colors.BLACK}}
                  cleared={value !== null && value !== ''}
                  value={getTenHienThi(value, listNguoiXuLy)}
                  onPress={() => refModalNguoiXuLy.current.show()}
                  onPressClear={() => setValue('giamDinhVien', '')}
                  error={errors.giamDinhVien && getErrMessage('giamDinhVien', errors.giamDinhVien.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="lyDo"
              rules={{
                required: true,
              }}
              render={({field: {value, onChange}}) => (
                <TextInputOutlined
                  title={titleInput[2]}
                  value={value}
                  onChangeText={onChange}
                  placeholder={titleInput[2]}
                  multiline={true}
                  isRequired={true}
                  error={errors.lyDo && getErrMessage('lyDo', errors.lyDo.type)}
                />
              )}
            />
          </View>
        </KeyboardAwareScrollView>
        <ModalChiNhanhTheoDangCay
          ref={refModalChiNhanhTheoDangCay}
          showCheckCha={true}
          multiple={false}
          setListMaDonViXuLySelected={setListMaDonViXuLySelected}
          setListItemDonViXulySelected={(value) => setValue('donViGiamDinh', value, {shouldValidate: true})}
        />
        <ModalNguoiXuLy
          value={giamDinhVien}
          data={listNguoiXuLy}
          ref={refModalNguoiXuLy}
          onBackPress={() => refModalNguoiXuLy.current.hide()}
          setValue={(val) => setValue('giamDinhVien', val.ma, {shouldValidate: true})}
        />
      </View>
    );
  };
  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle={'Chuyển giám định viên'}
      renderView={renderContent()}
      footer={<ButtonLinear title="Lưu" onPress={handleSubmit(onPressSave)} disabled={dialogLoading} />}
    />
  );
};

export const ChuyenGDVHienTruongXMScreen = memo(ChuyenGDVHienTruongScreenXMComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 10,
  },
  scrollView: {
    flex: 1,
  },
  contentView: {
    flex: 1,
    zIndex: 8000,
    marginHorizontal: 15,
  },
  dropDownView: {
    marginVertical: 10,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  inputTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },

  textInputView: {
    marginBottom: 10,
  },
  footerView: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    paddingHorizontal: spacing.small,
  },
  inputStyles: {
    height: 60,
    textAlignVertical: 'top',
  },
});
