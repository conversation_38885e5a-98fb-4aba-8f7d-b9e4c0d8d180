import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {StyleSheet, Dimensions, Platform} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },
  footerView: {
    flex: 1,
    left: 0,
    right: 0,
    bottom: 0,
    width: width,
    paddingVertical: 10,
    position: 'absolute',
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: 16,
    marginBottom: Platform.OS === 'ios' ? getStatusBarHeight() - 10 : 0,
  },
  txtButton: {
    fontWeight: '700',
    color: colors.WHITE,
  },
  radioView: {
    marginLeft: 5,
    marginTop: 10,
  },
  dropDownTitle: {
    marginTop: 5,
    marginLeft: 10,
    fontWeight: 'bold',
  },
  imageNoData: {
    width: width / 3,
    height: width / 3,
  },
  profileItemView: {
    borderRadius: 10,
    borderWidth: 0.4,
    marginVertical: 5,
    marginHorizontal: 10,
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderColor: colors.GRAY,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
  },
  profileItemRightView: {
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: colors.GRAY4,
  },
  profileItemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'space-between',
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  profileTxtHoSo: {
    fontSize: 14,
    marginBottom: 4,
    color: colors.BLACK_03,
  },
  profileImgClock: {
    width: 14,
    height: 14,
    opacity: 0.8,
    marginRight: 5,
  },
  txtLabel: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400',
    color: colors.BLACK_03,
  },
  noDataView: {
    alignItems: 'center',
  },
  btnSearch: {
    borderRadius: 5,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
  },
  txtTimKiem: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
    textTransform: 'uppercase',
  },
  txtHeaderList: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.BLACK_03,
  },
  date: {
    flex: 1,
    color: colors.PRIMARY,
  },
  txtSoHS: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  renderHeader: {
    paddingVertical: 15,
    marginHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  detail: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500',
    color: colors.BLACK_03,
  },
  component: {
    flex: 1,
    paddingTop: spacing.tiny,
  },
  txtStyles: {
    color: colors.PRIMARY,
    fontWeight: '600',
  },
  rowStyles: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  totalPrice: {
    color: colors.RED1,
    fontWeight: 'bold',
  },
  removeIc: {
    top: 0,
    right: 0,
    position: 'absolute',
  },
  txtPrice: {
    textAlign: 'right',
    color: colors.PRIMARY,
  },
});
