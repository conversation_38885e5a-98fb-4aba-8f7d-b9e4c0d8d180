import {colors} from '@app/commons/Theme';
import {FontSize, dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    flex: 1,
    // marginTop: vScale(spacing.smaller),
  },
  txtBtnRequestEnd: {
    fontWeight: 'bold',
    color: colors.BLACK_03,
  },
  footerView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    width: dimensions.width,
    paddingVertical: vScale(10),
    borderTopColor: colors.GRAY,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonIcon: {
    height: 22,
    fontSize: 20,
    color: 'white',
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderRadius: 35,
    alignSelf: 'center',
  },
  btnStyles: {
    marginHorizontal: scale(10),
  },
  btnLuuView: {
    paddingHorizontal: spacing.small,
    justifyContent: 'center',
    flex: 1,
  },
  countWarningView: {
    position: 'absolute',
    top: 0,
    right: spacing.smaller,
    backgroundColor: '#FFF',
    width: 18,
    height: 18,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtCountWarning: {
    color: colors.PRIMARY,
    fontWeight: 'bold',
  },
  doubleBtn: {
    paddingVertical: 0,
    borderTopWidth: 0,
  },
  textWarning: {
    color: colors.RED1,
    fontSize: FontSize.size14,
    marginHorizontal: scale(spacing.small),
  },
});
