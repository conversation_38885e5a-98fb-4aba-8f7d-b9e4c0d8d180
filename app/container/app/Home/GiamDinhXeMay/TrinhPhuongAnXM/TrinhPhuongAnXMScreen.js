import {MA_MAU_IN, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CustomTabBar, DropdownPicker, Icon, ScreenComponent, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Pdf from 'react-native-pdf';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import styles from './TrinhPhuongAnXmStyles';

const TrinhPhuongAnXMScreenComponent = ({route}) => {
  console.log('TrinhPhuongAnXMScreenComponent');
  const {profileInfo, maMauIn, loaiTrinh, action, screenTitle, profileData} = route?.params;

  const [isLoading, setIsLoading] = useState(false);
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);

  // const [urlFile, setUrlFile] = useState('');
  // const [maActionApi, setMaActionApi] = useState('');
  const [pdfData, setPDFData] = useState(null);
  const [btnTabActive, setBtnTabActive] = useState(0);

  const [nhomChiTiet, setNhomChiTiet] = useState([]);
  // const [lsTrinhDuyet, setLsTrinhDuyet] = useState([]);
  const [itemSelected, setItemSelected] = useState();
  const [dropdownData, setDropdownData] = useState([]);
  const [nhomFilter, setNhomFilter] = useState([]);
  const [arrNSDDuyet, setArrNSDDuyet] = useState([]);
  const [arrMaDoiTac, setArrMaDoiTac] = useState([]);
  const [arrCNDuyet, setArrCNDuyet] = useState([]);
  const [arrSTTDuyet, setArrSTTDuyet] = useState([]);
  const [arrPheDuyet, setArrPheDuyet] = useState([]);

  const [noiDungTrinh, setNoiDungTrinh] = useState('Kính trình');
  // const [email, setEmail] = useState('');
  const [bt, setBt] = useState('');
  const [sendEmailType, setSendEmailType] = useState('C');
  const [filterTrangThai, setFilterTrangThai] = useState(false);
  const [buttonTitle, setButtonTitle] = useState('Trình');
  const tabTitle = screenTitle === 'Trình báo cáo giám định' ? 'Trình BCGĐ' : 'Trình phương án';

  const tabViewRef = useRef(null);
  const [tenMauIn, setTenMauIn] = useState('Tờ trình');
  const [fileType, setFileType] = useState(1);

  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    await layThongTinToTrinh(maMauIn);
    layLichSuTrinh();
  };
  useEffect(() => {
    if (filterTrangThai) setButtonTitle('Huỷ trình');
  }, [filterTrangThai]);

  useEffect(() => {
    if (dropdownData?.length > 0) setItemSelected(dropdownData[0].so_id);
  }, [dropdownData]);

  useEffect(() => {
    if (itemSelected && nhomChiTiet?.length > 0) {
      let nhomCT = nhomChiTiet.filter((item) => item.so_id === itemSelected);
      setNhomFilter(nhomCT);
      let arrNSD = nhomCT.map((item) => {
        return item.nsd_duyet;
      });
      let arrMaDT = nhomCT.map((item) => {
        return item.ma_doi_tac_duyet;
      });
      let arrCN = nhomCT.map((item) => {
        return item.ma_chi_nhanh_duyet;
      });
      let arrStt = nhomCT.map((item) => {
        return item.stt_duyet;
      });
      let arrPD = nhomCT.map((item) => {
        return item.phe_duyet;
      });
      setArrNSDDuyet(arrNSD);
      setArrMaDoiTac(arrMaDT);
      setArrCNDuyet(arrCN);
      setArrSTTDuyet(arrStt);
      setArrPheDuyet(arrPD);
    }
  }, [itemSelected, nhomChiTiet]);

  const layLichSuTrinh = async () => {
    try {
      let params = {
        so_id: profileInfo?.so_id,
        ma_doi_tac: profileInfo?.ma_doi_tac,
        nghiep_vu: profileInfo.nghiep_vu,
        loai_trinh: loaiTrinh,
        ma_dt_trinh: '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_LICH_SU_TRINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const nhom = response.data_info.nhom;
      setDropdownData(nhom);
      if (response.data_info.nhom_ct.length > 0) setNhomChiTiet(response?.data_info?.nhom_ct);
      if (response.data_info.ls_trinh_duyet.length > 0) {
        const dataLichSu = response.data_info.ls_trinh_duyet;
        // setLsTrinhDuyet(response.data_info.ls_trinh_duyet);
        let arrBt = dataLichSu.filter((item) => item.phe_duyet == 1);
        setBt(arrBt[0].bt);
        setNoiDungTrinh(arrBt[0].nd);
        setItemSelected(arrBt[0].so_id_nhom_trinh);
        let filterTrangThai = [];
        filterTrangThai = dataLichSu.filter((item) => item.loai === loaiTrinh && item.trang_thai === 'C' && item.phe_duyet == 1);
        if (filterTrangThai.length > 0) setFilterTrangThai(true);
      }
      if (response.data_info.cau_hinh_email) {
        const cauHinhEmail = response.data_info.cau_hinh_email;
        // setEmail(cauHinhEmail.email_nhan);
        setSendEmailType(cauHinhEmail.tu_dong);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layThongTinToTrinh = async (mauIn) => {
    try {
      let params = {
        ma_mau_in: mauIn,
        ma_doi_tac_ql: profileInfo?.ma_doi_tac_ql,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_THONG_TIN_TO_TRINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let maActionApi = response.data_info.ma_action_api;
      let urlFile = response.data_info.url_file;
      if (maActionApi !== '' || maActionApi !== undefined) layMauInPDF(urlFile, maActionApi, mauIn);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layMauInPDF = async (urlFile, maActionApi, mauIn) => {
    setIsLoading(true);
    try {
      const params = {
        ma_mau_in: mauIn,
        ma_doi_tac: profileInfo.ma_doi_tac,
        so_id: profileInfo?.so_id,
        url_file: urlFile,
        loai: loaiTrinh,
      };
      let response = await ESmartClaimEndpoint.exportPdfBase64(maActionApi, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let pdfData = {
        base64: response.data_info.base64_string,
        filePath: '',
      };
      setPDFData(pdfData);
      if (mauIn === MA_MAU_IN.TRINH_PHUONG_AN_XE_MAY) {
        setFileType(1);
        setTenMauIn('Tờ trình');
      } else if (mauIn === MA_MAU_IN.ESCS_THONG_BAO_DUYET_BAO_LANH_XE_MAY) {
        setFileType(2);
        setTenMauIn('Thư bảo lãnh');
      }
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const handleTrinh = () => {
    // if (garaBaoGia?.tong_duyet < 1) return Alert.alert('Thông báo', 'Số tiền duyệt phải lớn hơn 0');
    if (buttonTitle === 'Trình') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn trình duyệt hồ sơ không?', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onSubmitTrinh()},
      ]);
    }

    if (buttonTitle === 'Huỷ trình') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn huỷ trình không?', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onSubmitHuyTrinh()},
      ]);
    }
  };

  // submit TRÌNH
  const onSubmitTrinh = async () => {
    setIsLoading(true);
    try {
      let params = {
        ma_doi_tac: profileInfo?.ma_doi_tac,
        so_id: profileInfo?.so_id,
        bt: null,
        nv: profileInfo.nghiep_vu,
        lan: null,
        hanh_dong: action,
        loai: loaiTrinh,
        nd: noiDungTrinh,
        ma_dt_trinh: '',
        gui_email: sendEmailType,
        arr_nsd_duyet: arrNSDDuyet,
        arr_phe_duyet: arrPheDuyet,
        arr_stt_duyet: arrSTTDuyet,
        so_id_trinh_mau: itemSelected,
        arr_ma_doi_tac_duyet: arrMaDoiTac,
        arr_ma_chi_nhanh_duyet: arrCNDuyet,
        create_file: maMauIn,
        remove_file: maMauIn,
        nghiep_vu_khac: '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TRINH_PHE_DUYET, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        if (response?.state_info?.message_body === 'Không xác định được đơn vị nhận hóa đơn') {
          Alert.alert('Thông báo', response.state_info.message_body, [
            {text: 'Để sau', style: 'destructive'},
            {text: 'Bổ sung', onPress: () => NavigationUtil.push(SCREEN_ROUTER_APP.DANH_GIA_DONG_CUA_BTV_XE_MAY, {profileData: profileData, loaiDanhGia: 'GD'})},
          ]);
        }
        return;
      }
      FlashMessageHelper.showFlashMessage('Thông báo', 'Hồ sơ trình duyệt thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  // submit HUỶ TRÌNH
  const onSubmitHuyTrinh = async () => {
    setIsLoading(true);
    let params = {
      ma_doi_tac: profileInfo?.ma_doi_tac,
      so_id: profileInfo?.so_id,
      bt: bt,
      ma_dt_trinh: null,
      remove_file: maMauIn,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.HUY_TRINH_BAO_LANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ trình hồ sơ thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeShowFile = (type) => {
    if (type === 1) {
      layThongTinToTrinh(MA_MAU_IN.ESCS_THONG_BAO_DUYET_BAO_LANH_XE_MAY);
    }
    if (type === 2) {
      layThongTinToTrinh(MA_MAU_IN.TRINH_PHUONG_AN_XE_MAY);
    }
  };

  // RENDER
  const renderPDFView = () => {
    if (!pdfData) return;
    return (
      <Pdf
        source={{
          uri: 'data:application/pdf;base64,' + pdfData.base64,
        }}
        onLoadComplete={(numberOfPages, filePath) => {
          let pdfDataTmp = pdfData;
          if (!pdfDataTmp) return;
          pdfDataTmp.filePath = filePath;
          setPDFData(pdfDataTmp);
        }}
        onPageChanged={(page, numberOfPages) => {}}
        onError={(error) => Alert.alert('Thông báo', error)}
        style={styles.pdf}
      />
    );
  };

  const renderForm = () => {
    return (
      <View style={styles.trinhPhuongAnView}>
        <View style={styles.chonNhomPheDuyetView}>
          <Text style={styles.title} children="Chọn nhóm phê duyệt" />
          {__DEV__ && (
            <TouchableOpacity style={styles.btnThemNhomPheDuyetView} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_NHOM_PHE_DUYET, {profileInfo})}>
              <Icon.MaterialIcons name="group-add" size={20} color="#FFF" />
              <Text children="Thêm nhóm" style={styles.txtThemNhom} />
            </TouchableOpacity>
          )}
        </View>
        <DropdownPicker
          zIndex={8000}
          searchable={false}
          isOpen={isOpenDropdown}
          setOpen={setIsOpenDropdown}
          items={dropdownData}
          itemSelected={itemSelected}
          setItemSelected={setItemSelected}
          schema={{
            label: 'ten_nhom',
            value: 'so_id',
          }}
          placeholder="Chọn nhóm phê duyệt"
          containerStyle={{marginTop: 0}}
        />
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          <>
            {nhomFilter.map((item, index) => {
              return (
                <View style={styles.item} key={index}>
                  <View style={styles.row}>
                    <Text style={styles.label}>Tên người duyệt: </Text>
                    <Text style={styles.content}>{item?.ten_nsd_duyet}</Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.label}>Chức danh: </Text>
                    <Text style={styles.content}>{item?.ten_chuc_danh}</Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.label}>Tài khoản: </Text>
                    <Text style={styles.content}>{item?.nsd_duyet}</Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.label}>Phân cấp phê duyệt: </Text>
                    <Text style={styles.content}>{item?.phan_cap}</Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.label}>Quyền: </Text>
                    <Text style={[styles.content, {color: colors.PRIMARY}]}>{item?.phe_duyet_chinh}</Text>
                  </View>
                </View>
              );
            })}
          </>
          <TextInputOutlined
            editable={true}
            multiline={true}
            value={noiDungTrinh}
            title="Nội dung trình"
            placeholder="Nhập nội dung trình"
            onChangeText={(t) => setNoiDungTrinh(t)}
            numberOfLines={4}
          />
        </KeyboardAwareScrollView>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={isLoading}
      headerTitle={screenTitle !== '' ? screenTitle : 'Trình phương án'}
      renderView={
        <View flex={1}>
          <ScrollableTabView
            showsVerticalScrollIndicator={false}
            ref={tabViewRef}
            style={styles.container}
            initialPage={0}
            onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))}
            renderTabBar={() => <CustomTabBar />}>
            <View tabLabel="Thông tin tờ trình" style={styles.container}>
              {action === 'PHUONG_AN_BAO_LANH' && (
                <View style={styles.btnDoiMauInView}>
                  <Text style={styles.txtTenMauIn}>{tenMauIn}</Text>
                  <View>
                    <TouchableOpacity onPress={() => onChangeShowFile(fileType)} style={styles.btnDoiMauIn}>
                      <Text style={styles.txtBtnDoiMauIn}>{fileType === 2 ? 'Xem tờ trình' : 'Xem Thư bảo lãnh'}</Text>
                      <Icon.Foundation name="page-export-pdf" size={20} color={colors.PRIMARY} />
                    </TouchableOpacity>
                  </View>
                </View>
              )}
              {renderPDFView()}
            </View>
            <View flex={1} tabLabel={tabTitle}>
              {renderForm()}
            </View>
            {/* <ScrollView tabLabel="Lịch sử" style={styles.centerView}>
              {renderHistory()}
            </ScrollView> */}
          </ScrollableTabView>
          {+btnTabActive === 1 && (
            <View style={styles.footerView}>
              <ButtonLinear loading={isLoading} disabled={isLoading} onPress={() => handleTrinh()} title={buttonTitle} />
            </View>
          )}
        </View>
      }
    />
  );
};

export const TrinhPhuongAnXMScreen = memo(TrinhPhuongAnXMScreenComponent, isEqual);
