import {colors} from '@app/commons/Theme';
import {scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {},
  stepIndicator: {
    marginVertical: scale(10),
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GREEN,
  },
  footerView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: vScale(10),
    justifyContent: 'space-around',
  },
  btnHint: {
    borderRadius: 40,
    flex: 0,
  },
  btnBack: {
    flex: 1,
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    marginHorizontal: scale(10),
    backgroundColor: colors.PRIMARY_08,
  },
  iconRightBtnView: {
    backgroundColor: colors.PRIMARY_DARK_08,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconRightBtn: {
    flex: 0,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(10),
  },
  iconLeftBtnView: {
    backgroundColor: colors.PRIMARY_DARK_08,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconLeftBtn: {
    flex: 0,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(10),
  },
  btnNext: {
    flex: 1,
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    marginHorizontal: scale(10),
    backgroundColor: colors.PRIMARY_08,
  },
  txtBtnBottom: {
    flex: 1,
    textAlign: 'center',
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  btnHintView: {
    position: 'absolute',
    bottom: vScale(90),
    right: scale(10),
  },
  btnImageView: {
    position: 'absolute',
    bottom: vScale(90),
    left: scale(10),
  },
  btnRowContainer: {
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
    flexDirection: 'row',
    marginTop: vScale(spacing.tiny),
    marginHorizontal: scale(spacing.small),
    paddingHorizontal: scale(spacing.smaller),
  },
  btnEdit: {
    flex: 1,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: vScale(spacing.smaller),
    paddingVertical: vScale(spacing.smaller),
    paddingHorizontal: scale(spacing.smaller),
  },
  txtTab: {
    marginLeft: spacing.tiny,
    color: colors.BLACK_03,
  },
  videoView: {
    flex: 1,
  },
  btnLuuView: {
    paddingHorizontal: spacing.small,
    justifyContent: 'center',
    flex: 1,
  },
});
