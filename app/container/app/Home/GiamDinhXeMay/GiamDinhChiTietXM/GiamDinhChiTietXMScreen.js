import R from '@R';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {default as AxiosConfig, default as axiosConfig} from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {getImageNameFromUriCamera, saveToCameraRoll} from '@app/utils/CameraProvider';
import {
  cloneObject,
  getAllHangMucTonThat,
  getCauHinhHoSoByMa,
  laySttAnhLonNhatTheoMaHangMuc,
  laySttHangMucAnhLonNhat,
  laySttHangMucAnhTheoHangMucId,
  sapXepAnhTheoSttHangMuc,
} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {requestCameraPermissions, requestLibraryPermissions, requestRecordAudioPermissions} from '@app/utils/PermisstionProvider';
import {Icon, ModalVideo, ScreenComponent, Text} from '@component';
import {ANH_HO_SO_GIAY_TO, CATEGORY_COMMON_KEY, DATA_CONSTANT, DATA_HUONG_DAN_CHUP_ANH_GIAM_DINH, FIRST_INDICATOR_STYLES_DATA, SCREEN_ROUTER_APP, isIOS} from '@constant';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, BackHandler, Image, ScrollView, TouchableOpacity, View} from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import ImageResizer from 'react-native-image-resizer';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import StepIndicator from 'react-native-step-indicator';
import {connect} from 'react-redux';
import styles from './GiamDinhChiTietXMStyles';
import {
  DanhGiaStep5,
  ModalCaiDatGiamDinhChiTietXeMay,
  ModalCameraWithVideo,
  ModalHangMuc,
  ModalHuongDanChupAnh,
  ModalKetThucChupAnh,
  ModalXemChiTietAnh,
  ModalXemLaiAnh,
  TakePhotoStep2,
  TakePhotoStep3,
  TakePhotoStep4,
  TakeVideo,
  ThongKeUpload,
} from './components';
import {GIAY_TO_VE_XE_OTO, LOAI_XE_MAY_DUOI_50_CC, button, MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC} from './Constant';
import moment from 'moment';
import {useForm} from 'react-hook-form';
import {logErrorTryCatch} from '@app/utils';

const GiamDinhChiTietXMScreenComponent = (props) => {
  console.log('GiamDinhChiTietXMScreenComponent');
  const {route, navigation, categoryCommon, userInfo, appSettings} = props;
  const {hangMucAnh, loaiAnh, profileData, doiTuongDuocChupAnh} = route.params;

  let inset = useSafeAreaInsets();
  let categoryImage = route.params.profileData.nhom_hang_muc ? route.params.profileData.nhom_hang_muc : props.categoryImageXeMay;

  let refModalHangMuc = useRef(null);

  const [isLoading, setIsLoading] = useState(false);
  const [chiTietHoSo, setChiTietHoSo] = useState(profileData);
  const [anhHoSo, setAnhHoSo] = useState(route.params.imagesData || []);
  const [currentPosition, setCurrentPosition] = useState(null);
  const [currentPage, setCurrentPage] = useState(loaiAnh === 'ANH_TON_THAT' ? 1 : loaiAnh === 'ANH_HO_SO' ? 2 : 0);

  const [toggleLoading, setToggleLoading] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ
  const [imageDataStep2, setImageDataStep2] = useState([]);
  const [imageDataStep3, setImageDataStep3] = useState([]); //dữ liệu kiểu : [{//thông tin menu,images}]
  const [imageDataStep4, setImageDataStep4] = useState([]); //dữ liệu kiểu : [{//thông tin menu,images}]
  const [menuImageStep2Selected, setMenuImageStep2Selected] = useState({}); //danh mục ảnh hiển thị step2 ở  pickermodal
  const [menuImageStep3Selected, setMenuImageStep3Selected] = useState({}); //danh mục ảnh hiển thị step3 ở  pickermodal
  const [menuImageStep3, setMenuImageStep3] = useState([]); //Danh mục Ảnh tổn thất
  const [indexView, setIndexView] = useState(0);

  //data của modal camera
  const [giayToDuocChon, setGiayToDuocChon] = useState(null);

  // modal chọn hạng mục
  const [timeoutId, setTimeoutId] = useState(null);
  const [hangMucTonThatRoot, setHangMucTonThatRoot] = useState(null);

  //data step 4
  const [listGaraRoot, setListGaraRoot] = useState([]);
  const [listGara, setListGara] = useState([]);
  const [dsNghiepVu, setDsNghiepVu] = useState([]);

  const [dialogLoading, setDialogLoading] = useState(false);

  const [totalImagelUpload, setTotalImageUpload] = useState([0, 0, 0]); //số lượng ảnh cần upload ở bước 1,2,3
  const [imageUploaded, setImageUploaded] = useState([0, 0, 0]); //số lượng ảnh đã upload ở bước 1,2,3

  const [mucDoSelected, setMucDoSelected] = useState(null);

  const [listVideo, setListVideo] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  const [soLanUploadLai, setSoLanUploadLai] = useState(0); //số lần upload lại

  let refModalCameraWithVideo = useRef(null);
  let refModalVideo = useRef(null);
  let refModalXemChiTietAnh = useRef(null);
  let refModalXemLaiAnh = useRef(null);
  let refModalHuongDanChupAnh = useRef(null);
  let refModalKetThucChupAnh = useRef(null);
  let refModalCaiDatGiamDinhChiTietOto = useRef(null);
  let toggleLoadingRef = useRef(false); //xử lý dừng upload ảnh, phải dùng useRef vì biến useState sẽ không update sau mỗi lần re-render

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      lh_nv: '', //loại hình nghiệp vụ
      danh_gia_gdv: '', //đánh giá sau khi giám định
      y_kien: '', //kiến nghị giải quyết
      gara: '', //Gara
      y_kien_kh: '', //ý kiến của khách hàng
      uoc_ton_that: 0, //ước tổn thất
    },
    mode: 'onChange',
  });

  /* FUNCTION  */
  useEffect(() => {
    try {
      if (route.params.anhDaPhanLoai) {
        let {anhDaPhanLoai} = route.params;
        let imageDataStep3Tmp = imageDataStep3;
        let viTriCu,
          viTriMoi = -1;
        for (let i = 0; i < imageDataStep3Tmp.length; i++) {
          if (imageDataStep3Tmp[i].ma === anhDaPhanLoai[0].nhom.ma) viTriCu = i; //hạng mục cũ
          if (imageDataStep3Tmp[i].ma === anhDaPhanLoai[0].nhomMoi.ma) viTriMoi = i; //hạng mục mới
        }
        //nếu hạng mục ảnh mới chưa có trong imageDataStep3 -> push mới vào
        if (viTriMoi === -1) {
          imageDataStep3Tmp[viTriCu].images = [{path: ''}]; //xoá ảnh ở vị trí cũ
          let hangMucMoi = {};
          anhDaPhanLoai.map((item) => (item.nhom = item.nhomMoi));
          Object.assign(hangMucMoi, anhDaPhanLoai[0].nhomMoi); //lưu lại nhóm đã chọn
          hangMucMoi.images = anhDaPhanLoai;
          hangMucMoi.anhDaUpload = [];
          hangMucMoi.images.push({path: ''});
          imageDataStep3Tmp.push(hangMucMoi);
        }
        //nếu không thay đổi hạng mục ảnh
        else if (viTriCu === viTriMoi) {
          imageDataStep3Tmp[viTriCu].images = anhDaPhanLoai;
          imageDataStep3Tmp[viTriCu].images.push({path: ''});
        } else if (viTriCu !== viTriMoi && viTriMoi !== -1) {
          imageDataStep3Tmp.splice(viTriCu, 1); //xoá ảnh ở vị trí cũ
          anhDaPhanLoai.map((item) => (item.nhom = item.nhomMoi)); //cập nhật lại hạng mục ảnh
          imageDataStep3Tmp[viTriMoi].images.pop(); //bỏ thằng cuối cùng
          imageDataStep3Tmp[viTriMoi].images = imageDataStep3Tmp[viTriMoi].images.concat(anhDaPhanLoai); //gộp 2 thằng vào.
          imageDataStep3Tmp[viTriMoi].images.push({path: ''}); //thêm lại thằng cuối
        }
        imageDataStep3Tmp = imageDataStep3Tmp.filter((item) => item.images.length > 1);
        setImageDataStep3([...imageDataStep3Tmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  }, [route.params]);

  useEffect(() => {
    soLanUploadLai === 1 && onPressNext();
  }, [soLanUploadLai]);

  useEffect(() => {
    if (indexView === 1) getListVideo();
  }, [indexView]);

  useEffect(() => {
    setIsLoading(true);
    requestPermistion(); //request quyền truy cập ảnh, thư viện...
    initData();
    initHangMucTonThatBuoc2();
    //request quyền truy cập vị trí hiện tại
    layViTriHienTai();

    //xử lý nút Back
    let backHandler;
    navigation.addListener('focus', () => (backHandler = BackHandler.addEventListener('hardwareBackPress', backAction)));
    navigation.addListener('blur', () => backHandler?.remove());
  }, []);

  const layViTriHienTai = () => {
    requestCurrentLocation(
      (position) => setCurrentPosition(position),
      (err) => console.log(err),
    );
  };

  const initData = async () => {
    await getThumbnailDocument(chiTietHoSo);
    await getGaraData(null); //lấy dữ liệu data ở bước ĐÁNH GIÁ
  };
  const requestPermistion = async () => {
    await requestLibraryPermissions();
    await requestCameraPermissions();
    await requestRecordAudioPermissions();
  };

  const initDataBuoc4 = async () => {
    try {
      setDialogLoading(true);
      let params = {
        so_id: profileData?.ho_so?.so_id,
        so_id_doi_tuong: doiTuongDuocChupAnh?.so_id_doi_tuong,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_NGHIEP_VU_CUA_HS_BOI_THUONG_XM, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return setDialogLoading(false); //bỏ setDialogLoading trong này do ở dưới còn gọi hàm nữa
      if (response.data_info.length === 0) return setDialogLoading(false);
      setDsNghiepVu(response.data_info);
      let filter = response.data_info.filter((e) => e.chon === 1);
      if (filter.length > 0) {
        setValue('lh_nv', filter[0].ma, {shouldValidate: true});
        getDataDanhGia(filter[0].ma); //khởi tạo dữ liệu bước ĐÁNH GIÁ
      }
      if (response.data_info.length === 1) setValue('lh_nv', response.data_info[0].ma);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDataDanhGia = async (loaiHinhDuocChon) => {
    try {
      let params = {
        so_id: profileData.ho_so.so_id,
        lh_nv: loaiHinhDuocChon,
        so_id_doi_tuong: doiTuongDuocChupAnh.so_id_doi_tuong,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_THONG_TIN_DANH_GIA_THEO_LOAI_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setValue('danh_gia_gdv', response.data_info.danh_gia_gdv, {shouldValidate: true});
      setValue('y_kien', response.data_info.kien_nghi_gq, {shouldValidate: true});
      setValue('y_kien_kh', response.data_info.y_kien_khach_hang, {shouldValidate: true});
      setValue('uoc_ton_that', response.data_info.uoc_ton_that, {shouldValidate: true});
      setValue('gara', response.data_info.ma_gara);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  // REFRESH LIST VIDEO
  const onRefreshTabView = async () => {
    setRefreshing(true);
    await getListVideo();
    setRefreshing(false);
  };

  // GET DATA LIST VIDEO
  const getListVideo = async () => {
    try {
      let params = {so_id: profileData.ho_so.so_id};
      let response = await CarClaimEndpoint.getListVideo(axiosConfig.ACTION_CODE.GET_LIST_VIDEO, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListVideo([...response.data_info]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const onChangeSearchTextHangMuc = (textSearch) => {
    try {
      if (!textSearch.trim()) {
        setMenuImageStep3([...hangMucTonThatRoot]);
        clearTimeout(timeoutId);
        return;
      }
      clearTimeout(timeoutId);
      let timeoutIdTmp = setTimeout(() => {
        let result = [];
        // let arrTextSearch = textSearch.trim().split(' ');
        // arrTextSearch = arrTextSearch.filter((item) => item !== '');
        if (categoryImage.length === 0) Alert.alert('Thông báo', 'Danh sách hạng mục rỗng');
        // let nhomHangMucFilter = categoryCommon.listHangMucXeMay;
        let nhomHangMucFilter = [];
        //VẬT CHẤT XE : ĐỐI TƯỢNG XE MÁY, TNDS : ĐỐI TƯỢNG XE MÁY BÊN THỨ 3
        if (doiTuongDuocChupAnh.kieu_dt === 'TT' && (doiTuongDuocChupAnh.nhom === 'XE' || doiTuongDuocChupAnh.loai === 'XE_MAY')) nhomHangMucFilter = categoryCommon.listHangMucXeMay;
        // TNDS : Ô TÔ BÊN THỨ 3
        else if (doiTuongDuocChupAnh.loai === 'XE') nhomHangMucFilter = categoryCommon.type1;
        const lowerCaseSearchText = textSearch?.toLowerCase();
        result = nhomHangMucFilter.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText) && (item.loai === 'CHINH' || item.loai === 'BT_TOAN_BO'));
        // for (let i = 0; i < nhomHangMucFilter.length; i++) {
        //   if (doiTuongDuocChupAnh.hang_muc === 'XE' && nhomHangMucFilter[i].loai !== 'CHINH' && nhomHangMucFilter[i].loai !== 'BT_TOAN_BO') continue;
        //   let arrTenHangMuc = nhomHangMucFilter[i].ten.split(' ');
        //   let tonTai = 0; //nếu tonTai===(arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
        //   let arrTenHangMucVietTat = nhomHangMucFilter[i].ten_alias?.split(' ') || [];
        //   let tonTaiVietTat = 0; //nếu tonTai===(arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
        //   for (let j = 0; j < arrTextSearch.length; j++) {
        //     for (let k = 0; k < arrTenHangMuc.length; k++) {
        //       /*
        //       j + 1 !== tonTai : để loại trường hợp chuỗi tên hạng mục có 2 từ giống nhau
        //       ví dụ :
        //       tên hạng mục : tôi là tôi
        //       từ cần tìm : tôi là
        //       -> khi duyệt từ 'tôi' ở từ cần tìm -> 'tôi' sẽ được tính 2 lần ->  dẫn đến sai kết quả của biến tonTai
        //       //có cách khác là remove duplicate ở cả 2 mảng arrTenHangMuc và arrTextSearch r tìm -> sẽ không bị dính trường hợp trên =))
        //       */
        //       if (arrTenHangMuc[k].toUpperCase() === arrTextSearch[j].toUpperCase() && j + 1 !== tonTai) {
        //         tonTai = tonTai + 1;
        //         break;
        //       }
        //     }
        //     for (let k = 0; k < arrTenHangMucVietTat.length; k++) {
        //       if (arrTenHangMucVietTat[k].toUpperCase() === arrTextSearch[j].toUpperCase() && j + 1 !== tonTaiVietTat) {
        //         tonTaiVietTat = tonTaiVietTat + 1;
        //         break;
        //       }
        //     }
        //   }
        //   if (tonTai === arrTextSearch.length || tonTaiVietTat === arrTextSearch.length) result.push(nhomHangMucFilter[i]);
        // }
        // // console.log('result', result);
        // let soViTriDoi = 0;
        // for (let i = 0; i < result.length; i++) {
        //   let arrResultItem = result[i].ten.trim().split(' ');
        //   let soTuGiong = 0;
        //   for (let j = 0; j < arrTextSearch.length; j++) {
        //     if (j < arrResultItem.length) if (arrTextSearch[j].toUpperCase() === arrResultItem[j].toUpperCase()) soTuGiong = soTuGiong + 1;
        //   }
        //   if (soTuGiong === arrTextSearch.length && soViTriDoi < result.length) {
        //     [result[soViTriDoi], result[i]] = [result[i], result[soViTriDoi]];
        //     soViTriDoi = soViTriDoi + 1;
        //   }
        // }
        // console.log('result', result);
        setMenuImageStep3([...result]);
      }, 500);
      setTimeoutId(timeoutIdTmp);
    } catch (error) {
      Alert.alert('Thông báo tìm kiếm hạng mục', JSON.stringify(error));
    }
  };

  //GET GARA DATA
  const getGaraData = async (searchInput) => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LIST_GARA, {ten: searchInput, nv: profileData.ho_so.nghiep_vu});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const listGaraFilter = response.data_info.filter((e) => e.nv === 'XE_MAY');
      setListGara([...listGaraFilter.slice(0, 50)]);
      if (!searchInput) setListGaraRoot([...response.data_info.filter((e) => e.nv === 'XE_MAY')]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // CHECK ẢNH CHƯA UPLOAD
  const checkAnhChuaUpload = (listAnhStep) => {
    let tonTaiAnhChuaUpload = false;
    listAnhStep.forEach((itemHangMuc) => {
      itemHangMuc.images.forEach((itemAnh) => {
        if (itemAnh.path && !itemAnh.uploadThanhCong) tonTaiAnhChuaUpload = true;
      });
    });
    return tonTaiAnhChuaUpload;
  };

  //xử lý nút quay lại ở màn Chụp ảnh
  const backAction = () => {
    if (refModalCameraWithVideo?.current?.getVisible()) return refModalCameraWithVideo.current.hide();

    toggleLoadingRef.current = false;
    let arrCheckAnhChuaUpload = [checkAnhChuaUpload(imageDataStep2), checkAnhChuaUpload(imageDataStep3), checkAnhChuaUpload(imageDataStep4)];

    if (arrCheckAnhChuaUpload.includes(true)) {
      let detail = 'Có';
      arrCheckAnhChuaUpload[0] && (detail += ' ẢNH TOÀN CẢNH');
      arrCheckAnhChuaUpload[1] && (detail += (detail.includes('ẢNH TOÀN CẢNH') ? ',' : '') + ' ẢNH TỔN THẤT');
      arrCheckAnhChuaUpload[2] && (detail += (detail.includes('ẢNH TOÀN CẢNH') || detail.includes('ẢNH TỔN THẤT') ? ',' : '') + ' ẢNH HỒ SƠ');
      detail += ' chưa tải lên hệ thống. Bạn có muốn thoát Giám định chi tiết?';
      Alert.alert('Thông báo', detail, [
        {
          text: 'Thoát',
          onPress: () => NavigationUtil.pop(),
          style: 'destructive',
        },
        {
          text: 'Ở lại',
        },
      ]);
    } else NavigationUtil.pop();
    return true;
  };

  //lấy ảnh thumbnail của hồ sơ
  const getThumbnailDocument = async (chiTietHoSo) => {
    try {
      let response = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, {so_id: chiTietHoSo.ho_so?.so_id, nv: chiTietHoSo.ho_so?.nghiep_vu});
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        initDuLieuAnhBuoc1([]);
        initDuLieuAnhBuoc2([], chiTietHoSo);
        initDuLieuAnhBuoc3([]);
        return;
      }
      let imagesTmp = response.data_info.map((item) => {
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          ma: item.ma_file,
          ten: item.nhom_anh,
        };
        item.nhom = nhom;
        return item;
      });
      initDuLieuAnhBuoc1(imagesTmp);
      initDuLieuAnhBuoc2(imagesTmp, chiTietHoSo);
      initDuLieuAnhBuoc3(imagesTmp);
      setAnhHoSo([...imagesTmp]);
    } catch (error) {
      initDuLieuAnhBuoc1([]);
      initDuLieuAnhBuoc2([], chiTietHoSo);
      initDuLieuAnhBuoc3([]);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getChiTietHoSoGiamDinhXeMay = async () => {
    //lấy chi tiết hồ sơ
    let paramsProfileDetail = {
      ma_doi_tac: chiTietHoSo.ho_so?.ma_doi_tac,
      so_id: chiTietHoSo.ho_so?.so_id,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_CHI_TIET_HS_XM, paramsProfileDetail);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setChiTietHoSo(response.data_info);
      return response.data_info;
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getImageByMaHangMuc = (listAnhDaUploadLenHeThong, maHangMuc) => {
    let result = listAnhDaUploadLenHeThong.filter((item) => item.ma_file === maHangMuc && item.so_id_doi_tuong == doiTuongDuocChupAnh.so_id_doi_tuong).sort((a, b) => b.stt - a.stt);
    return result;
  };

  //khởi tạo ảnh step 2
  const initDuLieuAnhBuoc1 = (listAnhDaUploadLenHeThong) => {
    try {
      if (categoryImage.length === 0) return;
      let imageDataStep2Tmp = [];

      //Ảnh BLX, ĐKX, ĐK, Giấy chứng nhận bảo hiểm
      let temDangKiem = null;
      let anhGDV = null;
      let gcnBH = null;
      let thongBaoTaiNanVaYeuCauBoiThuong = null;
      let listGiayToConLai = [];
      for (let i = 0; i < categoryImage.length; i++) {
        if (categoryImage[i].nhom === CATEGORY_COMMON_KEY.ANH_TAI_LIEU) {
          let tmp = {...categoryImage[i]};
          let imageData = {
            path: '',
            name: '',
            nhom: {...categoryImage[i]},
          };
          tmp.images = [{...imageData}, {...imageData}];
          let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.ma);
          tmp.anhDaUpload = anhDaUploadLenHeThong;
          tmp.isShowAnhDaUpload = false;
          if (tmp.nhom_hang_muc === 'ANH_GDV') {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            anhGDV = tmp;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_1_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else tmp.images[1].preView = R.images.img_take_photo_step4_1_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_SAU;
            imageDataStep2Tmp.push(tmp);
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_2_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else tmp.images[1].preView = R.images.img_take_photo_step4_2_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_SAU;
            imageDataStep2Tmp.push(tmp);
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_3_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else tmp.images[1].preView = R.images.img_take_photo_step4_3_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_SAU;
            imageDataStep2Tmp.push(tmp);
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            gcnBH = tmp;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_4_3;
            tmp.images.pop();
            temDangKiem = tmp;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.THONG_BAO_TAI_NAN) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            thongBaoTaiNanVaYeuCauBoiThuong = tmp;
          } else if (tmp.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && tmp.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            listGiayToConLai.push(tmp);
          }
        }
      }

      //xử lý ảnh toàn cảnh trước
      for (let i = 0; i < categoryImage.length; i++) {
        if (categoryImage[i].nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
          let tmp = {};
          tmp.images = [];
          tmp.nhom = {...categoryImage[i]};
          tmp.ten = 'Ảnh toàn cảnh';
          tmp.isShowAnhDaUpload = false;
          let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.nhom.ma);
          tmp.anhDaUpload = anhDaUploadLenHeThong;
          tmp.images.push({path: '', name: '', nhom: tmp.nhom});
          if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
          else tmp.images[0].preView = doiTuongDuocChupAnh.kieu_dt === 'TT' && doiTuongDuocChupAnh.nhom === 'TAI_SAN' ? R.icons.ic_gallery : R.images.img_anh_toan_canh_xe_may;
          imageDataStep2Tmp.push(tmp);
          break;
        }
      }

      let soKhungSoMayGrouped = {
        ten: '',
        ma: [],
        nhom: [],
        nhom_hang_muc: [],
        arrTen: [],
        images: [],
        isShowAnhDaUpload: false,
        anhDaUpload: [],
      };

      //xử lý số khung - số máy
      let khungMayTemTmp = categoryImage.filter(
        (item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG || item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY,
      );
      let anhSoKhungUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG);
      soKhungSoMayGrouped.anhDaUpload = anhSoKhungUploadLenHeThong;
      let anhSoMayUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY);
      soKhungSoMayGrouped.anhDaUpload = soKhungSoMayGrouped.anhDaUpload.concat(anhSoMayUploadLenHeThong);
      khungMayTemTmp.map((item, index) => {
        soKhungSoMayGrouped.ten = soKhungSoMayGrouped.ten + item.ten + (index === 0 ? ', ' : '');
        soKhungSoMayGrouped.ma.push(item.ma);
        soKhungSoMayGrouped.nhom.push(item.nhom);
        soKhungSoMayGrouped.nhom_hang_muc.push(item.nhom_hang_muc);
        soKhungSoMayGrouped.arrTen.push(item.ten);
        soKhungSoMayGrouped.images.push({
          name: '',
          path: '',
          nhom: {
            loai: item.loai,
            ma: item.ma,
            ma_ct: item.ma_ct,
            nhom: item.nhom,
            nhom_doi_tuong: item.nhom_doi_tuong,
            nhom_hang_muc: item.nhom_hang_muc,
            stt: item.stt,
            ten: item.ten,
          },
          preView: '',
        });
        if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG) {
          if (anhSoKhungUploadLenHeThong && anhSoKhungUploadLenHeThong[0])
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = `data:image/gif;base64,${anhSoKhungUploadLenHeThong[0].duong_dan}`;
          else soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = R.images.img_take_photo_step4_4_1;
        } else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY) {
          if (anhSoMayUploadLenHeThong && anhSoMayUploadLenHeThong[0])
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = `data:image/gif;base64,${anhSoMayUploadLenHeThong[0].duong_dan}`;
          else soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = R.images.img_take_photo_step4_4_2;
        }
      });
      imageDataStep2Tmp.push(soKhungSoMayGrouped);
      thongBaoTaiNanVaYeuCauBoiThuong && imageDataStep2Tmp.push(thongBaoTaiNanVaYeuCauBoiThuong); //THÔNG BÁO TAI NẠN VÀ YÊU CẦU BỒI THƯỜNG
      if (doiTuongDuocChupAnh.kieu_dt === 'BH') imageDataStep2Tmp = imageDataStep2Tmp.concat(listGiayToConLai);
      anhGDV && imageDataStep2Tmp.unshift(anhGDV);
      gcnBH && imageDataStep2Tmp.splice(4, 0, gcnBH);
      setImageDataStep2(imageDataStep2Tmp);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //khởi tạo categoryImage step 3
  const initHangMucTonThatBuoc2 = () => {
    try {
      if (categoryImage.length === 0) return;
      let nhomHangMucFilter = [];
      //VCX : ĐỐI TƯỢNG XE MÁY
      if (doiTuongDuocChupAnh.hang_muc === 'XE' && doiTuongDuocChupAnh.nhom === 'XE') {
        console.log('VÂT CHÁT XE MÁY TỔN THẤT');
        nhomHangMucFilter = profileData.nhom_hang_muc.filter(
          (item) =>
            (item.nhom_doi_tuong === doiTuongDuocChupAnh.nhom || (item.nhom_doi_tuong === 'XE' && doiTuongDuocChupAnh.nhom === 'TAI_SAN' && doiTuongDuocChupAnh.loai === 'XE')) &&
            item.nhom === CATEGORY_COMMON_KEY.ANH_TON_THAT,
        );
      }
      //TNDS : ĐỐI TƯỢNG XE MÁY TỔN THẤT
      else if (doiTuongDuocChupAnh.kieu_dt === 'TT' && doiTuongDuocChupAnh.loai === 'XE_MAY') {
        console.log('TNDS XE MÁY TỔN THẤT');
        nhomHangMucFilter = profileData.nhom_hang_muc.filter((item) => item.nhom === CATEGORY_COMMON_KEY.ANH_TON_THAT);
      }
      //TNDS : ĐỐI TƯỢNG Ô TÔ TỔN THẤT
      else if (doiTuongDuocChupAnh.kieu_dt === 'TT' && doiTuongDuocChupAnh.loai === 'XE') {
        console.log('TNDS Ô TÔ TỔN THẤT');
        nhomHangMucFilter = categoryCommon.type1.filter((item) => item.loai === 'CHINH' && item.nhom !== 'HSGT').slice(0, 50);
      }
      // ĐỐI TƯỢNG TNDS TÀI SẢN
      else {
        console.log('TNDS TNDS TÀI SẢN');
        if (doiTuongDuocChupAnh.hang_muc === 'TAI_SAN')
          nhomHangMucFilter = categoryCommon.listHangMucXeMay.filter((item) => item.nhom === doiTuongDuocChupAnh.hang_muc || item.ma === doiTuongDuocChupAnh.hang_muc);
        else nhomHangMucFilter = categoryCommon.listHangMucXeMay.filter((item) => item.ma === doiTuongDuocChupAnh.hang_muc);
      }
      setHangMucTonThatRoot(cloneObject(nhomHangMucFilter));
      setMenuImageStep3(cloneObject(nhomHangMucFilter));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // khởi tạo hạng mục ảnh tổn thất đã chụp
  const initDuLieuAnhBuoc2 = (listAnhDaUploadLenHeThong, chiTietHoSo) => {
    try {
      if (chiTietHoSo.hang_muc_chup.length === 0) return;
      let categoryImageStep3Tmp = [];
      let hangMucTonThat = chiTietHoSo.hang_muc_chup.filter((item) => item.loai === 'TT' && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
      //xử lý các danh mục còn lại
      for (let i = 0; i < hangMucTonThat.length; i++) {
        let tmp = {
          loai: hangMucTonThat[i].loai_hang_muc,
          ma: hangMucTonThat[i].hang_muc,
          nhom: 'ANH_TON_THAT',
          nhom_doi_tuong: hangMucTonThat[i].doi_tuong,
          ten: hangMucTonThat[i].ten_hang_muc,
          anhDaUpload: [],
        };
        let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.ma);
        let imageData = {
          path: '',
          name: '',
          nhom: {
            loai: hangMucTonThat[i].loai_hang_muc,
            ma: hangMucTonThat[i].hang_muc,
            nhom_doi_tuong: hangMucTonThat[i].doi_tuong,
            ten: hangMucTonThat[i].ten_hang_muc,
          },
          muc_do: anhDaUploadLenHeThong.length > 0 ? anhDaUploadLenHeThong[0].muc_do : null,
        };
        tmp.images = [{...imageData}];
        tmp.anhDaUpload = anhDaUploadLenHeThong;
        tmp.isShowAnhDaUpload = false;
        categoryImageStep3Tmp.push(tmp);
      }
      setImageDataStep3([...categoryImageStep3Tmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //khởi tạo categoryImage step 3
  const initDuLieuAnhBuoc3 = (listAnhDaUploadLenHeThong) => {
    try {
      if (categoryImage.length === 0) return;
      let categoryImageStep4Tmp = [];
      //xử lý các danh mục còn lại
      for (let i = 0; i < categoryImage.length; i++) {
        if (categoryImage[i].nhom === CATEGORY_COMMON_KEY.ANH_TAI_LIEU) {
          let tmp = {...categoryImage[i]};
          let imageData = {
            path: '',
            name: '',
            nhom: {...categoryImage[i]},
          };
          tmp.images = [{...imageData}, {...imageData}];
          let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.ma);
          tmp.anhDaUpload = anhDaUploadLenHeThong;
          tmp.isShowAnhDaUpload = false;
          if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_1_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else tmp.images[1].preView = R.images.img_take_photo_step4_1_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_SAU;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_2_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else tmp.images[1].preView = R.images.img_take_photo_step4_2_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_SAU;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_3_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else tmp.images[1].preView = R.images.img_take_photo_step4_3_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_SAU;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_cccd_mat_truoc;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else tmp.images[1].preView = R.images.img_cccd_mat_sau;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.CCCD_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.CCCD_MAT_SAU;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.images.img_take_photo_step4_4_3;
            tmp.images.pop();
          } else {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop(); //do bên trên để 2 thằng, nên phải pop ra để hiển thị 1 thằng thôi
          }
          categoryImageStep4Tmp.push(tmp);
        }
      }

      //lọc để bỏ đi SO_KHUNG, SO_MAY (số khung, số máy ở Ảnh toàn cảnh rồi)
      categoryImageStep4Tmp = categoryImageStep4Tmp.filter(
        (item) => item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY,
      );
      categoryImageStep4Tmp.map((item, index) => {
        if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE)
          [categoryImageStep4Tmp[0], categoryImageStep4Tmp[index]] = [categoryImageStep4Tmp[index], categoryImageStep4Tmp[0]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE)
          [categoryImageStep4Tmp[1], categoryImageStep4Tmp[index]] = [categoryImageStep4Tmp[index], categoryImageStep4Tmp[1]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM)
          [categoryImageStep4Tmp[2], categoryImageStep4Tmp[index]] = [categoryImageStep4Tmp[index], categoryImageStep4Tmp[2]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BAO_HIEM)
          [categoryImageStep4Tmp[3], categoryImageStep4Tmp[index]] = [categoryImageStep4Tmp[index], categoryImageStep4Tmp[3]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM)
          [categoryImageStep4Tmp[4], categoryImageStep4Tmp[index]] = [categoryImageStep4Tmp[index], categoryImageStep4Tmp[4]];
      });
      //sort lại theo đúng stt
      categoryImageStep4Tmp.sort(function (a, b) {
        return a.stt - b.stt;
      });
      setImageDataStep4(cloneObject(categoryImageStep4Tmp));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //xử lý ảnh khi chụp
  //type :  0 - camera ; type : 1 - library
  const handleImage = async (imageData, menuImageDataSelected, indexOpened, type) => {
    const isAnhChonTuThuVien = type === 1;
    try {
      let imageName = '';
      if (imageData.length === undefined) imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri

      if (profileData && profileData.cau_hinh && !isAnhChonTuThuVien) {
        let cauHinhLuuAnh = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.LUU_ANH_THU_VIEN, profileData.cau_hinh);
        if (appSettings.luuAnhGiamDinhKhiChup !== false && cauHinhLuuAnh?.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) saveToCameraRoll(imageData.path);
      }

      //ẢNH TOÀN CẢNH
      if (currentPage === 0) {
        let imagesTmp = imageDataStep2;
        let viTriChup = imagesTmp.findIndex((item) => item.ma === menuImageDataSelected.hangMucChup.ma);
        if (viTriChup === -1) {
          if (menuImageDataSelected.hangMucChup.nhom_hang_muc === 'TOAN_CANH') viTriChup = imagesTmp.findIndex((item) => item.nhom.nhom_hang_muc === 'TOAN_CANH');
          else if (menuImageDataSelected.hangMucChup.nhom_hang_muc === 'SO_MAY' || menuImageDataSelected.hangMucChup.nhom_hang_muc === 'SO_KHUNG')
            viTriChup = imagesTmp.findIndex((item) => item.ten === 'Số khung xe máy, Số máy' || item.ten === 'Số máy, Số khung xe máy');
        }
        //nếu là ảnh toàn cảnh
        if (menuImageDataSelected.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
          // if (menuImageDataSelected.indexOpened <= 3) {
          //   imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].path = imageData.path;
          //   imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].name = imageName;
          // } else
          imagesTmp[viTriChup].images.splice(imagesTmp[viTriChup].images.length - 1, 0, {
            path: imageData.path,
            name: imageName,
            nhom: menuImageDataSelected.hangMucChup,
          });
        }
        //nếu là ẢNH GIÁM ĐỊNH VIÊN, phải đặt ở trên điều kiện GCN BẢOH HIỂM, đặt ở dưới thì sẽ vào ĐK GDN BẢO HIỂM TRƯỚC
        else if (menuImageDataSelected.hangMucChup.nhom_hang_muc === 'ANH_GDV') {
          imagesTmp[0].images[menuImageDataSelected.indexOpened].path = imageData.path;
          imagesTmp[0].images[menuImageDataSelected.indexOpened].name = imageName;
        }
        //nếu là GIẤY CHỨNG NHẬN BẢO HIỂM
        else if (!GIAY_TO_VE_XE_OTO.includes(menuImageDataSelected.hangMucChup.nhom_hang_muc) && menuImageDataSelected.hangMucChup.ma !== 'CMND_CCCD') {
          viTriChup = imagesTmp.findIndex((item) => item.ma === menuImageDataSelected.hangMucChup.ma);
          if (!isAnhChonTuThuVien) {
            imagesTmp[viTriChup].images.splice(imagesTmp[viTriChup].images.length - 1, 0, {
              path: imageData.path,
              name: imageName,
              nhom: menuImageDataSelected.hangMucChup,
            });
          } else {
            imageData.map((image) => {
              imageName = getImageNameFromUriCamera(image.path); //lấy ra tên ảnh từ uri
              imagesTmp[viTriChup].images.splice(imagesTmp[viTriChup].images.length - 1, 0, {
                path: image.path,
                name: imageName,
                nhom: menuImageDataSelected.hangMucChup,
              });
            });
          }
        }
        //nếu là số khung - số máy - bằng lái xe, đăng ký xe, dăng kiểm, CCCD
        else {
          imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].path = imageData.path;
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].name = imageName;
        }
        setImageDataStep2([...imagesTmp]);
      }

      //ẢNH TỔN THẤT
      else if (currentPage === 1) {
        imageName = getImageNameFromUriCamera(imageData.path);
        let imageDataStep3Tmp = imageDataStep3;
        let daTonTai = false;
        imageDataStep3Tmp = imageDataStep3Tmp.map((item, index) => {
          if (item.ma === menuImageDataSelected.ma) {
            daTonTai = true;
            let anhDaUpload = getAnhDaUploadTheoMaHangMuc(item.ma);
            if (!item.images) item.images = []; //nếu chưa có mảng image thì khởi tạo
            if (item.images.length - 1 >= MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC - anhDaUpload.length) {
              Alert.alert('Thông báo', 'Hạng mục này chỉ chụp tối đa ' + MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC + ' ảnh');
              return item;
            }
            //push ảnh lên đầu
            item.images.unshift({
              path: imageData.path,
              name: imageName,
              nhom: {...menuImageDataSelected},
            });

            if (item.images.length > 0) {
              if (item.images[1].muc_do) item.images[0].muc_do = item.images[1].muc_do;
            } else if (!item.images[0].muc_do && mucDoSelected) item.images[0].muc_do = mucDoSelected?.ma || null;
          }
          return item;
        });
        if (!daTonTai) {
          let hangMucMoi = {...menuImageDataSelected};
          hangMucMoi.images = [
            {
              path: imageData.path,
              name: imageName,
              nhom: {...menuImageDataSelected},
            },
            {path: '', nhom: {...menuImageDataSelected}, name: '', muc_do: mucDoSelected ? mucDoSelected.ma : null},
          ];
          hangMucMoi.anhDaUpload = [];
          hangMucMoi.isShowAnhDaUpload = false;
          if (mucDoSelected) hangMucMoi.images[0].muc_do = mucDoSelected.ma;
          imageDataStep3Tmp.push(hangMucMoi);
        }
        setImageDataStep3(imageDataStep3Tmp);
      }
      //ẢNH HỒ SƠ, GIẤY TỜ
      else if (currentPage === 2) {
        let imagesTmp = imageDataStep4;
        imagesTmp.map((item) => {
          if (item.ma === menuImageDataSelected.ma) {
            if (
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM ||
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD
            ) {
              item.images[indexOpened].path = imageData.path;
              item.images[indexOpened].name = imageName;
              setImageDataStep4([...imagesTmp]);
            } else {
              if (!isAnhChonTuThuVien)
                item.images.unshift({
                  path: imageData.path,
                  name: imageName,
                  nhom: menuImageDataSelected,
                });
              //nếu là chọn nhiều ảnh từ thư viện
              else
                imageData.map((image) => {
                  imageName = getImageNameFromUriCamera(image.path); //lấy ra tên ảnh từ uri
                  item.images.unshift({
                    path: image.path,
                    name: imageName,
                    nhom: menuImageDataSelected,
                  });
                });
            }
          }
          return item;
        });
        setImageDataStep4([...imagesTmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //xoá ảnh
  const removeImage = (imageData) => {
    Alert.alert('Xoá ảnh', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Xoá',
        onPress: () => {
          try {
            //ẢNH TOÀN CẢNH
            if (currentPage === 0) {
              let imagesStep2Tmp = imageDataStep2;
              let viTriXoa = imagesStep2Tmp.findIndex((item) => item.ma === imageData.item.nhom.ma);
              if (viTriXoa === -1) {
                if (imageData.item.nhom.nhom_hang_muc === 'TOAN_CANH') viTriXoa = imagesStep2Tmp.findIndex((item) => item.nhom.nhom_hang_muc === 'TOAN_CANH');
                else if (imageData.item.nhom.nhom_hang_muc === 'SO_MAY' || imageData.item.nhom.nhom_hang_muc === 'SO_KHUNG')
                  viTriXoa = imagesStep2Tmp.findIndex((item) => item.ten === 'Số khung xe máy, Số máy' || item.ten === 'Số máy, Số khung xe máy');
              }
              //nếu là ảnh toàn cảnh
              if (imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
                // if (imageData.index <= 3) {
                //   imagesStep2Tmp[viTriXoa].images[imageData.index].path = '';
                //   imagesStep2Tmp[viTriXoa].images[imageData.index].uploadThanhCong = false;
                //   imagesStep2Tmp[viTriXoa].images[imageData.index].uploadThatBai = false;
                //   imagesStep2Tmp[viTriXoa].images[imageData.index].sttAnh = undefined;
                //   imagesStep2Tmp[viTriXoa].images[imageData.index].rootPath ? (imagesStep2Tmp[viTriXoa].images[imageData.index].rootPath = undefined) : '';
                // } else
                imagesStep2Tmp[viTriXoa].images.splice(imageData.index, 1);
              } else {
                let GIAY_TO_KHONG_SPICE = ['SO_MAY', 'SO_KHUNG', 'DANG_KIEM', 'DANG_KY', 'BANG_LAI', 'TEM_DANG_KIEM', 'CCCD', 'ANH_GDV'];
                if (GIAY_TO_KHONG_SPICE.includes(imageData.item.nhom.nhom_hang_muc) || imageData.item.nhom.ma === 'CMND_CCCD') {
                  imagesStep2Tmp[viTriXoa].images[imageData.index].path = ''; //nếu là số khung - số máy
                  imagesStep2Tmp[viTriXoa].images[imageData.index].uploadThanhCong = false;
                  imagesStep2Tmp[viTriXoa].images[imageData.index].uploadThatBai = false;
                  imagesStep2Tmp[viTriXoa].images[imageData.index].sttAnh = undefined;
                  imagesStep2Tmp[viTriXoa].images[imageData.index].rootPath ? (imagesStep2Tmp[viTriXoa].images[imageData.index].rootPath = undefined) : '';
                } else imagesStep2Tmp[viTriXoa].images.splice(imageData.index, 1);
              }
              setImageDataStep2([...imagesStep2Tmp]);
            }
            //ẢNH TỔN THẤT
            else if (currentPage === 1) {
              let tmpImageDataStep3 = imageDataStep3; //lấy dữ liệu tmp
              let indexHangMucXoa = tmpImageDataStep3.findIndex((item) => item.ma === imageData.item.nhom.ma);
              if (indexHangMucXoa !== -1) {
                tmpImageDataStep3[indexHangMucXoa].images.splice(imageData.index, 1); //xoá ảnh đi
                //nếu là hạng mục chưa upload lên server -> kiểm tra xem có xoá hết ảnh của hạng mục không, nếu xoá hết ảnh của hạng mục -> xoá hạng mục khỏi LIST
                if (!chiTietHoSo.hang_muc_chup.find((hangMucDaUpload) => hangMucDaUpload.hang_muc === imageData.item.nhom.ma) && tmpImageDataStep3[indexHangMucXoa].images.length === 1)
                  tmpImageDataStep3.splice(indexHangMucXoa, 1); //nếu mà đã xoá hết ảnh -> XOÁ LUÔN HẠNG MỤC
              }
              setImageDataStep3([...tmpImageDataStep3]);
            }
            //ẢNH HỒ SƠ, GIẤY TỜ
            else if (currentPage === 2) {
              let tmpImageDataStep4 = imageDataStep4; //lấy dữ liệu tmp
              //nếu là ảnh BẰNG LÁI, ĐĂNG KÝ XE, ĐĂNG KIỂM XE
              tmpImageDataStep4 = tmpImageDataStep4.map((itemImgData) => {
                if (itemImgData.ma === imageData.item.nhom.ma) {
                  if (
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM ||
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD
                  ) {
                    itemImgData.images[imageData.index].path = '';
                    itemImgData.images[imageData.index].base64 = '';
                    itemImgData.images[imageData.index].uploadThanhCong = false;
                    itemImgData.images[imageData.index].uploadThatBai = false;
                    itemImgData.images[imageData.index].sttAnh = false;
                    itemImgData.images[imageData.index].rootPath ? (itemImgData.images[imageData.index].rootPath = undefined) : '';
                  } else itemImgData.images.splice(imageData.index, 1);
                }
                return itemImgData;
              });
              setImageDataStep4([...tmpImageDataStep4]);
            }
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };
  //xoay ảnh
  const rotationImage = (imageData, rotationType) => {
    try {
      imageData.item.rootPath = !imageData.item.rootPath ? imageData.item.path : imageData.item.rootPath; //lưu lại ảnh gốc
      /**
       * direction = 1 : ảnh gốc quay 0 độ
       * direction = 2 : ảnh quay phải 90 độ
       * direction = 3 : ảnh quay phải 180 độ
       * direction = 4 : ảnh quay phải 270 độ
       */
      imageData.item.direction = imageData.item.direction ? imageData.item.direction : 1; //
      let newDirection = 0;
      if (rotationType === 0) newDirection = imageData.item.direction === 4 ? 1 : imageData.item.direction + 1;
      else if (rotationType === 1) newDirection = imageData.item.direction === 1 ? 4 : imageData.item.direction - 1;
      let gocQuay = 0;
      if (newDirection === 1) gocQuay = 0;
      else if (newDirection === 2) gocQuay = 90;
      else if (newDirection === 3) gocQuay = 180;
      else if (newDirection === 4) gocQuay = 270;

      Image.getSize(imageData.item.rootPath, async (imageWidth, imageHeight) => {
        try {
          let response = await ImageResizer.createResizedImage(imageData.item.rootPath, imageWidth, imageHeight, 'PNG', 100, gocQuay);
          imageData.item.direction = newDirection;
          if (currentPage === 0) {
            let imagesStep2Tmp = imageDataStep2;
            imagesStep2Tmp = imagesStep2Tmp.map((itemImgData) => {
              if (
                itemImgData.ma === imageData.item.nhom.ma || //giấy tờ khác
                (itemImgData.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH &&
                  imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) || //ảnh toàn cảnh
                //số khung, số máy
                (itemImgData.nhom_hang_muc &&
                  ((itemImgData.nhom_hang_muc[0] === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG &&
                    imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG) || //số khung
                    (itemImgData.nhom_hang_muc[1] === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY && imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY))) //số máy
              )
                itemImgData.images[imageData.index].path = response.uri;
              return itemImgData;
            });
            setImageDataStep2([...imagesStep2Tmp]);
          }
          //ẢNH TỔN THẤT
          else if (currentPage === 1) {
            let tmpImageDataStep3 = imageDataStep3;
            tmpImageDataStep3 = tmpImageDataStep3.map((itemImgData) => {
              if (itemImgData.ma === imageData.item.nhom.ma) itemImgData.images[imageData.index].path = response.uri;
              return itemImgData;
            });
            setImageDataStep3([...tmpImageDataStep3]);
          }
          //ẢNH HỒ SƠ, GIẤY TỜ
          else if (currentPage === 2) {
            let tmpImageDataStep4 = imageDataStep4;
            tmpImageDataStep4 = tmpImageDataStep4.map((itemImgData) => {
              if (itemImgData.ma === imageData.item.nhom.ma) itemImgData.images[imageData.index].path = response.uri;
              return itemImgData;
            });
            setImageDataStep4([...tmpImageDataStep4]);
          }
        } catch (error) {
          Alert.alert('Có lỗi khi xoay ảnh', JSON.stringify(error));
        }
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //XỬ LÝ ẢNH UPLOAD THẤT BẠI
  const xuLyAnhUploadThatBai = (anhThatBai) => {
    try {
      let imageTmp = [];
      if (currentPage === 0) imageTmp = imageDataStep2;
      else if (currentPage === 1) imageTmp = imageDataStep3;
      else if (currentPage === 2) imageTmp = imageDataStep4;
      imageTmp.map((itemHangMuc) => {
        itemHangMuc.images.map((itemAnh) => {
          if (itemAnh.path === anhThatBai.path) {
            itemAnh.uploadThatBai = true;
            itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
          }
          return itemAnh;
        });
        return itemHangMuc;
      });
      if (currentPage === 0) setImageDataStep2([...imageTmp]);
      else if (currentPage === 1) setImageDataStep3([...imageTmp]);
      else if (currentPage === 2) setImageDataStep4([...imageTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //XỬ LÝ ẢNH UPLOAD THÀNH CÔNG
  const xuLyAnhUploadThanhCong = (anhThanhCong) => {
    try {
      let imageTmp = [];
      if (currentPage === 0) imageTmp = imageDataStep2;
      else if (currentPage === 1) imageTmp = imageDataStep3;
      else if (currentPage === 2) imageTmp = imageDataStep4;
      imageTmp.map((itemHangMuc) => {
        itemHangMuc.images.map((itemAnh) => {
          if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
          return itemAnh;
        });
        return itemHangMuc;
      });
      if (currentPage === 0) setImageDataStep2([...imageTmp]);
      else if (currentPage === 1) setImageDataStep3([...imageTmp]);
      else if (currentPage === 2) setImageDataStep4([...imageTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //XOÁ HẠNG MỤC
  const onPressXoaHangMuc = (data) => {
    Alert.alert('Xoá hạng mục', 'Bạn có chắc muốn xoá hạng mục ' + data.item.ten, [
      {
        text: 'Để sau',
      },
      {
        text: 'Xoá',
        onPress: () => {
          let imageDataStep3Tmp = imageDataStep3;
          imageDataStep3Tmp.splice(data.index, 1);
          setImageDataStep3([...imageDataStep3Tmp]);
        },
      },
    ]);
  };
  //ấn nút BACK
  const onPressBack = () => {
    if (currentPage === 0) return;
    setCurrentPage(currentPage - 1);
  };
  // ấn nút NEXT
  const onPressNext = async (type) => {
    try {
      if (toggleLoading) {
        Alert.alert('Dừng tải lên', 'Ứng dụng đang tải ảnh lên hệ thống, Bạn có chắc muốn dừng tải ảnh lên', [
          {
            text: 'Dừng',
            style: 'destructive',
            onPress: () => {
              setToggleLoading(false);
              toggleLoadingRef.current = false;
            },
          },
          {
            text: 'Tiếp tục tải lên',
          },
        ]);
        return;
      }
      let imagesUploadToServer = [];
      let imageUploadedTmp = [0, 0, 0];
      soLanUploadLai === 0 && setImageUploaded([...imageUploadedTmp]);
      //nếu không phải là bước đánh giá
      if (currentPage !== 3) {
        //kiểm tra điều kiện trước khi upload ảnh
        if (currentPage === 0) {
          imageDataStep2.map((imageItem, index) => {
            if (imageItem.images) {
              /* XỬ LÝ STT ẢNH */
              let sttAnhLonNhat = -1;
              let anhHangMucDaChup = [];

              anhHangMucDaChup = anhHoSo.filter(
                (item) =>
                  (item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH && item.ma_file === imageItem.images[0].nhom.ma && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong) ||
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM,
              );
              sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[0].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
              let sttAnhTang = 1;
              for (let i = imageItem.images.length - 1; i >= 0; i--) {
                //xử lý số khung - số máy
                if (index === 5) {
                  sttAnhTang = 1;
                  anhHangMucDaChup = anhHoSo.filter(
                    (item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU && item.ma_file === imageItem.images[i].nhom.ma && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
                  );
                  sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[i].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
                }
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  imageItem.images[i].sttAnh = sttAnhLonNhat + sttAnhTang;
                  sttAnhTang += 1;
                }
              }
              /*END XỬ LÝ STT ẢNH */

              for (let i = 0; i < imageItem.images.length; i++) {
                if (imageItem.images[i].path) !imageItem.images[i].uploadThanhCong && imagesUploadToServer.push(imageItem.images[i]);
              }
            }
          });
          setImageDataStep2([...imageDataStep2]);
          soLanUploadLai === 0 && setTotalImageUpload([imagesUploadToServer.length, 0, 0]);
        } else if (currentPage === 1) {
          let anhTonThatDaChup = [];
          anhTonThatDaChup = anhHoSo.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TON_THAT);
          let sttMax = laySttHangMucAnhLonNhat(anhTonThatDaChup); //lấy stt hạng mục lớn nhất của ảnh tổn thất
          imageDataStep3.map((imageItem, index) => {
            let chuaTonTaiHangMuc = false;
            if (imageItem.images && imageItem.images.length > 1) {
              /* XỬ LÝ STT ẢNH */
              let sttAnhLonNhat = -1;
              let anhHangMucDaChup = [];
              anhHangMucDaChup = anhHoSo.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TON_THAT && item.ma_file === imageItem.images[0].nhom.ma);
              sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[0].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
              let sttAnhTang = 1;
              for (let i = imageItem.images.length - 1; i >= 0; i--) {
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  imageItem.images[i].sttAnh = sttAnhLonNhat + sttAnhTang;
                  sttAnhTang += 1;
                }
              }
              /* END XỬ LÝ STT ẢNH */

              /* XỬ LÝ STT HẠNG MỤC ẢNH */
              for (let i = 0; i < imageItem.images.length; i++)
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  let sttHangMucAnh = laySttHangMucAnhTheoHangMucId(anhTonThatDaChup, imageItem.images[i].nhom.ma); //lấy stt hạng mục hiện tại của ảnh
                  if (sttHangMucAnh !== -1) imageItem.images[i].sttHangMuc = sttHangMucAnh;
                  //nếu hạng muc đã up r -> thì lấy stt cũ
                  else {
                    imageItem.images[i].sttHangMuc = sttMax + 1;
                    chuaTonTaiHangMuc = true;
                  } //nếu không -> stt sẽ là stt tiếp theo
                  imagesUploadToServer.push(imageItem.images[i]);
                }
            }
            /* END XỬ LÝ STT HẠNG MỤC ẢNH */
            if (chuaTonTaiHangMuc) sttMax += 1;
          });
          setImageDataStep3([...imageDataStep3]);
          soLanUploadLai === 0 && setTotalImageUpload([0, imagesUploadToServer.length, 0]);
        } else if (currentPage === 2) {
          imageDataStep4.map((imageItem) => {
            if (imageItem.images) {
              let sttAnhLonNhat = -1;
              let anhHangMucDaChup = [];
              anhHangMucDaChup = anhHoSo.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU && item.ma_file === imageItem.images[0].nhom.ma);
              sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[0].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
              let sttAnhTang = 1;
              for (let i = imageItem.images.length - 1; i >= 0; i--) {
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  imageItem.images[i].sttAnh = sttAnhLonNhat + sttAnhTang;
                  sttAnhTang += 1;
                  imagesUploadToServer.push(imageItem.images[i]);
                }
              }
            }
          });
          setImageDataStep4([...imageDataStep4]);
          soLanUploadLai === 0 && setTotalImageUpload([0, 0, imagesUploadToServer.length]);
        }
        //nếu k có ảnh nào thì k cho upload ảnh
        if (imagesUploadToServer.length === 0) {
          //nếu là cập nhật hồ sơ giấy tờ -> pop ra luôn
          if ((currentPage === 1 || currentPage === 2) && (route.params.loaiAnh || hangMucAnh)) {
            NavigationUtil.pop();
            return;
          }
          if (currentPage === 0) {
            if (profileData.ho_so.nv_ma === 'TN' || (profileData.ho_so.nv_ma === 'BB' && doiTuongDuocChupAnh.kieu_dt === 'BH')) {
              //kiểm tra tiếp đến ảnh từ server
              /**
               * index : 0 -> Ảnh toàn cảnh
               * index : 1 -> ảnh số khung
               * index : 2 -> ảnh tem đăng kiểm
               * index : 3 -> ảnh số máy
               * index : 4 -> ảnh GPLX
               * index : 5 -> ảnh ĐKX
               * index : 6 -> ảnh GCN bảo hiểm
               */
              let daTonTaiAnh = [false, false, false, false]; //ảnh đã upload trên server : index : 0 - ảnh toàn cảnh; index : 1 - ảnh số khung; index : 2 - ảnh tem đăng kiểm; index : 3 - số máy
              let imgsTmp = anhHoSo.filter(
                (item) =>
                  item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH &&
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH &&
                  item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
              );
              if (imgsTmp.length > 0) daTonTaiAnh[0] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[1] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[2] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.ANH_GIAM_DINH_VIEN && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[3] = true;

              if (daTonTaiAnh.includes(false)) {
                let messageWarning = 'Vui lòng chụp';
                if (!daTonTaiAnh[0]) messageWarning += ' ít nhất 1 ảnh Toàn Cảnh';
                if (!daTonTaiAnh[1] && !daTonTaiAnh[2]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Số khung hoặc Số máy';
                // if (!daTonTaiAnh[2]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Tem đăng kiểm';
                //xe dưới 50CC thì k cần giấy phép lái xe
                // if (!daTonTaiAnh[3]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Số máy';
                // if (!LOAI_XE_MAY_DUOI_50_CC.includes(profileData.ho_so.loai_xe) && !daTonTaiAnh[4]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Giấy phép lái xe';
                // if (!daTonTaiAnh[5]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Đăng ký xe';
                // if (!daTonTaiAnh[6]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Giấy chứng nhận bảo hiểm';
                // if (!daTonTaiAnh[3]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Ảnh giám định viên';

                if (messageWarning !== 'Vui lòng chụp') return FlashMessageHelper.showFlashMessage('Thông báo', messageWarning);
              }
            }
          }
          if (currentPage === 0) initHangMucTonThatBuoc2();
          if (doiTuongDuocChupAnh.kieu_dt === 'TT') setCurrentPage(currentPage + 1);
          else if (doiTuongDuocChupAnh.kieu_dt === 'BH') NavigationUtil.pop(1);
          setCurrentPage(currentPage + 1);
          if (currentPage === 2) initDataBuoc4();
          return;
        }
        //kiểm tra hết điều kiện xong -> upload ảnh
        setToggleLoading(true);
        toggleLoadingRef.current = true;
        let timeStart = moment().format('HH:mm:ss');
        // CODE CŨ
        // let response = await Promise.all(
        //   imagesUploadToServer.map(async (item, index) => {
        //     return uploadImageToServer(
        //       [item],
        //       (anhThanhCong) => {
        //         imageUploadedTmp[currentPage] = imageUploadedTmp[currentPage] + 1;
        //         setImageUploaded([...imageUploadedTmp]);
        //         xuLyAnhUploadThanhCong(anhThanhCong);
        //       },
        //       (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
        //       index,
        //     );
        //   }),
        // );

        // CODE MỚI
        let response = [];
        try {
          for (let i = 0; i < imagesUploadToServer.length; i++) {
            if (!toggleLoadingRef.current) return;
            const item = imagesUploadToServer[i];
            response.push(
              await uploadImageToServer(
                [item],
                (anhThanhCong) => {
                  setImageUploaded((prevImageUploaded) => {
                    let prevImageUploadedTmp = cloneObject(prevImageUploaded);
                    prevImageUploadedTmp[currentPage] = prevImageUploadedTmp[currentPage] + 1;
                    return [...prevImageUploadedTmp];
                  });
                  xuLyAnhUploadThanhCong(anhThanhCong);
                },
                (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
                i,
              ),
            );
          }
        } catch (error) {
          console.log('error', error);
        }
        let timeEnd = moment().format('HH:mm:ss');
        let content = '';
        if (userInfo) content = userInfo.nguoi_dung?.nsd || '';
        content += '-' + timeStart + '-' + timeEnd + '-' + JSON.stringify(response);
        try {
          // let networkInfo = await NetInfo.fetch();
          // content += '-' + JSON.stringify(networkInfo);
        } catch (error) {}

        let paramsWriteLog = {
          content: content,
        };
        //END CODE MỚI

        // CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR
        if (soLanUploadLai === 0 && response.includes('"Network Error"')) {
          setToggleLoading(false);
          setSoLanUploadLai(1); //nếu thay đổi soLanUpload -> useEffect ở trên sẽ check -> gọi lại hàm onPressNext
          return;
        }
        setSoLanUploadLai(0); //reset lại số lần upload về 0. để xử lý cho các bước tiếp theo
        // END CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR
        if (response.includes('Không có vụ tổn thất nào thuộc phạm vi bảo hiểm')) {
          Alert.alert('Thông báo', 'Không có vụ tổn thất nào thuộc phạm vi bảo hiểm! Vui lòng chọn "Cập nhật" để chỉnh sửa thông tin vụ tổn thất.', [
            {
              text: 'OK',
            },
            {
              text: 'Cập nhật',
              onPress: () => NavigationUtil.push(SCREEN_ROUTER_APP.DS_CAC_VU_TON_THAT_XE_MAY, {profileData}),
            },
          ]);
        }

        let haveErr = '';
        response = response.filter((item) => item !== true);
        //bỏ đi các thông tin trùng
        let uniqueChars = response.filter((element, index) => {
          return response.indexOf(element) === index;
        });
        if (uniqueChars.length > 0) haveErr = uniqueChars.join(', ');

        //thực hiện xong hết thì mới reset lại data
        setToggleLoading(false);
        toggleLoadingRef.current = false;

        if (haveErr) return FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi xảy ra khi tải ảnh lên hệ thống \n' + haveErr, 'info');
        let chiTietHoSo = await getChiTietHoSoGiamDinhXeMay();
        getThumbnailDocument(chiTietHoSo);

        if (currentPage === 0) {
          // KIỂM TRA ĐIỀU KIỆN BẮT BUỘC CHỤP ẢNH TOÀN CẢNH, SỐ KHUNG, TEM ĐĂNG KIỂM
          if (profileData.ho_so.nv_ma === 'TN' || (profileData.ho_so.nv_ma === 'BB' && doiTuongDuocChupAnh.kieu_dt === 'BH')) {
            //kiểm tra ảnh vừa chụp trước
            /**
             * index : 0 -> Ảnh toàn cảnh
             * index : 1 -> ảnh số khung
             * index : 2 -> ảnh tem đăng kiểm
             * index : 3 -> ảnh số máy
             * index : 4 -> ảnh GPLX
             * index : 5 -> ảnh ĐKX
             * index : 6 -> ảnh GCN bảo hiểm
             */
            let daChupAnh = [false, false, false, false];
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH)) daChupAnh[0] = true;
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG)) daChupAnh[1] = true;
            // if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM)) daChupAnh[2] = true;
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY)) daChupAnh[2] = true;
            // if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE)) daChupAnh[4] = true;
            // if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE)) daChupAnh[5] = true;
            // if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM)) daChupAnh[6] = true;
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.ANH_GIAM_DINH_VIEN)) daChupAnh[3] = true;
            //nếu anhDaChup không có 1 trong 3 thằng kia
            if (daChupAnh.includes(false)) {
              //kiểm tra tiếp đến ảnh từ server
              /**
               * index : 0 -> Ảnh toàn cảnh
               * index : 1 -> ảnh số khung
               * index : 2 -> ảnh tem đăng kiểm
               * index : 3 -> ảnh số máy
               * index : 4 -> ảnh GPLX
               * index : 5 -> ảnh ĐKX
               * index : 6 -> ảnh GCN bảo hiểm
               */
              let daTonTaiAnh = [false, false, false, false];
              let imgsTmp = anhHoSo.filter(
                (item) =>
                  item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH &&
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH &&
                  item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
              );
              if (imgsTmp.length > 0) daTonTaiAnh[0] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[1] = true;
              // imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              // if (imgsTmp.length > 0) daTonTaiAnh[2] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[2] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.ANH_GIAM_DINH_VIEN && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[3] = true;
              // imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              // if (imgsTmp.length > 0) daTonTaiAnh[4] = true;
              // imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              // if (imgsTmp.length > 0) daTonTaiAnh[5] = true;
              // imgsTmp = anhHoSo.filter(
              //   (item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
              // );
              // if (imgsTmp.length > 0) daTonTaiAnh[6] = true;
              if (daTonTaiAnh.includes(false)) {
                let messageWarning = 'Vui lòng chụp';
                if (!daTonTaiAnh[0] && !daChupAnh[0]) messageWarning += ' ít nhất 1 ảnh Toàn Cảnh';
                if (!daTonTaiAnh[1] && !daChupAnh[1] && !daTonTaiAnh[2] && !daChupAnh[2]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Số khung hoặc Số máy';
                // if (!daTonTaiAnh[3] && !daChupAnh[3]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Ảnh giám định viên';
                // if (!daTonTaiAnh[2] && !daChupAnh[2]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Số máy';
                // if (!daTonTaiAnh[3] && !daChupAnh[3]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Số máy';
                //xe dưới 50CC thì k cần giấy phép lái xe
                // if (!LOAI_XE_MAY_DUOI_50_CC.includes(profileData.ho_so.loai_xe) && !daTonTaiAnh[4] && !daChupAnh[4])
                //   messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Giấy phép lái xe';
                // if (!daTonTaiAnh[5] && !daChupAnh[5]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Đăng ký xe';
                // if (!daTonTaiAnh[6] && !daChupAnh[6]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Giấy chứng nhận bảo hiểm';
                if (messageWarning !== 'Vui lòng chụp') return FlashMessageHelper.showFlashMessage('Thông báo', messageWarning);
              }
            }
          }
          //nếu là đối tượng TỔN THẤT
          if (doiTuongDuocChupAnh.kieu_dt === 'TT') setCurrentPage(currentPage + 1);
          else if (doiTuongDuocChupAnh.kieu_dt === 'BH') NavigationUtil.pop(1); //nếu là đối tượng BẢO HIỂM
          setCurrentPage(currentPage + 1);

          initHangMucTonThatBuoc2();
          FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
        } else if (currentPage === 1) {
          if (route.params.loaiAnh) NavigationUtil.pop();
          else setCurrentPage(currentPage + 1);
          if (route.params.anhDaPhanLoai) navigation.setParams({anhDaPhanLoai: undefined});
          FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
        } else if (currentPage === 2) {
          if (route.params.loaiAnh || hangMucAnh) {
            NavigationUtil.pop();
            return FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
          }
          initDataBuoc4();
          setCurrentPage(currentPage + 1);
          FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressHoanThanh = async (data) => {
    try {
      setDialogLoading(true);
      let params = {
        so_id: profileData.ho_so.so_id,
        ...data,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHI_DINH_GARA_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        if (response.state_info.message_body === 'Không xác định được các hạng mục/đối tượng tổn thất theo loại hình nghiệp vụ')
          return Alert.alert('Thông báo', response.state_info.message_body + '. Vui lòng quay lại "Bước 2 : Ảnh tổn thất" để chụp ảnh hạng mục', [
            {
              text: 'Để sau',
            },
            {
              text: 'Đồng ý',
              onPress: () => setCurrentPage(1),
            },
          ]);
        return;
      }
      FlashMessageHelper.showFlashMessage('Thông báo', 'Đánh giá thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  //mở ảnh đã chụp
  // const onPressAnhDaChup = () => {
  //   let imgsTmp = [];
  //   imgsTmp = anhHoSo.filter((item) => {
  //     if (currentPage === 0)
  //       return (
  //         (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH && item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH) ||
  //         item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG ||
  //         item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY ||
  //         item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
  //         item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
  //         item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM
  //       );
  //     else if (currentPage === 1) return item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TON_THAT;
  //     else if (currentPage === 2)
  //       return (
  //         item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU &&
  //         item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG &&
  //         item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY &&
  //         item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE &&
  //         item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE &&
  //         item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM
  //       );
  //   });
  //   //đoạn dưới để group theo mã file
  //   let imgsGroup = groupBy(imgsTmp, 'ma_file'); //return object
  //   imgsTmp = [];
  //   for (const property in imgsGroup) {
  //     let newItem = {
  //       images: imgsGroup[property],
  //       ma: imgsGroup[property][0].ma_file,
  //       ten: imgsGroup[property][0].nhom_anh,
  //       sttHangMuc: imgsGroup[property][0].stt_hang_muc,
  //     };
  //     if (newItem.images[0].extension === '.jpg') imgsTmp.push(newItem);
  //   }
  //   if (currentPage === 1) imgsTmp = sapXepAnhTheoSttHangMuc(imgsTmp);
  //   refModalXemLaiAnh.current.show(imgsTmp);
  // };

  //nhóm ảnh
  const groupBy = (xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  };

  //ấn nút mở CAMERA (step 1 - step 2), mở modal (step 3 - step 4), mở modalCAMERA (giấy tờ bước 4)
  //type : 0 - camera ; type : 1 - lib
  const onPressOpenCamera = (indexOpened, menuImageData, type) => {
    try {
      // console.log('onPressOpenCamera', indexOpened, menuImageData, type);
      //mở camera
      if (currentPage === 0) {
        let menuImageDataSelected = {
          hangMucChup: menuImageData,
          indexOpened,
        };

        if (type === 1) {
          openCamera(indexOpened, menuImageDataSelected, type);
          return setMenuImageStep2Selected(menuImageDataSelected);
        }
        if (menuImageData) {
          setGiayToDuocChon({
            menuImageData,
            indexOpened,
          });
          refModalCameraWithVideo.current.show();
        }
        setMenuImageStep2Selected(menuImageDataSelected);
        refModalCameraWithVideo.current.show();
      }
      //mở camera
      else if (currentPage === 1) {
        if (menuImageStep3.length === 0) {
          FlashMessageHelper.showFlashMessage('Thông báo', 'Không có hạng mục cho Ảnh tổn thất.');
          return initHangMucTonThatBuoc2();
        }
        setMenuImageStep3([...hangMucTonThatRoot]);
        refModalHangMuc.current.show();
      }
      //mở modal chọn danh mục
      else {
        if (type === 1) return openCamera(indexOpened, menuImageData, type);
        if (menuImageData) {
          setGiayToDuocChon({
            indexOpened,
            menuImageData,
          });
          refModalCameraWithVideo.current.show();
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //mở IMAGE CROP CAMERA
  //type : 0 - camera ; type : 1 - library
  const openCamera = async (indexOpened, menuImageData, type) => {
    try {
      let imgCropOpts = {
        mediaType: 'photo', //mặc định là chụp ảnh từ camera
        cropping: false,
        enableRotationGesture: true,
        compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
        showCropGuidelines: true, //tắt khung 3x3 đi
        compressImageQuality: 0.5,
        useFrontCamera: false,
        includeExif: true,
      };
      if (type === 1) {
        let nhomHangMuc = '';
        let maHangMuc = '';
        nhomHangMuc = menuImageData.hangMucChup ? menuImageData.hangMucChup.nhom_hang_muc : menuImageData.nhom_hang_muc;
        maHangMuc = menuImageData.hangMucChup ? menuImageData.hangMucChup.ma : menuImageData.ma;
        imgCropOpts.multiple = GIAY_TO_VE_XE_OTO.includes(nhomHangMuc) || maHangMuc === 'ANH_GDV' || nhomHangMuc === 'TOAN_CANH' ? false : true;
        imgCropOpts.maxFiles = GIAY_TO_VE_XE_OTO.includes(nhomHangMuc) || maHangMuc === 'ANH_GDV' || nhomHangMuc === 'TOAN_CANH' ? 1 : 10;
        let data = await ImageCropPicker.openPicker(imgCropOpts);
        return handleImage(data, menuImageData, indexOpened, type);
      }
      //Open Camera
      let data = await ImageCropPicker.openCamera(imgCropOpts);
      handleImage(data, menuImageData, indexOpened, type);
    } catch (error) {
      if (error.code !== 'E_PICKER_CANCELLED') Alert.alert('Thông báo', error.message);
    }
  };
  const onPressStep = (step) => {
    // console.log('onPressStep', step);
    // setCurrentPage(step);
  };
  //mở modal camera
  const openCameraModal = (hangMuc) => {
    if (currentPage === 0) {
      setMenuImageStep2Selected(hangMuc);
      refModalCameraWithVideo.current.show();
    } else if (currentPage === 1) {
      setMenuImageStep3Selected(hangMuc);
      refModalCameraWithVideo.current.show();
    }
  };
  //mở hướng dẫn
  const onPressTutorial = () => {
    if (currentPage === 0) refModalHuongDanChupAnh.current.show(DATA_HUONG_DAN_CHUP_ANH_GIAM_DINH[1]);
    else if (currentPage === 1) refModalHuongDanChupAnh.current.show(DATA_HUONG_DAN_CHUP_ANH_GIAM_DINH[2]);
    else if (currentPage === 2) refModalHuongDanChupAnh.current.show(DATA_HUONG_DAN_CHUP_ANH_GIAM_DINH[3]);
  };
  //upload ảnh lên server
  const uploadImageToServer = (imagesData, cbSuccess, cbErr, indexImage) => {
    // console.log('uploadImageToServer', imagesData);
    return new Promise(
      async (resolve) => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            nhom: e.nhom.ma,
            x: currentPosition?.coords?.latitude,
            y: currentPosition?.coords?.longitude,
            stt_hang_muc: e.sttHangMuc,
          };
          file.stt = e.sttAnh !== undefined ? e.sttAnh : 0;
          if (e.nhomMoi) {
            file.loai = e.loai;
            file.vu_tt = e.vu_tt;
            file.lh_nv = e.lh_nv;
            file.hang_muc = e.hang_muc;
            file.muc_do = e.muc_do;
            file.thay_the_sc = e.thay_the_sc;
            file.chinh_hang = e.chinh_hang;
            file.thu_hoi = e.thu_hoi;
            file.tien_tu_dong = e.tien_tu_dong;
            file.tien_gd = e.tien_gd;
            file.ghi_chu = e.ghi_chu;
          } else {
            if (e.muc_do) file.muc_do = e.muc_do;
            if (e.chinh_hang) file.chinh_hang = e.chinh_hang;
            if (e.thay_the_sc) file.thay_the_sc = e.thay_the_sc;
            if (e.thu_hoi) file.thu_hoi = e.thu_hoi;
            if (e.tien_gd !== null) file.tien_gd = e.tien_gd;
            if (e.tien_tu_dong !== null) file.tien_tu_dong = e.tien_tu_dong;
          }
          files.push(file);
        });
        imagesData = imagesData.map((item) => {
          item = cloneObject(item);
          delete item.preView;
          delete item.uploadThanhCong;
          return item;
        });
        let params = {
          images: imagesData,
          so_id: profileData.ho_so.so_id,
          pm: 'GD',
          ma_doi_tac: profileData.ho_so.ma_doi_tac,
          files: files,
          ung_dung: 'MOBILE_BT',
          so_id_doi_tuong: doiTuongDuocChupAnh.so_id_doi_tuong,
          nv: profileData.ho_so.nghiep_vu,
        };
        setToggleLoading(true);
        //CODE GIÁ LẬP LỖI NETWORK ERROR, UPLOAD > 4 cái ảnh
        // if ((indexImage === 3 || indexImage === 2) && soLanUploadLai === 0) {
        //   imagesData[0].lyDoLoi = JSON.stringify('Network Error');
        //   cbErr(imagesData[0]);
        //   resolve(JSON.stringify('Network Error'));
        //   return;
        // }
        try {
          let response = await ESmartClaimEndpoint.uploadFile(AxiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          if (response && response.state_info && response.state_info.status === AxiosConfig.SERVER_RESPONSE_STATUS.NOT_OK) {
            resolve(response.state_info.message_body);
            imagesData[0].lyDoLoi = response.state_info.message_body;
            cbErr(imagesData[0]);
            return;
          } else if (response && response.state_info && response.state_info.status === AxiosConfig.SERVER_RESPONSE_STATUS.OK) {
            cbSuccess(imagesData[0]);
            resolve(true);
          } else {
            imagesData[0].lyDoLoi = JSON.stringify(response?.message || response || '');
            cbErr(imagesData[0]);
            resolve(JSON.stringify(response?.message || response || ''));
          }
        } catch (error) {
          Alert.alert('Thông báo tải ảnh lên hệ thống', JSON.stringify(error?.message || error || ''));
          resolve(false);
        }
      },
      (reject) => reject(),
    );
  };

  //xem chi tiết ảnh đã chụp
  const onPressXemLaiAnh = (imageData) => {
    refModalXemChiTietAnh.current.show({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
  };

  //nút phân loại ảnh
  const onPressPhanLoaiAnh = (danhMucAnh) => {
    let danhMucAnhDuocPhanLoai = imageDataStep3.find((item) => item.ma === danhMucAnh.ma);
    let anhCanPhanLoai = danhMucAnhDuocPhanLoai.images.slice(0, danhMucAnhDuocPhanLoai.images.length - 1);
    if (danhMucAnhDuocPhanLoai.images.length === 1) return FlashMessageHelper.showFlashMessage('Thông báo', `Vui lòng chụp ảnh ${danhMucAnhDuocPhanLoai.ten} và đánh giá`);
    NavigationUtil.push(SCREEN_ROUTER_APP.PHAN_LOAI_HANG_MUC_XE_MAY, {
      imagesClassify: anhCanPhanLoai,
      profileData,
      prevScreen: SCREEN_ROUTER_APP.GIAM_DINH_CHI_TIET_XM,
      doiTuongDuocPhanLoai: doiTuongDuocChupAnh,
    });
  };

  const onPressXemChiTietAnhDaUpload = (index) => {
    if (currentPage === 0) {
      let imageDataStep2Tmp = [...imageDataStep2];
      imageDataStep2Tmp[index].isShowAnhDaUpload = !imageDataStep2Tmp[index].isShowAnhDaUpload;
      setImageDataStep2([...imageDataStep2Tmp]);
    } else if (currentPage === 1) {
      let imageDataStep3Tmp = [...imageDataStep3];
      imageDataStep3Tmp[index].isShowAnhDaUpload = !imageDataStep3Tmp[index].isShowAnhDaUpload;
      setImageDataStep3([...imageDataStep3Tmp]);
    } else if (currentPage === 2) {
      let imageDataStep4Tmp = [...imageDataStep4];
      imageDataStep4Tmp[index].isShowAnhDaUpload = !imageDataStep4Tmp[index].isShowAnhDaUpload;
      setImageDataStep4([...imageDataStep4Tmp]);
    }
  };

  const onPressLayDataHangMuc = async () => {
    await getAllHangMucTonThat();
  };

  const onPressXoaAnhDaUpload = async ({item, index}) => {
    try {
      Alert.alert('Xoá ảnh', 'Bạn có chắc muốn xoá ảnh đã tải lên hệ thống này', [
        {
          text: 'Để sau',
        },
        {
          text: 'Xoá',
          style: 'destructive',
          onPress: async () => {
            let bt = [];
            bt.push(item.bt);
            let params = {
              so_id: item?.so_id,
              bt: bt,
              nv: 'XE_MAY',
              pm: 'GD',
              so_id_dt: 0,
            };
            setDialogLoading(true);
            let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_ANH, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            if (currentPage === 1) {
              let imageDataStep3Tmp = [...imageDataStep3];
              imageDataStep3Tmp.map((itemHangMuc) => {
                if (itemHangMuc.ma === item.ma_file) itemHangMuc.anhDaUpload.splice(index, 1);
                return itemHangMuc;
              });
              setImageDataStep3([...imageDataStep3Tmp]);
            }
          },
        },
      ]);
    } catch (error) {
      setDialogLoading(false);
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const getAnhDaUploadTheoMaHangMuc = (maHangMuc) => {
    try {
      let hangMuc = imageDataStep3.find((itemHangMuc) => itemHangMuc.ma === maHangMuc);
      if (hangMuc && hangMuc.anhDaUpload) return hangMuc.anhDaUpload;
      return [];
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
      return [];
    }
  };

  /* RENDER  */
  // các nút ở footer
  const renderFooter = () => {
    return (
      <View style={[styles.footerView, {marginBottom: inset.bottom === 0 ? spacing.smaller : 0}]}>
        <TouchableOpacity activeOpacity={0.5} onPress={onPressBack} style={styles.btnBack}>
          {currentPage !== 0 && !route.params.loaiAnh && !hangMucAnh && (
            <>
              <View style={styles.iconLeftBtnView}>
                <Icon.Ionicons name="arrow-back" size={25} color={colors.WHITE} style={styles.iconLeftBtn} />
              </View>
              <Text style={styles.txtBtnBottom} font="bold14" children="Trước" />
            </>
          )}
        </TouchableOpacity>
        {/* NÚT TIẾP TỤC - HOÀN THÀNH   */}
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={handleSubmit(currentPage === 3 ? onPressHoanThanh : onPressNext)}
          style={styles.btnNext}
          // disabled={toggleLoading}
        >
          {!toggleLoading ? (
            <Text style={styles.txtBtnBottom} font="bold14" children={currentPage === 3 || doiTuongDuocChupAnh.kieu_dt === 'BH' ? 'Hoàn thành' : 'Tiếp'} />
          ) : (
            <ActivityIndicator size="large" color={colors.GRAY} style={{flex: 1}} />
          )}
          <View style={styles.iconRightBtnView}>
            <Icon.Ionicons
              name={currentPage === 3 || doiTuongDuocChupAnh.kieu_dt === 'BH' ? 'checkmark-sharp' : 'arrow-forward'}
              // name={currentPage === 3 ? 'checkmark-sharp' : 'arrow-forward'}
              size={25}
              style={styles.iconRightBtn}
              color={colors.WHITE}
            />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // VIEW NÚT ẢNH / VIDEO
  const renderBtnSwitchView = () => (
    <View style={styles.btnRowContainer}>
      {button.map((item, index) => {
        let selected = indexView === index;
        if (index === 1 && (currentPage === 2 || currentPage === 3)) return;
        return (
          <TouchableOpacity
            key={index}
            style={[styles.btnEdit, {backgroundColor: selected ? colors.PRIMARY : '#FFF'}, index === 1 && {marginLeft: spacing.smaller}]}
            flex={1}
            onPress={() => setIndexView(index)}>
            <Icon.FontAwesome name={item.iconName} size={16} color={selected ? colors.WHITE : colors.PRIMARY} />
            <Text style={[styles.txtTab, {color: selected ? colors.WHITE : colors.PRIMARY}]}>{item.title}</Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  const renderTakeImageView = () => (
    <>
      <View style={styles.stepIndicator}>
        <StepIndicator
          customStyles={FIRST_INDICATOR_STYLES_DATA}
          currentPosition={currentPage}
          labels={doiTuongDuocChupAnh.kieu_dt === 'TT' ? ['Ảnh toàn cảnh', 'Ảnh tổn thất', 'Ảnh giấy tờ', 'Đánh giá'] : ['Ảnh toàn cảnh, giấy tờ']}
          // labels={['Ảnh toàn cảnh', 'Ảnh tổn thất', 'Ảnh giấy tờ', 'Đánh giá']}
          renderLabel={({position, label, currentPosition}) => <Text style={position === currentPosition ? styles.stepLabelSelected : styles.stepLabel} children={label} font="regular12" />}
          onPress={(position) => onPressStep(position)}
          stepCount={doiTuongDuocChupAnh.kieu_dt === 'TT' ? 4 : 1}
          // stepCount={4}
        />
        <ThongKeUpload totalImagelUpload={totalImagelUpload} imageUploaded={imageUploaded} doiTuongDuocChupAnh={doiTuongDuocChupAnh} />
      </View>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {currentPage === 0 && (
          <TakePhotoStep2
            key={1}
            imagesData={imageDataStep2}
            removeImage={removeImage}
            onPressOpenCamera={onPressOpenCamera}
            onPressXemLaiAnh={onPressXemLaiAnh}
            openCameraModal={openCameraModal}
            profileData={profileData}
            rotationImage={rotationImage}
            // anhHoSo={anhHoSo}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
            onPressXemChiTietAnhDaUpload={onPressXemChiTietAnhDaUpload}
          />
        )}

        {currentPage === 1 && (
          <TakePhotoStep3
            key={2}
            imagesData={imageDataStep3}
            removeImage={removeImage}
            onPressOpenCamera={onPressOpenCamera}
            openCamera={openCamera}
            onPressXemLaiAnh={onPressXemLaiAnh}
            onPressPhanLoaiAnh={onPressPhanLoaiAnh}
            profileData={profileData}
            openCameraModal={openCameraModal}
            rotationImage={rotationImage}
            onPressXoaHangMuc={onPressXoaHangMuc}
            onPressXemChiTietAnhDaUpload={onPressXemChiTietAnhDaUpload}
            onPressXoaAnhDaUpload={onPressXoaAnhDaUpload}
          />
        )}
        {currentPage === 2 && (
          <TakePhotoStep4
            key={3}
            hangMucAnh={hangMucAnh}
            profileData={profileData}
            imagesData={imageDataStep4}
            removeImage={removeImage}
            onPressOpenCamera={onPressOpenCamera}
            openCamera={openCamera}
            onPressXemLaiAnh={onPressXemLaiAnh}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
            rotationImage={rotationImage}
            // anhHoSo={anhHoSo}
            onPressXemChiTietAnhDaUpload={onPressXemChiTietAnhDaUpload}
          />
        )}
        {currentPage === 3 && (
          <DanhGiaStep5
            control={control}
            errors={errors}
            setValue={setValue}
            watch={watch}
            listGaraRoot={listGaraRoot}
            listGara={listGara}
            getGaraData={getGaraData}
            dsNghiepVu={dsNghiepVu}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
          />
        )}
      </ScrollView>
    </>
  );
  // RENDER ICON 3 CHẤM ...
  const renderRightHeader = () => {
    if (currentPage === 3)
      return (
        <TouchableOpacity style={styles.btnLuuView}>
          <Icon.Entypo name="dots-three-vertical" color="transparent" size={25} />
        </TouchableOpacity>
      );
    return (
      <TouchableOpacity style={styles.btnLuuView} onPress={() => refModalCaiDatGiamDinhChiTietOto.current.show()}>
        <Icon.Entypo name="dots-three-vertical" color="#FFF" size={25} />
      </TouchableOpacity>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      isLoading={isLoading}
      headerBack
      headerTitle="Giám định chi tiết XM"
      headerSubTitle={doiTuongDuocChupAnh.ten_doi_tuong}
      onPressBack={backAction}
      renderRightHeader={renderRightHeader}
      renderView={
        <>
          {renderBtnSwitchView()}
          {indexView === 0 && renderTakeImageView()}
          {indexView === 1 && <TakeVideo profileData={profileData} listVideo={listVideo} getListVideo={getListVideo} onRefresh={onRefreshTabView} refreshing={refreshing} />}
          {/* render phần nút dưới cùng */}
          {indexView === 0 && renderFooter()}

          <ModalHuongDanChupAnh
            key="ModalHuongDanChupAnh"
            ref={refModalHuongDanChupAnh}
            showModalXemChiTietAnh={(imageData) => {
              isIOS && refModalHuongDanChupAnh.current.hide();
              setTimeout(() => refModalXemChiTietAnh.current.show(imageData), isIOS ? 400 : 0);
            }}
          />
          {/* modal hiển thị ảnh trong phần hướng dẫn */}
          <ModalXemChiTietAnh key="ModalXemChiTietAnh" ref={refModalXemChiTietAnh} />
          {/*modal hiển thị khi thực hiện xong 4 bước và có upload ảnh */}
          <ModalKetThucChupAnh key="ModalKetThucChupAnh" ref={refModalKetThucChupAnh} />
          <ModalHangMuc
            ref={refModalHangMuc}
            profileData={profileData}
            data={menuImageStep3}
            clearSearch={() => setMenuImageStep3([...hangMucTonThatRoot])}
            onChangeSearchTextHangMuc={onChangeSearchTextHangMuc}
            onHangMucSelected={(data) => {
              setMenuImageStep3Selected(data);
              setDialogLoading(true);
              setTimeout(
                () => {
                  setDialogLoading(false);
                  refModalCameraWithVideo.current.show();
                },
                !isIOS ? 100 : 1000,
              );
            }}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
            categoryCommon={categoryCommon}
            setMucDoSelected={setMucDoSelected}
            mucDoSelected={mucDoSelected}
            onPressLayDataHangMuc={onPressLayDataHangMuc}
            getAnhDaUploadTheoMaHangMuc={getAnhDaUploadTheoMaHangMuc}
          />

          <ModalXemLaiAnh
            key="ModalXemLaiAnh"
            ref={refModalXemLaiAnh}
            showModalXemChiTietAnh={(imageData) => {
              isIOS && refModalXemLaiAnh.current.hide();
              setTimeout(() => refModalXemChiTietAnh.current.show(imageData), isIOS ? 400 : 0);
            }}
            currentPage={currentPage}
          />

          <ModalCameraWithVideo
            ref={refModalCameraWithVideo}
            key="ModalCameraWithVideo"
            giayToDuocChon={giayToDuocChon}
            handleImage={handleImage}
            setDialogLoading={setDialogLoading}
            currentPage={currentPage}
            imageDataStep3={imageDataStep3}
            menuImageStep3Selected={menuImageStep3Selected}
            menuImageStep2Selected={menuImageStep2Selected}
            onPressPhanLoaiAnh={onPressPhanLoaiAnh}
            mucDoSelected={mucDoSelected}
            categoryCommon={categoryCommon}
            cauHinh={profileData.cau_hinh}
            currentPosition={currentPosition}
            profileData={profileData}
            layViTriHienTai={layViTriHienTai}
            getAnhDaUploadTheoMaHangMuc={getAnhDaUploadTheoMaHangMuc}
          />
          <ModalVideo ref={refModalVideo} key="ModalVideo" />
          <ModalCaiDatGiamDinhChiTietXeMay
            ref={refModalCaiDatGiamDinhChiTietOto}
            appSettings={appSettings}
            onPressTutorial={onPressTutorial}
            // onPressAnhDaChup={onPressAnhDaChup}
            currentPage={currentPage}
          />
        </>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  categoryImageXeMay: state.categoryImageXeMay.data,
  categoryCommon: state.categoryCommon.data,
  userInfo: state.user.data,
  appSettings: state.appSetting,
});
const GiamDinhChiTietXMScreenConnect = connect(mapStateToProps, {})(GiamDinhChiTietXMScreenComponent);
export const GiamDinhChiTietXMScreen = memo(GiamDinhChiTietXMScreenConnect, isEqual);
