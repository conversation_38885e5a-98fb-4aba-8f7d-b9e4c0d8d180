import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {getCauHinhHoSoByMa} from '@app/utils/DataProvider';
import {Icon, Text} from '@component';
import {APP_NAME, DATA_CONSTANT, isIOS} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Image, Platform, StyleSheet, TouchableOpacity, Vibration, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import ImageCropPicker from 'react-native-image-crop-picker';
import ImageResizer from 'react-native-image-resizer';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

const anhToanCanhTitle = ['Ảnh hiện trường', 'Ảnh toàn cảnh', 'Số khung', 'Số máy'];
const BLXTitle = ['Bằng lái xe mặt trước', 'Bằng lái xe mặt sau'];
const DKXTitle = ['Đăng ký xe mặt trước', 'Đăng ký xe mặt sau'];
const DKTitle = ['Đăng kiểm xe mặt trước', 'Đăng kiểm xe mặt sau'];

function ModalCamera(props) {
  let cameraRef;
  const [flashData, setFlashType] = useState({
    flashIcon: 'flash-off',
    flashMode: RNCamera.Constants.FlashMode.off,
  });
  const [cameraType, setCameraType] = useState(RNCamera.Constants.Type.back);
  const {toggleModalCamera, tatCameraModal, giayToDuocChon, handleImage, setDialogLoading, currentPage, menuImageStep3Selected, menuImageStep2Selected, onPressPhanLoaiAnh, appSetting} = props;
  const [countPicture, setCountPicture] = useState(0);
  const [enableCameraBtn, setEnableCameraBtn] = useState(false);
  const [viTriChupAnhToanCanh, setViTriChupAnhToanCanh] = useState(0);
  const [loaiCat, setLoaiCat] = useState(0); //0 là điện thoại chụp dọc - 1 điện thoại chụp ngang

  useEffect(() => {
    if (currentPage == 0) {
      if (!menuImageStep2Selected.hangMucChup) return;
      if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'ANH_TOAN_CANH') setViTriChupAnhToanCanh(menuImageStep2Selected.indexOpened);
      // else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'SO_KHUNG') setViTriChupAnhToanCanh(4);
      // else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'SO_MAY') setViTriChupAnhToanCanh(5);
    }
  }, [menuImageStep2Selected]);

  // const prepareRatio = async () => {
  //   if (Platform.OS === 'android' && cameraRef) {
  //     const ratios = await cameraRef.getSupportedRatiosAsync();
  //     // See if the current device has your desired ratio, otherwise get the maximum supported one
  //     // Usually the last element of "ratios" is the maximum supported ratio
  //     // const ratio = ratios.find((ratio) => ratio === DESIRED_RATIO) || ratios[ratios.length - 1];
  //     // console.log(ratios);
  //     // setRatio(ratio);
  //   }
  // };

  //xử lý khi icon FLash được click
  const onPressChangeFlash = () => {
    //Nếu đang auto -> flash
    if (flashData.flashIcon == 'flash-auto') {
      setFlashType({
        flashIcon: 'flash',
        flashMode: RNCamera.Constants.FlashMode.on,
      });
    }
    //nếu đang flash -> tắt
    else if (flashData.flashIcon == 'flash') {
      setFlashType({
        flashIcon: 'flash-off',
        flashMode: RNCamera.Constants.FlashMode.off,
      });
    }
    //nếu đang tắt flash -> flash-auto
    else if (flashData.flashIcon == 'flash-off') {
      setFlashType({
        flashIcon: 'flash-auto',
        flashMode: RNCamera.Constants.FlashMode.auto,
      });
    }
  };
  //chuyển camera trước - sau
  const onPressSwapCamera = () => {
    if (cameraType == RNCamera.Constants.Type.back) {
      setCameraType(RNCamera.Constants.Type.front);
    } else if (cameraType == RNCamera.Constants.Type.front) {
      setCameraType(RNCamera.Constants.Type.back);
    }
  };
  //ẩn modal camera
  const onPressTatCameraModal = () => {
    tatCameraModal();
    setFlashType({
      flashIcon: 'flash-off',
      flashMode: RNCamera.Constants.FlashMode.off,
    });
    setCameraType(RNCamera.Constants.Type.back);
    setCountPicture(0);
    setLoaiCat(0);
  };

  const getAnhTitle = () => {
    if (currentPage == 0) {
      if (!menuImageStep2Selected.hangMucChup) return;
      if (giayToDuocChon && giayToDuocChon.menuImageData) {
        if (giayToDuocChon.menuImageData.nhom_hang_muc == 'BANG_LAI') return BLXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc == 'DANG_KY') return DKXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc == 'DANG_KIEM') return DKTitle[giayToDuocChon.indexOpened];
      }
      if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'TOAN_CANH') return anhToanCanhTitle[viTriChupAnhToanCanh < 4 ? viTriChupAnhToanCanh : 4];
      else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'SO_KHUNG') return anhToanCanhTitle[5];
      else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'SO_MAY') return anhToanCanhTitle[6];
    } else if (currentPage == 1) return menuImageStep3Selected?.ten;
    else if (currentPage == 2) {
      if (giayToDuocChon && giayToDuocChon.menuImageData) {
        if (giayToDuocChon.menuImageData.nhom_hang_muc == 'BANG_LAI') return BLXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc == 'DANG_KY') return DKXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc == 'DANG_KIEM') return DKTitle[giayToDuocChon.indexOpened];
        else return giayToDuocChon.menuImageData.ten;
      }
    }
  };

  const onPressChupAnh = async () => {
    if (cameraRef) {
      let cameraOptions = {fixOrientation: true};
      let cauHinhWidthAnh = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.ANH_TON_THAT_WIDTH, props.cauHinh);
      let cauHinhHeightAnh = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.ANH_TON_THAT_HEIGHT, props.cauHinh);
      if (currentPage !== 2) {
        cameraOptions.quality = 0.5;
        cameraOptions.width = 1800;
        if (cauHinhWidthAnh && cauHinhWidthAnh.gia_tri !== '0' && cauHinhHeightAnh && cauHinhHeightAnh.gia_tri !== '0') cameraOptions.quality = 0.8;
      } else cameraOptions.quality = 0.8;

      const dataImage = await cameraRef.takePictureAsync(cameraOptions);
      if (appSetting.rung) Vibration.vibrate(200);
      setCountPicture(countPicture + 1);
      //nếu đang chụp ảnh HỒ SƠ, GIẤY TỜ
      if (currentPage === 2) {
        if (
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD
        ) {
          Image.getSize(
            dataImage.uri,
            (imageWidth, imageHeight) => {
              let cropOptions = {
                path: dataImage.uri,
                freeStyleCropEnabled: true,
                cropperToolbarTitle: 'Xác nhận ảnh',
                disableCropperColorSetters: true,
                width: imageWidth,
                height: loaiCat === 0 ? imageWidth * 0.63 : imageHeight * 0.9,
              };
              if (giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM) cropOptions.height = loaiCat == 0 ? imageWidth * 0.67 : imageHeight * 0.9;
              setEnableCameraBtn(true);
              onPressTatCameraModal();
              if (!isIOS) {
                setDialogLoading(false);
                ImageCropPicker.openCropper(cropOptions)
                  .then((imageCropped) => {
                    setEnableCameraBtn(false);
                    handleImage(imageCropped, giayToDuocChon.menuImageData, giayToDuocChon.indexOpened, 0);
                  })
                  .catch((err) => setEnableCameraBtn(false));
              } else {
                setDialogLoading(true);
                setTimeout(() => {
                  setDialogLoading(false);
                  ImageCropPicker.openCropper(cropOptions)
                    .then((imageCropped) => {
                      setEnableCameraBtn(false);
                      handleImage(imageCropped, giayToDuocChon.menuImageData, giayToDuocChon.indexOpened, 0);
                    })
                    .catch((err) => setEnableCameraBtn(false));
                }, 1500);
              }
            },
            (err) => {
              Alert.alert('Có lỗi xảy ra khi chụp ảnh, vui lòng chụp lại', JSON.stringify(err));
            },
          );
        } else {
          dataImage.path = dataImage.uri;
          handleImage(dataImage, giayToDuocChon.menuImageData, giayToDuocChon.indexOpened, 0);
          if (giayToDuocChon.menuImageData.nhom_hang_muc === 'TEM_DANG_KIEM') onPressTatCameraModal();
        }
      } else if (currentPage === 1) {
        dataImage.path = dataImage.uri;
        if (cauHinhWidthAnh && cauHinhWidthAnh.gia_tri !== '0' && cauHinhHeightAnh && cauHinhHeightAnh.gia_tri !== '0') {
          try {
            let resizeResponse = await ImageResizer.createResizedImage(dataImage.path, +cauHinhWidthAnh.gia_tri, +cauHinhHeightAnh.gia_tri, 'PNG', 100, 0, undefined, undefined, {mode: 'stretch'});
            dataImage.path = resizeResponse.uri;
            handleImage(dataImage, menuImageStep3Selected, null, 0);
          } catch (error) {
            Alert.alert('Có lỗi khi chỉnh kích thước ảnh, vui lòng chụp lại', JSON.stringify(error));
          }
        } else handleImage(dataImage, menuImageStep3Selected, null, 0);
      } else if (currentPage === 0) {
        if (
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM
        ) {
          Image.getSize(
            dataImage.uri,
            (imageWidth, imageHeight) => {
              let cropOptions = {
                path: dataImage.uri,
                freeStyleCropEnabled: true,
                cropperToolbarTitle: 'Xác nhận ảnh',
                disableCropperColorSetters: true,
                width: imageWidth,
                height: loaiCat === 0 ? imageWidth * 0.63 : imageHeight * 0.9,
              };
              if (giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM) cropOptions.height = loaiCat == 0 ? imageWidth * 0.67 : imageHeight * 0.9;
              setEnableCameraBtn(true);
              onPressTatCameraModal();
              if (!isIOS) {
                setDialogLoading(false);
                ImageCropPicker.openCropper(cropOptions)
                  .then((imageCropped) => {
                    setEnableCameraBtn(false);
                    handleImage(imageCropped, menuImageStep2Selected, giayToDuocChon.indexOpened, 0);
                  })
                  .catch((err) => setEnableCameraBtn(false));
              } else {
                setDialogLoading(true);
                setTimeout(() => {
                  setDialogLoading(false);
                  ImageCropPicker.openCropper(cropOptions)
                    .then((imageCropped) => {
                      setEnableCameraBtn(false);
                      handleImage(imageCropped, menuImageStep2Selected, giayToDuocChon.indexOpened, 0);
                    })
                    .catch((err) => setEnableCameraBtn(false));
                }, 1500);
              }
            },
            (err) => {
              Alert.alert('Có lỗi xảy ra khi chụp ảnh, vui lòng chụp lại', JSON.stringify(err));
            },
          );
        } else {
          dataImage.path = dataImage.uri;
          let menuImageStep2SelectedTmp = menuImageStep2Selected;
          handleImage(dataImage, menuImageStep2SelectedTmp, giayToDuocChon.indexOpened, 0);

          if (
            menuImageStep2SelectedTmp.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG ||
            menuImageStep2SelectedTmp.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY
          ) {
            onPressTatCameraModal();
            return;
          }
          //nếu đã chụp 4 ảnh - thì tắt modal camera
          if (menuImageStep2SelectedTmp.indexOpened === 3) {
            onPressTatCameraModal();
            return;
          }
          menuImageStep2SelectedTmp.indexOpened = menuImageStep2SelectedTmp.indexOpened + 1;
          setViTriChupAnhToanCanh(menuImageStep2SelectedTmp.indexOpened);
        }
      }
    }
  };

  /* RENDER */
  return (
    <Modal isVisible={toggleModalCamera} swipeDirection={'down'} style={styles.modal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          <View style={{flex: 1}}>
            <RNCamera
              ref={(ref) => (cameraRef = ref)}
              playSoundOnCapture={appSetting.amThanhKhiChup}
              // onCameraReady={prepareRatio}
              ratio={
                (currentPage == 2 || currentPage == 0) &&
                (giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD)
                  ? '16:9'
                  : undefined
              } //nếu đang chụp ảnh HỒ SƠ, GIẤY TỜ
              style={styles.cameraPreview}
              type={cameraType}
              flashMode={flashData.flashMode}
              captureAudio={false}
              zoom={0}
              useNativeZoom={true}
              androidCameraPermissionOptions={{
                title: 'Cho phép ' + APP_NAME + ' truy cập camera của bạn',
                message: APP_NAME + ' muốn truy cập camera của bạn để chụp ảnh',
                buttonPositive: 'Đồng ý',
                buttonNegative: 'Để sau',
              }}>
              {(currentPage == 2 || currentPage == 0) &&
                (giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD) && (
                  <View style={styles.cropView}>
                    <View style={[styles.cropTopView, {marginBottom: 10}]} />
                    <View
                      // style={[styles.khungCatAnhCamera, giayToDuocChon?.menuImageData.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ? styles.sizeBLXDoc : styles.sizeDKDoc]}
                      style={[
                        styles.khungCatAnhCamera,
                        giayToDuocChon?.menuImageData.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM
                          ? loaiCat == 0
                            ? styles.sizeBLXDoc
                            : styles.sizeBLXNgang
                          : loaiCat == 0
                          ? styles.sizeDKDoc
                          : styles.sizeDKNgang,
                      ]}
                    />
                    <View style={[styles.cropTopView, {marginTop: 10}]} />
                  </View>
                )}
              <View style={styles.topViewCamera}>
                <TouchableOpacity onPress={onPressTatCameraModal} style={styles.btnCloseCamera}>
                  <Icon.AntDesign name="arrowleft" size={40} color={colors.WHITE} />
                </TouchableOpacity>
                {currentPage == 1 && (
                  <TouchableOpacity
                    onPress={() => {
                      if (countPicture == 0) {
                        Alert.alert('Thông báo', 'Vui lòng chụp ít nhất 1 ảnh để phân loại');
                        return;
                      }
                      onPressTatCameraModal();
                      setDialogLoading(true);
                      setTimeout(() => {
                        onPressPhanLoaiAnh(menuImageStep3Selected);
                        setDialogLoading(false);
                      }, 1500);
                    }}
                    style={styles.btnDanhGia}>
                    <Text children="Đánh giá" style={styles.txtDanhGia} />
                  </TouchableOpacity>
                )}
                {(currentPage == 2 || currentPage == 0) &&
                  (giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                    giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                    giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                    giayToDuocChon?.menuImageData.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD) && (
                    <TouchableOpacity
                      onPress={() => {
                        setLoaiCat(loaiCat == 0 ? 1 : 0);
                      }}
                      style={styles.btnDanhGia}>
                      <Icon.MaterialCommunityIcons name="crop-rotate" size={40} color="#FFF" />
                    </TouchableOpacity>
                  )}
              </View>

              <View style={styles.anhTitleView}>
                <Text children={getAnhTitle()} style={styles.txtTitleAnh} />
                {/* {currentPage === 1 && props.mucDoSelected && <Text children={props.mucDoSelected?.ten} style={styles.txtTitleAnh} />} */}
                <Text />
              </View>

              <View style={styles.countPicture}>
                <Text children={countPicture} style={styles.txtCountPicture} />
              </View>
            </RNCamera>
          </View>
          <View style={styles.btnsCameraView}>
            <TouchableOpacity onPress={onPressChangeFlash} style={styles.btnCameraView}>
              <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressChupAnh} disabled={enableCameraBtn}>
              <Icon.MaterialCommunityIcons name={'circle-slice-8'} size={100} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
              <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnsCameraView: {
    flexDirection: 'row',
    backgroundColor: colors.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    height: 120,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  topViewCamera: {
    position: 'absolute',
    left: 0,
    top: Platform.OS == 'android' ? 20 : 50,
    right: 0,
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    // flex: 1,
    // borderWidth: 1,
    // borderColor: '#FFF',
    flexDirection: 'row',
  },
  btnCloseCamera: {
    // position: 'absolute',
    // left: 10,
    // top: Platform.OS == 'android' ? 30 : 30,
  },
  btnDanhGia: {
    // position: 'absolute',
    // right: 10,
    // top: Platform.OS == 'android' ? 20 : 70,
    flexDirection: 'row',
    alignItems: 'center',
  },
  countPicture: {
    position: 'absolute',
    right: 15,
    bottom: 5,
  },
  txtCountPicture: {
    fontSize: 40,
    color: '#FFF',
    fontWeight: 'bold',
  },
  khungCatAnhCamera: {
    borderWidth: 3,
    borderStyle: 'dashed',
    borderRadius: 20,
  },
  //kích thước BANG_LAI_XE DỌC
  sizeBLXDoc: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.63,
  },
  //kích thước DANG_KIEM DỌC
  sizeDKDoc: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.67,
  },

  //kích thước BANG_LAI_XE DỌC
  sizeBLXNgang: {
    width: dimensions.width - 30,
    height: dimensions.height * 0.63,
  },
  //kích thước DANG_KIEM DỌC
  sizeDKNgang: {
    width: dimensions.width - 30,
    height: dimensions.height * 0.67,
  },

  txtDanhGia: {
    fontWeight: 'bold',
    fontSize: 16,
    color: colors.WHITE,
    marginRight: 5,
  },
  anhTitleView: {
    position: 'absolute',
    bottom: 10,
  },
  txtTitleAnh: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
    paddingHorizontal: 30,
    textAlign: 'center',
  },
  cropView: {
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cropTopView: {
    flex: 1,
    backgroundColor: '#000',
    width: dimensions.width,
    opacity: 0.8,
  },
});

const mapStateToProps = (state) => ({
  appSetting: state.appSetting,
});

// const mapDispatchToProps = {};

// export default connect(mapStateToProps, mapDispatchToProps)(ModalCamera);
const ModalCameraConnect = connect(mapStateToProps, {})(ModalCamera);
export default memo(ModalCameraConnect, isEqual);
