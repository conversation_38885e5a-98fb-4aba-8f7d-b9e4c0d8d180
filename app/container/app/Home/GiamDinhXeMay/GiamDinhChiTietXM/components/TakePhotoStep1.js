import {colors, dimension} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, ImageComp} from '@component';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';

const TakePhotoStep1Component = ({onPressOpenCamera, removeImage, imagesData, onPressXemLaiAnh}) => {
  const renderCameraButton = () => {
    return (
      <TouchableOpacity style={styles.cameraView} onPress={onPressOpenCamera}>
        <Icon.MaterialCommunityIcons name="camera-plus" size={40} color={colors.GRAY11} style={styles.iconCamera} />
      </TouchableOpacity>
    );
  };
  const renderImageItem = (imageData) => {
    let index = imageData.index;
    if (index === imagesData.length - 1) return renderCameraButton();
    return <ImageComp imageData={imageData} removeImage={removeImage} onPressOpenCamera={onPressOpenCamera} onPressXemLaiAnh={onPressXemLaiAnh} />;
  };
  return (
    <View>
      <FlatList scrollEnabled={true} data={imagesData} renderItem={renderImageItem} numColumns={3} horizontal={false} />
    </View>
  );
};

const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    width: dimensions.width,
  },
  btnLoginView: {
    flexDirection: 'row',
    backgroundColor: colors.PRIMARY,
    marginHorizontal: 10,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerView: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    backgroundColor: colors.WHITE,
  },
  txtBtnLogin: {
    paddingRight: 5,
    paddingVertical: 15,
    fontSize: 20,
    textTransform: 'uppercase',
    color: colors.WHITE,
  },
  headerTitle: {
    marginVertical: 10,
    fontSize: 16,
  },
  contentDetail: {
    paddingHorizontal: 20,
    marginBottom: 7,
    backgroundColor: colors.WHITE,
    borderRadius: 10,
    marginHorizontal: 10,
  },
  modalSelectorView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    marginRight: 15,
  },

  stepIndicator: {
    marginVertical: 20,
  },
  page: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GREEN,
  },
  cameraView: {
    borderWidth: 1,
    borderColor: colors.GRAY11,
    borderStyle: 'dashed',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: dimension.width / 4,
    height: dimension.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
  },
  iconCamera: {
    padding: 10,
  },
});

const TakePhotoStep1Memo = memo(TakePhotoStep1Component, isEqual);
export const TakePhotoStep1 = TakePhotoStep1Memo;
