import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalKetThucChupAnhComponent = forwardRef(({}, ref) => {
  const [visible, setVisible] = useState(false);
  useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
    },
    hide: onPressHideModal,
    getVisible: () => {
      return visible;
    },
  }));
  const onPressHideModal = () => {
    setVisible(false);
  };

  return (
    <Modal
      isVisible={visible} //toggleModalSuccess
      swipeDirection={'right'}
      style={styles.modal}>
      <View style={styles.modalSuccessView}>
        <View>
          <View style={styles.successTopView}>
            <Icon.Ionicons name="checkmark-circle" color={colors.GREEN6} size={80} />
            <Text style={styles.successTitle}>Hoàn thành chụp ảnh giám định</Text>
          </View>
          <View style={styles.successCenterView}>
            <>
              <TouchableOpacity
                style={{
                  ...styles.btnSuccessView,
                  backgroundColor: colors.PRIMARY_08,
                }}
                onPress={() => {
                  onPressHideModal();
                  setTimeout(() => NavigationUtil.pop(), 250);
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                  }}>
                  <Icon.AntDesign name="profile" color={colors.WHITE} size={20} />
                  <Text style={styles.txtSuccess} children="Xem chi tiết hồ sơ" />
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  ...styles.btnSuccessView,
                  backgroundColor: 'green',
                }}
                onPress={() => {
                  onPressHideModal();
                  setTimeout(() => NavigationUtil.pop(4), 300);
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                  }}>
                  <Icon.Feather name="home" color={colors.WHITE} size={20} />
                  <Text style={styles.txtSuccess} children="Trở về trang chủ" />
                </View>
              </TouchableOpacity>
            </>
          </View>
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalSuccessView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  successTopView: {
    width: dimensions.width - 30,
    height: dimensions.height / 5,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
  },
  btnSuccessView: {
    height: dimensions.height / 20,
    width: dimensions.width - 50,

    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
  },
  successCenterView: {
    width: dimensions.width - 30,
    height: dimensions.height / 4,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  txtSuccess: {
    textAlign: 'center',
    paddingHorizontal: 10,
    color: colors.WHITE,
    fontSize: 16,
  },
  btnFormKetThucGiamDinh: {
    height: dimensions.height / 20,
    width: (dimensions.width - 100) / 2,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 5,
  },
});

const ModalKetThucChupAnhMemo = memo(ModalKetThucChupAnhComponent, isEqual);
export const ModalKetThucChupAnh = ModalKetThucChupAnhMemo;
