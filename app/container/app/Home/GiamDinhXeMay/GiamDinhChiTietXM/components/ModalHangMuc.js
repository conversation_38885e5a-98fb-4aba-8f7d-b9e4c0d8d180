import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, FlatList, Image, Keyboard, SafeAreaView, ScrollView, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import ImageZoom from 'react-native-image-pan-zoom';
import Modal from 'react-native-modal';
import {TOA_DO_ANH_BEN_LAI_XE, TOA_DO_ANH_BEN_PHU_XE, TOA_DO_ANH_SAU_XE, TOA_DO_ANH_TRUOC_XE, anhXungQuanhXe} from './Constant';
import {MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC} from '../Constant';

const ModalHangMucComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  let scrollViewModalRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState(dimensions.height);
  const [searchInput, setSearchInput] = useState('');
  //DROPDOWN MỨC ĐỔ TỔN THẤT
  // const [levelLossSelected, setLevelLossSelected] = useState(null);
  const [levelLossData, setLevelLossData] = useState([]);
  const [hangMucSelected, setHangMucSelected] = useState(null);

  const collapsedHeight = 50;
  const [tabActive, setTabActive] = useState(0);
  const [kichThuocAnhThat, setKichThuocAnhThat] = useState({
    width: 0,
    height: 0,
  }); //kích thước ảnh thật
  const [isXoayAnh, setIsXoayAnh] = useState(false);
  const [kichThuocVungChuaANh, setKichThuocVungChuaAnh] = useState(null); //kích thước vùng chứa ảnh
  const [viTriAnhPress, setViTriAnhPress] = useState(null);
  const [anhDuocChon, setAnhDuocChon] = useState(2);
  const [kichThuocAnhHienThi, setKichThuocAnhHienThi] = useState({
    width: dimensions.width,
    height: dimensions.width,
  });

  const [isReloadTatCaHangMuc, setIsReloadTatCaHangMuc] = useState(false);

  useEffect(() => {
    initMucDoTonThat();
  }, []);
  const onModalHide = () => setHangMucSelected(null);

  const initMucDoTonThat = () => {
    const {doiTuongDuocChupAnh, categoryCommon, profileData} = props;
    const maDoiTuong = doiTuongDuocChupAnh.nhom;
    let mucDoTonThatFilter = [];
    // FILTER THEO NGHIỆP VỤ
    let mucDoTonThatFilterNghiepVu = categoryCommon.levelLost.filter((item) => item.nv_hs === 'XE');
    //NẾU ĐỐI TƯỢNG LÀ XE
    if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    // NẾU ĐỐI TƯỢNG LÀ HÀNG HOÁ
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.HANG_HOA) mucDoTonThatFilter.push(mucDo);
      }
    }
    // NẾU ĐỐI TƯỢNG LÀ NGƯỜI
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.NGUOI) mucDoTonThatFilter.push(mucDo);
      }
    }
    // NẾU ĐÓI TƯỢNG LÀ TÀI SẢN
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) {
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN KHÁC
      if (doiTuongDuocChupAnh?.loai === 'KHAC') mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === doiTuongDuocChupAnh.nhom);
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN XE
      else if (doiTuongDuocChupAnh?.loai === 'XE' || doiTuongDuocChupAnh?.loai === 'XE_MAY')
        mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    }
    setLevelLossData([...mucDoTonThatFilter]);
  };

  const checkToaDoHangMuc = (toaDoAnhPressThuc, toaDoHangMuc) =>
    toaDoAnhPressThuc.x > toaDoHangMuc.TOA_DO_1.x && toaDoAnhPressThuc.x < toaDoHangMuc.TOA_DO_2.x && toaDoAnhPressThuc.y > toaDoHangMuc.TOA_DO_1.y && toaDoAnhPressThuc.y < toaDoHangMuc.TOA_DO_3.y;

  const onPressAnh = (event) => {
    let toaDoAnhPress = {
      // x: +event.nativeEvent.locationX.toFixed(3),
      // y: +event.nativeEvent.locationY.toFixed(3),
      x: +event.locationX.toFixed(3),
      y: +event.locationY.toFixed(3),
    };
    if (toaDoAnhPress.x === 0 && toaDoAnhPress.y === 0) return;
    setViTriAnhPress(toaDoAnhPress);
    let toaDoAnhPressThuc = !isXoayAnh
      ? {
          x: (toaDoAnhPress.x * kichThuocAnhThat.width) / kichThuocAnhHienThi.width,
          y: (toaDoAnhPress.y * kichThuocAnhThat.width) / kichThuocAnhHienThi.width,
        }
      : {
          x: (toaDoAnhPress.x * kichThuocAnhThat.width) / kichThuocAnhHienThi.width,
          y: (toaDoAnhPress.y * kichThuocAnhThat.height) / kichThuocAnhHienThi.height,
        };
    // console.log('=============');
    // console.log('toaDoAnhPress', toaDoAnhPress); //trả ra kích thước thật của ảnh
    // console.log('toaDoAnhPressThuc', toaDoAnhPressThuc); //trả ra kích thước thật của ảnh
    // console.log('kichThuocAnhHienThi', kichThuocAnhHienThi); //trả ra kích thước thật của ảnh
    // return;
    /* ẢNH TRƯỚC XE */
    if (anhDuocChon === 0) {
      //KÍNH CHẮN GIÓ
      if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.KINH_CHAN_GIO_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.KINH_CHAN_GIO_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.KINH_CHAN_GIO_DOC.ma);
      //GƯƠNG CHIẾU HẬU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.GUONG_CHIEU_HAU_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.GUONG_CHIEU_HAU_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.GUONG_CHIEU_HAU_BEN_PHU_NGANG.ma);
      //GƯƠNG CHIẾU HẬU BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.GUONG_CHIEU_HAU_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.GUONG_CHIEU_HAU_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.GUONG_CHIEU_HAU_BEN_LAI_DOC.ma);
      //NẮP CAPO
      else if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.NAP_CAPO_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.NAP_CAPO_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.NAP_CAPO_DOC.ma);
      //ĐÈN PHA BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_LAI_NGANG.ma);
      //ĐÈN PHA BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_PHU_DOC.ma);
      //ĐÈN PHA BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.DEN_PHA_BEN_PHU_DOC.ma);
      //CẢN TRƯỚC
      else if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_DOC.ma);
      //CẢN TRƯỚC BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_BEN_PHU_DOC.ma);
      //CẢN TRƯỚC BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.CAN_TRUOC_BEN_LAI_DOC.ma);
      //NÓC XE
      else if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.NOC_XE_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.NOC_XE_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.NOC_XE_DOC.ma);
      //LỐP TRƯỚC BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.LOP_TRUOC_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.LOP_TRUOC_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.LOP_TRUOC_BEN_LAI_NGANG.ma);
      //LỐP TRƯỚC BÊN LÁI
      else if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.LOP_TRUOC_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_TRUOC_XE.LOP_TRUOC_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_TRUOC_XE.LOP_TRUOC_DOC.ma);
    } else if (anhDuocChon === 1) {
      /* ẢNH SAU XE */
      // NÓC XE
      if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.NOC_XE_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.NOC_XE_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.NOC_XE_DOC.ma);
      //GƯƠNG CHIẾU HẬU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.GUONG_CHIEU_HAU_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.GUONG_CHIEU_HAU_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.GUONG_CHIEU_HAU_BEN_PHU_DOC.ma);
      //GƯƠNG CHIẾU HẬU BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.GUONG_CHIEU_HAU_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.GUONG_CHIEU_HAU_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.GUONG_CHIEU_HAU_BEN_LAI_DOC.ma);
      //KÍNH CHẮN GIÓ HẬU
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.KINH_CHAN_GIO_HAU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.KINH_CHAN_GIO_HAU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.KINH_CHAN_GIO_HAU_DOC.ma);
      //CỐP SAU
      else if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.COP_SAU_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.COP_SAU_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.COP_SAU_DOC.ma);
      //ĐÈN BÁO PHANH BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.DEN_BAO_PHANH_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.DEN_BAO_PHANH_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.DEN_BAO_PHANH_BEN_LAI_DOC.ma);
      //ĐÈN BÁO PHANH BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.DEN_BAO_PHANH_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.DEN_BAO_PHANH_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.DEN_BAO_PHANH_BEN_PHU_DOC.ma);
      //CẢN SAU BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.CAN_SAU_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.CAN_SAU_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.CAN_SAU_BEN_LAI_DOC.ma);
      //CẢN SAU
      else if ((!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.CAN_SAU_DOC)) || (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.CAN_SAU_NGANG)))
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.CAN_SAU_DOC.ma);
      //CẢN SAU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.CAN_SAU_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.CAN_SAU_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.CAN_SAU_BEN_PHU_DOC.ma);
      //LỐP SAU BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.LOP_SAU_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.LOP_SAU_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.LOP_SAU_BEN_LAI_DOC.ma);
      //LỐP SAU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.LOP_SAU_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_SAU_XE.LOP_SAU_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_SAU_XE.LOP_SAU_BEN_PHU_DOC.ma);
    } else if (anhDuocChon === 2) {
      /* ẢNH BÊN LÁI */
      // MÂM TRƯỚC BÊN LÁI
      if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.MAM_TRUOC_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.MAM_TRUOC_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_LAI_XE.MAM_TRUOC_BEN_LAI_DOC.ma);
      // MÂM TRƯỚC BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.MAM_SAU_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.MAM_SAU_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_LAI_XE.MAM_SAU_BEN_LAI_DOC.ma);
      // KÍNH CỬA TRƯỚC BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.KINH_CUA_TRUOC_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.KINH_CUA_TRUOC_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_LAI_XE.KINH_CUA_TRUOC_BEN_LAI_DOC.ma);
      // KÍNH CỬA SAU BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.KINH_CUA_SAU_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.KINH_CUA_SAU_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_LAI_XE.KINH_CUA_SAU_BEN_LAI_DOC.ma);
      // CỬA SAU BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.CUA_SAU_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.CUA_SAU_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_LAI_XE.CUA_SAU_BEN_LAI_DOC.ma);
      // CỬA TRƯỚC BÊN LÁI
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.CUA_TRUOC_BEN_LAI_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.CUA_TRUOC_BEN_LAI_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_LAI_XE.CUA_TRUOC_BEN_LAI_DOC.ma);
      // ĐÈN BÁO PHANH
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.DEN_BAO_PHANH_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_LAI_XE.DEN_BAO_PHANH_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_LAI_XE.DEN_BAO_PHANH_DOC.ma);
    } else if (anhDuocChon === 3) {
      // ĐÈN BÁO PHANH
      if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.DEN_BAO_PHANH_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.DEN_BAO_PHANH_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_PHU_XE.DEN_BAO_PHANH_DOC.ma);
      // MÂM SAU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.MAM_SAU_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.MAM_SAU_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_PHU_XE.MAM_SAU_BEN_PHU_DOC.ma);
      // MÂM SAU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.MAM_TRUOC_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.MAM_TRUOC_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_PHU_XE.MAM_TRUOC_BEN_PHU_DOC.ma);
      // CỬA TRƯỚC BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.CUA_TRUOC_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.CUA_TRUOC_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_PHU_XE.CUA_TRUOC_BEN_PHU_DOC.ma);
      // CỬA SAU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.CUA_SAU_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.CUA_SAU_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_PHU_XE.CUA_SAU_BEN_PHU_DOC.ma);
      // KÍNH CỬA SAU BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.KINH_SAU_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.KINH_SAU_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_PHU_XE.KINH_SAU_BEN_PHU_DOC.ma);
      // KÍNH CỬA TRƯỚC BÊN PHỤ
      else if (
        (!isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.KINH_TRUOC_BEN_PHU_DOC)) ||
        (isXoayAnh && checkToaDoHangMuc(toaDoAnhPressThuc, TOA_DO_ANH_BEN_PHU_XE.KINH_TRUOC_BEN_PHU_NGANG))
      )
        handleHangMucDuocChon(TOA_DO_ANH_BEN_PHU_XE.KINH_TRUOC_BEN_PHU_DOC.ma);
    }

    /**
      identifier : định danh duy nhất được gán cho sự kiện
      locationX, locationY : toạ độ press so với ảnh, ảnh có chiều dài, rộng = rộng màn hình 
                              -> giá trị của locationX, location Y sẽ <= chiều rộng màn hình
      pageX, pageY : toạ độ press so với toàn màn hình

      Ý TƯỞNG:
      - CLIENT
      + từ ảnh 4 góc của xe => mỗi hạng mục sẽ có 4 toạ độ trên ảnh gốc tương ứng với 4 góc
      + lấy được kích thước thật của ảnh 
      + kích thước ảnh hiển thi sẽ = kích thước màn hình
      + tính ra tỉ lệ kích thước ảnh trên màn hình / kích thước thật của ảnh 
      + có toạ độ tương ứng
      + khi press vào 1 điểm trên ảnh -> sẽ lấy được toạ độ press -> so sánh với 4 toạ độ của hạng mục -> tìm ra hạng mục được press
       */
  };
  const handleHangMucDuocChon = (ma) => {
    const {categoryCommon} = props;
    categoryCommon.type1.forEach((hangMuc) => {
      if (hangMuc.ma === ma) {
        setHangMucSelected(hangMuc);
        return;
      }
    });
  };

  const onPressXoayAnh = () => {
    if (isXoayAnh) {
      setKichThuocAnhHienThi({
        width: dimensions.width,
        height: dimensions.width,
      });
    } else {
      setKichThuocAnhHienThi({
        // width: kichThuocVungChuaANh.height - collapsedHeight - 20, //-50 vì k biết tại sao nó ra đủ màn hình :v
        // height: kichThuocVungChuaANh.width,
        width: kichThuocVungChuaANh.width,
        height: kichThuocVungChuaANh.height - collapsedHeight - 50,
      });
    }
    setIsXoayAnh(!isXoayAnh);
    setViTriAnhPress(null);
  };

  const onPressTaiLaiHangMuc = async () => {
    setIsReloadTatCaHangMuc(true);
    await props.onPressLayDataHangMuc();
    setIsReloadTatCaHangMuc(false);
    Alert.alert('Thành công', 'Tải dữ liệu hạng mục mới thành công');
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <TouchableOpacity style={styles.backBtn} onPress={() => setIsVisible(false)}>
          <Icon.AntDesign name="arrowleft" size={20} style={styles.iconBack} />
        </TouchableOpacity>
        <TextInput
          style={styles.searchInput}
          value={searchInput}
          placeholder="Tìm kiếm..."
          placeholderTextColor={colors.BLACK}
          onChangeText={(value) => {
            setSearchInput(value);
            setHangMucSelected(null);
            props.onChangeSearchTextHangMuc(value);
          }}
          onFocus={() => Keyboard.o}
        />

        <TouchableOpacity
          style={styles.backBtn}
          onPress={() => {
            props.clearSearch();
            setSearchInput('');
          }}>
          <Icon.MaterialCommunityIcons name="filter-remove-outline" size={20} style={styles.iconBack} />
        </TouchableOpacity>

        <View style={{width: 50, justifyContent: 'center', alignItems: 'center'}}>
          {isReloadTatCaHangMuc ? (
            <ActivityIndicator size="small" color={colors.PRIMARY} />
          ) : (
            <TouchableOpacity onPress={() => onPressTaiLaiHangMuc()}>
              <Icon.AntDesign name="download" size={20} style={styles.iconBack} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };
  const renderItemHangMuc = (item, index) => {
    let anhHangMucDaUpload = props.getAnhDaUploadTheoMaHangMuc(item.ma);
    return (
      <TouchableOpacity
        key={index}
        style={styles.itemHangMucView}
        onPress={() => {
          if (anhHangMucDaUpload.length >= MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC) {
            setHangMucSelected(null);
            return Alert.alert('Thông báo', 'Hạng mục này đã chụp tối đa ' + MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC + ' ảnh. Vui lòng xoá bớt ảnh để chụp thêm');
          }
          setHangMucSelected(item);
        }}>
        {hangMucSelected?.ma === item.ma ? <Icon.AntDesign name="check" color={colors.PRIMARY} size={20} /> : <Icon.AntDesign size={20} name="check" color="transparent" />}
        <Text style={[{color: hangMucSelected?.ma == item.ma ? colors.PRIMARY : '#000'}, {marginLeft: 5}]}>
          {item.ten}
          {anhHangMucDaUpload.length > 0 && <Text color={colors.PRIMARY}>{' (' + anhHangMucDaUpload.length + '/' + MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC + ')'}</Text>}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderItemMucDoTonThat = (item, index) => (
    <TouchableOpacity
      key={index}
      style={[styles.itemHangMucView, {paddingVertical: spacing.small}]}
      onPress={() => {
        setIsVisible(false);
        props.setMucDoSelected(item);
        props.onHangMucSelected(hangMucSelected);
      }}>
      <Text children={item.ten} />
    </TouchableOpacity>
  );

  const renderMucDoTonThat = () => (
    <View style={styles.mucDoTonThatView}>
      <View style={styles.mucDoTonThatHeader}>
        <View style={{flex: 1, marginRight: spacing.tiny}}>
          <Text children="Chọn mức độ tổn thất" style={styles.txtChonMucDoTonThat} />
          {tabActive === 1 && hangMucSelected && <Text children={hangMucSelected.ten} style={[styles.txtTenHangMucSelected]} color={colors.PRIMARY} />}
        </View>
        <TouchableOpacity onPress={() => setHangMucSelected(null)}>
          <Icon.AntDesign name="closecircleo" size={25} />
        </TouchableOpacity>
      </View>
      <ScrollView showsVerticalScrollIndicator={false}>{levelLossData.map((item, index) => renderItemMucDoTonThat(item, index))}</ScrollView>
      <View style={{borderTopWidth: 1, borderColor: '#000'}}>
        <TouchableOpacity
          onPress={() => {
            setIsVisible(false);
            props.setMucDoSelected(null);
            props.onHangMucSelected(hangMucSelected);
          }}>
          <Text children="Tiếp tục" style={styles.txtTiepTuc} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAnhXungQuanhXe = ({item, index}) => {
    return (
      <TouchableOpacity
        onPress={() => {
          setAnhDuocChon(index);
          setViTriAnhPress(null);
        }}
        activeOpacity={0.8}
        style={[styles.anhXungQuanhXeItemView, {borderBottomWidth: 2, borderBottomColor: index === anhDuocChon ? colors.PRIMARY : '#FFF'}]}>
        <Text children={item.name} style={index === anhDuocChon && {color: colors.PRIMARY, fontWeight: 'bold'}} />
      </TouchableOpacity>
    );
  };

  const renderChonVungXeChupAnh = () => (
    <View style={styles.vungXeChupAnhView}>
      <View style={styles.flatListView}>
        <FlatList data={anhXungQuanhXe} renderItem={renderAnhXungQuanhXe} horizontal scrollEnabled={false} />
      </View>
    </View>
  );

  const renderTabAnhHangMuc = () => {
    return (
      <View style={styles.tabAnhHangMucView}>
        <View
          style={{
            flex: 1,
          }}>
          <View
            style={[{flex: 1}]}
            onLayout={(event) => {
              setKichThuocVungChuaAnh({
                width: event.nativeEvent.layout.width,
                height: event.nativeEvent.layout.height,
              });
            }}
            activeOpacity={0.9}
            // onPress={(event) => onPressAnh(event)}
          >
            {!isXoayAnh && (
              <View>
                <View style={[styles.imageSelectedView]}>
                  <ImageZoom
                    cropWidth={kichThuocAnhHienThi.width}
                    cropHeight={kichThuocAnhHienThi.height}
                    imageWidth={kichThuocAnhHienThi.width}
                    imageHeight={kichThuocAnhHienThi.height}
                    onClick={(event) => onPressAnh(event)}
                    // onClick={(event) => onPressAnh2(event)}
                  >
                    <FastImage
                      source={anhXungQuanhXe[anhDuocChon].image}
                      resizeMode="stretch"
                      style={[styles.imgSelected, {width: kichThuocAnhHienThi.width, height: kichThuocAnhHienThi.height}]}
                      onLoad={(onLoadEvent) => {
                        let image = Image.resolveAssetSource(anhXungQuanhXe[anhDuocChon].image);
                        setKichThuocAnhThat({
                          width: image.width,
                          height: image.height,
                        });
                      }}
                    />
                  </ImageZoom>

                  {/* {viTriAnhPress && (
                    <View
                      style={{
                        position: 'absolute',
                        top: viTriAnhPress.y - 10, //-10 là kích thước của icon nên phải trừ đi 10 cho chính xác
                        left: viTriAnhPress.x - 10,
                      }}>
                      <Icon.MaterialCommunityIcons name="target" size={25} color={colors.RED3} />
                    </View>
                  )} */}
                </View>
              </View>
            )}

            {isXoayAnh && (
              <View>
                <View style={[styles.imageSelectedView]}>
                  <ImageZoom
                    cropWidth={kichThuocAnhHienThi.width}
                    cropHeight={kichThuocAnhHienThi.height}
                    imageWidth={kichThuocAnhHienThi.width}
                    imageHeight={kichThuocAnhHienThi.height}
                    onClick={(event) => onPressAnh(event)}>
                    <FastImage
                      source={anhXungQuanhXe[anhDuocChon].imageNgang}
                      resizeMode="stretch"
                      style={[
                        styles.imgSelected,
                        {
                          width: kichThuocAnhHienThi.width,
                          height: kichThuocAnhHienThi.height,
                        },
                      ]}
                      onLoad={(onLoadEvent) => {
                        let image = Image.resolveAssetSource(anhXungQuanhXe[anhDuocChon].imageNgang);
                        setKichThuocAnhThat({
                          width: image.width,
                          height: image.height,
                        });
                      }}
                    />
                  </ImageZoom>
                  {/* {viTriAnhPress && (
                    <View
                      style={{
                        position: 'absolute',
                        top: viTriAnhPress.y - 10, //-10 là kích thước của icon nên phải trừ đi 10 cho chính xác
                        left: viTriAnhPress.x - 10,
                      }}>
                      <Icon.MaterialCommunityIcons name="target" size={25} color={colors.RED3} />
                    </View>
                  )} */}
                </View>
              </View>
            )}
          </View>
          {/* {renderChonVungXeChupAnh()} */}
        </View>

        <TouchableOpacity style={styles.iconXoayAnhView} onPress={onPressXoayAnh}>
          <Icon.Feather name={isXoayAnh ? 'rotate-ccw' : 'rotate-cw'} size={25} color={colors.PRIMARY} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderTabDanhSachHangMuc = () => {
    const {data} = props;
    return (
      <ScrollView
        style={{marginRight: spacing.small, marginLeft: spacing.small, marginTop: 20}}
        ref={scrollViewModalRef}
        onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}>
        <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>{data.map((item, index) => renderItemHangMuc(item, index))}</View>
      </ScrollView>
    );
  };
  const renderContent = () => {
    const {doiTuongDuocChupAnh} = props;
    const isDoiTuongXeMay = (doiTuongDuocChupAnh.hang_muc === 'XE' && doiTuongDuocChupAnh.nhom === 'XE') || (doiTuongDuocChupAnh.kieu_dt === 'TT' && doiTuongDuocChupAnh.loai === 'XE_MAY');
    return (
      <View style={{flex: 1}}>
        {/* {isDoiTuongXeMay && (
          <View style={styles.tabViewList}>
            <View style={styles.bgTabbar}>
              <View style={styles.tabView}>
                <TouchableOpacity style={[styles.tabBar, tabActive === 0 && styles.borderBottom]} onPress={() => setTabActive(0)}>
                  <Text style={[styles.tabBarTxt, tabActive === 0 && styles.tabBarTxtActive]}>Danh sách</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.tabBar, tabActive === 1 && styles.borderBottom]} onPress={() => setTabActive(1)}>
                  <Text style={[styles.tabBarTxt, tabActive === 1 && styles.tabBarTxtActive]}>Ảnh</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )} */}

        {tabActive === 0 && renderTabDanhSachHangMuc()}
        {tabActive === 1 && renderTabAnhHangMuc()}
        {hangMucSelected && renderMucDoTonThat()}
      </View>
    );
  };
  return (
    <Modal isVisible={isVisible} style={styles.modal} onModalHide={onModalHide} avoidKeyboard={true}>
      <SafeAreaView style={{flex: 1}}>
        <View style={styles.modalCameraView}>
          <View style={styles.modalCameraContent}>
            {renderHeader()}
            {renderContent()}
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
    backgroundColor: colors.WHITE,
    paddingVertical: spacing.small,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingLeft: spacing.small,
    flex: 1,
    borderRadius: 5,
    height: 50,
  },
  headerView: {
    flexDirection: 'row',
  },
  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 50,
  },
  iconBack: {
    paddingHorizontal: spacing.small,
  },
  itemHangMucView: {
    paddingVertical: spacing.smaller,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mucDoTonThatView: {
    position: 'absolute',
    right: 0,
    top: 20,
    bottom: 0,
    backgroundColor: '#FFF',
    paddingHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,

    width: dimensions.width * 0.5,
  },
  tabViewList: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: colors.WHITE,
  },
  bgTabbar: {},
  tabView: {
    flexDirection: 'row',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: spacing.small,
    backgroundColor: colors.WHITE,
  },
  tabBar: {
    flex: 1,
    borderBottomWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: colors.BORDER_GRAY,
    paddingVertical: spacing.medium,
  },
  borderBottom: {
    borderBottomWidth: 3,
    borderColor: colors.PRIMARY,
  },
  tabBarTxtActive: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  mucDoTonThatHeader: {
    borderBottomWidth: 1,
    borderColor: '#000',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.smaller,
  },
  txtChonMucDoTonThat: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tabAnhHangMucView: {
    flex: 1,
  },
  matXeView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    marginHorizontal: spacing.small,
    borderBottomColor: '#000',
    paddingBottom: spacing.small,
  },
  txtTiepTuc: {
    paddingVertical: spacing.small,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imgAnhChonHangMuc: {},
  imgAnhXungQuanhXeItem: {
    width: dimensions.width / 5,
    height: dimensions.width / 5,
    borderRadius: 20,
  },
  anhXungQuanhXeItemView: {
    width: dimensions.width / 4 - spacing.small,
    alignItems: 'center',
    borderColor: '#CCC',
    backgroundColor: '#FFF',
    paddingVertical: spacing.small,
  },
  anhXungQuanhXeItemViewActive: {},
  imgSelected: {
    width: dimensions.width,
    height: dimensions.width,
  },
  imageSelectedView: {},
  txtHangMucKhac: {
    marginTop: spacing.small,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: spacing.small,
    fontSize: 16,
  },
  boPhanKhacView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedView: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  flatListView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtChonKhuVucXe: {
    textAlign: 'center',
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  chonKhuVucXeView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.small,
    backgroundColor: colors.PRIMARY,
    paddingTop: spacing.small,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  iconUpDownView: {
    alignSelf: 'center',
  },
  iconXoayAnhView: {
    position: 'absolute',
    right: 10,
    top: 10,
    alignItems: 'center',
  },
  txtHuongDanView: {
    backgroundColor: colors.PRIMARY,
    paddingVertical: spacing.tiny,
    paddingHorizontal: spacing.smaller,
    borderRadius: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtHuongDanPosition: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtHuongDan: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFF',
    marginLeft: spacing.tiny,
  },
  vungXeChupAnhView: {position: 'absolute', bottom: 0, left: 0, right: 0},
  txtTenHangMucSelected: {
    textAlign: 'center',
    fontWeight: 'bold',
  },
});
export const ModalHangMuc = memo(ModalHangMucComponent, isEqual);
