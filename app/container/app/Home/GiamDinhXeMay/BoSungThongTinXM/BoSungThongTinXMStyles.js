import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    paddingVertical: vScale(spacing.smaller),
    paddingHorizontal: scale(spacing.small),
  },
  inputView: {
    flex: 1,
    marginBottom: vScale(spacing.small),
  },
  btn: {
    flex: 1,
    borderRadius: 30,
  },

  btnView: {
    paddingVertical: vScale(10),
    borderTopWidth: 0.2,
    flexDirection: 'row',
    width: dimensions.width,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
});
