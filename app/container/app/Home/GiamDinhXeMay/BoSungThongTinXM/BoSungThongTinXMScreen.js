import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, TextInputOutlined} from '@component';
import {REGUlAR_EXPRESSION, SCREEN_ROUTER_APP} from '@constant';
import moment from 'moment';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View, SafeAreaView} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import styles from './BoSungThongTinXMStyles';
import {HANG_GPLXM} from './Constans';
import {colors} from '@app/commons/Theme';
import {onChangeAlias} from '@app/utils/string';
const BoSungThongTinXMScreenComponent = ({route, navigation}) => {
  console.log('BoSungThongTinXMScreenComponent');
  const {params} = route;
  const {profileData, thongTinGiayTo, doiTuongDuocChupAnh, prevScreen} = params;

  const [openVuTT, setOpenVuTT] = useState(false);
  const [openDoiTuong, setOpenDoiTuong] = useState(false);
  const [openHangGPLX, setOpenHangGPLX] = useState(false);
  const [hangGPLX, setHangGPLX] = useState(!profileData.dien_bien[0].gplx_hang ? ['A1'] : profileData.dien_bien[0].gplx_hang.split(','));
  const [toggleNgayCapGPLX, setToggleNgayCapGPLX] = useState(false);
  const [toggleNgayHetHanGPLX, setToggleNgayHetHanGPLX] = useState(false);
  const [onSubmiting, setOnSubmiting] = useState(false);
  const [listVuTonThat, setListVuTonThat] = useState([]);

  useEffect(() => {
    setValue('hangGPLX', hangGPLX);
  }, [hangGPLX]);

  useEffect(() => {
    let listVuTonThat = profileData.dien_bien;
    listVuTonThat.forEach((item) => {
      item.label = item.ngay_xr + item.dia_diem;
      item.value = item.vu_tt;
    });
    let all = [{label: 'Áp dụng tất cả', value: '-1'}];
    setListVuTonThat([...all, ...listVuTonThat]);
  }, []);

  // const getListVuTonThat = () => {
  //   let listVuTonThat = profileData.dien_bien;
  //   listVuTonThat.forEach((item) => {
  //     item.label = item.ngay_xr + item.dia_diem;
  //     item.value = item.vu_tt;
  //   });
  //   let all = [{label: 'Áp dụng tất cả', value: '-1'}];
  //   return listVuTonThat;
  // };
  // const getListDoiTuong = () => {
  //   let listDoiTuong = profileData.ds_doi_tuong;
  //   const filter = listDoiTuong.filter((item) => item.nhom === 'XE' || item.loai === 'XE');
  //   return filter;
  // };
  const getDefaultFormValue = () => {
    const dienBien = profileData.dien_bien;
    const listDoiTuong = profileData.ds_doi_tuong;
    return {
      doiTuong: listDoiTuong.length === 1 ? listDoiTuong[0].so_id_doi_tuong : null,
      vuTonThat: dienBien.length === 1 ? dienBien[0].vu_tt : null,
      hoTen: dienBien[0].ten_lxe ? dienBien[0].ten_lxe : '',
      dienThoai: dienBien[0].dthoai_lxe ? dienBien[0].dthoai_lxe : '',
      email: dienBien[0].email_lxe ? dienBien[0].email_lxe : '',
      soGPLX: dienBien[0].gplx_so ? dienBien[0].gplx_so : '',
      hangGPLX: dienBien[0].gplx_hang ? dienBien[0].gplx_hang.split(',') : ['B2'],
      ngayCapGPLX: dienBien[0].gplx_hieu_luc ? new Date(moment(dienBien[0].gplx_hieu_luc, 'DD/MM/YYYY')) : '',
      ngayHetHanGPLX: dienBien[0].gplx_het_han ? new Date(moment(dienBien[0].gplx_het_han, 'DD/MM/YYYY')) : '',
      soDangKiem: dienBien[0].dangkiem_so ? dienBien[0].dangkiem_so : '',
      ngayCapDK: dienBien[0].dangkiem_hieu_luc ? new Date(moment(dienBien[0].dangkiem_hieu_luc, 'DD/MM/YYYY')) : new Date(),
      ngayHetHanDK: dienBien[0].dangkiem_het_han ? new Date(moment(dienBien[0].dangkiem_het_han, 'DD/MM/YYYY')) : new Date(),
    };
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'dienThoai') {
      if (errType === 'pattern') return 'Số điện thoại sai định dạng';
    } else if (inputName === 'email') {
      if (errType === 'pattern') return 'Email sai định dạng';
    }
    return '';
  };

  const onSubmit = async (data) => {
    setOnSubmiting(true);
    if (thongTinGiayTo.nhom_hang_muc === 'BANG_LAI') {
      try {
        let soGPLX = onChangeAlias(data.soGPLX, {xoaKyTuDacBiet: false}).toUpperCase();
        let params = {
          so_id: profileData.ho_so.so_id,
          so_id_doi_tuong: prevScreen === SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY ? data.doiTuong : doiTuongDuocChupAnh.so_id_doi_tuong,
          vu_tt: data.vuTonThat,
          ten_lxe: data.hoTen,
          email_lxe: data.email,
          dthoai_lxe: data.dienThoai,
          gplx_so: soGPLX,
          gplx_hang: data.hangGPLX.join(','),
          gplx_hieu_luc: +moment(data.ngayCapGPLX).format('YYYYMMDD'), // chuyển thành number
          gplx_het_han: +moment(data.ngayHetHanGPLX).format('YYYYMMDD'), // chuyển thành number
        };
        setValue('soGPLX', soGPLX);
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.BO_SUNG_THONG_TIN_BANG_LAI_XE_XM, params);
        setOnSubmiting(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật bằng lái xe thành công', 'success');
        let profileDataUpdate = profileData;
        profileDataUpdate.dien_bien.forEach((dienBienItem) => {
          if (dienBienItem.vu_tt === params.vu_tt) {
            dienBienItem.dthoai_lxe = params.dthoai_lxe;
            dienBienItem.ten_lxe = params.ten_lxe;
            dienBienItem.email_lxe = params.email_lxe;
            dienBienItem.gplx_so = params.gplx_so;
            dienBienItem.gplx_hang = params.gplx_hang;
            dienBienItem.gplx_hieu_luc = moment(data.ngayCapGPLX).format('DD/MM/YYYY');
            dienBienItem.gplx_het_han = moment(data.ngayHetHanGPLX).format('DD/MM/YYYY');
          }
        });
        if (prevScreen === SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY) {
          let routes = navigation.getState().routes;
          NavigationUtil.updateParams(routes[routes.length - 2].key, {profileData: profileDataUpdate});
        }
        NavigationUtil.pop();
      } catch (error) {
        setOnSubmiting(false);
        Alert.alert('Thông báo', error.message);
      }
      return;
    }
  };
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const onPressDateConfirm = (date, setToggleDateTime, setDate, type) => {
    setToggleDateTime(false);
    setDate(date);
  };
  const onChangeVuTonThat = (title, items, value) => {
    const dienBien = profileData.dien_bien;
    dienBien.forEach((dienBienItem) => {
      if (dienBienItem.vu_tt === value) {
        setValue('hoTen', dienBienItem.ten_lxe);
        setValue('dienThoai', dienBienItem.dthoai_lxe);
        setValue('email', dienBienItem.email);
        setValue('soGPLX', dienBienItem.gplx_so);
        setValue('hangGPLX', dienBienItem.gplx_hang ? dienBienItem.gplx_hang.split(',') : ['B2']);
        setValue('ngayCapGPLX', dienBienItem.gplx_hieu_luc ? new Date(moment(dienBien[0].gplx_hieu_luc, 'DD/MM/YYYY')) : new Date());
        setValue('ngayHetHanGPLX', dienBienItem.gplx_het_han ? new Date(moment(dienBien[0].gplx_het_han, 'DD/MM/YYYY')) : new Date());
        setValue('soDangKiem', dienBienItem.soDangKiem);
        setValue('ngayCapDK', dienBienItem.dangkiem_hieu_luc ? new Date(moment(dienBien[0].dangkiem_hieu_luc, 'DD/MM/YYYY')) : new Date());
        setValue('ngayHetHanDK', dienBienItem.dangkiem_het_han ? new Date(moment(dienBien[0].dangkiem_het_han, 'DD/MM/YYYY')) : new Date());
      }
    });
  };
  // RENDER
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );
  const renderInputBangLai = () => (
    <View>
      <View style={{flexDirection: 'row'}}>
        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="hoTen"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView, {marginRight: spacing.small}]}
              title="Họ tên lái xe"
              placeholder="Họ tên"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.hoTen && getErrMessage('hoTen', errors.hoTen.type)}
            />
          )}
        />

        <Controller
          control={control}
          rules={{
            pattern: REGUlAR_EXPRESSION.REG_PHONE,
          }}
          name="dienThoai"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={styles.inputView}
              title="Điện thoại"
              placeholder="Điện thoại"
              value={value}
              onChangeText={onChange}
              keyboardType="phone-pad"
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.dienThoai && getErrMessage('dienThoai', errors.dienThoai.type)}
            />
          )}
        />
      </View>

      <Controller
        control={control}
        rules={{
          pattern: REGUlAR_EXPRESSION.REG_EMAIL,
        }}
        name="email"
        render={({field: {onChange, value}}) => (
          <TextInputOutlined
            containerStyle={styles.inputView}
            title="Email"
            placeholder="Email"
            value={value}
            onChangeText={onChange}
            keyboardType="email-address"
            returnKeyType={'next'}
            blurOnSubmit={false}
            error={errors.email && getErrMessage('email', errors.email.type)}
          />
        )}
      />

      <Controller
        control={control}
        name="hangGPLX"
        render={({field: {value}}) => (
          <DropdownPicker
            title="Hạng giấy phép lái xe"
            zIndex={9000}
            items={HANG_GPLXM}
            itemSelected={value}
            placeholder="Chọn hạng giấy phép lái xe"
            isRequired={true}
            searchable={false}
            isOpen={openHangGPLX}
            setOpen={setOpenHangGPLX}
            setItemSelected={setHangGPLX}
            multiple={true}
            min={1}
            max={10}
            multipleText={value.join(', ')}
            maxHeight={150}
          />
        )}
      />

      <Controller
        control={control}
        name="soGPLX"
        rules={{
          required: true,
        }}
        render={({field: {onChange, onBlur, value}}) => (
          <TextInputOutlined
            containerStyle={styles.inputView}
            title="Số giấy phép lái xe"
            placeholder="Giấy phép lái xe"
            value={value ? value.toUpperCase() : value}
            onChangeText={onChange}
            returnKeyType={'next'}
            blurOnSubmit={false}
            isRequired
            error={errors.soGPLX && getErrMessage('soGPLX', errors.soGPLX.type)}
            onBlur={() => value !== '' && onChange(onChangeAlias(value, {xoaKyTuDacBiet: false}))}
            autoCapitalize={'characters'}
          />
        )}
      />

      <View flexDirection="row">
        <Controller
          control={control}
          name="ngayCapGPLX"
          rules={{
            required: true,
          }}
          render={({field: {value, onChange}}) => (
            <View flex={1} marginRight={10}>
              <TextInputOutlined
                title="Ngày cấp GPLX"
                isTouchableOpacity={true}
                onPress={() => setToggleNgayCapGPLX(true)}
                value={value ? moment(value).format('DD/MM/YYYY') : ''}
                editable={false}
                isRequired={true}
                isDateTimeField
                placeholder="Ngày cấp"
                inputStyle={{color: colors.BLACK}}
              />
              {renderDateTimeComp(toggleNgayCapGPLX, setToggleNgayCapGPLX, (newValue) => onChange(newValue), moment(value).isValid() ? value : new Date())}
            </View>
          )}
        />

        <Controller
          control={control}
          name="ngayHetHanGPLX"
          rules={{
            required: false,
          }}
          render={({field: {value, onChange}}) => (
            <View flex={1}>
              <TextInputOutlined
                title="Ngày hết hạn GPLX"
                isTouchableOpacity={true}
                onPress={() => setToggleNgayHetHanGPLX(true)}
                value={value ? moment(value).format('DD/MM/YYYY') : ''}
                editable={false}
                isDateTimeField
                placeholder="Ngày hết hạn"
                inputStyle={{color: colors.BLACK}}
              />
              {renderDateTimeComp(toggleNgayHetHanGPLX, setToggleNgayHetHanGPLX, (newValue) => onChange(newValue), moment(value).isValid() ? value : new Date())}
            </View>
          )}
        />
      </View>
    </View>
  );
  return (
    <ScreenComponent
      headerBack
      headerTitle="Bổ sung thông tin xe máy"
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
            <View>
              <Controller
                control={control}
                name="vuTonThat"
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title="Vụ tổn thất"
                    placeholder="Chọn vụ tổn thất"
                    zIndex={10000}
                    searchable={false}
                    isOpen={openVuTT}
                    setOpen={setOpenVuTT}
                    items={listVuTonThat}
                    onChangeValue={onChangeVuTonThat}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    containerStyle={{marginTop: 0}}
                  />
                )}
              />
              {/* {prevScreen === SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY && (
                <Controller
                  control={control}
                  name="doiTuong"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title="Đối tượng"
                      placeholder="Chọn đối tượng"
                      zIndex={9000}
                      searchable={false}
                      isOpen={openDoiTuong}
                      setOpen={setOpenDoiTuong}
                      items={getListDoiTuong()}
                      itemSelected={value}
                      isRequired
                      schema={{
                        label: 'ten_doi_tuong',
                        value: 'so_id_doi_tuong',
                      }}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      dropdownStyle={{marginBottom: spacing.tiny}}
                      inputErr={errors.doiTuong && getErrMessage('doiTuong', errors.doiTuong.type)}
                    />
                  )}
                />
              )} */}

              {/* INPUT BẰNG LÁI */}
              {thongTinGiayTo.nhom_hang_muc === 'BANG_LAI' && renderInputBangLai()}
            </View>
          </KeyboardAwareScrollView>
          <View style={styles.btnView}>
            <ButtonLinear loading={onSubmiting} disabled={onSubmiting} title="Cập nhật" onPress={handleSubmit(onSubmit)} linearStyle={{marginHorizontal: spacing.small}} />
          </View>
        </SafeAreaView>
      }
    />
  );
};

export const BoSungThongTinXMScreen = memo(BoSungThongTinXMScreenComponent, isEqual);
