import {StyleSheet} from 'react-native';
import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    marginHorizontal: scale(spacing.small),
  },
  selectedView: {
    borderRadius: 10,
    backgroundColor: colors.GRAY,
  },
  btnSelectedView: {
    padding: 5,
  },
  mapView: {
    flex: 1,
  },
  markerLabelView: {
    width: 100,
  },
  trafficView: {
    position: 'absolute',

    backgroundColor: colors.WHITE,
    borderRadius: 10,
  },
  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },

  linearBtnView: {
    marginHorizontal: 5,
    borderRadius: 30,
    backgroundColor: colors.WHITE,
    marginBottom: 20,
  },
  btnReceivingRecords: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    minHeight: 50,
    borderRadius: 10,
  },
  txtBtnReceivingRecords: {
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  txtHeaderInput: {
    marginBottom: 8,
    fontWeight: 'bold',
  },
  textInputType: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: colors.GRAY,
    paddingHorizontal: 15,
    color: colors.BLACK,
    backgroundColor: colors.WHITE,
    marginTop: 10,
    flexDirection: 'row',
  },
  textInputView: {
    marginBottom: 10,
  },

  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: dimensions.width - 20,
    marginVertical: 20,
    // marginBottom: 10,
  },
  iconBtnTopLeftView: {
    marginRight: 15,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    alignSelf: 'center',
  },

  itemYKienView: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  dropDownTitle: {
    marginTop: 5,
    fontWeight: 'bold',
  },
  dropDownView: {
    marginVertical: 10,
  },
  footerView: {
    flex: 1,
    flexDirection: 'row',
  },
  bottomBtn: {
    marginHorizontal: scale(spacing.small),
  },
  checkboxView: {
    marginHorizontal: spacing.tiny,
    flexDirection: 'row',
    alignItems: 'center',
    width: dimensions.width / 2,
  },
  inputTitle: {
    fontWeight: 'bold',
  },
});
