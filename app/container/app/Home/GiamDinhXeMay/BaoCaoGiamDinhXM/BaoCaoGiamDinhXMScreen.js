import {DATA_CONSTANT, MA_MAU_IN, SCREEN_ROUTER_APP, isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, DropdownPicker, ScreenComponent, Text, TextInputOutlined} from '@component';
import lodash from 'lodash';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, ScrollView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import RadioGroup from 'react-native-radio-buttons-group';
import styles from './BaoCaoGiamDinhXMStyles';

const BaoCaoGiamDinhXMScreenComponent = ({route}) => {
  console.log('BaoCaoGiamDinhXMScreenComponent');
  const {profileData} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [inputErr, setInputErr] = useState([]);
  const [dataFormBaoCaoGiamDinh, setDataFormBaoCaoGiamDinh] = useState([]);
  const [baoCaoGiamDinhData, setBaoCaoGiamDinhData] = useState(null);
  const [countReloadBaoCaoGiamDinh, setCountReloadBaoCaoGiamDinh] = useState(0);

  const [openChonGara, setOpenChonGara] = useState(false);
  const [garaSelected, setGaraSelected] = useState(null);
  const [garaData, setGaraData] = useState([]);

  useEffect(() => {
    initDataDong();
  }, []);

  useEffect(() => {
    dienDataVaoForm();
  }, [dataFormBaoCaoGiamDinh, baoCaoGiamDinhData]);

  const initDataDong = async () => {
    await getBaoCaoGiamDinhData();
    await getDataFormBaoCaoGiamDinh();
    getGaraData();
  };

  //lấy dữ liệu để hiển thị form đánh giá hiện trường động
  const getDataFormBaoCaoGiamDinh = async () => {
    let paramsFormDanhGia = {};
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GET_FORM_BAO_CAO_GIAM_DINH_DONG, paramsFormDanhGia);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let formDanhGiaHienTruongInput = response.data_info.loai;
      let formDanhGiaHienTruongGiaTri = response.data_info.lke;
      formDanhGiaHienTruongInput.map((item) => {
        if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXTAREA || item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.NUMBER) {
          item.value = ''; //giá trị input thay đổi
          item.multiline = true;
          item.numberOfLines = 2;
        } else if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.RADIO) {
          item.radioData = [];
          //filter lấy giá trị theo loai
          let giaTriRadio = formDanhGiaHienTruongGiaTri.filter((itemGiaTri) => itemGiaTri.loai === item.loai);
          //convert dữ liệu để hiển thị radio
          giaTriRadio.map((itemRadio) => {
            itemRadio.id = itemRadio.dg_stt;
            itemRadio.label = itemRadio.ten;
            itemRadio.value = itemRadio.ma;
            itemRadio.containerStyle = {
              marginRight: 0,
            };
            itemRadio.selected = false;
            itemRadio.size = 18;
            return itemRadio;
          });
          //nếu radio chưa được selectec giá trị và radio đấy bắt buộc nhập -> chọn 1 giá trị mặc định cho nó
          if (item.bat_buoc_nhap === 1) giaTriRadio[0].selected = true;
          item.radioData = giaTriRadio;
        } else if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.CHECKBOX) {
          item.checkBoxData = [];
          //filter lấy giá trị theo loai
          let checkBoxData = formDanhGiaHienTruongGiaTri.filter((itemGiaTri) => itemGiaTri.loai === item.loai);
          //convert dữ liệu để hiển thị checkbox
          checkBoxData.map((itemCheckbox) => {
            itemCheckbox.checked = false;
            return itemCheckbox;
          });
          item.checkBoxData = checkBoxData;
        }
      });
      setDataFormBaoCaoGiamDinh(formDanhGiaHienTruongInput);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  //sau khi có dữ liệu đánh giá hiện trường - thì điền data vào form đánh giá
  const dienDataVaoForm = () => {
    if (dataFormBaoCaoGiamDinh.length > 0 && baoCaoGiamDinhData.length > 0 && countReloadBaoCaoGiamDinh === 0) {
      let dataFormBaoCaoGiamDinhTmp = dataFormBaoCaoGiamDinh;
      dataFormBaoCaoGiamDinhTmp.map((itemForm) => {
        for (let i = 0; i < baoCaoGiamDinhData.length; i++) {
          if (itemForm.loai === baoCaoGiamDinhData[i].loai) {
            if (
              itemForm.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXTAREA ||
              itemForm.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXT ||
              itemForm.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.NUMBER
            ) {
              itemForm.value = baoCaoGiamDinhData[i].noi_dung;
              break;
            } else if (itemForm.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.RADIO) {
              itemForm.radioData.map((itemRadio) => {
                if (itemRadio.ma === baoCaoGiamDinhData[i].noi_dung) itemRadio.selected = true;
                else if (itemForm.bat_buoc_nhap === 1) itemRadio.selected = false;
                return itemRadio;
              });
              break;
            } else if (itemForm.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.CHECKBOX) {
              let giaTriCheckbox = baoCaoGiamDinhData[i].noi_dung.split(',');
              itemForm.checkBoxData.map((itemCheckbox) => {
                for (let j = 0; j < giaTriCheckbox.length; j++) if (itemCheckbox.ma === giaTriCheckbox[j]) itemCheckbox.checked = true;
                return itemCheckbox;
              });
              break;
            } else if (itemForm.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.INPUT_DISABLED) {
              if (itemForm.loai === 'UOC_TON_THAT' || itemForm.loai === 'GARA_CHINH_HANG' || itemForm.loai === 'GARA_HOP_TAC') itemForm.value = baoCaoGiamDinhData[i].noi_dung;
              else if (itemForm.loai === 'GARA_DE_XUAT') setGaraSelected(baoCaoGiamDinhData[i].noi_dung);
              break;
            }
          }
        }
        return itemForm;
      });
      setInputErr(new Array(dataFormBaoCaoGiamDinhTmp.length).fill(''));
      setDataFormBaoCaoGiamDinh([...dataFormBaoCaoGiamDinhTmp]);
      setCountReloadBaoCaoGiamDinh(1);
    }
  };
  //GARA ĐỀ XUẤT THAY ĐỔI
  const onChangeGara = (title, items, garaSelected) => {
    garaData.map((gara) => {
      if (gara.ma === garaSelected) {
        setDataFormBaoCaoGiamDinh((prevValue) => {
          let newValue = lodash.cloneDeep(prevValue);
          newValue.map((itemFormBCGD) => {
            if (itemFormBCGD.loai === 'GARA_CHINH_HANG') itemFormBCGD.value = gara.chinh_hang;
            else if (itemFormBCGD.loai === 'GARA_HOP_TAC') itemFormBCGD.value = gara.hop_tac;
          });
          return [...newValue];
        });
        return;
      }
    });
  };
  //lấy dữ liệu gara
  const getGaraData = async (searchInput) => {
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LIST_GARA, {ten: searchInput, nv: profileData.ho_so.nghiep_vu});
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      response.data_info = response.data_info.map((item) => {
        item.label = item.ten || item.ten_tat || '';
        item.value = item.ma;
        return item;
      });
      setGaraData(__DEV__ ? response.data_info.slice(0, 10) : response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  //lấy dữ liệu báo cáo giám định
  const getBaoCaoGiamDinhData = async () => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      so_id: profileData.ho_so.so_id,
      nguon: 'MOBILE',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_THONG_TIN_BAO_CAO_GIAM_DINH_DONG_XE_MAY, params);
      console.log('response', response);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setBaoCaoGiamDinhData(response.data_info.bcgd_ct);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  //nút lưu
  const onPressLuu = async () => {
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        nguon: 'MOBILE',
        data_loai: [],
        data_ten_loai: [],
        data_noi_dung: [],
      };
      let haveErr = false;
      let inputErrTmp = inputErr;
      dataFormBaoCaoGiamDinh.map((item, index) => {
        if (
          item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXTAREA ||
          item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.NUMBER ||
          item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXT
        ) {
          if (item.bat_buoc_nhap === 1 && (!item.value || !item.value?.trim())) {
            inputErrTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          } else if (item.value) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            let value = '';
            if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXTAREA || item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXT) value = item.value;
            else if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.NUMBER) {
              if (item.value.includes(',')) value = +item.value.split(',').join('');
              else value = +item.value;
            }
            params.data_noi_dung.push(value);
          }
        } else if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.RADIO) {
          item.radioData.map((itemRadio) => {
            if (itemRadio.selected) {
              params.data_noi_dung.push(itemRadio.ma);
              params.data_loai.push(item.loai);
              params.data_ten_loai.push(item.ten_loai);
            }
          });
        } else if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.CHECKBOX) {
          let coChecked = false,
            data_noi_dung = '';
          item.checkBoxData.map((itemCheckbox) => {
            if (itemCheckbox.checked) {
              coChecked = true;
              data_noi_dung = data_noi_dung + itemCheckbox.ma + ',';
            }
          });
          if (coChecked) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            params.data_noi_dung.push(data_noi_dung);
          }
          if (!coChecked && item.bat_buoc_nhap) {
            inputErrTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          }
        } else if (item.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.INPUT_DISABLED) {
          params.data_loai.push(item.loai);
          params.data_ten_loai.push(item.ten_loai);
          params.data_noi_dung.push(item.loai === 'GARA_DE_XUAT' ? garaSelected : item.value);
        }
      });
      if (haveErr) {
        setInputErr([...inputErrTmp]);
        return;
      }
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LUU_THONG_TIN_BAO_CAO_GIAM_DINH_DONG_XE_MAY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu báo cáo giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  // NÚT TRÌNH
  const onPressTrinh = () => {
    NavigationUtil.push(SCREEN_ROUTER_APP.TRINH_PHUONG_AN_XM, {
      profileInfo: profileData.ho_so,
      maMauIn: MA_MAU_IN.ESCS_BCGD_XE_MAY,
      loaiTrinh: 'XE_MAY_TRINH_DUYET_BAO_CAO_GD',
      action: '',
      screenTitle: 'Trình báo cáo giám định',
    });
  };
  const onPressHeader = (title) => {
    if (title !== 'Nội dung công việc đã thực hiện') return;
    NavigationUtil.push(SCREEN_ROUTER_APP.NOI_DUNG_CONG_VIEC_XE_MAY, {profileData: profileData});
  };
  const onChangeTxtInput = (index, value) => {
    let dataFormBaoCaoGiamDinhTmp = dataFormBaoCaoGiamDinh;
    let inputErrTmp = inputErr;
    if (!value.trim() && dataFormBaoCaoGiamDinhTmp[index].bat_buoc_nhap === 1) inputErrTmp[index] = 'Thông tin bắt buộc';
    else if (value.trim()) inputErrTmp[index] = '';
    setInputErr([...inputErrTmp]);
    dataFormBaoCaoGiamDinhTmp[index].value = value;
    setDataFormBaoCaoGiamDinh([...dataFormBaoCaoGiamDinhTmp]);
  };
  /**RENDER  */

  // const renderTitle = (title, iconName) => {
  //   return (
  //     <TouchableOpacity style={styles.inforHeaderView} onPress={() => onPressHeader(title)}>
  //       <View activeOpacity={1} style={[styles.headerCollap]}>
  //         <View style={{flexDirection: 'row'}}>
  //           <Icon.FontAwesome name={iconName} size={15} style={styles.iconBtnTopLeftView} />
  //           <Text children={title} style={{fontWeight: 'bold'}} />
  //         </View>
  //       </View>
  //       {title === 'Nội dung công việc đã thực hiện' && <Icon.FontAwesome name="chevron-right" size={15} style={styles.iconBtnTopLeftView} />}
  //     </TouchableOpacity>
  //   );
  // };
  // const renderGaraCheckboxItem = (data, extraData) => {
  //   let item = data.item;
  //   let garaCheckBoxData = extraData.title === headerTitle[8] ? garaChinhHangCheckbox : garaHopTacCheckbox;
  //   let setGaraCheckBoxData = extraData.title === headerTitle[8] ? setGaraChinhHangCheckbox : setGaraHopTacCheckbox;
  //   return (
  //     <TouchableOpacity
  //       style={styles.itemYKienView}
  //       onPress={() => {
  //         let garaCheckBoxDataTmp = garaCheckBoxData;
  //         garaCheckBoxDataTmp[data.index] = true;
  //         if (data.index === 0) garaCheckBoxDataTmp = [true, false];
  //         else garaCheckBoxDataTmp = [false, true];
  //         setGaraCheckBoxData([...garaCheckBoxDataTmp]);
  //       }}>
  //       <CheckboxComp
  //         disabled={true}
  //         value={garaCheckBoxData[data.index]}
  //         // onValueChange={(value) => {
  //         //   let garaCheckBoxDataTmp = garaCheckBoxData;
  //         //   garaCheckBoxDataTmp[data.index] = value;
  //         //   if (data.index === 0) garaCheckBoxDataTmp = [value, false];
  //         //   else garaCheckBoxDataTmp = [false, value];
  //         //   setGaraCheckBoxData([...garaCheckBoxDataTmp]);
  //         // }}
  //         checkboxStyle={{marginRight: spacing.smaller}}
  //       />
  //       <View>
  //         <Text children={item.label} />
  //       </View>
  //     </TouchableOpacity>
  //   );
  // };
  // const renderGaraOptions = (title, garaOptionData) => {
  //   return (
  //     <View>
  //       <Text children={title} style={styles.dropDownTitle} />
  //       <FlatList data={garaOptionData} keyExtractor={(item) => item.id} renderItem={(item) => renderGaraCheckboxItem(item, {title: title})} numColumns={2} />
  //     </View>
  //   );
  // };
  const renderBottomView = () => {
    const showBtnTrinh = baoCaoGiamDinhData && baoCaoGiamDinhData.nsd_xly === baoCaoGiamDinhData.nsd_bcgd ? true : false;
    return (
      <View style={styles.footerView}>
        {showBtnTrinh && <ButtonLinear title="Trình" onPress={onPressTrinh} />}
        <ButtonLinear title="Lưu" onPress={onPressLuu} linearStyle={{marginLeft: spacing.small}} />
      </View>
    );
  };

  const renderRadioInput = (title, radioButtons, onPressRadioButton, isRequired) => {
    let radioButtonsTmp = cloneObject(radioButtons);
    if (radioButtons.length === 2) radioButtonsTmp.map((item) => (item.containerStyle.flex = 1));
    return (
      <View style={{marginVertical: spacing.tiny}}>
        <Text style={styles.inputTitle}>
          {title} {isRequired && <Text children="(*)" style={{color: colors.RED1}} />}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <RadioGroup radioButtons={radioButtonsTmp} onPress={onPressRadioButton} layout={'row'} />
        </ScrollView>
      </View>
    );
  };
  const renderItemCheckbox = (item, extraData) => {
    return (
      <TouchableOpacity style={styles.checkboxView} onPress={() => extraData.onChangeValue(!item.checked)}>
        <CheckboxComp value={item.checked} disabled checkboxStyle={{marginRight: spacing.smaller}} />
        <Text children={item.ten} />
      </TouchableOpacity>
    );
  };
  const renderItemFormDanhGia = (data) => {
    let itemDanhGia = data.item;
    let dataFormBaoCaoGiamDinhTmp = dataFormBaoCaoGiamDinh;
    if (
      itemDanhGia.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXTAREA ||
      itemDanhGia.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.NUMBER ||
      itemDanhGia.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.TEXT
    )
      return (
        <TextInputOutlined
          title={itemDanhGia.ten_loai}
          value={itemDanhGia.value}
          placeholder={itemDanhGia.ten_loai}
          keyboardType={itemDanhGia.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.NUMBER ? 'numeric' : ''}
          multiline={itemDanhGia.multiline}
          numberOfLines={itemDanhGia.numberOfLines}
          error={inputErr[data.index]}
          isRequired={itemDanhGia.bat_buoc_nhap === 1 ? true : false}
          onChangeText={(value) => onChangeTxtInput(data.index, value)}
        />
      );
    else if (itemDanhGia.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.RADIO) {
      return renderRadioInput(
        itemDanhGia.ten_loai,
        itemDanhGia.radioData,
        (value) => {
          itemDanhGia.radioData = value;
          dataFormBaoCaoGiamDinhTmp[data.index] = itemDanhGia;
          setDataFormBaoCaoGiamDinh([...dataFormBaoCaoGiamDinhTmp]);
        },
        itemDanhGia.bat_buoc_nhap === 0 ? false : true,
      );
    } else if (itemDanhGia.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.CHECKBOX) {
      return (
        <View style={{marginBottom: spacing.small}}>
          <Text style={[styles.inputTitle, {marginBottom: spacing.tiny}]}>
            {itemDanhGia.ten_loai} {itemDanhGia.bat_buoc_nhap === 1 ? <Text children="(*)" style={{color: colors.RED1}} /> : ''}
          </Text>
          <FlatList
            data={itemDanhGia.checkBoxData}
            keyExtractor={(itemCheckbox) => itemCheckbox.ma}
            renderItem={({item, index}) =>
              renderItemCheckbox(item, {
                onChangeValue: (value) => {
                  //BẮT BUỘC PHẢI CÓ 1 Ô CHECKED
                  dataFormBaoCaoGiamDinhTmp[data.index].checkBoxData.map((itemCheckbox, indexItemCheckbox) => {
                    if (indexItemCheckbox === index && !itemCheckbox.checked) itemCheckbox.checked = value;
                    else if (indexItemCheckbox !== index) itemCheckbox.checked = false;
                    return itemCheckbox;
                  });
                  setDataFormBaoCaoGiamDinh([...dataFormBaoCaoGiamDinhTmp]);
                  if (itemDanhGia.bat_buoc_nhap && value) {
                    let inputErrTmp = inputErr;
                    inputErrTmp[data.index] = '';
                    setInputErr([...inputErrTmp]);
                  }
                },
              })
            }
            horizontal
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
          />
          {inputErr[data.index] !== '' && <Text children={inputErr[data.index]} style={{color: colors.RED1}} />}
        </View>
      );
    } else if (itemDanhGia.kieu === DATA_CONSTANT.FORM_BAO_CAO_GIAM_DINH_KIEU_INPUT.INPUT_DISABLED) {
      if (itemDanhGia.loai === 'UOC_TON_THAT' || itemDanhGia.loai === 'GARA_HOP_TAC' || itemDanhGia.loai === 'GARA_CHINH_HANG') {
        let value = null;
        if (itemDanhGia.loai === 'UOC_TON_THAT') value = itemDanhGia.value ? itemDanhGia.value : 0;
        else {
          value = 'Chưa xác định';
          if (itemDanhGia.loai === 'GARA_HOP_TAC') {
            if (itemDanhGia.value === 'C') value = 'Hợp tác';
            else if (itemDanhGia.value === 'K') value = 'Không hợp tác';
          } else if (itemDanhGia.loai === 'GARA_CHINH_HANG') {
            if (itemDanhGia.value === 'C') value = 'Chính hãng';
            else if (itemDanhGia.value === 'K') value = 'Không chính hãng';
          }
        }
        return (
          <View style={{zIndex: -1}}>
            <TextInputOutlined
              title={itemDanhGia.ten_loai}
              value={value}
              placeholder={itemDanhGia.ten_loai}
              keyboardType={itemDanhGia.loai === 'UOC_TON_THAT' ? 'numeric' : ''}
              multiline={false}
              numberOfLines={1}
              error={inputErr[data.index]}
              isRequired={itemDanhGia.bat_buoc_nhap === 1 ? true : false}
              onChangeText={(value) => onChangeTxtInput(data.index, value)}
              // disabled={true}
              // editable={false}
            />
          </View>
        );
      } else if (itemDanhGia.loai === 'GARA_DE_XUAT') {
        return (
          <DropdownPicker
            title={itemDanhGia.ten_loai}
            zIndex={9000}
            isOpen={openChonGara}
            setOpen={setOpenChonGara}
            items={garaData}
            itemSelected={garaSelected}
            setItemSelected={setGaraSelected}
            onChangeValue={onChangeGara}
            placeholder="Chọn gara"
            containerStyle={{marginBottom: openChonGara ? 200 : spacing.small}}
            returnKeyType={'next'}
            maxHeight={200}
          />
        );
      }
    }
  };
  const renderFormDanhGiaHienTruong = () => {
    return (
      <FlatList
        data={dataFormBaoCaoGiamDinh}
        key={(item) => item.loai_stt + ''}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItemFormDanhGia}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Lập báo cáo giám định xe máy"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false} scrollEnabled={isIOS ? true : !openChonGara}>
            {dataFormBaoCaoGiamDinh.length > 0 && <View style={styles.contentView}>{renderFormDanhGiaHienTruong()}</View>}
          </KeyboardAwareScrollView>
        </View>
      }
      footer={renderBottomView()}
    />
  );
};

export const BaoCaoGiamDinhXMScreen = memo(BaoCaoGiamDinhXMScreenComponent, isEqual);
