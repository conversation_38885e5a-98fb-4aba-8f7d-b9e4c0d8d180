import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, ScreenComponent, TextInputOutlined, Text} from '@component';
import {DATA_CONSTANT} from '@constant';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, SafeAreaView, ScrollView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import RadioGroup from 'react-native-radio-buttons-group';
import styles from './DanhGiaHienTruongXMStyles';
import {ModalKetThucDanhGia} from './Modal/ModalKetThucDanhGia';

const DanhGiaHienTruongScreenXMComponent = (props) => {
  console.log('DanhGiaHienTruongScreenXMComponent');
  const {route} = props;
  const {prevScreen} = route.params;
  const [profileData, setProfileData] = useState(route.params.profileData);
  const [inputErr, setInputErr] = useState([]);
  const [dataFormDanhGiaHienTruong, setDataFormDanhGiaHienTruong] = useState([]);
  const [danhGiaHienTruong, setDanhGiaHienTruong] = useState(route.params.danhGiaHienTruong);

  const [isSubmiting, setIsSubmiting] = useState(false);

  //dữ liệu của modal thông báo thành công

  let refModalKetThucDanhGia = useRef(null);

  useEffect(() => {
    setProfileData(route.params.profileData);
    getDataFormDanhGiaHienTruong();
  }, [route.params]);

  //sau khi có dữ liệu đánh giá hiện trường - thì điền data vào form đánh giá
  const dienDataVaoForm = (dataFormDanhGiaHienTruong) => {
    if (dataFormDanhGiaHienTruong.length > 0 && danhGiaHienTruong.length > 0) {
      let dataFormDanhGiaHienTruongTmp = dataFormDanhGiaHienTruong;
      dataFormDanhGiaHienTruongTmp.map((itemForm) => {
        for (let i = 0; i < danhGiaHienTruong.length; i++) {
          if (itemForm.loai == danhGiaHienTruong[i].loai) {
            if (itemForm.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || itemForm.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
              itemForm.value = danhGiaHienTruong[i].noi_dung;
              break;
            } else if (itemForm.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
              itemForm.radioData.map((itemRadio) => {
                if (itemRadio.ma == danhGiaHienTruong[i].noi_dung) itemRadio.selected = true;
                else if (itemForm.bat_buoc_nhap == 1) itemRadio.selected = false;
                return itemRadio;
              });
              break;
            } else if (itemForm.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
              let giaTriCheckbox = danhGiaHienTruong[i].noi_dung.split(',');
              itemForm.checkBoxData.map((itemCheckbox) => {
                for (let j = 0; j < giaTriCheckbox.length; j++) if (itemCheckbox.ma == giaTriCheckbox[j]) itemCheckbox.checked = true;
                return itemCheckbox;
              });
              break;
            }
          }
        }
        return itemForm;
      });
      setInputErr(new Array(dataFormDanhGiaHienTruongTmp.length).fill(''));
      setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
    }
  };

  const onPressHoanThanhGiamDinhHienTruong = async () => {
    try {
      let params = {
        so_id: profileData.ho_so.so_id,
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        create_file: 'ESCS_BBGD_HIEN_TRUONG_XE_MAY',
        remove_file: 'ESCS_BBGD_HIEN_TRUONG_XE_MAY',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.KET_THUC_CHUP_ANH_DANH_GIA_HIEN_TRUONG_XM, params);
      refModalKetThucDanhGia.current.hide();
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      refModalKetThucDanhGia.current.hide();
      FlashMessageHelper.showFlashMessage('Thông báo', 'Nộp đánh giá hiện trường thành công', 'success');
      setTimeout(() => (prevScreen ? NavigationUtil.pop(2) : NavigationUtil.pop()), 250);
    } catch (error) {
      refModalKetThucDanhGia.current.hide();
      Alert.alert('Thông báo', error.message);
    }
  };

  //lấy dữ liệu để hiển thị form đánh giá hiện trường động
  const getDataFormDanhGiaHienTruong = async () => {
    try {
      let paramsFormDanhGia = {
        ma_doi_tac: route.params.profileData.ho_so.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.GET_FORM_DANH_GIA_HIEN_TRUONG, paramsFormDanhGia);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let formDanhGiaHienTruongInput = response.data_info.loai;
      let formDanhGiaHienTruongGiaTri = response.data_info.lke;
      formDanhGiaHienTruongInput.map((item) => {
        if (item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
          item.value = ''; //giá trị input thay đổi
          item.multiline = true;
          item.numberOfLines = 2;
        } else if (item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
          item.radioData = [];
          //filter lấy giá trị theo loai
          let giaTriRadio = formDanhGiaHienTruongGiaTri.filter((itemGiaTri) => itemGiaTri.loai == item.loai);
          //convert dữ liệu để hiển thị radio
          giaTriRadio.map((itemRadio) => {
            itemRadio.id = itemRadio.dg_stt;
            itemRadio.label = itemRadio.ten;
            itemRadio.value = itemRadio.ma;
            itemRadio.containerStyle = {
              marginRight: 0,
            };
            itemRadio.selected = false;
            itemRadio.size = 18;
            return itemRadio;
          });
          //nếu radio chưa được selectec giá trị và radio đấy bắt buộc nhập -> chọn 1 giá trị mặc định cho nó
          if (item.bat_buoc_nhap == 1) giaTriRadio[0].selected = true;
          item.radioData = giaTriRadio;
        } else if (item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
          item.checkBoxData = [];
          //filter lấy giá trị theo loai
          let checkBoxData = formDanhGiaHienTruongGiaTri.filter((itemGiaTri) => itemGiaTri.loai == item.loai);
          //convert dữ liệu để hiển thị checkbox
          checkBoxData.map((itemCheckbox) => {
            itemCheckbox.checked = false;
            return itemCheckbox;
          });
          item.checkBoxData = checkBoxData;
        }
      });
      setDataFormDanhGiaHienTruong(formDanhGiaHienTruongInput);
      dienDataVaoForm(formDanhGiaHienTruongInput);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //đánh giá hiện trường
  const onPressSave = async () => {
    try {
      setIsSubmiting(true);
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        data_loai: [],
        data_ten_loai: [],
        data_noi_dung: [],
      };
      let haveErr = false;
      let inputErrTmp = inputErr;
      dataFormDanhGiaHienTruong.map((item, index) => {
        if (item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
          if (item.bat_buoc_nhap == 1 && (!item.value || !item.value?.trim())) {
            inputErrTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          } else if (item.value) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            let value = '';
            if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA) value = item.value;
            else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
              if (item.value.includes(',')) value = +item.value.split(',').join('');
              else value = +item.value;
            }
            params.data_noi_dung.push(value);
          }
        } else if (item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
          item.radioData.map((itemRadio) => {
            if (itemRadio.selected) {
              params.data_noi_dung.push(itemRadio.ma);
              params.data_loai.push(item.loai);
              params.data_ten_loai.push(item.ten_loai);
            }
          });
        } else if (item.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
          let coChecked = false,
            data_noi_dung = '';
          item.checkBoxData.map((itemCheckbox) => {
            if (itemCheckbox.checked) {
              coChecked = true;
              data_noi_dung = data_noi_dung + itemCheckbox.ma + ',';
            }
          });
          if (coChecked) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            params.data_noi_dung.push(data_noi_dung);
          }
          if (!coChecked && item.bat_buoc_nhap) {
            inputErrTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          }
        }
      });
      if (haveErr) {
        setInputErr([...inputErrTmp]);
        return;
      }
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.DANH_GIA_HIEN_TRUONG_XM, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', danhGiaHienTruong ? 'Cập nhật thành công' : 'Đánh giá hiện trường thành công', 'success');
      refModalKetThucDanhGia.current.show();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeTxtInput = (index, value) => {
    let dataFormDanhGiaHienTruongTmp = dataFormDanhGiaHienTruong;
    let inputErrTmp = inputErr;
    if (!value.trim() && dataFormDanhGiaHienTruongTmp[index].bat_buoc_nhap == 1) {
      inputErrTmp[index] = 'Thông tin bắt buộc';
    } else if (value.trim()) inputErrTmp[index] = '';
    setInputErr([...inputErrTmp]);
    dataFormDanhGiaHienTruongTmp[index].value = value;
    setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
  };

  /**RENDER  */
  const renderRadioInput = (title, radioButtons, onPressRadioButton, isRequired) => {
    let radioButtonsTmp = cloneObject(radioButtons);
    if (radioButtons.length == 2) radioButtonsTmp.map((item) => (item.containerStyle.flex = 1));
    return (
      <View style={{marginVertical: 5}}>
        <Text style={styles.dropDownTitle}>
          {title} {isRequired && <Text children="(*)" style={{color: colors.RED1}} />}
        </Text>
        <ScrollView horizontal>
          <RadioGroup radioButtons={radioButtonsTmp} onPress={onPressRadioButton} layout={'row'} />
        </ScrollView>
      </View>
    );
  };
  const renderItemCheckbox = (item, extraData) => {
    return (
      <View style={styles.checkboxView}>
        <Text children={item.ten} />
        <CheckboxComp value={item.checked} onValueChange={extraData.onChangeValue} checkboxStyle={{marginLeft: 5}} />
      </View>
    );
  };
  const renderItemFormDanhGia = (data) => {
    let itemDanhGia = data.item;
    // console.log('renderItemFormDanhGia', itemDanhGia);
    let dataFormDanhGiaHienTruongTmp = dataFormDanhGiaHienTruong;
    if (itemDanhGia.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || itemDanhGia.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER)
      return (
        <TextInputOutlined
          title={itemDanhGia.ten_loai}
          value={itemDanhGia.value}
          placeholder={itemDanhGia.ten_loai}
          keyboardType={itemDanhGia.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA ? '' : 'numeric'}
          multiline={itemDanhGia.multiline}
          numberOfLines={itemDanhGia.numberOfLines}
          error={inputErr[data.index]}
          isRequired={itemDanhGia.bat_buoc_nhap == 1 ? true : false}
          onChangeText={(value) => onChangeTxtInput(data.index, value)}
        />
      );
    else if (itemDanhGia.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
      return renderRadioInput(
        itemDanhGia.ten_loai,
        itemDanhGia.radioData,
        (value) => {
          itemDanhGia.radioData = value;
          dataFormDanhGiaHienTruongTmp[data.index] = itemDanhGia;
          setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
        },
        itemDanhGia.bat_buoc_nhap == 0 ? false : true,
      );
    } else if (itemDanhGia.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
      return (
        <View>
          <Text style={styles.dropDownTitle}>
            {itemDanhGia.ten_loai} {itemDanhGia.bat_buoc_nhap && <Text children="(*)" style={{color: colors.RED1}} />}
          </Text>
          <FlatList
            data={itemDanhGia.checkBoxData}
            keyExtractor={(itemCheckbox) => itemCheckbox.ma}
            renderItem={({item, index}) =>
              renderItemCheckbox(item, {
                onChangeValue: (value) => {
                  itemDanhGia.checkBoxData[index].checked = value;
                  dataFormDanhGiaHienTruongTmp[data.index] = itemDanhGia;
                  setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
                  if (itemDanhGia.bat_buoc_nhap && value) {
                    let inputErrTmp = inputErr;
                    inputErrTmp[data.index] = '';
                    setInputErr([...inputErrTmp]);
                  }
                },
              })
            }
            // numColumns={3}
            horizontal
          />
          {inputErr[data.index] != '' && <Text children={inputErr[data.index]} style={{color: colors.RED1}} />}
        </View>
      );
    }
  };
  const renderFormDanhGiaHienTruong = () => {
    return <FlatList data={dataFormDanhGiaHienTruong} key={(item) => item.loai_stt + ''} renderItem={renderItemFormDanhGia} />;
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle="Đánh giá hiện trường xe máy"
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView
            // resetScrollToCoords={{x: 0, y: 0}} //tọa độ này sẽ được xử dụng để reset scroll khi keyboard hide
            showsVerticalScrollIndicator={false}>
            {dataFormDanhGiaHienTruong.length > 0 && <View style={styles.contentView}>{renderFormDanhGiaHienTruong()}</View>}
          </KeyboardAwareScrollView>
          <View style={styles.footerView}>
            <ButtonLinear disabled={isSubmiting} loading={isSubmiting} title="Lưu" onPress={onPressSave} linearStyle={styles.bottomBtn} />
          </View>
          <ModalKetThucDanhGia
            ref={refModalKetThucDanhGia}
            prevScreen={prevScreen}
            onPressHoanThanhGiamDinhHienTruong={onPressHoanThanhGiamDinhHienTruong}
            onBackPress={() => refModalKetThucDanhGia.current.hide()}
          />
        </SafeAreaView>
      }
    />
  );
};

export const DanhGiaHienTruongXMScreen = memo(DanhGiaHienTruongScreenXMComponent, isEqual);
