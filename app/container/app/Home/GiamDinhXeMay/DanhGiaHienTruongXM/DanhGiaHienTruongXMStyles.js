import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 10,
  },
  contentView: {
    flex: 1,
    marginHorizontal: spacing.small,
    marginTop: 20,
  },
  dropDownView: {
    marginVertical: 10,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  inputTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  linearBtnView: {
    flex: 1,
    marginHorizontal: 5,
    borderRadius: 30,
    backgroundColor: colors.WHITE,
    marginBottom: 20,
  },
  btnReceivingRecords: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    minHeight: 50,
    borderRadius: 10,
  },
  txtBtnReceivingRecords: {
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  textInputType: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: colors.GRAY,
    paddingLeft: 15,
    color: colors.BLACK,
    backgroundColor: colors.WHITE,
    paddingRight: 15,
  },
  textInputView: {
    marginBottom: 10,
  },
  checkboxView: {
    marginRight: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    // flex: 1,
    // borderWidth: 1,
  },
  footerView: {
    paddingTop: spacing.small,
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    flexDirection: 'row',
  },
  bottomBtn: {
    marginHorizontal: spacing.small,
  },
});
