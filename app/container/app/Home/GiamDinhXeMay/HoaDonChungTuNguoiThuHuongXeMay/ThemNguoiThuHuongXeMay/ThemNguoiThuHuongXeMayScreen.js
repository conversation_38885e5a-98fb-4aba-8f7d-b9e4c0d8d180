import { colors } from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { FlashMessageHelper } from '@app/utils/FlashMessageHelper';
import { ButtonLinear, DropdownPicker, Empty, ScreenComponent, Text, TextInputOutlined } from '@component';
import React, { memo, useEffect, useRef, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Controller, useForm } from 'react-hook-form';
import { Al<PERSON>, FlatList, SafeAreaView, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { ModalChiNhanh, ModalChonSoHoaDon, ModalNganHang } from '../Components';
import styles from './Styles';
import { dimensions } from '@app/theme';

const ThemNguoiThuHuongXeMayScreenComponent = (props) => {
  console.log('ThemNguoiThuHuongXeMayScreenComponent');
  const { route } = props;
  const { profileData, dataSoHoaDonCT, itemSelected } = route?.params;

  let soTaiKhoanRef = useRef();
  let tenDoiTuongThuHuongRef = useRef();
  let tienTruocThueRef = useRef();
  let tienThueRef = useRef();
  let noiDungRef = useRef();

  let refModalNganHang = useRef(null);
  let refModalChiNhanh = useRef(null);
  let refModalChonSoHoaDon = useRef(null);

  const [dataDvThuHuong, setDataDvThuHuong] = useState([]); //dropdown đơn vị phát hành
  const [openDvThuHuong, setOpenDvThuHuong] = useState(false); //dropdown đơn vị phát hành
  const [openPhuongThucThanhToan, setOpenPhuongThucThanhToan] = useState(false); // dropdown phương thức thanh toán
  const [dataNganHang, setDataNganHang] = useState([]); // tất cả ngân hàng
  const [dataChiNhanhBase, setDataChiNhanhBase] = useState([]); // tất cả chi nhánh ngân hàng
  const [dataChiNhanhTheoNganHang, setDataChiNhanhTheoNganHang] = useState([]); // chi nhánh theo ngân hàng được chọn
  const [listSoHoaDonDuocChon, setListSoHoaDonDuocChon] = useState(['']);
  const [soHoaDon, setSoHoaDon] = useState('');
  const [dialogLoading, setDialogLoading] = useState(false);
  const [openDoiTuongTonThat, setOpenDoiTuongTonThat] = useState(false);
  const [listSoTienThanhToanTheoLHNV, setListSoTienThanhToanTheoLHNV] = useState([]);
  const [openLoaiHinhNghiepVu, setOpenLoaiHinhNghiepVu] = useState(false);

  const getDefaultFormValue = () => {
    let doiTuongTonThat = '';
    if (itemSelected) doiTuongTonThat = itemSelected.so_id_doi_tuong;
    if (profileData?.ds_doi_tuong?.length === 1) doiTuongTonThat = profileData?.ds_doi_tuong[0].so_id_doi_tuong;
    return {
      dvThuHuong: itemSelected?.dvi_th || '',
      tenDoiTuongThuHuong: itemSelected?.ten || '',
      soHoaDon: '',
      phuongThucThanhToan: itemSelected?.pttt || '',
      nganHangThuHuong: itemSelected?.ma_ngan_hang || '',
      chiNhanhNganHangThuHuong: itemSelected?.ma_chi_nhanh || '',
      soTKThuHuong: itemSelected?.tk_cmt || '',
      tienTruocThue: itemSelected?.tien_chua_vat || '',
      tienThue: itemSelected?.tien_thue || '',
      soTien: itemSelected?.tien || '',
      noiDung: itemSelected?.dien_giai || '',
      doiTuongTonThat: doiTuongTonThat,
      loaiHinhNghiepVu: itemSelected?.lh_nv || ''
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    setError,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const PHUONG_THUC_TT = [
    { label: 'Chuyển khoản', value: 'CK' },
    { label: 'Tiền mặt', value: 'TM' },
  ];

  const watchTienTruocThue = watch('tienTruocThue');
  const watchTienThue = watch('tienThue');
  const watchDVThuHuong = watch('dvThuHuong');
  const phuongThucThanhToan = watch('phuongThucThanhToan');
  const nganHangThuHuong = watch('nganHangThuHuong');
  const chiNhanhNganHangThuHuong = watch('chiNhanhNganHangThuHuong');

  useEffect(() => {
    getAllDanhMuc();
    getDataSoTienThanhToanTheoLHNV();
    initDataSoHoSo();
  }, []);

  const initDataSoHoSo = () => {
    if (itemSelected) {
      setSoHoaDon(itemSelected?.so_hdon_ttoan);
      let arrSoHoSo = dataSoHoaDonCT;
      let arrName = [];
      arrSoHoSo.forEach((e, index) => {
        if (itemSelected && itemSelected?.so_hdon_ttoan !== null && itemSelected?.so_hdon_ttoan !== '') {
          let arrId = itemSelected?.so_hdon_ttoan.split(',');
          arrId.forEach((x) => {
            if (x == e.value) {
              arrName.push(e.label);
            }
          });
        }
      });
      setValue('soHoaDon', arrName.toString());
    }
  };

  const getDataSoTienThanhToanTheoLHNV = async () => {
    try {
      setDialogLoading(true);
      let params = {
        so_id: profileData?.ho_so?.so_id || '',
        nv: profileData?.ho_so?.nghiep_vu, // xe máy thì truyền nv là XE_MAY
        bt: 0,
      };

      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_SO_TIEN_THANH_TOAN_THEO_LHNV, params);
      setDialogLoading(false);
      setListSoTienThanhToanTheoLHNV(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onSetDefaultValue = (data) => {
    setValue('tenDoiTuongThuHuong', data?.ten);
    setValue('nganHangThuHuong', data?.ngan_hang);
    setValue('soTKThuHuong', data?.tai_khoan);
    setValue('tienTruocThue', data?.tien);
    setValue('tienThue', data?.thue);
    setValue('noiDung', data?.dien_giai);
    if (data.tai_khoan) {
      setValue('phuongThucThanhToan', 'CK');
    }
  };

  useEffect(() => {
    setValue('soTien', +watchTienThue + +watchTienTruocThue);
  }, [watchTienTruocThue, watchTienThue]);

  const onInputFocus = () => { };

  const getDataChiNhanh = (data, value) => {
    setDataChiNhanhTheoNganHang(data.filter((e) => e.ma_ngan_hang === value));
  };

  const layThongTinHoaDonCT = async (loai) => {
    try {
      const params = {
        so_id: profileData?.ho_so?.so_id,
        loai: loai,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_THONG_TIN_HOA_DON_CT_XM, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info;
      onSetDefaultValue(data);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  useEffect(() => {
    if (watchDVThuHuong && !itemSelected) {
      layThongTinHoaDonCT(watchDVThuHuong);
    }
  }, [watchDVThuHuong]);

  const onPressLuu = async (data) => {
    setDialogLoading(true);
    try {
      let arrLHNV = [];
      let arrTienThuHuong = [];
      listSoTienThanhToanTheoLHNV.map((item) => {
        arrLHNV.push(item.lh_nv);
        arrTienThuHuong.push(+item.tien_thu_huong);
      });
      const params = {
        so_id: profileData?.ho_so?.so_id, //id hồ sơ
        bt: itemSelected?.bt || '', // ID người thụ hưởng - nếu thêm mới thì truyền 0, còn sửa thì phải truyền tham số này
        dvi_th: data.dvThuHuong, // Đơn vị thụ hưởng
        pttt: data.phuongThucThanhToan, // 	Phương thức thanh toán
        tk_cmt: data.soTKThuHuong, // Tài khoản thụ hưởng
        ten: data.tenDoiTuongThuHuong, // Tên đối tượng thụ hưởng
        ma_ngan_hang: data.nganHangThuHuong, //	Mã ngân hàng
        ma_chi_nhanh: data.chiNhanhNganHangThuHuong, // 	Mã chi nhánh ngân hàng
        dien_giai: data.noiDung, // Nội dung diễn giải
        tien_chua_vat: data.tienTruocThue, // Tiền chưa VAT
        tien_thue: data.tienThue, // 	Tiền thuế
        loai: 'TH', // 	Loại thụ hưởng mặc định "TH"
        so_id_tu: 0, // Số id tạm ứng mặc định: 0
        so_hdon_ttoan: listSoHoaDonDuocChon.join(), // Danh sách số id hóa đơn cách nhau bởi dấu ",": VD: 12345678,87654321
        so_id_doi_tuong: data.doiTuongTonThat,
        arr_lh_nv: arrLHNV,
        arr_tien_thu_huong: arrTienThuHuong,
        lh_nv: data.loaiHinhNghiepVu // loại hình nv của hồ sơ
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_THONG_TIN_THU_HUONG_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (itemSelected) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Sửa thông tin người thụ hưởng thành công', 'success');
      } else FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm người thụ hưởng thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getAllDanhMuc = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_TAT_CA_DANH_MUC, {});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const dataThuHuong = response.data_info.dvi_thu_huong;
      setDataDvThuHuong(dataThuHuong);
      setDataNganHang(response.data_info.ngan_hang);
      setDataChiNhanhBase(response.data_info.cn_ngan_hang);
      if (itemSelected) getDataChiNhanh(response.data_info.cn_ngan_hang, itemSelected.ma_ngan_hang);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const getTenHienThi = (value, data) => {
    if (!value) return '';
    let displayText = '';
    data.forEach((item) => {
      if (value === item.ma) {
        displayText = item.ten;
      }
    });
    return displayText;
  };

  const onSelectItemsSoHoSo = (list) => {
    let arrName = [];
    let arrId = [];
    list.map((item) => {
      arrName.push(item.label);
      arrId.push(item.value);
    });
    setListSoHoaDonDuocChon(arrId);
    setSoHoaDon(arrId.toString());
    setValue('soHoaDon', arrName.toString());
    refModalChonSoHoaDon.current.hide();
  };

  const onPressInputChonChiNhanh = () => {
    if (!getValues('nganHangThuHuong')) return setError('nganHangThuHuong', { type: 'required', message: 'Thông tin bắt buộc' });
    refModalChiNhanh.current.show();
  };

  const onChangeValueNganHang = (val) => {
    setValue('nganHangThuHuong', val.ma, { shouldValidate: true });
    setValue('chiNhanhNganHangThuHuong', '');
    getDataChiNhanh(dataChiNhanhBase, val.ma);
  };

  const onChangeInputValue = (item, index, val) => {
    let listSoTienThanhToanTheoLHNVTmp = listSoTienThanhToanTheoLHNV;
    listSoTienThanhToanTheoLHNVTmp[index].tien_thu_huong = val;
    setListSoTienThanhToanTheoLHNV([...listSoTienThanhToanTheoLHNVTmp]);
  };

  /*RENDER */
  const renderItemSoTienThanhToanTheoLHNV = ({ item, index }) => {
    return (
      <View style={styles.itemSoTienThanhToanTheoLHNVView}>
        <View style={[styles.inputRow, index > 0 && { borderTopWidth: 0 }]}>
          <View width={dimensions.width * 0.4} flexDirection="row">
            <Text style={styles.txtTenHangMuc}>{item.ten_lh_nv}</Text>
          </View>
          <View style={styles.rowStyles}>
            <View style={styles.frame}>
              <TextInputOutlined inputStyle={styles.inputStyle} value={item?.tien_duyet?.toString()} containerStyle={styles.inputContainer} editable={false} keyboardType="numeric" />
            </View>
            <View style={styles.frame}>
              <TextInputOutlined
                // maxLength={5}
                placeholder="0"
                keyboardType="numeric"
                inputStyle={styles.inputStyle}
                value={item?.tien_thu_huong?.toString()}
                containerStyle={styles.inputContainer}
                onChangeText={(value) => onChangeInputValue(item, index, value)}
              />
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderBangSanPhamThamGia = () => (
    <View>
      <View style={styles.tableTitleRow}>
        <View width={dimensions.width * 0.4} justifyContent="center" alignItems="center">
          <Text style={styles.txtHangMuc} children="Sản phẩm tham gia" font="medium14" />
        </View>
        <View style={[styles.rowStyles]}>
          <View style={[styles.frame, styles.checkboxRow]}>
            <Text children="Số tiền duyệt" font="regular12" />
          </View>
          <View style={[styles.frame, styles.checkboxRow]}>
            <Text children="Số tiền thanh toán" font="regular12" />
          </View>
        </View>
      </View>
      <FlatList
        data={listSoTienThanhToanTheoLHNV}
        scrollEnabled={true}
        renderItem={renderItemSoTienThanhToanTheoLHNV}
        keyExtractor={(item, index) => item.lh_nv?.toString() || ''}
        ListEmptyComponent={<Empty />}
      />
    </View>
  );
  const renderContent = () => {
    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <Controller
              control={control}
              name="dvThuHuong"
              rules={{
                required: true,
              }}
              render={({ field: { onChange, value } }) => (
                <DropdownPicker
                  title={'Đơn vị thụ hưởng'}
                  zIndex={8000}
                  items={dataDvThuHuong}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  isOpen={openDvThuHuong}
                  setOpen={setOpenDvThuHuong}
                  placeholder="Chọn đơn vị thụ hưởng"
                  schema={{
                    label: 'ten',
                    value: 'ma',
                  }}
                  inputErr={errors.dvThuHuong && getErrMessage('dvThuHuong', errors.dvThuHuong.type)}
                  isRequired={true}
                  searchable={false}
                // onChangeValue={onChangeDvThuHuong}
                />
              )}
            />

            {/* <Controller
              control={control}
              name="doiTuongTonThat"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  title="Đối tượng tổn thất"
                  zIndex={7000}
                  items={profileData?.ds_doi_tuong}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  isOpen={openDoiTuongTonThat}
                  setOpen={setOpenDoiTuongTonThat}
                  placeholder="Chọn đối tượng"
                  schema={{
                    label: 'ten_doi_tuong',
                    value: 'so_id_doi_tuong',
                  }}
                  inputErr={errors.doiTuongTonThat && getErrMessage('doiTuongTonThat', errors.doiTuongTonThat.type)}
                  isRequired={true}
                  searchable={false}
                  // onChangeValue={onChangeDvPhatHanh}
                />
              )}
            /> */}


            <Controller
              control={control}
              name="loaiHinhNghiepVu"
              rules={{
                required: true,
              }}
              render={({ field: { onChange, value } }) => (
                <DropdownPicker
                  title="Loại hình nghiệp vụ"
                  zIndex={7000}
                  items={profileData?.lh_nv}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  isOpen={openLoaiHinhNghiepVu}
                  setOpen={setOpenLoaiHinhNghiepVu}
                  placeholder="Chọn loại hình nghiệp vụ"
                  schema={{
                    label: 'ten',
                    value: 'ma',
                  }}
                  inputErr={errors.loaiHinhNghiepVu && getErrMessage('loaiHinhNghiepVu', errors.loaiHinhNghiepVu.type)}
                  isRequired={true}
                  searchable={false}
                // onChangeValue={onChangeDvPhatHanh}
                />
              )}
            />

            <Controller
              control={control}
              name="phuongThucThanhToan"
              rules={{
                required: true,
              }}
              render={({ field: { onChange, value } }) => (
                <DropdownPicker
                  isRequired
                  zIndex={5000}
                  searchable={false}
                  itemSelected={value}
                  items={PHUONG_THUC_TT}
                  title="Phương thức thanh toán"
                  isOpen={openPhuongThucThanhToan}
                  setOpen={setOpenPhuongThucThanhToan}
                  placeholder="Chọn phương thức thanh toán"
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  inputErr={errors.phuongThucThanhToan && getErrMessage('phuongThucThanhToan', errors.phuongThucThanhToan.type)}
                />
              )}
            />

            {phuongThucThanhToan === 'CK' && (
              <>
                <Controller
                  control={control}
                  name="nganHangThuHuong"
                  rules={{
                    required: true,
                  }}
                  render={({ field: { onChange, value } }) => (
                    <TextInputOutlined
                      isRequired
                      isDropdown
                      editable={false}
                      isTouchableOpacity
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      onFocus={onInputFocus}
                      onChangeText={onChange}
                      title="Ngân hàng thụ hưởng"
                      placeholder="Chọn ngân hàng"
                      inputStyle={{ color: colors.BLACK_03 }}
                      value={getTenHienThi(value, dataNganHang)}
                      onPress={() => refModalNganHang.current.show()}
                      error={errors.nganHangThuHuong && getErrMessage('nganHangThuHuong', errors.nganHangThuHuong.type)}
                    />
                  )}
                />

                <Controller
                  control={control}
                  name="chiNhanhNganHangThuHuong"
                  rules={{
                    required: false,
                  }}
                  render={({ field: { onChange, value } }) => (
                    <TextInputOutlined
                      isDropdown
                      // isRequired
                      editable={false}
                      isTouchableOpacity
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      onFocus={onInputFocus}
                      onChangeText={onChange}
                      title="Chi nhánh thụ hưởng"
                      placeholder="Chọn chi nhánh ngân hàng"
                      inputStyle={{ color: colors.BLACK_03 }}
                      value={getTenHienThi(value, dataChiNhanhBase)}
                      onPress={onPressInputChonChiNhanh}
                    // error={errors.chiNhanhNganHangThuHuong && getErrMessage('chiNhanhNganHangThuHuong', errors.chiNhanhNganHangThuHuong.type)}
                    />
                  )}
                />
              </>
            )}
            <Controller
              control={control}
              name="soTKThuHuong"
              rules={{
                required: true,
              }}
              render={({ field: { onChange, value } }) => (
                <TextInputOutlined
                  value={value}
                  isRequired={true}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  title={phuongThucThanhToan === 'CK' ? 'Số tài khoản thụ hưởng' : 'Số CMT/CCCD thụ hưởng'}
                  placeholder={phuongThucThanhToan === 'CK' ? 'Nhập số tài khoản' : 'Nhập số CMT/CCCD'}
                  getRef={(ref) => (soTaiKhoanRef = ref)}
                  onSubmitEditing={() => tenDoiTuongThuHuongRef?.focus()}
                  error={errors.soTKThuHuong && getErrMessage('soTKThuHuong', errors.soTKThuHuong.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="tenDoiTuongThuHuong"
              rules={{
                required: true,
              }}
              render={({ field: { onChange, value } }) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  title="Tên đối tượng thụ hưởng"
                  getRef={(ref) => (tenDoiTuongThuHuongRef = ref)}
                  placeholder="Nhập tên đối tượng thụ hưởng"
                  onSubmitEditing={() => tienTruocThueRef?.focus()}
                  error={errors.tenDoiTuongThuHuong && getErrMessage('tenDoiTuongThuHuong', errors.tenDoiTuongThuHuong.type)}
                />
              )}
            />

            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="tienTruocThue"
                  rules={{
                    required: true,
                  }}
                  render={({ field: { onChange, value } }) => (
                    <TextInputOutlined
                      isRequired
                      value={value}
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      onFocus={onInputFocus}
                      title="Tiền (chưa VAT)"
                      onChangeText={onChange}
                      keyboardType="numeric"
                      placeholder="0"
                      inputStyle={{ textAlign: 'right' }}
                      getRef={(ref) => (tienTruocThueRef = ref)}
                      onSubmitEditing={() => tienThueRef?.focus()}
                      error={errors.tienTruocThue && getErrMessage('tienTruocThue', errors.tienTruocThue.type)}
                    />
                  )}
                />
              </View>

              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="tienThue"
                  rules={{
                    required: true,
                  }}
                  render={({ field: { onChange, value } }) => (
                    <TextInputOutlined
                      isRequired
                      title="Thuế"
                      value={value}
                      blurOnSubmit={false}
                      onFocus={onInputFocus}
                      returnKeyType={'next'}
                      keyboardType="numeric"
                      onChangeText={onChange}
                      placeholder="0"
                      inputStyle={{ textAlign: 'right' }}
                      getRef={(ref) => (tienThueRef = ref)}
                      onSubmitEditing={() => noiDungRef?.focus()}
                      error={errors.tienThue && getErrMessage('tienThue', errors.tienThue.type)}
                    />
                  )}
                />
              </View>
            </View>
            <Controller
              control={control}
              name="soTien"
              rules={{
                required: true,
              }}
              render={({ field: { onChange, value } }) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  disabled={true}
                  placeholder="0"
                  title="Số tiền"
                  editable={false}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  keyboardType="numeric"
                  onChangeText={onChange}
                  inputStyle={{ textAlign: 'right' }}
                  error={errors.soTien && getErrMessage('soTien', errors.soTien.type)}
                />
              )}
            />

            <Controller
              control={control}
              name="soHoaDon"
              rules={{
                required: false,
              }}
              render={({ field: { onChange, value } }) => (
                <TextInputOutlined
                  value={value}
                  editable={false}
                  title="Số hoá đơn"
                  isTouchableOpacity
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  placeholder="Chọn số hoá đơn"
                  inputStyle={{ color: colors.BLACK_03 }}
                  onPress={() => refModalChonSoHoaDon.current.show()}
                />
              )}
            />

            <View paddingBottom={100}>
              <Controller
                control={control}
                name="noiDung"
                rules={{
                  required: true,
                }}
                render={({ field: { onChange, value } }) => (
                  <TextInputOutlined
                    value={value}
                    title="Nội dung"
                    isRequired={true}
                    blurOnSubmit={false}
                    onFocus={onInputFocus}
                    onChangeText={onChange}
                    placeholder="Nhập nội dung"
                    getRef={(ref) => (noiDungRef = ref)}
                    error={errors.noiDung && getErrMessage('noiDung', errors.noiDung.type)}
                  />
                )}
              />
            </View>
          </View>
          {/* {renderBangSanPhamThamGia()} */}
        </KeyboardAwareScrollView>
        <ModalNganHang data={dataNganHang} value={nganHangThuHuong} ref={refModalNganHang} setValue={(val) => onChangeValueNganHang(val)} onBackPress={() => refModalNganHang.current.hide()} />
        <ModalChiNhanh
          data={dataChiNhanhTheoNganHang}
          ref={refModalChiNhanh}
          value={chiNhanhNganHangThuHuong}
          setValue={(val) => setValue('chiNhanhNganHangThuHuong', val.ma)}
          onBackPress={() => refModalChiNhanh.current.hide()}
        />
        <ModalChonSoHoaDon
          soHoaDon={soHoaDon}
          data={dataSoHoaDonCT}
          ref={refModalChonSoHoaDon}
          setValue={(list) => onSelectItemsSoHoSo(list)}
          onBackPress={() => refModalChonSoHoaDon.current.hide()}
        />
      </SafeAreaView>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={'Thêm người thụ hưởng xe máy'}
      renderView={renderContent()}
      footer={<ButtonLinear disabled={dialogLoading} title="Lưu" onPress={handleSubmit(onPressLuu)} />}
    />
  );
};

export const ThemNguoiThuHuongXeMayScreen = memo(ThemNguoiThuHuongXeMayScreenComponent, isEqual);
