import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, CustomTabBar, Empty, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {NumericFormat} from 'react-number-format';
import styles from './Styles';
import {RenderContentModalChuyenThanhToan} from '../Components';
import {CustomModal} from '@app/components/CustomModal';
import moment from 'moment';

let timer;
const DEFAULT_DATE = 30000101;
const DanhSachNguoiThuHuongXeMayScreenComponent = (props) => {
  console.log('DanhSachNguoiThuHuongXeMayScreenComponent');
  const {navigation, route} = props;
  const {profileData} = route?.params;

  const [dialogLoading, setDialogLoading] = useState(false);
  const [dataChungTu, setDataChungTu] = useState([]);
  const [dataThuHuong, setDataThuHuong] = useState([]);
  const [dataSoHoaDonChungTu, setDataSoHoaDonChungTu] = useState([]);

  const [tongTienCT, setTongTienCT] = useState(0);
  const [btnTabActive, setBtnTabActive] = useState(0);
  const [tongTienThuHuong, setTongTienThuHuong] = useState(0);
  const [ngayChuyenThanhToan, setNgayChuyenThanhToan] = useState(0);

  let tabViewRef = useRef(null);
  let refModalChuyenThanhToan = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
      setNgayChuyenThanhToan(profileData?.ho_so?.ngay_chuyen_tt);
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getData = async () => {
    setDialogLoading(true);
    let params = {
      so_id: profileData?.ho_so?.so_id || '',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_NGUOI_THU_HUONG_HOA_DON_CT_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrSoHoaDon = [];
      let tongTienCt = 0;
      let tongTienThuHuong = 0;
      let arrChungTu = response.data_info.chung_tu;
      let arrThuHuong = response.data_info?.thu_huong;

      arrChungTu.map((item) => {
        let sum = item.tong_cong;
        tongTienCt += sum;
      });
      arrThuHuong.map((item) => {
        let sum = item.tien;
        tongTienThuHuong += sum;
      });
      setTongTienThuHuong(tongTienThuHuong);
      setTongTienCT(tongTienCt);
      setDataChungTu(arrChungTu);
      setDataThuHuong(arrThuHuong);
      arrChungTu.map((item) => {
        arrSoHoaDon.push({label: item.mau_hdon + ' - ' + item.ten_dvi_phat_hanh, value: item.bt});
      });
      setDataSoHoaDonChungTu(arrSoHoaDon);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const handleXoaChungTu = (item) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn xoá hoá đơn/chứng từ này không?', [
      {text: 'Để sau', style: 'destructive'},
      {text: 'Đồng ý', onPress: () => xoaChungTu(item)},
    ]);
  };
  const handleXoaThuHuong = (item) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn xoá người thụ hưởng này không?', [
      {text: 'Để sau', style: 'destructive'},
      {text: 'Đồng ý', onPress: () => xoaNguoiThuHuong(item)},
    ]);
  };

  const xoaChungTu = async (item) => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id || '',
        bt: item.bt,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_HOA_DON_CT_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá hoá đơn/chứng từ thành công', 'success');
      getData();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const xoaNguoiThuHuong = async (item) => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id || '',
        bt: item.bt,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_THONG_TIN_NG_THU_HUONG_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá người thụ hưởng thành công', 'success');
      getData();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onRefresh = () => {
    getData();
  };

  const onEditChungTu = (it) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.THEM_HOA_DON_CHUNG_TU_XM, {profileData: profileData, itemSelected: it});
  };
  const onEditThuHuong = (it) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.THEM_NGUOI_THU_HUONG_XM, {profileData: profileData, dataSoHoaDonCT: dataSoHoaDonChungTu, itemSelected: it});
  };

  const handleHuyChuyenThanhToan = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn huỷ chuyển thanh toán không?', [
      {
        text: 'Huỷ',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            setDialogLoading(true);
            let params = {
              so_id: profileData?.ho_so?.so_id,
            };
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.HUY_CHUYEN_THANH_TOAN_BOI_THUONG_XM, params);
            setDialogLoading(false);
            // let response = await CarClaimEndpoint.huyChuyenThanhToan(AxiosConfig.ACTION_CODE.HUY_CHUYEN_THANH_TOAN_BOI_THUONG_XM, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ chuyển thanh toán thành công!', 'success');
            setNgayChuyenThanhToan(DEFAULT_DATE);
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  // footer
  const renderFooter = () => {
    if (!profileData) return;
    let showBtnChuyenThanhToan = false;
    let showBtnHuyChuyenThanhToan = false;
    let showBtnDoiTuongApDungHoaDon = false;
    if (profileData?.ho_so?.ngay_duyet_hs >= DEFAULT_DATE && ngayChuyenThanhToan >= DEFAULT_DATE) showBtnChuyenThanhToan = true;
    if (profileData?.ho_so?.ngay_duyet_hs >= DEFAULT_DATE) showBtnDoiTuongApDungHoaDon = true;
    if (ngayChuyenThanhToan < DEFAULT_DATE) showBtnHuyChuyenThanhToan = true;
    return (
      <View style={styles.footerView}>
        {btnTabActive == 0 && showBtnChuyenThanhToan && (
          <ButtonLinear title="Thêm hoá đơn CT" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_HOA_DON_CHUNG_TU_XM, {profileData: profileData})} />
        )}
        {btnTabActive == 0 && showBtnDoiTuongApDungHoaDon && (
          <ButtonLinear
            title="Đối tượng AD hoá đơn"
            onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.DOI_TUONG_AD_HOA_DON, {profileData: profileData})}
            linearStyle={{marginLeft: spacing.small}}
          />
        )}
        {btnTabActive == 1 && showBtnChuyenThanhToan && (
          <ButtonLinear title="Thêm người thụ hưởng" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_NGUOI_THU_HUONG_XM, {profileData: profileData, dataSoHoaDonCT: dataSoHoaDonChungTu})} />
        )}
        {/* {showBtnChuyenThanhToan && <ButtonLinear title="Chuyển kế toán" linearStyle={styles.btnStyles} onPress={() => refModalChuyenThanhToan.current.show()} />} */}
        {showBtnHuyChuyenThanhToan && <ButtonLinear title="Huỷ chuyển thanh toán" isSubBtn onPress={() => handleHuyChuyenThanhToan()} />}
      </View>
    );
  };

  const ListFooter = (price) => {
    return (
      <View flexDirection="row" justifyContent="space-between" flex={1} paddingHorizontal={20} marginBottom={60}>
        <Text style={styles.totalPrice}>Tổng cộng</Text>
        <NumericFormat value={price} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.totalPrice}>{value}</Text>} />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Hoá đơn/chứng từ/người thụ hưởng xe máy"
      renderView={
        <SafeAreaView style={styles.container}>
          <ScrollableTabView ref={tabViewRef} initialPage={0} onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))} renderTabBar={() => <CustomTabBar />}>
            <View tabLabel="Hoá đơn chứng từ" style={styles.component}>
              <FlatList
                data={dataChungTu}
                renderItem={(item) => <RenderItemHoaDonCT data={dataChungTu} items={item} onPressTrash={(it) => handleXoaChungTu(it)} onPressItem={(it) => onEditChungTu(it)} />}
                keyExtractor={(item, index) => index.toString()}
                refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={onRefresh} />}
                ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
                ListFooterComponent={dataChungTu.length > 0 && ListFooter(tongTienCT.toFixed())}
              />
            </View>
            <View tabLabel="Người thụ hưởng" style={styles.component}>
              <FlatList
                data={dataThuHuong}
                renderItem={(item) => <RenderItemNguoiThuHuong data={dataThuHuong} items={item} onPressTrash={(it) => handleXoaThuHuong(it)} onPressItem={(it) => onEditThuHuong(it)} />}
                keyExtractor={(item, index) => index.toString()}
                refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={onRefresh} />}
                ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
                ListFooterComponent={dataThuHuong.length > 0 && ListFooter(tongTienThuHuong.toFixed())}
              />
            </View>
          </ScrollableTabView>
          <CustomModal
            ref={refModalChuyenThanhToan}
            renderContent={() => (
              <RenderContentModalChuyenThanhToan
                profileData={profileData}
                onBackPress={() => {
                  setNgayChuyenThanhToan(moment(new Date()).format('YYYYMMDD'));
                  setTimeout(() => {
                    getData();
                  }, 500);
                }}
                onCancel={() => {
                  refModalChuyenThanhToan.current.hide();
                }}
              />
            )}
          />
        </SafeAreaView>
      }
      footer={renderFooter()}
    />
  );
};

const RenderItemNguoiThuHuong = (props) => {
  const {items, onPressTrash, onPressItem} = props;
  const item = items.item;
  const content = (label, value, style) => {
    return (
      <View flex={1} marginVertical={4}>
        {label === 'Số tài khoản: ' ? (
          <View marginVertical={2} flex={1}>
            <Text style={[styles.txtLabel]}>{label}</Text>
            <Text style={[styles.detail, style]}>{value}</Text>
          </View>
        ) : (
          <Text style={[styles.txtLabel]}>
            {label}
            {typeof value == 'number' ? (
              <NumericFormat value={value?.toFixed()} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={[styles.detail, style]}>{val}</Text>} />
            ) : (
              <Text style={[styles.detail, style]}>{value}</Text>
            )}
          </Text>
        )}
      </View>
    );
  };

  const renderViewPrice = (label, value, style) => {
    return (
      <View marginVertical={4}>
        <Text style={styles.txtLabel}>{label}</Text>
        <NumericFormat value={value} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={[styles.detail, style]}>{val}</Text>} />
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={() => onPressItem(item)}>
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <View flexDirection="row">
            {content('Đối tượng thụ hưởng: ', item.ten, styles.txtStyles)}
            <TouchableOpacity style={{marginTop: 2}} onPress={() => onPressTrash(item)}>
              <Icon.AntDesign name="closesquareo" color={colors.RED1} size={24} />
            </TouchableOpacity>
          </View>
          {content('Đối tượng: ', item.ten_doi_tuong)}
          {content('Ngân hàng: ', item.ten_ngan_hang)}
          <View style={styles.rowStyles}>
            {content('Số tài khoản: ', item.tk_cmt)}
            {renderViewPrice('Số tiền: ', item.tien, styles.txtPrice)}
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const RenderItemHoaDonCT = (props) => {
  const {items, onPressTrash, onPressItem} = props;
  const item = items.item;

  const content = (label, value, style) => {
    return (
      <View marginVertical={2} flex={1}>
        <Text style={[styles.txtLabel, {marginBottom: 4}]}>
          {label}
          <Text style={[styles.detail, style]}>{value}</Text>
        </Text>
      </View>
    );
  };

  const renderViewPrice = (label, value, style) => {
    return (
      <View marginVertical={2}>
        <Text style={styles.txtLabel}>{label}</Text>
        <NumericFormat value={value?.toFixed()} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={[styles.detail, style]}>{val}</Text>} />
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={() => onPressItem(item)}>
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <View flexDirection="row">
            {content('Đ.v phát hành: ', item.ten_dvi_phat_hanh, styles.txtStyles)}
            <TouchableOpacity style={{marginTop: 2}} onPress={() => onPressTrash(item)}>
              <Icon.AntDesign name="closesquareo" color={colors.RED1} size={24} />
            </TouchableOpacity>
          </View>
          {content('Đối tượng: ', item.ten_doi_tuong)}
          <View flexDirection="row" justifyContent="space-between">
            {content('Ngày phát hành: ', item.ngay_ct)}
            {content('Số: ', item.so_hdon)}
          </View>
          <View style={styles.rowStyles}>
            {renderViewPrice('Số tiền', item.tien, styles.txtPrice)}
            {renderViewPrice('Tiền thuế', item.thue, styles.txtPrice)}
            {renderViewPrice('Tiền giảm', item.tien_giam, styles.txtPrice)}
          </View>
          <View style={[styles.rowStyles, {justifyContent: 'flex-end'}]}>{renderViewPrice('Tổng cộng', item.tong_cong, styles.txtPrice)}</View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export const DanhSachNguoiThuHuongXeMayScreen = memo(DanhSachNguoiThuHuongXeMayScreenComponent, isEqual);
