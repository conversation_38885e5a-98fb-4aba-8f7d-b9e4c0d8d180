import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {ButtonLinear, CheckboxComp, Empty, Icon, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {FlatList, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonSoHoaDonComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, setValue, data, soHoaDon} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [listSoHoaDon, setListSoHoaDon] = useState([]);

  const onPressItem = (item, idx, val) => {
    let newList = listSoHoaDon;
    newList.forEach((item, index) => {
      newList[idx].isChecked = val;
    });
    setListSoHoaDon([...newList]);
  };

  const initModalData = () => {
    if (data.length > 0) {
      let newArrData = data;
      newArrData.forEach((e, index) => {
        newArrData[index].isChecked = false;
        if (soHoaDon && soHoaDon !== null && soHoaDon !== '') {
          let arrHm = soHoaDon.split(',');
          arrHm.forEach((x) => {
            if (x == e.value) {
              newArrData[index].isChecked = true;
            }
          });
        }
      });
      setListSoHoaDon([...newArrData]);
    }
  };

  const onPressLuu = () => {
    let newArr = [];
    listSoHoaDon.forEach((item) => {
      if (item.isChecked === true) {
        newArr.push(item);
      }
    });
    setValue && setValue(newArr);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Chọn số hoá đơn" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index, !item.isChecked)}>
        <CheckboxComp value={item.isChecked} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
        <Text style={{color: item.isChecked ? colors.PRIMARY : colors.BLACK_03, marginTop: Platform.OS == 'android' ? 4 : 2}}>{item.label}</Text>
      </TouchableOpacity>
    );
  };

  const listFooter = () => {
    return (
      <View flexDirection="row" marginBottom={30}>
        <ButtonLinear title="Đóng" loading={false} onPress={() => onBackPress()} linearStyle={styles.bottomBtn} textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} />
        <ButtonLinear linearStyle={styles.bottomBtn} title="Lưu" onPress={onPressLuu} />
      </View>
    );
  };

  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onModalShow={initModalData}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        <View style={styles.content}>
          <FlatList data={listSoHoaDon} renderItem={renderItem} keyExtractor={(item, index) => index.toString()} ListEmptyComponent={<Empty imageStyle={styles.imageStyles} />} />
        </View>
        {listFooter()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: 16,
    borderColor: colors.GRAY,
    // flex: 1,
    height: 40,
    margin: 16,
    borderRadius: 25,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    padding: 5,
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    flex: 1,
    margin: 10,
    // paddingBottom: 20,
  },
  imageStyles: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  bottomBtn: {
    marginHorizontal: 10,
  },
});

export const ModalChonSoHoaDon = ModalChonSoHoaDonComponent;
