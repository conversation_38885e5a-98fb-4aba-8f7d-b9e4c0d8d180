import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {StyleSheet, Dimensions} from 'react-native';
const {width} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  inputStyle: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: colors.GRAY,
    marginHorizontal: spacing.small,
    paddingHorizontal: spacing.small,
  },
  item: {
    alignItems: 'flex-start',
    paddingTop: spacing.tiny,
  },
  content: {
    flex: 1,
    marginHorizontal: spacing.small,
  },
  footerView: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: 16,
  },
  boxChat: {
    margin: spacing.tiny,
    padding: spacing.small,
    borderTopLeftRadius: 0,
    borderRadius: spacing.smaller,
    backgroundColor: colors.WHITE1,
    maxWidth: width * 0.7,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtTime: {
    lineHeight: 20,
    fontSize: 10,
    fontStyle: 'italic',
    color: colors.GRAY6,
    marginRight: spacing.smaller,
  },
  txtNoiDung: {
    color: colors.BLACK_03,
  },
  txtTitle: {
    fontSize: 12,
    lineHeight: 20,
    color: colors.BLACK_03,
  },
  isUserBoxChatStyles: {
    borderTopRightRadius: 0,
    backgroundColor: '#E7F3FF',
    borderTopLeftRadius: spacing.smaller,
  },
  txtEmpty: {
    marginVertical: 10,
    textAlign: 'center',
  },
  marginBottom: {
    marginBottom: 70,
  },
});
