import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {getCauHinhHoSoByMa} from '@app/utils/DataProvider';
import {Icon, ImageComp, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';

const TakePhotoStep1Component = ({profileData, rotationImage, imagesData, removeImage, onPressOpenCamera, onPressXemLaiAnh, dataAnhXMHT}) => {
  const [chonAnhTuThuVien, setChonAnhTuThuVien] = useState(false);

  useEffect(() => {
    if (profileData && profileData.cau_hinh) {
      let cauHinhChonAnhTuThuVien = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.CHON_ANH_THU_VIEN, profileData.cau_hinh);
      if (cauHinhChonAnhTuThuVien?.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) setChonAnhTuThuVien(true);
    }
  }, []);

  // console.log('TakePhotoStep1', props);
  const renderCameraButton = () => {
    return (
      <TouchableOpacity style={styles.cameraView} onPress={onPressOpenCamera}>
        <Icon.MaterialCommunityIcons name="camera-plus" size={40} color={colors.GRAY11} style={styles.iconCamera} />
      </TouchableOpacity>
    );
  };
  const renderImageItem = (imageData) => {
    // if (index == imagesData.length - 1) return renderCameraButton();
    return (
      <ImageComp
        imageData={imageData}
        removeImage={removeImage}
        onPressOpenCamera={(index, menuData, type) => onPressOpenCamera(index, 'ANH_HIEN_TRUONG', type)}
        onPressXemLaiAnh={onPressXemLaiAnh}
        uploadFromLib={true}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        rotationImage={rotationImage}
      />
    );
  };

  const renderImageItemAnhXacMinhHienTruong = (imageData) => {
    // if (index == imagesData.length - 1) return renderCameraButton();
    return (
      <ImageComp
        imageData={imageData}
        removeImage={removeImage}
        onPressOpenCamera={(index, menuData, type) => onPressOpenCamera(index, 'XMHT', type)}
        onPressXemLaiAnh={onPressXemLaiAnh}
        uploadFromLib={chonAnhTuThuVien}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        rotationImage={rotationImage}
      />
    );
  };
  return (
    <ScrollView style={{flex: 1}}>
      <FlatList
        ListHeaderComponent={
          <Text style={styles.headerTitle} font="medium16">
            Ảnh hiện trường
          </Text>
        }
        scrollEnabled={true}
        data={imagesData}
        renderItem={renderImageItem}
        numColumns={2}
        horizontal={false}
        keyExtractor={(item, index) => index + ''}
      />
      <FlatList
        ListHeaderComponent={
          <Text style={styles.headerTitle} font="medium16">
            Ảnh xác minh hiện trường
          </Text>
        }
        scrollEnabled={false}
        data={dataAnhXMHT}
        renderItem={renderImageItemAnhXacMinhHienTruong}
        numColumns={2}
        horizontal={false}
        keyExtractor={(_, index) => index.toString()}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    // paddingHorizontal: 15,
    backgroundColor: colors.WHITE,
    // marginTop: 10,
    // backgroundColor: colors.blueLight,
  },
  scrollView: {
    width: dimensions.width,
    // backgroundColor: colors.blueLight,
  },
  btnLoginView: {
    flexDirection: 'row',
    backgroundColor: colors.PRIMARY,
    marginHorizontal: 10,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerView: {
    // flex: 1,
    // justifyContent: 'flex-end',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    // borderRadius : 30,
    // marginBottom: 30,
    // marginTop: 20,
    backgroundColor: colors.WHITE,
    // justifyContent: 'center',
  },
  txtBtnLogin: {
    paddingRight: 5,
    paddingVertical: 15,
    fontSize: 20,
    textTransform: 'uppercase',
    color: colors.WHITE,
  },
  headerTitle: {
    marginHorizontal: spacing.medium,
    marginTop: spacing.medium,
  },
  contentDetail: {
    paddingHorizontal: 20,
    marginBottom: 7,
    backgroundColor: colors.WHITE,
    borderRadius: 10,
    marginHorizontal: 10,
  },
  modalSelectorView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    marginRight: 15,
  },

  stepIndicator: {
    marginVertical: 20,
  },
  page: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GREEN,
  },
  cameraView: {
    borderWidth: 1,
    borderColor: colors.GRAY11,
    borderStyle: 'dashed',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
  },
  iconCamera: {
    padding: 10,
  },
});

const TakePhotoStep1Memo = memo(TakePhotoStep1Component, isEqual);
export const TakePhotoStep1 = TakePhotoStep1Memo;
