import {colors} from '@app/commons/Theme';
import {getAppSetting} from '@app/redux/slices/AppSettingSlice';
import {selectUser} from '@app/redux/slices/UserSlice';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import {APP_NAME, isIOS} from '@constant';
import moment from 'moment';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import {useRef} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, StyleSheet, TouchableOpacity, Vibration, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import DeviceInfo from 'react-native-device-info';
import ImageMarker, {Position} from 'react-native-image-marker';
import Modal from 'react-native-modal';
import {useSelector} from 'react-redux';

const ModalCameraComponent = forwardRef(({handleImage, currentPosition, menuDataType}, ref) => {
  // console.log('ModalVideoComponent');
  // console.log('ModalCamera', props);
  const userInfo = useSelector(selectUser);
  let cameraRef = useRef(null);
  const [visible, setVisible] = useState(false);
  useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
    },
    hide: () => setVisible(false),
    getVisible: () => {
      return visible;
    },
  }));

  const [flashData, setFlashType] = useState({
    flashIcon: 'flash-off',
    flashMode: RNCamera.Constants.FlashMode.off,
  });
  const [cameraType, setCameraType] = useState(RNCamera.Constants.Type.back);
  const appSetting = useSelector(getAppSetting);
  const [countPicture, setCountPicture] = useState(0);

  // const prepareRatio = async () => {
  //   if (Platform.OS === 'android' && cameraRef) {
  //     const ratios = await cameraRef.getSupportedRatiosAsync();
  //     // See if the current device has your desired ratio, otherwise get the maximum supported one
  //     // Usually the last element of "ratios" is the maximum supported ratio
  //     // const ratio = ratios.find((ratio) => ratio === DESIRED_RATIO) || ratios[ratios.length - 1];
  //     // console.log(ratios);
  //     // setRatio(ratio);
  //   }
  // };

  //xử lý khi icon FLash được click
  const onPressChangeFlash = () => {
    //Nếu đang auto -> flash
    if (flashData.flashIcon == 'flash-auto') {
      setFlashType({
        flashIcon: 'flash',
        flashMode: RNCamera.Constants.FlashMode.on,
      });
    }
    //nếu đang flash -> tắt
    else if (flashData.flashIcon == 'flash') {
      setFlashType({
        flashIcon: 'flash-off',
        flashMode: RNCamera.Constants.FlashMode.off,
      });
    }
    //nếu đang tắt flash -> flash-auto
    else if (flashData.flashIcon == 'flash-off') {
      setFlashType({
        flashIcon: 'flash-auto',
        flashMode: RNCamera.Constants.FlashMode.auto,
      });
    }
  };
  //chuyển camera trước - sau
  const onPressSwapCamera = () => {
    if (cameraType == RNCamera.Constants.Type.back) {
      setCameraType(RNCamera.Constants.Type.front);
    } else if (cameraType == RNCamera.Constants.Type.front) {
      setCameraType(RNCamera.Constants.Type.back);
    }
  };
  //ẩn modal camera
  const onPressTatCameraModal = () => {
    setVisible(false);
    setFlashType({
      flashIcon: 'flash-off',
      flashMode: RNCamera.Constants.FlashMode.off,
    });
    setCameraType(RNCamera.Constants.Type.back);
    setCountPicture(0);
  };

  const onPressChupAnh = async () => {
    if (cameraRef) {
      const cameraOptions = {quality: 0.5, width: 1800, fixOrientation: true};
      const dataImage = await cameraRef.takePictureAsync(cameraOptions);
      if (appSetting.rung) Vibration.vibrate(200);
      setCountPicture(countPicture + 1);
      dataImage.path = dataImage.uri;
      let imageAddText = await chenThongTinLenAnh(dataImage);
      dataImage.path = (!isIOS ? 'file://' : '') + imageAddText;
      handleImage(dataImage, menuDataType, null, 0);
    }
  };

  const chenThongTinLenAnh = async dataImage => {
    //ngày giờ / toạ độ / người / thông tin máy
    let txtChen = '';
    txtChen += userInfo.nguoi_dung.nsd;
    let tenMay = await DeviceInfo.getBrand();
    txtChen += '\n' + moment().format('HH:mm DD/MM/YYYY') + ' ';
    txtChen += tenMay;
    if (currentPosition && currentPosition.coords) txtChen += '\n' + currentPosition.coords.longitude + ' ; ' + currentPosition.coords.latitude + ' ';

    let imageAddText = await ImageMarker.markText({
      backgroundImage: {
        src: dataImage.path,
        scale: 1,
      },
      watermarkTexts: [
        {
          text: txtChen,
          positionOptions: {
            position: Position.bottomRight,
          },
          style: {
            color: '#FFF',
            fontSize: isIOS ? 30 : 30,
          },
        },
      ],
      quality: 100,
    }).catch(err => {
      Alert.alert('Chèn toạ độ không thành công', err.message);
      return '';
    });
    return imageAddText;
  };

  /* RENDER */
  return (
    <Modal isVisible={visible} swipeDirection={'down'} style={styles.modal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          <View style={{flex: 1}}>
            <RNCamera
              ref={ref => (cameraRef = ref)}
              playSoundOnCapture={appSetting.amThanhKhiChup}
              // onCameraReady={prepareRatio}
              // ratio={'4:3'} //nếu đang chụp ảnh HỒ SƠ, GIẤY TỜ
              style={styles.cameraPreview}
              type={cameraType}
              flashMode={flashData.flashMode}
              captureAudio={false}
              zoom={0}
              useNativeZoom={true}
              androidCameraPermissionOptions={{
                title: 'Cho phép ' + APP_NAME + ' truy cập camera của bạn',
                message: APP_NAME + ' muốn truy cập camera của bạn để chụp ảnh',
                buttonPositive: 'Đồng ý',
                buttonNegative: 'Để sau',
              }}>
              <View style={styles.btnCloseCamera}>
                <TouchableOpacity onPress={onPressTatCameraModal}>
                  <Icon.AntDesign name="arrowleft" size={40} color={colors.WHITE} />
                </TouchableOpacity>
              </View>
              <View style={styles.countPicture}>
                <Text children={countPicture} style={styles.txtCountPicture} />
              </View>
            </RNCamera>
          </View>
          <View style={styles.btnsCameraView}>
            <TouchableOpacity onPress={onPressChangeFlash} style={styles.btnCameraView}>
              <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressChupAnh}>
              <Icon.MaterialCommunityIcons name={'circle-slice-8'} size={100} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
              <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnsCameraView: {
    flexDirection: 'row',
    backgroundColor: colors.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    height: 120,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnCloseCamera: {
    position: 'absolute',
    left: 10,
    top: !isIOS ? 10 : 50,
  },
  countPicture: {
    position: 'absolute',
    right: 15,
    bottom: 5,
  },
  txtCountPicture: {
    fontSize: 40,
    color: '#FFF',
    fontWeight: 'bold',
  },
  khungCatAnhCamera: {
    borderWidth: 3,
    borderStyle: 'dashed',
    borderRadius: 20,
  },
  //kích thước BANG_LAI_XE
  sizeBLX: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.63,
  },
  //kích thước DANG_KIEM
  sizeDK: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.67,
  },
});

// const mapStateToProps = (state) => ({
//   appSetting: state.appSetting,
// });

// export default connect(mapStateToProps, {})(ModalCamera);
const ModalCameraMemo = memo(ModalCameraComponent, isEqual);
export const ModalCamera = ModalCameraMemo;
