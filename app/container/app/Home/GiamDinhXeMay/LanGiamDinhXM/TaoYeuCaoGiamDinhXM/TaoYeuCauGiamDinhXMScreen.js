import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiem, selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import {selectCities} from '@app/redux/slices/CitiesSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Icon, ModalChiNhanhTheoDangCay, ScreenComponent, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {ActivityIndicator, Al<PERSON>, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
import {ModalNguoiXuLy} from './Components';
import styles from './TaoYeuCauGiamDinhXMStyles';
import {geoCodeChuyenToaDoThanhDiaChi, geoFormatLaiDiaChi, requestCurrentLocation} from '@app/utils/LocationProvider';
import {logErrorTryCatch} from '@app/utils';
import {isRequiredFieldXaPhuong, NGAY_CHUYEN_DOI} from '@app/commons/Constant';

const TaoYeuCauGiamDinhScreenXMComponent = ({route}) => {
  console.log('TaoYeuCauGiamDinhScreenXMComponent');
  const {params} = route;
  const {lanGiamDinh, profileData} = params;
  const hoSoSauNgayChuyenDoi = profileData?.ho_so?.ngay_mo_hs >= NGAY_CHUYEN_DOI && profileData?.ho_so?.ngay_mo_hs >= profileData?.ho_so?.ngay_upd_dvi_hanh_chinh;

  const citiesData = useSelector(selectCities);
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const [openTinhThanh, setOpenTinhThanh] = useState(false);
  const [openQuanHuyen, setOpenQuanHuyen] = useState(false);
  const [listQuanHuyen, setListQuanHuyen] = useState(lanGiamDinh ? citiesData.find((tinhThanh) => tinhThanh.ma === lanGiamDinh.tinh_thanh).district : []);
  const [openXaPhuong, setOpenXaPhuong] = useState(false);
  const [listXaPhuong, setListXaPhuong] = useState(
    lanGiamDinh ? citiesData.find((tinhThanh) => tinhThanh.ma === lanGiamDinh.tinh_thanh).district.find((quanHuyen) => quanHuyen.ma === lanGiamDinh.quan_huyen).ward : [],
  );

  const [openListDoiTuong, setOpenListDoiTuong] = useState(false);
  const [listDoiTuong, setListDoiTuong] = useState(
    lanGiamDinh ? lanGiamDinh.doi_tuong_giam_dinh.split(',').map((item) => +item) : profileData.ds_doi_tuong.length === 1 ? [profileData.ds_doi_tuong[0].so_id_doi_tuong] : [''],
  );

  const chiNhanhBaoHiem = useSelector(selectChiNhanhBaoHiem);
  const [openDonViXuLy, setOpenDonViXuLy] = useState(false);

  const [openCanBoGiamDinh, setOpenCanBoGiamDinh] = useState(false);
  const [listCanBoGiamDinh, setListCanBoGiamDinh] = useState([]);

  const [visibleGioGiamDinh, setVisibleGioGiamDinh] = useState(false);
  const [visibleNgayGiamDinh, setVisibleNgayGiamDinh] = useState(false);

  const [dialogLoading, setDialogLoading] = useState(false);
  const [listMaDonViXuLySeleted, setListMaDonViXuLySelected] = useState([]); //list mã đơn vị xử lý được chọn

  const [openNguoiXuLy, setOpenNguoiXuLy] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);

  let refModalChiNhanhTheoDangCay = useRef(null);
  let refModalNguoiXuLy = useRef(null);

  const [disableBtnLayDiaChi, setDisableBtnLayDiaChi] = useState(false);

  useEffect(() => {
    if (lanGiamDinh) onChangeDonViGiamDinh('', '', lanGiamDinh.dvi_gdinh, true);
  }, []);

  useEffect(() => {
    let listDoiTuongTmp = listDoiTuong;
    if (listDoiTuong[0] === '') listDoiTuongTmp.shift();
    setValue('listDoiTuongGiamDinh', listDoiTuongTmp, {shouldValidate: true});
  }, [listDoiTuong]);

  useEffect(() => {
    initChiNhanhBaoHiemDangCay();
  }, []);
  useEffect(() => {
    if (listMaDonViXuLySeleted.length > 0) onChangeDonViGiamDinh('', '', listMaDonViXuLySeleted);
    else setListNguoiXuLy([]);
  }, [listMaDonViXuLySeleted]);

  const initChiNhanhBaoHiemDangCay = () => {
    // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
    let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay;
    chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
      return {
        ...item,
        listCon: [],
        isExpand: true,
        isCheck: false, //bỏ check or check
        hasChildCheck: false, //list chi nhánh cha, có child checked
        isShow: true,
      };
    });
    let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
    for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
      let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
      chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
    }
    // setListChiNhanhBaoHiem([...chiNhanhBaoHiemCha]);
    refModalChiNhanhTheoDangCay.current.setData(chiNhanhBaoHiemCha);
  };

  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
    let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
    if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
    if (listConLai.length === 0) return [];
    else {
      for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
      return listConFilter;
    }
  };

  useEffect(() => {
    let listDoiTuongTmp = listDoiTuong;
    if (listDoiTuong[0] === '') listDoiTuongTmp.shift();
    setValue('listDoiTuongGiamDinh', listDoiTuongTmp);
  }, [listDoiTuong]);

  const getDefaultFormValue = () => {
    let donViGiamDinh = [];
    return {
      tinhThanh: lanGiamDinh ? lanGiamDinh.tinh_thanh : null,
      quanHuyen: lanGiamDinh ? lanGiamDinh.quan_huyen : null,
      xaPhuong: lanGiamDinh ? lanGiamDinh.phuong_xa : null,
      diaDiemChiTiet: lanGiamDinh ? lanGiamDinh.dia_diem_gd : '',
      listDoiTuongGiamDinh: lanGiamDinh
        ? lanGiamDinh.doi_tuong_giam_dinh.split(',').map((item) => +item)
        : profileData.ds_doi_tuong.length === 1
        ? [+profileData.ds_doi_tuong[0].so_id_doi_tuong]
        : [''],
      ghiChu: lanGiamDinh ? lanGiamDinh.ghi_chu : '',
      donViGiamDinh: donViGiamDinh,
      canBoGiamDinh: lanGiamDinh ? lanGiamDinh.ma_gdv : null,
      ngayGiamDinh: null,
      gioGiamDinh: null,
    };
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onSubmit = async (data) => {
    let canBoGiamDinh = listNguoiXuLy.find((item) => item.ma === data.canBoGiamDinh);
    try {
      let bodyParams = {
        so_id: profileData.ho_so.so_id,
        lan_gd: lanGiamDinh ? lanGiamDinh.lan_gd : null,
        tinh_thanh: data.tinhThanh,
        quan_huyen: data.quanHuyen,
        phuong_xa: data.xaPhuong,
        dia_diem: data.diaDiemChiTiet,
        dvi_gdinh: canBoGiamDinh ? canBoGiamDinh.ma_chi_nhanh : '',
        ma_gdv: data.canBoGiamDinh,
        doi_tuong_gd: data.listDoiTuongGiamDinh.join(','),
        ghi_chu: data.ghiChu,
        gio_gd: moment(data.gioGiamDinh).format('HH:mm'),
        ngay_gd: +moment(data.ngayGiamDinh).format('YYYYMMDD'),
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TAO_MOI_LAN_GIAM_DINH_XM, bodyParams);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tạo mới lần giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const canBoGiamDinh = watch('canBoGiamDinh');

  const onChaneTinhThanh = (title, items, value) => {
    citiesData.forEach((city) => {
      if (city.ma === value) {
        setListQuanHuyen(city.district);
        return;
      }
    });
  };
  const onChangeQuanHuyen = (title, items, value) => {
    listQuanHuyen.forEach((quanHuyen) => {
      if (quanHuyen.ma === value) {
        setListXaPhuong(quanHuyen.ward);
        return;
      }
    });
  };
  const onOpenDropdown = (type) => {
    type !== 0 && openTinhThanh && setOpenTinhThanh(false);
    type !== 1 && openQuanHuyen && setOpenQuanHuyen(false);
    type !== 2 && openXaPhuong && setOpenXaPhuong(false);
    type !== 3 && openListDoiTuong && setOpenListDoiTuong(false);
    type !== 4 && openDonViXuLy && setOpenDonViXuLy(false);
    type !== 5 && openCanBoGiamDinh && setOpenCanBoGiamDinh(false);

    if (type === 1 && listQuanHuyen.length === 0) setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    if (type === 2 && listXaPhuong.length === 0) {
      setError('quanHuyen', {type: 'required', message: 'Thông tin bắt buộc'});
      listQuanHuyen.length === 0 && setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    }
    if (type === 5 && listCanBoGiamDinh.length === 0 && !getValues('donViGiamDinh')) setError('donViGiamDinh', {type: 'required', message: 'Thông tin bắt buộc'});
  };

  const getDisplayTextDoiTuong = (listDoiTuongSelected) => {
    if (!listDoiTuongSelected) return '';
    if (listDoiTuongSelected[0] === '') return 'Chọn đối tượng';
    let displayText = [];
    profileData.ds_doi_tuong.forEach((doiTuong) => {
      if (listDoiTuongSelected.includes(doiTuong.so_id_doi_tuong)) displayText.push(doiTuong.ten_doi_tuong);
    });
    return displayText.join(' ; ');
  };

  //firstCall : biến kiểm tra xem có phải gọi lần đầu khi có lanGiamDinh không
  const onChangeDonViGiamDinh = async (title, items, item) => {
    if (!item) return;
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      ma_chi_nhanh: item.join(','),
    };
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
      if (openNguoiXuLy) setOpenNguoiXuLy(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let giamDinhVien = response.data_info.map((item) => {
        item.value = item.ma;
        item.label = item.ten + (item.ten_chuc_danh ? ` (${item.ten_chuc_danh})` : '');
        return item;
      });
      setListNguoiXuLy([...giamDinhVien]);
      setValue('canBoGiamDinh', null);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getTenHienThi = (value, data = []) => {
    let name = '';
    data.map((e) => {
      if (e.ma === value) name = e.ten;
    });
    return name;
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const onPressLayDiaChiHienTai = () => {
    requestCurrentLocation(
      async (position) => {
        setDisableBtnLayDiaChi(true);
        let response = await geoCodeChuyenToaDoThanhDiaChi({
          lat: position.coords.latitude,
          lon: position.coords.longitude,
        });

        // console.log('response', response);
        setDisableBtnLayDiaChi(false);
        if (response) {
          if (response.data) {
            let diaChiFormat = {
              maTinhThanh: null,
              maQuanHuyen: null,
              maXaPhuong: null,
              diaChiDayDu: null,
            };
            diaChiFormat = geoFormatLaiDiaChi(response.data);
            // LOG RA LỖI NẾU KHÔNG FILL ĐỦ DATA VÀO
            if (response.data.error || diaChiFormat?.maTinhThanh === null || diaChiFormat?.maQuanHuyen === null || diaChiFormat?.maXaPhuong === null) {
              logErrorTryCatch({
                code: 'GEOCODE_KHAI_BAO_TON_THAT',
                message: JSON.stringify(response.data),
              });
            }
            if (diaChiFormat.maTinhThanh) {
              citiesData.forEach((itemTinhThanh) => {
                if (itemTinhThanh.ma === diaChiFormat.maTinhThanh) {
                  setValue('tinhThanh', itemTinhThanh.ma, {shouldValidate: true}); //set Tỉnh thành được chọn
                  setListQuanHuyen([...itemTinhThanh.district]); //set list quận huyện của tỉnh thành
                  //nếu có quận huyện được chọn
                  if (diaChiFormat.maQuanHuyen) {
                    let listQuanHuyen = itemTinhThanh.district;
                    listQuanHuyen.forEach((itemQuanHuyen) => {
                      if (itemQuanHuyen.ma === diaChiFormat.maQuanHuyen) {
                        setValue('quanHuyen', itemQuanHuyen.ma, {shouldValidate: true}); //set quận huyện được chọn
                        setListXaPhuong([...itemQuanHuyen.ward]); //set list xã phường của quận huyện
                        if (diaChiFormat.maXaPhuong) setValue('xaPhuong', diaChiFormat.maXaPhuong, {shouldValidate: true});
                      }
                    });
                  }
                }
              });
            }
          } else FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa tồn tại địa chỉ tại địa điểm này. Vui lòng thử lại');
        }
      },
      (error) => logErrorTryCatch(error),
    );
  };

  // RENDER
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderThongTinGiamDinh = () => (
    <>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin giám định" style={styles.subLabel} />
      </View>
      <View style={{marginHorizontal: spacing.small}}>
        <View flexDirection="row">
          <View flex={1} marginRight={10}>
            <Controller
              control={control}
              name="gioGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <>
                  <TextInputOutlined
                    isRequired
                    isDateTimeField
                    isTouchableOpacity
                    editable={false}
                    value={value ? moment(value).format('HH:mm') : ''}
                    title="Giờ giám định"
                    placeholder="Chọn giờ"
                    onPress={() => setVisibleGioGiamDinh(true)}
                    inputStyle={{color: colors.BLACK}}
                    error={errors.gioGiamDinh && getErrMessage('gioGiamDinh', errors.gioGiamDinh.type)}
                  />
                  {renderDateTimeComp(visibleGioGiamDinh, setVisibleGioGiamDinh, onChange, value || new Date(), 'time', null, null, 0)}
                </>
              )}
            />
          </View>
          <View flex={1}>
            <Controller
              control={control}
              name="ngayGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => {
                return (
                  <>
                    <TextInputOutlined
                      title="Ngày giám định"
                      isTouchableOpacity={true}
                      onPress={() => setVisibleNgayGiamDinh(true)}
                      value={value ? moment(value).format('DD/MM/YYYY') : ''}
                      editable={false}
                      isRequired={true}
                      isDateTimeField
                      inputStyle={{color: colors.BLACK}}
                      placeholder="Chọn ngày"
                      error={errors.ngayGiamDinh && getErrMessage('ngayGiamDinh', errors.ngayGiamDinh.type)}
                    />
                    {renderDateTimeComp(visibleNgayGiamDinh, setVisibleNgayGiamDinh, onChange, value || new Date(), 'date')}
                  </>
                );
              }}
            />
          </View>
        </View>
        <Controller
          control={control}
          name="tinhThanh"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <View style={{zIndex: 10000, flexDirection: 'row', alignItems: 'flex-start'}}>
              <DropdownPicker
                title="Tỉnh thành"
                placeholder="Chọn tỉnh thành"
                zIndex={10000}
                isOpen={openTinhThanh}
                setOpen={setOpenTinhThanh}
                items={citiesData.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                schema={{
                  label: 'ten',
                  value: 'ma',
                }}
                isRequired
                onChangeValue={onChaneTinhThanh}
                itemSelected={value}
                setItemSelected={(dispatch) => onChange(dispatch())}
                onOpen={() => onOpenDropdown(0)}
                inputErr={errors.tinhThanh && getErrMessage('tinhThanh', errors.tinhThanh.type)}
                containerStyle={{marginBottom: openTinhThanh ? 300 : spacing.smaller, flex: 1, marginTop: 0}}
              />
              <TouchableOpacity style={{marginTop: spacing.large, marginLeft: spacing.smaller}} onPress={onPressLayDiaChiHienTai} disabled={disableBtnLayDiaChi}>
                {!disableBtnLayDiaChi ? <Icon.Entypo name="location" color={colors.PRIMARY} size={30} /> : <ActivityIndicator size="large" color={colors.PRIMARY} />}
              </TouchableOpacity>
            </View>
          )}
        />
        <Controller
          control={control}
          name="quanHuyen"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title={hoSoSauNgayChuyenDoi ? 'Xã phường' : 'Quận huyện'}
              placeholder={`Chọn ${hoSoSauNgayChuyenDoi ? 'Xã phường' : 'Quận huyện'}`}
              zIndex={9000}
              isOpen={openQuanHuyen}
              setOpen={setOpenQuanHuyen}
              items={listQuanHuyen.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              isRequired
              onChangeValue={onChangeQuanHuyen}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(hoSoSauNgayChuyenDoi ? 2 : 1)}
              inputErr={errors.quanHuyen && getErrMessage('quanHuyen', errors.quanHuyen.type)}
            />
          )}
        />
        {/* <Controller
          control={control}
          name="xaPhuong"
          rules={{
            required: isRequiredFieldXaPhuong,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              isRequired={isRequiredFieldXaPhuong}
              title="Xã phường"
              placeholder="Chọn xã phường"
              zIndex={8000}
              isOpen={openXaPhuong}
              setOpen={setOpenXaPhuong}
              items={listXaPhuong}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(2)}
              inputErr={errors.xaPhuong && getErrMessage('xaPhuong', errors.xaPhuong.type)}
            />
          )}
        /> */}

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="diaDiemChiTiet"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Địa điểm chi tiết"
              placeholder="Nhập địa điểm chi tiết"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.diaDiemChiTiet && getErrMessage('diaDiemChiTiet', errors.diaDiemChiTiet.type)}
            />
          )}
        />
        <Controller
          control={control}
          name="listDoiTuongGiamDinh"
          rules={{
            required: true,
          }}
          render={({field: {value}}) => (
            <DropdownPicker
              title="Đối tượng"
              placeholder="Chọn đối tượng"
              zIndex={7000}
              isOpen={openListDoiTuong}
              setOpen={setOpenListDoiTuong}
              items={profileData.ds_doi_tuong}
              searchable={false}
              schema={{
                label: 'ten_doi_tuong',
                value: 'so_id_doi_tuong',
              }}
              isRequired
              itemSelected={value}
              setItemSelected={setListDoiTuong}
              onOpen={() => onOpenDropdown(3)}
              inputErr={errors.listDoiTuongGiamDinh && getErrMessage('listDoiTuongGiamDinh', errors.listDoiTuongGiamDinh.type)}
              multiple={true}
              multipleText={getDisplayTextDoiTuong(value)}
              maxHeight={100}
              containerStyle={{marginBottom: openListDoiTuong ? 100 : spacing.small}}
            />
          )}
        />
        <Controller
          control={control}
          name="ghiChu"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView, {zIndex: 4000}]}
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              value={value}
              onChangeText={onChange}
              returnKeyType={'next'}
              blurOnSubmit={false}
              multiline={true}
            />
          )}
        />
      </View>
    </>
  );
  const renderThongTinCanBoGiamDinh = () => (
    <View style={{zIndex: 6000}}>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin cán bộ giám định" style={styles.subLabel} />
      </View>
      <View style={{marginHorizontal: spacing.small, zIndex: 6000}}>
        <Controller
          control={control}
          rules={{
            required: false,
          }}
          name="donViGiamDinh"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              isTouchableOpacity
              editable={false}
              isDropdown
              title="Đơn vị giám định"
              value={value.length === 0 ? 'Chọn đơn vị giám định' : value.length === 1 ? value[0].ten_chi_nhanh : `Có ${value.length} đơn vị được chọn`}
              placeholder="Đơn vị giám định"
              onPress={() => refModalChiNhanhTheoDangCay.current.show()}
            />
          )}
        />

        <Controller
          control={control}
          name="canBoGiamDinh"
          rules={{
            required: false,
          }}
          render={({field: {value, onChange}}) => {
            let cleared = value && value !== null && value !== '';
            return (
              <TextInputOutlined
                cleared={cleared}
                isDropdown
                isTouchableOpacity
                onPressClear={() => setValue('canBoGiamDinh', null)}
                editable={false}
                title="Cán bộ giám định hiện trường"
                placeholder="Chọn cán bộ giám định"
                inputStyle={{color: colors.BLACK}}
                value={getTenHienThi(value, listNguoiXuLy)}
                onPress={() => refModalNguoiXuLy.current.show()}
              />
            );
          }}
        />
      </View>
    </View>
  );
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={!lanGiamDinh ? 'Tạo yêu cầu giám định' : 'Yêu cầu giám định'}
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
            <View style={styles.content}>
              {renderThongTinGiamDinh()}
              {renderThongTinCanBoGiamDinh()}
            </View>
          </KeyboardAwareScrollView>
          <View style={styles.btnView}>
            <ButtonLinear title={lanGiamDinh ? 'Cập nhật' : 'Tạo mới'} onPress={handleSubmit(onSubmit)} linearStyle={{marginHorizontal: spacing.small}} />
          </View>
          <ModalChiNhanhTheoDangCay
            ref={refModalChiNhanhTheoDangCay}
            showCheckCha={true}
            multiple={false}
            setListMaDonViXuLySelected={setListMaDonViXuLySelected}
            setListItemDonViXulySelected={(value) => setValue('donViGiamDinh', value, {shouldValidate: true})}
          />
          <ModalNguoiXuLy
            value={canBoGiamDinh}
            data={listNguoiXuLy}
            ref={refModalNguoiXuLy}
            onBackPress={() => refModalNguoiXuLy.current.hide()}
            setValue={(val) => setValue('canBoGiamDinh', val.ma, {shouldValidate: true})}
          />
        </SafeAreaView>
      }
    />
  );
};

export const TaoYeuCauGiamDinhXMScreen = memo(TaoYeuCauGiamDinhScreenXMComponent, isEqual);
