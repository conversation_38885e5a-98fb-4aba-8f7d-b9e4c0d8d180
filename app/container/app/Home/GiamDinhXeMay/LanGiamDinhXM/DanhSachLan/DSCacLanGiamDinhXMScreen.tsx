import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import styles from './DSCacLanGiamDinhXMStyles';

const DanhSachCacLanGiamDinhXMScreenComponent = ({route, navigation}) => {
  const {profileData} = route.params;
  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [listLanGiamDinh, setListLanGiamDinh] = useState([]);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
    });
  }, []);

  const getData = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_CAC_LAN_GIAM_DINH_XM, params);
      setRefreshing(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info;
      setListLanGiamDinh(arrData);
    } catch (error) {
      setRefreshing(false);
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onRefresh = () => {
    getData();
  };

  const xoaLanGiamDinh = async (item) => {
    Alert.alert('Xoá lần giám định', 'Bạn có chắc chắn muốn xoá lần giám định này hay không?', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          setDialogLoading(true);
          try {
            let params = {
              so_id: profileData?.ho_so?.so_id,
              lan_gd: item.lan_gd,
              ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
            };
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_LAN_GD_XM, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            getData();
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const renderItemLanGiamDinh = ({item}) => {
    const statusColorText = item.ngay_kt < 30000101 ? colors.GREEN : item.ngay_kt >= 30000101 ? colors.RED1 : null;
    const renderLabel = (title: string, value: any, style = null) => {
      return (
        <Text style={[styles.label]}>
          {title}: <Text style={[styles.detail, style]} children={value} />
        </Text>
      );
    };
    return (
      <View style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
            {renderLabel('Lần GĐ', item.ten_lan_gd, styles.txtSoHS)}
            <TouchableOpacity onPress={() => xoaLanGiamDinh(item)}>
              <Icon.FontAwesome name="trash-o" size={24} color={colors.RED1} />
            </TouchableOpacity>
          </View>
          {renderLabel('Ngày GĐ', item.ngay_gd)}
          {/* {renderLabel('Ngày KT', item.ngay_kt !== 30000101 ? moment(item.ngay_kt, 'YYYYMMDD').format('DD/MM/YYYY') : '')} */}
          {renderLabel('Cán bộ GĐ', item.ten_gdv)}
          {renderLabel('Địa điểm GĐ', item.dia_diem)}
          {renderLabel('Đối tượng', item.doi_tuong_gd)}
          {renderLabel('Trạng thái', item.trang_thai, {color: statusColorText})}
        </View>
      </View>
    );
  };

  // footer

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Các lần giám định Xe máy"
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            data={listLanGiamDinh}
            renderItem={renderItemLanGiamDinh}
            onEndReachedThreshold={0.5}
            keyExtractor={(item, index) => index.toString()}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
          />
        </SafeAreaView>
      }
      footer={<ButtonLinear title="Tạo yêu cầu giám định" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TAO_YEU_CAU_GIAM_DINH_XM, {profileData: profileData})} linearStyle={styles.footerBtn} />}
    />
  );
};

export const DanhSachCacLanGiamDinhXMScreen = memo(DanhSachCacLanGiamDinhXMScreenComponent, isEqual);
