import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },

  txtButton: {
    fontWeight: '700',
    color: colors.WHITE,
  },
  radioView: {
    marginLeft: 5,
    marginTop: 10,
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  profileItemView: {
    paddingHorizontal: spacing.smaller,
    paddingVertical: spacing.smaller,
    marginVertical: spacing.tiny,
    backgroundColor: '#FFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
  },
  profileItemCenterView: {
    flex: 1,
    paddingRight: 5,
    paddingVertical: 5,
    borderBottomColor: colors.GRAY4,
  },
  profileItemRightView: {
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: colors.GRAY4,
  },
  profileItemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  profileTxtHoSo: {
    fontSize: 14,
    marginBottom: 4,
    color: colors.BLACK_03,
  },
  profileImgClock: {
    width: 14,
    height: 14,
    opacity: 0.8,
    marginRight: 5,
  },
  label: {
    fontSize: 14,
    marginBottom: 5,
    fontWeight: '500',
    color: colors.GRAY6,
  },
  noDataView: {
    alignItems: 'center',
  },
  btnSearch: {
    borderRadius: 5,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
  },
  txtTimKiem: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
    textTransform: 'uppercase',
  },
  txtHeaderList: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.BLACK_03,
  },
  date: {
    flex: 1,
    color: colors.PRIMARY,
  },
  txtSoHS: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  renderHeader: {
    marginBottom: 5,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    borderBottomWidth: 0.2,
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  detail: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.BLACK_03,
    lineHeight: 20,
  },
});
