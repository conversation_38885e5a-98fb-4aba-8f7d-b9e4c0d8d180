import {colors} from '@app/commons/Theme';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    paddingBottom: vScale(spacing.small),
  },
  inputView: {
    flex: 1,
    marginBottom: vScale(spacing.small),
  },
  btnView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },
  subLabel: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: FontSize.size16,
    marginVertical: vScale(spacing.tiny),
    paddingHorizontal: scale(spacing.small),
  },
  headerTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: vScale(2),
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
    paddingVertical: vScale(spacing.smaller),
  },
  doubleInputRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: scale(spacing.small),
  },
  inputStyles: {
    minHeight: 60,
    textAlignVertical: 'top',
  },
  titleLabel: {
    fontWeight: '700',
    marginVertical: spacing.small,
  },
  titleSubLabel: {
    fontWeight: '500',
    marginVertical: spacing.small,
    fontStyle: 'italic',
    color: colors.PRIMARY,
  },
});
