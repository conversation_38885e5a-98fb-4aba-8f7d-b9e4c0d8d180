import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Platform, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonHangGPLXComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, data, setArrChecked, arrChecked} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [dataGPLX, setDataGPLX] = useState(data);

  useEffect(() => {
    data.forEach((item) => {
      item.isChecked = arrChecked.findIndex((x) => x.ma == item.ma) != -1;
    });
    setDataGPLX(data);
  }, []);

  const onChangeCheckBoxValue = (item, index, value) => {
    let dataUpdate = [...dataGPLX];
    dataUpdate[index].isChecked = value;
    setDataGPLX(dataUpdate);
    const isCheckedDataArr = dataGPLX.filter((item) => item.isChecked === true);
    setArrChecked && setArrChecked(isCheckedDataArr);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Chọn hạng GPLX" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = (item, index) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={(value) => onChangeCheckBoxValue(item, index, !item.isChecked)}>
        <CheckboxComp value={item.isChecked} checkboxStyle={styles.checkbox} onValueChange={(value) => onChangeCheckBoxValue(item, index, value)} />
        <Text style={{color: item.isChecked ? colors.PRIMARY : colors.BLACK_03, marginTop: Platform.OS == 'android' ? 4 : 2}}>{item.ten}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View>{dataGPLX.map((item, index) => renderItem(item, index))}</View>
      </ScrollView>
    );
  };
  return (
    <Modal
      ///onModalShow
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height * 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 25,
    borderColor: colors.GRAY,
    margin: scale(spacing.medium),
    paddingLeft: scale(spacing.medium),
  },

  itemHangMucView: {
    flexDirection: 'row',
    paddingVertical: vScale(8),
    paddingHorizontal: scale(5),
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'center',
    marginLeft: scale(30),
    fontSize: FontSize.size16,
    marginVertical: vScale(15),
  },
  closeView: {
    borderRadius: 25,
    marginRight: scale(15),
    paddingVertical: vScale(1),
    paddingHorizontal: scale(2),
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: scale(8),
  },
  content: {
    margin: scale(10),
    paddingBottom: vScale(20),
  },
});

export const ModalChonHangGPLX = ModalChonHangGPLXComponent;
