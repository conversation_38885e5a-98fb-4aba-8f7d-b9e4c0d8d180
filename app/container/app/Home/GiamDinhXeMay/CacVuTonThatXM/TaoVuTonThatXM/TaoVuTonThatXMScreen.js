import {FORMAT_DATE_TIME, NGAY_CHUYEN_DOI, REGUlAR_EXPRESSION, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectDanhMucSanPhamXe, selectSuKienBaoHiem} from '@app/redux/slices/CategoryCommonSlice';
import {selectCities} from '@app/redux/slices/CitiesSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {scale, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Icon, ScreenComponent, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {ActivityIndicator, Alert, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
import {ModalChonHangGPLX, ModalChonNhomNguyenNhan, ModalChonNhomSuKienBaoHiem} from './Component';
import styles from './TaoVuTonThatXMStyles';
import {geoCodeChuyenToaDoThanhDiaChi, geoFormatLaiDiaChi, requestCurrentLocation} from '@app/utils/LocationProvider';
import {logErrorTryCatch} from '@app/utils';

const hangGPLX = [{ten: 'A4'}, {ten: 'B2'}, {ten: 'B1'}, {ten: 'C'}, {ten: 'D'}, {ten: 'E'}, {ten: 'F'}];

const TaoVuTonThatXMScreenComponent = ({route, navigation}) => {
  console.log('TaoVuTonThatXMScreenComponent');
  const {itemSelected, profileData} = route.params || {};

  const hoSoSauNgayChuyenDoi = profileData?.ho_so?.ngay_mo_hs >= NGAY_CHUYEN_DOI && profileData?.ho_so?.ngay_mo_hs >= profileData?.ho_so?.ngay_upd_dvi_hanh_chinh;

  const citiesData = useSelector(selectCities);
  const danhMucSanPhamXe = useSelector(selectDanhMucSanPhamXe);
  const danhSachSuKienBaoHiem = useSelector(selectSuKienBaoHiem);

  const [openTinhThanh, setOpenTinhThanh] = useState(false);
  const [openQuanHuyen, setOpenQuanHuyen] = useState(false);
  const [listQuanHuyen, setListQuanHuyen] = useState([]);
  const [openXaPhuong, setOpenXaPhuong] = useState(false);
  const [listXaPhuong, setListXaPhuong] = useState([]);

  const [openListDoiTuong, setOpenListDoiTuong] = useState(false);
  const [openDonViXuLy, setOpenDonViXuLy] = useState(false);
  const [openNguoiXuLy, setOpenNguoiXuLy] = useState(false);
  const nhomNguyenNhan = danhMucSanPhamXe;

  const [dialogLoading, setDialogLoading] = useState(false);

  const [toggleGioXR, setToggleGioXR] = useState(false);
  const [toggleNgayXR, setToggleNgayXR] = useState(false);
  const [toggleNgayCapGPLX, setToggleNgayCapGPLX] = useState(false);
  const [toggleNgayHetHanGPLX, setToggleNgayHetHanGPLX] = useState(false);
  const [listHangGPLX, setListHangGPLX] = useState([]);
  const [tenHangGPLX, setTenHangGPLX] = useState(itemSelected?.gplx_hang);
  const [dsNguoiLamChung, setDsNguoiLamChung] = useState([]);
  const [listSuKienBaoHiem, setListSuKienBaoHiem] = useState([]);

  const [disableBtnLayDiaChi, setDisableBtnLayDiaChi] = useState(false);

  let refModalChonHangGPLX = useRef(null);
  let refModalChonNhomNguyenNhan = useRef(null);
  let refModalChonNhomSuKienBaoHiem = useRef();

  const getDefaultFormValue = () => {
    const DEFAULT_DATE = 30000101;
    return {
      tinhThanh: itemSelected ? itemSelected?.tinh_thanh : null,
      quanHuyen: itemSelected ? itemSelected?.quan_huyen : null,
      xaPhuong: itemSelected ? itemSelected?.phuong_xa : null,
      diaDiemXayRa: itemSelected?.dia_diem ? itemSelected?.dia_diem : '',
      gioXayRa: itemSelected?.gio_xr ? moment(itemSelected?.gio_xr, 'HH:mm').toDate() : null,
      ngayXayRa: itemSelected?.ngay_xr ? moment(itemSelected.ngay_xr, FORMAT_DATE_TIME.API_DATE_FORMAT).toDate() : null,
      nguyenNhan: itemSelected?.nguyen_nhan ? itemSelected?.nguyen_nhan : '',
      hauQua: itemSelected?.hau_qua ? itemSelected?.hau_qua : '',
      hoTenLaiXe: itemSelected?.ten_lxe ? itemSelected?.ten_lxe : '',
      dienThoai: itemSelected?.dthoai_lxe ? itemSelected?.dthoai_lxe : '',
      email: itemSelected?.email_lxe ? itemSelected?.email_lxe : '',
      ngayCapGPLX: itemSelected?.gplx_hieu_luc && itemSelected?.gplx_hieu_luc < DEFAULT_DATE ? moment(itemSelected?.gplx_hieu_luc, FORMAT_DATE_TIME.API_DATE_FORMAT).toDate() : '',
      ngayHetHanGPLX: itemSelected?.gplx_het_han && itemSelected?.gplx_het_han < DEFAULT_DATE ? moment(itemSelected?.gplx_het_han, FORMAT_DATE_TIME.API_DATE_FORMAT).toDate() : '',
      nhomNguyenNhan: itemSelected?.nhom_nguyen_nhan ? itemSelected?.nhom_nguyen_nhan : '',
      soGPLX: itemSelected?.gplx_so ? itemSelected?.gplx_so : '',
      hangGPLX: itemSelected?.gplx_hang ? itemSelected?.gplx_hang : '',
      hauQuaNguoiThuBa: itemSelected ? itemSelected?.hau_qua_ntba : '',
      nhomSuKien: itemSelected ? itemSelected?.nhom_su_kien : '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  useEffect(() => {
    if (listHangGPLX.length > 0) {
      const listTenHang = listHangGPLX.map((item) => {
        return item.ten;
      });
      let tenHang = JSON.stringify(listTenHang);
      setTenHangGPLX(tenHang.replace(/[\[\]'"]+/g, ''));
    }
  }, [listHangGPLX]);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDanhSachNguoiLamChung();
    });
    //init sự kiện bảo hiểm

    let newArrData = JSON.parse(JSON.stringify(danhSachSuKienBaoHiem));
    newArrData.map((e, index) => {
      newArrData[index].isChecked = false;
      if (nhomSuKien !== null && nhomSuKien !== '') {
        let arrHm = nhomSuKien.split(',');
        arrHm.map((x) => {
          if (x == e.bt) {
            newArrData[index].isChecked = true;
          }
        });
      }
    });
    setListSuKienBaoHiem([...newArrData]);
  }, []);

  const tinhThanh = watch('tinhThanh');
  const nhomNguyenNhanSelected = watch('nhomNguyenNhan');
  const nhomSuKien = watch('nhomSuKien');

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'dienThoai' && errType === 'pattern') return 'Không đúng định dạng';
    if (inputName === 'email' && errType === 'pattern') return 'Không đúng định dạng';
    return '';
  };

  const getDanhSachNguoiLamChung = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        vu_tt: itemSelected ? itemSelected.vu_tt : '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_NGUOI_LAM_CHUNG_XE_MAY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info;
      setDsNguoiLamChung(arrData);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onSubmit = async (data) => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData.ho_so.so_id,
        vu_tt: itemSelected ? itemSelected.vu_tt : 0,
        gio_xr: moment(data.gioXayRa).format('HH:mm'),
        ngay_xr: +moment(data.ngayXayRa).format(FORMAT_DATE_TIME.API_DATE_FORMAT),
        tinh_thanh: data.tinhThanh,
        quan_huyen: data.quanHuyen,
        phuong_xa: data.xaPhuong,
        dia_diem: data.diaDiemXayRa,
        nhom_nguyen_nhan: data.nhomNguyenNhan,
        nguyen_nhan: data.nguyenNhan,
        hau_qua: data.hauQua,
        ten_lxe: data.hoTenLaiXe,
        dthoai_lxe: data.dienThoai,
        email_lxe: data.email,
        dchi_lxe: '',
        gplx_so: data.soGPLX,
        gplx_hang: tenHangGPLX,
        gplx_hieu_luc: +moment(data.ngayCapGPLX).format(FORMAT_DATE_TIME.API_DATE_FORMAT),
        gplx_het_han: +moment(data.ngayHetHanGPLX).format(FORMAT_DATE_TIME.API_DATE_FORMAT),
        ghi_chu: '',
        nhom_su_kien: data.nhomSuKien,
        hau_qua_ntba: data.hauQuaNguoiThuBa,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_VU_TON_THAT_XE_MAY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật vụ tổn thất thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeTinhThanh = (title, items, value) => {
    citiesData.forEach((city) => {
      if (city.ma === value) {
        setListQuanHuyen(city.district);
        return;
      }
    });
  };

  useEffect(() => {
    citiesData.forEach((city) => {
      if (city.ma === tinhThanh) {
        setListQuanHuyen(city.district);
        return;
      }
    });
  }, [tinhThanh]);

  const onChangeQuanHuyen = (title, items, value) => {
    listQuanHuyen.forEach((quanHuyen) => {
      if (quanHuyen.ma === value) {
        setListXaPhuong(quanHuyen.ward);
        return;
      }
    });
  };

  const onOpenDropdown = (type) => {
    type !== 0 && openTinhThanh && setOpenTinhThanh(false);
    type !== 1 && openQuanHuyen && setOpenQuanHuyen(false);
    type !== 2 && openXaPhuong && setOpenXaPhuong(false);
    type !== 3 && openListDoiTuong && setOpenListDoiTuong(false);
    type !== 4 && openDonViXuLy && setOpenDonViXuLy(false);
    type !== 5 && openNguoiXuLy && setOpenNguoiXuLy(false);

    if (type === 1 && listQuanHuyen.length === 0) setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    if (type === 2 && listXaPhuong.length === 0) {
      setError('quanHuyen', {type: 'required', message: 'Thông tin bắt buộc'});
      listQuanHuyen.length === 0 && setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    }
    // if (type === 5 && listNguoiXuLy.length === 0 && !getValues('donViGiamDinh')) setError('donViGiamDinh', {type: 'required', message: 'Thông tin bắt buộc'});
  };
  // const getDisplayTextDoiTuong = (listDoiTuongSelected) => {
  //   if (!listDoiTuongSelected) return '';
  //   if (listDoiTuongSelected[0] === '') return 'Chọn đối tượng';
  //   let displayText = [];
  //   listDoiTuongSelected.forEach((idDoiTuong) => {
  //     profileData.ds_doi_tuong.forEach((doiTuong) => {
  //       if (idDoiTuong === doiTuong.so_id_doi_tuong) {
  //         displayText.push(doiTuong.ten_doi_tuong);
  //       }
  //     });
  //   });
  //   return displayText.join(' ; ');
  // };

  const getDisplayTextNhomNguyenNhan = (val) => {
    let text = '';
    nhomNguyenNhan.map((e) => {
      if (e.ma == val) {
        text = e.ten;
      }
    });
    return text;
  };

  const getTenHienThiNhomSuKien = (value, data) => {
    let name = '';
    if (value && value !== '') {
      const filterData = data.filter((e) => e.isChecked);
      const arrTenSuKien = filterData.map((item) => {
        return item.ten_sk;
      });
      name = arrTenSuKien.toString();
    }
    return name;
  };

  const onSetValueSuKienBaoHiem = (arr) => {
    if (arr.length > 0) {
      let filterMaSuKien = arr.filter((e) => e.isChecked);
      const maSuKien = filterMaSuKien.map((item) => {
        return item.bt;
      });
      let ma = maSuKien.toString();
      setValue('nhomSuKien', ma);
    } else {
      setValue('nhomSuKien', '');
    }
    setListSuKienBaoHiem(arr);
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate, type) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const onSelectedNhomNguyenNhan = (val) => {
    setValue('nhomNguyenNhan', val.ma, {shouldValidate: true});
  };

  const onInputFocus = () => {
    setOpenTinhThanh(false);
    setOpenQuanHuyen(false);
    setOpenXaPhuong(false);
  };

  const onPressLayDiaChiHienTai = () => {
    requestCurrentLocation(
      async (position) => {
        setDisableBtnLayDiaChi(true);
        let response = await geoCodeChuyenToaDoThanhDiaChi({
          lat: position.coords.latitude,
          lon: position.coords.longitude,
        });

        // console.log('response', response);
        setDisableBtnLayDiaChi(false);
        if (response) {
          if (response.data) {
            let diaChiFormat = {
              maTinhThanh: null,
              maQuanHuyen: null,
              maXaPhuong: null,
              diaChiDayDu: null,
            };
            diaChiFormat = geoFormatLaiDiaChi(response.data);
            // LOG RA LỖI NẾU KHÔNG FILL ĐỦ DATA VÀO
            if (response.data.error || diaChiFormat?.maTinhThanh === null || diaChiFormat?.maQuanHuyen === null || diaChiFormat?.maXaPhuong === null) {
              logErrorTryCatch({
                code: 'GEOCODE_KHAI_BAO_TON_THAT',
                message: JSON.stringify(response.data),
              });
            }
            if (diaChiFormat.maTinhThanh) {
              citiesData.forEach((itemTinhThanh) => {
                if (itemTinhThanh.ma === diaChiFormat.maTinhThanh) {
                  setValue('tinhThanh', itemTinhThanh.ma, {shouldValidate: true}); //set Tỉnh thành được chọn
                  setListQuanHuyen([...itemTinhThanh.district]); //set list quận huyện của tỉnh thành
                  //nếu có quận huyện được chọn
                  if (diaChiFormat.maQuanHuyen) {
                    let listQuanHuyen = itemTinhThanh.district;
                    listQuanHuyen.forEach((itemQuanHuyen) => {
                      if (itemQuanHuyen.ma === diaChiFormat.maQuanHuyen) {
                        setValue('quanHuyen', itemQuanHuyen.ma, {shouldValidate: true}); //set quận huyện được chọn
                        setListXaPhuong([...itemQuanHuyen.ward]); //set list xã phường của quận huyện
                        if (diaChiFormat.maXaPhuong) setValue('xaPhuong', diaChiFormat.maXaPhuong, {shouldValidate: true});
                      }
                    });
                  }
                }
              });
            }
          } else FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa tồn tại địa chỉ tại địa điểm này. Vui lòng thử lại');
        }
      },
      (error) => logErrorTryCatch(error),
    );
  };

  // RENDER
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderThongTinGiamDinh = () => (
    <>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin vụ tổn thất" style={styles.subLabel} />
      </View>

      <View style={styles.doubleInputRowView}>
        <View flex={1} marginRight={scale(spacing.smaller)}>
          <Controller
            control={control}
            name="gioXayRa"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <>
                <TextInputOutlined
                  isDateTimeField
                  editable={false}
                  isRequired={true}
                  isTouchableOpacity
                  title="Giờ xảy ra"
                  returnKeyType={'next'}
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[styles.inputView]}
                  onPress={() => setToggleGioXR(true)}
                  value={value ? moment(value).format('HH:mm') : ''}
                  error={errors.gioXayRa && getErrMessage('gioXayRa', errors.gioXayRa.type)}
                  placeholder="Chọn giờ"
                />
                {renderDateTimeComp(toggleGioXR, setToggleGioXR, onChange, value || new Date(), 'time', null, null, 0)}
              </>
            )}
          />
        </View>
        <View flex={1}>
          <Controller
            control={control}
            name="ngayXayRa"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <>
                <TextInputOutlined
                  isDateTimeField
                  editable={false}
                  isRequired={true}
                  isTouchableOpacity
                  title="Ngày xảy ra"
                  returnKeyType={'next'}
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[styles.inputView]}
                  onPress={() => setToggleNgayXR(true)}
                  value={value ? moment(value).format('DD/MM/YYYY') : ''}
                  error={errors.ngayXayRa && getErrMessage('ngayXayRa', errors.ngayXayRa.type)}
                  placeholder="Chọn ngày"
                />
                {renderDateTimeComp(toggleNgayXR, setToggleNgayXR, onChange, value || new Date(), 'date', null, new Date(), 0)}
              </>
            )}
          />
        </View>
      </View>
      <View style={{marginHorizontal: spacing.small}}>
        <Controller
          control={control}
          name="tinhThanh"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <View style={{zIndex: 10000, flexDirection: 'row', alignItems: 'flex-start'}}>
              <DropdownPicker
                title="Tỉnh thành"
                placeholder="Chọn tỉnh thành"
                zIndex={10000}
                isOpen={openTinhThanh}
                setOpen={setOpenTinhThanh}
                items={citiesData.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                schema={{
                  label: 'ten',
                  value: 'ma',
                }}
                isRequired
                onChangeValue={onChangeTinhThanh}
                itemSelected={value}
                setItemSelected={(dispatch) => onChange(dispatch())}
                onOpen={() => onOpenDropdown(0)}
                inputErr={errors.tinhThanh && getErrMessage('tinhThanh', errors.tinhThanh.type)}
                containerStyle={{marginBottom: openTinhThanh ? 300 : spacing.smaller, flex: 1, marginTop: 0}}
              />
              <TouchableOpacity style={{marginTop: spacing.large, marginLeft: spacing.smaller}} onPress={onPressLayDiaChiHienTai} disabled={disableBtnLayDiaChi}>
                {!disableBtnLayDiaChi ? <Icon.Entypo name="location" color={colors.PRIMARY} size={30} /> : <ActivityIndicator size="large" color={colors.PRIMARY} />}
              </TouchableOpacity>
            </View>
          )}
        />

        <Controller
          control={control}
          name="quanHuyen"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => {
            return (
              <DropdownPicker
                title={hoSoSauNgayChuyenDoi ? 'Xã phường' : 'Quận huyện'}
                placeholder={`Chọn ${hoSoSauNgayChuyenDoi ? 'Xã phường' : 'Quận huyện'}`}
                zIndex={9000}
                isOpen={openQuanHuyen}
                setOpen={setOpenQuanHuyen}
                items={listQuanHuyen.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                schema={{
                  label: 'ten',
                  value: 'ma',
                }}
                isRequired
                onChangeValue={onChangeQuanHuyen}
                itemSelected={value}
                setItemSelected={(dispatch) => onChange(dispatch())}
                onOpen={() => onOpenDropdown(hoSoSauNgayChuyenDoi ? 2 : 1)}
                inputErr={errors.quanHuyen && getErrMessage('quanHuyen', errors.quanHuyen.type)}
              />
            );
          }}
        />

        {/* <Controller
          control={control}
          name="xaPhuong"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title="Xã phường"
              placeholder="Chọn xã phường"
              zIndex={8000}
              isOpen={openXaPhuong}
              setOpen={setOpenXaPhuong}
              items={listXaPhuong}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              isRequired
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(2)}
              inputErr={errors.xaPhuong && getErrMessage('xaPhuong', errors.xaPhuong.type)}
            />
          )}
        /> */}

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="diaDiemXayRa"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Địa điểm xảy ra"
              placeholder="Nhập địa điểm xảy ra"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.diaDiemXayRa && getErrMessage('diaDiemXayRa', errors.diaDiemXayRa.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="nhomNguyenNhan"
          rules={{
            required: true,
          }}
          render={({field: {value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Nhóm nguyên nhân"
              placeholder="Chọn nhóm nguyên nhân"
              value={getDisplayTextNhomNguyenNhan(value)}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              editable={false}
              isTouchableOpacity
              onPress={() => refModalChonNhomNguyenNhan.current.show()}
              isDropdown
              inputStyle={{color: colors.BLACK}}
              error={errors.nhomNguyenNhan && getErrMessage('nhomNguyenNhan', errors.nhomNguyenNhan.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="nhomSuKien"
          rules={{
            required: false,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              isDropdown
              editable={false}
              isTouchableOpacity
              title="Nhóm sự kiện bảo hiểm"
              inputStyle={{color: colors.BLACK}}
              onPress={() => refModalChonNhomSuKienBaoHiem.current.show()}
              value={getTenHienThiNhomSuKien(value, listSuKienBaoHiem)}
              placeholder="Chọn nhóm sự kiện"
            />
          )}
        />

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="nguyenNhan"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Nguyên nhân/diễn biến"
              placeholder="Nhập nguyên nhân/diễn biến"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              multiline
              inputStyle={styles.inputStyles}
              error={errors.nguyenNhan && getErrMessage('nguyenNhan', errors.nguyenNhan.type)}
            />
          )}
        />
        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="hauQua"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Hậu quả"
              placeholder="Nhập hậu quả"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.hauQua && getErrMessage('hauQua', errors.hauQua.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="hauQuaNguoiThuBa"
          rules={{
            required: false,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined value={value} title="Hậu quả bên thứ 3" placeholder="Hậu quả" blurOnSubmit={false} returnKeyType={'next'} onFocus={onInputFocus} onChangeText={onChange} />
          )}
        />

        <TouchableOpacity style={{flexDirection: 'row'}} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.DS_NGUOI_LAM_CHUNG_XE_MAY, {profileData, vuTonThat: itemSelected})}>
          <Text style={styles.titleLabel}>
            Người làm chứng <Text style={{color: colors.RED1}}>({dsNguoiLamChung.length})</Text>
          </Text>
          <Text style={styles.titleSubLabel}>(Bấm để xem)</Text>
        </TouchableOpacity>
      </View>
    </>
  );
  const renderThongTinCanBoGiamDinh = () => (
    <View style={{zIndex: 6000}}>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin chủ xe" style={styles.subLabel} />
      </View>
      <View style={styles.doubleInputRowView}>
        <View flex={1} marginRight={scale(spacing.smaller)}>
          <Controller
            control={control}
            rules={{
              required: false,
            }}
            name="hoTenLaiXe"
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                containerStyle={[styles.inputView]}
                title="Họ tên lái xe"
                placeholder="Nhập họ tên lái xe"
                value={value}
                onChangeText={onChange}
                returnKeyType={'next'}
                // error={errors.hauQua && getErrMessage('hauQua', errors.hauQua.type)}
              />
            )}
          />
        </View>
        <View flex={1}>
          <Controller
            control={control}
            rules={{
              required: false,
              pattern: REGUlAR_EXPRESSION.REG_PHONE,
            }}
            name="dienThoai"
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                containerStyle={[styles.inputView]}
                title="Điện thoại liên hệ"
                placeholder="Nhập số điện thoại"
                value={value}
                onChangeText={onChange}
                returnKeyType={'next'}
                keyboardType="phone-pad"
                error={errors.dienThoai && getErrMessage('dienThoai', errors.dienThoai.type)}
              />
            )}
          />
        </View>
      </View>
      <View marginHorizontal={scale(spacing.small)}>
        <Controller
          control={control}
          rules={{
            required: false,
            pattern: REGUlAR_EXPRESSION.REG_EMAIL,
          }}
          name="email"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Email"
              placeholder="Nhập email"
              value={value}
              onChangeText={onChange}
              returnKeyType={'next'}
              keyboardType="email-address"
              error={errors.email && getErrMessage('email', errors.email.type)}
            />
          )}
        />
      </View>

      <View style={styles.doubleInputRowView}>
        <View flex={1} marginRight={scale(spacing.smaller)}>
          <Controller
            control={control}
            rules={{
              required: false,
            }}
            name="soGPLX"
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                containerStyle={[styles.inputView]}
                title="Số GPLX"
                placeholder="Nhập số GPLX"
                value={value}
                onChangeText={onChange}
                returnKeyType={'next'}
                // error={errors.hauQua && getErrMessage('hauQua', errors.hauQua.type)}
              />
            )}
          />
        </View>
        <View flex={1}>
          <TextInputOutlined
            containerStyle={[styles.inputView]}
            title="Hạng GPLX"
            placeholder="Chọn hạng GPLX"
            value={tenHangGPLX}
            returnKeyType={'next'}
            isTouchableOpacity
            isDropdown
            editable={false}
            onPress={() => refModalChonHangGPLX.current.show()}
            // error={errors.hauQua && getErrMessage('hauQua', errors.hauQua.type)}
          />
        </View>
      </View>
      <View style={styles.doubleInputRowView}>
        <View flex={1} marginRight={scale(spacing.smaller)}>
          <Controller
            control={control}
            name="ngayCapGPLX"
            rules={{
              required: false,
            }}
            render={({field: {onChange, value}}) => (
              <>
                <TextInputOutlined
                  containerStyle={[styles.inputView]}
                  title="Ngày cấp GPLX"
                  placeholder="Ngày cấp"
                  value={value ? moment(value).format('DD/MM/YYYY') : ''}
                  returnKeyType={'next'}
                  onPress={() => setToggleNgayCapGPLX(true)}
                  isTouchableOpacity
                  isDateTimeField
                  editable={false}
                  inputStyle={{color: colors.BLACK}}
                />
                {renderDateTimeComp(toggleNgayCapGPLX, setToggleNgayCapGPLX, (value) => onChange(value), value !== '' ? value : new Date(), 'date', null, new Date(), 0)}
              </>
            )}
          />
        </View>
        <View flex={1}>
          <Controller
            control={control}
            name="ngayHetHanGPLX"
            rules={{
              required: false,
            }}
            render={({field: {onChange, value}}) => (
              <>
                <TextInputOutlined
                  containerStyle={[styles.inputView]}
                  title="Ngày hết hạn GPLX"
                  placeholder="Ngày hết hạn"
                  value={value ? moment(value).format('DD/MM/YYYY') : ''}
                  returnKeyType={'next'}
                  onPress={() => setToggleNgayHetHanGPLX(true)}
                  isTouchableOpacity
                  isDateTimeField
                  editable={false}
                  inputStyle={{color: colors.BLACK}}
                />
                {renderDateTimeComp(toggleNgayHetHanGPLX, setToggleNgayHetHanGPLX, (value) => onChange(value), value !== '' ? value : new Date(), 'date', null, null, 0)}
              </>
            )}
          />
        </View>
      </View>
    </View>
  );
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={itemSelected ? 'Cập nhật vụ tổn thất XM' : 'Tạo vụ tổn thất XM'}
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
            <View flex={1}>
              {renderThongTinGiamDinh()}
              {renderThongTinCanBoGiamDinh()}
            </View>
          </KeyboardAwareScrollView>
          <View style={styles.btnView}>
            <ButtonLinear title={itemSelected ? 'Cập nhật' : 'Tạo mới'} onPress={handleSubmit(onSubmit)} />
          </View>
          <ModalChonHangGPLX data={hangGPLX} ref={refModalChonHangGPLX} arrChecked={listHangGPLX} setArrChecked={setListHangGPLX} onBackPress={() => refModalChonHangGPLX.current.hide()} />
          <ModalChonNhomNguyenNhan
            ref={refModalChonNhomNguyenNhan}
            danhMucSanPhamXe={nhomNguyenNhan}
            nhomNguyenNhanSelected={nhomNguyenNhanSelected}
            setValue={(val) => onSelectedNhomNguyenNhan(val)}
            onBackPress={() => refModalChonNhomNguyenNhan.current.hide()}
          />
          <ModalChonNhomSuKienBaoHiem
            baseData={listSuKienBaoHiem}
            value={nhomSuKien}
            setValue={onSetValueSuKienBaoHiem}
            ref={refModalChonNhomSuKienBaoHiem}
            onBackPress={() => refModalChonNhomSuKienBaoHiem.current.hide()}
          />
        </View>
      }
    />
  );
};

export const TaoVuTonThatXMScreen = memo(TaoVuTonThatXMScreenComponent, isEqual);
