import {REGUlAR_EXPRESSION} from '@app/commons/Constant';
import {ButtonLinear, ScreenComponent, TextInputOutlined} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, SafeAreaView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './ThemNguoiLamChungXMStyles';

const ThemNguoiLamChungXMScreenComponent = ({route, navigation}) => {
  console.log('ThemNguoiLamChungXMScreenComponent');
  const {profileData, vuTonThat, nguoiLamChung} = route.params || {};

  const [dialogLoading, setDialogLoading] = useState(false);

  const {
    control,
    handleSubmit,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      hoTen: nguoiLamChung ? nguoiLamChung.ten : '',
      diaChi: nguoiLamChung ? nguoiLamChung.dia_chi : '',
      dienThoai: nguoiLamChung ? nguoiLamChung.dien_thoai : '',
      loiKhai: nguoiLamChung ? nguoiLamChung.loi_khai_nhan_chung : '',
    },
    mode: 'onChange',
  });

  const onSubmit = async (data) => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        vu_tt: vuTonThat ? vuTonThat.vu_tt : '',
        bt: nguoiLamChung ? nguoiLamChung.bt : '',
        ten: data.hoTen,
        dia_chi: data.diaChi,
        dien_thoai: data.dienThoai,
        email: '',
        loi_khai_nhan_chung: data.loiKhai,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.THEM_SUA_NGUOI_LAM_CHUNG_XE_MAY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      NavigationUtil.pop();
      FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm người làm chứng thành công!', 'success');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'dienThoai' && errType === 'pattern') {
      return 'Không đúng định dạng';
    }
    if (inputName === 'email' && errType === 'pattern') {
      return 'Không đúng định dạng';
    }
    return '';
  };

  // footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <ButtonLinear title="Lưu" onPress={handleSubmit(onSubmit)} linearStyle={styles.footerBtn} />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Thêm người làm chứng XM"
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView marginHorizontal={12}>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              name="hoTen"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  containerStyle={[styles.inputView]}
                  title="Họ tên"
                  placeholder="Họ tên người làm chứng"
                  value={value}
                  onChangeText={onChange}
                  isRequired={true}
                  returnKeyType={'next'}
                  blurOnSubmit={false}
                  inputStyle={styles.inputStyles}
                  error={errors.hoTen && getErrMessage('hoTen', errors.hoTen.type)}
                />
              )}
            />
            <Controller
              control={control}
              rules={{
                required: false,
              }}
              name="diaChi"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  containerStyle={[styles.inputView]}
                  title="Địa chỉ"
                  placeholder="Ghi rõ địa chỉ"
                  value={value}
                  onChangeText={onChange}
                  returnKeyType={'next'}
                  blurOnSubmit={false}
                  inputStyle={styles.inputStyles}
                />
              )}
            />
            <Controller
              control={control}
              rules={{
                required: false,
                pattern: REGUlAR_EXPRESSION.REG_PHONE,
              }}
              name="dienThoai"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  keyboardType="phone-pad"
                  containerStyle={[styles.inputView]}
                  title="Điện thoại"
                  placeholder="Nhập số điện thoại"
                  value={value}
                  onChangeText={onChange}
                  returnKeyType={'next'}
                  blurOnSubmit={false}
                  inputStyle={styles.inputStyles}
                  error={errors.dienThoai && getErrMessage('dienThoai', errors.dienThoai.type)}
                />
              )}
            />
            <Controller
              control={control}
              rules={{
                required: false,
              }}
              name="loiKhai"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  containerStyle={[styles.inputView]}
                  title="Lời khai"
                  placeholder="Lời khai của nhân chứng"
                  value={value}
                  onChangeText={onChange}
                  returnKeyType={'next'}
                  multiline
                  inputStyle={{minHeight: 60, textAlignVertical: 'top'}}
                />
              )}
            />
          </KeyboardAwareScrollView>
          {renderFooter()}
        </SafeAreaView>
      }
    />
  );
};

export const ThemNguoiLamChungXMScreen = memo(ThemNguoiLamChungXMScreenComponent, isEqual);
