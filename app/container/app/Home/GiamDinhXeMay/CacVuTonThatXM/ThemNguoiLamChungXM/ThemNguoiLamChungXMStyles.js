import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, moderateScale, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },
  footerView: {
    width: dimensions.width,
    paddingVertical: vScale(10),
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: scale(spacing.small),
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  profileItemView: {
    borderRadius: 10,
    borderWidth: 0.4,
    flexDirection: 'row',
    paddingLeft: scale(10),
    borderColor: colors.GRAY,
    marginVertical: vScale(5),
    marginHorizontal: scale(10),
  },
  profileItemCenterView: {
    flex: 1,
    paddingRight: scale(5),
    paddingVertical: vScale(5),
    borderBottomColor: colors.GRAY4,
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  label: {
    fontWeight: '500',
    color: colors.GRAY6,
    fontSize: FontSize.size14,
    lineHeight: moderateScale(20),
  },
  txtSoHS: {
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  detail: {
    fontWeight: '500',
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    lineHeight: moderateScale(20),
  },
  labelView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: vScale(2),
  },
  inputStyles: {},
});
