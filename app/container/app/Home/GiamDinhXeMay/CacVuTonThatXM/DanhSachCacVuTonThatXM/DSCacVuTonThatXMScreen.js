import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing, vScale} from '@app/theme';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styles from './Styles';
import {logErrorTryCatch} from '@app/utils';

let timer;

const DanhSachCacVuTonThatXMScreenComponent = ({route, navigation}) => {
  console.log('DanhSachCacVuTonThatXMScreenComponent');
  const {profileData, prevScreenIsHoSoDiaBan} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [listLanGiamDinh, setListLanGiamDinh] = useState([]);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getData = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_CAC_VU_TON_THAT_XE_MAY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info;
      setListLanGiamDinh(arrData);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getChiTietVuTonThat = async (item) => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: item.so_id,
        ma_doi_tac: item.ma_doi_tac,
        vu_tt: item.vu_tt,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_VU_TON_THAT_XE_MAY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let selectedItem = response.data_info;
      NavigationUtil.push(SCREEN_ROUTER_APP.TAO_VU_TON_THAT_XE_MAY, {profileData, itemSelected: selectedItem});
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onRefresh = () => {
    getData();
  };

  const onPressChiTietVuTonThat = (item) => {
    if (profileData?.ho_so?.hien_thi_button == '1') return;
    !prevScreenIsHoSoDiaBan && getChiTietVuTonThat(item);
    // NavigationUtil.push(SCREEN_ROUTER_APP.TAO_VU_TON_THAT, {profileData: profileData, itemSelected: item});
  };

  const handleXoaVuTonThat = (item) => {
    Alert.alert('Xoá vụ tổn thất', 'Bạn có chắc chắn muốn xoá vụ tổn thất này không?', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            let params = {
              so_id: item.so_id,
              vu_tt: item.vu_tt,
            };
            setDialogLoading(true);
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_VU_TON_THAT_GD_XM, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            getData();
          } catch (error) {
            logErrorTryCatch(error);
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const renderProfileItem = ({item, index}) => {
    const renderLabel = (title, value, style) => {
      return (
        <Text style={[styles.label]}>
          {title}: <Text style={style || styles.detail} children={value} />
        </Text>
      );
    };
    return (
      <TouchableOpacity onPress={() => onPressChiTietVuTonThat(item)}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            <View flexDirection="row" flex={1} justifyContent="space-between">
              {renderLabel('Vụ tổn thất', item.ten, styles.txtSoHS)}
              <TouchableOpacity onPress={() => handleXoaVuTonThat(item)}>
                <Icon.FontAwesome name="trash-o" size={24} color={colors.RED1} />
              </TouchableOpacity>
            </View>
            {renderLabel('Nguyên nhân', item.nguyen_nhan)}
            {renderLabel('Hậu quả', item.hau_qua)}
            {renderLabel('Phạm vi BH', item.ly_do, {color: item.pham_vi == 1 ? colors.GREEN : colors.RED1})}
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Các vụ tổn thất XM"
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            data={listLanGiamDinh}
            onEndReachedThreshold={0.5}
            renderItem={renderProfileItem}
            keyExtractor={(item, index) => index.toString()}
            style={{paddingTop: vScale(spacing.smaller)}}
            ListEmptyComponent={<Empty imageStyle={styles.imageNoData} />}
            refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={onRefresh} />}
          />
        </SafeAreaView>
      }
      footer={
        !prevScreenIsHoSoDiaBan &&
        profileData?.ho_so.hien_thi_button != '1' && <ButtonLinear title="Tạo vụ tổn thất" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TAO_VU_TON_THAT_XE_MAY, {profileData})} />
      }
    />
  );
};

export const DanhSachCacVuTonThatXMScreen = memo(DanhSachCacVuTonThatXMScreenComponent, isEqual);
