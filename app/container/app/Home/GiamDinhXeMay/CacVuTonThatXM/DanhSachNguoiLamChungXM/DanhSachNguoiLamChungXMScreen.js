import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing, vScale} from '@app/theme';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styles from './DanhSachNguoLamChungXMStyles';

const DanhSachNguoiLamChungXMScreenComponent = ({route, navigation}) => {
  console.log('DanhSachNguoiLamChungXMScreenComponent');
  const {profileData, vuTonThat} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dsNguoiLamChung, setDsNguoiLamChung] = useState([]);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDanhSachNguoiLamChung();
    });
  }, []);

  const getDanhSachNguoiLamChung = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        vu_tt: vuTonThat ? vuTonThat.vu_tt : '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_NGUOI_LAM_CHUNG_XE_MAY, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info;
      setDsNguoiLamChung(arrData);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onRefresh = () => {
    getDanhSachNguoiLamChung();
  };

  const onPressTrash = (item) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn xoá người làm chứng này không?', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            let params = {
              so_id: item.so_id,
              ma_doi_tac: item.ma_doi_tac,
              vu_tt: item.vu_tt,
              bt: item.bt,
            };
            setDialogLoading(true);
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_NGUOI_LAM_CHUNG_XE_MAY, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            getDanhSachNguoiLamChung();
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const renderItem = ({item, index}) => {
    const renderLabel = (title, value) => {
      return (
        <View style={styles.labelView}>
          <Text style={styles.label}>{title}: </Text>
          <Text style={[styles.detail, title === 'Họ tên' && {color: colors.PRIMARY}]}>{value}</Text>
        </View>
      );
    };

    return (
      <TouchableOpacity key={index} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_NGUOI_LAM_CHUNG_XE_MAY, {profileData, vuTonThat, nguoiLamChung: item})}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            <View flexDirection="row" justifyContent="space-between">
              {renderLabel('Họ tên', item.ten)}
              <TouchableOpacity onPress={() => onPressTrash(item)}>
                <Icon.AntDesign name="closesquareo" color={colors.RED1} size={24} />
              </TouchableOpacity>
            </View>
            {renderLabel('Địa chỉ', item.dia_chi)}
            {renderLabel('Điện thoại', item.dien_thoai)}
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  // footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <ButtonLinear title="Thêm người làm chứng" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_NGUOI_LAM_CHUNG_XE_MAY, {profileData, vuTonThat})} linearStyle={styles.footerBtn} />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Người làm chứng XM"
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            data={dsNguoiLamChung}
            onEndReachedThreshold={0.5}
            renderItem={renderItem}
            style={{paddingTop: vScale(spacing.smaller)}}
            ListEmptyComponent={<Empty imageStyle={styles.imageNoData} />}
            refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}
          />
          {renderFooter()}
        </SafeAreaView>
      }
    />
  );
};

export const DanhSachNguoiLamChungXMScreen = memo(DanhSachNguoiLamChungXMScreenComponent, isEqual);
