import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Text, TextInputOutlined} from '@component';
import React, {forwardRef, memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';

const BORDER_RADIUS = 8;
const RenderContentModalChuyenHoSoVeDonViGocComponent = forwardRef((props, ref) => {
  const {profileData, onBackPress} = props;
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      doViGoc: profileData?.ho_so ? profileData.ho_so?.ma_chi_nhanh_ql : '',
      ghiChu: '',
      giamDinhVien: '',
      maChiNhanh: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    getListGiamDinhVien();
  }, []);

  const getListGiamDinhVien = async () => {
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh_ql,
        nv: 'XE_MAY',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN_TRUONG, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListNguoiXuLy(response.data_info);
      setValue('maChiNhanh', response.data_info[0].ma_chi_nhanh);
      if (response.data_info.length === 1) setValue('giamDinhVien', response.data_info[0].nsd, {shouldValidate: true});
    } catch (error) {
      console.log('error', error);
    }
  };
  const onPressChuyen = async (data) => {
    try {
      setIsSubmiting(true);
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        nsd_gdvt: data.giamDinhVien,
        ghi_chu: data.ghiChu,
        ma_chi_nhanh_gdvt: data.maChiNhanh,
        nv: profileData.ho_so.nghiep_vu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHUYEN_HS_VE_DV_GOC, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển hồ sơ về đơn vị gốc thành công!', 'success');
      onBackPress && onBackPress();
      setTimeout(() => {
        NavigationUtil.popToRootStack();
      }, 500);
    } catch (error) {
      setIsSubmiting(false);
      console.log('error', error);
    }
  };

  const getTenHienThi = (value, data = []) => {
    let name = '';
    data.map((e) => {
      if (e.ma_chi_nhanh === value) name = e.ten_chi_nhanh;
    });
    return name;
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  /* RENDER */

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Thông báo: Bạn phải chuyển hồ sơ về đơn vị gốc
        </Text>
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={false}>
        <Controller
          control={control}
          name="doViGoc"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              title="Đơn vị gốc"
              value={getTenHienThi(value, chiNhanhBaoHiemDangCay)}
              editable={false}
              disabled
              isRequired
              error={errors.doViGoc && getErrMessage('doViGoc', errors.doViGoc.type)}
            />
          )}
        />
        <Controller
          control={control}
          name="giamDinhVien"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              zIndex={9001}
              isRequired={true}
              searchable={false}
              items={listNguoiXuLy}
              itemSelected={value}
              isOpen={isOpenDropdown}
              setOpen={setIsOpenDropdown}
              title="GĐVT / Trưởng nhóm"
              maxHeight={100}
              placeholder="Chọn GĐVT / Trưởng nhóm"
              setItemSelected={(dispatch) => onChange(dispatch())}
              inputErr={errors.giamDinhVien && getErrMessage('giamDinhVien', errors.giamDinhVien.type)}
              schema={{
                label: 'ten_can_bo',
                value: 'nsd',
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="ghiChu"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              isRequired
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              inputStyle={{maxHeight: 80}}
              containerStyle={{zIndex: -1}}
              error={errors.ghiChu && getErrMessage('ghiChu', errors.ghiChu.type)}
            />
          )}
        />
      </KeyboardAwareScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={onBackPress} title="Để sau" linearColors={[colors.GRAY2, colors.GRAY2]} linearStyle={{marginRight: 10}} textStyle={{color: colors.BLACK_03}} />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(onPressChuyen)} title="Chuyển" />
      </View>
    </View>
  );
});
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
    // height: dimensions.height * 0.5,
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.smaller,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalChuyenHoSoVeDonViGoc = memo(RenderContentModalChuyenHoSoVeDonViGocComponent, isEqual);
