import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Text, TextInputOutlined} from '@component';
import React, {forwardRef, memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

const BORDER_RADIUS = 8;
const RenderContentModalHuyHoSoComponent = forwardRef((props, ref) => {
  const {profileData, onBackPress, onCancel} = props;
  const [isSubmiting, setIsSubmiting] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      noiDung: '',
    },
    mode: 'onChange',
  });

  const onPressSaveNoiDungHuy = (data) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn huỷ hồ sơ không?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          onCancel && onCancel();
          setTimeout(async () => {
            try {
              setIsSubmiting(true);
              let params = {
                so_id: profileData.ho_so.so_id,
                ma_doi_tac: profileData?.ho_so.ma_doi_tac,
                nguon_api: 'MOBILE',
                nd_huy: data.noiDung,
              };
              let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.HUY_HO_SO_GIAM_DINH_XE_MAY, params);
              setIsSubmiting(false);
              if (!response || !response.state_info || response.state_info.status !== 'OK') return;
              FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ hồ sơ giám định thành công!', 'success');
              onBackPress && onBackPress();
            } catch (error) {
              setIsSubmiting(false);
              Alert.alert('Thông báo', error.message);
            }
            return;
          }, 500);
        },
      },
    ]);
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  /* RENDER */

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Huỷ hồ sơ
        </Text>
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={false}>
        <Controller
          control={control}
          name="noiDung"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              isRequired
              title="Nội dung huỷ"
              placeholder="Nhập rõ nội dung huỷ"
              inputStyle={{minHeight: 100, maxHeight: dimensions.height * 0.25}}
              containerStyle={{zIndex: -1}}
              error={errors.noiDung && getErrMessage('noiDung', errors.noiDung.type)}
              blurOnSubmit={false}
            />
          )}
        />
      </KeyboardAwareScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={() => onCancel && onCancel()} title="Để sau" linearStyle={{marginRight: 10}} isSubBtn />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(onPressSaveNoiDungHuy)} title="Huỷ hồ sơ" />
      </View>
    </View>
  );
});
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.smaller,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalHuyHoSo = memo(RenderContentModalHuyHoSoComponent, isEqual);
