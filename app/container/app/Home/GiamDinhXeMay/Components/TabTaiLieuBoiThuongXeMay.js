import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {getCauHinhHoSoByMa} from '@app/utils/DataProvider';
import {CheckboxComp, DropdownPicker, Icon, Text} from '@component';
import {DATA_CONSTANT, isIOS, SCREEN_ROUTER_APP} from '@constant';
import R from '@R';
import React, {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, FlatList, Image, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import Modal from 'react-native-modal';
import Progress from 'react-native-progress/Circle';
import {anhTaiLieuDropdown, extensionsFile, extensionsImage} from './Constant';

const TabTaiLieuBoiThuongXeMayComponent = forwardRef((props, ref) => {
  const {
    xemTaiLieuSelected,
    setXemTaiLieuSelected,
    profileData,
    prevScreen,
    setDoiTuongDuocChon,
    toggleModalChonDoiTuongHoSo,
    setToggleModalChonDoiTuongHoSo,
    onPressOpenImageView,
    switchImgView,
    onPressImageCheck,
    onPressToggleCheckAll,
    setSwitchImgView,
    imageDataStep1,
    imageDataStep2,
    imageDataStep3,
    imageDataStep4,
    anhDanhGiaRuiRo,
    onPressRemoveAnh,
    dataAnhCapDon,
    anhNghiemThu,
    anhThuHoiVatTu,
    anhXacMinhHienTruong,
    onPressDanhGiaHangMucTheoTen,
    onPressToggleExpandHangMuc,
    listTaiLieuPdf,
    onPressExpandAllHangMuc,
    prevScreenIsHoSoDiaBan,
  } = props;

  useImperativeHandle(ref, () => ({
    setViTriHangMucDangTai: (value) => setViTriHangMucDangTai(value),
  }));

  const [cauHinhPhanLoai, setCauHinhPhanLoai] = useState(null);
  const [openDropdownLoaiTaiLieu, setOpenDropdownLoaiTaiLieu] = useState(false);

  const [viTriHangMucDangTai, setViTriHangMucDangTai] = useState(null);

  let scrollViewModalRef = useRef(null);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState((dimensions.height / 3) * 4);
  const [toggleModal, setToggleModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalData, setModalData] = useState([]);
  const [hangMucSelected, setHangMucSelected] = useState(null);
  const [showChiTietHangMucChuaDanhGia, setShowChiTietHangMucChuaDanhGia] = useState(false);
  const [expandAllHangMuc, setExpandAllHangMuc] = useState(false);

  useEffect(() => {
    let cauHinhPhanLoai;
    if (profileData && profileData.cau_hinh) {
      cauHinhPhanLoai = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.PHAN_LOAI_DANH_GIA, profileData.cau_hinh);
      setCauHinhPhanLoai(cauHinhPhanLoai);
    }
  }, [profileData]);

  useEffect(() => {
    let coHangMucExpand = false;
    let imageDataTmp = [];
    if (xemTaiLieuSelected === 'ANH_HO_SO') imageDataTmp = imageDataStep4;
    else if (xemTaiLieuSelected === 'ANH_TON_THAT') imageDataTmp = imageDataStep3;
    imageDataTmp.map((hangMucAnh) => {
      if (hangMucAnh.expanded) coHangMucExpand = true;
    });
    setExpandAllHangMuc(coHangMucExpand);
  }, [xemTaiLieuSelected, imageDataStep4, imageDataStep3]);

  //xử lý trường hợp mở modal chọn đối tượng khi chụp ảnh
  useEffect(() => {
    if (toggleModalChonDoiTuongHoSo) {
      setToggleModal(true);
      setModalTitle('Chọn đối tượng chụp ảnh');
      setModalData(profileData.ds_doi_tuong);
    } else setToggleModal(false);
  }, [toggleModalChonDoiTuongHoSo]);

  const onPressTatModal = () => {
    setToggleModal(false);
    setToggleModalChonDoiTuongHoSo(false);
  };
  const sortAnhDanhGiaRuiRo = () => {
    let imgsGroup = groupBy(anhDanhGiaRuiRo, 'ma_file'); //return object
    let anhDanhGiaRuiRoTmp = [];
    for (const property in imgsGroup) {
      anhDanhGiaRuiRoTmp.push({
        images: imgsGroup[property],
        ma: imgsGroup[property][0].ma_file,
        ten: imgsGroup[property][0].nhom_anh,
      });
    }
    return anhDanhGiaRuiRoTmp;
  };
  const groupBy = useCallback((xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  }, []);

  /**RENDER */
  const renderRowItemDoiTuongTonThat = (title, value) => (
    <View style={styles.joinResolveDetailView}>
      <Text style={styles.txtTitle}>{title}</Text>
      <Text style={styles.txtDetail}>{value}</Text>
    </View>
  );
  const renderDoiTuongTonThatItem = ({item, index}) => {
    let nhomDoiTuong = '';
    for (let i = 0; i < profileData.nhom_doi_tuong.length; i++) {
      if (item.nhom === profileData.nhom_doi_tuong[i].ma) {
        nhomDoiTuong = profileData.nhom_doi_tuong[i].ten;
        break;
      }
    }
    return (
      <View style={styles.joinResolveView}>
        <View style={{flex: 7}}>
          {renderRowItemDoiTuongTonThat('Nhóm đối tượng', nhomDoiTuong)}
          {renderRowItemDoiTuongTonThat('Tên đối tượng', item.ten_doi_tuong)}
          {renderRowItemDoiTuongTonThat('Tên khách hàng', item.ten_kh)}
          {renderRowItemDoiTuongTonThat('Ghi chú', item.ghi_chu)}
        </View>
        {/* nút chụp ảnh theo đối tượng */}
        <View style={{flex: 0, alignItems: 'center'}}>
          <TouchableOpacity style={{flex: 1, justifyContent: 'center'}} onPress={() => setDoiTuongDuocChon({...item})}>
            <Icon.FontAwesome name="camera" size={20} color={colors.BLACK} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // không có dữ liệu
  const renderNoData = () => (
    <View style={{justifyContent: 'center', alignItems: 'center'}}>
      <Image
        source={R.images.img_no_data}
        style={{
          width: dimensions.width / 5,
          height: dimensions.width / 5,
        }}
        resizeMode={'contain'}
      />
      <Text style={{color: colors.GRAY7}}>Chưa có dữ liệu</Text>
    </View>
  );

  const renderThongBaoHangMucChuaDanhGia = (tonTaiHangMucChuaDanhGia) => (
    <View style={styles.warningHangMucChuaDanhGiaView}>
      <View>
        <TouchableOpacity style={styles.titleWarningHangMucChuaDanhGiaView} onPress={() => setShowChiTietHangMucChuaDanhGia(!showChiTietHangMucChuaDanhGia)}>
          <View style={{flexDirection: 'row', flex: 1}}>
            <Text children={`${tonTaiHangMucChuaDanhGia.length} hạng mục chưa đánh giá`} color={colors.ORANGE} font="medium16" />
          </View>
          <Icon.Ionicons name="information-circle" color={colors.ORANGE} size={20} style={{paddingRight: spacing.smaller}} />
        </TouchableOpacity>
        {showChiTietHangMucChuaDanhGia && (
          <FlatList
            scrollEnabled={false}
            data={tonTaiHangMucChuaDanhGia}
            renderItem={({item}) => (
              <TouchableOpacity style={{flexDirection: 'row', justifyContent: 'space-between'}} onPress={() => onPressDanhGiaHangMucTheoTen(item)}>
                <Text children={`${item.hang_muc?.ten_hang_muc}`} color={colors.ORANGE} style={{fontStyle: 'italic', paddingBottom: spacing.tiny}} font="regular12" />
                <Icon.AntDesign name="arrowright" size={16} color={colors.ORANGE} style={{paddingRight: spacing.smaller}} />
              </TouchableOpacity>
            )}
            style={{marginBottom: spacing.tiny}}
          />
        )}
      </View>
    </View>
  );

  const renderPdfItem = ({item, index}) => {
    let iconName = '',
      iconColor = '';
    if (item.extension === extensionsFile[0]) {
      iconName = 'file-pdf-box';
      iconColor = colors.RED2;
    } else if (item.extension === extensionsFile[1] || item.extension === extensionsFile[2]) {
      iconName = 'file-word';
      iconColor = colors.BLUE2;
    } else if (item.extension === extensionsFile[3]) {
      iconName = 'file-code-outline';
      iconColor = colors.ORANGE;
    } else if (item.extension === extensionsFile[4] || item.extension === extensionsFile[5]) {
      iconName = 'file-excel';
      iconColor = colors.GREEN3;
    }

    return (
      <View>
        <Text style={[styles.headerSubTitle]} font="medium14" children={item.ten_hang_muc} />
        <TouchableOpacity onPress={() => onPressOpenImageView({item: item})}>
          <View style={styles.imageDocument}>
            <Icon.MaterialCommunityIcons name={iconName} color={iconColor} size={dimensions.width / 4} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderListPdf = () => (
    <FlatList
      scrollEnabled={false}
      data={listTaiLieuPdf}
      renderItem={renderPdfItem}
      keyExtractor={(item, index) => index.toString()}
      style={{marginBottom: spacing.huge, marginLeft: spacing.small}}
      ListEmptyComponent={renderNoData()}
    />
  );

  const renderImageItemStep12 = (data, {listImage}) => {
    let imageData = data.item;
    let source = {uri: `data:image/gif;base64,${imageData.duong_dan}`};
    if (xemTaiLieuSelected === 'ANH_CAP_DON') source = {uri: imageData.pa_att_url};
    return (
      <TouchableOpacity onPress={() => onPressOpenImageView(data, listImage)}>
        {!prevScreenIsHoSoDiaBan && (
          <TouchableOpacity style={styles.btnClose} onPress={() => onPressRemoveAnh(imageData)}>
            <Icon.MaterialCommunityIcons name="close" color="#FFF" size={24} />
          </TouchableOpacity>
        )}

        <ImageProcess
          source={source}
          indicator={Progress.Circle}
          style={styles.imageDocument}
          imageStyle={{borderRadius: 20}}
          indicatorProps={{
            size: 70,
            borderWidth: 0,
            color: colors.PRIMARY,
            unfilledColor: colors.PRIMARY_LIGHT,
          }}
          renderError={() => (
            <View>
              <Image source={R.images.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />
            </View>
          )}
        />
        {switchImgView && xemTaiLieuSelected !== 'ANH_CAP_DON' && (
          <View style={styles.checkboxImgView}>
            <CheckboxComp
              value={imageData.checked}
              onValueChange={() => {
                onPressImageCheck(imageData);
              }}
            />
          </View>
        )}
        {imageData.stt !== undefined && <Text children={imageData.stt} style={{position: 'absolute', top: 20, left: 25, color: '#FFF', fontSize: 18, fontWeight: 'bold'}} />}
      </TouchableOpacity>
    );
  };
  const renderImageStep12 = (title, imageData) => {
    let checkAll = true;
    imageData.map((imgItem) => {
      if (imgItem.checked === false) checkAll = false;
    });
    imageData = imageData.sort(function (a, b) {
      return a.stt - b.stt;
    });
    return (
      <FlatList
        scrollEnabled={false}
        data={imageData}
        renderItem={(itemImage) => renderImageItemStep12(itemImage, {listImage: imageData})}
        keyExtractor={(item, index) => index.toString()}
        numColumns={3}
        horizontal={false}
        style={{marginBottom: spacing.huge}}
        ListEmptyComponent={renderNoData()}
        ListHeaderComponent={
          <View style={styles.headerView}>
            <View style={{flexDirection: 'row'}}>
              {switchImgView && // đang bật CHỌN ẢNH
                (xemTaiLieuSelected === 'ANH_TOAN_CANH' || xemTaiLieuSelected === 'ANH_TON_THAT' || xemTaiLieuSelected === 'ANH_HO_SO') &&
                imageData.length > 0 && (
                  <CheckboxComp
                    value={checkAll}
                    onValueChange={() => {
                      !checkAll
                        ? onPressToggleCheckAll(1, imageData) //1 : chọn tất cả
                        : onPressToggleCheckAll(0, imageData); //0 : bỏ chọn tất cả
                    }}
                    checkboxStyle={{marginRight: spacing.smaller}}
                  />
                )}
              <Text style={[styles.headerSubTitle]} font="medium14" children={title} />
            </View>
          </View>
        }
      />
    );
  };

  const renderImageItemStep34 = ({item, index}, extraData) => {
    //nếu đang ở DROPDOW ẢNH HỒ SƠ, GIẤY TỜ + LÀ CÁC HẠNG MỤC DƯỚI THÌ KHÔNG HIỂN THỊ
    let checkAll = true;
    let checkAllIsImage = true;
    item.images.map((imgItem) => {
      if (imgItem.checked === false) checkAll = false;
      if (!extensionsImage.includes(imgItem.extension)) checkAllIsImage = false;
    });
    if (item.images.length === 0) checkAll = false;
    item.images = item.images.sort(function (a, b) {
      return a.stt - b.stt;
    });
    let {hang_muc} = item;
    return (
      <View style={{backgroundColor: index % 2 == 0 ? colors.WHITE8 : '#FFF', paddingVertical: spacing.small, borderBottomWidth: 0.5, borderColor: '#CCC'}}>
        <View style={styles.headerItemHangMucView}>
          <View style={styles.headerTitleView}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {switchImgView && // đang bật CHỌN ẢNH
                xemTaiLieuSelected !== 'ANH_TOAN_CANH' &&
                checkAllIsImage && ( //tất cả file trong hạng mục phải là ảnh
                  <CheckboxComp
                    value={checkAll}
                    onValueChange={() => {
                      !checkAll
                        ? onPressToggleCheckAll(1, item.images, index) //1 : chọn tất cả
                        : onPressToggleCheckAll(0, item.images, index); //0 : bỏ chọn tất cả
                    }}
                    checkboxStyle={{marginRight: spacing.smaller}}
                  />
                )}
              <TouchableOpacity
                onPress={() => {
                  if (xemTaiLieuSelected === 'ANH_TOAN_CANH') return;
                  onPressToggleExpandHangMuc(index);
                  setHangMucSelected(index);
                }}
                style={{flex: 1}}>
                <Text style={[styles.headerSubTitle]} font="medium14" color={index === hangMucSelected && item.expanded ? colors.ORANGE : '#000'} children={item.ten} />
              </TouchableOpacity>
            </View>
            {profileData.ds_doi_tuong.length > 1 && (
              <TouchableOpacity
                onPress={() => {
                  onPressToggleExpandHangMuc(index);
                  setHangMucSelected(index);
                }}>
                <Text style={{fontStyle: 'italic', marginTop: spacing.smaller}} font="regular12" children={item.tenDoiTuong} />
              </TouchableOpacity>
            )}
            {/* CHỈ HIỂN THỊ THÔNG TIN ĐÁNH GIÁ VỚI ẢNH TỔN THẤT */}
            {xemTaiLieuSelected === 'ANH_TON_THAT' && (
              <>
                {hang_muc.muc_do_ten && (
                  <TouchableOpacity
                    style={styles.thongTinDanhGiaView}
                    onPress={() => {
                      onPressToggleExpandHangMuc(index);
                      setHangMucSelected(index);
                    }}>
                    <Text
                      style={{fontStyle: 'italic'}}
                      font="regular10"
                      children={`${hang_muc.muc_do_ten} / ${hang_muc.thay_the_sc === 'S' ? 'Sữa chữa' : 'Thay thế'} / ${hang_muc.thu_hoi === 'K' ? 'Không thu hồi' : 'Có thu hồi'}`}
                    />
                    {/* <NumericFormat
                      value={hang_muc.gia_giam_dinh}
                      displayType={'text'}
                      thousandSeparator={true}
                      renderText={(value) => <Text style={{fontStyle: 'italic'}} font="regular12" children={value + ' VNĐ'} />}
                    /> */}
                  </TouchableOpacity>
                )}
                {/* NẾU CHƯA CÓ MỨC ĐỘ TÊN -> HIỂN THỊ HẠNG MỤC NÀY CHƯA ĐÁNH GIÁ*/}
                {!hang_muc.muc_do_ten && (
                  <View style={styles.thongTinDanhGiaView}>
                    <Icon.Entypo name="warning" size={14} color={colors.ORANGE} />
                    <Text children="Chưa đánh giá" color={colors.ORANGE} style={{fontStyle: 'italic'}} font="regular12" />
                  </View>
                )}
              </>
            )}
          </View>
          {/* HIỂN THỊ NÚT EXPAND/COLPAN VỚI ẢNH TỔN THẤT VÀ ẢNH HỒ SƠ */}
          {(xemTaiLieuSelected === 'ANH_TON_THAT' || xemTaiLieuSelected === 'ANH_HO_SO' || item.ma === 'ANH_NGHIEM_THU') && (
            <>
              {viTriHangMucDangTai === index && <ActivityIndicator size="small" color={colors.PRIMARY} />}
              <TouchableOpacity
                onPress={(event) => {
                  onPressToggleExpandHangMuc(index);
                  setHangMucSelected(index);
                }}
                style={{padding: spacing.small}}>
                <Icon.FontAwesome name={item.expanded ? 'angle-down' : 'angle-up'} size={18} color={colors.BLACK} />
              </TouchableOpacity>
            </>
          )}
        </View>
        {/* CHỈ HIỂN THỊ LIST ẢNH VỚI TRƯỜNG HỢP item.expanded(ẢNH HỒ SƠ, ẢNH TỔN THẤT CÓ PROS NÀY) VÀ CÁC LOẠI ẢNH KHÁC ANH_TON_THAT VÀ ANH_HO_SO */}
        {(item.expanded || (xemTaiLieuSelected !== 'ANH_TON_THAT' && xemTaiLieuSelected !== 'ANH_HO_SO')) && (
          <FlatList
            scrollEnabled={false}
            data={item.images}
            renderItem={(itemImage) => renderImageItemStep12(itemImage, {listImage: item.images})}
            keyExtractor={(itemImage) => itemImage.bt.toString()}
            ListEmptyComponent={renderNoData()}
            numColumns={3}
            horizontal={false}
          />
        )}
      </View>
    );
  };
  const renderImageStep34 = (title, imagesData) => {
    let tonTaiHangMucChuaDanhGia = [];
    if (xemTaiLieuSelected === 'ANH_TON_THAT') tonTaiHangMucChuaDanhGia = imagesData.filter((hangMuc) => hangMuc?.hang_muc?.muc_do === null);
    return (
      <>
        {tonTaiHangMucChuaDanhGia.length > 0 && renderThongBaoHangMucChuaDanhGia(tonTaiHangMucChuaDanhGia)}
        <FlatList
          scrollEnabled={false}
          data={imagesData}
          renderItem={(item) => renderImageItemStep34(item, {title: title, imagesData})}
          initialNumToRender={50}
          ListEmptyComponent={renderNoData()}
          style={{marginBottom: spacing.huge}}
          ListHeaderComponent={
            title !== 'Ảnh toàn cảnh' ? (
              <TouchableOpacity
                style={styles.headerView}
                onPress={() => {
                  setExpandAllHangMuc(!expandAllHangMuc);
                  onPressExpandAllHangMuc(!expandAllHangMuc);
                }}>
                <Text style={styles.headerTitle} font="medium16" children={title} />
                <View style={{padding: spacing.small}}>
                  <Icon.FontAwesome name={expandAllHangMuc ? 'angle-double-down' : 'angle-double-up'} size={20} color={colors.BLACK} />
                </View>
              </TouchableOpacity>
            ) : null
          }
        />
      </>
    );
  };

  const renderModal = () => (
    <Modal
      isVisible={toggleModal}
      onSwipeComplete={onPressTatModal}
      onBackdropPress={onPressTatModal}
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
      scrollOffset={scrollOffSet}
      scrollOffsetMax={(dimensions.height * 3) / 4 - flatListHeight} // content height - ScrollView height
      propagateSwipe={true}
      style={styles.modal}>
      <View style={styles.modalView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle}>{modalTitle}</Text>
          <TouchableOpacity style={styles.closeView} onPress={onPressTatModal}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>
        <ScrollView ref={scrollViewModalRef} onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)} scrollEventThrottle={16} showsVerticalScrollIndicator={false}>
          <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>
            <FlatList data={modalData} renderItem={renderDoiTuongTonThatItem} style={{marginBottom: spacing.massive}} />
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  const hienThiNutCheckAnhDanhGia =
    profileData?.ho_so?.hien_thi_button != 1 &&
    cauHinhPhanLoai?.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO &&
    prevScreen !== SCREEN_ROUTER_APP.HO_SO_DIA_BAN &&
    (xemTaiLieuSelected === 'ANH_TON_THAT' || xemTaiLieuSelected === 'ANH_HO_SO');
  return (
    <View>
      <View style={styles.filterCheckboxView}>
        <DropdownPicker
          zIndex={8000}
          searchable={false}
          isOpen={openDropdownLoaiTaiLieu}
          setOpen={setOpenDropdownLoaiTaiLieu}
          items={anhTaiLieuDropdown}
          maxHeight={150}
          itemSelected={xemTaiLieuSelected}
          setItemSelected={setXemTaiLieuSelected}
          containerStyle={{marginBottom: !isIOS && openDropdownLoaiTaiLieu ? 150 : 0, marginVertical: 0, flex: 1}}
        />
        {hienThiNutCheckAnhDanhGia && (
          <TouchableOpacity style={styles.btnSwitchCheckImageView} onPress={() => setSwitchImgView(!switchImgView)}>
            <Icon.MaterialCommunityIcons name={!switchImgView ? 'format-list-checkbox' : 'format-list-checks'} size={24} color="#FFF" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView>
        {xemTaiLieuSelected === 'FILE_PDF' && renderListPdf()}
        {xemTaiLieuSelected === 'ANH_CAP_DON' && renderImageStep12('Ảnh cấp đơn', dataAnhCapDon)}
        {xemTaiLieuSelected === 'ANH_HIEN_TRUONG' && renderImageStep12('Ảnh hiện trường', imageDataStep1)}
        {xemTaiLieuSelected === 'ANH_TOAN_CANH' && renderImageStep34('Ảnh toàn cảnh', imageDataStep2)}
        {xemTaiLieuSelected === 'ANH_TON_THAT' && renderImageStep34('Ảnh tổn thất', imageDataStep3)}
        {xemTaiLieuSelected === 'ANH_HO_SO' && renderImageStep34('Ảnh hồ sơ, giấy tờ', imageDataStep4)}
        {xemTaiLieuSelected === 'ANH_NGHIEM_THU' && renderImageStep12('Ảnh nghiệm thu', anhNghiemThu)}
        {xemTaiLieuSelected === 'ANH_THVT' && renderImageStep12('Ảnh thu hồi vật tư', anhThuHoiVatTu)}
        {xemTaiLieuSelected === 'XMHT' && renderImageStep12('Ảnh xác minh hiện trường', anhXacMinhHienTruong)}
        {xemTaiLieuSelected === 'ANH_DANH_GIA_RUI_RO' && renderImageStep34('Ảnh đánh giá rủi ro', sortAnhDanhGiaRuiRo(anhDanhGiaRuiRo))}
      </ScrollView>

      {renderModal()}
    </View>
  );
});

const styles = StyleSheet.create({
  containerView: {
    backgroundColor: colors.WHITE,
    paddingHorizontal: scale(spacing.small),
  },
  imageDocument: {
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginVertical: vScale(spacing.small),
    marginHorizontal: scale(spacing.small),
  },
  headerTitle: {
    flex: 1,
  },
  headerView: {
    paddingHorizontal: spacing.small,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerItemHangMucView: {
    paddingHorizontal: spacing.small,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitleView: {
    flex: 1,
    justifyContent: 'space-between',
  },
  headerSubTitle: {
    marginRight: spacing.tiny,
  },
  checkboxImgView: {
    position: 'absolute',
    right: spacing.small,
    bottom: spacing.small,
    borderTopLeftRadius: 5,
    backgroundColor: colors.WHITE,
  },
  classifyInfoView: {
    borderRadius: 10,
    borderWidth: 0.4,
    borderColor: colors.GRAY,
    marginVertical: vScale(5),
    paddingLeft: scale(spacing.small),
    paddingRight: scale(spacing.small),
    paddingVertical: vScale(spacing.tiny),
    marginHorizontal: scale(spacing.small),
  },
  classifyInfoDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: scale(spacing.small),
  },
  classifyInfoTitle: {
    fontSize: FontSize.size12,
  },
  classifyInfoValue: {
    fontWeight: 'bold',
    fontSize: FontSize.size12,
  },
  txtDaDanhGia: {
    fontWeight: 'bold',
    color: colors.GREEN,
    fontSize: FontSize.size14,
    marginRight: scale(spacing.tiny),
  },
  selectAllView: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalTitleView: {
    height: 50,
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  joinResolveDetailView: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: vScale(spacing.tiny),
  },
  joinResolveView: {
    alignItems: 'center',
    borderBottomWidth: 2,
    flexDirection: 'row',
    borderColor: colors.GRAY4,
    marginTop: vScale(spacing.tiny),
    paddingHorizontal: scale(spacing.small),
  },
  modalView: {
    width: dimensions.width,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    backgroundColor: colors.WHITE,
    height: (dimensions.height * 3) / 4,
  },
  modalTitle: {
    fontSize: 18,
    textAlign: 'center',
    marginVertical: vScale(spacing.small),
  },
  closeView: {
    position: 'absolute',
    top: 15,
    right: 15,
  },
  txtTitle: {
    fontSize: 12,
    marginBottom: vScale(spacing.tiny),
  },
  txtDetail: {
    flexShrink: 1,
    color: colors.GRAY6,
    textAlign: 'justify',
    paddingLeft: scale(spacing.small),
    paddingRight: scale(spacing.small),
  },
  switchContentView: {
    flex: 1,
    flexWrap: 'wrap',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    maxHeight: dimensions.width / 2,
    paddingVertical: vScale(spacing.tiny),
  },
  txtChonTatCa: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
    marginRight: scale(spacing.tiny),
  },
  switchContainerView: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    marginHorizontal: scale(spacing.small),
  },
  btnClose: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 2,
    backgroundColor: colors.RED1,
    borderRadius: 50,
  },
  thongTinDanhGiaView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: spacing.smaller,
  },
  filterCheckboxView: {
    zIndex: 8000,
    flexDirection: 'row',
    justifyContent: 'center',
    // alignItems: 'center',
    marginHorizontal: spacing.small,
    marginBottom: spacing.smaller,
  },
  btnSwitchCheckImageView: {
    backgroundColor: colors.BLUE3_07,
    borderRadius: 8,
    height: 45,
    width: 45,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.small,
  },
  titleWarningHangMucChuaDanhGiaView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.tiny,
  },
  warningHangMucChuaDanhGiaView: {
    paddingLeft: spacing.small,
    paddingRight: spacing.small,
    borderBottomWidth: 0.5,
    borderColor: colors.ORANGE,
  },
});

const TabTaiLieuBoiThuongXeMayMemo = memo(TabTaiLieuBoiThuongXeMayComponent, isEqual);
export const TabTaiLieuBoiThuongXeMay = TabTaiLieuBoiThuongXeMayMemo;
