import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {Empty, Icon, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalCanhBaoHoSoXeMayComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: hideModal,
    }),
    [],
  );

  const {listCanhBaoHoSo, profileData} = props;
  const [isVisible, setIsVisible] = useState(false);

  const hideModal = () => setIsVisible(false);

  const onPressItem = (e) => {
    const canFix = e.loai === 'OCR_CANH_BAO_DANG_KIEM' || e.loai === 'OCR_CANH_BAO_GPLX';
    let params = {
      profileData,
      prevScreen: SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY,
      thongTinGiayTo: {},
    };
    if (e.loai === 'OCR_CANH_BAO_GPLX') params.thongTinGiayTo.nhom_hang_muc = 'BANG_LAI';
    else if (e.loai === 'OCR_CANH_BAO_DANG_KIEM') params.thongTinGiayTo.nhom_hang_muc = 'DANG_KIEM';
    if (canFix) {
      NavigationUtil.push(SCREEN_ROUTER_APP.BO_SUNG_THONG_TIN_GIAY_TO_XM, params);
      hideModal();
    }
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Nhắc nhở" />
        <TouchableOpacity style={styles.closeView} onPress={hideModal}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItemCanhBao = ({item, index}) => {
    const showIcon = item.loai === 'OCR_CANH_BAO_DANG_KIEM' || item.loai === 'OCR_CANH_BAO_GPLX';
    return (
      <TouchableOpacity key={item.loai} style={[styles.itemCanhBaoView, index === listCanhBaoHoSo.length - 1 && {borderColor: '#FFF'}]} onPress={() => onPressItem(item)}>
        <View flexDirection="row" alignItems="center" justifyContent="space-between">
          <Text children={item.ngay} style={styles.txtNgayCanhBao} />
          {showIcon && <Icon.AntDesign name="tool" color={colors.BLACK_03} size={22} />}
        </View>
        <Text children={item.noi_dung} style={styles.txtNoiDungCanhBao} />
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {listCanhBaoHoSo.length > 0 ? <View>{listCanhBaoHoSo.map((item, index) => renderItemCanhBao({item, index}))}</View> : <Empty imageStyle={styles.imageStyles} />}
      </ScrollView>
    );
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} onSwipeComplete={hideModal} onBackdropPress={hideModal} onBackButtonPress={hideModal}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },

  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.ORANGE,
    margin: scale(spacing.small),
  },
  closeView: {
    borderRadius: 25,
    backgroundColor: colors.GRAY2,
    marginRight: scale(spacing.small),
  },
  content: {
    margin: scale(spacing.small),
    paddingBottom: vScale(spacing.small),
    flex: 1,
  },
  imageStyles: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  itemCanhBaoView: {
    paddingBottom: vScale(spacing.smaller),
    marginBottom: vScale(spacing.smaller),
    borderColor: colors.GRAY,
    borderBottomWidth: 0.5,
  },
  txtNgayCanhBao: {
    marginBottom: spacing.tiny,
  },
  txtNoiDungCanhBao: {
    color: colors.ORANGE,
    fontSize: FontSize.size14,
  },
});

export const ModalCanhBaoHoSoXeMay = ModalCanhBaoHoSoXeMayComponent;
