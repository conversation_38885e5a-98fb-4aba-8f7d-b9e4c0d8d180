import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, TextInputOutlined} from '@component';
import {DATA_CONSTANT} from '@constant';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './DoiTuongTonThatXMStyles';
const titleInput = ['Nhóm đối tượng', 'Tên đối tượng', '<PERSON>ê<PERSON> khách hàng / chủ đối tượng', '<PERSON><PERSON> chú', '<PERSON><PERSON><PERSON> tài sản', '<PERSON><PERSON><PERSON> đối tượ<PERSON>', 'Số CMND/CCCD', 'Địa chỉ', 'Mức độ thương tật'];
const listLoaiDoiTuong = [
  {
    label: 'TNDS về người',
    value: 'TNDS',
  },
  {
    label: 'Người ngồi trên xe',
    value: 'NNTX',
  },
  // {
  //   label: 'Lái phụ xe',
  //   value: 'LPHU_XE',
  // },
  // {
  //   label: 'Hành khách trên xe',
  //   value: 'NGUOI_HK',
  // },
];
const listLoaiTaiSan = [
  {
    label: 'Xe ô tô',
    value: 'XE',
  },
  {
    label: 'Xe máy',
    value: 'XE_MAY',
  },
  {
    label: 'Tài sản khác',
    value: 'KHAC',
  },
];

const listMucDoThuongTat = [
  {
    label: 'Tử vong',
    value: 'TU_VONG',
  },
  {
    label: 'Thương tật',
    value: 'THUONG_TAT',
  },
];

const DoiTuongTonThatXMScreenComponent = ({route}) => {
  console.log('DoiTuongTonThatXMScreenComponent');

  const {profileData, doiTuongTonThat} = route.params;

  // DROPDOWN NHÓM ĐỐI TƯỢNG
  const [openNhomDoiTuong, setOpenNhomDoiTuong] = useState(false);
  // DROPDOWN LOẠI TÀI SẢN - dành riêng cho nhóm đối tượng là tài sản
  const [openLoaiTaiSan, setOpenLoaiTaiSan] = useState(false);
  // DROPDOWN LOẠI ĐỐI TƯỢNG - dành riêng cho nhóm đối tượng là con người
  const [openLoaiDoiTuong, setOpenLoaiDoiTuong] = useState(false);
  // DROPDOWN MỨC ĐỘ Thương tật - dành riêng cho nhóm đối tượng là con người
  const [openMucDoThuongTat, setOpenMucDoThuongTat] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);

  const getDefaultFormValue = () => {
    let mucDoThuongTat = '';
    if (doiTuongTonThat && doiTuongTonThat.nhom === 'NGUOI') mucDoThuongTat = doiTuongTonThat.muc_do;
    else mucDoThuongTat = listMucDoThuongTat[1].value; // nếu có đối tượng tt thì lấy từ đối tượng tổn thất. còn không thì mặc định là thương tật
    return {
      nhomDoiTuong: doiTuongTonThat ? doiTuongTonThat.nhom : profileData.nhom_doi_tuong.length === 1 ? profileData.nhom_doi_tuong[0].value : '',
      loaiTaiSan: doiTuongTonThat && doiTuongTonThat.nhom === 'TAI_SAN' ? doiTuongTonThat.loai : '',
      loaiDoiTuong: doiTuongTonThat && doiTuongTonThat.nhom === 'NGUOI' ? doiTuongTonThat.loai : '',
      mucDoThuongTat: mucDoThuongTat,
      tenDoiTuong: doiTuongTonThat ? doiTuongTonThat.ten_doi_tuong : '',
      tenKhachHang: doiTuongTonThat ? doiTuongTonThat.ten_kh : '',
      soCMND: doiTuongTonThat ? doiTuongTonThat.cmnd : '',
      diaChi: doiTuongTonThat ? doiTuongTonThat.dia_chi : '',
      ghiChu: doiTuongTonThat ? doiTuongTonThat.ghi_chu : '',
      namSanXuat: doiTuongTonThat ? doiTuongTonThat.nam_sx : '',
      tienThoaThuan: doiTuongTonThat ? doiTuongTonThat.tien_thoa_thuan : '',
      mucDoNguoi: doiTuongTonThat ? doiTuongTonThat.muc_do : '',
    };
  };

  const {
    control,
    handleSubmit,
    // setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });
  const watchNhomDoiTuong = watch('nhomDoiTuong');

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const closeDropdown = (title) => {
    openNhomDoiTuong && setOpenNhomDoiTuong(false);
    openLoaiDoiTuong && setOpenLoaiDoiTuong(false);
  };

  const onPressSave = async (data) => {
    setDialogLoading(true);
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      so_id: profileData.ho_so.so_id,
      so_id_doi_tuong: doiTuongTonThat ? doiTuongTonThat.so_id_doi_tuong : '',
      nhom: data.nhomDoiTuong,
      ten_kh: data.tenKhachHang,
      ten_doi_tuong: data.tenDoiTuong,
      ghi_chu: data.ghiChu,
      loai: data.nhomDoiTuong === 'TAI_SAN' ? data.loaiTaiSan : data.loaiDoiTuong,
      cmnd: data.soCMND,
      dia_chi: data.diaChi,
      muc_do: data.mucDoThuongTat,
      tien_thoa_thuan: data.tienThoaThuan,
      nam_sx: data.namSanXuat,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DOI_TUONG_TON_THAT_XM, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', doiTuongTonThat ? 'Cập nhật thành công' : 'Tạo đối tượng tổn thất thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressXoa = () => {
    Alert.alert('Thông báo', 'Bạn có muốn xoá đối tượng này', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          let params = {
            ma_doi_tac: profileData.ho_so.ma_doi_tac,
            so_id: profileData.ho_so.so_id,
            so_id_doi_tuong: doiTuongTonThat.so_id_doi_tuong,
          };
          try {
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_DOI_TUONG_TON_THAT_XE_MAY, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá đối tượng tổn thất thành công', 'success');
            NavigationUtil.pop();
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };
  /**RENDER  */
  const renderContent = () => {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false} scrollEnabled={true}>
          <View style={styles.contentView}>
            <Controller
              control={control}
              name="nhomDoiTuong"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  title={titleInput[0]}
                  zIndex={9000}
                  searchable={doiTuongTonThat ? true : false}
                  isOpen={openNhomDoiTuong}
                  setOpen={setOpenNhomDoiTuong}
                  // items={profileData.nhom_doi_tuong}
                  items={profileData.nhom_doi_tuong.filter((item) => item.ma === profileData.ho_so.nv_xly)}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  onOpen={() => openLoaiTaiSan && setOpenLoaiTaiSan(false)}
                  disabled={doiTuongTonThat ? true : false}
                  placeholder="Chọn nhóm đối tượng"
                  containerStyle={{marginBottom: spacing.small}}
                  isRequired={true}
                  inputErr={errors.nhomDoiTuong && getErrMessage('nhomDoiTuong', errors.nhomDoiTuong.type)}
                  schema={{
                    label: 'ten',
                    value: 'ma',
                  }}
                />
              )}
            />

            {watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma && (
              <Controller
                control={control}
                name="loaiTaiSan"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title={titleInput[4]}
                    zIndex={8000}
                    searchable={false}
                    isOpen={openLoaiTaiSan}
                    setOpen={setOpenLoaiTaiSan}
                    items={listLoaiTaiSan}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    placeholder="Chọn loại tài sản"
                    containerStyle={{marginBottom: spacing.small}}
                    isRequired={true}
                    inputErr={errors.loaiTaiSan && getErrMessage('loaiTaiSan', errors.loaiTaiSan.type)}
                  />
                )}
              />
            )}

            {watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma && (
              <>
                <Controller
                  control={control}
                  name="loaiDoiTuong"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title={titleInput[5]}
                      zIndex={7000}
                      searchable={false}
                      isOpen={openLoaiDoiTuong}
                      setOpen={setOpenLoaiDoiTuong}
                      items={listLoaiDoiTuong}
                      itemSelected={value}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      placeholder="Chọn loại đối tượng"
                      containerStyle={{marginBottom: spacing.small}}
                      isRequired={true}
                      inputErr={errors.loaiDoiTuong && getErrMessage('loaiDoiTuong', errors.loaiDoiTuong.type)}
                    />
                  )}
                />
                <Controller
                  control={control}
                  name="mucDoThuongTat"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title={titleInput[8]}
                      zIndex={6000}
                      searchable={false}
                      isOpen={openMucDoThuongTat}
                      setOpen={setOpenMucDoThuongTat}
                      items={listMucDoThuongTat}
                      itemSelected={value}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      placeholder="Chọn mức độ thương tật"
                      containerStyle={{marginBottom: spacing.small}}
                      isRequired={true}
                      inputErr={errors.mucDoThuongTat && getErrMessage('mucDoThuongTat', errors.mucDoThuongTat.type)}
                    />
                  )}
                />
              </>
            )}

            {/* tên đối tượng */}
            <Controller
              control={control}
              name="tenDoiTuong"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  title={titleInput[1]}
                  value={value}
                  onChangeText={onChange}
                  placeholder={titleInput[1]}
                  onFocus={closeDropdown}
                  isRequired={true}
                  error={errors.tenDoiTuong && getErrMessage('tenDoiTuong', errors.tenDoiTuong.type)}
                />
              )}
            />

            {(watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma || watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) && (
              <Controller
                control={control}
                name="namSanXuat"
                rules={{
                  required: false,
                }}
                render={({field: {onChange, value}}) => <TextInputOutlined value={value} placeholder={'Nhập năm sản xuất'} onChangeText={onChange} onFocus={closeDropdown} title={'Năm sản xuất'} />}
              />
            )}

            {/* Tên khách hàng */}
            <Controller
              control={control}
              name="tenKhachHang"
              render={({field: {onChange, value}}) => <TextInputOutlined title={titleInput[2]} value={value} onChangeText={onChange} placeholder={titleInput[2]} onFocus={closeDropdown} />}
            />
            <Controller
              control={control}
              name="soCMND"
              render={({field: {onChange, value}}) => <TextInputOutlined title={titleInput[6]} value={value} onChangeText={onChange} placeholder={titleInput[6]} onFocus={closeDropdown} />}
            />

            <Controller
              control={control}
              name="diaChi"
              render={({field: {onChange, value}}) => <TextInputOutlined title={titleInput[7]} value={value} onChangeText={onChange} placeholder={titleInput[7]} onFocus={closeDropdown} />}
            />

            {watchNhomDoiTuong !== DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma && (
              <Controller
                rules={{
                  required: false,
                }}
                control={control}
                name="tienThoaThuan"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    title="Tiền thoả thuận"
                    keyboardType="numeric"
                    value={value}
                    onChangeText={onChange}
                    placeholder="0"
                    onFocus={closeDropdown}
                    inputStyle={{textAlign: 'right'}}
                    // error={errors.tienThoaThuan && getErrMessage('tienThoaThuan', errors.tienThoaThuan.type)}
                  />
                )}
              />
            )}

            {/* Ghi chú */}
            <Controller
              control={control}
              name="ghiChu"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined title={titleInput[3]} value={value} onChangeText={onChange} placeholder={titleInput[3]} onFocus={closeDropdown} numberOfLines={3} multiline={true} />
              )}
            />
          </View>
        </KeyboardAwareScrollView>
      </View>
    );
  };

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {doiTuongTonThat && <ButtonLinear title="Xoá" onPress={onPressXoa} linearColors={[colors.GRAY, colors.GRAY]} linearStyle={{marginRight: spacing.small}} textStyle={{color: colors.BLACK}} />}
        <ButtonLinear title="Lưu" onPress={handleSubmit(onPressSave)} />
      </View>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Đối tượng tổn thất xe máy"
      renderView={renderContent()}
      footer={renderFooter()}
      //
    />
  );
};

export const DoiTuongTonThatXMScreen = memo(DoiTuongTonThatXMScreenComponent, isEqual);
