import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 10,
  },
  contentView: {
    flex: 1,
    minHeight: dimensions.height / 2,
    marginHorizontal: 15,
  },
  imgZoom: {
    width: dimensions.width,
    height: dimensions.height / 3 + 50,
  },
  itemImageView: {
    marginHorizontal: 3,
    borderColor: colors.WHITE,
    borderWidth: 2,
    borderRadius: 10,
  },
  itemImage: {
    width: dimensions.width / 6,
    height: dimensions.width / 6,
    borderRadius: 10,
  },
  foregroundView: {
    paddingTop: 10,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    borderColor: colors.GRAY,
  },
  dropDownView: {
    marginVertical: 10,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  linearBtnView: {
    borderRadius: 30,
    backgroundColor: colors.WHITE,
  },
  btnReceivingRecords: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    minHeight: 50,
    borderRadius: 10,
    flex: 1,
  },
  txtBtnReceivingRecords: {
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  textInputType: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: colors.GRAY,
    paddingLeft: 15,
    color: colors.BLACK,
  },
  textInputView: {
    marginBottom: 10,
  },
});
