import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing} from '@app/theme';
import {CheckboxComp, Empty, Icon, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalNhanXetComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, setValue, value, data, refreshing} = props;
  const [isSelectedItemIndex, setIsSelectedItemIndex] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const [dlMau, setDlMau] = useState([]);

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setIsSelectedItemIndex(index);
  };

  const checkSelected = () => {
    if (data) {
      setDlMau(data);
    }
    if (value != '') {
      let isCheck = data.findIndex((item) => item.noi_dung === value);
      setIsSelectedItemIndex(isCheck);
    }
  };

  useEffect(() => {}, []);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Nhận xét" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = (item, index) => {
    const isCheck = isSelectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK_03, marginTop: !isIOS ? 4 : 2}}>{item.noi_dung}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false} refreshControl={<RefreshControl refreshing={refreshing} />}>
        {dlMau?.length > 0 ? <View>{dlMau.map((item, index) => renderItem(item, index))}</View> : <Empty imageStyle={styles.imageStyles} />}
      </ScrollView>
    );
  };
  return (
    <Modal
      onModalShow={checkSelected}
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: scale(spacing.small),
    borderColor: colors.GRAY,
    height: 40,
    margin: scale(spacing.medium),
    borderRadius: 25,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: scale(spacing.medium),
  },
  itemHangMucView: {
    padding: scale(spacing.tiny),
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: FontSize.size16,
    marginLeft: scale(30),
    fontWeight: 'bold',
    marginVertical: scale(spacing.medium),
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
  imageStyles: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});

export const ModalNhanXet = ModalNhanXetComponent;
