import R from '@app/assets/R';
import { SCREEN_ROUTER_APP, isIOS } from '@app/commons/Constant';
import { colors } from '@app/commons/Theme';
import { Empty, Icon, Text } from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { FontSize, dimensions, spacing, vScale } from '@app/theme';
import React, { memo, useEffect, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Alert, FlatList, RefreshControl, ScrollView, StatusBar, StyleSheet, TouchableOpacity, View } from 'react-native';
import FastImage from 'react-native-fast-image';
// import { MainActionXeCoGioiComponent } from '../../Components/MainActionXeCoGioi';
import { MainActionComponent } from './MainAction';
// import {AddInspectionScheduleToPhoneEvent} from '@app/utils/';

const RenderHomeXeCoGioiComponent = (props) => {
  const { setRefreshing, refreshing, userInfo } = props;
  const [profileMenu, setProfileMenu] = useState([]);
  const [menuDataHoSoDaKetThucGiamDinh, setMenuDataHoSoDaKetThucGiamDinh] = useState([]);
  const [areaMenu, setAreaMenu] = useState([]);

  useEffect(() => {
    getHomeMenuXeCoGioi();
    // AddInspectionScheduleToPhoneEvent(axiosConfig.ACTION_CODE.LAY_DS_LICH_GIAM_DINH_CHUNG);
  }, []);

  //Call APIs
  const getHomeMenuXeCoGioi = async () => {
    try {
      setRefreshing && setRefreshing(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DANH_MUC_HOME);
      setRefreshing && setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setProfileMenu(response.data_info.filter((e) => e.nhom === 'CA_NHAN' && e.man_hinh === 'HOME'));
      setMenuDataHoSoDaKetThucGiamDinh(response.data_info.filter((e) => e.nhom === 'CA_NHAN' && e.man_hinh === 'CHI_TIET'));
      setAreaMenu(response.data_info.filter((e) => e.nhom === 'KHU_VUC'));
    } catch (error) {
      setRefreshing && setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  //ACTIONs
  const onPressMenuProfile = (item, label) => {
    if (item.loai === 'DA_KET_THUC_GD') NavigationUtil.push(SCREEN_ROUTER_APP.MENU_HS_DA_KET_THUC_GD, { title: label, menuData: menuDataHoSoDaKetThucGiamDinh });
    else if (item.loai) NavigationUtil.push(SCREEN_ROUTER_APP.PROFILE, { profileTitle: label, loaiHoSo: item.loai });
  };

  /* RENDER */
  const renderProfileSectionHeader = (title, txtColor, bgColor, iconColor) => {
    return (
      <View style={[styles.profileSectionView, { backgroundColor: bgColor }]}>
        <View flexDirection="row" alignItems="center">
          <Icon.MaterialCommunityIcons name="folder-account-outline" size={24} color={iconColor} />
          <Text style={[styles.headerTitle, { color: txtColor }]} font="regular16">
            {title}
          </Text>
        </View>
      </View>
    );
  };

  const renderProfileContentSection = (menuData) => {
    const renderItem = ({ item, index }) => {
      const lastItem = menuData.length - 1;
      const border0 = lastItem === index && { borderBottomWidth: 0 };
      return (
        <TouchableOpacity style={[styles.itemRow, border0]} onPress={() => onPressMenuProfile(item, item.ten)}>
          <Text style={styles.txtLabel}>{item.ten}</Text>
          <View flexDirection="row" alignItems="center">
            <View style={styles.countView}>
              <Text style={styles.txtCount}>{item.sl || 0}</Text>
            </View>
            <Icon.Ionicons name={'arrow-forward-outline'} size={20} color={colors.GRAY} style={{ marginLeft: 10 }} />
          </View>
        </TouchableOpacity>
      );
    };

    return (
      <View style={styles.contentProfileSectionView}>
        <FlatList data={menuData} renderItem={renderItem} keyExtractor={(e, i) => i.toString()} ListEmptyComponent={<Empty imageStyle={styles.emptyImage} />} />
      </View>
    );
  };

  return (
    <ScrollView
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={() => getHomeMenuXeCoGioi()} />}
      showsVerticalScrollIndicator={false}
      style={styles.scrollViewStyle}
      endFillColor="#FFF">
      {/* <MainActionXeCoGioiComponent userInfo={userInfo} /> */}
      <MainActionComponent />
      <View style={styles.listProfileBg}>
        <View style={styles.tabViewList}>
          <View>
            {renderProfileSectionHeader('Hồ sơ cá nhân', colors.PRIMARY, '#e5eaf0', colors.PRIMARY)}
            {renderProfileContentSection(profileMenu)}
            {renderProfileSectionHeader('Hồ sơ phân tự động theo khu vực', colors.PRIMARY_RED, '#FDE9E9', colors.PRIMARY_RED)}
            {renderProfileContentSection(areaMenu)}
          </View>
        </View>
      </View>
      {/* <View style={styles.bottomView} /> */}
    </ScrollView>
  );
};

export const RenderHomeXeCoGioi = memo(RenderHomeXeCoGioiComponent, isEqual);

const STATUSBAR_HEIGHT = StatusBar.currentHeight;
const APPBAR_HEIGHT = isIOS ? 44 : 56;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.PRIMARY,
  },
  tabView: {
    flexDirection: 'row',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  tabBar: {
    flex: 1,
    borderBottomWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: colors.BORDER_GRAY,
    paddingVertical: vScale(spacing.medium),
  },

  borderBottom: {
    borderBottomWidth: 3,
    borderColor: colors.PRIMARY,
  },
  tabBarTxt: {
    color: '#566573',
    textAlign: 'center',
    fontSize: FontSize.size16,
  },
  tabBarTxtActive: {
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size15,
  },
  btnQRCode: {
    paddingHorizontal: spacing.small,
  },
  tabViewList: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: colors.WHITE,
    marginHorizontal: spacing.medium,
    marginBottom: 20,
  },
  txtName: {
    color: colors.WHITE,
    fontSize: FontSize.size16,
    fontWeight: '500',
    lineHeight: 20,
  },
  txtPhone: {
    lineHeight: 20,
    fontWeight: '500',
    color: colors.WHITE,
    fontSize: FontSize.size14,
    opacity: 0.7,
  },
  menuBtn: {
    width: 44,
    height: 44,
    marginRight: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  headerMenu: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: vScale(10),
    justifyContent: 'space-between',
    backgroundColor: colors.PRIMARY,
    paddingHorizontal: spacing.medium,
  },
  iconUser: {
    width: 18,
    height: 18,
  },
  iconBell: {
    width: 22,
    height: 24,
  },
  notifyCount: {
    top: -8,
    right: -10,
    width: 20,
    height: 20,
    borderRadius: 20,
    textAlign: 'center',
    position: 'absolute',
    justifyContent: 'center',
    fontSize: FontSize.size14,
    backgroundColor: colors.PRIMARY_RED,
  },
  txtCountNotify: {
    alignSelf: 'center',
    color: colors.WHITE,
    fontSize: FontSize.size12,
  },
  btnNoti: {
    marginRight: 10,
  },
  iconBottom: {
    width: 22,
    height: 22,
    marginRight: 10,
  },
  profileSectionView: {
    flex: 1,
    borderBottomWidth: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderColor: colors.BORDER_GRAY,
    padding: spacing.medium,
    marginTop: 20,
  },
  contentProfileSectionView: {
    flex: 1,
    borderWidth: 1,
    paddingHorizontal: spacing.medium,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    borderColor: colors.BORDER_GRAY,
    borderTopWidth: 0,
  },
  headerTitle: {
    fontSize: FontSize.size16,
    fontWeight: '500',
    lineHeight: 20,
    marginLeft: 10
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: colors.BORDER_GRAY,
    paddingVertical: spacing.medium,
  },
  icArrowRight: {
    width: 16,
    height: 16,
    marginLeft: 10,
  },
  countView: {
    backgroundColor: colors.PRIMARY_RED,
    borderRadius: 20,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 40,
    alignItems: 'center',
  },
  txtCount: {
    color: colors.WHITE,
    fontSize: FontSize.size12,
    fontWeight: '500',
  },
  txtLabel: {
    color: colors.IN_ACTIVE_BLACK,
    fontSize: FontSize.size16,
    lineHeight: 20,
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },

  statusBar: {
    height: STATUSBAR_HEIGHT,
  },
  appBar: {
    backgroundColor: '#79B45D',
    height: APPBAR_HEIGHT,
  },
  content: {
    flex: 1,
    backgroundColor: '#33373B',
  },
  listProfileBg: {
    backgroundColor: '#FFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scrollViewStyle: {
    backgroundColor: '#FFF',
  },
  filterNghiepVuBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: spacing.medium,
  },
  filterView: {
    position: 'absolute',
    right: spacing.medium,
    zIndex: 99,
    backgroundColor: 'white',
    top: spacing.mediumPlush,
    borderWidth: 1,
    borderColor: colors.GRAY,
    padding: spacing.tiny,
    borderRadius: 4,
    paddingHorizontal: spacing.small,
    width: dimensions.width * 0.5 - 16,
  },
  itemNghiepVuBtn: {
    marginVertical: spacing.smaller,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bottomView: {
    position: 'absolute',
    bottom: -600,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    height: 600,
  },
});
