import { SCREEN_ROUTER_APP } from '@app/commons/Constant';
import { colors } from '@app/commons/Theme';
import { Icon, Text } from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import { FontSize, dimensions, spacing, vScale } from '@app/theme';
import { FlashMessageHelper } from '@app/utils/FlashMessageHelper';
import React, { memo, useCallback } from 'react';
import isEqual from 'react-fast-compare';
import { StyleSheet, TouchableOpacity, View } from 'react-native';


const HOME_MENU_ACTION_TITLE = [
  'Tra cứu bảo hiểm', //0
  'Danh bạ', //1
  'Cơ sở y tế', //2
  'Bảo lãnh viện phí', //3
  'Tiếp nhận trực tiếp', //4
  'Tính toán bồi thường', //5
];


const MainActionConNguoiComponent = () => {

  const onPressMenu = (screenName) => {
    if (screenName !== '') {
      let params = {};
      NavigationUtil.navigate(screenName, params);
    } else {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tính năng đang phát triển!');
    }
  };
  const renderItemScreen = useCallback((title, screenName, iconName, iconColor, iconType) => {
    let styleView = [styles.actionItem];
    return (
      <TouchableOpacity style={styleView} onPress={() => onPressMenu(screenName)} activeOpacity={0.7}>
        {iconType === 'Ionicons' && <Icon.Ionicons name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'MaterialCommunityIcons' && <Icon.MaterialCommunityIcons name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'FontAwesome5' && <Icon.FontAwesome5 name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'Foundation' && <Icon.Foundation name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'Feather' && <Icon.Feather name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'MaterialIcons' && <Icon.MaterialIcons name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'SimpleLineIcons' && <Icon.SimpleLineIcons name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'Fontisto' && <Icon.Fontisto name={iconName} size={20} color={iconColor} style={styles.iconActionItem} />}
        <Text style={styles.txtActionTitle}>{title}</Text>
      </TouchableOpacity>
    );
  }, []);

  return (
    <View style={styles.listActionView}>
      <View style={styles.listActionView}>
        {renderItemScreen(HOME_MENU_ACTION_TITLE[0], SCREEN_ROUTER_APP.SEARCH, 'search', colors.BLUE1, 'FontAwesome5')}
        {renderItemScreen(HOME_MENU_ACTION_TITLE[1], SCREEN_ROUTER_APP.PHONE_BOOK, 'phone', colors.RED3, 'FontAwesome5')}
        {renderItemScreen(HOME_MENU_ACTION_TITLE[2], SCREEN_ROUTER_APP.TRA_CUU_DICH_VU_Y_TE, 'clinic-medical', colors.RED1, 'FontAwesome5')}
      </View>
      <View style={[styles.listActionView, { borderBottomWidth: 0.3 }]}>
        {renderItemScreen(HOME_MENU_ACTION_TITLE[3], SCREEN_ROUTER_APP.DS_HO_SO_BAO_BAO_LANH, 'user-shield', colors.VIOLET1, 'FontAwesome5')}
        {renderItemScreen(HOME_MENU_ACTION_TITLE[4], SCREEN_ROUTER_APP.DS_HS_TIEP_NHAN_CON_NG, 'emoticon-sick', colors.GREEN, 'MaterialCommunityIcons')}
        {renderItemScreen(HOME_MENU_ACTION_TITLE[5], SCREEN_ROUTER_APP.DS_HS_TINH_TOAN_BT_CON_NG, 'calculator-variant', colors.BLUE1, 'MaterialCommunityIcons')}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  actionItem: {
    alignItems: 'center',
    borderColor: colors.GRAY,
    width: dimensions.width / 3,
    paddingVertical: vScale(spacing.small),
    borderLeftWidth: 0.3,
  },
  iconActionItem: {
    marginBottom: vScale(4),
  },
  listActionView: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignContent: 'space-between',
    flexWrap: 'wrap',
    width: dimensions.width,
    alignSelf: 'center',
    borderTopWidth: 0.3,
    borderColor: colors.GRAY,
  },
  txtActionTitle: {
    textAlign: 'center',
    fontSize: FontSize.size12,
    paddingHorizontal: spacing.tiny,
  },
});
export const MainActionConNguoi = memo(MainActionConNguoiComponent, isEqual);
