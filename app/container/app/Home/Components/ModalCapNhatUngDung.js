import {isIOS, URL_APP_ON_STORE} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Text} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import R from '@R';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Linking, StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import Modal from 'react-native-modal';
const ModalCapNhatUngDungComponent = forwardRef((props, ref) => {
  const {} = props;

  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  const hideModal = () => setIsVisible(false);

  const onPressCapNhatUngDung = () => Linking.openURL(isIOS ? URL_APP_ON_STORE.IOS : URL_APP_ON_STORE.ANDROID);

  /* RENDER */

  return (
    <Modal
      // onBackButtonPress={hideModal}
      // onBackdropPress={hideModal}
      animationIn="fadeIn"
      animationOut="fadeOut"
      isVisible={isVisible}
      style={styles.modal}>
      <View style={styles.content}>
        <FastImage source={R.images.img_app_update} style={styles.imgAppUpDate} resizeMode="contain" />
        <View style={styles.descView}>
          <Text font="bold16" style={styles.txtCapNhatUngDung}>
            Cập nhật ứng dụng
          </Text>
          <Text style={styles.txtVuiLongCapNhat}>Ứng dụng chưa được cập nhật. Vui lòng cập nhật ứng dụng để có được trải nghiệm tốt nhất.</Text>
          <View style={styles.btnView}>
            {/* <ButtonLinear title="Để sau" linearStyle={{flex: 0, width: dimensions.width * 0.5, marginBottom: spacing.small}} linearColors={[colors.GRAY1, colors.GRAY]} onPress={hideModal} /> */}
            <ButtonLinear title="ĐỒNG Ý" linearStyle={{flex: 0, width: dimensions.width * 0.5}} onPress={onPressCapNhatUngDung} />
          </View>
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    paddingVertical: 10,
    // backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.medium,
    marginHorizontal: spacing.mediumPlush,
    height: dimensions.height * 0.8,
    borderRadius: 20,
    backgroundColor: '#FFF',
  },
  descView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imgAppUpDate: {
    width: dimensions.width * 0.7,
    aspectRatio: 1,
  },
  txtCapNhatUngDung: {
    textTransform: 'uppercase',
    color: colors.PRIMARY,
  },
  btnView: {
    marginTop: spacing.mediumPlush,
  },
  txtVuiLongCapNhat: {
    textAlign: 'center',
    marginTop: spacing.small,
    marginHorizontal: spacing.smaller,
    color: colors.GRAY_DARK1,
  },
});

export const ModalCapNhatUngDung = memo(ModalCapNhatUngDungComponent, isEqual);
