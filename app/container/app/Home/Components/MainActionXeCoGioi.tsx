import R from '@R';
import { SCREEN_ROUTER_APP } from '@app/commons/Constant';
import { colors } from '@app/commons/Theme';
import { Text } from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import { FontSize, dimensions, spacing, vScale } from '@app/theme';
import { FlashMessageHelper } from '@app/utils/FlashMessageHelper';
import React, { memo } from 'react';
import isEqual from 'react-fast-compare';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { HOME_MENU_ACTION_TITLE } from '../ProfileAssessment/Components/Constant';


const HOME_MENU_ACTION_TITLE = [
  'Tra cứu bảo hiểm', //0
  'Danh bạ', //1
  'Cơ sở y tế', //2
  'Bảo lãnh viện phí', //3
  'Tiếp nhận trực tiếp', //4
  'Tính toán bồi thường', //5
];

const MainActionXeCoGioi = ({ userInfo }) => {
  // const isUserReviewIOS = userInfo?.nguoi_dung?.nsd === USER_REVIEW_IOS;
  const renderItemScreen = (title, screenName, source) => {
    return (
      <TouchableOpacity
        style={styles.actionItem}
        onPress={() => {
          if (title === '') return;
          screenName ? NavigationUtil.navigate(screenName) : FlashMessageHelper.showFlashMessage('Thông báo', 'Tính năng sắp ra mắt');
        }}
        // onLongPress={() => {
        //   if (isUserReviewIOS && screenName === SCREEN_ROUTER_APP.DS_HO_SO_KHAI_BAO_TON_THAT) NavigationUtil.navigate(SCREEN_ROUTER_APP.APPROVAL);
        // }}
        activeOpacity={0.7}>
        <FastImage source={source} resizeMode="contain" style={styles.imgIcons} />
        <Text style={styles.txtActionTitle}>{title}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.listActionView}>
      <View style={styles.renderRow}>
        {renderItemScreen(HOME_MENU_ACTION_TITLE[0], SCREEN_ROUTER_APP.LICH_GIAM_DINH, R.icons.ic_calendar)}
        {/* {!isUserReviewIOS && renderItemScreen(HOME_MENU_ACTION_TITLE[1], SCREEN_ROUTER_APP.APPROVAL, R.icons.ic_user_check)}
        {isUserReviewIOS && renderItemScreen(HOME_MENU_ACTION_TITLE[7], isUserReviewIOS ? SCREEN_ROUTER_APP.DS_HO_SO_KHAI_BAO_TON_THAT : '', R.icons.ic_social_care)} */}
        {renderItemScreen(HOME_MENU_ACTION_TITLE[2], SCREEN_ROUTER_APP.GARA, R.icons.ic_repair_car)}
      </View>
      <View style={styles.renderRow}>
        {renderItemScreen(HOME_MENU_ACTION_TITLE[3], SCREEN_ROUTER_APP.SEARCH, R.icons.ic_search_gcn)}
        {renderItemScreen(HOME_MENU_ACTION_TITLE[4], SCREEN_ROUTER_APP.PHONE_BOOK, R.icons.ic_contact)}
        {renderItemScreen(HOME_MENU_ACTION_TITLE[5], SCREEN_ROUTER_APP.ANNUAL_LEAVE, R.icons.ic_calender_check)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  actionItem: {
    alignItems: 'center',
    width: (dimensions.width - 64) / 3,
    height: (dimensions.width - 64) / 3,
    paddingVertical: vScale(spacing.small),
    paddingHorizontal: spacing.small,
    backgroundColor: colors.WHITE,
    borderRadius: 12,
  },
  iconActionItem: {
    marginBottom: vScale(4),
  },
  listActionView: {
    backgroundColor: colors.PRIMARY,
    paddingTop: vScale(spacing.small),
    // borderWidth: 1,
    // borderColor: 'blue',
  },
  txtActionTitle: {
    textAlign: 'center',
    fontSize: FontSize.size14,
    marginTop: spacing.medium,
    lineHeight: 20,
    color: '#182531',
  },
  imgIcons: {
    width: 32,
    height: 32,
  },
  renderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: spacing.medium,
    marginBottom: spacing.small,
  },
});
export const MainActionXeCoGioiComponent = memo(MainActionXeCoGioi, isEqual);
