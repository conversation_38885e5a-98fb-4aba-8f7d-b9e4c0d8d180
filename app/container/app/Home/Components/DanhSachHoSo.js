import R from '@app/assets/R';
import {DATA_CONSTANT, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Empty, Icon, ProfileItem, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import React, {memo, useCallback} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';

const DanhSachHoSo = (props) => {
  const {listHoSo, title} = props;
  let {TEN_TRANG_THAI_HO_SO} = DATA_CONSTANT;

  const onPressViewAll = useCallback(() => NavigationUtil.push(SCREEN_ROUTER_APP.PROFILE, {profileTitle: title}), []);

  const onPressItem = (item) => {
    if (item.nghiep_vu === 'XE_MAY') {
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY, {
        profileDetail: item,
      });
    } else {
      NavigationUtil.push(SCREEN_ROUTER_APP.PROFILE_ASSESSMENT, {
        profileDetail: item,
      });
    }
  };
  /* RENDER */
  const renderProfileItem = useCallback(({index, item}) => {
    let isHoSoKhac = false;

    if (
      title === 'Hồ sơ cá nhân' &&
      item.trang_thai_ten !== TEN_TRANG_THAI_HO_SO.CHO_CHI_DINH_GDV &&
      item.trang_thai_ten !== TEN_TRANG_THAI_HO_SO.DANG_GIAM_DINH &&
      item.trang_thai_ten !== TEN_TRANG_THAI_HO_SO.YEU_CAU_GIAM_DINH
    )
      isHoSoKhac = true;
    const icColor = item.nghiep_vu === 'XE_MAY' ? colors.VIOLET1 : colors.PRIMARY;
    const icName = item.nghiep_vu === 'XE_MAY' ? 'motorcycle' : 'automobile';
    const icSize = item.nghiep_vu === 'XE_MAY' ? 16 : 15;
    return (
      <TouchableOpacity key={index} onPress={() => onPressItem(item)} style={styles.profileItemView}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemLinearView}>
          <View style={styles.profileItemCenterView}>
            <Text style={styles.profileTxtHoSo}>{item.so_hs}</Text>
            <View style={styles.profileItemDetail}>
              <View style={styles.profileTimeView}>
                <FastImage source={R.images.img_clock} style={styles.profileImgClock} />
                <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.ngay_tb}</Text>
              </View>
              <View flexDirection="row">
                <Icon.FontAwesome name={icName} size={icSize} style={styles.iconItem} color={icColor} />
                <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.doi_tuong}</Text>
              </View>
            </View>

            <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
              <View style={styles.profileTimeView}>
                <Icon.Entypo name="info-with-circle" size={FontSize.size15} style={styles.iconItem} color={colors.BLUE3} />
                <Text style={styles.profileTxtThoiGian(isHoSoKhac ? colors.GREEN : colors.GRAY5)}>{item.trang_thai_ten}</Text>
              </View>
            </View>
            <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
              <View style={styles.profileTimeView}>
                <Icon.Entypo name="info-with-circle" size={FontSize.size15} style={styles.iconItem} color={colors.BLUE3} />
                <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.nv === 'TN' ? 'Tự nguyện' : 'Bắt buộc'}</Text>
              </View>
            </View>
            <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
              <View style={styles.profileTimeView}>
                <Icon.Entypo name="location" size={FontSize.size15} style={styles.iconItem} color={colors.BLUE3} />
                <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.hien_truong === 'K' ? 'Xe không ở hiện trường' : 'Xe đang ở hiện trường'}</Text>
              </View>
            </View>
          </View>
          <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={FontSize.size25} style={styles.iconRightItem} color={colors.BLUE1} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  }, []);

  return (
    <>
      <FlatList
        scrollEnabled={false}
        data={listHoSo}
        renderItem={(data) => <ProfileItem data={data} onPressItem={() => onPressItem(data.item)} dataType="HS_HOME" />}
        ListEmptyComponent={<Empty />}
        keyExtractor={(e, i) => i.toString()}
      />
      <TouchableOpacity onPress={onPressViewAll} style={styles.viewBottomBtn}>
        <Text style={styles.txtBottomBtn}>Xem tất cả</Text>
      </TouchableOpacity>
    </>
  );
};

const styles = StyleSheet.create({
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: vScale(spacing.tiny),
    paddingRight: scale(spacing.tiny),
  },
  profileTxtHoSo: {
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    marginBottom: vScale(spacing.tiny),
  },
  profileTxtThoiGian: (color) => {
    return {
      color: color,
      fontWeight: 'bold',
      fontSize: FontSize.size12,
    };
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImgClock: {
    marginRight: spacing.tiny,
    width: FontSize.size15,
    height: FontSize.size15,
    opacity: 0.8,
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemLinearView: {
    flexDirection: 'row',
    marginVertical: scale(spacing.tiny),
    paddingLeft: scale(spacing.medium),
    paddingRight: scale(spacing.smaller),
    borderRadius: 10,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
    marginHorizontal: scale(spacing.smaller),
  },
  profileItemView: {
    shadowColor: colors.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },

  viewBottomBtn: {
    marginVertical: vScale(spacing.medium),
  },
  txtBottomBtn: {
    fontSize: FontSize.size16,
    textAlign: 'center',
    color: colors.PRIMARY,
    textDecorationLine: 'underline',
  },
  iconItem: {
    marginRight: spacing.tiny,
    opacity: 0.6,
  },
  iconRightItem: {
    opacity: 0.6,
    alignSelf: 'center',
  },
});
export const DanhSachHoSoComponent = memo(DanhSachHoSo, isEqual);
