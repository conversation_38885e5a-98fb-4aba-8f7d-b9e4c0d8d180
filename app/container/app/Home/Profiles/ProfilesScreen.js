import R from '@R';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ProfileItem, ScreenComponent, SearchBar, Text} from '@component';
import {IS_PROD, SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, Keyboard, RefreshControl, View} from 'react-native';
import styles from './ProfilesStyle';
import {AsyncStorageProvider, logErrorServer} from '@app/utils';

const ProfilesScreenComponent = ({navigation, route}) => {
  console.log('ProfilesScreenComponent');
  const {loaiHoSo, profileTitle} = route.params;

  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState([]);
  const [searchInput, setSearchInput] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(20);

  useEffect(() => {
    navigation.addListener('focus', () => {
      setDialogLoading(true);
      getListIndemnify(loaiHoSo, []);
      setCurrent(1);
    });
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    getListIndemnify(loaiHoSo, []);
    setCurrent(1);
  };

  //lấy danh sách hồ sơ giám định
  const getListIndemnify = async (type, oldData = [], trang = 1, so_dong = 20, bien_xe = '') => {
    let params = {
      loai: type,
      so_dong: so_dong,
      trang: trang,
      bien_xe: bien_xe,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DANH_MUC_THEO_LOAI_HS, params);
      setRefreshing(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info.filter((item) => item.loai === type);
      setTotal(response.out_value.tong_so_dong);
      let listHoSoTmp = [...oldData, ...arrData];
      if (type === 'DANG_GIAM_DINH' && !IS_PROD) await AsyncStorageProvider.luuListHoSoDangGiamDinh(listHoSoTmp);
      setProfileData(listHoSoTmp);
    } catch (error) {
      setRefreshing(false);
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setDialogLoading(true);
      setCurrent(current + 1);
      getListIndemnify(loaiHoSo, profileData, current + 1);
    }
  };
  const onChangeTextSearch = (value) => {
    setSearchInput(value);
    if (!value.trim()) getListIndemnify(loaiHoSo, []);
  };
  //search hồ sơ giám định
  const onPressSearch = () => {
    Keyboard.dismiss();
    if (!searchInput.trim()) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập từ khoá', 'info');
    getListIndemnify(loaiHoSo, [], 1, 100, searchInput);
  };

  const onPressItem = (item) => {
    if (item.nghiep_vu === 'XE_MAY') {
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY, {
        profileDetail: item,
      });
    } else {
      NavigationUtil.push(SCREEN_ROUTER_APP.PROFILE_ASSESSMENT, {
        profileDetail: item,
      });
    }
  };

  /** RENDER */
  // const renderProfileItem = ({item}) => {
  //   let isHoSoKhac = false;
  //   if (
  //     item.ten_trang_thai !== DATA_CONSTANT.TEN_TRANG_THAI_HO_SO.CHO_CHI_DINH_GDV &&
  //     item.ten_trang_thai !== DATA_CONSTANT.TEN_TRANG_THAI_HO_SO.DANG_GIAM_DINH &&
  //     item.ten_trang_thai !== DATA_CONSTANT.TEN_TRANG_THAI_HO_SO.YEU_CAU_GIAM_DINH
  //   )
  //     isHoSoKhac = true;
  //   const icColor = item.nghiep_vu === 'XE_MAY' ? colors.VIOLET1 : colors.PRIMARY;
  //   const icName = item.nghiep_vu === 'XE_MAY' ? 'motorcycle' : 'automobile';
  //   const icSize = item.nghiep_vu === 'XE_MAY' ? 16 : 15;
  //   return (
  //     <TouchableOpacity onPress={() => onPressItem(item)} style={styles.profileItemView}>
  //       <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemLinearView}>
  //         <View style={styles.profileItemCenterView}>
  //           <Text style={styles.profileTxtHoSo}>{item.so_hs}</Text>
  //           <View style={styles.profileItemDetail}>
  //             <View style={styles.profileTimeView}>
  //               <Image source={R.images.img_clock} style={styles.profileImgClock} />
  //               <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.ngay_tb}</Text>
  //             </View>
  //             <View flexDirection="row">
  //               <Icon.FontAwesome name={icName} size={icSize} style={{marginRight: spacing.tiny, opacity: 0.6}} color={icColor} />
  //               <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.doi_tuong}</Text>
  //             </View>
  //           </View>
  //           <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
  //             <View style={styles.profileTimeView}>
  //               <Icon.Entypo name="info-with-circle" size={15} style={{marginRight: spacing.tiny, opacity: 0.6}} color={colors.BLUE3} />
  //               <Text style={styles.profileTxtThoiGian(isHoSoKhac ? colors.GREEN : colors.GRAY5)}>{item.ten_trang_thai}</Text>
  //             </View>
  //           </View>
  //           <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
  //             <View style={styles.profileTimeView}>
  //               <Icon.Entypo name="info-with-circle" size={15} style={{marginRight: spacing.tiny, opacity: 0.6}} color={colors.BLUE3} />
  //               <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.nv === 'TN' ? 'Tự nguyện' : 'Bắt buộc'}</Text>
  //             </View>
  //           </View>
  //           <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
  //             <View style={styles.profileTimeView}>
  //               <Icon.Entypo name="info-with-circle" size={15} style={{marginRight: spacing.tiny, opacity: 0.6}} color={colors.BLUE3} />
  //               <Text style={styles.profileTxtThoiGian(colors.GRAY5)}>{item.hien_truong === 'K' ? 'Xe không ở hiện trường' : 'Xe đang ở hiện trường'}</Text>
  //             </View>
  //           </View>
  //         </View>
  //         <View style={styles.profileItemRightView}>
  //           <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
  //         </View>
  //       </LinearGradient>
  //     </TouchableOpacity>
  //   );
  // };

  // const renderProfile = () => {
  //   return (
  //     <View>
  //       {profileData.length > 0 ? (
  //         <>
  //           {/* <FlatList data={profileData} renderItem={renderProfileItem} keyExtractor={(item) => item.so_id} /> */}
  //           {profileData.map((item, index) => renderProfileItem({item: item, index: index}))}
  //         </>
  //       ) : (
  //         <View style={styles.noDataView}>
  //           <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
  //           <Text>Chưa có dữ liệu</Text>
  //         </View>
  //       )}
  //     </View>
  //   );
  // };
  // const renderRadioFilter = () => {
  //   return (
  //     <View style={styles.radioView}>
  //       <Text style={styles.dropDownTitle} children="Trạng thái hồ sơ" />
  //       <RadioGroup radioButtons={profileTypeRadio} onPress={onPressLoaiHoSo} layout={'row'} />
  //     </View>
  //   );
  // };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={profileTitle}
      renderView={
        <View style={styles.container}>
          {/* {route.params.profileTitle === profileTile[1] && renderRadioFilter()} */}
          {/* <View flexDirection="row" justifyContent="space-between">
            {DATA_NV.map((e) => {
              return (
                <TouchableOpacity style={styles.btn} onPress={() => filterTheoNghiepVu(e.ma)}>
                  <Text>{e.label}</Text>
                </TouchableOpacity>
              );
            })}
          </View> */}
          <SearchBar
            placeholder="Biển số xe, tên khách hàng, số hồ sơ"
            onTextChange={(value) => onChangeTextSearch(value)}
            onPressSearch={onPressSearch}
            value={searchInput}
            onSubmitEditing={onPressSearch}
          />
          <FlatList
            data={profileData || []}
            renderItem={(data) => <ProfileItem data={data} onPressItem={() => onPressItem(data.item)} dataType="HS_CN" />}
            keyExtractor={(item, index) => item.so_id + index.toString()}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.3}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            ListEmptyComponent={
              <View style={styles.noDataView}>
                <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
                <Text>Chưa có dữ liệu</Text>
              </View>
            }
          />
          {/* <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} showsVerticalScrollIndicator={true} onMomentumScrollEnd={(event) => handleLoadMore()}>
            <View style={styles.contentView}>{renderProfile()}</View>
          </ScrollView> */}
        </View>
      }
    />
  );
};

export const ProfilesScreen = memo(ProfilesScreenComponent, isEqual);
