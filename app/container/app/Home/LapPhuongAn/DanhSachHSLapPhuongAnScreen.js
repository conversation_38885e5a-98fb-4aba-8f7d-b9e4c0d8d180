import {HOME_ACTION_TYPE, NGHIEP_VU, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {Empty, ProfileItem, ScreenComponent} from '@app/components';
import ListProfilesHeader from '@app/components/ListProfilesHeader';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView} from 'react-native';
import {ModalTimKiemHoSoLapPhuongAn} from './Components';
import styles from './DanhSachHSLapPhuongAnStyles';

let timer;

const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment().format('YYYYMMDD');

const DanhSachHSLapPhuongAnScreenComponent = (props) => {
  console.log('DanhSachHSLapPhuongAnScreenComponent');

  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);

  const [objParams, setObjParams] = useState({
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    so_hd: '',
    so_hs: '',
    nv: '',
    bien_xe: '',
    ten_kh: '',
  });
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(20);
  const [current, setCurrent] = useState(1);

  let refModalTimKiem = useRef(null);

  useEffect(() => {
    timer = setTimeout(() => {
      getData(objParams);
    }, 500);
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getData = async (defaultObj, oldData = [], trang = 1, so_dong = 20, isSearch = false) => {
    setDialogLoading(true);
    let subObj = {
      trang: trang,
      so_dong: so_dong,
    };
    try {
      let params = {...subObj, ...defaultObj};
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_HS_LAP_PHUONG_AN, params);
      setRefreshing(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.data.length > 0) {
        refModalTimKiem.current.hide();
      } else if (isSearch) {
        Alert.alert('Thông báo', 'Không tìm thấy kết quả phù hợp!');
      }
      setTotal(response.data_info.tong_so_dong);
      let arrData = response.data_info?.data;
      let mergeData = [...oldData, ...arrData];
      setData(mergeData);
    } catch (error) {
      setRefreshing(false);
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onRefresh = () => {
    setDialogLoading(true);
    setCurrent(1);
    getData(objParams, [], 1, 20);
  };

  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setCurrent(current + 1);
      getData(objParams, data, current + 1);
    }
  };

  const onPressSearch = (params) => {
    getData(params, [], 1, 20, true);
    setObjParams(params);
  };

  const onPressItem = (item) => {
    let screen = item.nghiep_vu === NGHIEP_VU.XE_MAY ? SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY : SCREEN_ROUTER_APP.PROFILE_ASSESSMENT;
    NavigationUtil.push(screen, {
      profileDetail: item,
      actionType: HOME_ACTION_TYPE.LAP_PHUONG_AN,
    });
  };

  //!Tạm thời chưa bỏ 21/6/2024
  // const renderProfileItem = ({item}) => {
  //   const icColor = item.nghiep_vu === NGHIEP_VU.XE_MAY ? colors.VIOLET1 : colors.PRIMARY;
  //   const icName = item.nghiep_vu === NGHIEP_VU.XE_MAY ? 'motorcycle' : 'automobile';
  //   const icSize = item.nghiep_vu === NGHIEP_VU.XE_MAY ? 16 : 15;
  //   return (
  //     <TouchableOpacity onPress={() => onPressItem(item)}>
  //       <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
  //         <View style={styles.profileItemCenterView}>
  //           <Text style={[styles.profileTxtThoiGian]}>
  //             Số hồ sơ: <Text style={styles.txtSoHS}>{item.so_hs}</Text>
  //           </Text>
  //           <Text style={[styles.profileTxtThoiGian]}>
  //             Ngày mở hồ sơ: <Text style={styles.detail}>{item.ngay}</Text>
  //           </Text>
  //           <Text style={[styles.profileTxtThoiGian]}>
  //             Tên khách hàng: <Text style={[styles.detail]} children={item.ten_kh} />
  //           </Text>
  //           <View flexDirection="row" justifyContent="space-between">
  //             <View flex={1} flexDirection="row" alignItems="center">
  //               <Icon.FontAwesome name={icName} size={icSize} style={{marginRight: spacing.smaller}} color={icColor} />
  //               <Text style={styles.detail} children={item.doi_tuong} />
  //             </View>
  //             <View flex={1}>
  //               <Text style={[styles.profileTxtThoiGian]}>
  //                 N.vụ: <Text style={styles.detail} children={item.nv} />
  //               </Text>
  //             </View>
  //           </View>

  //           <Text style={[styles.profileTxtThoiGian]}>
  //             Trạng thái: <Text style={[styles.detail, {color: colors.GREEN}]} children={item.trang_thai} />
  //           </Text>
  //         </View>
  //         <View style={styles.profileItemRightView}>
  //           <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
  //         </View>
  //       </LinearGradient>
  //     </TouchableOpacity>
  //   );
  // };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Lập phương án"
      renderView={
        <SafeAreaView style={styles.container}>
          <ListProfilesHeader ngayDau={objParams.ngay_d} ngayCuoi={objParams.ngay_c} onPressSearch={() => refModalTimKiem.current.show()} />
          <FlatList
            data={data}
            // renderItem={renderProfileItem}
            renderItem={(data) => <ProfileItem data={data} onPressItem={() => onPressItem(data.item)} dataType="HS_LPA" />}
            onEndReachedThreshold={0.5}
            onEndReached={handleLoadMore}
            keyExtractor={(_, index) => index.toString()}
            ListEmptyComponent={<Empty imageStyle={styles.imageNoData} />}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          />
          <ModalTimKiemHoSoLapPhuongAn
            ref={refModalTimKiem}
            loading={dialogLoading}
            initFormInput={objParams}
            setLoading={setDialogLoading}
            onPressSearch={(params) => onPressSearch(params)}
            onBackPress={() => refModalTimKiem.current.hide()}
          />
        </SafeAreaView>
      }
    />
  );
};

export const DanhSachHSLapPhuongAnScreen = memo(DanhSachHSLapPhuongAnScreenComponent, isEqual);
