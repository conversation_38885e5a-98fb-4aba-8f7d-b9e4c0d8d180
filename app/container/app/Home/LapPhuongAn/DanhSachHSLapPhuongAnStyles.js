import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },
  imageNoData: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  profileItemView: {
    borderRadius: 10,
    borderWidth: 0.4,
    flexDirection: 'row',
    paddingLeft: scale(10),
    borderColor: colors.GRAY,
    marginVertical: vScale(5),
    marginHorizontal: scale(10),
  },
  profileItemCenterView: {
    flex: 1,
    paddingRight: scale(5),
    paddingVertical: vScale(5),
    borderBottomColor: colors.GRAY4,
  },
  profileItemRightView: {
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: colors.GRAY4,
  },
  profileItemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'space-between',
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  profileTxtHoSo: {
    fontSize: 14,
    color: colors.BLACK_03,
    marginBottom: vScale(4),
  },
  profileImgClock: {
    width: 14,
    height: 14,
    opacity: 0.8,
    marginRight: scale(5),
  },
  profileTxtThoiGian: {
    fontSize: 14,
    fontWeight: '500',
    color: 'rgba(0, 0, 0, 0.6)',
    marginVertical: scale(spacing.tiny),
  },
  noDataView: {
    alignItems: 'center',
  },

  txtTimKiem: {
    fontSize: 16,
    color: colors.WHITE,
    textTransform: 'uppercase',
    paddingVertical: vScale(10),
  },
  txtSoHS: {
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  renderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
    paddingVertical: vScale(spacing.small),
    paddingHorizontal: scale(spacing.small),
  },
  detail: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.BLACK_03,
  },
});
