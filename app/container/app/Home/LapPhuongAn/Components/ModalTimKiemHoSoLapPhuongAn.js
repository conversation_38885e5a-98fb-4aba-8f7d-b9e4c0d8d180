import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {scale, spacing, vScale} from '@app/theme';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon} from '@component';
import moment from 'moment';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Dimensions, SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment().format('YYYYMMDD');
const {width, height} = Dimensions.get('screen');

const ModalTimKiemHoSoLapPhuongAnComp = forwardRef((props, ref) => {
  const {setLoading, onBackPress, onPressSearch, loading, initFormInput} = props;
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const formClear = {
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    so_hd: '',
    so_hs: '',
    bien_xe: '',
    ten_kh: '',
  };
  const titleInput = ['Ngày đầu', 'Ngày cuối', 'Số hồ sơ', 'Số hợp đồng', 'Biển số xe', 'Tên khách hàng'];
  const [formInput, setFormInput] = useState(initFormInput);

  // modal value
  const [ngayDau, setNgayDau] = useState(moment(startOfMonth).toDate());
  const [ngayCuoi, setNgayCuoi] = useState(moment(currentDate).toDate());
  // toggle modal
  const [toggleNgayDau, setToggleNgayDau] = useState(false);
  const [toggleNgayCuoi, setToggleNgayCuoi] = useState(false);

  const onChangeText = async (field, value) => {
    setFormInput((prev) => ({...prev, [field]: value}));
  };

  useEffect(() => {
    if (ngayDau) {
      onChangeText('ngay_d', Number(moment(ngayDau).format('YYYYMMDD')));
    }
    if (ngayCuoi) {
      onChangeText('ngay_c', Number(moment(ngayCuoi).format('YYYYMMDD')));
    }
  }, [ngayDau, ngayCuoi]);

  const onSearchPress = () => {
    setLoading && setLoading(true);
    onPressSearch && onPressSearch(formInput);
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetField = () => {
    setFormInput(formClear);
    setNgayDau(moment(startOfMonth).toDate());
    setNgayCuoi(moment(currentDate).toDate());
  };

  /**RENDER  */
  const renderDateTimeComp = (toggle, setToggle, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      date={date}
      mode={mode}
      locale={'vi_VN'}
      display="spinner"
      isVisible={toggle}
      confirmTextIOS="Chọn"
      maximumDate={maxDate}
      minimumDate={minDate}
      cancelTextIOS="Để sau"
      onCancel={() => setToggle(false)}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggle, setDateTime)}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.formInput}>
          {/* Ngày đầu/Ngày cuối */}
          <View style={styles.doubleRowView}>
            <View style={{width: (width - 30) / 2}}>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                isDateTimeField
                editable={true}
                disabled={false}
                placeholder={titleInput[0]}
                styleContainer={styles.containerInput}
                onPressInput={() => setToggleNgayDau(true)}
                value={moment(ngayDau).format('DD/MM/YYYY')}
              />
              {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, setNgayDau, ngayDau, 'date', null, new Date(), 0)}
            </View>
            <View style={{width: (width - 30) / 2}}>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                editable={true}
                disabled={false}
                placeholder={titleInput[1]}
                styleContainer={styles.containerInput}
                value={moment(ngayCuoi).format('DD/MM/YYYY')}
                isDateTimeField
                onPressInput={() => setToggleNgayCuoi(true)}
              />
              {renderDateTimeComp(toggleNgayCuoi, setToggleNgayCuoi, setNgayCuoi, ngayCuoi, 'date', null, new Date(), 0)}
            </View>
          </View>

          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.so_hs}
            placeholder={titleInput[2]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_hs', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            keyboardType="default"
            value={formInput.so_hd}
            placeholder={titleInput[3]}
            onChangeText={(text) => onChangeText('so_hd', text)}
            disabled={false}
            editable={true}
            styleContainer={styles.containerInput}
          />
          <CommonOutlinedTextFieldWithIcon
            value={formInput.bien_xe}
            keyboardType="default"
            placeholder={titleInput[4]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('bien_xe', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            value={formInput.ten_kh}
            keyboardType="default"
            placeholder={titleInput[5]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('ten_kh', text)}
          />
        </View>
      </View>
    );
  };

  /* RENDER */
  return (
    <Modal onBackButtonPress={onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal title="Tìm kiếm hồ sơ" onBackPress={onBackPress} />
        <KeyboardAwareScrollView style={styles.content}>{renderFormInput()}</KeyboardAwareScrollView>
        <View style={styles.footer}>
          <ButtonLinear textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} onPress={resetField} title="Nhập lại" />
          <ButtonLinear loading={loading} onPress={onSearchPress} linearStyle={{marginLeft: spacing.small}} title="Tìm kiếm" />
        </View>
      </SafeAreaView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  content: {
    marginHorizontal: scale(spacing.small),
    paddingTop: 10,
  },
  modalView: {
    flex: 1,
    width: width,
    height: height,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },

  containerInput: {
    height: 45,
  },
  doubleRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footer: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    paddingVertical: vScale(10),
    borderTopColor: colors.GRAY2,
    paddingHorizontal: scale(spacing.small),
  },
});

export const ModalTimKiemHoSoLapPhuongAn = memo(ModalTimKiemHoSoLapPhuongAnComp, isEqual);
