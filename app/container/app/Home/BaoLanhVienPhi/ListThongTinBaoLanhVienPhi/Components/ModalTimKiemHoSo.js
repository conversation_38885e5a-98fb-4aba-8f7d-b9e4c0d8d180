import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon, ModalSelectSimple} from '@component';
import moment from 'moment';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment().format('YYYYMMDD');

const ModalTimKiemHoSoComponent = forwardRef((props, ref) => {
  const {setValue, onBackPress, userInfo} = props;
  const [isVisible, setIsVisible] = useState(false);
  let refModalChonBaoLanhVien = useRef(null);
  let refModalTrangThaiHsGoc = useRef(null);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const dataBLV = [
    {label: 'Hồ sơ cá nhân', value: userInfo?.nguoi_dung?.nsd},
    {label: 'Tất cả', value: ''},
  ];
  const dataTrangThaiHSGoc = [
    {label: 'Chưa đầy đủ', value: 'C'},
    {label: 'Đã đầy đủ', value: 'D'},
  ];

  const initFormInput = {
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    blv: dataBLV[0].value,
    so_hd: '',
    so_hs: '',
    ten: '',
    ngay_sinh: '',
    trang_thai_hs_goc: '',
  };
  const titleInput = ['Ngày đầu *', 'Ngày cuối *', 'Số hồ sơ', 'Số hợp đồng', 'Tên người được bảo hiểm', 'Bảo lãnh viên', 'Ngày sinh', 'Trạng thái hồ sơ gốc', 'Điện thoại'];
  const [formInput, setFormInput] = useState(initFormInput);

  // modal value
  const [ngayDau, setNgayDau] = useState(moment(startOfMonth).toDate());
  const [ngayCuoi, setNgayCuoi] = useState(moment(currentDate).toDate());
  // toggle modal
  const [toggleNgayDau, setToggleNgayDau] = useState(false);
  const [toggleNgayCuoi, setToggleNgayCuoi] = useState(false);

  const onChangeText = async (field, value) => {
    setFormInput((prev) => ({...prev, [field]: value}));
  };

  useEffect(() => {
    if (ngayDau) {
      onChangeText('ngay_d', Number(moment(ngayDau).format('YYYYMMDD')));
    }
    if (ngayCuoi) {
      onChangeText('ngay_c', Number(moment(ngayCuoi).format('YYYYMMDD')));
    }
  }, [ngayDau, ngayCuoi]);

  const onSearchPress = () => {
    setValue && setValue(formInput);
    onBackPress && onBackPress();
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetField = () => {
    setFormInput(initFormInput);
    setNgayDau(moment(startOfMonth).toDate());
    setNgayCuoi(moment(currentDate).toDate());
    onChangeText('blv', dataBLV[0].value);
    onChangeText('trang_thai_hs_goc', '');
  };

  const getTenHienThi = (val, data) => {
    let name = '';
    data.map((e) => {
      if (val === e.value) name = e.label;
    });
    return name;
  };

  /**RENDER  */
  const renderDateTimeComp = (toggle, setToggle, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      date={date}
      mode={mode}
      locale={'vi_VN'}
      display="spinner"
      isVisible={toggle}
      confirmTextIOS="Chọn"
      maximumDate={maxDate}
      minimumDate={minDate}
      cancelTextIOS="Để sau"
      onCancel={() => setToggle(false)}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggle, setDateTime)}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.formInput}>
          {/* Ngày đầu/Ngày cuối */}
          <View style={styles.doubleRowView}>
            <View style={{width: (dimensions.width - 30) / 2}}>
              <CommonOutlinedTextFieldWithIcon
                isDateTimeField
                editable={true}
                disabled={false}
                placeholder={titleInput[0]}
                styleContainer={styles.containerInput}
                onPressInput={() => setToggleNgayDau(true)}
                value={ngayDau ? moment(ngayDau).format('DD/MM/YYYY') : moment().startOf('month').format('DD/MM/YYYY')}
              />
              {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, setNgayDau, ngayDau, 'date', null, new Date(), 0)}
            </View>
            <View style={{width: (dimensions.width - 30) / 2}}>
              <CommonOutlinedTextFieldWithIcon
                editable={true}
                disabled={false}
                placeholder={titleInput[1]}
                styleContainer={styles.containerInput}
                value={ngayCuoi ? moment(ngayCuoi).format('DD/MM/YYYY') : moment().format('DD/MM/YYYY')}
                isDateTimeField
                onPressInput={() => setToggleNgayCuoi(true)}
              />
              {renderDateTimeComp(toggleNgayCuoi, setToggleNgayCuoi, setNgayCuoi, ngayCuoi, 'date', null, new Date(), 0)}
            </View>
          </View>

          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.so_hs}
            placeholder={titleInput[2]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_hs', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            keyboardType="default"
            value={formInput.so_hd}
            placeholder={titleInput[3]}
            onChangeText={(text) => onChangeText('so_hd', text)}
            disabled={false}
            editable={true}
            styleContainer={styles.containerInput}
          />
          <CommonOutlinedTextFieldWithIcon
            value={formInput.ten}
            keyboardType="default"
            placeholder={titleInput[4]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('ten', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            disabled={false}
            editable={true}
            isPickerModal={true}
            placeholder={titleInput[5]}
            value={getTenHienThi(formInput.blv, dataBLV)}
            styleContainer={styles.containerInput}
            onPressInput={() => refModalChonBaoLanhVien.current.show()}
          />

          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            isPickerModal={true}
            keyboardType="default"
            placeholder={titleInput[7]}
            styleContainer={styles.containerInput}
            value={getTenHienThi(formInput.trang_thai_hs_goc, dataTrangThaiHSGoc)}
            onPressInput={() => refModalTrangThaiHsGoc.current.show()}
            onChangeText={(text) => onChangeText('trang_thai_hs_goc', text)}
          />
        </View>
      </View>
    );
  };

  /* RENDER */
  return (
    <Modal onBackButtonPress={onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal title="Tìm kiếm hồ sơ" onBackPress={onBackPress} />
        <KeyboardAwareScrollView>{renderFormInput()}</KeyboardAwareScrollView>
        <View style={styles.footer}>
          <ButtonLinear textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} onPress={resetField} title="Nhập lại" />
          <ButtonLinear onPress={onSearchPress} linearStyle={{marginLeft: spacing.small}} title="Tìm kiếm" />
        </View>
      </SafeAreaView>
      {/* Modal chọn blv */}
      <ModalSelectSimple
        title={'Chọn bảo lãnh viên'}
        ref={refModalChonBaoLanhVien}
        baseData={dataBLV}
        value={formInput.blv}
        setValue={(val) => onChangeText('blv', val.value)}
        onBackPress={() => refModalChonBaoLanhVien.current.hide()}
      />
      {/* Modal chọn trạng thái hs gốc */}
      <ModalSelectSimple
        value={formInput.trang_thai_hs_goc}
        title={'Chọn trạng thái hồ sơ'}
        ref={refModalTrangThaiHsGoc}
        baseData={dataTrangThaiHSGoc}
        setValue={(val) => onChangeText('trang_thai_hs_goc', val.value)}
        onBackPress={() => refModalTrangThaiHsGoc.current.hide()}
      />
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  modalView: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  formInput: {
    marginHorizontal: 10,
  },
  containerInput: {
    height: 45,
  },
  doubleRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footer: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    paddingVertical: vScale(10),
    borderTopColor: colors.GRAY2,
    paddingHorizontal: scale(spacing.small),
  },
});

export const ModalTimKiemHoSo = memo(ModalTimKiemHoSoComponent, isEqual);
