import {MA_TRANG_THAI, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@app/components';
import ListProfilesHeader from '@app/components/ListProfilesHeader';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {vScale} from '@app/theme';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {ModalTimKiemHoSo} from './Components';
import styles from './DanhSachHoSoBaoLanhStyles';

let timer;

const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment().format('YYYYMMDD');

const ListThongTinBaoLanhVienPhiScreenComponent = ({route, navigation}) => {
  console.log('ListThongTinBaoLanhVienPhiScreenComponent');
  const userInfo = useSelector(selectUser);
  const [dialogLoading, setDialogLoading] = useState(false);

  const [objParams, setObjParams] = useState({
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    blv: userInfo?.nguoi_dung?.nsd,
    so_hd: '',
    so_hs: '',
    ten: '',
    ngay_sinh: null,
    trang_thai_hs_goc: '',
  });

  const [data, setData] = useState([]);
  const [total, setTotal] = useState(20);
  const [current, setCurrent] = useState(1);

  let refModalTimKiemHoSo = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
    });
  }, []);

  useEffect(() => {
    timer = setTimeout(() => {
      getData();
    }, 500);
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [objParams]);

  const getData = async (oldData = [], trang = 1, so_dong = 20) => {
    setDialogLoading(true);
    let subObj = {
      trang: trang,
      so_dong: so_dong,
    };
    let params = {...subObj, ...objParams};
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_HS_BAO_LANH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        if (response?.state_info?.message_body === 'Tài khoản chưa được cấp quyền, vui lòng liên hệ với quản trị viên (BLVP)') {
          Alert.alert('Thông báo', response.state_info.message_body, [{text: 'Quay lại', style: 'destructive', onPress: () => NavigationUtil.pop()}]);
        }
        return;
      }
      setTotal(response.data_info.tong_so_dong);
      let arrData = response.data_info?.data;
      let mergeData = [...oldData, ...arrData];
      setData(mergeData);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  const onRefresh = () => {
    setDialogLoading(true);
    setCurrent(1);
    getData([], 1, 20);
  };

  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setCurrent(current + 1);
      getData(data, current + 1);
    }
  };

  const renderProfileItem = ({item, index}) => {
    return (
      <TouchableOpacity onPress={() => NavigationUtil.navigate(SCREEN_ROUTER_APP.BLVP_THEM_QUYEN_LOI_BAO_LANH, {id: item.so_id, ma_doi_tac: item.ma_doi_tac})}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            <Text style={styles.profileTxtThoiGian}>
              Số hồ sơ: <Text style={styles.txtSoHS}>{item.so_hs}</Text>
            </Text>
            <Text style={styles.profileTxtThoiGian}>
              Người được BH: <Text style={styles.detail} children={item.ten} />
            </Text>
            <Text style={styles.profileTxtThoiGian}>
              Ngày thông báo: <Text style={styles.detail}>{item.ngay}</Text>
            </Text>
            <Text style={styles.profileTxtThoiGian}>
              Trạng thái hồ sơ gốc: <Text style={[styles.profileTxtThoiGian, {color: item.trang_thai_hs_goc === 'D' ? colors.GREEN : 'orange', flex: 1}]}>{item.ten_trang_thai_hs_goc_mobile}</Text>
            </Text>
            <Text style={styles.profileTxtThoiGian}>
              Trạng thái:
              <Text
                style={
                  ([styles.profileTxtThoiGian],
                  {
                    color: item.ma_trang_thai === MA_TRANG_THAI.XNHAN ? colors.GREEN : item.ma_trang_thai === MA_TRANG_THAI.BSHS ? 'orange' : colors.BLACK_03,
                    textDecorationLine: item.ma_trang_thai === MA_TRANG_THAI.HS_HUY ? 'line-through' : 'none',
                  })
                }>
                {' ' + item.trang_thai_mobile}
              </Text>
            </Text>

            <View flexDirection="row" alignItems="center" justifyContent="space-between">
              <View flex={1} flexDirection="row">
                <Text style={styles.profileTxtThoiGian}>Tiền yêu cầu: </Text>
                <NumericFormat
                  value={item.so_tien_yc}
                  displayType={'text'}
                  thousandSeparator={true}
                  renderText={(value) => <Text children={value + ' đ'} style={[styles.detail, {marginVertical: vScale(3)}]} />}
                />
              </View>
              <View flex={1} flexDirection="row">
                <Text style={styles.profileTxtThoiGian}>Tiền duyệt: </Text>
                <NumericFormat
                  value={item.so_tien_duyet}
                  displayType={'text'}
                  thousandSeparator={true}
                  renderText={(value) => <Text children={value + ' đ'} style={[styles.detail, {marginVertical: vScale(3)}]} />}
                />
              </View>
            </View>
          </View>
          <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={styles.iconRight} color={colors.BLUE1} />
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Danh sách hồ sơ bảo lãnh"
      renderView={
        <SafeAreaView style={styles.container}>
          <ListProfilesHeader ngayDau={objParams.ngay_d} ngayCuoi={objParams.ngay_c} onPressSearch={() => refModalTimKiemHoSo.current.show()} />
          <FlatList
            data={data}
            renderItem={renderProfileItem}
            keyExtractor={(_, index) => index.toString()}
            onEndReachedThreshold={0.5}
            onEndReached={handleLoadMore}
            refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={onRefresh} />}
            ListEmptyComponent={<Empty />}
          />
          <ModalTimKiemHoSo ref={refModalTimKiemHoSo} userInfo={userInfo} initFormInput={objParams} setValue={setObjParams} onBackPress={() => refModalTimKiemHoSo.current.hide()} />
        </SafeAreaView>
      }
      footer={<ButtonLinear title="Thêm mới hồ sơ bảo lãnh" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.BAO_LANH_VIEN_PHI, {page: 0, info: {}})} />}
    />
  );
};

export const ListThongTinBaoLanhVienPhiScreen = memo(ListThongTinBaoLanhVienPhiScreenComponent, isEqual);
