import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {CheckboxComp, Empty, Icon, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Keyboard, Platform, RefreshControl, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalBenhVienComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, selectedItemIndex, setSelectedItemIndex} = props;

  const [data, setData] = useState([]);
  const [searchInput, setSearchInput] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const getData = async (tim = '', trang = 1, so_dong = 100) => {
    let params = {
      tim: tim,
      trang: trang,
      so_dong: so_dong,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DANH_SACH_BENH_VIEN, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setData(response.data_info);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const _onBackPress = () => {
    onBackPress && onBackPress();
  };

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setSelectedItemIndex(index);
  };

  const onRefresh = () => {
    setRefreshing(true);
    getData(searchInput, 1);
  };

  useEffect(() => {
    getData(searchInput, 1);
  }, [searchInput]);

  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 500);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <TouchableOpacity style={styles.backBtn} onPress={() => _onBackPress()}>
          <Icon.AntDesign name="arrowleft" size={22} style={styles.iconBack} />
        </TouchableOpacity>
        <TextInput
          defaultValue={searchInput}
          returnKeyType="search"
          onFocus={() => Keyboard.o}
          style={styles.searchInput}
          placeholder="Tìm kiếm bệnh viện..."
          placeholderTextColor={colors.GRAY3}
          onSubmitEditing={() => getData(searchInput, 1)}
          onChangeText={debounced}
        />
        <TouchableOpacity style={styles.backBtn} onPress={() => getData(searchInput, 1)}>
          <Icon.AntDesign name="search1" size={20} color={colors.GRAY3} style={styles.searchIcon} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    const isCheck = selectedItemIndex === index;
    const isUnShow = item.bl_noitru?.toUpperCase() === 'K' && item.bl_ngoaitru?.toUpperCase() === 'K' && item.bl_rang?.toUpperCase() === 'K';
    if (isUnShow) return;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} disabled={true} />
        <View style={{marginRight: spacing.default, flex: 1}}>
          <View style={styles.txtView}>
            <Text>{item.ten}</Text>
            {/* <Text style={{color: colors.RED3, fontStyle: 'italic'}}>
              {'('}
              {item.bl_noitru?.toUpperCase() === 'C' && 'BL nội trú/'} {item.bl_ngoaitru?.toUpperCase() === 'C' && 'BL ngoại trú/'} {item.bl_rang?.toUpperCase() === 'C' && 'BL răng'}
              {')'}
            </Text> */}
          </View>
          {item.mst && (
            <Text font="regular12" color={colors.GRAY6}>
              {item.mst}
            </Text>
          )}
          {item.dia_chi && (
            <Text font="regular12" style={{textTransform: 'capitalize'}} numberOfLines={1} color={colors.GRAY6}>
              {item.dia_chi}
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        style={styles.fList}
        renderItem={renderItem}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={isIOS ? false : true}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={<Empty description="Không có dữ liệu" imageStyle={styles.noDataImage} />}
      />
    );
  };
  return (
    <Modal onBackButtonPress={_onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal} onModalShow={() => getData(searchInput, 1)}>
      <>
        {renderHeader()}
        {renderContent()}
      </>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
    paddingTop: isIOS ? 50 : 10,
  },
  content: {
    flex: 1,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 10,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    marginRight: 10,
    marginBottom: 10,
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    marginHorizontal: 15,
  },
  itemHangMucView: {
    marginBottom: spacing.default,
    flexDirection: 'row',
  },
  checkbox: {
    marginRight: spacing.smaller,
    width: 16,
    height: 16,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
  },
  searchIcon: {
    marginLeft: 10,
    marginRight: 5,
  },
  fList: {
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  noDataImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});

export const ModalBenhVien = memo(ModalBenhVienComponent, isEqual);
