import { colors } from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import { selectDanhMucSanPhamConNguoi } from '@app/redux/slices/CategoryCommonSlice';
import axiosConfig from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { spacing } from '@app/theme';
import { logErrorTryCatch } from '@app/utils';
import { ButtonLinear, CommonOutlinedTextFieldWithIcon, DateTimePickerComponent, DialogLoading, Icon, Text } from '@component';
import moment from 'moment';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Controller, useForm } from 'react-hook-form';
import { Alert, SafeAreaView, StyleSheet, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import { useSelector } from 'react-redux';
import { ModalChonDoiTac } from './ModalChonDoiTac';
import { ModalChonSanPhamConNguoi } from './ModalChonSanPhamConNguoi';

let timer;

const titleInput = [
  'Đối tác bảo hiểm', //0
  'Đơn vị khai khác', //1
  'Số GCN bảo hiểm', //2
  'Số hợp đồng', //3
  'Tên người được bảo hiểm', //4
  'Ngày sinh', //5
  'Số CCCD/CMT', //6
  'Điện thoại', //7
  'Nhóm sản phẩm', //8
  'Ngày xảy ra', //9
  'Quyền lợi sản phẩm', //10
];
const ModalTimKiemHopDongComponent = forwardRef((props, ref) => {
  const { onBackPress, setData } = props;

  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const [dialogLoading, setDialogLoading] = useState(false);

  const [toggleDob, setToggleDob] = useState();
  const [toggleNgayXayRa, setToggleNgayXayRa] = useState();

  const [listDoiTac, setListDoiTac] = useState([]);
  let refModalChonDoiTac = useRef(null);

  const listSanPhamConNguoi = useSelector(selectDanhMucSanPhamConNguoi);
  let refModalSanPhamConNguoi = useRef(null);

  const [isValid, setIsValid] = useState(false);

  const {
    control,
    formState: { errors },
    setValue,
    reset,
    watch,
    handleSubmit,
    clearErrors,
    setError,
    getValues,
  } = useForm({
    defaultValues: {
      doiTacBH: null,
      soGCN: '',
      soHopDong: __DEV__ ? 'P-24/TCT/CNG/6901/700004' : '',
      tenNguoiDuocBH: '',
      dob: null,
      soCCCD: __DEV__ ? '013536581911' : '',
      dienThoai: '',
      ngayXayRa: null,
      sanPham: null,
    },
    mode: 'onChange',
  });

  useImperativeHandle(
    ref,
    () => ({
      show: () => {
        setIsVisible(true);
        getListDoiTacBH();
      },
      hide: () => setIsVisible(false),
    }),
    [],
  );

  useEffect(() => {
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getData = async (data) => {
    try {
      setLoading(true);
      let params = {
        nd_tim: data.soCCCD,
        ma_doi_tac: data.doiTacBH?.value || '',
        ma_chi_nhanh: '',
        gcn: data.soGCN,
        so_hd: data.soHopDong,
        ten_kh: data.tenNguoiDuocBH,
        lhnv: data.sanPham?.value || '',
        ngay_sinh: data.dob ? moment(data.dob).format('YYYYMMDD') : null,
        d_thoai: data.dienThoai,
        ngay_vv: data.ngayXayRa ? moment(data.ngayXayRa).format('YYYYMMDD') : null,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TIM_KIEM_NGUOI_DUOC_BAO_HIEM, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) {
        return Alert.alert('Thông báo', 'Không có dữ liệu');
      } else {
        setData && setData(response.data_info);
        timer = setTimeout(() => {
          onBackPress && onBackPress();
        }, 200);
      }
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const getListDoiTacBH = async (tim = '', trang = 1, so_dong = 100) => {
    try {
      let params = {
        tim,
        trang,
        so_dong,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DANH_SACH_DOI_TAC, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListDoiTac(response.data_info);
      if (response.data_info.length === 1) {
        setValue('doiTacBH', { label: response.data_info[0].ten, value: response.data_info[0].ma });
        refModalChonDoiTac.current?.setItemActive(0);
      }
    } catch (error) {
      setDialogLoading(false);
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };
  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'soCCCD' && errType === 'minLength') {
      return 'Số CMT/CCCD tối thiểu 9 ký tự';
    } else if (inputName === 'soGCN' && errType === 'minLength') {
      return 'Số GCN tối thiểu 5 ký tự';
    } else if (inputName === 'soHopDong' && errType === 'minLength') {
      return 'Số hợp đồng tối thiểu 5 ký tự';
    } else if (inputName === 'tenNguoiDuocBH' && errType === 'minLength') {
      return 'Tên người được bảo hiểm tối thiểu 7 ký tự';
    } else if (inputName === 'dienThoai' && errType === 'minLength') {
      return 'Điện thoại tối thiểu 10 ký tự';
    }
  };

  const getValidValue = () => {
    let totalCount = 0;
    if (getValues('soHopDong') !== '') totalCount += 1;
    if (getValues('soGCN') !== '') totalCount += 1;
    if (getValues('tenNguoiDuocBH') !== '') totalCount += 1;
    if (getValues('dienThoai') !== '') totalCount += 1;
    if (getValues('soCCCD') !== '') totalCount += 1;
    if (totalCount >= 1) setIsValid(true);
    else setIsValid(false);
  };

  const onChangeText = async (field, value) => {
    if (field === 'soGCN' && value.length > 0 && value.length < 5) setError('soGCN', { type: 'minLength', message: '5 ký tự' });
    else clearErrors('soGCN');
    if (field === 'soHopDong' && value.length > 0 && value.length < 5) setError('soHopDong', { type: 'minLength', message: '5 ký tự' });
    else clearErrors('soHopDong');
    if (field === 'tenNguoiDuocBH' && value.length > 0 && value.length < 7) setError('tenNguoiDuocBH', { type: 'minLength', message: '5 ký tự' });
    else clearErrors('tenNguoiDuocBH');
    if (field === 'soCCCD' && value.length > 0 && value.length < 9) setError('soCCCD', { type: 'minLength', message: '9 ký tự' });
    else clearErrors('soCCCD');
    if (field === 'dienThoai' && value.length > 0 && value.length < 10) setError('dienThoai', { type: 'minLength', message: '7 ký tự' });
    else clearErrors('dienThoai');
    setValue(field, value);
    getValidValue();
  };

  /*RENDER */
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerComponent
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime)}
      onCancel={() => setToggleDateTime(false)}
      date={date}
      maxDate={maxDate}
      minDate={minDate}
    />
  );

  const renderFormInput = () => {
    return (
      <View style={styles.formInput}>
        <Controller
          control={control}
          name="doiTacBH"
          render={({ field: { onChange, value } }) => (
            <CommonOutlinedTextFieldWithIcon
              editable={false}
              disabled={true}
              value={value?.label}
              placeholder={titleInput[0]}
              styleContainer={styles.containerInput}
              onChangeText={onChange}
              options={true}
              onPressInput={() => refModalChonDoiTac.current?.show()}
            />
          )}
        />

        <Controller
          control={control}
          name="soHopDong"
          render={({ field: { onChange, value } }) => (
            <CommonOutlinedTextFieldWithIcon
              value={value}
              placeholder={titleInput[3]}
              onChangeText={(text) => onChangeText('soHopDong', text)}
              styleContainer={styles.containerInput}
              error={errors.soHopDong && getErrMessage('soHopDong', errors.soHopDong.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="soGCN"
          rules={{
            minLength: 5,
          }}
          render={({ field: { onChange, value } }) => (
            <CommonOutlinedTextFieldWithIcon
              value={value}
              placeholder={titleInput[2]}
              styleContainer={styles.containerInput}
              onChangeText={(text) => onChangeText('soGCN', text)}
              error={errors.soGCN && getErrMessage('soGCN', errors.soGCN.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="ngayXayRa"
          rules={{
            required: true,
          }}
          render={({ field: { onChange, value } }) => (
            <>
              <CommonOutlinedTextFieldWithIcon
                isRequired
                editable={true}
                disabled={false}
                placeholder={titleInput[9]}
                styleContainer={styles.containerInput}
                value={value ? moment(value).format('DD/MM/YYYY') : ''}
                onPressInput={() => setToggleNgayXayRa(true)}
                isDateTimeField
                error={errors.ngayXayRa && getErrMessage('ngayXayRa', errors.ngayXayRa.type)}
              />
              {renderDateTimeComp(toggleNgayXayRa, setToggleNgayXayRa, onChange, moment(value).isValid() ? value : new Date(), 'date', null, new Date())}
            </>
          )}
        />

        <Controller
          control={control}
          rules={{
            minLength: 7,
          }}
          name="tenNguoiDuocBH"
          render={({ field: { onChange, value } }) => (
            <CommonOutlinedTextFieldWithIcon
              value={value}
              placeholder={titleInput[4]}
              styleContainer={styles.containerInput}
              onChangeText={(text) => onChangeText('tenNguoiDuocBH', text)}
              error={errors.tenNguoiDuocBH && getErrMessage('tenNguoiDuocBH', errors.tenNguoiDuocBH.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="dob"
          render={({ field: { onChange, value } }) => (
            <>
              <CommonOutlinedTextFieldWithIcon
                editable={true}
                disabled={false}
                placeholder={titleInput[5]}
                styleContainer={styles.containerInput}
                value={value ? moment(value).format('DD/MM/YYYY') : ''}
                onPressInput={() => setToggleDob(true)}
                isDateTimeField
              />
              {renderDateTimeComp(toggleDob, setToggleDob, onChange, moment(value).isValid() ? value : new Date(), 'date', null, new Date())}
            </>
          )}
        />
        <Controller
          control={control}
          name="soCCCD"
          rules={{
            // required: true,
            minLength: 9,
          }}
          render={({ field: { onChange, value } }) => (
            <CommonOutlinedTextFieldWithIcon
              // isRequired
              keyboardType="numeric"
              value={value}
              placeholder={titleInput[6]}
              styleContainer={styles.containerInput}
              onChangeText={(text) => onChangeText('soCCCD', text)}
              error={errors.soCCCD && getErrMessage('soCCCD', errors.soCCCD.type)}
            />
          )}
        />
        <Controller
          control={control}
          name="dienThoai"
          rules={{
            // required: true,
            minLength: 10,
          }}
          render={({ field: { onChange, value } }) => (
            <CommonOutlinedTextFieldWithIcon
              value={value}
              placeholder={titleInput[7]}
              keyboardType="numeric"
              styleContainer={styles.containerInput}
              onChangeText={(text) => onChangeText('dienThoai', text)}
              error={errors.dienThoai && getErrMessage('dienThoai', errors.dienThoai.type)}
            />
          )}
        />
        <Controller
          control={control}
          name="sanPham"
          render={({ field: { onChange, value } }) => (
            <CommonOutlinedTextFieldWithIcon
              editable={false}
              disabled={true}
              value={value?.label}
              placeholder={titleInput[10]}
              styleContainer={styles.containerInput}
              onChangeText={onChange}
              options={true}
              onPressInput={() => refModalSanPhamConNguoi.current?.show()}
            />
          )}
        />

        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Icon.Ionicons name={isValid ? 'checkmark-circle' : 'warning'} color={isValid ? colors.GREEN : colors.ORANGE} size={20} />
          <Text flex={1} children="Nhập ít nhất 1 trường trong các trường thông tin (Số hợp đồng, Số GCN bảo hiểm, Tên NĐBH, Điện thoại, CCCD)" color={isValid ? colors.GREEN : colors.ORANGE} />
        </View>
      </View>
    );
  };

  /* RENDER */
  const renderContent = () => {
    return (
      <SafeAreaView style={styles.container}>
        <View style={{ flex: 1 }}>{renderFormInput()}</View>
      </SafeAreaView>
    );
  };
  return (
    <Modal animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} onBackButtonPress={onBackPress} onBackdropPress={onBackPress} style={styles.modal}>
      <SafeAreaView flex={1}>
        <HeaderModal title="Tìm kiếm hợp đồng bảo hiểm" onBackPress={onBackPress} />
        <KeyboardAwareScrollView>
          <View>{renderContent()}</View>
          <View flexDirection="row">
            <ButtonLinear
              title="Nhập lại"
              loading={false}
              disabled={loading}
              onPress={() => reset()}
              linearStyle={styles.searchButton}
              textStyle={{ color: colors.BLACK_03 }}
              linearColors={[colors.GRAY2, colors.GRAY2]}
            />
            <ButtonLinear
              linearColors={!isValid ? [colors.GRAY, colors.GRAY] : [colors.BUTTON.LIGHT.PRIMARY, colors.BUTTON.LIGHT.SECONDARY]}
              linearStyle={styles.searchButton}
              title="Tìm kiếm"
              loading={loading}
              disabled={!isValid || loading}
              onPress={handleSubmit(getData)}
            />
          </View>
        </KeyboardAwareScrollView>
      </SafeAreaView>
      <ModalChonDoiTac
        ref={refModalChonDoiTac}
        setValue={(itemSelected) => setValue('doiTacBH', { label: itemSelected.ten, value: itemSelected.ma })}
        onBackPress={() => refModalChonDoiTac.current?.hide()}
        data={listDoiTac}
      />
      <ModalChonSanPhamConNguoi
        ref={refModalSanPhamConNguoi}
        setValue={(itemSelected) => setValue('sanPham', { label: itemSelected.label, value: itemSelected.value })}
        onBackPress={() => refModalSanPhamConNguoi.current?.hide()}
        data={listSanPhamConNguoi}
      />
      {dialogLoading && <DialogLoading />}
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: spacing.default,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  container: {
    flex: 1,
  },
  formInput: {
    marginHorizontal: spacing.default,
  },
  searchButton: {
    marginVertical: 10,
    marginHorizontal: 16,
  },
  containerInput: { marginBottom: 0 },
});

export const ModalTimKiemHopDong = memo(ModalTimKiemHopDongComponent, isEqual);
