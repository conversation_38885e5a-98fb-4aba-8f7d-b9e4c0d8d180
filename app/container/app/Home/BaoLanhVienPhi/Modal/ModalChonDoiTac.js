import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {dimensions} from '@app/theme';
import {CheckboxComp, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonDoiTacComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, data} = props;

  const [refreshing, setRefreshing] = useState(false);
  const [selectedItemIndex, setSelectedItemIndex] = useState(-1);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
      setItemActive: (index) => {
        setSelectedItemIndex(index);
      },
    }),
    [],
  );

  const _onBackPress = () => {
    onBackPress && onBackPress();
  };

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setSelectedItemIndex(index);
    setIsVisible(false);
  };

  const onRefresh = () => {
    setRefreshing(true);
  };

  /* RENDER */
  const renderItem = ({item, index}) => {
    const isCheck = selectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
        <View style={styles.txtView}>
          <Text style={{flex: 1}}>{item.ten}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.1}
        style={{marginBottom: 20, paddingHorizontal: 10}}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={isIOS ? false : true}
        // refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      />
    );
  };
  return (
    <Modal onBackButtonPress={_onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal}>
      <>
        <HeaderModal title="Chọn đối tác" onBackPress={_onBackPress} />
        {renderContent()}
      </>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
    paddingTop: Platform.OS == 'ios' ? 50 : 10,
    // paddingHorizontal: 10,
  },
  modalCameraView: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
    justifyContent: 'center',
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingRight: 10,
    paddingBottom: 10,
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
  },
  checkbox: {
    marginRight: 8,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Platform.OS === 'android' ? 4 : 0,
  },
});

export const ModalChonDoiTac = memo(ModalChonDoiTacComponent, isEqual);
