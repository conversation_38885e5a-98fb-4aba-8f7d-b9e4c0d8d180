import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {CheckboxComp, Empty, Icon, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Keyboard, Platform, RefreshControl, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalNhaThuocComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, selectedItemIndex, setSelectedItemIndex} = props;

  const [data, setData] = useState();
  const [searchInput, setSearchInput] = useState('');

  const [refreshing, setRefreshing] = useState();

  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const _onBackPress = () => {
    onBackPress && onBackPress();
  };

  const getData = async (tim = '', trang = 1, so_dong = 100) => {
    let params = {
      tim: tim,
      trang: trang,
      so_dong: so_dong,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DANH_SACH_NHA_THUOC, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setData(response.data_info);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setSelectedItemIndex(index);
  };

  const onRefresh = () => {
    setRefreshing(true);
    getData(searchInput, 1);
  };

  useEffect(() => {
    getData(searchInput, 1);
  }, [searchInput]);

  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 500);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <TouchableOpacity style={styles.backBtn} onPress={() => _onBackPress()}>
          <Icon.AntDesign name="arrowleft" size={22} style={styles.iconBack} />
        </TouchableOpacity>
        <TextInput
          defaultValue={searchInput}
          style={styles.searchInput}
          returnKeyType="search"
          onFocus={() => Keyboard.o}
          placeholder="Tìm kiếm nhà thuốc..."
          placeholderTextColor={colors.GRAY3}
          onSubmitEditing={() => getData(searchInput)}
          onChangeText={debounced}
        />
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    const isCheck = selectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
        <View style={styles.txtView}>
          <Text style={{flex: 1}}>{item.ten}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.1}
        style={{marginBottom: 20, paddingHorizontal: 10}}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={Platform.OS === 'ios' ? false : true}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={<Empty description="Không có dữ liệu" imageStyle={styles.noDataImage} />}
      />
    );
  };
  return (
    <Modal onBackButtonPress={_onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal} onModalShow={() => getData(searchInput)}>
      <>
        {renderHeader()}
        {renderContent()}
      </>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
    paddingTop: isIOS ? 50 : 10,
    // paddingHorizontal: 10,
  },
  modalCameraView: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
    justifyContent: 'center',
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingRight: 10,
    paddingBottom: 10,
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
  },
  checkbox: {
    marginRight: 8,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: !isIOS ? 4 : 0,
  },
  noDataImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});

export const ModalNhaThuoc = memo(ModalNhaThuocComponent, isEqual);
