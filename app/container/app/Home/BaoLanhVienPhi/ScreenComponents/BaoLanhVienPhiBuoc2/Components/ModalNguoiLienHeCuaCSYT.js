import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {FlatList, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalNguoiLienHeCuaCSYTComp = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  const {onBackPress, data, setValue, removeValue} = props;

  const [isVisible, setIsVisible] = useState(false);
  const [indexSelected, setIndexSelected] = useState(-1);

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setIndexSelected(index);
  };

  const onCancel = () => {
    onBackPress && onBackPress();
    removeValue && removeValue();
    setIndexSelected(-1);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <TouchableOpacity style={{marginLeft: 15}} onPress={onCancel}>
          <Text style={styles.txtCancel}>Xoá</Text>
        </TouchableOpacity>
        <Text style={styles.modalTitle} children="Chọn người thông báo" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderItem = ({item, index}) => {
    const isChecked = indexSelected === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <Text style={[styles.txtView, {color: isChecked ? colors.PRIMARY : colors.BLACK_03}]}>{item.ten}</Text>
        {isChecked && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return <FlatList data={data} extraData={data} renderItem={renderItem} onEndReachedThreshold={0.3} keyExtractor={(item, index) => item + index.toString()} style={styles.flStyles} />;
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} onBackButtonPress={onBackPress} onBackdropPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    justifyContent: 'space-between',
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  checkbox: {
    marginRight: 5,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: !isIOS ? 4 : 2,
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  flStyles: {
    marginTop: 10,
    paddingHorizontal: 10,
  },
  txtCancel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.RED1,
  },
});

export const ModalNguoiLienHeCuaCSYT = ModalNguoiLienHeCuaCSYTComp;
