import {colors} from '@app/commons/Theme';
import {StyleSheet, Dimensions} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  stepIndicator: {
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  headerView: {
    marginVertical: 10,
    marginHorizontal: 10,
    justifyContent: 'space-between',
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  buttonChonBenhVien: {
    minHeight: 40,
    borderWidth: 1,
    paddingLeft: 15,
    borderRadius: 10,
    paddingRight: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: colors.GRAY,
    textAlignVertical: 'center',
    backgroundColor: colors.WHITE,
    justifyContent: 'space-between',
  },
  icon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  headerTitleView: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    backgroundColor: colors.WHITE1,
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.BLUE3,
  },
  formInput: {
    marginHorizontal: 10,
  },
  btnLoginView: {
    flex: 1,
    borderRadius: 10,
    marginHorizontal: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: colors.PRIMARY,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
  },
  nameCoSoYTe: {
    flex: 1,
  },
  btnSaveContact: {
    width: 100,
    padding: 4,
    borderWidth: 1,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.BLUE1,
    justifyContent: 'space-around',
  },
  searchButton: {
    marginVertical: 10,
    marginHorizontal: 16,
  },
});
