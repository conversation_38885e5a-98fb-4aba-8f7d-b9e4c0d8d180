import {REGUlAR_EXPRESSION} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon, Icon, Text} from '@component';
import React, {memo, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, TouchableOpacity, View} from 'react-native';
import {ModalBenhVien, ModalNhaThuoc} from '../../Modal';
import styles from './BaoLanhVienPhiBuoc2Styles';
import {ModalNguoiLienHeCuaCSYT} from './Components';

const BaoLanhVienPhiBuoc2ScreenComponent = (props) => {
  const {
    maBV,
    setMaBV,
    nameBenh<PERSON><PERSON>,
    nameNha<PERSON>hu<PERSON>,
    setMa<PERSON>haThuoc,
    setNameBenhVien,
    setNameNhaThuoc,
    tenNguoiThongBao,
    emailNguoiThongBao,
    setTenNguoiThongBao,
    setEmailNguoiThongBao,
    soDienThoaiNguoiThongBao,
    setSoDienThoaiNguoiThongBao,
  } = props;
  const [loading, setLoading] = useState(false);
  const [loadingSave, setLoadingSave] = useState(false);
  const [selectedItemIndexBV, setSelectedItemIndexBV] = useState(-1);
  const [selectedItemIndexNT, setSelectedItemIndexNT] = useState(-1);

  let refModal = useRef();
  let refModalBenhVien = useRef();
  let refModalNhaThuoc = useRef();

  const titleInput = ['Cơ sở y tế', 'Nhà thuốc', 'Họ tên', 'Điện thoại', 'Email'];
  const [inputErrLienHe, setInputErrLienHe] = useState(['', '', '']);
  const [contactData, setContactData] = useState([]);
  const [bt, setBt] = useState('');

  const onChangeText = async (field, value) => {
    let inputErrLienHeTmp = inputErrLienHe;
    if (field === 'name') {
      inputErrLienHeTmp[0] = '';
      if (value === '') {
        inputErrLienHeTmp[0] = 'Không được để trống';
      } else inputErrLienHeTmp[0] = '';
      setInputErrLienHe([...inputErrLienHeTmp]);
      setTenNguoiThongBao && setTenNguoiThongBao(value);
    }
    if (field === 'email') {
      inputErrLienHeTmp[2] = '';
      if (!REGUlAR_EXPRESSION.REG_EMAIL.test(value)) {
        inputErrLienHeTmp[2] = 'Email không đúng định dạng';
      }
      if (value == '') {
        inputErrLienHeTmp[2] = '';
      }
      setInputErrLienHe([...inputErrLienHeTmp]);
      setEmailNguoiThongBao && setEmailNguoiThongBao(value);
    }
    if (field === 'phone') {
      inputErrLienHeTmp[1] = '';
      if (!REGUlAR_EXPRESSION.REG_PHONE.test(value)) {
        inputErrLienHeTmp[1] = 'Số điện thoại sai định dạng';
      }
      if (value == '') {
        inputErrLienHeTmp[1] = '';
      }
      setInputErrLienHe([...inputErrLienHeTmp]);
      setSoDienThoaiNguoiThongBao && setSoDienThoaiNguoiThongBao(value);
    }
  };

  const resetNameBenhVien = () => {
    setMaBV && setMaBV('');
    setSelectedItemIndexBV(-1);
    setNameBenhVien && setNameBenhVien('');
  };
  const resetNhaThuoc = () => {
    setMaNhaThuoc && setMaNhaThuoc('');
    setSelectedItemIndexNT(-1);
    setNameNhaThuoc && setNameNhaThuoc('');
  };

  const onPressBenhVien = (item) => {
    refModalBenhVien.current.hide();
    setMaBV && setMaBV(item.ma);
    setNameBenhVien && setNameBenhVien(item.ten);
  };
  const onPressNhaThuoc = (item) => {
    refModalNhaThuoc.current.hide();
    setMaNhaThuoc && setMaNhaThuoc(item.ma);
    setNameNhaThuoc && setNameNhaThuoc(item.ten);
  };

  const submitLuuLienHe = async () => {
    let inputErrLienHeTmp = inputErrLienHe;
    if (maBV === '') {
      return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn bệnh viện', 'warning');
    }
    if (tenNguoiThongBao === '') {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập họ tên');
      inputErrLienHeTmp[0] = 'Không được để trống';
      setInputErrLienHe([...inputErrLienHeTmp]);
      return;
    }

    setLoadingSave(true);
    let params = {
      bt: bt || '',
      ma_bv: maBV,
      ten: tenNguoiThongBao,
      dien_thoai: soDienThoaiNguoiThongBao,
      email: emailNguoiThongBao,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_LIEN_HE_CSYT, params);
      setLoadingSave(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu thông tin liên hệ thành công', 'success');
    } catch (error) {
      setLoadingSave(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const timKiem = async () => {
    if (maBV === '') {
      return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn bệnh viện');
    }
    setLoading(true);
    let params = {
      ma_bv: maBV,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_NGUOI_LH_CUA_CSYT, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length > 0) {
        let data = response.data_info;
        setContactData(data);
        refModal.current.show();
      } else return FlashMessageHelper.showFlashMessage('Thông báo', 'Không có thông tin');
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onSetValue = (value) => {
    setBt(value.bt);
    setTenNguoiThongBao(value.ten);
    setEmailNguoiThongBao(value.email);
    setSoDienThoaiNguoiThongBao(value.dien_thoai);
  };

  const removeValue = (value) => {
    setBt('');
    setTenNguoiThongBao('');
    setEmailNguoiThongBao('');
    setSoDienThoaiNguoiThongBao('');
  };

  /**RENDER  */

  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin liên hệ với cơ sở y tế</Text>
        </View>
        <View style={styles.formInput}>
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={tenNguoiThongBao}
            error={inputErrLienHe[0]}
            placeholder={titleInput[2]}
            onChangeText={(text) => onChangeText('name', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            keyboardType="numeric"
            error={inputErrLienHe[1]}
            placeholder={titleInput[3]}
            value={soDienThoaiNguoiThongBao}
            onChangeText={(text) => onChangeText('phone', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            value={emailNguoiThongBao}
            error={inputErrLienHe[2]}
            placeholder={titleInput[4]}
            keyboardType="email-address"
            onChangeText={(text) => onChangeText('email', text)}
          />
          <View flexDirection="row">
            <ButtonLinear
              title="Tìm kiếm"
              loading={loading}
              disabled={loading}
              onPress={timKiem}
              linearStyle={styles.searchButton}
              textStyle={{color: colors.BLACK_03}}
              linearColors={[colors.GRAY2, colors.GRAY2]}
            />
            <ButtonLinear linearStyle={styles.searchButton} title="Lưu liên hệ" loading={loadingSave} disabled={loadingSave} onPress={submitLuuLienHe} />
          </View>
        </View>
      </View>
    );
  };

  const iconSize = 22;
  /**RENDER  */
  return (
    <View style={{flex: 1}}>
      <View style={styles.headerView}>
        {titleInput && (
          <Text style={styles.dropDownTitle}>
            {titleInput[0]} {<Text children="(*)" style={{color: colors.RED1}} />}
          </Text>
        )}
        <TouchableOpacity style={styles.buttonChonBenhVien} onPress={() => refModalBenhVien.current.show()}>
          <Text style={styles.nameCoSoYTe}>{nameBenhVien ? nameBenhVien : 'Chọn bệnh viện'}</Text>
          {!nameBenhVien ? (
            <Icon.MaterialIcons name="keyboard-arrow-down" size={iconSize} style={styles.icon} color={colors.BLUE1} />
          ) : (
            <TouchableOpacity onPress={resetNameBenhVien}>
              <Icon.MaterialIcons name="close" size={iconSize} style={styles.icon} color={colors.BLUE1} />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </View>
      <View style={styles.headerView}>
        {titleInput && <Text style={styles.dropDownTitle}>{titleInput[1]}</Text>}
        <TouchableOpacity style={styles.buttonChonBenhVien} onPress={() => refModalNhaThuoc.current.show()}>
          <Text style={styles.nameCoSoYTe}>{nameNhaThuoc ? nameNhaThuoc : 'Chọn nhà thuốc'}</Text>
          {!nameNhaThuoc ? (
            <Icon.MaterialIcons name="keyboard-arrow-down" size={iconSize} style={styles.icon} color={colors.BLUE1} />
          ) : (
            <TouchableOpacity onPress={resetNhaThuoc}>
              <Icon.MaterialIcons name="close" size={iconSize} style={styles.icon} color={colors.BLUE1} />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </View>
      {renderFormInput()}
      <ModalBenhVien
        ref={refModalBenhVien}
        setValue={onPressBenhVien}
        selectedItemIndex={selectedItemIndexBV}
        setSelectedItemIndex={setSelectedItemIndexBV}
        onBackPress={() => refModalBenhVien.current.hide()}
      />
      <ModalNhaThuoc
        ref={refModalNhaThuoc}
        setValue={onPressNhaThuoc}
        selectedItemIndex={selectedItemIndexNT}
        onBackPress={() => refModalNhaThuoc.current.hide()}
        setSelectedItemIndex={setSelectedItemIndexNT}
      />
      <ModalNguoiLienHeCuaCSYT data={contactData} ref={refModal} removeValue={removeValue} setValue={(value) => onSetValue(value)} onBackPress={() => refModal.current.hide()} />
    </View>
  );
};

export const BaoLanhVienPhiBuoc2Screen = memo(BaoLanhVienPhiBuoc2ScreenComponent, isEqual);
