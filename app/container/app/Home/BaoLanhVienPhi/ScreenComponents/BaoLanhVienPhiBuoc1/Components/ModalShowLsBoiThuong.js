import {MA_TRANG_THAI} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Icon, Text, HeaderModal} from '@app/components';
import {FontSize, dimensions, spacing} from '@app/theme';
import React, {forwardRef, useImperativeHandle, useRef, useState} from 'react';
import {FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Modal from 'react-native-modal';
import {NumericFormat} from 'react-number-format';
import {ModalShowNote} from '../../../ThongTinBaoLanh/Components/ModalShowNote';

const ModalShowLsBoiThuongComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  let refModalShowNote = useRef(null);

  const {onBackPress, data, refreshing, tongTienNoiTru, tongTienNgoaiTru} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [note, setNote] = useState('');

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  const onPressShowNote = (item) => {
    setNote(item.ghi_chu);
    refModalShowNote.current.show();
  };

  /* RENDER */

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <View paddingHorizontal={10} flex={1}>
          <View flexDirection="row" marginBottom={8}>
            <Text style={styles.footerLabel} children="Tổng tiền nội trú: " />
            <NumericFormat value={tongTienNoiTru} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} />} />
          </View>
          <View flexDirection="row">
            <Text style={styles.footerLabel} children="Tổng tiền ngoại trú: " />
            <NumericFormat value={tongTienNgoaiTru} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} />} />
          </View>
        </View>
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    const statusColors = item.ma_trang_thai === MA_TRANG_THAI.XNHAN ? colors.GREEN : item.ma_trang_thai === MA_TRANG_THAI.HS_HUY ? colors.RED1 : colors.PRIMARY;

    const renderLabel = (label, value, style) => {
      return (
        <View style={styles.row}>
          <Text style={styles.label}>{label}: </Text>
          <Text style={{...styles.date, ...style}}>{value}</Text>
        </View>
      );
    };
    return (
      <View>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.itemView}>
          <View style={styles.contentView}>
            <Text style={styles.label}>
              Số hồ sơ: <Text style={styles.txtSoHS}>{item.so_hs}</Text>
            </Text>
            <View style={styles.doubleCol}>
              {renderLabel('Ngày mở', item.ngay_ht)}
              <Text style={styles.label}>
                Loại: <Text style={{color: colors.PRIMARY, flex: 1}}>{item.loai_ten_text}</Text>
              </Text>
            </View>
            <View style={styles.doubleCol}>
              {renderLabel('Ngày vv', item.ngay_vv)}
              {renderLabel('Ngày rv', item.ngay_rv)}
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Trạng thái: </Text>
              <Text style={[styles.contentDetail, {color: statusColors}]}>{item.trang_thai}</Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.row}>
              <Text style={styles.label}>Quyền lợi: </Text>
              <Text style={[styles.contentDetail, {color: colors.PRIMARY}]}>{item.quyen_loi_ten}</Text>
            </View>

            {renderLabel('Nguyên nhân', item.ten_nguyen_nhan)}
            {renderLabel('Số ngày duyệt', item.so_ngay_duyet)}
            {renderLabel('Hình thức điều trị', item.hinh_thuc_ten, styles.contentDetail)}
            {renderLabel('Cơ sở y tế', item.ten_benh_vien, styles.contentDetail)}
            {renderLabel('Chẩn đoán', item.chan_doan, styles.contentDetail)}
            <View style={styles.divider} />
            <View style={styles.doubleCol}>
              <View style={styles.row}>
                <Text style={styles.label}>Tiền yc: </Text>
                <NumericFormat value={item.so_tien_yc} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.price} />} />
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Tiền duyệt: </Text>
                <NumericFormat value={item.so_tien_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.price} />} />
              </View>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Nguyên nhân giảm: </Text>
              <Text style={styles.contentDetail}>{item.nguyen_nhan}</Text>
            </View>
            <View flexDirection="row" justifyContent="space-between" alignItems="center">
              <TouchableOpacity style={styles.row} onPress={() => onPressShowNote(item)}>
                <Text style={styles.label}>Ghi chú: </Text>
                <Icon.Feather name="file-text" size={16} color={item.ghi_chu !== null ? colors.PRIMARY : colors.GRAY3} />
              </TouchableOpacity>
              <Text style={styles.currency}>
                Nguyên tệ: <Text style={{color: colors.PRIMARY}}>USD</Text>
              </Text>
            </View>
          </View>
        </LinearGradient>
      </View>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        style={{paddingHorizontal: 10}}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={true}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={() => {}} />}
        ListEmptyComponent={<Text style={styles.txtEmpty}>Chưa có dữ liệu</Text>}
      />
    );
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={onPressBack} title="Lịch sử bồi thường" />
        <ModalShowNote title="Ghi chú" ref={refModalShowNote} detail={note} onBackPress={() => refModalShowNote.current.hide(false)} />
        {renderContent()}
        {data.length > 0 && renderFooter()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  flStyles: {
    paddingTop: 10,
  },
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
    // paddingHorizontal: 10,
  },
  contentView: {
    flex: 1,
    paddingRight: 5,
    paddingVertical: 5,
    borderBottomColor: colors.GRAY4,
  },
  itemView: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
    borderWidth: 0.4,
    marginVertical: 5,
    flexDirection: 'row',
    borderColor: colors.GRAY,
  },
  contentDetail: {
    flex: 1,
    fontSize: 13,
  },
  date: {
    fontSize: 13,
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  profileTxtHoSo: {
    fontSize: 14,
    marginBottom: 4,
    color: colors.BLACK_03,
  },
  label: {
    fontSize: 13,
    fontWeight: '600',
    color: colors.GRAY6,
  },
  row: {
    marginTop: 5,
    flexDirection: 'row',
  },
  txtSoHS: {
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  doubleCol: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  price: {
    color: colors.RED1,
    fontWeight: '400',
  },
  divider: {
    height: 1,
    backgroundColor: colors.GRAY2,
    marginTop: 5,
    marginBottom: 2,
  },
  footerView: {
    flexDirection: 'row',
    width: dimensions.width,
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  footerLabel: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  txtEmpty: {
    textAlign: 'center',
    color: colors.GRAY10,
    marginVertical: spacing.tiny,
  },
  currency: {
    color: colors.RED1,
    fontSize: FontSize.size14,
  },
});

export const ModalShowLsBoiThuong = ModalShowLsBoiThuongComponent;
