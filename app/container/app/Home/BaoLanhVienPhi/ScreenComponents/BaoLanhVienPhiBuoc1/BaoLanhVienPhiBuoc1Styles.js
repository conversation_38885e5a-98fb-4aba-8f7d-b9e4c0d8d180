import {colors} from '@app/commons/Theme';
import {StyleSheet, Dimensions} from 'react-native';
const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  stepIndicator: {
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
  },
  headerView: {
    // flexDirection: 'row',
    justifyContent: 'space-between',
    // alignItems: 'center',
    marginVertical: 10,
    marginHorizontal: 16,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  buttonChonBenhVien: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 10,
    paddingLeft: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
    minHeight: 40,
    paddingRight: 15,
    textAlignVertical: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  icon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  headerTitleView: {
    paddingHorizontal: 12,
    paddingVertical: 9,
    backgroundColor: colors.WHITE1,
    marginBottom: 10,
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.BLUE3,
  },
  formInput: {
    marginHorizontal: 16,
  },
  btnLoginView: {
    borderRadius: 10,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 16,
    justifyContent: 'center',
    backgroundColor: colors.PRIMARY,
  },
  txtBtnLogin: {
    // paddingRight: 5,
    paddingVertical: 10,
    fontSize: 16,
    // textTransform: 'uppercase',
    color: colors.WHITE,
  },

  profileItemView: {
    padding: 8,
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: 5,
    marginHorizontal: 10,
    borderColor: colors.GRAY,
  },
  subLabel: {
    color: colors.GRAY6,
  },
  contentRow: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  content: {
    color: colors.PRIMARY,
  },
  btnSearch: {
    marginVertical: 8,
    marginHorizontal: 16,
  },
  txtTimKiem: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
    textTransform: 'uppercase',
  },
  searchIcon: {
    color: colors.GRAY3,
  },
  imageNoData: {
    width: width / 4,
    height: width / 4,
  },
  noDataView: {
    alignItems: 'center',
  },
  bottomBtn: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    height: 0.5,
    marginVertical: 10,
    backgroundColor: colors.GRAY,
  },
});
