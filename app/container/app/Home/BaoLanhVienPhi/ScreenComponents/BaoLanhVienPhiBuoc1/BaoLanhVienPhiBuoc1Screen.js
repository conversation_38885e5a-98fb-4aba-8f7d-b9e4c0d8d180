import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, Text} from '@app/components';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Image, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {ModalTimKiemHopDong} from '../../Modal';
import styles from './BaoLanhVienPhiBuoc1Styles';
import {ModalShowLsBoiThuong, ModalShowQlBaoHiem} from './Components';

let timer;
function BaoLanhVienPhiBuoc1ScreenComponent(props) {
  const {setChiTietHS, setData, data} = props;
  const [selectedItem, setSelectedItem] = useState(-1);
  const [loading, setLoading] = useState(false);
  const [dataQlBaoHiem, setDataQlBaoHiem] = useState([]);
  const [dataLsBoiThuong, setDataLsBoiThuong] = useState([]);
  const [tongTienNoiTru, setTongTienNoiTru] = useState(0);
  const [tongTienNgoaiTru, setTongTienNgoaiTru] = useState(0);

  let refModalShowQlBaoHiem = useRef(null);
  let refModalShowLsBoiThuong = useRef(null);
  let refModalTimKiem = useRef(null);

  const onPressSearch = () => {
    refModalTimKiem.current.show();
  };

  useEffect(() => {
    if (data.length <= 0) {
      timer = setTimeout(() => {
        refModalTimKiem.current.show();
      }, 500);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [data]);

  const onPressItem = async (i) => {
    setSelectedItem(i);
    props.setSelectedItemHoSo(i);
    const params = {
      so_id_hd: i.so_id_hd,
      so_id_gcn: i.so_id_dt,
      ma_doi_tac_ql: i.ma_doi_tac_ql,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_CHI_TIET_GCN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setChiTietHS && setChiTietHS(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const xemQlBaoHiem = async (i) => {
    setLoading(true);
    refModalShowQlBaoHiem.current?.show();
    timer = setTimeout(async () => {
      const params = {
        so_id_hd: i.so_id_hd,
        ma_doi_tac_ql: i.ma_doi_tac_ql,
        so_id_dt: i.so_id_dt,
      };

      try {
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_TT_QL_GOC, params);
        setLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        setDataQlBaoHiem(response.data_info);
      } catch (error) {
        setLoading(false);
        Alert.alert('Thông báo', error.message);
      }
      return;
    }, 500);
  };

  const xemLsBoiThuong = async (i) => {
    setLoading(true);
    refModalShowLsBoiThuong.current.show();
    timer = setTimeout(async () => {
      const params = {
        so_id_hd: i.so_id_hd,
        so_id_dt: i.so_id_dt,
        ma_doi_tac: i.ma_doi_tac,
        so_id: '',
      };
      try {
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_LS_BOI_THUONG, params);
        setLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        let tienNoiTru = 0;
        let tienNgoaiTru = 0;
        let dataNoitru = response.data_info.ho_so.filter((item) => item.hinh_thuc_ten === 'Nội trú');
        dataNoitru.filter((item) => {
          let sum = +item.so_tien_duyet;
          tienNoiTru += sum;
        });
        let dataNgoaitru = response.data_info.ho_so.filter((item) => item.hinh_thuc_ten === 'Ngoại trú');
        dataNgoaitru.filter((item) => {
          let sum = +item.so_tien_duyet;
          tienNgoaiTru += sum;
        });
        setTongTienNoiTru(tienNoiTru);
        setTongTienNgoaiTru(tienNgoaiTru);
        setDataLsBoiThuong(response.data_info.ho_so);
      } catch (error) {
        setLoading(false);
        Alert.alert('Thông báo', error.message);
      }

      return;
    }, 500);
  };

  /**RENDER  */
  const renderProfileItem = (item, index) => {
    const renderLabel = (label, value) => {
      return (
        <View style={styles.contentRow}>
          <Text style={styles.subLabel}>{label}: </Text>
          <Text style={styles.content}>{value}</Text>
        </View>
      );
    };

    return (
      <TouchableOpacity key={index} onPress={() => onPressItem(item)}>
        <LinearGradient
          colors={[colors.WHITE1, colors.WHITE1]}
          start={{x: 0, y: 0}}
          end={{x: 0, y: 1}}
          style={[styles.profileItemView, {borderColor: selectedItem === item ? colors.PRIMARY : colors.GRAY}]}>
          {renderLabel('Số hợp đồng', item.so_hd)}
          {renderLabel('Số GCN bảo hiểm', item.gcn)}
          {renderLabel('Sản phẩm', item.goi_bh)}
          {renderLabel('Tên người được bảo hiểm', item.ten)}
          {renderLabel('Số CMT/CCCD', item.so_cmt)}
          {renderLabel('Ngày sinh', item.ngay_sinh)}
          {renderLabel('Ngày hiệu lực', item.ngay_hl)}
          {renderLabel('Số điện thoại', item.d_thoai)}
          {renderLabel('Đơn vị cấp đơn', item.ten_dvi_cap_don)}
          <View style={styles.divider} />
          <View flexDirection="row" justifyContent="space-between">
            <TouchableOpacity style={styles.bottomBtn} onPress={() => xemQlBaoHiem(item)}>
              <Icon.FontAwesome name="list-alt" size={15} color={colors.PRIMARY} />
              <Text style={[styles.content, {fontWeight: '600', marginLeft: 8}]}>Quyền lợi bảo hiểm</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.bottomBtn} onPress={() => xemLsBoiThuong(item)}>
              <Icon.FontAwesome name="history" size={15} color={colors.PRIMARY} />
              <Text style={[styles.content, {fontWeight: '600', marginLeft: 8}]}>Lịch sử bồi thường</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <View flex={1}>
      <ButtonLinear linearStyle={styles.btnSearch} title="Tìm kiếm hợp đồng" onPress={onPressSearch} />
      {data.length > 0 ? (
        data.map((item, index) => renderProfileItem(item, index))
      ) : (
        <View style={styles.noDataView}>
          <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
          <Text>Chưa có dữ liệu</Text>
        </View>
      )}
      <ModalTimKiemHopDong ref={refModalTimKiem} setData={setData} onBackPress={() => refModalTimKiem.current.hide()} />
      <ModalShowQlBaoHiem refreshing={loading} ref={refModalShowQlBaoHiem} data={dataQlBaoHiem} onBackPress={() => refModalShowQlBaoHiem.current.hide()} />
      <ModalShowLsBoiThuong
        refreshing={loading}
        data={dataLsBoiThuong}
        ref={refModalShowLsBoiThuong}
        tongTienNoiTru={tongTienNoiTru}
        tongTienNgoaiTru={tongTienNgoaiTru}
        onBackPress={() => refModalShowLsBoiThuong.current.hide()}
      />
    </View>
  );
}

export const BaoLanhVienPhiBuoc1Screen = memo(BaoLanhVienPhiBuoc1ScreenComponent, isEqual);
