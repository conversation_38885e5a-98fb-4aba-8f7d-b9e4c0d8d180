import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonMoiQuanHeComponent = forwardRef((props, ref) => {
  const {onBackPress, setLabel, setMa, data} = props;
  const [isSelectedItemIndex, setIsSelectedItemIndex] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setLabel && setLabel(item.label);
    setMa && setMa(item.ma);
    setIsSelectedItemIndex(index);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn mối quan hệ" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderItem = (item, index) => {
    const isCheck = isSelectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK}}>{item.label}</Text>
        {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={{marginHorizontal: 16, paddingBottom: 20}} showsVerticalScrollIndicator={false}>
        <View>{data.map((item, index) => renderItem(item, index))}</View>
      </ScrollView>
    );
  };
  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      swipeDirection={['down', 'right']}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    // height: 300,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingLeft: 16,
    // flex: 1,
    borderRadius: 25,
    height: 40,
    margin: 16,
  },

  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    backgroundColor: colors.GRAY2,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
  },
  modalContentView: {
    marginHorizontal: 10,
    marginTop: 10,
  },
});

export const ModalChonMoiQuanHe = memo(ModalChonMoiQuanHeComponent, isEqual);
