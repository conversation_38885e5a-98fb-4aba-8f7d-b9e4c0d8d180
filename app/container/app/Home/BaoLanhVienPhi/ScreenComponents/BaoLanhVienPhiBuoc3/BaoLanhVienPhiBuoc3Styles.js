import {StyleSheet} from 'react-native';
import {colors} from '@app/commons/Theme';
export default StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    // paddingHorizontal: 15,
    backgroundColor: colors.WHITE,
    // marginTop: 10,
    backgroundColor: colors.PRIMARY_LIGHT,
  },
  scrollView: {
    width: width,
    backgroundColor: colors.PRIMARY_LIGHT,
  },
  btnLoginView: {
    flexDirection: 'row',
    backgroundColor: colors.PRIMARY,
    marginHorizontal: 10,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerView: {
    // flex: 1,
    // justifyContent: 'flex-end',
    // borderWidth: 1,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    // marginBottom: 30,
    // marginTop: 20,
    backgroundColor: colors.WHITE,
    // justifyContent: 'center',
  },
  stepIndicator: {
    backgroundColor: colors.WHITE,
    paddingVertical: 20,
  },
  txtBtnLogin: {
    paddingRight: 5,
    paddingVertical: 10,
    fontSize: 16,
    textTransform: 'uppercase',
    color: colors.WHITE,
  },
  headerTitle: {
    // marginBottom: 10,
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.BLUE3,
  },
  contentDetail: {
    // paddingHorizontal: 10,
    // marginBottom: 7,
    backgroundColor: colors.WHITE,
    borderRadius: 10,
    // marginHorizontal: 10,
  },
  modalSelectorView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    marginRight: 15,
  },

  dropDownSelectedTxt: {
    paddingVertical: 10,
    flex: 1,
    paddingLeft: 10,
    fontSize: 14,
    paddingRight: 5,
  },
  headerTitleView: {
    paddingHorizontal: 12,
    paddingVertical: 9,
    backgroundColor: colors.WHITE1,
    marginBottom: 10,
  },
  checkBoxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  txtDesc: {
    flex: 1,
    color: colors.BLACK_06,
  },
  subLabel: {
    color: colors.BLACK,
  },
  txtDetail: {
    color: colors.GRAY6,
  },
  profileInfoRow: {
    marginBottom: 10,
    flexDirection: 'row',
  },
  input: {
    height: 40,
    width: (width - 30) / 2,
  },
});
