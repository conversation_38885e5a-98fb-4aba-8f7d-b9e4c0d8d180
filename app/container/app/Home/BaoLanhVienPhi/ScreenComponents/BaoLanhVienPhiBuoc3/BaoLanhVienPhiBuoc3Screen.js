import {colors} from '@app/commons/Theme';
import {CommonOutlinedTextFieldWithIcon, DateTimePickerComponent, Text} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {View} from 'react-native';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import styles from './BaoLanhVienPhiBuoc3Styles';
import {ModalChonMoiQuanHe} from './Components';
import moment from 'moment';
import {spacing} from '@app/theme';

const data = [
  {label: 'Bản thân', ma: 'BAN_THAN'},
  {label: 'Bố', ma: 'BO'},
  {label: 'Mẹ', ma: 'BAN_THAN'},
  {label: 'Người khác (anh / chị , em ...)', ma: 'KHAC'},
];

const BaoLanhVienPhiBuoc3ScreenComponent = (props) => {
  const {
    isChecked,
    moiQuanHe,
    profileInfo,
    setMoiQuanHe,
    setMaMoiQuanHe,
    thongTinNguoiLienHe,
    thongtinNguoiDuocHuong,
    setThongTinNguoiLienHe,
    gioThongBao,
    setGioThongBao,
    ngayThongBao,
    setNgayThongBao,
    //format
  } = props;

  let refModalChonMoiQuanHe = useRef(null);
  const [toggleGioThongBao, setToggleGioThongBao] = useState(false);
  const [toggleNgayThongBao, setToggleNgayThongBao] = useState(false);

  useEffect(() => {
    if (isChecked) {
      setMoiQuanHe && setMoiQuanHe(data[0].label);
      setMaMoiQuanHe && setMaMoiQuanHe(data[0].ma);
    }
  }, [isChecked]);

  const onSelectMoiQuanHe = () => {
    if (isChecked) {
      return;
    } else refModalChonMoiQuanHe.current.show();
  };

  const onChangeText = async (field, value) => {
    setThongTinNguoiLienHe && setThongTinNguoiLienHe((prev) => ({...prev, [field]: value}));
  };

  /**RENDER  */

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerComponent
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        setToggleDateTime(false);
        setDateTime(dateSelected);
      }}
      onCancel={() => setToggleDateTime(false)}
      date={date}
      maximumDate={maxDate}
      minimumDate={minDate}
    />
  );
  // thông tin người được hưởng
  const renderContactInfo = () => {
    return (
      <>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin người được bảo hiểm</Text>
        </View>
        <View style={{marginHorizontal: 10}}>
          <View style={styles.profileInfoRow}>
            <Text style={styles.subLabel}>Họ tên: </Text>
            <Text style={styles.txtDetail} children={thongtinNguoiDuocHuong?.ten || profileInfo?.ten} />
          </View>
          <View style={styles.profileInfoRow}>
            <Text style={styles.subLabel}>Ngày sinh: </Text>
            <Text style={styles.txtDetail} children={thongtinNguoiDuocHuong?.ngay_sinh || profileInfo?.ngay_sinh} />
          </View>
          <View style={styles.profileInfoRow}>
            <Text style={styles.subLabel}>Số điện thoại: </Text>
            <Text style={styles.txtDetail} children={thongtinNguoiDuocHuong?.d_thoai || profileInfo?.dien_thoai} />
          </View>
          <View style={styles.profileInfoRow}>
            <Text style={styles.subLabel}>Email: </Text>
            <Text style={styles.txtDetail} children={thongtinNguoiDuocHuong?.email || profileInfo?.email} />
          </View>
        </View>
      </>
    );
  };
  /**THÔNG TIN NGƯỜI LIÊN HỆ */
  const renderContactUser = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin người liên hệ</Text>
        </View>
        <View style={{marginHorizontal: 10}}>
          <View style={styles.checkBoxRow}>
            <Text style={styles.txtDesc}>Người được bảo hiểm đồng thời là người liên hệ</Text>
            <BouncyCheckbox
              iconStyle={{borderColor: colors.PRIMARY}}
              fillColor={colors.PRIMARY}
              isChecked={isChecked}
              onPress={() => {
                props.onPressCheckBox();
              }}
            />
          </View>

          <CommonOutlinedTextFieldWithIcon
            isPickerModal={!isChecked}
            disabled={true}
            editable={false}
            value={moiQuanHe}
            styleContainer={{marginTop: 15}}
            placeholder={'Mối quan hệ'}
            onPressInput={() => onSelectMoiQuanHe()}
          />

          <View style={{flexDirection: 'row', flex: 1, justifyContent: 'space-between'}}>
            <CommonOutlinedTextFieldWithIcon
              placeholder="Họ tên"
              disabled={isChecked}
              editable={!isChecked}
              styleContainer={styles.input}
              onChangeText={(value) => {
                onChangeText('ten', value);
              }}
              value={thongTinNguoiLienHe?.ten}
            />
            <CommonOutlinedTextFieldWithIcon
              keyboardType="numeric"
              placeholder="Điện thoại"
              disabled={isChecked}
              editable={!isChecked}
              value={thongTinNguoiLienHe?.d_thoai}
              styleContainer={styles.input}
              onChangeText={(value) => onChangeText('d_thoai', value)}
            />
          </View>
          <CommonOutlinedTextFieldWithIcon
            placeholder="Email"
            disabled={isChecked}
            editable={!isChecked}
            keyboardType="email-address"
            value={thongTinNguoiLienHe?.email}
            onChangeText={(value) => onChangeText('email', value)}
            styleContainer={{height: 40}}
          />
        </View>
      </View>
    );
  };

  // render tên gói BH
  const renderGoiBaohiem = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={{margin: 10, flexDirection: 'row'}}>
          <Text style={styles.headerTitle}>Gói bảo hiểm: </Text>
          <Text style={{color: colors.BLACK, fontWeight: '600', flex: 1}}>{thongtinNguoiDuocHuong?.goi_bh}</Text>
        </View>
        <View style={{flexDirection: 'row', marginHorizontal: spacing.default}}>
          <View style={{flex: 1}}>
            <CommonOutlinedTextFieldWithIcon
              isDateTimeField
              editable={true}
              disabled={false}
              placeholder={'Giờ thông báo'}
              styleContainer={{marginRight: spacing.default}}
              onPressInput={() => setToggleGioThongBao(true)}
              value={gioThongBao ? moment(gioThongBao).format('HH:mm') : ''}
            />
            {renderDateTimeComp(toggleGioThongBao, setToggleGioThongBao, setGioThongBao, gioThongBao ? gioThongBao : new Date(), 'time')}
          </View>
          <View style={{flex: 1}}>
            <CommonOutlinedTextFieldWithIcon
              isDateTimeField
              isRequired
              editable={true}
              disabled={false}
              placeholder={'Ngày thông báo'}
              styleContainer={styles.containerInput}
              onPressInput={() => setToggleNgayThongBao(true)}
              value={ngayThongBao ? moment(ngayThongBao).format('DD/MM/YYYY') : ''}
            />
            {renderDateTimeComp(toggleNgayThongBao, setToggleNgayThongBao, setNgayThongBao, ngayThongBao ? ngayThongBao : new Date())}
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      {/* Thông tin gói bảo hiểm */}
      {renderGoiBaohiem()}
      {/* thông tin người được hưởng */}
      {renderContactInfo()}
      {/* Thông tin người liên hệ */}
      {renderContactUser()}
      <ModalChonMoiQuanHe ref={refModalChonMoiQuanHe} data={data} setMa={setMaMoiQuanHe} setLabel={setMoiQuanHe} onBackPress={() => refModalChonMoiQuanHe.current.hide()} />
    </View>
  );
};

export const BaoLanhVienPhiBuoc3Screen = memo(BaoLanhVienPhiBuoc3ScreenComponent, isEqual);
