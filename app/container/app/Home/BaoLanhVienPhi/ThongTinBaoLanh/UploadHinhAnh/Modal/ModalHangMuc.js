import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Empty, HeaderModal, SearchBar, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

function ModalHangMuc(props) {
  const {data} = props;
  const {toggleModalHangMuc, onHangMucSelected, closeModal} = props;

  const [searchInput, setSearchInput] = useState('');
  const [dataHangMuc, setDataHangMuc] = useState([]);

  useEffect(() => {
    if (data?.length > 0) {
    }
  }, []);

  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 500);

  useEffect(() => {
    if (searchInput) {
      const lowerCaseSearchText = searchInput?.toLowerCase();
      const filter = data.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDataHangMuc(filter);
    } else if (searchInput === '') {
      setDataHangMuc(data);
    }
  }, [searchInput]);

  /* RENDER */
  const renderItemHangMuc = ({item, index}) => (
    <TouchableOpacity
      key={index}
      style={styles.itemHangMucView}
      onPress={() => {
        closeModal(false);
        onHangMucSelected(item);
      }}>
      <Text children={item.ten} />
    </TouchableOpacity>
  );

  const renderContent = () => {
    return (
      <FlatList
        data={dataHangMuc}
        extraData={dataHangMuc}
        renderItem={renderItemHangMuc}
        onEndReachedThreshold={0.3}
        style={styles.flStyles}
        keyExtractor={(item, index) => item + index.toString()}
        ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
      />
    );
  };
  return (
    <Modal onModalShow={() => setDataHangMuc(data)} isVisible={toggleModalHangMuc} style={styles.modal}>
      <SafeAreaView style={{flex: 1}}>
        <HeaderModal centerComponent={<SearchBar placeholder="Tìm kiếm hạng mục" onTextChange={debounced} />} onBackPress={() => closeModal(false)} />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },

  searchInput: {
    flex: 1,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 10,
    marginHorizontal: 10,
  },
  title: {
    fontSize: 18,
    marginLeft: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});

// const mapStateToProps = (state) => ({});
// const mapDispatchToProps = {};
// export default connect(mapStateToProps, mapDispatchToProps)(ModalHangMuc);
// const ModalHangMucConnect = connect(mapStateToProps, {})(ModalHangMuc);
export default memo(ModalHangMuc, isEqual);
