import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import {APP_NAME} from '@constant';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Platform, StyleSheet, TouchableOpacity, Vibration, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

function ModalCamera(props) {
  let cameraRef;
  const [flashData, setFlashType] = useState({
    flashIcon: 'flash-auto',
    flashMode: RNCamera.Constants.FlashMode.auto,
  });
  const [cameraType, setCameraType] = useState(RNCamera.Constants.Type.back);
  const {toggleModalCamera, tatCameraModal, giayToDuocChon, handleImage, setDialogLoading, menuImageStep3Selected, appSetting} = props;
  const [countPicture, setCountPicture] = useState(0);
  const [enableCameraBtn, setEnableCameraBtn] = useState(false);
  const [loaiCat, setLoaiCat] = useState(0); //0 là điện thoại chụp dọc - 1 điện thoại chụp ngang

  //xử lý khi icon FLash được click
  const onPressChangeFlash = () => {
    //Nếu đang auto -> flash
    if (flashData.flashIcon == 'flash-auto') {
      setFlashType({
        flashIcon: 'flash',
        flashMode: RNCamera.Constants.FlashMode.on,
      });
    }
    //nếu đang flash -> tắt
    else if (flashData.flashIcon == 'flash') {
      setFlashType({
        flashIcon: 'flash-off',
        flashMode: RNCamera.Constants.FlashMode.off,
      });
    }
    //nếu đang tắt flash -> flash-auto
    else if (flashData.flashIcon == 'flash-off') {
      setFlashType({
        flashIcon: 'flash-auto',
        flashMode: RNCamera.Constants.FlashMode.auto,
      });
    }
  };
  //chuyển camera trước - sau
  const onPressSwapCamera = () => {
    if (cameraType == RNCamera.Constants.Type.back) {
      setCameraType(RNCamera.Constants.Type.front);
    } else if (cameraType == RNCamera.Constants.Type.front) {
      setCameraType(RNCamera.Constants.Type.back);
    }
  };
  //ẩn modal camera
  const onPressTatCameraModal = () => {
    tatCameraModal();
    setFlashType({
      flashIcon: 'flash-auto',
      flashMode: RNCamera.Constants.FlashMode.auto,
    });
    setCameraType(RNCamera.Constants.Type.back);
    setCountPicture(0);
    setLoaiCat(0);
  };

  const getAnhTitle = () => {
    return menuImageStep3Selected?.ten;
  };

  const onPressChupAnh = async () => {
    if (cameraRef) {
      let cameraOptions = {fixOrientation: true, quality: 0.8};
      const dataImage = await cameraRef.takePictureAsync(cameraOptions);
      dataImage.path = dataImage.uri;
      handleImage(dataImage, menuImageStep3Selected, null);
      if (appSetting.rung) Vibration.vibrate(200);
      setCountPicture(countPicture + 1);
    }
  };

  /* RENDER */
  return (
    <Modal isVisible={toggleModalCamera} swipeDirection={'down'} style={styles.modal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          <View style={{flex: 1}}>
            <RNCamera
              ref={(ref) => (cameraRef = ref)}
              playSoundOnCapture={appSetting.amThanhKhiChup}
              // onCameraReady={prepareRatio}
              style={styles.cameraPreview}
              type={cameraType}
              flashMode={flashData.flashMode}
              captureAudio={false}
              zoom={0}
              useNativeZoom={true}
              androidCameraPermissionOptions={{
                title: 'Cho phép ' + APP_NAME + ' truy cập camera của bạn',
                message: APP_NAME + ' muốn truy cập camera của bạn để chụp ảnh',
                buttonPositive: 'Đồng ý',
                buttonNegative: 'Để sau',
              }}>
              <View style={styles.topViewCamera}>
                <TouchableOpacity onPress={onPressTatCameraModal} style={styles.btnCloseCamera}>
                  <Icon.AntDesign name="arrowleft" size={24} color={colors.WHITE} />
                </TouchableOpacity>
                <TouchableOpacity onPress={onPressTatCameraModal} style={styles.btnCloseCamera}>
                  {/* <Text children="Xong" style={styles.txtDanhGia} /> */}
                  <Icon.Feather name="check" size={24} color={colors.WHITE} />
                </TouchableOpacity>
              </View>

              <View style={styles.anhTitleView}>
                <Text children={getAnhTitle()} style={styles.txtTitleAnh} />
              </View>

              <View style={styles.countPicture}>
                <Text children={countPicture} style={styles.txtCountPicture} />
              </View>
            </RNCamera>
          </View>
          <View style={styles.btnsCameraView}>
            <TouchableOpacity onPress={onPressChangeFlash} style={styles.btnCameraView}>
              <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressChupAnh} disabled={enableCameraBtn}>
              <Icon.MaterialCommunityIcons name={'circle-slice-8'} size={100} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
              <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnsCameraView: {
    flexDirection: 'row',
    backgroundColor: colors.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    height: 120,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  topViewCamera: {
    position: 'absolute',
    left: 0,
    top: Platform.OS == 'android' ? 20 : 50,
    right: 0,
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    // flex: 1,
    // borderWidth: 1,
    // borderColor: '#FFF',
    flexDirection: 'row',
  },
  btnCloseCamera: {
    // position: 'absolute',
    // left: 10,
    // top: Platform.OS == 'android' ? 30 : 30,
    padding: 8,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  btnDanhGia: {
    // position: 'absolute',
    // right: 10,
    // top: Platform.OS == 'android' ? 20 : 70,
    flexDirection: 'row',
    alignItems: 'center',
  },
  countPicture: {
    position: 'absolute',
    right: 15,
    bottom: 5,
  },
  txtCountPicture: {
    fontSize: 40,
    color: '#FFF',
    fontWeight: 'bold',
  },
  khungCatAnhCamera: {
    borderWidth: 3,
    borderStyle: 'dashed',
    borderRadius: 20,
  },
  //kích thước BANG_LAI_XE DỌC
  sizeBLXDoc: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.63,
  },
  //kích thước DANG_KIEM DỌC
  sizeDKDoc: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.67,
  },

  //kích thước BANG_LAI_XE DỌC
  sizeBLXNgang: {
    width: dimensions.width - 30,
    height: dimensions.height * 0.63,
  },
  //kích thước DANG_KIEM DỌC
  sizeDKNgang: {
    width: dimensions.width - 30,
    height: dimensions.height * 0.67,
  },

  txtDanhGia: {
    fontWeight: 'bold',
    fontSize: 16,
    color: colors.WHITE,
    marginRight: 5,
  },
  anhTitleView: {
    position: 'absolute',
    bottom: 10,
  },
  txtTitleAnh: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
    paddingHorizontal: 30,
    textAlign: 'center',
  },
  cropView: {
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cropTopView: {
    flex: 1,
    backgroundColor: '#000',
    width: dimensions.width,
    opacity: 0.8,
  },
});

const mapStateToProps = (state) => ({
  appSetting: state.appSetting,
});

// const mapDispatchToProps = {};

// export default connect(mapStateToProps, mapDispatchToProps)(ModalCamera);
const ModalCameraConnect = connect(mapStateToProps, {})(ModalCamera);
export default memo(ModalCameraConnect, isEqual);
