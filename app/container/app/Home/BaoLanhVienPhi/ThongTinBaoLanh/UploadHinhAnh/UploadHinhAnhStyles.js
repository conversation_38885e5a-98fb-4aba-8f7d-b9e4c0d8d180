import {colors} from '@app/commons/Theme';
import {StyleSheet, Dimensions, Platform} from 'react-native';
const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  imageCompView: {
    width: width / 2,
    position: 'relative',
  },
  scrollView: {
    width: width,
  },
  imageProcess: {
    borderRadius: 20,
    marginVertical: 15,
    resizeMode: 'cover',
    marginHorizontal: 15,
    width: width / 2 - 30,
    height: width / 2 - 30,
    backgroundColor: colors.GRAY2,
  },
  stepIndicator: {
    marginVertical: 10,
  },
  stepLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    color: colors.GREEN,
  },
  footerView: {
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    // marginBottom: Platform.OS === 'android' ? 10 : 30,
  },
  buttonCameraView: {
    bottom: 0,
    alignSelf: 'center',
    position: 'absolute',
    flexDirection: 'row',
  },
  btnHint: {
    flex: 0,
    borderRadius: 40,
  },
  btnBack: {
    flex: 1,
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    backgroundColor: colors.PRIMARY_08,
  },
  iconRightBtnView: {
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
    backgroundColor: colors.PRIMARY_DARK_08,
  },
  iconRightBtn: {
    flex: 0,
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconLeftBtnView: {
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    backgroundColor: colors.PRIMARY_DARK_08,
  },
  iconLeftBtn: {
    flex: 0,
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  btnNext: {
    flex: 1,
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    backgroundColor: colors.PRIMARY_08,
  },
  txtBtnBottom: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.WHITE,
  },
  btnHintView: {
    right: 10,
    bottom: 90,
    position: 'absolute',
  },
  btnImageView: {
    right: 10,
    bottom: 90,
    position: 'absolute',
  },
  imageCategoryTitleView: {
    flex: 1,
    marginTop: 10,
    flexDirection: 'row',
    marginHorizontal: 15,
    alignItems: 'center',
    borderColor: colors.GRAY,
  },
  imageCategoryTitle: {
    flex: 1,
    marginLeft: 10,
    fontWeight: 'bold',
    color: colors.BLACK,
    justifyContent: 'flex-start',
  },
  btnCamera: {
    opacity: 0.5,
    borderRadius: 40,
    paddingVertical: 10,
    marginHorizontal: 10,
    paddingHorizontal: 10,
    backgroundColor: colors.PRIMARY,
  },
});
