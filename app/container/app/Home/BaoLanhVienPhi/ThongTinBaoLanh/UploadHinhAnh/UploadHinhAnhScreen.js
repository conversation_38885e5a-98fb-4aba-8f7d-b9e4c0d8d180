import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {getImageNameFromUriCamera, saveToCameraRoll} from '@app/utils/CameraProvider';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {Icon, ImageComp, ScreenComponent, Text} from '@component';
import {DATA_CONSTANT} from '@constant';
import R from '@R';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, FlatList, Platform, ScrollView, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import ImageCropPicker from 'react-native-image-crop-picker';
import {connect} from 'react-redux';
import ModalCamera from './Modal/ModalCamera';
import ModalHangMuc from './Modal/ModalHangMuc';
import ModalXemChiTietAnh from './Modal/ModalXemChiTietAnh';
import ModalXemLaiAnh from './Modal/ModalXemLaiAnh';
import styles from './UploadHinhAnhStyles';

const UploadHinhAnhScreenComponent = (props) => {
  const {route, navigation} = props;
  const {action, profileData} = route.params;
  const categoryImage = profileData.nhom_hang_muc ? profileData.nhom_hang_muc : categoryImage;

  const [anhHoSo, setAnhHoSo] = useState(route.params.imagesData || []);
  const [currentPosition, setCurrentPosition] = useState({});
  const [toggleLoading, setToggleLoading] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ
  const [toggleModalHangMuc, setToggleModalHangMuc] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ
  const [imgsData, setImgsData] = useState([]);
  //data của modal hướng dẫn chụp ảnh
  const [toggleModalImage, setToggleModalImage] = useState(false);
  const [modalImageSelectedData, setModalImageSelectedData] = useState(null);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [menuImageSelected, setMenuImageSelected] = useState({});

  //data modal phần xem lại ảnh
  const [anhXemLai, setAnhXemLai] = useState([]);
  const [toggleModalXemLaiAnh, setToggleModalXemLaiAnh] = useState(false);

  //data modal camera
  const [toggleModalCamera, setToggleModalCamera] = useState(false);

  const [dataHangMuc, setDataHangMuc] = useState([]);

  let [type, setType] = useState(-1);
  let [indexOpened, setIndexOpened] = useState(-1);

  /* FUNCTION  */
  useEffect(() => {
    getData();
    requestCurrentLocation(
      (position) => setCurrentPosition(position),
      (err) => console.log(err),
    );
  }, []);

  const getData = async () => {
    let params = {
      ma_doi_tac: profileData?.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DANH_SACH_HANG_MUC_ANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const filter = response.data_info.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU);
      setDataHangMuc(cloneObject(filter));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //xử lý nút Back
  // useEffect(() => {
  //   let backHandler;
  //   navigation.addListener('focus', () => {
  //     backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
  //   });
  //   navigation.addListener('blur', () => {
  //     backHandler.remove();
  //   });
  // }, []);

  //xử lý nút quay lại ở màn Chụp ảnh
  const backAction = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn thoát chụp ảnh?', [
      {
        text: 'Huỷ',
        onPress: () => null,
        style: 'cancel',
      },
      {
        text: 'Đồng ý',
        onPress: () => NavigationUtil.pop(),
      },
    ]);
    return true;
  };
  //lấy ảnh thumbnail của hồ sơ
  const getThumbnailDocument = async () => {
    try {
      let response = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, {so_id: profileData.ho_so?.so_id});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let imagesTmp = response.data_info.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
        };
        item.nhom = nhom;
        return item;
      });
      setAnhHoSo([...imagesTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //khởi tạo ảnh step 1
  const initimgsData = () => {
    let imgsDataTmp = [[]];
    setImgsData(imgsDataTmp);
  };

  //xử lý ảnh khi chụp

  const handleImage = (imageData, menuImageSelected, indexOpened, type) => {
    const imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri
    if (type === 0) {
      saveToCameraRoll(imageData.path);
    }
    //ẢNH TỔN THẤT
    let imageDataTmp = imgsData;
    let daTonTai = false;
    imageDataTmp = imageDataTmp.map((item, index) => {
      if (item.ma == menuImageSelected.ma) {
        daTonTai = true;
        if (!item.images) images.item = []; //nếu chưa có mảng image thì khởi tạo
        //push ảnh lên đầu
        item.images.unshift({
          path: imageData.path,
          name: imageName,
          nhom: {...menuImageSelected},
        });
      }
      return item;
    });
    if (!daTonTai) {
      let hangMucMoi = {...menuImageSelected};
      hangMucMoi.images = [
        {
          path: imageData.path,
          name: imageName,
          nhom: {...menuImageSelected},
        },
        {path: '', preView: R.icons.ic_gallery, nhom: {...menuImageSelected}},
      ];
      imageDataTmp.push(hangMucMoi);
    }
    setImgsData(imageDataTmp);
  };

  // Xl ảnh thành công
  const xuLyAnhUploadThanhCong = (anhThanhCong) => {
    let imageDataTmp = imgsData;
    imageDataTmp.map((itemHangMuc) => {
      itemHangMuc.images.map((itemAnh) => {
        if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
        return itemAnh;
      });
      return itemHangMuc;
    });
    setImgsData([...imageDataTmp]);
  };

  //xoá ảnh
  const removeImage = (imageData) => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Đồng ý',
        onPress: () => {
          let arr = imgsData; //lấy dữ liệu tmp
          arr = arr.map((item) => {
            if (item.ma === imageData.item.nhom.ma) item.images.splice(imageData.index, 1);
            return item;
          });
          setImgsData([...arr]);
        },
      },
    ]);
  };

  // xl ảnh thất bại
  const xuLyAnhUploadThatBai = (anhThatBai) => {
    let imageDataTmp = imgsData;
    imageDataTmp.map((itemHangMuc) => {
      itemHangMuc.images.map((itemAnh) => {
        if (itemAnh.path == anhThatBai.path) {
          itemAnh.uploadThatBai = true;
          itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
        }
        return itemAnh;
      });
      return itemHangMuc;
    });
    setImgsData([...imageDataTmp]);
  };

  // ấn nút NEXT
  const onPressNext = async () => {
    if (imgsData.length <= 0) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn ít nhất 1 ảnh', 'warning');
    if (toggleLoading) return;
    let imagesUploadToServer = [];
    // console.log('sttMax', sttMax);
    imgsData.map((imageItem, index) => {
      if (imageItem.images) {
        for (let i = 0; i < imageItem.images.length; i++)
          if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
            imagesUploadToServer.push(imageItem.images[i]);
          }
      }
    });
    setImgsData([...imgsData]);
    setToggleLoading(true);
    let response = await Promise.all(
      imagesUploadToServer.map(async (item) => {
        return uploadImageToServer(
          [item],
          (anhThanhCong) => {
            // // let imageUploadedTmp = imageUploaded;
            // imageUploadedTmp[currentPage] = imageUploadedTmp[currentPage] + 1;
            // // console.log('imageUploadedTmp', imageUploadedTmp);
            // setImageUploaded([...imageUploadedTmp]);
            xuLyAnhUploadThanhCong(anhThanhCong);
          },
          (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
        );
      }),
    );
    let haveErr = false;
    response.map((item, index) => {
      if (item != true) {
        haveErr = true;
        if (item.message == 'Network Error') {
          FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng kiểm tra lại đường truyền');
          return;
        } else FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi khi upload ảnh thứ ' + index + 1, 'info');
      }
    });
    //thực hiện xong hết thì mới reset lại data
    setToggleLoading(false);
    getThumbnailDocument();
    if (haveErr) return;
    // if (route.params.loaiAnh) {
    //   NavigationUtil.pop();
    //   FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
    //   return;
    // }
    FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
    NavigationUtil.pop();
  };

  //mở ảnh đã chụp
  const onPressAnhDaChup = () => {
    setToggleModalXemLaiAnh(true);
    let imgsTmp = [];
    //ảnh hiện trường
    imgsTmp = anhHoSo.filter((item) => item.loai == DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH && item.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.HIEN_TRUONG);
    setAnhXemLai([...imgsTmp]);
  };

  //type : 0 - camera ; type : 1 - lib
  const onPressOpenCamera = (indexOpened, menuImageData, type) => {
    setMenuImageSelected(menuImageData);
    if (menuImageData === undefined) {
      setToggleModalHangMuc(true);
    } else if (type === 0) {
      openCameraModal();
    } else if (type === 1) {
      openCamera(indexOpened, menuImageData, type);
    }
    setType(type);
    setIndexOpened(indexOpened);
    // actionSheetChupAnhRef.show();
  };

  //mở IMAGE CROP CAMERA
  //type : 0 - camera ; type : 1 - library
  const openCamera = (indexOpened, menuImageData, type) => {
    let imgCropOpts = {
      mediaType: 'photo', //mặc định là chụp ảnh từ camera
      cropping: false,
      enableRotationGesture: true,
      compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
      showCropGuidelines: true, //tắt khung 3x3 đi
      compressImageQuality: 0.5,
      useFrontCamera: false,
    };

    if (type == 1) {
      ImageCropPicker.openPicker(imgCropOpts)
        .then((data) => handleImage(data, menuImageData, indexOpened, type))
        .catch((err) => console.log('err', err));
      return;
    }
    //Open Camera
    ImageCropPicker.openCamera(imgCropOpts)
      .then((data) => handleImage(data, menuImageData, indexOpened, type))
      .catch((err) => console.log('err', err));
  };

  //tắt modal camera
  const onPressTatCameraModal = () => {
    setToggleModalCamera(false);
  };
  //mở modal camera
  const openCameraModal = () => {
    setToggleModalCamera(true);
  };

  //upload ảnh lên server
  const uploadImageToServer = (imagesData, cbSuccess) => {
    return new Promise(
      async (resolve) => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            nhom: e.nhom.ma,
            x: currentPosition.coords?.latitude,
            y: currentPosition.coords?.longitude,
            stt_hang_muc: 0,
          };
          files.push(file);
        });
        let pm = action === 'TTBT' ? 'BT' : action === 'HSTT' ? 'TN' : 'BL';
        try {
          let params = {
            pm: pm, //null
            nv: 'NG', // HSGD
            files: files,
            images: imagesData,
            ung_dung: 'MOBILE_BT', //MOBILE
            so_id: profileData.so_id,
            ma_doi_tac: profileData.ma_doi_tac,
          };
          console.log('🚀 ~ file: UploadHinhAnhScreen.js:354 ~ params:', params);

          let response = await ESmartClaimEndpoint.uploadFile(AxiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          setToggleLoading(false);
          if (!response || !response.state_info || response.state_info.status !== 'OK') {
            resolve(response);
            return;
          }
          resolve(true);
        } catch (error) {
          Alert.alert('Thông báo', error.message);
          resolve(error);
        }
        return;
      },
      (reject) => reject(),
    );
  };

  //lấy chi tiết ảnh đã chụp
  const onPressLayChiTietAnh = async (imageData) => {
    try {
      let params = {
        so_id: imageData.so_id,
        bt: imageData.bt,
      };
      let response = await ESmartClaimEndpoint.getFile(AxiosConfig.ACTION_CODE.DOCUMENT_DETAIL, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setModalImageSelectedData({
        title: 'Ảnh chi tiết',
        imageData: response.data_info.duong_dan,
        base64: true,
      });
      setToggleModalImage(true);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  //xem chi tiết ảnh đã chụp
  const onPressXemLaiAnh = (imageData) => {
    setModalImageSelectedData({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
    setToggleModalImage(true);
  };

  //nút xem lại ảnh đã chụp
  const renderBtnImages = () => {
    return (
      <TouchableOpacity style={{...styles.btnHint}} activeOpacity={0.5} onPress={onPressAnhDaChup}>
        <Icon.Ionicons name={'image'} size={40} color={colors.PRIMARY_03} />
      </TouchableOpacity>
    );
  };
  // các nút ở footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <View style={styles.btnBack} />
        {/* NÚT TIẾP TỤC - HOÀN THÀNH   */}
        <TouchableOpacity activeOpacity={0.5} onPress={onPressNext} style={styles.btnNext} disabled={toggleLoading}>
          {!toggleLoading ? <Text style={styles.txtBtnBottom}>Tải lên</Text> : <ActivityIndicator size="large" color={colors.GRAY} style={{flex: 1}} />}
          <View style={styles.iconRightBtnView}>
            <Icon.Ionicons name={'checkmark-sharp'} size={25} style={styles.iconRightBtn} color={colors.WHITE} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  //render ra 1 item của menu
  const renderItemMenuImage = (data) => {
    if (!data.item.images || data.item.images.length == 1) return null;
    return (
      <View style={[styles.itemMenuView, data.index % 2 == 0 && {backgroundColor: colors.WHITE8}]}>
        <View style={styles.imageCategoryTitleView}>
          <Icon.Ionicons name="images-outline" size={20} />
          <Text numberOfLines={2} style={styles.imageCategoryTitle}>
            {data.item.ten}
          </Text>
        </View>
        <FlatList
          numColumns={2}
          horizontal={false}
          scrollEnabled={true}
          data={data.item.images}
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, index) => item + index.toString()}
          renderItem={(item) => renderImageItem(item, {images: data.item.images})}
        />
      </View>
    );
    // else return null;
  };

  const renderImageCategoryButton = () => {
    return (
      <View marginTop={10}>
        <View style={styles.imageCategoryTitleView}>
          <Icon.Ionicons name="images-outline" size={20} />
          <Text style={styles.imageCategoryTitle}>Thêm hạng mục mới</Text>
        </View>
        <TouchableOpacity style={styles.imageCompView} onPress={() => onPressOpenCamera(-1, undefined, 0)}>
          <FastImage
            style={{
              ...styles.imageProcess,
            }}
            source={R.icons.ic_gallery}
          />
          <View style={styles.buttonCameraView}>
            <TouchableOpacity style={styles.btnCamera} onPress={() => onPressOpenCamera(-1, undefined, 0)}>
              <Icon.FontAwesome name={'camera'} size={20} color={colors.WHITE} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.btnCamera} onPress={() => onPressOpenCamera(-1, undefined, 1)}>
              <Icon.Ionicons name={'images'} size={20} color={colors.WHITE} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderImageItem = (imageData) => {
    return (
      <ImageComp
        uploadFromLib={true}
        imageData={imageData}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        removeImage={removeImage}
        onPressXemLaiAnh={onPressXemLaiAnh}
        onPressOpenCamera={onPressOpenCamera}
      />
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Tải ảnh lên"
      onPressBack={backAction}
      renderView={
        <View flex={1}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <FlatList data={imgsData} renderItem={renderItemMenuImage} initialNumToRender={imgsData.length} />
            {renderImageCategoryButton()}

            {/* modal camera  */}
            <ModalCamera
              key="ModalCamera"
              handleImage={handleImage}
              giayToDuocChon={menuImageSelected}
              setDialogLoading={setDialogLoading}
              toggleModalCamera={toggleModalCamera}
              tatCameraModal={onPressTatCameraModal}
              menuImageStep3Selected={menuImageSelected}
            />
            <ModalXemChiTietAnh key="ModalXemChiTietAnh" toggleModalImage={toggleModalImage} setToggleModalImage={setToggleModalImage} modalImageSelectedData={modalImageSelectedData} />
            <ModalXemLaiAnh setToggleModalXemLaiAnh={setToggleModalXemLaiAnh} toggleModalXemLaiAnh={toggleModalXemLaiAnh} anhXemLai={anhXemLai} onPressLayChiTietAnh={onPressLayChiTietAnh} />
            <ModalHangMuc
              data={dataHangMuc}
              closeModal={setToggleModalHangMuc}
              toggleModalHangMuc={toggleModalHangMuc}
              onHangMucSelected={(data) => {
                setMenuImageSelected(data);
                setDialogLoading(true);
                setTimeout(
                  () => {
                    setDialogLoading(false);
                    // openCameraModal();
                    if (type === 0) {
                      openCameraModal();
                    } else openCamera(indexOpened, data, 1);
                  },
                  Platform.OS == 'android' ? 100 : 1000,
                );
              }}
            />
          </ScrollView>
          {/* nút xem lại ảnh đã chụp */}
          {/* <View style={styles.btnImageView}>{renderBtnImages()}</View> */}
          {/* render phần nút dưới cùng */}

          {renderFooter()}
        </View>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  categoryImage: state.categoryImage.data,
  categoryCommon: state.categoryCommon.data,
});
const UploadHinhAnhScreenConnect = connect(mapStateToProps, {})(UploadHinhAnhScreenComponent);
export const UploadHinhAnhScreen = memo(UploadHinhAnhScreenConnect, isEqual);
