import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 10,
  },
  scrollView: {
    flex: 1,
    marginBottom: 10,
  },
  contentView: {
    flex: 1,
    marginHorizontal: spacing.small,
  },
  dropDownView: {
    marginVertical: 10,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  inputTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
    // zIndex: 1000,
  },
  linearBtnView: {
    flex: 1,
    marginHorizontal: 5,
    // zIndex: 1000,
    borderRadius: 30,
    backgroundColor: colors.WHITE,
    marginBottom: 20,
  },
  btnReceivingRecords: {
    justifyContent: 'center',
    alignItems: 'center',
    // justifyContent: 'space-around',
    padding: 10,
    minHeight: 50,
    borderRadius: 10,
  },
  txtBtnReceivingRecords: {
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  textInputType: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: colors.GRAY,
    paddingLeft: 15,
    color: colors.BLACK,
    backgroundColor: colors.WHITE,
    // zIndex: 1000,
  },
  textInputView: {
    marginBottom: 10,
    // zIndex: 1000,
  },
  footerView: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingHorizontal: spacing.medium,
  },
  filterView: {
    marginTop: 20,
    marginBottom: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
