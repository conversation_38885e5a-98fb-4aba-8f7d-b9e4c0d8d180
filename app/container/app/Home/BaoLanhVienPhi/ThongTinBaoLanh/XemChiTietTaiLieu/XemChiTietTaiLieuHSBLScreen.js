import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, Text} from '@component';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {SafeAreaView, ScrollView, View} from 'react-native';
import {Switch} from 'react-native-switch';
import {connect} from 'react-redux';
import RenderImage from './Component/RenderImage';
import styles from './XemChiTietTaiLieuHSBLStyles';

const XemChiTietTaiLieuHSBLComponent = (props) => {
  console.log('XemChiTietTaiLieuHSBLComponent');
  const {navigation, route} = props;
  const {profileInfo, action} = route?.params;

  const [switchImgView, setSwitchImgView] = useState(false);
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // useEffect(() => {
  //   navigation.addListener('focus', () => {
  //     setProfileData(profileInfo);
  //   });
  // }, []);

  const onPressPhanLoaiDanhMuc = () => {
    let allImgChecked = [];
    data.map((item) => {
      item.images.map((itemImg) => {
        if (itemImg.checked) allImgChecked.push(itemImg);
      });
    });

    if (allImgChecked.length == 0) {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn ảnh phân loại', 'info');
      return;
    }
    NavigationUtil.push(SCREEN_ROUTER_APP.PHAN_LOAI_TAI_LIEU, {
      imagesClassify: allImgChecked,
      profileData: profileInfo,
      action,
    });
  };

  const renderSwitchImageViewButton = (title, value, onValueChange, activeText, inActiveText, switchLeftPx, switchRightPx, switchWidthMultiplier) => {
    return (
      <View style={styles.switch}>
        <Text style={{fontSize: 16, marginRight: 10}}>{title}</Text>
        <Switch
          value={value}
          onValueChange={onValueChange}
          disabled={false}
          activeText={activeText}
          inActiveText={inActiveText}
          circleSize={20}
          barHeight={25}
          circleBorderWidth={0}
          // màu khi active
          circleActiveColor={colors.BLUE3_07}
          backgroundActive={'gray'}
          //màu khi InActive
          circleInActiveColor={colors.WHITE}
          backgroundInactive={colors.BLUE3_07}
          // renderInsideCircle={() => <CustomComponent />} // custom component to render inside the Switch circle (Text, Image, etc.)
          changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
          innerCircleStyle={{alignItems: 'center', justifyContent: 'center'}} // style for inner animated circle for what you (may) be rendering inside the circle
          outerCircleStyle={{}} // style for outer animated circle
          renderActiveText={true}
          renderInActiveText={true}
          switchLeftPx={switchLeftPx} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
          switchRightPx={switchRightPx} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
          switchWidthMultiplier={switchWidthMultiplier} // multipled by the `circleSize` prop to calculate total width of the Switch
          switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
        />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={isLoading}
      headerTitle={'Ảnh hồ sơ, giấy tờ'}
      renderView={
        <SafeAreaView style={styles.container}>
          {renderSwitchImageViewButton('Chế độ ảnh', switchImgView, setSwitchImgView, 'XEM  ', 'CHỌN', 4, 5, 4)}
          <ScrollView showsVerticalScrollIndicator={false} style={{marginBottom: 50}}>
            <RenderImage setIsLoading={setIsLoading} navigation={navigation} profileInfo={profileInfo} switchImgView={switchImgView} setData={setData} />
          </ScrollView>
          <View style={styles.footerView}>
            <ButtonLinear title="Tải ảnh lên" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.UPLOAD_HINH_ANH, {profileData: profileInfo, action: action})} linearStyle={styles.footerBtn} />
            <ButtonLinear title="Phân loại danh mục" onPress={() => onPressPhanLoaiDanhMuc()} linearStyle={styles.footerBtn} />
          </View>
        </SafeAreaView>
      }
    />
  );
};
// export default CompensationDocument;

const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
});

const mapDispatchToProps = {};

const XemChiTietTaiLieuHSBLConnect = connect(mapStateToProps, mapDispatchToProps)(XemChiTietTaiLieuHSBLComponent);
export const XemChiTietTaiLieuHSBLScreen = memo(XemChiTietTaiLieuHSBLConnect, isEqual);
