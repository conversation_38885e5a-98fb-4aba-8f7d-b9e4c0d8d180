import {colors} from '@app/commons/Theme';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

const {StyleSheet, Dimensions, Platform} = require('react-native');

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  imageDocument: {
    width: width / 4,
    height: width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 1,
  },
  switch: {
    // flex: 1,
    padding: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY2,
  },
  checkboxImgView: {
    right: 15,
    bottom: 15,
    position: 'absolute',
    borderTopLeftRadius: 5,
    backgroundColor: colors.WHITE,
  },
  container: {
    flex: 1,
  },
  footerView: {
    flex: 1,
    left: 0,
    right: 0,
    bottom: 0,
    width: width,
    paddingVertical: 10,
    position: 'absolute',
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: 10,
    // marginBottom: Platform.OS === 'ios' ? getStatusBarHeight() - 10 : 0,
  },
});
