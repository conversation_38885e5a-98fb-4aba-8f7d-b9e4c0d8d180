import {colors} from '@app/commons/Theme';
import {StyleSheet, Dimensions, Platform} from 'react-native';
const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    flex: 1,
    marginBottom: 50,
  },
  btnActive: {
    borderBottomWidth: 0,
    backgroundColor: colors.WHITE,
  },
  iconBtnTopLeftView: {
    marginRight: 10,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    alignSelf: 'center',
    marginHorizontal: 15,
  },
  btnTopView: {
    width: width,
    flexDirection: 'row',
  },
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    justifyContent: 'center',
    backgroundColor: colors.WHITE5,
  },
  txtTitle: {
    fontSize: 12,
    marginBottom: 4,
    color: colors.BLACK_03,
  },
  txtDetail: {
    // flexShrink: 1,
    flex: 1,
    color: colors.GRAY6,
    textAlign: 'justify',
  },

  inforView: {
    flex: 1,
    paddingLeft: 16,
    paddingVertical: 8,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
    backgroundColor: colors.WHITE,
    // borderBottomWidth: 1,
  },
  inforHeaderView: {
    marginTop: 10,
    width: width - 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    backgroundColor: colors.WHITE5,
    justifyContent: 'space-between',
  },
  txtTime: {
    fontSize: 12,
  },
  txtLocation: {
    fontSize: 12,
    marginBottom: 8,
    fontWeight: 'bold',
    color: colors.GREEN2,
  },
  accidentInforView: {
    paddingHorizontal: 16,
    paddingVertical: 13,
  },
  resolveAccidentInforView: {
    paddingHorizontal: 16,
  },
  resolveAccidentView: {
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
  },
  btnReceivingRecords: {
    flex: 1,
    padding: 10,
    minHeight: 50,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  linearBtnView: {
    flex: 1,
    borderRadius: 30,
    marginHorizontal: 10,
    backgroundColor: colors.WHITE,
  },
  btnRequestEnd: {
    flex: 1,
    padding: 10,
    minHeight: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtBtnReceivingRecords: {
    fontWeight: 'bold',
    color: colors.WHITE,
  },
  txtBtnRequestEnd: {
    fontWeight: 'bold',
    color: colors.BLACK,
  },
  footerView: {
    left: 0,
    flex: 1,
    right: 0,
    bottom: 0,
    width: width,
    paddingVertical: 10,
    position: 'absolute',
    flexDirection: 'row',
    backgroundColor: colors.WHITE,
  },
  btnsFilterDocumentView: {
    paddingBottom: 8,
    flexDirection: 'row',
    borderBottomWidth: 1,
    marginHorizontal: 10,
    borderBottomColor: colors.GRAY,
  },
  btnFilterDocument: {
    padding: 8,
    marginRight: 8,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: colors.GRAY,
  },
  btnFilterDocumentActive: {
    backgroundColor: colors.PRIMARY,
  },
  txtBtnFilterDocumentActive: {
    fontWeight: 'bold',
    color: colors.WHITE,
  },

  txtBtnFilterDocument: {
    color: colors.PRIMARY,
  },
  imageDocument: {
    borderRadius: 10,
    width: width / 4,
    height: width / 4,
    marginVertical: 15,
    marginHorizontal: 15,
  },
  pdfComponent: {
    flex: 1,
    width: width,
    height: height,
  },
  headerTitle: {
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  headerSubTitle: {
    fontSize: 14,
    marginLeft: 20,
    marginBottom: 5,
  },
  shadowStyle: {
    shadowOpacity: 0.35,
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderRadius: 35,
    alignSelf: 'center',
  },
  modalView: {
    width: width,
    backgroundColor: colors.WHITE,
  },
  scrollContentView: {
    height: height * 3,
  },
  modalTitle: {
    fontSize: 18,
    marginVertical: 10,
    textAlign: 'center',
  },
  closeView: {
    top: 15,
    right: 15,
    position: 'absolute',
  },
  modal: {
    flex: 1,
    margin: 0,
    height: height,
    backgroundColor: colors.WHITE,
    paddingTop: Platform.OS == 'ios' ? 60 : 10,
  },
  modalTitleView: {
    height: 50,
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  headerTitleView: {
    flexDirection: 'row',
    paddingVertical: 10,
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
  },
  headerCSYT: {
    fontWeight: '700',
    marginVertical: 10,
    marginHorizontal: 16,
    color: colors.BLACK_03,
  },
  contentView: {
    marginVertical: 5,
    marginHorizontal: 16,
  },
  phone: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
  },
  laySoHoSoView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
