import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {Icon, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {useState} from 'react';
import {Alert, Linking, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';
import styles from './ProfileInformationStyles';
import {spacing} from '@app/theme';
const profileHeaderTitle = [
  'Thông tin chung', //0
  'Khách hàng', //1
  'Thông tin liên hệ', //2
  'Thông tin cơ sở y tế', //3
  'Xem hình ảnh hồ sơ,tài liệu', //4
  'Chuyển người xử lý', //5
  'Thông tin quá trình xử lý', //6
  'Tải ảnh lên', //7
  'Lị<PERSON> sử chi trả tiền Bảo hiểm', //8
  '<PERSON><PERSON> đơn chứng từ/người thụ hưởng', //9
  'Thông tin tỷ giá', //10
];
const iconHeader = ['info', 'user', 'list-alt', 'plus-circle', 'file-picture-o', 'random', 'list-ol', 'photo', 'history', 'file', 'balance-scale', 'tasks', 'money', 'info-circle', 'eercast'];
const contentLabel = ['Ngày mở HSBT', 'Ngày thông báo', 'Nguồn thông báo', 'Sản phẩm', 'Điện thoại', 'Gói bảo hiểm', 'Trạng thái hồ sơ', 'Số tiền yêu cầu', 'Số tiền duyệt', 'Ước tổn tất'];
function ProfileInformation(props) {
  const {profileInfo, handleAction, profileData, laySoHoSo} = props;

  const [toggleModal, setToggleModal] = useState(false);

  const onPressHeader = (headerTitle, modalData) => {
    if (headerTitle === profileHeaderTitle[3]) {
      setToggleModal(true);
    }
    if (headerTitle === profileHeaderTitle[4]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.XEM_CHI_TIET_TAI_LIEU_HSBL, {profileInfo: profileInfo});
    }
    if (headerTitle === profileHeaderTitle[5]) {
      handleAction && handleAction();
    }
    if (headerTitle === profileHeaderTitle[6]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.THONG_TIN_QUA_TRINH_XL, {profileInfo: profileInfo});
    }
    if (headerTitle === profileHeaderTitle[7]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.UPLOAD_HINH_ANH, {profileData: profileInfo});
    }
    if (headerTitle === profileHeaderTitle[8]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.LICH_SU_CHI_TRA_TIEN_BH, {profileInfo: profileInfo});
    }
    if (headerTitle === profileHeaderTitle[9]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.DS_CHUNG_TU_THU_HUONG_CON_NG, {profileData});
    }
    if (headerTitle === profileHeaderTitle[10]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.TY_GIA_NGUYEN_TE, {profileInfo: profileInfo, nguon: 'BL'});
    }
  };

  const onPressEditInfo = (page) => {
    if (profileInfo.so_hs == '') {
      Alert.alert('Không sửa xóa hồ sơ đã mở số hồ sơ!');
    } else {
      NavigationUtil.push(SCREEN_ROUTER_APP.BAO_LANH_VIEN_PHI, {page: page, info: profileInfo, title: 'Sửa hồ sơ'});
    }
  };
  /* RENDER */
  // RENDER header các đầu mục
  const renderProfileInformationHeader = (title, data) => {
    let indexIcon = profileHeaderTitle.findIndex((item) => item === title);
    let dataLength = data ? '(' + data.length + ')' : '';
    const showArrowRight =
      title === profileHeaderTitle[4] ||
      title === profileHeaderTitle[5] ||
      title === profileHeaderTitle[6] ||
      title === profileHeaderTitle[7] ||
      title === profileHeaderTitle[8] ||
      title === profileHeaderTitle[9] ||
      title === profileHeaderTitle[10];
    return (
      <View>
        <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => onPressHeader(title, data)}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name={iconHeader[indexIcon]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>{title + ' ' + dataLength}</Text>
            </View>
          </View>
          {title === profileHeaderTitle[1] && (
            <TouchableOpacity onPress={() => onPressEditInfo(0)}>
              <Icon.Feather name="edit" size={16} color={colors.BLACK} style={styles.iconBtnTopRightView} />
            </TouchableOpacity>
          )}
          {title === profileHeaderTitle[2] && (
            <TouchableOpacity onPress={() => onPressEditInfo(2)}>
              <Icon.Feather name="edit" size={16} color={colors.BLACK} style={styles.iconBtnTopRightView} />
            </TouchableOpacity>
          )}
          {title === profileHeaderTitle[3] && (
            <TouchableOpacity onPress={() => onPressEditInfo(1)}>
              <Icon.Feather name="edit" size={16} color={colors.BLACK} style={styles.iconBtnTopRightView} />
            </TouchableOpacity>
          )}
          {showArrowRight && <Icon.SimpleLineIcons name={'arrow-right'} size={15} style={styles.iconBtnTopRightView} />}
        </TouchableOpacity>
      </View>
    );
  };
  const renderThongTinChungChildren = (title, data, containerStyle, subValue) => {
    return (
      <View style={[styles.inforView, containerStyle]}>
        <Text style={styles.txtTitle} children={title} />
        {typeof data === 'number' ? (
          <NumericFormat
            value={data}
            displayType={'text'}
            thousandSeparator={true}
            renderText={(value) => <Text style={styles.txtDetail} selectable children={value + (subValue ? ' (' + subValue + '%)' : '')} />}
          />
        ) : (
          <Text style={[styles.txtDetail, {color: title === contentLabel[6] ? colors.PRIMARY : colors.GRAY6}]} selectable children={data} />
        )}
        {title === 'Số hồ sơ' && (!data || data === ' ') && (
          <TouchableOpacity style={styles.laySoHoSoView} onPress={laySoHoSo}>
            <Icon.FontAwesome name="ticket" color={colors.PRIMARY} size={20} style={{marginRight: spacing.smaller}} />
            <Text style={[styles.txtDetail, {color: colors.PRIMARY}]} children="Lấy số hồ sơ" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // render khách hàng
  const renderKhachHang = () => {
    return (
      <>
        <View style={styles.row}>{renderThongTinChungChildren('Tên khách hàng', profileInfo?.ten_khach, {borderTopLeftRadius: 20, backgroundColor: colors.WHITE})}</View>
        <View style={styles.row}>
          {renderThongTinChungChildren('Số HĐBH', profileInfo?.so_hd)}
          {renderThongTinChungChildren('Số GCN	', profileInfo?.gcn)}
        </View>
        <View style={styles.row}>
          {renderThongTinChungChildren('Đơn vị cấp', profileInfo?.ten_dvi_cap)}
          {renderThongTinChungChildren('Cán bộ cấp đơn', profileInfo?.nsd_cap)}
        </View>
        <View style={styles.row}>
          {renderThongTinChungChildren('Tên người được hưởng', profileInfo?.ten)}
          {renderThongTinChungChildren('Ngày sinh', profileInfo?.ngay_sinh)}
        </View>
        <View style={styles.row}>
          {renderThongTinChungChildren('Số CMND/CCCD', profileInfo?.so_cmt)}
          {profileInfo?.dien_thoai && (
            <View style={styles.inforView}>
              <TouchableOpacity onPress={() => Linking.openURL(`tel: ${profileInfo?.dien_thoai}}`)}>
                <Text style={styles.txtTitle}>Điện thoại</Text>
                <View style={styles.contactView}>
                  <Icon.Entypo name="old-phone" size={16} color={colors.PRIMARY} style={styles.icon} />
                  <Text style={styles.txtDetail}>{profileInfo?.dien_thoai}</Text>
                </View>
              </TouchableOpacity>
            </View>
          )}
        </View>
        {renderThongTinChungChildren('Hiệu lực BH', profileInfo?.hieu_luc)}
        {renderThongTinChungChildren('Thanh toán phí', profileInfo?.thanh_toan_phi_mobile)}
        {renderThongTinChungChildren('Đơn vị công tác', profileInfo?.dvi_ctac_ndbh)}
        {renderThongTinChungChildren('Chức vụ công tác', profileInfo?.chuc_vu_ndbh)}
      </>
    );
  };

  //render thông tin chung
  const renderThongTinChung = () => {
    return (
      <View>
        <View style={styles.row}>{renderThongTinChungChildren('Số hồ sơ', profileInfo?.so_hs)}</View>
        <View style={styles.row}>
          {renderThongTinChungChildren(contentLabel[0], profileInfo?.ngay_ht)}
          {renderThongTinChungChildren(contentLabel[1], profileInfo?.ngay_tb)}
        </View>

        <View style={styles.row}>
          {renderThongTinChungChildren(contentLabel[2], profileInfo?.nguon_tb_ten || '')}
          {renderThongTinChungChildren(contentLabel[3], profileInfo?.ten_sp)}
        </View>
        <View style={styles.row}>{renderThongTinChungChildren(contentLabel[5], profileInfo?.ten_goi_bh || '')}</View>
        {renderThongTinChungChildren(contentLabel[6], profileInfo?.trang_thai)}
        <View style={styles.row}>
          {renderThongTinChungChildren(contentLabel[7], profileInfo?.so_tien_yc)}
          {renderThongTinChungChildren(contentLabel[8], profileInfo?.so_tien_duyet)}
          {renderThongTinChungChildren(contentLabel[9], profileInfo?.uoc_ton_that)}
        </View>
      </View>
    );
  };

  // render thông tin người liên hệ
  const renderTTNguoiLienHe = () => {
    return (
      <>
        <View style={styles.row}>{renderThongTinChungChildren('Người liên hệ', profileInfo?.nguoi_lh, {borderTopLeftRadius: 20, backgroundColor: colors.WHITE})}</View>
        {true && (
          <View style={styles.inforView}>
            <TouchableOpacity onPress={() => Linking.openURL(`tel: ${profileInfo?.dthoai_nguoi_lh}`)}>
              <Text style={styles.txtTitle}>Số điện thoại</Text>
              <View style={styles.contactView}>
                <Icon.Entypo name="old-phone" size={16} color={colors.PRIMARY} style={styles.icon} />
                <Text style={styles.txtDetail}>{profileInfo?.dthoai_nguoi_lh}</Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
        {true && (
          <View style={styles.inforView}>
            <TouchableOpacity onPress={() => Linking.openURL(`email: ${profileInfo?.email_nguoi_lh}`)}>
              <Text style={styles.txtTitle}>Email</Text>
              <View style={styles.contactView}>
                <Icon.Entypo name="mail" size={16} color={colors.PRIMARY} style={styles.icon} />
                <Text style={styles.txtDetail}>{profileInfo?.email_nguoi_lh}</Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </>
    );
  };

  //render  thông tin hồ sơ
  const renderProfileInformation = () => {
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View>
          {renderProfileInformationHeader(profileHeaderTitle[0])}
          {renderThongTinChung()}
        </View>

        {/* khách hàng */}
        <View>
          {renderProfileInformationHeader(profileHeaderTitle[1])}
          {renderKhachHang()}
        </View>

        {/* thông tin người liên hệ */}
        <View>
          {renderProfileInformationHeader(profileHeaderTitle[2])}
          {renderTTNguoiLienHe()}
        </View>
        <View>
          {renderProfileInformationHeader(profileHeaderTitle[3])}
          <RenderTTCoSoYte profileInfo={profileInfo} />
        </View>
        {renderProfileInformationHeader(profileHeaderTitle[9])}
        {renderProfileInformationHeader(profileHeaderTitle[4])}
        {renderProfileInformationHeader(profileHeaderTitle[5])}
        {renderProfileInformationHeader(profileHeaderTitle[6])}
        {renderProfileInformationHeader(profileHeaderTitle[7])}
        <View>{renderProfileInformationHeader(profileHeaderTitle[8])}</View>
        <View marginBottom={20}>{renderProfileInformationHeader(profileHeaderTitle[10])}</View>
      </View>
    );
  };

  return <View>{renderProfileInformation()}</View>;
}

const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
});

//render thông tin cơ sở ý tế
const RenderTTCoSoYte = (props) => {
  const {profileInfo} = props;
  return (
    <View style={{flex: 1}}>
      <View>
        <View style={styles.contentView}>
          <Text children={'Bệnh viện'} style={styles.txtTitle} />
          <Text style={styles.txtDetail}>{profileInfo?.benh_vien_ten}</Text>
        </View>
        <View style={styles.contentView}>
          <Text children={'Nhà thuốc'} style={styles.txtTitle} />
          <Text style={styles.txtDetail}>{profileInfo?.nha_thuoc_ten}</Text>
        </View>
      </View>
      <View>
        <Text style={styles.headerCSYT}>Người liên hệ của CSYT</Text>
        <View style={styles.contentView}>
          <Text children={'Họ tên'} style={styles.txtTitle} />
          <Text children={profileInfo.nguoi_tb} style={styles.txtDetail} />
        </View>
        <View style={styles.contentView}>
          <TouchableOpacity onPress={() => Linking.openURL(`tel: ${profileInfo?.dthoai_nguoi_tb}`)}>
            <Text style={styles.txtTitle}>Điện thoại</Text>
            <View style={styles.phone}>
              <Icon.Entypo name="old-phone" size={16} color={colors.PRIMARY} style={styles.icon} />
              <Text style={styles.txtDetail} children={profileInfo?.dthoai_nguoi_tb}></Text>
            </View>
          </TouchableOpacity>
        </View>
        <View style={styles.contentView}>
          <Text children={'Email'} style={styles.txtTitle} />
          <Text children={profileInfo?.email_nguoi_tb} style={styles.txtDetail} />
        </View>
      </View>
    </View>
  );
};

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ProfileInformation);
