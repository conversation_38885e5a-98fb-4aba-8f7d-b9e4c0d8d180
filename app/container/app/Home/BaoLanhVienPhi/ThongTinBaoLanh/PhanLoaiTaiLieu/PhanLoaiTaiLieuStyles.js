import {colors} from '@app/commons/Theme';

const {StyleSheet, Dimensions} = require('react-native');

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  imageDocument: {
    width: width / 4,
    height: width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 1,
  },
  switch: {
    // flex: 1,
    marginVertical: 10,
    marginHorizontal: 10,
    alignItems: 'center',
    flexDirection: 'row',
  },
  checkboxImgView: {
    right: 15,
    bottom: 15,
    position: 'absolute',
    borderTopLeftRadius: 5,
    backgroundColor: colors.WHITE,
  },
  container: {
    flex: 1,
  },
  footerView: {
    flex: 1,
    left: 0,
    right: 0,
    bottom: 20,
    width: width,
    paddingVertical: 10,
    position: 'absolute',
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    padding: 5,
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY2,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
  actButtonGr: {
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  actButton: {
    borderRadius: 5,
    paddingVertical: 8,
    marginHorizontal: 8,
    paddingHorizontal: 30,
    backgroundColor: colors.PRIMARY,
  },
  actButtonTxt: {
    fontWeight: '600',
    color: colors.WHITE,
  },
  buttonChonBenhVien: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 10,
    paddingLeft: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
    minHeight: 40,
    paddingRight: 15,
    textAlignVertical: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  titleLabel: {
    fontWeight: '600',
    marginBottom: 5,
  },
});
