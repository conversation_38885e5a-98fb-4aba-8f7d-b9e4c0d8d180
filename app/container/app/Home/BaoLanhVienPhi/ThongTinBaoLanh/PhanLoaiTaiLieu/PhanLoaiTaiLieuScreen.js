import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, TouchableOpacity, View} from 'react-native';
import ModalDanhSachHangMuc from './ModalDanhSachHangMuc';
import styles from './PhanLoaiTaiLieuStyles';

const PhanLoaiTaiLieuScreenComponent = (props) => {
  console.log('PhanLoaiTaiLieuScreenComponent');
  const {route} = props;
  const {action} = route.params;
  const {imagesClassify, profileData} = route?.params;
  const [dataHangMuc, setDataHangMuc] = useState([]);
  const [isvisible, setIsvisible] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [tenHangMuc, setTenHangMuc] = useState();
  const [itemHangMuc, setItemHangMuc] = useState({});

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    let params = {
      ma_doi_tac: profileData?.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DANH_SACH_HANG_MUC_ANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      else {
        const filter = response.data_info.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU);
        setDataHangMuc(filter);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onSave = async () => {
    if (imagesClassify?.length > 0) {
      setIsSubmiting(true);
      let arrBt = imagesClassify.map((item) => item.bt);
      let params = {
        pm: action === 'TTBT' ? 'BT' : 'BL',
        loai: itemHangMuc?.loai,
        so_id: profileData?.so_id,
        hang_muc: itemHangMuc?.ma,
        ma_doi_tac: profileData?.ma_doi_tac,
        bt: arrBt,
      };
      try {
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.PHAN_LOAI_TAI_LIEU_THEO_HANG_MUC, params);
        setIsSubmiting(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        FlashMessageHelper.showFlashMessage('Thông báo', 'Phân loại hạng mục thành công', 'success');
        NavigationUtil.pop();
      } catch (error) {
        setIsSubmiting(false);
        Alert.alert('Thông báo', error.message);
      }
      return;
    }
  };

  const iconSize = 20;

  const renderContent = () => {
    return (
      <View marginTop={16} marginHorizontal={10}>
        <View marginBottom={10}>
          <Text style={styles.titleLabel}>
            Nhóm tài liệu
            <Text children="(*)" style={{color: colors.RED1}} />
          </Text>
          <TouchableOpacity style={[styles.buttonChonBenhVien, {backgroundColor: colors.GRAY2}]}>
            <Text style={{flex: 1}}>Giấy tờ tài liệu</Text>
          </TouchableOpacity>
        </View>
        <View marginBottom={10}>
          <Text style={styles.titleLabel}>
            Hạng mục tài liệu
            <Text children="(*)" style={{color: colors.RED1}} />
          </Text>
          <TouchableOpacity style={styles.buttonChonBenhVien} onPress={() => setIsvisible(true)}>
            <Text style={{flex: 1}}>{itemHangMuc.ten || 'Chọn hạng mục'}</Text>
            {!itemHangMuc.ten ? (
              <Icon.MaterialIcons name="keyboard-arrow-down" size={iconSize} style={styles.icon} color={colors.BLUE1} />
            ) : (
              <TouchableOpacity onPress={() => setItemHangMuc({})}>
                <Icon.MaterialIcons name="close" size={iconSize} style={styles.icon} color={colors.BLUE1} />
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.actButtonGr}>
          <TouchableOpacity style={[styles.actButton, {backgroundColor: colors.GRAY2}]} onPress={() => NavigationUtil.pop()}>
            <Text style={[styles.actButtonTxt, {color: colors.BLACK_03}]}>Huỷ</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actButton} onPress={onSave}>
            <Text style={styles.actButtonTxt}>Lưu</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={isSubmiting}
      headerTitle={'Phân loại tài liệu'}
      renderView={
        <SafeAreaView style={styles.container}>
          {renderContent()}
          <ModalDanhSachHangMuc setTenHangMuc={setTenHangMuc} setValue={setItemHangMuc} data={dataHangMuc} isVisible={isvisible} onBackPress={() => setIsvisible(false)} />
        </SafeAreaView>
      }
    />
  );
};

export const PhanLoaiTaiLieuScreen = memo(PhanLoaiTaiLieuScreenComponent, isEqual);
