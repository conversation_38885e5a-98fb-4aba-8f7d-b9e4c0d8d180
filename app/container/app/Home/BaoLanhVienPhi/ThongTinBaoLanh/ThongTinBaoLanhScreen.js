import {colors} from '@app/commons/Theme';
import {CustomTabBar, Empty} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@component';
import {LOAI_TRINH_BL, MA_MAU_IN, SCREEN_ROUTER_APP, TYPE_INIT_FORM} from '@constant';
import {useRoute} from '@react-navigation/native';
import React, {createRef, memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Al<PERSON>, FlatList, RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View} from 'react-native';
import ActionSheet from 'react-native-actionsheet';
import LinearGradient from 'react-native-linear-gradient';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';
import {ModalNhapNoiDungHuyHS, ModalShowNote} from './Components';
import ProfileInformation from './ProfileInformation/ProfileInformation';
import styles from './ThongTinBaoLanhStyles';

let timer;
const ThongTinBaoLanhVienPhiScreenComponent = (props) => {
  console.log('ThongTinBaoLanhVienPhiScreen');
  const {navigation} = props;
  const route = useRoute();
  const {id, ma_doi_tac} = route.params;

  let tabViewRef = useRef();
  let scrollViewRef = useRef();
  // let refModalChuyenNguoiXuLy = useRef();
  let refModalNhapNoiDungHuyHs = useRef();

  const [tabIndex, setTabIndex] = useState(0);

  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileInfo, setProfileInfo] = useState({});
  const [profileData, setProfileData] = useState({});
  const [dataQuyenLoiBL, setDataQuyenLoiBL] = useState([]);
  const [dataQuyenLoiGoc, setDataQuyenLoiGoc] = useState([]);
  const [selectedItemIndex, setSelectedItemIndex] = useState(0);
  const [dataCacLanBaoLanh, setDataCacLanBaoLanh] = useState([]);
  const [baseDataQuyenLoiBL, setBaseDataQuyenLoiBL] = useState([]);
  const [note, setNote] = useState('');
  const [idHS, setIdHS] = useState(id);
  const [ghiChu, setGhiChu] = useState('');
  const [typeBtn, setTypeBtn] = useState('');
  const [noiDungHuy, setNoiDungHuy] = useState('');
  const [ndTuChoiBL, setNdTuChoiBL] = useState('');

  const [thongTinLanBaoLanh, setThongTinLanBaoLanh] = useState({});
  const [thongTinChiPhi, setThongTinChiPhi] = useState([]);
  const [thongTinChiPhiTheoLan, setThongTinChiPhiTheoLan] = useState([]);

  const [isSubmiting, setIsSubmiting] = useState(false);
  const enableBtnCopy = thongTinLanBaoLanh?.trang_thai?.toUpperCase() === 'D' || thongTinLanBaoLanh?.trang_thai?.toUpperCase() === 'T';

  let actionSheetRef = createRef();
  let actionSheetDuyetBl = createRef();
  let refModalShowNote = useRef(null);

  const [count, setCount] = useState(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  // useEffect(() => {
  //   setDataQuyenLoiBL(dataQuyenLoi);
  // }, [dataCacLanBaoLanh]);

  const getData = async () => {
    try {
      setDialogLoading(true);
      let params = {
        so_id: idHS,
        ma_doi_tac: ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_CHI_TIET_HS_BAO_LANH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      else {
        setProfileInfo(response.data_info.ho_so);
        setProfileData(response.data_info);
        setIdHS(response.data_info.ho_so?.so_id);
      }
      if (response.data_info.lan_bao_lanh?.length > 0) {
        let data = response.data_info.lan_bao_lanh;
        setDataCacLanBaoLanh(data);
        setThongTinLanBaoLanh(data[0]);
        setCount(data[0].lan_blvp_hien_thi);
      }
      if (response.data_info.qloi_goc?.length > 0) {
        let data = response.data_info.qloi_goc;
        setDataQuyenLoiGoc(data);
      }
      if (response.data_info.qloi_bao_lanh?.length > 0) {
        let data = response.data_info.qloi_bao_lanh;
        setBaseDataQuyenLoiBL(data);
      }
      if (response.data_info.chi_phi?.length > 0) {
        let data = response.data_info.chi_phi;
        setThongTinChiPhi(data);
        let filter = data.filter((item) => item.lan === response.data_info.lan_bao_lanh[0].lan);
        setThongTinChiPhiTheoLan(filter);
      }
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  useEffect(() => {
    if (baseDataQuyenLoiBL.length > 0 && dataCacLanBaoLanh.length > 0) {
      const dataQuyenLoi = baseDataQuyenLoiBL.filter((e) => {
        return e.lan === dataCacLanBaoLanh[0].lan;
      });
      setDataQuyenLoiBL(dataQuyenLoi);
    }
  }, [baseDataQuyenLoiBL]);

  useEffect(() => {
    if (thongTinChiPhi.length > 0) {
      let arr = [];
      arr.push(thongTinChiPhi[0]);
      setThongTinChiPhiTheoLan(arr);
    }
  }, [thongTinChiPhi]);

  const onPressThongTinLan = (item, index) => {
    setCount(item.lan_blvp_hien_thi);
    setSelectedItemIndex(index);
    const dataQuyenLoi = baseDataQuyenLoiBL.filter((e) => e.lan === item.lan);
    setDataQuyenLoiBL(dataQuyenLoi);
    const chiPhi = thongTinChiPhi.filter((it) => it.lan === item.lan);
    setThongTinChiPhiTheoLan(chiPhi);
    setThongTinLanBaoLanh(item);
  };

  const onPressShowNote = (item) => {
    refModalShowNote.current.show(true);
    setNote(item.ghi_chu);
  };

  const handleAction = (type) => {
    if (type === 'go_huy') {
      return Alert.alert('Thông báo', 'Bạn có chắc chắn muốn gỡ huỷ hồ sơ', [
        {
          text: 'Huỷ',
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onConfirmGoHuyHoSo()},
      ]);
    }
    if (type === 'huy_chuyen_tt') {
      return Alert.alert('Thông báo', 'Bạn có chắc muốn huỷ chuyển thanh toán', [
        {
          text: 'Huỷ',
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onConfirmHuyChuyenTT()},
      ]);
    }
    if (type === 'chuyen_tt') {
      return Alert.alert('Thông báo', 'Bạn có chắc muốn chuyển thanh toán', [
        {
          text: 'Huỷ',
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onConfirmChuyenTT()},
      ]);
    }
    if (type === 'duyet_bl') {
      return Alert.alert('Thông báo', 'Bạn có chắc muốn duyệt lần bảo lãnh này không?', [
        {
          text: 'Huỷ',
        },
        {text: 'Đồng ý', onPress: () => onConfirmSubmitDuyetBL()},
      ]);
    }
    if (type === 'huy_duyet_tu_choi') {
      return Alert.alert('Thông báo', 'Bạn có chắc huỷ từ chối không?', [
        {
          text: 'Huỷ',
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onConfirmHuyTuChoiBL()},
      ]);
    }
    if (type === 'chuyen_nxl') {
      // return refModalChuyenNguoiXuLy.current.show();
      return NavigationUtil.push(SCREEN_ROUTER_APP.CHUYEN_NGUOI_XY_LY_HO_SO_BAO_LANH_CON_NUGOI, {profileData});
    }
    if (type === 'huy_hs') {
      setTypeBtn(type);
      return refModalNhapNoiDungHuyHs.current.show();
    }
    if (type === 'huy_duyet_bl') {
      return Alert.alert('Thông báo', 'Bạn có chắc muốn huỷ duyệt lần bảo lãnh này không?', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onConfirmSubmitHuyDuyetBL()},
      ]);
    }
    if (type === 'trinh_bl' || type === 'huy_trinh' || type === 'trinh_tc') {
      let maMauIn = type === 'trinh_bl' ? MA_MAU_IN.TRINH_DUYET_BL : type === 'trinh_tc' ? MA_MAU_IN.TRINH_TC : null;
      let loaiTrinh = type === 'trinh_bl' ? LOAI_TRINH_BL.TRINH_DUYET_BL : type === 'trinh_tc' ? LOAI_TRINH_BL.TRINH_TC : null;
      NavigationUtil.push(SCREEN_ROUTER_APP.TRINH_BAO_LANH, {profileInfo: profileInfo, type: type, maMauIn: maMauIn, loaiTrinh: loaiTrinh, idLan: thongTinLanBaoLanh.lan});
    }
    if (type === 'tu_choi_bl') {
      setTypeBtn(type);
      refModalNhapNoiDungHuyHs.current.show();
    }
  };

  const onNhanHoSo = () => {
    return Alert.alert('Thông báo', 'Bạn có chắc muốn nhận hồ sơ bảo lãnh này không?', [
      {
        text: 'Huỷ',
        style: 'cancel',
      },
      {text: 'Đồng ý', onPress: () => onConfirmNhanHoSoBaoLanh()},
    ]);
  };

  //Chuyển người xử lý
  const onPressSaveNguoiXuLy = (ma_cn, ma_cb) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn chuyển người khác xử lý hồ sơ này?', [
      {
        text: 'Huỷ',
        onPress: () => console.log('Cancel Pressed'),
        style: 'cancel',
      },
      {text: 'Đồng ý', onPress: () => onConfirmSubmitChuyenNXL(ma_cn, ma_cb)},
    ]);
  };
  const onConfirmSubmitChuyenNXL = async (ma_cn, ma_cb) => {
    let params = {
      so_id: idHS,
      ma_doi_tac: ma_doi_tac,
      pm: 'BAO_LANH',
      ma_chi_nhanh_moi: ma_cn,
      nsd_moi: ma_cb,
    };
    setIsSubmiting(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHUYEN_NGUOI_XU_LY, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển người xử lý thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // Huỷ duyệt hs
  const onConfirmSubmitHuyDuyetBL = async () => {
    let params = {
      so_id: idHS,
    };
    setIsSubmiting(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_DUYET_HS_BAO_LANH, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ duyệt lần bảo lãnh thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  // Từ chối bảo lãnh
  const onConfirmTuChoiBL = async () => {
    let params = {
      so_id: idHS,
      ma_doi_tac: ma_doi_tac,
      nd_tchoi: ndTuChoiBL,
    };
    setIsSubmiting(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.TU_CHOI_BAO_LANH, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Từ chối bảo lãnh thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      setIsSubmiting(false);
    }
    return;
  };
  // Huỷ từ chối bảo lãnh
  const onConfirmHuyTuChoiBL = async () => {
    let params = {
      so_id: idHS,
      ma_doi_tac: ma_doi_tac,
    };
    setIsSubmiting(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_TU_CHOI_BAO_LANH, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ từ chối bảo lãnh thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      setIsSubmiting(false);
    }
    return;
  };
  // Duyệt hs
  const onConfirmSubmitDuyetBL = async () => {
    let params = {
      so_id: idHS,
    };
    setIsSubmiting(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.DUYET_HS_BAO_LANH, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Duyệt lần bảo lãnh thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      setIsSubmiting(false);
    }
    return;
  };

  // Huỷ chuyển thanh toán
  const onConfirmHuyChuyenTT = async () => {
    let params = {
      so_id: idHS,
      ma_doi_tac: profileInfo?.ma_doi_tac,
      nguon: profileInfo?.nguon,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_CHUYEN_TT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ chuyển thanh toán thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  //

  const onConfirmNhanHoSoBaoLanh = async () => {
    let params = {
      so_id: idHS,
    };
    setIsSubmiting(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.NHAN_HS_BLVP, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Nhận hồ sơ bảo lãnh thành công!', 'success');
      getData();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // Huỷ hồ sơ
  const onPressSaveNoiDungHuy = () => {
    if (typeBtn === 'huy_hs') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn muốn huỷ hồ sơ', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onConfirmHuyHoSo()},
      ]);
    }
    if (typeBtn === 'tu_choi_bl') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn muốn từ chối bảo lãnh', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onConfirmTuChoiBL()},
      ]);
    }
  };

  const onConfirmHuyHoSo = () => {
    refModalNhapNoiDungHuyHs.current.hide();
    timer = setTimeout(async () => {
      let params = {
        so_id: idHS,
        ma_doi_tac: profileInfo?.ma_doi_tac,
        nd_huy: noiDungHuy,
      };
      try {
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_HO_SO_BAO_LANH, params);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ hồ sơ bảo lãnh thành công!', 'success');
        NavigationUtil.pop();
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
      return;
    }, 500);
  };
  //

  // gỡ hồ sơ
  const onConfirmGoHuyHoSo = async () => {
    let params = {
      so_id: idHS,
      ma_doi_tac: profileInfo?.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GO_HUY_HO_SO_BAO_LANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Gỡ huỷ hồ sơ bảo lãnh thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  //

  // Chuyển thanh toán
  const onConfirmChuyenTT = async () => {
    let params = {
      so_id: idHS,
      ma_doi_tac: profileInfo?.ma_doi_tac,
      nguon: profileInfo?.nguon,
    };

    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHUYEN_TT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển thanh toán thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };
  //

  const onChangeText = (text) => {
    setNoiDungHuy(text);
    setNdTuChoiBL(text);
  };

  const onCopyLanBaoLanh = async () => {
    let params = {
      so_id: idHS,
    };

    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.COPY_LAN_BAO_LANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Copy lần bảo lãnh thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onActionPress = (i) => {
    if (i === 0) {
      handleAction('trinh_bl');
    }
    if (i === 1) {
      handleAction('trinh_tc');
    }
  };
  const onActionDuyetPress = (i) => {
    if (i === 0) {
      handleAction('duyet_bl');
    }
    if (i === 1) {
      handleAction('tu_choi_bl');
    }
  };

  const onPressSuaQuyenLoi = (item) => {
    if (thongTinLanBaoLanh?.trang_thai?.toUpperCase() === 'C') {
      NavigationUtil.push(SCREEN_ROUTER_APP.BLVP_TAO_MOI_LAN_BAO_LANH, {
        thongTinQuyenLoi: item,
        chiTietHoSo: profileInfo,
        type: TYPE_INIT_FORM.EDIT,
        thongTinLan: thongTinLanBaoLanh,
        dataQuyenLoiGoc: dataQuyenLoiGoc,
        thongTinChiPhi: thongTinChiPhiTheoLan,
      });
    }
  };

  const laySoHoSo = async () => {
    let params = {
      so_id: profileData.ho_so.so_id,
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
    };
    try {
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_SO_HO_SO_CON_NGUOI, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      else {
        let profileDataTmp = profileData;
        profileDataTmp.ho_so.so_hs = response.out_value.so_hs;
        setProfileData(cloneObject(profileDataTmp));
        Alert.alert('Thông báo', 'Lấy số hồ sơ thành công ' + response.out_value.so_hs);
      }
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  // RENDER
  const validate1 = [profileInfo?.ngay_huy === 30000101, profileInfo?.ngay_huy === null, profileInfo?.ngay_huy?.toString().trim() === ''];
  const validate2 = [profileInfo?.ngay_dong_hs === 30000101, profileInfo?.ngay_dong_hs === null, profileInfo?.ngay_dong_hs?.toString().trim() === ''];
  const validate3 = [profileInfo?.ngay_chuyen_tt === null, profileInfo?.ngay_chuyen_tt === 30000101, profileInfo?.ngay_chuyen_tt?.toString().trim() === ''];
  const validate4 = [profileInfo?.ngay_tchoi === null, profileInfo?.ngay_tchoi === 30000101, profileInfo?.ngay_tchoi?.toString().trim() === ''];
  const validate5 = [profileInfo?.ngay_duyet_bl === null, profileInfo?.ngay_duyet_bl === 30000101, profileInfo?.ngay_duyet_bl?.toString().trim() === ''];
  const validate6 = [profileInfo?.ngay_trinh_tchoi === null, profileInfo?.ngay_trinh_tchoi === 30000101, profileInfo?.ngay_trinh_tchoi?.toString().trim() === ''];
  const validate7 = [profileInfo?.ngay_trinh_bl === null, profileInfo?.ngay_trinh_bl === 30000101, profileInfo?.ngay_trinh_bl?.toString().trim() === ''];

  const switchButton = () => {
    let ngayNhanHoSo = false;
    if (profileInfo?.ma_trang_thai === 'HSBT_NG_BV_CHUYEN_BL') {
      ngayNhanHoSo = true;
    }
    if (ngayNhanHoSo) {
      return (
        <View style={styles.doubleBtn}>
          {/* <ButtonLinear loading={isSubmiting} title={'Huỷ trình'} onPress={() => handleAction('huy_trinh')} linearStyle={styles.footerBtn} /> */}
          <ButtonLinear loading={isSubmiting} title={'Nhận hồ sơ'} linearStyle={styles.footerBtn} onPress={() => onNhanHoSo()} />
        </View>
      );
    }
    if (validate1.indexOf(true) === -1) {
      if (tabIndex === 1) return null;
      return (
        <View style={styles.doubleBtn}>
          <ButtonLinear loading={isSubmiting} title={'Gỡ huỷ hồ sơ'} onPress={() => handleAction('go_huy')} linearStyle={styles.footerBtn} />
        </View>
      );
    } else if (validate2.indexOf(true) === -1) {
      if (tabIndex === 0) return null;
      return null;
    } else if (validate3.indexOf(true) === -1) {
      if (tabIndex === 0) return null;
      return (
        <View style={styles.doubleBtn}>
          <ButtonLinear loading={isSubmiting} title={'Huỷ chuyển thanh toán'} onPress={() => handleAction('huy_chuyen_tt')} linearStyle={styles.footerBtn} />
        </View>
      );
    } else if (validate4.indexOf(true) === -1) {
      if (tabIndex === 0) return null;
      return (
        <View style={styles.doubleBtn}>
          <ButtonLinear loading={isSubmiting} title={'Huỷ từ chối bảo lãnh'} onPress={() => handleAction('huy_duyet_tu_choi')} linearStyle={styles.footerBtn} />
        </View>
      );
    } else if (validate5.indexOf(true) === -1) {
      if (tabIndex === 0) return null;
      return (
        <View style={styles.doubleBtn}>
          <ButtonLinear loading={isSubmiting} title={'Chuyển thanh toán'} onPress={() => handleAction('chuyen_tt')} linearStyle={styles.footerBtn} />
          <ButtonLinear loading={isSubmiting} title={'Huỷ duyệt bảo lãnh'} onPress={() => handleAction('huy_duyet_bl')} linearStyle={styles.footerBtn} />
        </View>
      );
    } else if (validate6.indexOf(true) === -1) {
      if (tabIndex === 0) return null;
      return (
        <View style={styles.doubleBtn}>
          <ButtonLinear loading={isSubmiting} title={'Trình từ chối bảo lãnh'} linearStyle={styles.footerBtn} onPress={() => handleAction('trinh_tc')} />
        </View>
      );
    } else if (validate7.indexOf(true) === -1) {
      if (tabIndex === 0) return null;
      return (
        <View style={styles.doubleBtn}>
          {/* <ButtonLinear loading={isSubmiting} title={'Huỷ trình'} onPress={() => handleAction('huy_trinh')} linearStyle={styles.footerBtn} /> */}
          <ButtonLinear loading={isSubmiting} title={'Trình bảo lãnh'} linearStyle={styles.footerBtn} onPress={() => handleAction('trinh_bl')} />
        </View>
      );
    } else {
      if (tabIndex === 0) {
        return (
          <View style={styles.doubleBtn}>
            <ButtonLinear loading={isSubmiting} title={'Huỷ hồ sơ'} onPress={() => handleAction('huy_hs')} linearStyle={styles.footerBtn} />
          </View>
        );
      }

      return (
        <View style={[styles.doubleBtn]}>
          <ButtonLinear
            loading={isSubmiting}
            title={'Duyệt / Từ chối '}
            onPress={() => actionSheetDuyetBl.show()}
            linearColors={[colors.GRAY2, colors.GRAY2]}
            linearStyle={styles.footerBtn}
            textStyle={{color: colors.BLACK_03}}
          />
          <ButtonLinear
            loading={isSubmiting}
            title={'Trình'}
            onPress={() => actionSheetRef.show()}
            linearStyle={styles.footerBtn}
            // linearColors={[colors.ORANGE, colors.ORANGE]}
          />
        </View>
      );
    }
  };

  const renderItemTTCT = ({item, index}) => {
    return (
      <TouchableOpacity style={styles.itemThongTinChiTietQuyenLoi} onPress={() => onPressSuaQuyenLoi(item)}>
        <View flex={1}>
          <View>
            <View style={styles.contentRow}>
              <Text style={styles.labelItem}>Quyền lợi: </Text>
              <Text style={[styles.detailContent, {color: colors.PRIMARY}]}>{item.ten_quyen_loi}</Text>
            </View>
            <View style={styles.contentRow}>
              <Text style={styles.labelItem}>Chẩn đoán: </Text>
              <Text style={styles.detailContent}>{item.chan_doan}</Text>
            </View>
          </View>

          <View style={styles.contentRow}>
            <View style={styles.contentCol}>
              <Text style={styles.labelItem}>Số tiền yc</Text>
              <NumericFormat
                value={item.tien_yc}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => <Text children={value + ' ' + item.nt_tien_yc} style={[styles.detailContent, {color: colors.PRIMARY}]} />}
              />
            </View>
            <View style={styles.contentCol}>
              <Text style={styles.labelItem}>Giảm trừ</Text>
              <NumericFormat
                value={item.tong_tien_giam}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => <Text children={value} style={[styles.detailContent, {color: colors.PRIMARY}]} />}
              />
            </View>
            <View style={styles.contentCol}>
              <Text style={styles.labelItem}>Số tiền duyệt</Text>
              <NumericFormat
                value={item.tien_duyet}
                displayType={'text'}
                thousandSeparator={true}
                renderText={(value) => <Text children={value} style={[styles.detailContent, {color: colors.PRIMARY}]} />}
              />
            </View>
          </View>

          <View style={styles.contentRow}>
            <View flex={1} flexDirection="row" justifyContent="space-between">
              <Text style={styles.labelItem}>Quy đổi sang nguyên tệ số tiền bảo hiểm: </Text>
              {item.tien_yc_ntbh > 0 ? (
                <NumericFormat
                  value={item.tien_yc_ntbh}
                  displayType={'text'}
                  thousandSeparator={true}
                  renderText={(value) => <Text children={value + ' ' + item.ma_ntbh} style={[styles.detailContent, styles.price]} />}
                />
              ) : (
                <TouchableOpacity onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TY_GIA_NGUYEN_TE, {profileInfo, nguon: 'BL'})}>
                  <Text style={styles.txtNhapTyGia}>Nhập tỷ giá</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View style={styles.contentRow}>
            <View flex={1} flexDirection="row" justifyContent="space-between">
              <Text style={styles.labelItem}>Quy đổi sang VND: </Text>
              {item.tien_yc_vnd > 0 ? (
                <NumericFormat
                  value={item.tien_yc_vnd}
                  displayType={'text'}
                  thousandSeparator={true}
                  renderText={(value) => <Text children={value + ' VND'} style={[styles.detailContent, styles.price]} />}
                />
              ) : (
                <TouchableOpacity onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TY_GIA_NGUYEN_TE, {profileInfo, nguon: 'BL'})}>
                  <Text style={styles.txtNhapTyGia}>Nhập tỷ giá</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          <TouchableOpacity style={styles.contentRow} onPress={() => onPressShowNote(item)}>
            <Text style={styles.labelItem}>Ghi chú nội bộ: </Text>
            <Icon.Feather name="file-text" size={16} color={colors.PRIMARY} />
          </TouchableOpacity>
        </View>
        {thongTinLanBaoLanh?.trang_thai?.toUpperCase() === 'C' && <Icon.Feather name="chevron-right" size={20} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };

  const renderListThongTinBaoLanhLan = () => {
    return (
      <ScrollView>
        {dataCacLanBaoLanh.map((item, index) => {
          const switchTxtStatusColor = item.trang_thai === 'C' ? 'orange' : item.trang_thai === 'T' ? colors.RED1 : colors.GREEN;
          const isActive = selectedItemIndex === index;
          return (
            <TouchableOpacity onPress={() => onPressThongTinLan(item, index)} key={index}>
              <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.profileItemView, {borderColor: isActive ? colors.PRIMARY : colors.GRAY}]}>
                <View style={[styles.contentRow, {alignItems: 'center'}]}>
                  <Text style={[styles.labelItem, {color: colors.PRIMARY, fontSize: 14}]}>Lần bảo lãnh: Lần {item.lan_blvp_hien_thi}</Text>
                  <Text style={{color: switchTxtStatusColor, marginLeft: 16}}>{item.trang_thai_ten}</Text>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Ngày yêu cầu</Text>
                    <Text style={styles.detailContent}>{item.ngay_ht}</Text>
                  </View>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Ngày duyệt</Text>
                    <Text style={styles.detailContent}>{item.ngay_duyet_bl}</Text>
                  </View>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Người duyệt</Text>
                    <Text style={styles.detailContent}>{item.nsd_duyet}</Text>
                  </View>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Số tiền yc</Text>
                    <NumericFormat value={item.tien_yc} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                  </View>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Số tiền đề xuất</Text>
                    <NumericFormat value={item.tien_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                  </View>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Số tiền duyệt</Text>
                    <NumericFormat value={item.tien_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                  </View>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    );
  };

  const renderFooter = () => {
    return <View style={styles.footerView}>{switchButton()}</View>;
  };

  const renderActionSheetGiamDinh = () => (
    <ActionSheet
      ref={(o) => (actionSheetRef = o)}
      title={'Chọn loại trình'}
      options={['Trình duyệt bảo lãnh', 'Trình từ chối bảo lãnh', 'Để sau']}
      cancelButtonIndex={2}
      destructiveButtonIndex={2}
      onPress={(index) => onActionPress(index)}
    />
  );
  const renderActDuyetBl = () => (
    <ActionSheet
      ref={(o) => (actionSheetDuyetBl = o)}
      title={'Chọn thao tác'}
      options={['Duyệt bảo lãnh', 'Từ chối bảo lãnh', 'Để sau']}
      cancelButtonIndex={2}
      destructiveButtonIndex={2}
      onPress={(index) => onActionDuyetPress(index)}
    />
  );

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle={'Chi tiết hồ sơ bảo lãnh'}
      renderView={
        <SafeAreaView style={styles.container}>
          <ScrollableTabView ref={tabViewRef} style={styles.centerView} initialPage={0} onChangeTab={(value) => setTabIndex(value.i)} renderTabBar={() => <CustomTabBar />}>
            <ScrollView
              tabLabel="Thông tin hồ sơ"
              scrollEnabled={true}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={() => getData()} />}
              ref={scrollViewRef}
              showsVerticalScrollIndicator={false}>
              {profileInfo && <ProfileInformation handleAction={() => handleAction('chuyen_nxl')} profileInfo={profileInfo} profileData={profileData} laySoHoSo={laySoHoSo} />}
            </ScrollView>
            <ScrollView tabLabel="Thông tin bảo lãnh" style={styles.centerView}>
              <View marginBottom={50}>
                <View style={styles.headerTitleView}>
                  <Text style={styles.subLabel}>Thông tin các lần bảo lãnh viện phí </Text>
                  {enableBtnCopy && (
                    <TouchableOpacity onPress={onCopyLanBaoLanh}>
                      {/* <Icon.Foundation name="page-copy" size={22} color={colors.PRIMARY} /> */}
                      <Text style={{color: colors.PRIMARY, textDecorationLine: 'underline'}}>Copy</Text>
                    </TouchableOpacity>
                  )}
                </View>
                {dataCacLanBaoLanh?.length > 0 ? (
                  renderListThongTinBaoLanhLan()
                ) : (
                  <View style={styles.emptyView}>
                    <Empty
                      addMore
                      textAddMore="Tạo mới lần bảo lãnh"
                      description="Chưa có thông tin bảo lãnh viện phí!"
                      imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}}
                      onPress={() =>
                        NavigationUtil.push(SCREEN_ROUTER_APP.BLVP_TAO_MOI_LAN_BAO_LANH, {
                          thongTinChiPhi: [],
                          thongTinQuyenLoi: {},
                          thongTinLan: thongTinLanBaoLanh,
                          type: TYPE_INIT_FORM.CREATE_NEW,
                          dataQuyenLoiGoc: dataQuyenLoiGoc,
                          chiTietHoSo: profileInfo,
                        })
                      }
                    />
                  </View>
                )}
                <View style={styles.headerTitleView}>
                  <Text style={styles.subLabel}>Thông tin quyền lợi bảo lãnh viện phí lần {count} </Text>
                </View>
                <View style={{marginHorizontal: 10}}>
                  <FlatList refreshControl={<RefreshControl refreshing={refreshing} />} keyExtractor={(item, index) => index.toString()} data={dataQuyenLoiBL} renderItem={renderItemTTCT} />
                  {thongTinLanBaoLanh?.trang_thai === 'C' && (
                    <TouchableOpacity
                      style={styles.btnThemChiPhi}
                      onPress={() =>
                        NavigationUtil.push(SCREEN_ROUTER_APP.BLVP_TAO_MOI_LAN_BAO_LANH, {
                          thongTinChiPhi: [],
                          thongTinQuyenLoi: dataQuyenLoiBL[0] || {},
                          type: TYPE_INIT_FORM.CREATE_STEP,
                          thongTinLan: thongTinLanBaoLanh,
                          dataQuyenLoiGoc: dataQuyenLoiGoc,
                          chiTietHoSo: profileInfo,
                        })
                      }>
                      <Icon.Feather name="plus" size={18} color={colors.PRIMARY} />
                      <Text style={styles.txtBtnThemChiPhi}> Thêm mới quyền lợi</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </ScrollView>
          </ScrollableTabView>
          <ModalShowNote ref={refModalShowNote} detail={note} onBackPress={() => refModalShowNote.current.hide(false)} />
          <ModalNhapNoiDungHuyHS
            type={typeBtn}
            value={noiDungHuy}
            ref={refModalNhapNoiDungHuyHs}
            onPressSave={() => onPressSaveNoiDungHuy()}
            onChangeText={(text) => onChangeText(text)}
            onBackPress={() => refModalNhapNoiDungHuyHs.current.hide()}
          />
          {/* <ModalChuyenNguoiXuLy
            value={ghiChu}
            ref={refModalChuyenNguoiXuLy}
            onChangeText={(text) => setGhiChu(text)}
            onBackPress={() => refModalChuyenNguoiXuLy.current.hide()}
            onPressSave={(ma_cn, ma_cb) => onPressSaveNguoiXuLy(ma_cn, ma_cb)}
          /> */}
          {dataCacLanBaoLanh.length > 0 && renderFooter()}
          {renderActionSheetGiamDinh()}
          {renderActDuyetBl()}
        </SafeAreaView>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  user: state.user.data,
  notificationFirebase: state.notificationFirebase.data,
});
const mapDispatchToProps = {};
const ThongTinBaoLanhVienPhiScreenConnect = connect(mapStateToProps, mapDispatchToProps)(ThongTinBaoLanhVienPhiScreenComponent);
export const ThongTinBaoLanhVienPhiScreen = memo(ThongTinBaoLanhVienPhiScreenConnect, isEqual);
