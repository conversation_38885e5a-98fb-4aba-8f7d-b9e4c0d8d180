import {ScreenComponent, Text} from '@app/components';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, View} from 'react-native';
import styles from './ThongTinQuaTrinhXuLyStyles';
import {colors} from '@app/commons/Theme';

const ThongTinQuaTrinhXuLyScreenCompnent = (props) => {
  console.log('ThongTinQuaTrinhXuLyScreenCompnent');
  const route = useRoute();
  const {profileInfo} = route?.params;
  const [dialogLoading, setDialogLoading] = useState(false);

  const [data, setData] = useState([]);

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    setDialogLoading(true);
    try {
      let params = {
        ma_doi_tac: profileInfo?.ma_doi_tac,
        so_id: profileInfo?.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_THONG_TIN_QUA_TRINH_XU_LY_HS, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) return;
      else setData(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // const onRefresh = () => {
  //   getData();
  // };

  const renderResolveItem = ({item, index}) => {
    return (
      <View key={index} style={styles.resolveItemView}>
        <View flexDirection="row">
          <View style={styles.verticalLineStep}>
            <Text style={{color: colors.WHITE}}>{index + 1}</Text>
          </View>
          <View style={styles.titleView}>
            <Text style={styles.title}>{item.ten}</Text>
            <Text style={styles.date}>{item.ngay}</Text>
          </View>
        </View>
        <View style={styles.contentColumn}>
          <Text style={styles.subLabel}>{item.nd}</Text>
        </View>
      </View>
    );
  };

  const renderResolutionProcess = () => {
    return (
      <FlatList
        data={data}
        style={styles.flStyles}
        renderItem={renderResolveItem}
        keyExtractor={(item, index) => index.toString()}
        refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={() => getData()} />}
      />
    );
  };

  return (
    <ScreenComponent dialogLoading={dialogLoading} headerBack headerTitle="Quá trình xử lý hồ sơ" renderView={<SafeAreaView style={styles.container}>{renderResolutionProcess()}</SafeAreaView>} />
  );
};

export const ThongTinQuaTrinhXuLyScreen = memo(ThongTinQuaTrinhXuLyScreenCompnent, isEqual);
