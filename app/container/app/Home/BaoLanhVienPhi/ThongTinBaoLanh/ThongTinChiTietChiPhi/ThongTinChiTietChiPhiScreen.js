import {colors} from '@app/commons/Theme';
import {CheckboxComp, Icon, ScreenComponent, TextInputOutlined, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {connect} from 'react-redux';
import ModalThemChiPhiKhamBenh from '../ThemQuyenLoiBaoLanh/Modal/ModalThemChiPhiKhamBenh';
import ModalThemDanhMucChiPhi from './Modal/ModalThemDanhMucChiPhi';
import styles from './ThongTinChiTietChiPhiStyles';
const listTitle = ['Tên chi phí khám', 'Số tiền', 'Gi<PERSON> tham khảo', 'Mặc định'];

const ThongTinChiTietChiPhiScreenComponent = (props) => {
  console.log('ThongTinChiTietChiPhi Screen ');
  const {route, navigation} = props;

  const [chiPhiKhamBenh, setChiPhiKhamBenh] = useState('');
  const [isVisibleModalThemChiPhiKham, setIsVisibleModalThemChiPhiKham] = useState(false);
  const [isVisibleInputModal, setIsVisibleInputModal] = useState(false);
  const [data, setData] = useState([]);
  const [valueInput, setValueInput] = useState('');
  const [isSelected, setSelection] = useState(false);

  useEffect(() => {
    const isDuplicateData = data.filter((item) => item.name === chiPhiKhamBenh)?.length > 0;
    if (chiPhiKhamBenh !== '' && !isDuplicateData) {
      let newArrData = [...data];
      newArrData.push({
        name: chiPhiKhamBenh,
        so_tien: 0,
        gia_tham_khao: 'demo',
        mac_dinh: true,
      });
      setData(newArrData);
    }
  }, [chiPhiKhamBenh]);

  const openModalInputByItem = (item, index) => {
    console.log(index, 'index');
    setIsVisibleInputModal(true);
  };

  const renderItem = ({item, index}) => {
    return (
      <View style={styles.itemChiPhi}>
        <View style={styles.itemContent}>
          <Text style={styles.subLabel}>Tên chi phí khám</Text>
          <Text style={styles.contentDetail}>{item.name}</Text>
        </View>
        <View style={styles.itemContent}>
          <Text style={styles.subLabel}>Tên chi phí khám</Text>
          <TextInputOutlined
            editable={true}
            value={''}
            inputStyle={styles.input}
            placeholder="0 đ"
            // onChangeText={(t) => setSoChungTu(t)}
            containerStyle={styles.containerInput}
            // error={inputErr[0]}
            // containerStyle={{flex: 1, marginRight: 10}}
            //   isRequired={true}
          />
        </View>
        <View style={styles.itemContent}>
          <Text style={styles.subLabel}>Giá tham khảo: </Text>
          <Text style={styles.txtItemContent}>{item.gia_tham_khao}</Text>
        </View>
        <View style={[styles.itemContent, {marginVertical: 10}]}>
          <Text style={styles.subLabel}>Mặc định: </Text>
          <CheckboxComp value={item.mac_dinh} checkboxStyle={styles.checkbox} onValueChange={setSelection} />
        </View>
        <TouchableOpacity>
          <Text style={styles.txtXoaItem}>Xoá chi phí</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle={'TT chi tiết chi phí'}
      renderView={
        <SafeAreaView style={styles.wrapper}>
          <View style={styles.container}>
            <TouchableOpacity style={styles.btnThemChiPhi} onPress={() => setIsVisibleModalThemChiPhiKham(true)}>
              <Icon.Feather name="plus" size={22} color={colors.PRIMARY} />
              <Text style={styles.txtBtnThemChiPhi}>Thêm chi phí khám bệnh</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.btnThemChiPhi} onPress={() => setIsVisibleInputModal(true)}>
              <Icon.Feather name="plus" size={22} color={colors.PRIMARY} />
              <Text style={styles.txtBtnThemChiPhi}>Tạo mã chi phí khám bệnh</Text>
            </TouchableOpacity>
            <FlatList scrollEnabled={false} data={data} renderItem={renderItem} />
          </View>
          <ModalThemChiPhiKhamBenh setValue={setChiPhiKhamBenh} isVisible={isVisibleModalThemChiPhiKham} onBackPress={() => setIsVisibleModalThemChiPhiKham(false)} />
          <ModalThemDanhMucChiPhi setValue={setValueInput} isVisible={isVisibleInputModal} onBackPress={() => setIsVisibleInputModal(false)} />
        </SafeAreaView>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  user: state.user.data,
  notificationFirebase: state.notificationFirebase.data,
});
const mapDispatchToProps = {};
const ThongTinChiTietChiPhiScreenConnect = connect(mapStateToProps, mapDispatchToProps)(ThongTinChiTietChiPhiScreenComponent);
export const ThongTinChiTietChiPhiScreen = memo(ThongTinChiTietChiPhiScreenConnect, isEqual);
