import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text, TextInputOutlined} from '@component';
import React, {useRef, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

function ThemDanhMucChiPhiModal(props) {
  const {isVisible, onBackPress, value, setValue} = props;

  let scrollViewModalRef = useRef(null);

  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState(dimensions.height);
  const [inputValue, setInputValue] = useState('');

  const onChangeText = (text) => {
    setInputValue(text);
  };

  const onSave = () => {
    onBackPress && onBackPress();
    setValue && setValue(inputValue);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <TouchableOpacity style={{marginLeft: 10}} onPress={onBackPress}>
            <Icon.Ionicons name="arrow-back-outline" size={22} color={'gray'} />
          </TouchableOpacity>
          <Text style={styles.modalTitle} children="Chọn chi phí khám bệnh" />

          <TouchableOpacity style={styles.saveBtn} onPress={onSave}>
            <Text style={styles.txtSaveBtn}>Lưu</Text>
          </TouchableOpacity>
        </View>

        {/* <TextInput
          style={styles.searchInput}
          value={searchInput}
          placeholder="Tìm kiếm ..."
          placeholderTextColor={colors.GRAY}
          onChangeText={(value) => {
            setSearchInput(value);
            // props.onChangeSearchTextHangMuc(value);
          }}
          //   onSubmitEditing={props.onChangeSearchTextHangMuc}
          onFocus={() => Keyboard.o}
        /> */}
      </View>
    );
  };

  const renderContent = () => {
    return (
      <View style={styles.modalContentView}>
        <TextInputOutlined
          editable={true}
          value={inputValue}
          containerStyle={{minHeight: 80}}
          inputStyle={styles.inputGhiChuNoiBo}
          onChangeText={(t) => onChangeText(t)}
          // error={inputErr[0]}
        />
      </View>
    );
  };
  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      scrollOffset={scrollOffSet}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      scrollOffsetMax={dimensions.height - flatListHeight} // content height - ScrollView height
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    // height: height / 2,
    // height: 300,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingLeft: 16,
    // flex: 1,
    borderRadius: 25,
    height: 40,
    margin: 16,
  },

  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    backgroundColor: colors.GRAY2,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
  },
  modalContentView: {
    marginHorizontal: 10,
    marginTop: 10,
  },
  txtSaveBtn: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.PRIMARY,
  },
  saveBtn: {
    padding: 10,
  },
});

const mapStateToProps = (state) => ({});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(ThemDanhMucChiPhiModal);
