import {StyleSheet} from 'react-native';
import {colors} from '@app/commons/Theme';

const colWidth = width / 3;
const borderTableColor = colors.GRAY3;

export default StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  container: {
    margin: 10,
  },
  btnThemChiPhi: {
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtBtnThemChiPhi: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  tableBorder: {
    borderRadius: 4,
    borderWidth: 0.5,
    marginHorizontal: 10,
    borderColor: borderTableColor,
  },
  titleTableView: {
    width: colWidth,
    borderRightWidth: 0.5,
    borderColor: borderTableColor,
  },
  tableTitle: {
    flex: 1,
    paddingLeft: 10,
    fontWeight: '600',
    paddingVertical: 5,
    color: colors.WHITE,
    backgroundColor: colors.PRIMARY_08,
  },
  itemContent: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtItemContent: {
    flex: 1,
    textAlign: 'right',
  },
  itemChiPhi: {
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: 5,
    paddingVertical: 5,
    paddingHorizontal: 8,
    borderColor: colors.PRIMARY_08,
  },
  subLabel: {
    fontWeight: '700',
    marginVertical: 5,
    color: colors.PRIMARY,
  },
  contentDetail: {
    color: colors.BLACK,
    fontWeight: '600',
  },
  input: {
    height: 40,
    textAlign: 'right',
    width: width / 2 - 32,
  },
  containerInput: {
    marginVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  checkbox: {
    marginLeft: 20,
  },
  txtXoaItem: {
    fontWeight: '600',
    color: colors.RED1,
  },
});
