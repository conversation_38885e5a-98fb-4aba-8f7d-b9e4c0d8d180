import {colors} from '@app/commons/Theme';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Platform, RefreshControl, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSelector} from 'react-redux';

const ModalChonChanBoComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, maChiNhanh} = props;
  const userInfo = useSelector(selectUser);
  const [data, setData] = useState();

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedItemIndex, setSelectedItemIndex] = useState(-1);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const _onBackPress = () => {
    onBackPress && onBackPress();
  };

  const getData = async () => {
    let params = {
      ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      ma_chi_nhanh: '',
      ma: '',
      nhom_chuc_nang: '',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DANH_SACH_CAN_BO_XU_LY, params);
      setLoading(false);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.nsd_quyen_blvp.length === 0) return;
      else {
        const dataCanBo = response.data_info.nsd_quyen_blvp.filter((item) => item.ma_chi_nhanh === maChiNhanh);
        setData(dataCanBo);
      }
    } catch (error) {
      setLoading(false);
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setSelectedItemIndex(index);
  };

  const onRefresh = () => {
    setRefreshing(true);
    getData();
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Đơn vị xử lý" />
        <TouchableOpacity style={styles.closeView} onPress={_onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderItem = ({item, index}) => {
    const isCheck = selectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
        <View style={styles.txtView}>
          <Text style={{flex: 1}}>
            {item.ten} <Text style={{color: colors.PRIMARY, fontStyle: 'italic'}}>({item.ma})</Text>
          </Text>
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.1}
        style={styles.fLStyles}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={Platform.OS === 'ios' ? false : true}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={<Text style={{textAlign: 'center'}} children="Không có dữ liệu" />}
      />
    );
  };
  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      swipeDirection={['down', 'right']}
      onModalShow={() => getData()}>
      <View style={styles.container}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingRight: 10,
    paddingBottom: 10,
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
  },
  checkbox: {
    marginRight: 8,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Platform.OS === 'android' ? 4 : 0,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  fLStyles: {
    paddingTop: 10,
    marginBottom: 20,
    paddingHorizontal: 10,
  },
});

export const ModalChonChanBo = memo(ModalChonChanBoComponent, isEqual);
