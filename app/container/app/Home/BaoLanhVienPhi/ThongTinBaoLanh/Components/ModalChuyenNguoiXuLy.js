// import {colors} from '@app/commons/Theme';
// import {dimensions} from '@app/theme';
// import {HeaderModal, Icon, Text} from '@component';
// import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
// import isEqual from 'react-fast-compare';
// import {SafeAreaView, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
// import Modal from 'react-native-modal';
// import {ModalChonChanBo} from './ModalChonCanBo';
// import {ModalChonDonViXuly} from './ModalChonDonViXuLy';

// const ModalChuyenNguoiXuLyComponent = forwardRef((props, ref) => {
//   const {onChangeText, value, onBackPress, onPressSave} = props;
//   const [maCanBo, setMaCanBo] = useState('');
//   const [tenCanBo, setTenCanBo] = useState('');
//   const [maChiNhanh, setMaChiNhanh] = useState('');
//   const [tenChiNhanh, setTenChiNhanh] = useState('');

//   const [isVisible, setIsVisible] = useState(false);

//   let refModalChonDonViXuly = useRef(null);
//   let refModalChonCanBo = useRef(null);

//   useImperativeHandle(ref, () => ({
//     show: () => setIsVisible(true),
//     hide: () => setIsVisible(false),
//   }));

//   const _onPressSave = () => {
//     onPressSave && onPressSave(maChiNhanh, maCanBo);
//   };

//   const onSelectDoiTac = (item) => {
//     refModalChonDonViXuly.current.hide();
//     setMaChiNhanh(item.ma);
//     setTenChiNhanh(item.ten_tat);
//   };
//   const onSelectCanBo = (item) => {
//     refModalChonDonViXuly.current.hide();
//     setMaCanBo(item.ma);
//     setTenCanBo(item.ten);
//   };
//   const onRemoveValueDoiTac = () => {
//     setMaChiNhanh('');
//     setTenChiNhanh('');
//   };
//   const onRemoveValueCanBo = () => {
//     setMaCanBo('');
//     setTenCanBo('');
//   };

//   /* RENDER */

//   const renderContent = () => {
//     return (
//       <View marginTop={16} marginHorizontal={10}>
//         <View marginBottom={10}>
//           <Text style={styles.titleLabel}>Đơn vị xử lý</Text>
//           <TouchableOpacity style={styles.buttonChonBenhVien} onPress={() => refModalChonDonViXuly.current.show()}>
//             <Text style={{flex: 1}}>{tenChiNhanh || 'Chọn đối tác'}</Text>
//             {!tenChiNhanh ? (
//               <Icon.MaterialIcons name="keyboard-arrow-down" size={iconSize} style={styles.icon} color={colors.BLUE1} />
//             ) : (
//               <TouchableOpacity onPress={onRemoveValueDoiTac}>
//                 <Icon.MaterialIcons name="close" size={iconSize} style={styles.icon} color={colors.BLUE1} />
//               </TouchableOpacity>
//             )}
//           </TouchableOpacity>
//         </View>
//         <View marginBottom={10}>
//           <Text style={styles.titleLabel}>Người xử lý</Text>
//           <TouchableOpacity style={styles.buttonChonBenhVien} onPress={() => refModalChonCanBo.current.show()}>
//             <Text style={{flex: 1}}>{tenCanBo || 'Chọn cán bộ'}</Text>
//             {!tenCanBo ? (
//               <Icon.MaterialIcons name="keyboard-arrow-down" size={iconSize} style={styles.icon} color={colors.BLUE1} />
//             ) : (
//               <TouchableOpacity onPress={onRemoveValueCanBo}>
//                 <Icon.MaterialIcons name="close" size={iconSize} style={styles.icon} color={colors.BLUE1} />
//               </TouchableOpacity>
//             )}
//           </TouchableOpacity>
//         </View>
//         <View marginBottom={10}>
//           <Text style={styles.titleLabel}>Ghi chú</Text>
//           <TextInput multiline placeholder="Nhập ghi chú" style={styles.searchInput} value={value} onChangeText={onChangeText} placeholderTextColor="#CCC" />
//         </View>
//         <View style={styles.actButtonGr}>
//           <TouchableOpacity style={[styles.actButton, {backgroundColor: colors.GRAY2}]} onPress={onBackPress}>
//             <Text style={[styles.actButtonTxt, {color: colors.BLACK_03}]}>Huỷ</Text>
//           </TouchableOpacity>
//           <TouchableOpacity style={styles.actButton} onPress={() => _onPressSave()}>
//             <Text style={styles.actButtonTxt}>Lưu</Text>
//           </TouchableOpacity>
//         </View>
//       </View>
//     );
//   };

//   const iconSize = 22;
//   return (
//     <Modal
//       style={styles.modal}
//       isVisible={isVisible}
//       propagateSwipe={true}
//       animationIn="fadeInRight"
//       animationOut="fadeOutRight"
//       onSwipeComplete={onBackPress}
//       onBackdropPress={onBackPress}
//       onBackButtonPress={onBackPress}>
//       <SafeAreaView style={styles.container}>
//         <HeaderModal onBackPress={onBackPress} title="Chuyển người xử lý" />
//         {renderContent()}
//       </SafeAreaView>
//       <ModalChonDonViXuly ref={refModalChonDonViXuly} setValue={onSelectDoiTac} onBackPress={() => refModalChonDonViXuly.current.hide()} />
//       <ModalChonChanBo ref={refModalChonCanBo} setValue={onSelectCanBo} maChiNhanh={maChiNhanh} onBackPress={() => refModalChonCanBo.current.hide()} />
//     </Modal>
//   );
// });
// const styles = StyleSheet.create({
//   modal: {
//     margin: 0,
//     justifyContent: 'flex-end',
//   },
//   container: {
//     flex: 1,
//     width: dimensions.width,
//     height: dimensions.height,
//     borderTopLeftRadius: 10,
//     borderTopRightRadius: 10,
//     backgroundColor: colors.WHITE,
//   },
//   modalCameraContent: {
//     flex: 1,
//     backgroundColor: colors.WHITE,
//     // borderWidth: 1,
//   },
//   searchInput: {
//     height: 100,
//     padding: 10,
//     borderWidth: 1,
//     borderRadius: 10,
//     borderColor: colors.GRAY,
//     textAlignVertical: 'top',
//   },

//   backBtn: {
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   iconBack: {
//     paddingHorizontal: 15,
//   },
//   itemHangMucView: {
//     padding: 5,
//     flexDirection: 'row',
//   },
//   modalTitleView: {
//     alignItems: 'center',
//     flexDirection: 'row',
//     borderBottomWidth: 0.5,
//     borderTopLeftRadius: 10,
//     borderTopRightRadius: 10,
//     borderColor: colors.GRAY2,
//     backgroundColor: colors.WHITE5,
//   },
//   modalTitle: {
//     flex: 1,
//     fontSize: 16,
//     marginLeft: 30,
//     fontWeight: 'bold',
//     marginVertical: 15,
//     textAlign: 'center',
//   },
//   closeView: {
//     marginRight: 15,
//     borderRadius: 25,
//     paddingVertical: 1,
//     paddingHorizontal: 2,
//     backgroundColor: colors.GRAY2,
//   },
//   checkbox: {
//     marginRight: 5,
//   },
//   content: {
//     margin: 10,
//     paddingBottom: 20,
//   },
//   actButtonGr: {
//     paddingTop: 10,
//     flexDirection: 'row',
//     justifyContent: 'center',
//   },
//   actButton: {
//     borderRadius: 5,
//     paddingVertical: 8,
//     marginHorizontal: 8,
//     paddingHorizontal: 30,
//     backgroundColor: colors.PRIMARY,
//   },
//   actButtonTxt: {
//     fontWeight: '600',
//     color: colors.WHITE,
//   },
//   buttonChonBenhVien: {
//     borderWidth: 1,
//     borderColor: colors.GRAY,
//     borderRadius: 10,
//     paddingLeft: 15,
//     color: colors.BLACK,
//     paddingVertical: 10,
//     backgroundColor: colors.WHITE,
//     minHeight: 40,
//     paddingRight: 15,
//     textAlignVertical: 'center',
//     alignItems: 'center',
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//   },
//   dropDownTitle: {
//     marginBottom: 5,
//     fontWeight: 'bold',
//   },
//   titleLabel: {
//     fontWeight: '600',
//     marginBottom: 5,
//   },
// });

// export const ModalChuyenNguoiXuLy = memo(ModalChuyenNguoiXuLyComponent, isEqual);
