import {colors} from '@app/commons/Theme';
import {FontSize, dimensions, moderateScale, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  flStyles: {
    paddingTop: vScale(10),
  },
  contentView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: vScale(spacing.tiny),
  },
  itemView: {
    borderRadius: 10,
    borderWidth: 0.4,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginHorizontal: scale(spacing.small),
    paddingHorizontal: scale(spacing.small),
  },
  contentDetail: {
    flex: 1,
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    lineHeight: moderateScale(20),
  },
  date: {
    color: colors.BLACK_03,
    fontSize: FontSize.size13,
    lineHeight: moderateScale(20),
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  label: {
    fontWeight: '500',
    fontSize: FontSize.size13,
    color: colors.LABEL_GRAY1,
    lineHeight: moderateScale(20),
  },
  row: {
    flexDirection: 'row',
    marginTop: vScale(spacing.tiny),
  },
  txtSoHS: {
    fontWeight: '600',
    color: colors.PRIMARY,
    lineHeight: moderateScale(20),
  },
  doubleCol: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  price: {
    color: colors.BLUE1,
    fontWeight: '600',
    lineHeight: moderateScale(20),
  },
  divider: {
    height: 1,
    backgroundColor: colors.GRAY2,
    marginTop: vScale(spacing.tiny),
  },
  footerView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    width: dimensions.width,
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
  },
  footerLabel: {
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  totalPrice: {
    fontWeight: '600',
    color: colors.ORANGE,
    fontSize: FontSize.size14,
  },
});
