import {MA_TRANG_THAI} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Empty, Icon, ScreenComponent, Text} from '@app/components';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {NumericFormat} from 'react-number-format';
import {ModalShowNote} from '../Components/ModalShowNote';
import styles from './LichSuChiTraTienBaoHiemStyles';

const LichSuChiTraTienBaoHiemScreenComponent = (props) => {
  console.log('LichSuChiTraTienBaoHiemScreenComponent');
  const route = useRoute();
  const {profileInfo} = route?.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [data, setData] = useState([]);
  const [note, setNote] = useState('');
  const [tongTienNoiTru, setTongTienNoiTru] = useState(0);
  const [tongTienNgoaiTru, setTongTienNgoaiTru] = useState(0);

  let refModalShowNote = useRef();

  useEffect(() => {
    getData();
    setDialogLoading(true);
  }, []);

  const getData = async () => {
    let params = {
      ma_doi_tac: profileInfo?.ma_doi_tac,
      so_id: profileInfo?.so_id,
      so_id_dt: profileInfo?.so_id_dt,
      so_id_hd: profileInfo?.so_id_hd,
    };

    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_LS_BOI_THUONG, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let tienNoiTru = 0;
      let tienNgoaiTru = 0;
      let dataNoitru = response.data_info.ho_so.filter((item) => item.hinh_thuc_ten === 'Nội trú');
      dataNoitru.filter((item) => {
        let sum = +item.so_tien_duyet;
        tienNoiTru += sum;
      });
      let dataNgoaitru = response.data_info.ho_so.filter((item) => item.hinh_thuc_ten === 'Ngoại trú');
      dataNgoaitru.filter((item) => {
        let sum = +item.so_tien_duyet;
        tienNgoaiTru += sum;
      });
      setTongTienNoiTru(tienNoiTru);
      setTongTienNgoaiTru(tienNgoaiTru);
      setData(response.data_info.ho_so);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // const onRefresh = () => {
  //   getData();
  // };

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <View paddingHorizontal={10} flex={1}>
          <View flexDirection="row" marginBottom={8}>
            <Text style={styles.footerLabel} children="Tổng tiền nội trú: " />
            <NumericFormat value={tongTienNoiTru} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.totalPrice} children={value} />} />
          </View>
          <View flexDirection="row">
            <Text style={styles.footerLabel} children="Tổng tiền ngoại trú: " />
            <NumericFormat value={tongTienNgoaiTru} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.totalPrice} children={value} />} />
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Lịch sử chi trả"
      renderView={
        <SafeAreaView style={styles.container}>
          {data.length > 0 && <RenderHisTory setNote={setNote} data={data} refModalShowNote={refModalShowNote} />}
          <ModalShowNote ref={refModalShowNote} detail={note} onBackPress={() => refModalShowNote.current.hide()} />
          {renderFooter()}
        </SafeAreaView>
      }
    />
  );
};

const RenderHisTory = (props) => {
  const {data, setNote, refModalShowNote} = props;

  const onPressShowNote = (item) => {
    setNote && setNote(item.ghi_chu);
    refModalShowNote.current.show();
  };

  const renderResolveItem = ({item, index}) => {
    const statusColors = item.ma_trang_thai === MA_TRANG_THAI.XNHAN ? colors.GREEN : item.ma_trang_thai === MA_TRANG_THAI.HS_HUY ? colors.RED1 : colors.PRIMARY;
    const typeColor = item.loai === 'BLVP' ? colors.PRIMARY : colors.ORANGE;
    const lastItemIndex = data.length - 1;
    return (
      <View>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.itemView, {marginBottom: lastItemIndex === index ? 50 : 5}]}>
          <View style={styles.contentView}>
            <Text style={styles.label}>
              Số hồ sơ: <Text style={styles.txtSoHS}>{item.so_hs}</Text>
            </Text>
            <View style={styles.doubleCol}>
              <View style={styles.row}>
                <Text style={styles.label}>Ngày mở: </Text>
                <Text style={styles.date}>{item.ngay_ht}</Text>
              </View>
              <Text style={styles.label}>
                Loại: <Text style={{color: typeColor, flex: 1}}>{item.loai_ten_text}</Text>
              </Text>
            </View>
            <View style={styles.doubleCol}>
              <View style={styles.row}>
                <Text style={styles.label}>Ngày vv: </Text>
                <Text style={styles.date}>{item.ngay_vv}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Ngày rv: </Text>
                <Text style={styles.date}>{item.ngay_rv}</Text>
              </View>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Trạng thái: </Text>
              <Text style={[styles.contentDetail, {color: statusColors}]}>{item.trang_thai}</Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.row}>
              <Text style={styles.label}>Nguyên nhân: </Text>
              <Text>{item.ten_nguyen_nhan}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Số ngày duyệt: </Text>
              <Text>{item.so_ngay_duyet}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Hình thức điều trị: </Text>
              <Text style={styles.contentDetail}>{item.hinh_thuc_ten}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Cơ sở y tế: </Text>
              <Text style={styles.contentDetail}>{item.ten_benh_vien}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Chẩn đoán: </Text>
              <Text style={styles.contentDetail}>{item.chan_doan}</Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.doubleCol}>
              <View style={styles.row}>
                <Text style={styles.label}>Tiền yc: </Text>
                <NumericFormat value={item.so_tien_yc} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value + ' đ'} style={styles.price} />} />
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Tiền duyệt: </Text>
                <NumericFormat value={item.so_tien_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value + ' đ'} style={styles.price} />} />
              </View>
            </View>
            <View style={styles.row}>
              <Text style={styles.label}>Nguyên nhân giảm: </Text>
              <Text style={styles.contentDetail}>{item.nguyen_nhan}</Text>
            </View>
            <TouchableOpacity style={styles.row} onPress={() => onPressShowNote(item)}>
              <Text style={styles.label}>Ghi chú: </Text>
              <Icon.Feather name="file-text" size={16} color={colors.PRIMARY} />
            </TouchableOpacity>
          </View>
          {/* <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
          </View> */}
        </LinearGradient>
      </View>
    );
  };

  return (
    <FlatList
      keyExtractor={(item, index) => index.toString()}
      style={styles.flStyles}
      data={data}
      renderItem={renderResolveItem}
      ListFooterComponent={<View height={30} />}
      ListEmptyComponent={<Empty />}
    />
  );
};

export const LichSuChiTraTienBaoHiemScreen = memo(LichSuChiTraTienBaoHiemScreenComponent, isEqual);
