import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {CheckboxComp, Empty, HeaderModal, SearchBar, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalNguyenTeComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, value, data} = props;
  const [searchText, setSearchText] = useState('');
  const [dsNguyenTe, setDsNguyenTe] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const initModalData = () => {
    if (data.length > 0) {
      let newArrData = data;
      newArrData.map((e, index) => {
        newArrData[index].isChecked = false;
        if (value === e.ma) {
          newArrData[index].isChecked = true;
        }
      });
      setDsNguyenTe([...newArrData]);
    }
  };

  useEffect(() => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = data.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText) || item?.ma.toLowerCase()?.includes(lowerCaseSearchText));
      setDsNguyenTe(filter);
    } else if (searchText === '') {
      setDsNguyenTe(data);
    }
  }, [searchText, data]);

  const debounced = useDebouncedCallback(
    // function
    (value) => {
      setSearchText(value);
    },
    // delay in ms
    300,
  );

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index, !item.isChecked)}>
        <CheckboxComp disabled value={item.isChecked} checkboxStyle={styles.checkbox} />
        <View style={styles.txtView}>
          <Text>{item.ma}: </Text>
          <Text style={{flex: 1}}>{item.ten}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={dsNguyenTe}
        extraData={dsNguyenTe}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        style={styles.flStyles}
        keyExtractor={(item, index) => item + index.toString()}
        refreshControl={<RefreshControl refreshing={false} />}
        ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
      />
    );
  };
  return (
    <Modal onModalWillShow={initModalData} style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal
          onBackPress={onBackPress}
          centerComponent={<SearchBar placeholder="Tìm kiếm ..." onTextChange={debounced} />}
          // rightComponent={<Text style={styles.txtBtnSave}>Chọn</Text>}
        />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchText: {
    height: 40,
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 20,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    // paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginRight: 10,
  },
  txtView: {
    flex: 1,
    marginTop: !isIOS ? 4 : 2,
    flexDirection: 'row',
  },
  flStyles: {
    // marginBottom: 60,
    paddingHorizontal: 10,
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});

export const ModalNguyenTe = ModalNguyenTeComponent;
