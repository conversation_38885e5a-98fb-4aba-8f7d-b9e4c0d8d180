import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {useEffect, useRef, useState} from 'react';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

const data = ['Quyền lợi BH 1', 'Quyền lợi BH 2', 'Quyền lợi BH 3', 'Quyền lợi BH 4', 'Quyền lợi BH 5', 'Quyền lợi BH 6', 'Quyền lợi BH 7'];

function ModalThemChiPhiKhamBenh(props) {
  const {isVisible, onBackPress, value, setValue} = props;
  const [dataQuyenLoiBH, setDataQuyenLoiBH] = useState(data);

  let scrollViewModalRef = useRef(null);

  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState(dimensions.height);
  const [searchInput, setSearchInput] = useState('');
  const [isSelectedItemIndex, setIsSelectedItemIndex] = useState(true);

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setIsSelectedItemIndex(index);
  };

  useEffect(() => {
    if (searchInput) {
      const lowerCaseSearchText = searchInput?.toLowerCase();
      console.log(lowerCaseSearchText);
      const filter = dataQuyenLoiBH.filter((item) => item?.toLowerCase()?.includes(lowerCaseSearchText));
      setDataQuyenLoiBH(filter);
    }
    if (searchInput === '') {
      setDataQuyenLoiBH(data);
    }
  }, [searchInput]);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn chi phí khám bệnh" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>

        {/* <TextInput
          style={styles.searchInput}
          value={searchInput}
          placeholder="Tìm kiếm ..."
          placeholderTextColor={colors.GRAY}
          onChangeText={(value) => {
            setSearchInput(value);
            // props.onChangeSearchTextHangMuc(value);
          }}
          //   onSubmitEditing={props.onChangeSearchTextHangMuc}
          onFocus={() => Keyboard.o}
        /> */}
      </View>
    );
  };
  const renderItemBenhVien = (data, index) => {
    // const item = data.item;
    const isCheck = isSelectedItemIndex === index;
    const item = data;
    return (
      <TouchableOpacity key={item} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK}}>{item}</Text>
        {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView
        style={{marginHorizontal: 16, paddingBottom: 20}}
        ref={scrollViewModalRef}
        onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}>
        <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>{dataQuyenLoiBH.map((item, index) => renderItemBenhVien(item, index))}</View>
      </ScrollView>
    );
  };
  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      scrollOffset={scrollOffSet}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      scrollOffsetMax={dimensions.height - flatListHeight} // content height - ScrollView height
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    // height: 300,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingLeft: 16,
    // flex: 1,
    borderRadius: 25,
    height: 40,
    margin: 16,
  },

  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    backgroundColor: colors.GRAY2,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
  },
  modalContentView: {
    marginHorizontal: 10,
    marginTop: 10,
  },
});

const mapStateToProps = (state) => ({});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(ModalThemChiPhiKhamBenh);
