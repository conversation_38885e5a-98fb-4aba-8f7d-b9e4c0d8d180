import {colors} from '@app/commons/Theme';
import {Empty, HeaderModal, Text} from '@component';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, spacing} from '@app/theme';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Alert, FlatList, Platform, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import Modal from 'react-native-modal';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';

const ModalShowQLChiTietGCNComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  const userInfo = useSelector(selectUser);
  const {onBackPress, soId} = props;

  const [dataQLoi, setDataQLoi] = useState([]);
  const [dataDKBS, setDataDKBS] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const getData = async () => {
    let params = {
      ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      so_id: soId,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_CHI_TIET_QUYEN_LOI_GCN, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info?.length === 0) return;
      if (response.data_info.qloi.length > 0) setDataQLoi(response.data_info.qloi);
      if (response.data_info.dkbs.length > 0) setDataDKBS(response.data_info.dkbs);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  const onRefresh = () => {
    setRefreshing(true);
    getData();
  };

  /* RENDER */
  const renderItem = ({item, index}) => {
    const renderLabel = (label, value) => {
      const renderPrice = label === 'Số tiền giới hạn(ngày)/năm' || label === 'Quyền lợi/năm' || label === 'Ql/năm';
      return (
        <View flexDirection="row">
          <Text style={styles.itemLabel}>- {label}: </Text>
          {renderPrice ? (
            <NumericFormat value={value} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text children={val} style={styles.txtItemDetail} />} />
          ) : (
            <Text style={styles.txtItemDetail} children={value} />
          )}
        </View>
      );
    };

    return (
      <View key={index} style={styles.itemView}>
        <View flexDirection="row" justifyContent="space-between">
          <Text style={item.lh_nv_ct === null || '' ? styles.txtParent : styles.txtChildren}>{item.ten_hien_thi}</Text>
          <Text color={colors.RED1}>
            Nguyên tệ: <Text style={styles.currency}>{item.nt_tien_bh}</Text>
          </Text>
        </View>
        <Text style={styles.subTitle}>Quyền lợi bảo hiểm gốc</Text>
        {renderLabel('Số lần(ngày)/năm', item.so_lan_ngay)}
        {renderLabel('Tỷ lệ đồng', item.dong_bh + '%')}
        {renderLabel('Số tiền giới hạn(ngày)/năm', item.tien_lan_ngay)}
        {renderLabel('Quyền lợi/năm', item.tien_nam)}
        {renderLabel('Số ngày chờ', item.so_ngay_cho)}
        <View flexDirection="row" justifyContent="space-between">
          <View>
            <Text style={styles.subTitle}>Quyền lợi đã sử dụng</Text>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Số lần(ngày): </Text>
              <Text style={styles.txtItemDetail} children={item.so_lan_ngay_duyet} />
            </View>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Ql/năm: </Text>
              <NumericFormat value={item.tien_nam_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtItemDetail} />} />
            </View>
          </View>
          <View>
            <Text style={styles.subTitle}>Quyền lợi còn lại</Text>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Số lần(ngày): </Text>
              <Text style={styles.txtItemDetail} children={item.so_lan_ngay_con} />
            </View>
            {renderLabel('Ql/năm', item.tien_nam_con)}
          </View>
        </View>
      </View>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={dataQLoi}
        extraData={dataQLoi}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        style={styles.flStyles}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={Platform.OS === 'android' ? true : false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={<Empty />}
      />
    );
  };
  return (
    <Modal onModalWillShow={() => getData()} style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={onPressBack} title="Quyền lợi chi tiết GCN bảo hiểm" />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },

  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 10,
  },
  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    // paddingRight: 16,
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtView: {
    flex: 1,
    marginTop: Platform.OS == 'android' ? 4 : 2,
    flexDirection: 'row',
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  txtParent: {
    flex: 1,
    color: colors.RED1,
    fontWeight: '500',
    fontSize: FontSize.size14,
  },
  txtChildren: {
    flex: 1,
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  itemView: {
    paddingVertical: 4,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
  },
  txtItemDetail: {
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  itemLabel: {
    fontWeight: '500',
    color: colors.GRAY6,
    fontSize: FontSize.size13,
  },
  subTitle: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.BLACK,
    marginTop: spacing.tiny,
  },
  contentRow: {
    flexDirection: 'row',
    marginTop: spacing.tiny,
  },
  flStyles: {
    paddingHorizontal: 10,
  },
  currency: {
    fontWeight: '500',
    color: colors.PRIMARY,
  },
});

export const ModalShowQLChiTietGCN = ModalShowQLChiTietGCNComponent;
