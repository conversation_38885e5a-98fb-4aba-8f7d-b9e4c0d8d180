import {colors} from '@app/commons/Theme';
import {FontSize, spacing} from '@app/theme';
import {Empty, HeaderModal, SearchBar, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {FlatList, Platform, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {NumericFormat} from 'react-number-format';
import {useDebouncedCallback} from 'use-debounce';

const ModalChonQuyenLoiBaoHiemComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  const {onBackPress, dataQuyenLoiGoc, setValue, maQuyenLoi} = props;
  const [dataQLoi, setDataQLoi] = useState([]);
  const [dataQLoiRoot, setDataQLoiRoot] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  const [searchText, setSearchText] = useState('');

  const initModalData = () => {
    let dataQuyenLoiGocTemp = dataQuyenLoiGoc;
    if (maQuyenLoi !== '') {
      dataQuyenLoiGocTemp.map((item) => {
        item.isChecked = false;
        if (item.lh_nv === maQuyenLoi) item.isChecked = true;
      });
    }
    setDataQLoiRoot(dataQuyenLoiGocTemp);
    setDataQLoi(dataQuyenLoiGocTemp);
  };

  useEffect(() => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = dataQLoiRoot.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText) || item?.lh_nv.toLowerCase()?.includes(lowerCaseSearchText));
      setDataQLoi(filter);
    } else if (searchText === '') {
      setDataQLoi(dataQLoiRoot);
    }
  }, [searchText, dataQLoiRoot]);

  const debounced = useDebouncedCallback((value) => {
    setSearchText(value);
  }, 300);

  const onPressChonQuyenLoi = (selectedItem) => {
    if (selectedItem.check_ql_la != 1) return;
    setValue && setValue(selectedItem);
    setIsVisible(false);
  };

  /* RENDER */
  const renderItem = ({item, index}) => {
    const quyenLoiCoTheChon = item.check_ql_la == 1;

    const renderLabel = (label, value) => {
      const renderPrice = label === 'Số tiền giới hạn(ngày)/năm' || label === 'Quyền lợi/năm' || label === 'Ql/năm';
      return (
        <View flexDirection="row">
          <Text style={styles.itemLabel}>- {label}: </Text>
          {renderPrice ? (
            <NumericFormat value={value} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text children={val} style={styles.txtItemDetail} />} />
          ) : (
            <Text style={styles.txtItemDetail} children={value} />
          )}
        </View>
      );
    };

    return (
      <TouchableOpacity key={index} style={[styles.itemView, item.isChecked && {backgroundColor: colors.GREEN1}]} onPress={() => onPressChonQuyenLoi(item)}>
        <View flexDirection="row" justifyContent="space-between">
          <Text style={!quyenLoiCoTheChon ? styles.txtParent : styles.txtChildren}>{item.ten_hien_thi}</Text>
          <Text color={colors.RED1}>
            Nguyên tệ: <Text style={styles.currency}>{item.nt_tien_bh}</Text>
          </Text>
        </View>
        <Text style={styles.subTitle}>Quyền lợi bảo hiểm gốc</Text>
        {renderLabel('Số lần(ngày)/năm', item.so_lan_ngay)}
        {renderLabel('Tỷ lệ đồng', item.dong_bh + '%')}
        {renderLabel('Số tiền giới hạn(ngày)/năm', item.tien_lan_ngay)}
        {renderLabel('Quyền lợi/năm', item.tien_nam)}
        {renderLabel('Số ngày chờ', item.so_ngay_cho)}
        <View flexDirection="row" justifyContent="space-between">
          <View>
            <Text style={styles.subTitle}>Quyền lợi đã sử dụng</Text>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Số lần(ngày): </Text>
              <Text style={styles.txtItemDetail} children={item.so_lan_ngay_duyet} />
            </View>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Ql/năm: </Text>
              <NumericFormat value={item.tien_nam_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtItemDetail} />} />
            </View>
          </View>
          <View>
            <Text style={styles.subTitle}>Quyền lợi còn lại</Text>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Số lần(ngày): </Text>
              <Text style={styles.txtItemDetail} children={item.so_lan_ngay_con} />
            </View>
            {renderLabel('Ql/năm', item.tien_nam_con)}
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={dataQLoi}
        extraData={dataQLoi}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={Platform.OS === 'android' ? true : false}
        // refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={<Empty />}
      />
    );
  };
  return (
    <Modal onModalWillShow={initModalData} style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={() => onBackPress && onBackPress()} title="Quyền lợi chi tiết GCN bảo hiểm" />
        <SearchBar onTextChange={debounced} />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },

  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 10,
  },
  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    // paddingRight: 16,
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtView: {
    flex: 1,
    marginTop: Platform.OS == 'android' ? 4 : 2,
    flexDirection: 'row',
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  txtParent: {
    flex: 1,
    color: colors.RED1,
    fontWeight: '500',
    fontSize: FontSize.size14,
  },
  txtChildren: {
    flex: 1,
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  itemView: {
    paddingVertical: 4,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    paddingHorizontal: spacing.small,
  },
  txtItemDetail: {
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  itemLabel: {
    fontWeight: '500',
    color: colors.GRAY6,
    fontSize: FontSize.size13,
  },
  subTitle: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.BLACK,
    marginTop: spacing.tiny,
  },
  contentRow: {
    flexDirection: 'row',
    marginTop: spacing.tiny,
  },
  flStyles: {
    // paddingHorizontal: 10,
  },
  currency: {
    fontWeight: '500',
    color: colors.PRIMARY,
  },
});

export const ModalChonQuyenLoiBaoHiem = ModalChonQuyenLoiBaoHiemComponent;
