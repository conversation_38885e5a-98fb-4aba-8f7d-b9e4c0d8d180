import {SERVER_RESPONSE_CATEGORIES} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Platform, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonNguyenNhanComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  const {onBackPress, value, setValue, data} = props;
  const [dataNhomNguyenNhan, setDataNhomNguyenNhan] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
  };

  const initModalData = () => {
    if (data.length > 0) {
      let filterData = data.filter((item) => item.nhom === SERVER_RESPONSE_CATEGORIES.NHOM_NGUYEN_NHAN);
      filterData.map((e, i) => {
        filterData[i].isCheck = false;
        if (e.ma === value) {
          filterData[i].isCheck = true;
        }
      });
      setDataNhomNguyenNhan(filterData);
    }
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn nguyên nhân" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderItem = (item, index) => {
    const isCheck = item.isCheck;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} disabled />
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK_03, marginTop: Platform.OS == 'android' ? 4 : 2}}>{item.ten}</Text>
        {/* {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />} */}
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View>{dataNhomNguyenNhan?.map((item, index) => renderItem(item, index))}</View>
      </ScrollView>
    );
  };
  return (
    <Modal
      onModalWillShow={initModalData}
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      //
    >
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    margin: 16,
    borderWidth: 1,
    paddingLeft: 16,
    borderRadius: 25,
    borderColor: colors.GRAY,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    padding: 5,
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    marginTop: 10,
    paddingBottom: 20,
    marginHorizontal: 10,
  },
});

export const ModalChonNguyenNhan = ModalChonNguyenNhanComponent;
