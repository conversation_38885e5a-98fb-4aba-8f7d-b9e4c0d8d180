import {SERVER_RESPONSE_CATEGORIES} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {CheckboxComp, Empty, Icon, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Alert, FlatList, Keyboard, Platform, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSelector} from 'react-redux';
import {useDebouncedCallback} from 'use-debounce';

const ModalNguyenNhanGiamTruComponent = forwardRef((props, ref) => {
  const userInfo = useSelector(selectUser);
  const {arrValue, onBackPress, setNguyenNhanGiamTru, dataNguyenNhanGiamTru, setDataNguyenNhanGiamTru} = props;

  const [searchInput, setSearchInput] = useState('');
  const [data, setData] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    try {
      let params = {
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_NHIEU_DANH_MUC_BLVP, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) return;
      const data = response.data_info;
      let filterData = data.filter((item) => item.nhom === SERVER_RESPONSE_CATEGORIES.GIAM_TRU);
      filterData.forEach((item) => {
        item.isChecked = arrValue.findIndex((x) => x.ma == item.ma) != -1;
      });
      setData(filterData);
      setDataNguyenNhanGiamTru(filterData);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  const onSave = () => {
    const isCheckedDataArr = data.filter((item) => item.isChecked === true);
    setNguyenNhanGiamTru && setNguyenNhanGiamTru(isCheckedDataArr);
    onBackPress && onBackPress();
  };

  const onChangeCheckBoxValue = (item, index, value) => {
    let dataUpdate = [...data];
    dataUpdate[index].isChecked = value;
    setData(dataUpdate);
  };
  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 500);

  useEffect(() => {
    const lowerCaseSearchText = searchInput?.toLowerCase();
    const filter = dataNguyenNhanGiamTru.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
    setData(filter);
  }, [searchInput]);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <TouchableOpacity style={styles.backBtn} onPress={() => onPressBack()}>
          <Icon.AntDesign name="arrowleft" size={22} style={styles.iconBack} />
        </TouchableOpacity>
        <TextInput
          style={styles.searchInput}
          defaultValue={searchInput}
          placeholder="Tìm kiếm nguyên nhân ..."
          placeholderTextColor={colors.BLACK}
          onChangeText={debounced}
          //   onSubmitEditing={props.onChangeSearchTextHangMuc}
          onFocus={() => Keyboard.o}
        />
        <TouchableOpacity
          style={styles.backBtn}
          onPress={() => {
            // props.clearSearch();
            // setSearchInput('');
            onSave();
          }}>
          <Text style={styles.txtBtnSave}>Chọn</Text>
        </TouchableOpacity>
      </View>
    );
  };
  const renderItemBenhVien = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index, !item.isChecked)}>
        <CheckboxComp disabled value={item.isChecked} checkboxStyle={{marginRight: 5}} />
        <Text style={styles.txtItem}>{item.ten}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        style={styles.fLStyles}
        onEndReachedThreshold={0.3}
        renderItem={renderItemBenhVien}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => item + index.toString()}
        ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
      />
    );
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onModalWillShow={() => getData()} onBackButtonPress={onBackPress}>
      <View style={styles.content}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
    paddingTop: Platform.OS == 'ios' ? 60 : 10,
    // paddingHorizontal: 10,
  },
  content: {
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingLeft: 20,
    flex: 1,
    borderRadius: 5,
    height: 40,
    marginHorizontal: 16,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    // paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
  },
  txtBtnSave: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  txtItem: {
    flex: 1,
    lineHeight: 20,
    marginTop: Platform.OS == 'android' ? 4 : 0,
  },
  fLStyles: {
    paddingTop: 10,
    marginBottom: 20,
    paddingHorizontal: 10,
  },
});

export const ModalNguyenNhanGiamTru = ModalNguyenNhanGiamTruComponent;
