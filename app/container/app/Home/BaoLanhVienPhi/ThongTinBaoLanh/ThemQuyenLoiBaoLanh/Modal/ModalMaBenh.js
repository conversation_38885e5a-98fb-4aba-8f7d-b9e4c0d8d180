import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {CheckboxComp, Empty, HeaderModal, SearchBar, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalMaBenhComponent = forwardRef((props, ref) => {
  const {onBackPress, setArrValue, arrValue, dataMaBenh, setDataMaBenh} = props;
  const [searchInput, setSearchInput] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [listMabenh, setListMabenh] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  useEffect(() => {
    // getData(searchInput);
    return () => {
      setDataMaBenh([]);
    };
  }, []);

  const getData = async (tim = '', trang = 1, so_dong = 100) => {
    setRefreshing(true);
    try {
      let params = {
        tim: tim,
        trang: trang,
        so_dong: so_dong,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TIM_KIEM_MA_BENH_ICD, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.data.length === 0) setDataMaBenh([]);
      let listData = response.data_info.data;
      listData.forEach((item) => {
        item.isChecked = arrValue.findIndex((x) => x == item.ma) != -1;
      });
      setDataMaBenh(listData);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onPressSearch = () => {
    getData(searchInput, 1, 40);
  };

  useEffect(() => {
    getData(searchInput, 1, 40);
  }, [searchInput]);

  const debounced = useDebouncedCallback(
    // function
    (value) => {
      setSearchInput(value);
    },
    // delay in ms
    500,
  );

  const onRefresh = () => {
    getData(searchInput, 1);
  };

  const onSave = () => {
    setArrValue && setArrValue(listMabenh);
    onBackPress && onBackPress();
  };

  const onChangeCheckBoxValue = (item, index, value) => {
    let dataUpdate = [...dataMaBenh];
    dataUpdate[index].isChecked = value;
    setDataMaBenh(dataUpdate);
    const isCheckedDataArr = dataMaBenh.filter((item) => item.isChecked === true);
    setListMabenh(isCheckedDataArr);
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index, !item.isChecked)}>
        <CheckboxComp disabled value={item.isChecked} checkboxStyle={styles.checkbox} />
        <View style={styles.txtView}>
          <Text>{item.ma}: </Text>
          <Text style={{flex: 1}}>{item.ten}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={dataMaBenh}
        extraData={dataMaBenh}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        style={styles.flStyles}
        keyExtractor={(item, index) => item + index.toString()}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
      />
    );
  };
  return (
    <Modal onModalWillShow={() => getData(searchInput)} style={styles.modal} isVisible={isVisible} onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal
          onPressRight={onSave}
          onBackPress={onBackPress}
          centerComponent={<SearchBar placeholder="Tìm kiếm bệnh" onSubmitEditing={onPressSearch} onTextChange={debounced} onPressSearch={onPressSearch} />}
          rightComponent={<Text style={styles.txtBtnSave}>Chọn</Text>}
        />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 20,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    // paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginRight: 10,
  },
  txtView: {
    flex: 1,
    marginTop: !isIOS ? 4 : 2,
    flexDirection: 'row',
  },
  flStyles: {
    // marginBottom: 60,
    paddingHorizontal: 10,
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});

export const ModalMaBenh = ModalMaBenhComponent;
