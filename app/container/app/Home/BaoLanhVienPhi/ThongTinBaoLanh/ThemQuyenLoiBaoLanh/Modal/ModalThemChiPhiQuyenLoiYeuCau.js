import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {CheckboxComp, HeaderModal, Text} from '@component';
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';

const ModalThemChiPhiQuyenLoiYeuCauComp = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  const {onBackPress, setValue, arrValue} = props;
  const [data, setData] = useState([]);
  const [baseData, setBaseData] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [listChecked, setListChecked] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    getData(data);
  }, []);

  const getData = async (oldData = []) => {
    let params = {};
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHON_LOAI_CHI_PHI, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) return;
      if (oldData.length > 0) {
        oldData.forEach((item, index) => {
          if (oldData[index].isChecked === true) return;
          oldData[index].isChecked = false;
        });
        setData(oldData);
        setBaseData(oldData);
      } else {
        let data = response.data_info;
        data.forEach((item) => {
          item.isChecked = arrValue.findIndex((x) => x.ma == item.ma) != -1;
        });
        setData(data);
        setBaseData(data);
      }
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const initModalData = () => {
    setListChecked([]);
    data.map((item) => {
      item.isChecked = arrValue.findIndex((x) => x.loai_ct === item.ma) != -1;
    });
  };

  const onSave = () => {
    setValue && setValue(listChecked);
    onBackPress && onBackPress();
  };

  const onChangeCheckBoxValue = (item, index, value) => {
    let dataUpdate = [...baseData];
    dataUpdate[index].isChecked = value;
    dataUpdate[index].nhap_nguyen_nhan = '';
    setData(dataUpdate);
    const isCheckedDataArr = data.filter((item) => item.isChecked === true);
    setListChecked(isCheckedDataArr);
  };

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  const onRefresh = () => {
    setRefreshing(true);
    getData(data);
  };

  /* RENDER */
  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index, !item.isChecked)}>
        <CheckboxComp value={item.isChecked} checkboxStyle={styles.checkbox} onValueChange={(value) => onChangeCheckBoxValue(item, index, value)} />
        <Text style={styles.txtView}>{item.ten}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        keyExtractor={(item, index) => item + index.toString()}
        style={styles.flStyles}
      />
    );
  };
  return (
    <Modal onModalWillShow={initModalData} style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={onPressBack} title="Chọn loại chi phí" rightComponent={<Text style={styles.txtBtnSave}>Chọn</Text>} onPressRight={onSave} />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 8,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingVertical: 10,
    flexDirection: 'row',
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginRight: 5,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: !isIOS ? 4 : 2,
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  flStyles: {
    paddingHorizontal: 10,
  },
});

export const ModalThemChiPhiQuyenLoiYeuCau = ModalThemChiPhiQuyenLoiYeuCauComp;
