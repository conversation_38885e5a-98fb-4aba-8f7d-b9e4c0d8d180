import NavigationUtil from '@app/navigation/NavigationUtil';
import {store} from '@app/redux/store';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {CheckboxComp, HeaderModal, Text} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import styles from './ChiPhiQuyenLoiYeuCauStyles';

let timer;

const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment().format('YYYYMMDD');
let userInfo = store.getState().user.data;
const ChiPhiQuyenLoiYeuCauScreenComponent = (props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);

  const [current, setCurrent] = useState(1);

  const [data, setData] = useState([]);
  const [baseData, setBaseData] = useState([]);

  const [listChecked, setListChecked] = useState();

  const [objParams, setObjParams] = useState({
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    blv: userInfo?.nguoi_dung?.nsd,
    so_hd: '',
    so_hs: '',
    ten: '',
    ngay_sinh: null,
    trang_thai_hs_goc: '',
  });

  useEffect(() => {
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    getData(data);
  }, []);

  const getData = async (oldData = []) => {
    let params = {};
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHON_LOAI_CHI_PHI, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) return;
      if (oldData.length > 0) {
        oldData.forEach((item, index) => {
          if (oldData[index].isChecked === true) return;
          oldData[index].isChecked = false;
        });
        setData(oldData);
      } else {
        let data = response.data_info;
        if (data.length > 0) {
          data.forEach((item, index) => {
            if (data[index].isChecked === true) return;
            data[index].isChecked = false;
          });
        }
        setData(data);
        setBaseData(data);
      }
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  useEffect(() => {
    timer = setTimeout(() => {
      getData();
    }, 500);
    setIsLoading(true);
    setRefreshing(true);
  }, [objParams]);

  const onRefresh = () => {
    setRefreshing(true);
    setCurrent(1);
    getData();
  };

  const onChangeCheckBoxValue = (item, index, value) => {
    let dataUpdate = [...baseData];
    dataUpdate[index].isChecked = value;
    setData(dataUpdate);
    const isCheckedDataArr = data.filter((item) => item.isChecked === true);
    setListChecked(isCheckedDataArr);
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index, !item.isChecked)}>
        <CheckboxComp value={item.isChecked} checkboxStyle={styles.checkbox} onValueChange={(value) => onChangeCheckBoxValue(item, index, value)} />
        <Text style={styles.txtView}>{item.ten}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        keyExtractor={(item, index) => item + index.toString()}
        style={{marginBottom: 60, paddingHorizontal: 10}}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <HeaderModal title="Chọn loại chi phí" rightComponent={<Text style={styles.txtBtnSave}>Lưu</Text>} onBackPress={() => NavigationUtil.pop()} onPressRight={() => {}} />
      <View style={styles.content}>{renderContent()}</View>
    </SafeAreaView>
  );
};
export const ChiPhiQuyenLoiYeuCauScreen = memo(ChiPhiQuyenLoiYeuCauScreenComponent, isEqual);
