import { colors } from '@app/commons/Theme';
import { FontSize, dimensions, scale, spacing, vScale } from '@app/theme';
import { Icon, Text, TextInputOutlined } from '@component';
import React, { memo, useEffect, useRef, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Alert, FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ModalChonGhiChu } from './ModalChonGhiChu';
import { getPercentageWithFixedValue } from '@app/utils/string';

const RenderFormThemChiPhiQuyenLoiYeuCauComp = (props) => {
  const {
    deleteItem,
    profileInfo,
    nguyenNhanGiamTru,
    setListNguyenNhanGiamTruRender,
    listChiPhiQuyenLoiYeuCau,
    setIsVisibleNguyenNhanGiamTru,
    setIsVisibleThemChiPhiQuyenLoi,
    setListChiPhiQuyenLoiYeuCau,
    setSelectedIndex,
    selectedIndex,
    // xxx
  } = props;

  const [ghiChu, setGhiChu] = useState('');
  const [soChungTu, setSoChungTu] = useState('');
  const [soTienDuyet, setSoTienDuyet] = useState(0);
  const [soTienYeuCau, setSoTienYeuCau] = useState(0);
  const [soTienGiamTru, setSoTienGiamTru] = useState(0);
  const [tyLeChiTra, setTyLeChiTra] = useState(0);

  const [tenLoaiChiPhi, setTenLoaiChiPhi] = useState('');
  const [listNguyenNhanGiamTru, setListNguyenNhanGiamTru] = useState([]);
  const [dataChiPhiQuyenLoiYeuCau, setDataChiPhiQuyenLoiYeuCau] = useState([]);
  const [dataGhiChu, setDataGhiChu] = useState([]);
  const [tenNguyenNhanGiamTru, setTenNguyenNhanGiamTru] = useState('');
  const [isShowModalGhiChu, setIsShowModalGhiChu] = useState(false);
  const [isNhapNguyenNhan, setIsNhapNguyenNhan] = useState(false);
  const [inputErr, setInputErr] = useState(['', '', '']);

  let refInput = useRef();

  const initDataItem = (item, index) => {
    listChiPhiQuyenLoiYeuCau[index].ghi_chu = item.ghi_chu || '';
    listChiPhiQuyenLoiYeuCau[index].isSave = item.lan ? true : false;
    listChiPhiQuyenLoiYeuCau[index].so_ct = item.so_ct || '';
    listChiPhiQuyenLoiYeuCau[index].loai_ct = item.loai_ct || item.ma;
    listChiPhiQuyenLoiYeuCau[index].tien_duyet = item.tien_duyet || '';
    listChiPhiQuyenLoiYeuCau[index].tien_yc = item.tien_yc || '';
    listChiPhiQuyenLoiYeuCau[index].tien_giam = item.tien_giam || '';
    listChiPhiQuyenLoiYeuCau[index].nguyen_nhan_giam_tru = item.nguyen_nhan_giam_tru || [];
    listChiPhiQuyenLoiYeuCau[index].nguyen_nhan_giam = item.nguyen_nhan_giam || '';
    listChiPhiQuyenLoiYeuCau[index].ten_loai_chi_phi = item.ten_loai_chi_phi || item.ten;
  };

  useEffect(() => {
    if (nguyenNhanGiamTru?.length > 0) {
      let arrName = [];
      setListNguyenNhanGiamTru(nguyenNhanGiamTru);
      nguyenNhanGiamTru.map((e, index) => {
        let name = '- ' + e.ten;
        if (index > 0) name = '\n' + name;
        arrName.push(name);
      });
      setTenNguyenNhanGiamTru(arrName.toString());
    } else setListNguyenNhanGiamTru([]);
  }, [nguyenNhanGiamTru]);

  useEffect(() => {
    if (listChiPhiQuyenLoiYeuCau?.length > 0) {
      listChiPhiQuyenLoiYeuCau.forEach((item, index) => {
        if (index === selectedIndex) {
          setGhiChu(item.ghi_chu);
          setSoChungTu(item.so_ct);
          setSoTienDuyet(item.tien_duyet);
          setTyLeChiTra(item.tl_tien_duyet);
          setSoTienYeuCau(item.tien_yc);
          setSoTienGiamTru(item.tien_giam);
          setTenLoaiChiPhi(item.ten_loai_chi_phi);
          setListNguyenNhanGiamTru(item.nguyen_nhan_giam_tru);
          if (item.nguyen_nhan_giam_tru?.length > 0) {
            let arrName = [];
            item.nguyen_nhan_giam_tru.map((e, index) => {
              let name = '- ' + e.ten;
              if (index > 0) name = '\n' + name;
              arrName.push(name);
            });
            setTenNguyenNhanGiamTru(arrName.toString());
          } else setTenNguyenNhanGiamTru(item.nhap_nguyen_nhan || '');
        }
        if (listChiPhiQuyenLoiYeuCau[index].isSave === true) return;
        initDataItem(item, index);
      });
      setDataChiPhiQuyenLoiYeuCau(listChiPhiQuyenLoiYeuCau);
    } else {
      resetField();
      setListNguyenNhanGiamTru([]);
      setDataChiPhiQuyenLoiYeuCau([]);
    }
  }, [listChiPhiQuyenLoiYeuCau, selectedIndex]);

  useEffect(() => {
    setTenLoaiChiPhi(listChiPhiQuyenLoiYeuCau[0]?.ten_loai_chi_phi);
    setSelectedIndex(0);
  }, [listChiPhiQuyenLoiYeuCau]);

  const xoaChiPhi = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn xoá chi phí này?  ', [
      { text: 'Để sau', style: 'destructive' },
      {
        text: 'Đồng ý',
        onPress: () => {
          deleteItem && deleteItem(selectedIndex);
          onDeleteItem(selectedIndex);
        },
      },
    ]);
  };

  const onDeleteItem = (index) => {
    // initDataItem(index);
    resetField();
  };

  const resetField = () => {
    setGhiChu('');
    setSoChungTu('');
    setSoTienDuyet('');
    setSoTienYeuCau('');
    setSoTienGiamTru('');
    setTyLeChiTra('');
  };

  const onPressItem = (item, index) => {
    setSelectedIndex(index);
    setTenLoaiChiPhi(item.ten_loai_chi_phi);
    setListNguyenNhanGiamTruRender(listChiPhiQuyenLoiYeuCau[index].nguyen_nhan_giam_tru);
    // resetField();
  };

  const luuChiPhi = (index) => {
    const updateData = [...dataChiPhiQuyenLoiYeuCau];
    if (soTienYeuCau <= 0) {
      Alert.alert('Thông báo', 'Vui lòng nhập số tiền yêu cầu');
    }
    if (soTienGiamTru > 0 && nguyenNhanGiamTru.length <= 0 && tenNguyenNhanGiamTru === '') {
      Alert.alert('Thông báo', 'Vui lòng nhập nguyên nhân giảm trừ');
    }

    try {
      updateData[index].so_ct = soChungTu || '';
      updateData[index].tien_yc = soTienYeuCau || 0;
      updateData[index].tien_giam = soTienGiamTru || 0;
      updateData[index].tien_duyet = soTienDuyet || 0;
      updateData[index].tl_tien_duyet = tyLeChiTra || 0;
      updateData[index].ghi_chu = ghiChu || '';
      updateData[index].nguyen_nhan_giam_tru = nguyenNhanGiamTru;
      updateData[index].isSave = true;
      updateData[index].nhap_nguyen_nhan = isNhapNguyenNhan ? tenNguyenNhanGiamTru : '';
      // setDataChiPhiQuyenLoiYeuCau(updateData);
      // setListChiPhiQuyenLoiYeuCau(updateData);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    Alert.alert('Thông báo', 'Lưu thành công');
  };

  const onSetValue = (value) => {
    setGhiChu(value.noi_dung);
  };

  const onPressNhapNguyenNhanGiamTru = (index) => {
    setIsNhapNguyenNhan(true);
    setTenNguyenNhanGiamTru('');
    setListNguyenNhanGiamTruRender([]);
    refInput?.focus();
    let newArr = listChiPhiQuyenLoiYeuCau;
    newArr[index].nguyen_nhan_giam_tru = [];
    newArr[index].nguyen_nhan_giam = [];
    setDataChiPhiQuyenLoiYeuCau(newArr);
  };

  const onPressChonNguyenNhan = () => {
    setIsNhapNguyenNhan(false);
    setIsVisibleNguyenNhanGiamTru(false);
    setTenNguyenNhanGiamTru('');
  };

  const onChangeSoTienYeuCau = () => {
    if (!soTienGiamTru || soTienGiamTru == 0) {
      setSoTienDuyet(soTienYeuCau);
      setTyLeChiTra(100);
    } else {
      let soTienDuyetTinhToan = Math.min(Math.max(0, soTienYeuCau - soTienGiamTru), soTienYeuCau);
      setSoTienDuyet(soTienDuyetTinhToan);
      let tyLeChiTraTinhToan = soTienYeuCau > 0 ? (soTienDuyetTinhToan / soTienYeuCau) * 100 : 0;
      setTyLeChiTra(tyLeChiTraTinhToan);
    }
  };
  const onChangeSoTienGiamTru = () => {
    if (!soTienGiamTru || soTienGiamTru == 0) {
      setSoTienDuyet(soTienYeuCau);
      setTyLeChiTra(100);
    } else {
      let soTienDuyetTinhToan = Math.min(Math.max(0, soTienYeuCau - soTienGiamTru), soTienYeuCau);
      setSoTienDuyet(soTienDuyetTinhToan);
      let tyLeChiTraTinhToan = soTienYeuCau > 0 ? (soTienDuyetTinhToan / soTienYeuCau) * 100 : 0;
      setTyLeChiTra(tyLeChiTraTinhToan);
    }
  };

  const onChangeSoTienDuyet = () => {
    let soTienDuyetTinhToan = Math.min(soTienDuyet, soTienYeuCau);
    let tyLeChiTraTinhToan = soTienYeuCau > 0 ? (soTienDuyetTinhToan / soTienYeuCau) * 100 : 0;
    setTyLeChiTra(tyLeChiTraTinhToan);
    setSoTienDuyet(soTienDuyetTinhToan);
  };

  const onChangeTyLeChiTra = () => {
    let soTienDuyetTinhToan = (tyLeChiTra / 100) * soTienYeuCau;
    setSoTienDuyet(soTienDuyetTinhToan);
    if (soTienGiamTru > 0) {
      let soTienGiamTruTinhToan = soTienYeuCau - soTienDuyetTinhToan;
      setSoTienGiamTru(soTienGiamTruTinhToan);
    }
  };

  const renderPageView = (item, index) => {
    if (item.item === 0)
      return (
        <View style={styles.viewPage}>
          {listChiPhiQuyenLoiYeuCau?.length > 0 && (
            <View style={styles.itemChiPhi}>
              <View style={styles.itemChiPhiRow}>
                <Text style={styles.subLabel}>Tên loại chi phí</Text>
                {/* <TouchableOpacity onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THONG_TIN_CHI_TIET_CHI_PHI)}>
          <Text style={styles.txtChonNguyenNhan}>Khám bệnh (0)</Text>
        </TouchableOpacity> */}
                <Text style={styles.txtContentTenLoaiChiPhi}>{tenLoaiChiPhi ? tenLoaiChiPhi : listChiPhiQuyenLoiYeuCau[0]?.ten_loai_chi_phi}</Text>
              </View>
              <TextInputOutlined
                editable={true}
                value={soChungTu}
                title="Số chứng từ"
                inputStyle={styles.input}
                placeholder="Nhập số chứng từ"
                onChangeText={(t) => setSoChungTu(t)}
                containerStyle={styles.containerInput}
              // error={inputErr[0]}
              // containerStyle={{flex: 1, marginRight: 10}}
              //   isRequired={true}
              />
              <TextInputOutlined
                editable={true}
                isRequired={true}
                keyboardType="numeric"
                title="Số tiền yêu cầu"
                placeholder="0"
                inputStyle={styles.input}
                value={soTienYeuCau?.toString()}
                containerStyle={styles.containerInput}
                onChangeText={(t) => setSoTienYeuCau(t)}
                onBlur={() => onChangeSoTienYeuCau()}
              // error={inputErr[0]}
              // containerStyle={{flex: 1, marginRight: 10}}
              />
              <TextInputOutlined
                editable={true}
                keyboardType="numeric"
                title="Số tiền giảm trừ"
                inputStyle={styles.input}
                placeholder="0"
                value={soTienGiamTru?.toString()}
                containerStyle={styles.containerInput}
                onChangeText={(t) => setSoTienGiamTru(t)}
                onBlur={() => onChangeSoTienGiamTru()}
              // error={inputErr[0]}
              // containerStyle={{flex: 1, marginRight: 10}}
              />
              <TextInputOutlined
                editable={true}
                keyboardType="numeric"
                title="% chi trả"
                inputStyle={styles.input}
                placeholder="0"
                value={getPercentageWithFixedValue(tyLeChiTra)}
                containerStyle={styles.containerInput}
                onChangeText={(t) => setTyLeChiTra(t)}
                onBlur={onChangeTyLeChiTra}

              // error={inputErr[0]}
              // containerStyle={{flex: 1, marginRight: 10}}
              />
              <TextInputOutlined
                editable={true}
                isRequired={true}
                title="Số tiền duyệt"
                keyboardType="numeric"
                inputStyle={styles.input}
                placeholder="0"
                value={soTienDuyet?.toString()}
                containerStyle={styles.containerInput}
                onChangeText={(t) => setSoTienDuyet(t)}
                onBlur={() => onChangeSoTienDuyet()}
              // error={inputErr[0]}
              // containerStyle={{flex: 1, marginRight: 10}}
              />
              {/* <TextInputOutlined
                editable={true}
                isRequired={true}
                title="% chi trả"
                keyboardType="numeric"
                inputStyle={styles.input}
                placeholder="Số tiền duyệt"
                value={phanTramChiTra?.toString()}
                containerStyle={styles.containerInput}
                onChangeText={(t) => setSoTienDuyet(t)}
                // error={inputErr[0]}
                // containerStyle={{flex: 1, marginRight: 10}}
              /> */}
              <View>
                <Text style={styles.subLabel}>Nguyên nhân giảm trừ</Text>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  {/* <TouchableOpacity onPress={setIsVisibleNguyenNhanGiamTru}>
                    {listNguyenNhanGiamTru?.length > 0 ? (
                      listNguyenNhanGiamTru.map((item, index) => {
                        return (
                          <TouchableOpacity key={index} onPress={setIsVisibleNguyenNhanGiamTru}>
                            <Text style={styles.txtDetailContent}>- {item?.ten}</Text>
                          </TouchableOpacity>
                        );
                      })
                    ) : (
                      <Text style={styles.txtChonNguyenNhan}>{'Chọn nguyên nhân'}</Text>
                    )}
                  </TouchableOpacity> */}
                  <TouchableOpacity onPress={onPressChonNguyenNhan}>
                    <Text style={styles.txtChonNguyenNhan}>{'Chọn nguyên nhân'}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => onPressNhapNguyenNhanGiamTru(selectedIndex)}>
                    <Text style={styles.txtChonNguyenNhan}>{'Nhập nguyên nhân'}</Text>
                  </TouchableOpacity>
                </View>
                <TextInputOutlined
                  editable={listNguyenNhanGiamTru?.length <= 0}
                  placeholder="Nguyên nhân giảm trừ"
                  value={tenNguyenNhanGiamTru}
                  onChangeText={setTenNguyenNhanGiamTru}
                  inputStyle={{ height: 120 }}
                  multiline
                  getRef={(ref) => (refInput = ref)}
                // error={inputErr[0]}
                // containerStyle={{flex: 1, marginRight: 10}}
                />
              </View>
              <View marginTop={10}>
                <View flexDirection="row" alignItems="center">
                  <Text style={styles.subLabel}>Ghi chú: </Text>
                  <TouchableOpacity onPress={() => setIsShowModalGhiChu(true)}>
                    <Text style={styles.txtBamDeChon}>(Bấm để chọn)</Text>
                  </TouchableOpacity>
                </View>
                <TextInputOutlined
                  value={ghiChu}
                  // title="Ghi chú"
                  editable={true}
                  multiline={true}
                  placeholder="Nhập ghi chú"
                  inputStyle={styles.noteInput}
                  onChangeText={(t) => setGhiChu(t)}
                  // containerStyle={styles.containerInput}
                  error={inputErr[0]}
                //   isRequired={true}
                //   keyboardType="numeric"
                />
              </View>
              <View style={styles.bottomBtnAct}>
                <TouchableOpacity onPress={xoaChiPhi}>
                  <Text style={styles.txtBtnAct}>Xoá chi phí</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => luuChiPhi(selectedIndex)}>
                  <Text style={[styles.txtBtnAct, { color: colors.PRIMARY }]}>Lưu chi phí</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      );
    if (item.item === 1) return null;
  };

  return (
    <>
      <View style={{ marginHorizontal: 10 }}>
        <TouchableOpacity style={styles.btnThemChiPhi} onPress={setIsVisibleThemChiPhiQuyenLoi}>
          <Icon.Feather name="plus" size={22} color={colors.PRIMARY} />
          <Text style={styles.txtBtnThemChiPhi}> Thêm chi phí quyền lợi yêu cầu</Text>
        </TouchableOpacity>
        <View style={styles.listTenChiPhi}>
          {dataChiPhiQuyenLoiYeuCau.map((item, index) => {
            return (
              <TouchableOpacity key={index} onPress={() => onPressItem(item, index)} style={[styles.itemLoaiChiPhi, { backgroundColor: selectedIndex === index ? colors.PRIMARY : null }]}>
                <Text numberOfLines={1} style={[styles.txtLoaiChiPhi, { color: selectedIndex === index ? colors.WHITE10 : colors.BLACK_06 }]} children={item.ten_loai_chi_phi} />
              </TouchableOpacity>
            );
          })}
        </View>
        <FlatList data={[0, 1]} horizontal pagingEnabled renderItem={renderPageView} showsHorizontalScrollIndicator={false} keyExtractor={(index) => index.toString()} />
      </View>
      <ModalChonGhiChu
        dataGhiChu={dataGhiChu}
        profileData={profileInfo}
        setDataGhiChu={setDataGhiChu}
        isVisible={isShowModalGhiChu}
        setValue={(value) => onSetValue(value)}
        onBackPress={() => setIsShowModalGhiChu(false)}
      />
    </>
  );
};
const colWidth = dimensions.width / 2 - 20;
const borderTableColor = colors.GRAY3;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },

  scrollView: {
    width: dimensions.width,
    // backgroundColor: colors.PRIMARY_LIGHT,
  },
  btnLoginView: {
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 10,
    justifyContent: 'center',
    backgroundColor: colors.PRIMARY,
  },
  footerView: {
    flex: 1,
    left: 0,
    right: 0,
    bottom: 0,
    width: dimensions.width,
    paddingVertical: 10,
    position: 'absolute',
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  stepIndicator: {
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingRight: 5,
    paddingVertical: 10,
    color: colors.WHITE,
    textTransform: 'uppercase',
  },
  headerTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: colors.BLUE3,
  },
  subHeaderTitle: {
    fontSize: 14,
    marginBottom: 10,
    fontWeight: '600',
    color: colors.BLUE3,
  },
  contentDetail: {
    backgroundColor: colors.WHITE,
  },
  modalSelectorView: {
    marginRight: 15,
    marginVertical: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },

  headerTitleView: {
    marginBottom: 2,
    paddingVertical: 8,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
  },
  errTxt: {
    fontSize: 12,
    marginLeft: 12,
    color: colors.RED1,
  },
  doubleInputRow: {
    width: (dimensions.width - 30) / 2,
  },
  doubleInputRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputDisableEdit: {
    height: 40,
    backgroundColor: colors.GRAY2,
  },
  inputContainer: {
    marginTop: 0,
  },
  txtBtnThemChiPhi: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  grTopBtn: {
    margin: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  btnThemChiPhi: {
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemChiPhi: {
    borderWidth: 1,
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderColor: colors.PRIMARY_08,
  },
  subLabel: {
    fontWeight: '700',
    marginVertical: 5,
  },
  value: {
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  itemChiPhiRow: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  input: {
    height: 40,
    textAlign: 'right',
    width: dimensions.width / 2 - 32,
    // marginBottom: 10,
  },
  btnThemYeuCau: {
    marginTop: 10,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignSelf: 'flex-end',
    backgroundColor: colors.PRIMARY_08,
  },
  txtBTnThemYeuCau: {
    fontWeight: '600',
    color: colors.WHITE,
  },
  txtContentTenLoaiChiPhi: {
    flex: 1,
    textAlign: 'right',
    fontWeight: '600',
    color: colors.PRIMARY_08,
  },
  txtChonNguyenNhan: {
    flex: 1,
    fontWeight: '600',
    color: colors.PRIMARY_08,
    textDecorationLine: 'underline',
  },
  containerInput: {
    marginVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  containerInputStyle: {
    minHeight: 80,
    maxHeight: 100,
    textAlignVertical: 'top',
  },
  btnAtc: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE,
  },
  txtBtnAct: {
    color: colors.RED1,
    fontWeight: '500',
    fontSize: FontSize.size15,
    marginHorizontal: scale(spacing.small),
    textDecorationLine: 'underline',
  },
  bottomBtnAct: {
    flexDirection: 'row',
    marginTop: vScale(16),
    justifyContent: 'center',
  },
  txtBamDeChon: {
    fontWeight: '600',
    fontStyle: 'italic',
    color: colors.PRIMARY_08,
  },
  ghiChuView: {
    marginTop: 24,
    marginHorizontal: 16,
  },
  footerBtn: {
    marginHorizontal: 16,
  },
  inputGhiChuNoiBo: {
    minHeight: 80,
    paddingTop: 10,
    textAlignVertical: 'top',
  },
  txtLoaiChiPhi: {
    fontSize: 12,
  },
  itemLoaiChiPhi: {
    padding: 5,
    marginRight: 4,
    marginBottom: 4,
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: colors.GRAY,
    maxWidth: (dimensions.width - 28) / 2,
  },
  btnEdit: {
    marginRight: 10,
    borderBottomWidth: 2,
    flexDirection: 'row',
  },

  tableBorder: {
    borderRadius: 4,
    borderWidth: 0.5,
    marginHorizontal: 10,
    borderColor: borderTableColor,
  },
  titleTableView: {
    width: colWidth,
    borderRightWidth: 0.5,
    borderColor: borderTableColor,
  },
  tableTitle: {
    flex: 1,
    paddingLeft: 10,
    fontWeight: '600',
    paddingVertical: 5,
    color: colors.WHITE,
    backgroundColor: colors.PRIMARY_08,
  },
  noteInput: {
    minHeight: 80,
    maxHeight: 100,
    textAlignVertical: 'top',
  },
  styleContainer: {
    marginVertical: 5,
  },
  txtDetailContent: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  listTenChiPhi: {
    flex: 1,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  viewPage: {
    marginTop: 10,
    width: dimensions.width - 20,
  },
  contentRow: {
    flexDirection: 'row',
  },
  chiTietItemChiPhi: {
    padding: 5,
    marginTop: 10,
    borderRadius: 5,
    borderWidth: 0.5,
    borderColor: colors.GRAY,
  },

  itemLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.BLACK_03,
  },
  txtItemDetail: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.PRIMARY,
    flex: 1,
  },
  btnShowQuyenLoi: {
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  txtTab: {
    color: colors.BLACK_03,
    marginLeft: 4,
    marginBottom: 2,
  },
});

export const RenderFormThemChiPhiQuyenLoiYeuCau = memo(RenderFormThemChiPhiQuyenLoiYeuCauComp, isEqual);
