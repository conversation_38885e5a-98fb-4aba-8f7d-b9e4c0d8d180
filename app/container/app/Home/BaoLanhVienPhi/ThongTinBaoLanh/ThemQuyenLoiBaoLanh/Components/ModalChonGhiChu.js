import {BL_DEFAULT_PARAMS, NGHIEP_VU} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {forwardRef, memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonGhiChuComponent = forwardRef((props, ref) => {
  const {isVisible, onBackPress, setValue, dataGhiChu, setDataGhiChu, profileData} = props;
  const [isSelectedItemIndex, setIsSelectedItemIndex] = useState(-1);

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    let params = {
      ma_doi_tac: profileData?.ma_doi_tac,
      ma_doi_tac_ql: profileData?.ma_doi_tac_ql,
      pm: BL_DEFAULT_PARAMS.PM_BL,
      nv: NGHIEP_VU.NG,
      nv_ct: 'GHI_CHU_BL',
    };

    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHON_LOAI_CHI_PHI, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDataGhiChu(response.data_info.noi_dung);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // useEffect(() => {
  //   if (nhomGhiChu !== '') {
  //     let isCheck = dataGhiChu.findIndex((item) => item.ma === nhomGhiChu);
  //     setIsSelectedItemIndex(isCheck);
  //     let contentGhiChu = dataGhiChu.find((item) => item.ma === nhomGhiChu);
  //     if (contentGhiChu !== undefined) {
  //       setValue && setValue(contentGhiChu);
  //     }
  //   }
  // }, [nhomGhiChu, dataGhiChu]);

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setIsSelectedItemIndex(index);
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn ghi chú" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderItemGhiChu = (item, index) => {
    const isCheck = isSelectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <View style={{flexDirection: 'row'}}>
          <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
          <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK, flex: 1}}>{item.noi_dung}: </Text>
        </View>
        {/* {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />} */}
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View>{dataGhiChu?.map((item, index) => renderItemGhiChu(item, index))}</View>
      </ScrollView>
    );
  };
  return (
    <Modal
      onModalShow={getData}
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    // height: 300,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: 16,
    borderColor: colors.GRAY,
    // flex: 1,
    borderRadius: 25,
    height: 40,
    margin: 16,
  },

  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 5,
    paddingHorizontal: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  modalContentView: {
    marginTop: 10,
    marginHorizontal: 10,
  },
  checkbox: {
    marginRight: 5,
  },
  scrollView: {
    paddingVertical: 10,
    marginHorizontal: 5,
  },
});

// const mapStateToProps = (state) => ({});
// const mapDispatchToProps = {};
// // export default connect(mapStateToProps, mapDispatchToProps)(ModalChonGhiChu);
export const ModalChonGhiChu = memo(ModalChonGhiChuComponent, isEqual);
