import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';
const colWidth = dimensions.width / 2 - 20;
const borderTableColor = colors.GRAY3;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },

  scrollView: {
    width: dimensions.width,
    // backgroundColor: colors.PRIMARY_LIGHT,
  },
  btnLoginView: {
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 10,
    justifyContent: 'center',
    backgroundColor: colors.PRIMARY,
  },
  footerView: {
    margin: spacing.small,
    flexDirection: 'row',
  },
  stepIndicator: {
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingRight: 5,
    paddingVertical: 10,
    color: colors.WHITE,
    textTransform: 'uppercase',
  },
  headerTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: colors.BLUE3,
  },
  subHeaderTitle: {
    fontSize: 14,
    marginBottom: 10,
    fontWeight: '600',
    color: colors.BLUE3,
  },
  contentDetail: {
    backgroundColor: colors.WHITE,
  },
  modalSelectorView: {
    marginRight: 15,
    marginVertical: 10,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },

  headerTitleView: {
    marginBottom: 2,
    paddingVertical: 8,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
  },
  errTxt: {
    fontSize: 12,
    marginLeft: 12,
    color: colors.RED1,
  },
  doubleInputRow: {
    width: (dimensions.width - 30) / 2,
  },
  doubleInputRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  inputDisableEdit: {
    height: 40,
    backgroundColor: colors.GRAY2,
  },
  inputContainer: {
    marginTop: 0,
  },
  txtBtnThemChiPhi: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  grTopBtn: {
    margin: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  btnThemChiPhi: {
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemChiPhi: {
    borderWidth: 1,
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderColor: colors.PRIMARY_08,
  },
  subLabel: {
    fontWeight: '700',
    marginVertical: 5,
  },
  value: {
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  itemChiPhiRow: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  input: {
    height: 40,
    textAlign: 'right',
    width: dimensions.width / 2 - 32,
  },
  btnThemYeuCau: {
    marginTop: 10,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignSelf: 'flex-end',
    backgroundColor: colors.PRIMARY_08,
  },
  txtBTnThemYeuCau: {
    fontWeight: '600',
    color: colors.WHITE,
  },
  txtContentTenLoaiChiPhi: {
    flex: 1,
    textAlign: 'right',
    fontWeight: '600',
    color: colors.PRIMARY_08,
  },
  txtChonNguyenNhan: {
    flex: 1,
    fontWeight: '600',
    color: colors.PRIMARY_08,
  },
  containerInput: {
    marginVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  containerInputStyle: {
    minHeight: 80,
    maxHeight: 100,
    textAlignVertical: 'top',
  },
  btnAtc: {
    borderRadius: 4,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.PRIMARY,
  },
  txtBtnAct: {
    color: colors.WHITE,
  },
  bottomBtnAct: {
    marginTop: 16,

    flexDirection: 'row',
    justifyContent: 'center',
  },
  txtBamDeChon: {
    fontWeight: '600',
    fontStyle: 'italic',
    color: colors.PRIMARY_08,
  },
  ghiChuView: {
    marginTop: 24,
    marginHorizontal: 16,
  },
  footerBtn: {
    marginHorizontal: 16,
    // marginBottom: Platform.OS == 'ios' ? getStatusBarHeight() - 10 : 0,
    // height: Platform.OS == 'ios' ? 60 : 40,
  },
  inputGhiChuNoiBo: {
    minHeight: 80,
    paddingTop: 10,
    textAlignVertical: 'top',
  },
  txtLoaiChiPhi: {
    fontSize: 12,
  },
  itemLoaiChiPhi: {
    padding: 5,
    marginRight: 4,
    marginBottom: 4,
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: colors.GRAY,
    maxWidth: (dimensions.width - 28) / 2,
  },
  btnEdit: {
    // borderBottomWidth: 2,
    borderRadius: 8,
    marginVertical: 5,
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    justifyContent: 'center',
  },

  tableBorder: {
    borderRadius: 4,
    borderWidth: 0.5,
    marginHorizontal: 10,
    borderColor: borderTableColor,
  },
  titleTableView: {
    width: colWidth,
    borderRightWidth: 0.5,
    borderColor: borderTableColor,
  },
  tableTitle: {
    flex: 1,
    paddingLeft: 10,
    fontWeight: '600',
    paddingVertical: 5,
    color: colors.WHITE,
    backgroundColor: colors.PRIMARY_08,
  },
  noteInput: {
    minHeight: 80,
    maxHeight: 100,
    textAlignVertical: 'top',
  },
  styleContainer: {
    marginVertical: 5,
  },
  txtDetailContent: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  listTenChiPhi: {
    flex: 1,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  viewPage: {
    marginTop: 10,
    width: dimensions.width - 20,
  },
  contentRow: {
    flexDirection: 'row',
  },
  chiTietItemChiPhi: {
    padding: 5,
    marginTop: 10,
    borderRadius: 5,
    borderWidth: 0.5,
    borderColor: colors.GRAY,
  },

  itemLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.BLACK_03,
  },
  txtItemDetail: {
    fontSize: 13,
    fontWeight: '600',
    color: colors.PRIMARY,
    flex: 1,
  },
  btnShowQuyenLoi: {
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  txtTab: {
    marginLeft: 4,
    marginBottom: 2,
    color: colors.BLACK_03,
  },
  btnRow: {
    flex: 1,
    marginBottom: 10,
    flexDirection: 'row',
    paddingHorizontal: 10,
    backgroundColor: colors.GRAY2,
    justifyContent: 'space-between',
  },
  txtLuu: {
    color: '#FFF',
    fontSize: FontSize.size16,
    fontWeight: 'bold',
  },
  btnLuuView: {
    paddingHorizontal: spacing.small,
    justifyContent: 'center',
    flex: 1,
  },
});
