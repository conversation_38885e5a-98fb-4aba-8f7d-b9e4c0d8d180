import {TYPE_INIT_FORM} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {cacDanhMucBoiThuongConNguoi, dataNguyenTe, selectSuKienBaoHiem} from '@app/redux/slices/CategoryCommonSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Icon, ScreenComponent, Text, TextInputOutlined} from '@component';
import {useRoute} from '@react-navigation/native';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {<PERSON><PERSON>, FlatList, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import RenderImage from '../XemChiTietTaiLieu/Component/RenderImage';
import {RenderFormThemChiPhiQuyenLoiYeuCau} from './Components';
import {
  ModalChonGhiChuNoiBo,
  ModalChonHinhThucDieuTri,
  ModalChonNguyenNhan,
  ModalChonNhomSuKienBaoHiem,
  ModalChonQuyenLoiBaoHiem,
  ModalMaBenh,
  ModalNguyenNhanGiamTru,
  ModalNguyenTe,
  ModalShowQLChiTietGCN,
  ModalThemChiPhiQuyenLoiYeuCau,
} from './Modal';
import styles from './ThemQuyenLoiBaoLanhStyles';
import {headerTitle, titleInput, titleInputNguyenNhanTaiNan, titleInputQuyenLoiBaoLanh, titleInputTTThanhToan} from './constant';

const button = [
  {
    title: 'Sửa',
    iconName: 'edit',
  },
  {
    title: 'Xem hình ảnh HS/TL',
    iconName: 'image',
  },
  {
    title: 'Chi phí',
    iconName: 'eye',
  },
];

const ThemQuyenLoiBaoLanhScreenComponent = (props) => {
  console.log('ThemQuyenLoiBaoLanhScreenComponent');
  const route = useRoute();
  const {thongTinLan, thongTinQuyenLoi, dataQuyenLoiGoc, thongTinChiPhi, chiTietHoSo, type, navigation} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const dsNguyenTe = useSelector(dataNguyenTe);
  const danhMucBoiThuongConNguoi = useSelector(cacDanhMucBoiThuongConNguoi);
  const danhSachSuKienBaoHiem = useSelector(selectSuKienBaoHiem);
  //model ref
  let refModalNguyenTe = useRef();
  let refModalQuyenLoi = useRef();
  let refModalMaBenhICD = useRef();
  let refModalGhiChuNoiBo = useRef();
  let refModalNhomNguyenNhan = useRef();
  let refModalHinhThucDieuTri = useRef();
  let refModalShowQLChiTietGCN = useRef();
  let refModalNguyenNhanGiamTru = useRef();
  let refModalThemChiPhiQuyenLoiYeuCau = useRef();
  let refModalChonNhomSuKienBaoHiem = useRef();

  //toggle picker modal
  const [toggleGioVaoVien, setToggleGioVaoVien] = useState(false);
  const [toggleGioRaVien, setToggleGioRaVien] = useState(false);
  const [toggleNgayVaoVien, setToggleNgayVaoVien] = useState(false);
  const [toggleNgayRaVien, setToggleNgayRaVien] = useState(false);
  const [toggleNgayTaiNan, setToggleNgayTaiNan] = useState(false);

  //toggle section

  const [indexView, setIndexView] = useState(0);

  const [quyenLoiBH, setQuyenLoiBH] = useState({});

  const [ghiChuNoiBo, setGhiChuNoiBo] = useState(thongTinLan?.ghi_chu || '');
  const [maNhomGhiChu, setMaNhomGhiChu] = useState(thongTinLan?.nhom_ghi_chu || '');
  const [ghiChuKhac, setGhiChuKhac] = useState(thongTinLan?.ghi_chu_khac || '');
  const [listMaBenhRender, setListMaBenhRender] = useState([]);
  const [dataMaBenh, setDataMaBenh] = useState([]);
  const [listNguyenNhanGiamTruRender, setListNguyenNhanGiamTruRender] = useState([]);
  const [dataGhiChu, setDataGhiChu] = useState([]);
  const [dataNguyenNhanGiamTru, setDataNguyenNhanGiamTru] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const [tongTienDuyet, setTongTienDuyet] = useState(0);
  const [tongTienYeuCau, setTongTienYeuCau] = useState(0);
  const [tongTienGiamTru, setTongTienGiamTru] = useState(0);

  const [listChiPhiQuyenLoiYeuCau, setListChiPhiQuyenLoiYeuCau] = useState([]);
  const [danhSachNguyenTe, setDanhSachNguyenTe] = useState([]);
  const [dataDanhMucBoiThuongConNguoi, setDataDanhMucBoiThuongConNguoi] = useState([]);
  const [dataQuyenLoiGocFilter, setDataQuyenLoiGocFilter] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [arrMaBenh, setArrMaBenh] = useState([]);
  const [listSuKienBaoHiem, setListSuKienBaoHiem] = useState([]);

  const getDefaultFormValue = () => {
    try {
      return {
        gioVaoVien: thongTinLan?.gio_vv ? moment(thongTinLan?.gio_vv, 'HH:mm').toDate() : new Date(),
        ngayTaoLanBl: thongTinLan?.ngay_ht ? moment(thongTinLan?.ngay_ht, 'DD/MM/YYYY').toDate() : new Date(),
        gioRaVien: thongTinLan?.gio_rv ? moment(thongTinLan?.gio_rv, 'HH:mm').toDate() : new Date(),
        ngayVaoVien: thongTinLan?.ngay_vv ? moment(thongTinLan?.ngay_vv, 'DD/MM/YYYY').toDate() : new Date(),
        ngayRaVien: thongTinLan?.ngay_rv ? moment(thongTinLan?.ngay_rv, 'DD/MM/YYYY').toDate() : new Date(),
        soTienYeuCau: thongTinLan?.tien_yc ? thongTinLan?.tien_yc : '0',
        nguyenTeTienYeuCau: thongTinQuyenLoi?.nt_tien_yc ? thongTinQuyenLoi?.nt_tien_yc : '',
        quyenLoiBaoHiem: type === TYPE_INIT_FORM.EDIT ? thongTinQuyenLoi?.lh_nv : '',
        hinhThucDieuTri: thongTinQuyenLoi?.hinh_thuc ? thongTinQuyenLoi?.hinh_thuc : '',
        nhomNguyenNhan: thongTinQuyenLoi?.nhom_nguyen_nhan ? thongTinQuyenLoi?.nhom_nguyen_nhan : '',
        lhnv: '',
        tyGia: '',
        soTienQuyDoi: '',
        chanDoan: thongTinQuyenLoi?.chan_doan ? thongTinQuyenLoi?.chan_doan : '',
        maBenhICD: thongTinQuyenLoi?.ma_benh ? thongTinQuyenLoi?.ma_benh : '',
        hauQuaTaiNan: thongTinLan?.hau_qua_ct ? thongTinLan?.hau_qua_ct : '',
        nguyenNhanXayRaTaiNan: thongTinLan?.nguyen_nhan_tnan ? thongTinLan?.nguyen_nhan_tnan : '',
        noiXayRaTaiNan: thongTinLan?.noi_xr ? thongTinLan?.noi_xr : '',
        ngayXayRaTaiNan: thongTinLan && thongTinLan?.ngay_xr > 0 ? moment(thongTinLan.ngay_xr, 'YYYYMMDD').toDate() : new Date(),
        soLanTrenNgayYeuCau: thongTinQuyenLoi?.so_ngay_yc ? thongTinQuyenLoi?.so_ngay_yc : 1,
        soLanTrenNgayDuyet: thongTinQuyenLoi?.so_ngay_duyet ? thongTinQuyenLoi?.so_ngay_duyet : 1,
        nhomSuKien: thongTinLan ? thongTinLan?.su_kien_bh : '',
      };
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const nhomNguyenNhan = watch('nhomNguyenNhan');
  const hinhThucDieuTri = watch('hinhThucDieuTri');
  const quyenLoiBaoHiem = watch('quyenLoiBaoHiem');
  const ngayXayRaTaiNan = watch('ngayXayRaTaiNan');
  const nguyenTeTienYeuCau = watch('nguyenTeTienYeuCau');
  const gioVaoVien = watch('gioVaoVien');
  const ngayVaoVien = watch('ngayVaoVien');
  const gioRaVien = watch('gioRaVien');
  const ngayRaVien = watch('ngayRaVien');
  const nhomSuKien = watch('nhomSuKien');

  useEffect(() => {
    if (dsNguyenTe) {
      setDanhSachNguyenTe(JSON.parse(JSON.stringify(dsNguyenTe)));
    }
    if (danhMucBoiThuongConNguoi) {
      setDataDanhMucBoiThuongConNguoi(JSON.parse(JSON.stringify(danhMucBoiThuongConNguoi)));
    }
    if (thongTinQuyenLoi?.ma_benh) {
      let maBenhSelected = thongTinQuyenLoi?.ma_benh.split(';');
      setArrMaBenh(maBenhSelected);
    }
    //init danh sách sự kiện bảo hiểm
    let newArrData = JSON.parse(JSON.stringify(danhSachSuKienBaoHiem));
    newArrData.map((e, index) => {
      newArrData[index].isChecked = false;
      if (nhomSuKien && nhomSuKien !== '') {
        let arrHm = nhomSuKien.split(',');
        arrHm.map((x) => {
          if (x == e.bt) {
            newArrData[index].isChecked = true;
          }
        });
      }
    });
    setListSuKienBaoHiem([...newArrData]);
  }, []);

  const onAddMoreChiPhiQuyenLoiYeuCau = (value) => {
    let newBaseData = [];
    if (value.length > 0) {
      value.map((itemValue) => {
        let tonTai = false;
        listChiPhiQuyenLoiYeuCau.map((itemChiPhi) => {
          if (itemChiPhi.loai_ct === itemValue.ma) {
            tonTai = true;
            newBaseData.push(itemChiPhi);
          }
        });
        if (!tonTai) newBaseData.push(itemValue);
      });
      setListChiPhiQuyenLoiYeuCau(newBaseData);
    }
  };

  useEffect(() => {
    if (type === 'EDIT') {
      if (listChiPhiQuyenLoiYeuCau.length > 0 && dataNguyenNhanGiamTru.length > 0) {
        listChiPhiQuyenLoiYeuCau.forEach((x, index) => {
          if (!Array.isArray(x.nguyen_nhan_giam)) {
            listChiPhiQuyenLoiYeuCau[index].nguyen_nhan_giam = x.nguyen_nhan_giam?.split(',');
          }
        });
        listChiPhiQuyenLoiYeuCau.forEach((x, index) => {
          let newArr = [];
          if (x.nguyen_nhan_giam !== undefined) {
            x.nguyen_nhan_giam.forEach((y) => {
              dataNguyenNhanGiamTru.forEach((z) => {
                if (y === z.ma) {
                  newArr.push(z);
                }
              });
            });
          }
          listChiPhiQuyenLoiYeuCau[index].nguyen_nhan_giam_tru = newArr;
          if (index === selectedIndex) setListNguyenNhanGiamTruRender(newArr);
        });
      }
    }
  }, [listChiPhiQuyenLoiYeuCau, dataNguyenNhanGiamTru]);

  useEffect(() => {
    let sumTienDuyet = 0;
    let sumTienYc = 0;
    let sumTienGiam = 0;
    for (let i = 0; i < listChiPhiQuyenLoiYeuCau.length; i++) {
      sumTienDuyet += Number(listChiPhiQuyenLoiYeuCau[i].tien_duyet);
      sumTienYc += Number(listChiPhiQuyenLoiYeuCau[i].tien_yc);
      sumTienGiam += Number(listChiPhiQuyenLoiYeuCau[i].tien_giam);
    }
    setTongTienDuyet(sumTienDuyet);
    setTongTienGiamTru(sumTienGiam);
    setTongTienYeuCau(sumTienYc);
  }, [listChiPhiQuyenLoiYeuCau]);

  useEffect(() => {
    if (thongTinChiPhi.length > 0) {
      const filter = thongTinChiPhi.filter((item) => item.id_qloi === thongTinQuyenLoi?.id_qloi);
      setListChiPhiQuyenLoiYeuCau(filter);
    }
  }, [thongTinChiPhi]);

  useEffect(() => {
    if (listMaBenhRender.length > 0) {
      const listMaBenh = listMaBenhRender.map((item) => {
        return item.ma;
      });
      let maBenhICD = JSON.stringify(listMaBenh);
      setValue('maBenhICD', maBenhICD.replace(/[\[\]'"]+/g, ''), {shouldValidate: true});
      const listTenBenh = listMaBenhRender.map((item) => {
        return item.ten;
      });
      let chanDoan = JSON.stringify(listTenBenh);
      setValue('chanDoan', chanDoan.replace(/[\[\]'"]+/g, ''), {shouldValidate: true});
      let maBenhSelected = [];
      listMaBenhRender.map((e) => {
        maBenhSelected.push(e.ma);
      });
      setArrMaBenh(maBenhSelected);
    } else {
      // setMaBenh('');
      // setTenBenh('');
    }
  }, [listMaBenhRender]);

  const onPressDateConfirm = (date, setToggleDateTime, setDate, type) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const onSetValueGhiChu = (value) => {
    setGhiChuNoiBo(value.ten);
    setMaNhomGhiChu(value.ma);
    setGhiChuKhac(value.ten);
  };

  const onDeleteChiPhiYeuCau = (itemIndex) => {
    const newArr = listChiPhiQuyenLoiYeuCau.filter((item, index) => {
      return index !== itemIndex;
    });
    setListChiPhiQuyenLoiYeuCau(newArr);
  };

  useEffect(() => {
    if (hinhThucDieuTri !== 'K') {
      if (dataQuyenLoiGoc.length > 0) {
        const filter = dataQuyenLoiGoc.filter((e) => e.nhom_nguyen_nhan !== null && e.nhom_nguyen_nhan.includes(nhomNguyenNhan));
        setDataQuyenLoiGocFilter(filter);
      }
    } else setDataQuyenLoiGocFilter([]);
  }, [hinhThucDieuTri, dataQuyenLoiGoc, nhomNguyenNhan]);

  const onSubmitLuuHoSo = async (data) => {
    let arr = [];
    listChiPhiQuyenLoiYeuCau.map((e) => {
      let json = {
        loai: e.loai,
        so_ct: e.so_ct,
        tien_yc: e.tien_yc,
        ghi_chu: e.ghi_chu,
        loai_ct: e.loai_ct,
        tien_giam: e.tien_giam,
        tien_duyet: e.tien_duyet,
        nguyen_nhan_giam: e.nguyen_nhan_giam.toString(),
        nhap_nguyen_nhan: e.nhap_nguyen_nhan,
        tl_tien_duyet: e.tl_tien_duyet,
      };
      arr.push(json);
    });

    const formatDate = (value) => {
      return +moment(value).format('YYYYMMDD');
    };
    const formatHour = (value) => {
      return moment(value).format('HH:mm');
    };
    try {
      let params = {
        so_id: chiTietHoSo?.so_id,
        lan: thongTinLan?.lan || '',
        gio_vv: formatHour(gioVaoVien) || '',
        ngay_vv: formatDate(ngayVaoVien) || 0,
        gio_rv: formatHour(gioRaVien) || '',
        ngay_rv: formatDate(ngayRaVien) || 0,
        noi_xr: data.noiXayRaTaiNan,
        ngay_xr: data.nhomNguyenNhan === 'TN' ? formatDate(data.ngayXayRaTaiNan) : 0,
        hau_qua_ct: data.hauQuaTaiNan,
        nguyen_nhan_tnan: data.nguyenNhanXayRaTaiNan,
        // thông tin quyền lợi
        nhom_nguyen_nhan: data.nhomNguyenNhan,
        hinh_thuc: data.hinhThucDieuTri || '',
        lh_nv: data.quyenLoiBaoHiem,
        id_qloi: type === TYPE_INIT_FORM.EDIT ? thongTinQuyenLoi?.id_qloi : '',
        ma_benh: data.maBenhICD.replace(/,+/g, ';'),
        chan_doan: data.chanDoan.replace(/,+/g, ';'),
        ty_le_dong: '', //ko cần
        so_ngay_cho: '', //ko cần
        so_ngay_yc: data.soLanTrenNgayYeuCau || 0,
        so_ngay_duyet: data.soLanTrenNgayDuyet || 0,
        nhom_ghi_chu: maNhomGhiChu,
        ghi_chu: ghiChuNoiBo,
        ghi_chu_khac: ghiChuKhac,
        nt_tien_yc: data.nguyenTeTienYeuCau,
        trang_thai: '',
        su_kien_bh: data.nhomSuKien,
        // chi tiết quyền lợi
        arr: arr,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.THEM_LAN_BAO_LANH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (type === TYPE_INIT_FORM.EDIT) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Sửa quyền lợi thành công', 'success');
        NavigationUtil.pop();
      } else {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm mới quyền lợi bảo lãnh thành công', 'success');
        NavigationUtil.pop();
      }
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // const backAction = () => {
  //   Alert.alert('Thông báo', 'Bạn có muốn lưu thông tin hay không?', [
  //     {
  //       text: 'Huỷ',
  //       onPress: () => null,
  //       style: 'cancel',
  //     },
  //     {
  //       text: 'Đồng ý',
  //       onPress: () => NavigationUtil.pop(),
  //     },
  //   ]);
  //   return true;
  // };

  const getTenHienThi = (field, val) => {
    let name = '';
    if (field === 'nguyen_te') {
      danhSachNguyenTe.map((e) => {
        if (val === e.ma) name = e.ten;
      });
    }
    if (field === 'ql_bh') {
      dataQuyenLoiGocFilter.map((e) => {
        if (val === e.lh_nv) name = e.ten;
      });
    } else {
      dataDanhMucBoiThuongConNguoi.map((e) => {
        if (val === e.ma) name = e.ten;
      });
    }
    return name;
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'dienThoai') {
      if (errType === 'pattern') return 'Số điện thoại sai định dạng';
    }
  };

  const onSetNguyenNhanGiamTru = (arrValue = []) => {
    let arrNguyenNhanGiam = [];
    if (arrValue.length > 0) {
      arrValue.map((itemNguyenNhanGiamTru) => {
        arrNguyenNhanGiam.push(itemNguyenNhanGiamTru.ma);
      });
    }
    let newArr = listChiPhiQuyenLoiYeuCau;
    newArr.map((e, index) => {
      if (index === selectedIndex) e.nguyen_nhan_giam = arrNguyenNhanGiam;
    });
    setListChiPhiQuyenLoiYeuCau(newArr);
    setListNguyenNhanGiamTruRender(arrValue);
  };

  const getTenHienThiNhomSuKien = (value, data) => {
    let name = '';
    if (value && value !== '') {
      const filterData = data.filter((e) => e.isChecked);
      const listTenSuKien = filterData.map((item) => {
        return item.ten_sk;
      });
      let ten = JSON.stringify(listTenSuKien);
      name = ten.replace(/[\[\]'"]+/g, '');
    }
    return name;
  };

  const onSetValueSuKienBaoHiem = (arr) => {
    if (arr.length > 0) {
      let filterMaSuKien = arr.filter((e) => e.isChecked);
      const maSuKien = filterMaSuKien.map((item) => {
        return item.bt;
      });
      let ma = JSON.stringify(maSuKien);
      setValue('nhomSuKien', ma.replace(/[\[\]'"]+/g, ''));
    } else {
      setValue('nhomSuKien', '');
    }
    setListSuKienBaoHiem(arr);
  };

  //render
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  /**Thông tin lần bảo lãnh  */
  const renderTTLanBaoLanh = () => {
    return (
      <View style={styles.contentDetail}>
        <View marginHorizontal={10}>
          {/* Ngày tạo lần bảo lãnh */}
          <Controller
            control={control}
            name="ngayTaoLanBl"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined isRequired isDateTimeField isTouchableOpacity editable={false} value={moment(value).format('DD/MM/YYYY')} title={titleInput[1]} inputStyle={{color: colors.BLACK}} />
            )}
          />

          {/* Ngày giờ vào viện */}
          <View style={styles.doubleInputRowView}>
            <View style={styles.doubleInputRow}>
              <Controller
                control={control}
                name="gioVaoVien"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDateTimeField
                    isTouchableOpacity
                    editable={false}
                    value={moment(value).format('HH:mm')}
                    title={titleInput[1]}
                    onPress={() => setToggleGioVaoVien(true)}
                    error={errors.gioVaoVien && getErrMessage('gioVaoVien', errors.gioVaoVien.type)}
                    inputStyle={{color: colors.BLACK}}
                  />
                )}
              />
              {renderDateTimeComp(toggleGioVaoVien, setToggleGioVaoVien, (value) => setValue('gioVaoVien', value), gioVaoVien, 'time', null, null, 0)}
            </View>
            <View style={styles.doubleInputRow}>
              <Controller
                control={control}
                name="ngayVaoVien"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDateTimeField
                    isTouchableOpacity
                    editable={false}
                    value={moment(value).format('DD/MM/YYYY')}
                    title={titleInput[2]}
                    onPress={() => setToggleNgayVaoVien(true)}
                    error={errors.ngayVaoVien && getErrMessage('ngayVaoVien', errors.ngayVaoVien.type)}
                    inputStyle={{color: colors.BLACK}}
                  />
                )}
              />
              {renderDateTimeComp(toggleNgayVaoVien, setToggleNgayVaoVien, (value) => setValue('ngayVaoVien', value), ngayVaoVien, 'date', null, new Date(), 0)}
            </View>
          </View>

          {/* Ngày giờ ra viện */}
          <View style={styles.doubleInputRowView}>
            <View style={styles.doubleInputRow}>
              <Controller
                control={control}
                name="gioRaVien"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDateTimeField
                    isTouchableOpacity
                    editable={false}
                    value={moment(value).format('HH:mm')}
                    title={titleInput[3]}
                    onPress={() => setToggleGioRaVien(true)}
                    error={errors.gioRaVien && getErrMessage('gioRaVien', errors.gioRaVien.type)}
                    inputStyle={{color: colors.BLACK}}
                  />
                )}
              />
              {renderDateTimeComp(toggleGioRaVien, setToggleGioRaVien, (value) => setValue('gioRaVien', value), gioRaVien, 'time', null, null, 0)}
            </View>
            <View style={styles.doubleInputRow}>
              <Controller
                control={control}
                name="ngayRaVien"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDateTimeField
                    isTouchableOpacity
                    editable={false}
                    value={moment(value).format('DD/MM/YYYY')}
                    title={titleInput[4]}
                    onPress={() => setToggleNgayRaVien(true)}
                    error={errors.ngayRaVien && getErrMessage('ngayRaVien', errors.ngayRaVien.type)}
                    inputStyle={{color: colors.BLACK}}
                  />
                )}
              />
              {renderDateTimeComp(toggleNgayRaVien, setToggleNgayRaVien, (value) => setValue('ngayRaVien', value), ngayRaVien, 'date', null, null, 0)}
            </View>
          </View>

          <Controller
            control={control}
            name="nhomSuKien"
            rules={{
              required: false,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isDropdown
                editable={false}
                isTouchableOpacity
                title="Nhóm sự kiện bảo hiểm"
                inputStyle={{color: colors.BLACK}}
                onPress={() => refModalChonNhomSuKienBaoHiem.current.show()}
                value={getTenHienThiNhomSuKien(value, listSuKienBaoHiem)}
                placeholder="Chọn nhóm sự kiện"
              />
            )}
          />
        </View>
      </View>
    );
  };

  /**Thông tin quyền lợi bảo lãnh  */
  const renderTTQuyenLoiBaoLanh = () => {
    return (
      <View style={styles.contentDetail}>
        <View marginHorizontal={10}>
          {/* Nguyên nhân */}
          <Controller
            control={control}
            name="nhomNguyenNhan"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                isDropdown
                isTouchableOpacity
                editable={false}
                value={getTenHienThi('nhom_nn', value)}
                title={titleInputQuyenLoiBaoLanh[0]}
                placeholder={titleInputQuyenLoiBaoLanh[0]}
                onPress={() => refModalNhomNguyenNhan.current.show()}
                error={errors.nhomNguyenNhan && getErrMessage('nhomNguyenNhan', errors.nhomNguyenNhan.type)}
                inputStyle={{color: colors.BLACK}}
              />
            )}
          />

          {/* Hình thức */}
          <Controller
            control={control}
            name="hinhThucDieuTri"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                isDropdown
                isTouchableOpacity
                editable={false}
                value={getTenHienThi('ht_dieu_tri', value)}
                title={titleInputQuyenLoiBaoLanh[1]}
                placeholder={titleInputQuyenLoiBaoLanh[1]}
                onPress={() => refModalHinhThucDieuTri.current.show()}
                error={errors.hinhThucDieuTri && getErrMessage('hinhThucDieuTri', errors.hinhThucDieuTri.type)}
                inputStyle={{color: colors.BLACK}}
              />
            )}
          />

          {/* Quyền lợi bảo hiểm */}

          <Controller
            control={control}
            name="quyenLoiBaoHiem"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                isDropdown
                isTouchableOpacity
                editable={false}
                value={getTenHienThi('ql_bh', value)}
                title={titleInputQuyenLoiBaoLanh[2]}
                placeholder={titleInputQuyenLoiBaoLanh[2]}
                onPress={() => refModalQuyenLoi.current.show()}
                error={errors.quyenLoiBaoHiem && getErrMessage('quyenLoiBaoHiem', errors.quyenLoiBaoHiem.type)}
                inputStyle={{color: colors.BLACK}}
              />
            )}
          />
          <TouchableOpacity style={styles.btnShowQuyenLoi} onPress={() => refModalShowQLChiTietGCN.current.show()}>
            <Icon.MaterialCommunityIcons name="file-search-outline" color={colors.PRIMARY} size={20} />
            <Text style={{color: colors.PRIMARY, marginLeft: 5}}>Xem quyền lợi chi tiết GCN bảo hiểm</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  // render nếu nhóm nguyên nhân được chọn là tai nạn
  const renderNguyenNhanTaiNan = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={{marginHorizontal: 10}}>
          <Text style={styles.subHeaderTitle}>(*) Nguyên nhân xảy ra tai nạn </Text>
          <View>
            <Controller
              control={control}
              name="ngayXayRaTaiNan"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  isDateTimeField
                  isTouchableOpacity
                  editable={false}
                  value={moment(value).format('DD/MM/YYYY')}
                  title={titleInputNguyenNhanTaiNan[0]}
                  placeholder={titleInputNguyenNhanTaiNan[0]}
                  onPress={() => setToggleNgayTaiNan(true)}
                  error={errors.ngayXayRaTaiNan && getErrMessage('ngayXayRaTaiNan', errors.ngayXayRaTaiNan.type)}
                  inputStyle={{color: colors.BLACK}}
                />
              )}
            />
            {renderDateTimeComp(toggleNgayTaiNan, setToggleNgayTaiNan, (value) => setValue('ngayXayRaTaiNan', value), ngayXayRaTaiNan, 'date', null, new Date(), 0)}
          </View>
          <Controller
            control={control}
            name="noiXayRaTaiNan"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                value={value}
                onChangeText={onChange}
                title={titleInputNguyenNhanTaiNan[1]}
                placeholder={titleInputNguyenNhanTaiNan[1]}
                error={errors.noiXayRaTaiNan && getErrMessage('noiXayRaTaiNan', errors.noiXayRaTaiNan.type)}
                inputStyle={{color: colors.BLACK}}
              />
            )}
          />

          <Controller
            control={control}
            name="nguyenNhanXayRaTaiNan"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                value={value}
                onChangeText={onChange}
                title={titleInputNguyenNhanTaiNan[2]}
                placeholder={titleInputNguyenNhanTaiNan[2]}
                error={errors.nguyenNhanXayRaTaiNan && getErrMessage('nguyenNhanXayRaTaiNan', errors.nguyenNhanXayRaTaiNan.type)}
                inputStyle={{color: colors.BLACK}}
              />
            )}
          />
          <Controller
            control={control}
            name="hauQuaTaiNan"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                value={value}
                onChangeText={onChange}
                title={titleInputNguyenNhanTaiNan[3]}
                placeholder={titleInputNguyenNhanTaiNan[3]}
                error={errors.hauQuaTaiNan && getErrMessage('hauQuaTaiNan', errors.hauQuaTaiNan.type)}
                inputStyle={{color: colors.BLACK}}
              />
            )}
          />
        </View>
      </View>
    );
  };
  // render thông tin thanh toán - kq từ api
  const renderThongTinThanhToan = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={{marginHorizontal: 10}}>
          <View style={styles.doubleInputRowView}>
            <View style={styles.doubleInputRow}>
              <TextInputOutlined
                disabled={true}
                editable={false}
                title={titleInputTTThanhToan[0]}
                inputStyle={styles.inputDisableEdit}
                containerStyle={styles.inputContainer}
                value={quyenLoiBH?.so_lan_ngay_con?.toString() || ''}
                // error={inputErr[0]}
              />
            </View>
            <View style={styles.doubleInputRow}>
              <TextInputOutlined
                disabled={true}
                editable={false}
                keyboardType="numeric"
                title={titleInputTTThanhToan[1]}
                inputStyle={styles.inputDisableEdit}
                containerStyle={styles.inputContainer}
                value={quyenLoiBH?.tien_lan_ngay?.toString() || ''}
                // error={inputErr[0]}
              />
            </View>
          </View>
          <View style={styles.doubleInputRowView}>
            <View style={styles.doubleInputRow}>
              <TextInputOutlined
                disabled={true}
                editable={false}
                keyboardType="numeric"
                title={titleInputTTThanhToan[2]}
                inputStyle={styles.inputDisableEdit}
                containerStyle={styles.inputContainer}
                value={quyenLoiBH?.tien_nam_con?.toString() || ''}
                // error={inputErr[0]}
              />
            </View>
            <View style={styles.doubleInputRow}>
              <TextInputOutlined
                disabled={true}
                editable={false}
                value={'0'}
                title={titleInputTTThanhToan[3]}
                inputStyle={styles.inputDisableEdit}
                containerStyle={styles.inputContainer}
                // error={inputErr[0]}
              />
            </View>
          </View>
          <View style={styles.doubleInputRowView}>
            <View style={styles.doubleInputRow}>
              <TextInputOutlined
                disabled={true}
                editable={false}
                value={'0'}
                title={titleInputTTThanhToan[4]}
                inputStyle={styles.inputDisableEdit}
                containerStyle={styles.inputContainer}
                // error={inputErr[0]}
              />
            </View>
            <View style={styles.doubleInputRow}>
              <TextInputOutlined
                disabled={true}
                editable={false}
                value={'100'}
                title={titleInputTTThanhToan[5]}
                inputStyle={styles.inputDisableEdit}
                containerStyle={styles.inputContainer}
                // error={inputErr[0]}
              />
            </View>
          </View>
        </View>
      </View>
    );
  };

  // render chẩn đoán bệnh
  const renderChanDoanBenh = () => {
    return (
      <View marginHorizontal={10}>
        <Controller
          control={control}
          name="maBenhICD"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              isDropdown
              isRequired
              isTouchableOpacity
              editable={false}
              value={value}
              title={titleInputNguyenNhanTaiNan[4]}
              placeholder={titleInputNguyenNhanTaiNan[4]}
              onPress={() => refModalMaBenhICD.current.show()}
              error={errors.maBenhICD && getErrMessage('maBenhICD', errors.maBenhICD.type)}
              inputStyle={{color: colors.BLACK}}
            />
          )}
        />
        <Controller
          control={control}
          name="chanDoan"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              isRequired
              multiline
              value={value}
              title={titleInputNguyenNhanTaiNan[5]}
              placeholder={titleInputNguyenNhanTaiNan[5]}
              error={errors.chanDoan && getErrMessage('chanDoan', errors.chanDoan.type)}
              inputStyle={{color: colors.BLACK}}
              onChangeText={onChange}
            />
          )}
        />

        <View style={styles.doubleInputRowView}>
          <View style={styles.doubleInputRow}>
            <Controller
              control={control}
              name="soLanTrenNgayYeuCau"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  keyboardType="numeric"
                  title={titleInputNguyenNhanTaiNan[6]}
                  placeholder={titleInputNguyenNhanTaiNan[6]}
                  error={errors.soLanTrenNgayYeuCau && getErrMessage('soLanTrenNgayYeuCau', errors.soLanTrenNgayYeuCau.type)}
                  inputStyle={{color: colors.BLACK}}
                  onChangeText={onChange}
                />
              )}
            />
          </View>
          <View style={styles.doubleInputRow}>
            <Controller
              control={control}
              name="soLanTrenNgayDuyet"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  keyboardType="numeric"
                  title={titleInputNguyenNhanTaiNan[7]}
                  placeholder={titleInputNguyenNhanTaiNan[7]}
                  error={errors.soLanTrenNgayYeuCau && getErrMessage('soLanTrenNgayDuyet', errors.soLanTrenNgayDuyet.type)}
                  inputStyle={{color: colors.BLACK}}
                  onChangeText={onChange}
                />
              )}
            />
          </View>
        </View>
        <Controller
          control={control}
          name="nguyenTeTienYeuCau"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              isDropdown
              isTouchableOpacity
              editable={false}
              isRequired
              keyboardType="numeric"
              value={getTenHienThi('nguyen_te', value)}
              placeholder="Nguyên tệ"
              title="Nguyên tệ"
              onPress={() => refModalNguyenTe.current.show()}
              inputStyle={{color: colors.BLACK}}
              error={errors.nguyenTeTienYeuCau && getErrMessage('nguyenTeTienYeuCau', errors.nguyenTeTienYeuCau.type)}
            />
          )}
        />

        {/* <View flexDirection="row">
          <View flex={1} marginRight={10}>
            <TextInputOutlined isRequired value={tyGia} placeholder="0" title="Tỷ giá so với VND" onChangeText={setTyGia} inputStyle={{color: colors.BLACK}} />
          </View>

          <View flex={1}>
            <TextInputOutlined title="Số tiền quy đổi (vnđ)" editable={false} disabled={true} keyboardType="numeric" value={1} placeholder="0" />
          </View>
        </View> */}
      </View>
    );
  };

  //render ghi chú nội bộ
  const renderGhiChuNoiBo = () => {
    return (
      <View style={styles.ghiChuView}>
        <>
          <TouchableOpacity style={{flexDirection: 'row'}} onPress={() => refModalGhiChuNoiBo.current.show()}>
            <Text style={styles.txtBtnThemChiPhi}>Ghi chú nội bộ: </Text>
            <Text style={styles.txtBamDeChon}>(Bấm để chọn)</Text>
          </TouchableOpacity>
          <TextInputOutlined
            editable={true}
            multiline={true}
            value={ghiChuNoiBo}
            containerStyle={styles.containerInputStyle}
            inputStyle={styles.inputGhiChuNoiBo}
            onChangeText={(t) => setGhiChuNoiBo(t)}
            placeholder="Nhập ghi chú"
            // error={inputErr[0]}
          />
        </>
        <>
          <Text style={styles.txtBtnThemChiPhi}>Ghi chú khác: </Text>
          <TextInputOutlined
            editable={true}
            multiline={true}
            value={ghiChuKhac}
            inputStyle={styles.inputGhiChuNoiBo}
            onChangeText={(t) => setGhiChuKhac(t)}
            containerStyle={styles.containerInputStyle}
            placeholder="Nhập ghi chú"
          />
        </>
      </View>
    );
  };

  //header title
  const HeaderTitleSection = (props) => {
    const {title} = props;
    return (
      <TouchableOpacity style={styles.headerTitleView}>
        <Text style={styles.headerTitle}>{title}</Text>
      </TouchableOpacity>
    );
  };

  const renderItem = ({item, index}) => {
    return (
      <View style={styles.chiTietItemChiPhi}>
        <Text style={styles.itemLabel}>
          Tên loại chi phí: <Text style={[styles.txtItemDetail, {fontWeight: 'bold'}]}>{item.ten_loai_chi_phi}</Text>
        </Text>
        <Text style={styles.itemLabel}>
          Số chứng từ : <Text style={[styles.txtItemDetail, {color: colors.GRAY10}]}>{item.so_ct}</Text>
        </Text>
        <View style={styles.contentRow}>
          <Text children="Số tiền yêu cầu: " style={styles.itemLabel} />
          <NumericFormat value={item.tien_yc} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.txtItemDetail, {color: colors.GRAY10}]} />} />
        </View>
        <View style={styles.contentRow}>
          <Text children="Số tiền giảm trừ: " style={styles.itemLabel} />
          <NumericFormat value={item.tien_giam} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.txtItemDetail, {color: colors.GRAY10}]} />} />
        </View>
        <View style={styles.contentRow}>
          <Text children="Số tiền duyệt: " style={styles.itemLabel} />
          <NumericFormat
            value={item.tien_duyet}
            displayType={'text'}
            thousandSeparator={true}
            renderText={(value) => <Text children={value} style={[styles.txtItemDetail, {color: colors.GRAY10}]} />}
          />
        </View>
        <View>
          <Text style={styles.itemLabel} children="Nguyên nhân giảm trừ:" />
          <View marginLeft={10}>
            {item?.nguyen_nhan_giam_tru?.length > 0 &&
              item?.nguyen_nhan_giam_tru.map((item, index) => {
                return <Text key={index} style={[styles.txtItemDetail, {fontWeight: '500'}]} children={'-' + item.ten} />;
              })}
          </View>
        </View>
        <View style={styles.contentRow}>
          <Text children="Ghi chú: " style={styles.itemLabel} />
          <Text style={[styles.txtItemDetail, {color: colors.GRAY10}]} children={item.ghi_chu} />
        </View>
      </View>
    );
  };
  // const renderRightHeader = () => {
  //   return (
  //     <TouchableOpacity onPress={handleSubmit(onSubmitLuuHoSo)} style={styles.btnLuuView}>
  //       <Text children="Lưu" style={styles.txtLuu} />
  //     </TouchableOpacity>
  //   );
  // };
  return (
    <ScreenComponent
      headerBack
      // renderRightHeader={renderRightHeader}
      dialogLoading={dialogLoading}
      headerTitle="Thêm quyền lợi bảo lãnh"
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView enableResetScrollToCoords={false} keyboardShouldPersistTaps="handled">
            <>
              <HeaderTitleSection title={headerTitle[0]} />
              {renderTTLanBaoLanh()}
            </>
            <>
              <HeaderTitleSection title={headerTitle[1]} />
              <>
                {renderTTQuyenLoiBaoLanh()}
                {nhomNguyenNhan === 'TN' && renderNguyenNhanTaiNan()}
                {renderThongTinThanhToan()}
                {renderChanDoanBenh()}
              </>
            </>

            <View style={{marginBottom: 60}}>
              <HeaderTitleSection title={headerTitle[2]} />
              <>
                <View style={styles.btnRow}>
                  {button.map((item, index) => {
                    let selected = indexView === index;
                    return (
                      <TouchableOpacity key={index} style={[styles.btnEdit, {backgroundColor: selected ? colors.PRIMARY : colors.GRAY2}]} flex={1} onPress={() => setIndexView(index)}>
                        <Icon.Feather name={item.iconName} size={16} color={selected ? colors.WHITE : colors.PRIMARY} />
                        <Text style={[styles.txtTab, {color: selected ? colors.WHITE : colors.PRIMARY}]}>{item.title}</Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
                {indexView === 0 && (
                  <>
                    <RenderFormThemChiPhiQuyenLoiYeuCau
                      selectedIndex={selectedIndex}
                      setSelectedIndex={setSelectedIndex}
                      navigation={navigation}
                      profileInfo={chiTietHoSo}
                      thongTinQuyenLoi={thongTinQuyenLoi}
                      nguyenNhanGiamTru={listNguyenNhanGiamTruRender}
                      setListNguyenNhanGiamTruRender={setListNguyenNhanGiamTruRender}
                      deleteItem={(index) => onDeleteChiPhiYeuCau(index)}
                      listChiPhiQuyenLoiYeuCau={listChiPhiQuyenLoiYeuCau}
                      setListChiPhiQuyenLoiYeuCau={setListChiPhiQuyenLoiYeuCau}
                      setIsVisibleNguyenNhanGiamTru={() => refModalNguyenNhanGiamTru.current.show()}
                      setIsVisibleThemChiPhiQuyenLoi={() => refModalThemChiPhiQuyenLoiYeuCau.current.show()}
                    />
                    {listChiPhiQuyenLoiYeuCau.length > 0 && renderGhiChuNoiBo()}
                    {/* {renderFooter()} */}
                  </>
                )}
                {indexView === 2 && (
                  <View style={{marginHorizontal: 10}}>
                    <Text style={styles.headerTitle}>Thông tin chi phí</Text>
                    <FlatList keyExtractor={(item, index) => item + index.toString()} scrollEnabled={false} data={listChiPhiQuyenLoiYeuCau} renderItem={renderItem} />
                    <View marginVertical={10}>
                      <Text>
                        Tổng cộng tiền yêu cầu: <NumericFormat value={tongTienYeuCau} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} />} />
                      </Text>
                      <Text>
                        Tổng cộng tiền yêu giảm: <NumericFormat value={tongTienGiamTru} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} />} />{' '}
                      </Text>
                      <Text>
                        Tổng cộng tiền duyệt: <NumericFormat value={tongTienDuyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} />} />{' '}
                      </Text>
                    </View>
                  </View>
                )}
                {indexView === 1 && <RenderImage setIsLoading={setIsLoading} navigation={navigation} profileInfo={chiTietHoSo} />}
              </>
            </View>
            {indexView === 0 && (
              <View style={styles.footerView}>
                <ButtonLinear title="Lưu tính toán" onPress={handleSubmit(onSubmitLuuHoSo)} />
              </View>
            )}
          </KeyboardAwareScrollView>
          <ModalChonNguyenNhan
            ref={refModalNhomNguyenNhan}
            data={dataDanhMucBoiThuongConNguoi}
            setValue={(val) => {
              if (val) {
                setValue('nhomNguyenNhan', val.ma, {shouldValidate: true});
              }
            }}
            onBackPress={() => refModalNhomNguyenNhan.current.hide()}
            value={nhomNguyenNhan}
          />
          <ModalChonHinhThucDieuTri
            data={dataDanhMucBoiThuongConNguoi}
            ref={refModalHinhThucDieuTri}
            setValue={(val) => {
              if (val) {
                setValue('hinhThucDieuTri', val.ma, {shouldValidate: true});
              }
            }}
            value={hinhThucDieuTri}
            onBackPress={() => refModalHinhThucDieuTri.current.hide()}
          />
          {/* <ModalChonQuyenLoiBH
            data={dataQuyenLoiGocFilter}
            ref={refModalQuyenLoi}
            type={type}
            dataQuyenLoiGoc={dataQuyenLoiGoc}
            setValue={(val) => {
              if (val) {
                setValue('quyenLoiBaoHiem', val.lh_nv, {shouldValidate: true});
                setQuyenLoiBH(val);
              }
            }}
            maQuyenLoi={quyenLoiBaoHiem}
            onBackPress={() => refModalQuyenLoi.current.hide()}
          /> */}
          <ModalChonQuyenLoiBaoHiem
            ref={refModalQuyenLoi}
            dataQuyenLoiGoc={dataQuyenLoiGocFilter}
            setValue={(val) => {
              if (val) {
                setValue('quyenLoiBaoHiem', val.lh_nv, {shouldValidate: true});
                setQuyenLoiBH(val);
              }
            }}
            maQuyenLoi={quyenLoiBaoHiem}
            onBackPress={() => refModalQuyenLoi.current.hide()}
          />
          <ModalMaBenh
            ref={refModalMaBenhICD}
            dataMaBenh={dataMaBenh}
            arrValue={arrMaBenh}
            setDataMaBenh={setDataMaBenh}
            setArrValue={setListMaBenhRender}
            onBackPress={() => refModalMaBenhICD.current.hide()}
            // onPressSelectItem={() => onPressSelectItem()}
          />
          <ModalThemChiPhiQuyenLoiYeuCau
            ref={refModalThemChiPhiQuyenLoiYeuCau}
            arrValue={listChiPhiQuyenLoiYeuCau}
            setValue={(value) => onAddMoreChiPhiQuyenLoiYeuCau(value)}
            onBackPress={() => refModalThemChiPhiQuyenLoiYeuCau.current.hide()}
          />
          <ModalNguyenNhanGiamTru
            selectedIndex={selectedIndex}
            ref={refModalNguyenNhanGiamTru}
            arrValue={listNguyenNhanGiamTruRender}
            dataNguyenNhanGiamTru={dataNguyenNhanGiamTru}
            setDataNguyenNhanGiamTru={setDataNguyenNhanGiamTru}
            setNguyenNhanGiamTru={onSetNguyenNhanGiamTru}
            onBackPress={() => refModalNguyenNhanGiamTru.current.hide()}
          />
          <ModalChonGhiChuNoiBo
            dataGhiChu={dataGhiChu}
            ref={refModalGhiChuNoiBo}
            setDataGhiChu={setDataGhiChu}
            setValue={(value) => onSetValueGhiChu(value)}
            nhomGhiChu={thongTinQuyenLoi?.nhom_ghi_chu || ''}
            onBackPress={() => refModalGhiChuNoiBo.current.hide()}
          />
          <ModalShowQLChiTietGCN ref={refModalShowQLChiTietGCN} soId={chiTietHoSo.so_id} onBackPress={() => refModalShowQLChiTietGCN.current.hide()} />
          <ModalNguyenTe
            ref={refModalNguyenTe}
            data={danhSachNguyenTe}
            value={nguyenTeTienYeuCau}
            setValue={(val) => {
              if (val) {
                setValue('nguyenTeTienYeuCau', val.ma, {shouldValidate: true});
              }
            }}
            onBackPress={() => refModalNguyenTe.current.hide()}
          />
          <ModalChonNhomSuKienBaoHiem
            baseData={listSuKienBaoHiem}
            value={nhomSuKien}
            setValue={onSetValueSuKienBaoHiem}
            ref={refModalChonNhomSuKienBaoHiem}
            onBackPress={() => refModalChonNhomSuKienBaoHiem.current.hide()}
          />
        </SafeAreaView>
      }
    />
  );
};

export const ThemQuyenLoiBaoLanhScreen = memo(ThemQuyenLoiBaoLanhScreenComponent, isEqual);
