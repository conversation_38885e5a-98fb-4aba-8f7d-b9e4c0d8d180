import {colors} from '@app/commons/Theme';
import {FontSize, spacing} from '@app/theme';
import {Dimensions, Platform, StyleSheet} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

const {width} = Dimensions.get('window');

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    flex: 1,
  },
  headerTitleView: {
    marginBottom: 2,
    paddingVertical: 8,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 10,
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: colors.BLUE3,
  },
  profileItemView: {
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: 5,
    paddingVertical: 5,
    marginHorizontal: 10,
    paddingHorizontal: 10,
    borderColor: colors.GRAY,
  },
  detailContent: {
    flex: 1,
    fontWeight: '500',
    color: colors.GRAY5,
  },
  contentRow: {
    marginBottom: 5,
    flexDirection: 'row',
  },
  contentCol: {
    width: (width - 20) / 3,
  },

  txtBtnThemChiPhi: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  subLabel: {
    fontSize: 16,
    marginVertical: 5,
    fontWeight: '700',
    color: colors.PRIMARY,
  },
  itemThongTinChiTietQuyenLoi: {
    marginTop: 5,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    flexDirection: 'row',
    justifyContent: 'space-between',
    // flex: 1,
    alignItems: 'center',
  },
  labelItem: {
    fontSize: 12,
    marginTop: 2,
    fontWeight: '600',
    color: colors.BLACK_03,
  },
  txtSum: {},
  btnShowNote: {
    flexDirection: 'row',
  },
  emptyView: {
    marginBottom: 10,
  },
  btnThemChiPhi: {
    flexDirection: 'row',
    paddingVertical: 10,
    marginBottom: 30,
  },
  footerView: {
    width: width,
    paddingVertical: 10,
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: spacing.tiny,
  },
  doubleBtn: {
    width: width - 20,
    marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtBtnStyles: {
    fontSize: 12,
  },
  bottomBtn: {
    flex: 1,
    height: 60,
    alignItems: 'center',
    textAlign: 'center',
  },
  price: {
    textAlign: 'right',
    color: colors.PRIMARY,
  },
  txtNhapTyGia: {
    fontSize: FontSize.size14,
    color: colors.RED1,
    textDecorationLine: 'underline',
  },
});
