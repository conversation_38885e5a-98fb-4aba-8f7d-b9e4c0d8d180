import {LOAI_TRINH_BL} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, Text, TextInputOutlined, CustomTabBar} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, ScrollView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Pdf from 'react-native-pdf';
import {RadioGroup} from 'react-native-radio-buttons-group';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {connect} from 'react-redux';
import styles from './TrinhBaoLanhStyles';

const radioData = [
  {
    id: '1',
    size: 20,
    value: 'K',
    selected: false,
    label: 'Gửi thủ công',
    color: colors.PRIMARY,
    containerStyle: {flex: 1},
    borderColor: colors.PRIMARY,
    labelStyle: {color: colors.PRIMARY, fontWeight: '600'},
  },
  {
    id: '2',
    size: 20,
    value: 'C',
    selected: true,
    label: 'Gửi tự động',
    color: colors.PRIMARY,
    containerStyle: {flex: 1},
    borderColor: colors.PRIMARY,
    labelStyle: {color: colors.PRIMARY, fontWeight: '600'},
  },
];

const TrinhBaoLanhScreenComponent = (props) => {
  console.log('TrinhBaoLanhScreenComponent');
  const route = useRoute();
  const {profileInfo, type, maMauIn, loaiTrinh, idLan} = route?.params;

  const [isLoading, setIsLoading] = useState(false);
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);

  const [urlFile, setUrlFile] = useState('');
  const [maActionApi, setMaActionApi] = useState('');
  const [pdfData, setPDFData] = useState(null);
  const [btnTabActive, setBtnTabActive] = useState(0);

  const [nhomChiTiet, setNhomChiTiet] = useState([]);
  const [lsTrinhDuyet, setLsTrinhDuyet] = useState([]);
  const [itemSelected, setItemSelected] = useState();
  const [dropdownData, setDropdownData] = useState([]);
  const [nhomFilter, setNhomFilter] = useState([]);
  const [arrNSDDuyet, setArrNSDDuyet] = useState([]);
  const [arrMaDoiTac, setArrMaDoiTac] = useState([]);
  const [arrCNDuyet, setArrCNDuyet] = useState([]);
  const [arrSTTDuyet, setArrSTTDuyet] = useState([]);
  const [arrPheDuyet, setArrPheDuyet] = useState([]);

  const [noiDungTrinh, setNoiDungTrinh] = useState('Kính trình');
  const [email, setEmail] = useState('');
  const [bt, setBt] = useState('');
  const [sendEmailType, setSendEmailType] = useState('C');
  const [inputErr, setInputErr] = useState(['', '']);
  const [filterTrangThai, setFilterTrangThai] = useState('');
  const [buttonTitle, setButtonTitle] = useState(type === 'trinh_bl' ? 'Trình bảo lãnh' : type === 'trinh_tc' ? 'Trình từ chối' : '');

  const scrollViewRef = useRef(null);
  const tabViewRef = useRef(null);

  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    if (filterTrangThai) {
      setButtonTitle('Huỷ trình');
    }
  }, [filterTrangThai]);

  useEffect(() => {
    if (maActionApi !== '' || undefined) {
      layMauInPDF();
    }
  }, [maActionApi]);

  useEffect(() => {
    if (dropdownData?.length > 0) {
      setItemSelected(dropdownData[0].value);
    }
  }, [dropdownData]);

  useEffect(() => {
    if (noiDungTrinh === '') {
      setInputErr(['Nội dung không được để trống', '']);
    } else setInputErr(['', '']);
  }, [noiDungTrinh]);

  useEffect(() => {
    if (itemSelected && nhomChiTiet?.length > 0) {
      let nhomCT = nhomChiTiet.filter((item) => item.so_id === itemSelected);
      setNhomFilter(nhomCT);
      let arrNSD = nhomCT.map((item) => {
        return item.nsd_duyet;
      });
      let arrMaDT = nhomCT.map((item) => {
        return item.ma_doi_tac_duyet;
      });
      let arrCN = nhomCT.map((item) => {
        return item.ma_chi_nhanh_duyet;
      });
      let arrStt = nhomCT.map((item) => {
        return item.stt_duyet;
      });
      let arrPD = nhomCT.map((item) => {
        return item.phe_duyet;
      });
      setArrNSDDuyet(arrNSD);
      setArrMaDoiTac(arrMaDT);
      setArrCNDuyet(arrCN);
      setArrSTTDuyet(arrStt);
      setArrPheDuyet(arrPD);
    }
  }, [itemSelected, nhomChiTiet]);

  const initData = async () => {
    await layThongTinToTrinh();
    layLichSuTrinh();
  };
  const layLichSuTrinh = async () => {
    let params = {
      so_id: profileInfo?.so_id,
      ma_doi_tac: profileInfo?.ma_doi_tac,
      nghiep_vu: 'NG',
      loai_trinh: loaiTrinh,
      ma_dt_trinh: '',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_LICH_SU_TRINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (!response.data_info.nhom) return;
      const nhom = response.data_info.nhom;
      let newArr = [];
      nhom.map((item) => {
        newArr.push({
          label: item.ten_nhom,
          value: item.so_id,
        });
      });
      setDropdownData(newArr);
      if (response.data_info.nhom_ct.length > 0) {
        setNhomChiTiet(response.data_info.nhom_ct);
      }
      if (response.data_info.ls_trinh_duyet.length > 0) {
        const dataLichSu = response.data_info.ls_trinh_duyet;
        setLsTrinhDuyet(response.data_info.ls_trinh_duyet);
        let arrBt = dataLichSu.filter((item) => item.phe_duyet == 1);
        setBt(arrBt[0].bt);
        setNoiDungTrinh(arrBt[0].nd);
        setItemSelected(arrBt[0].so_id_nhom_trinh);
        let filterTrangThai = [];
        filterTrangThai = dataLichSu.filter((item) => item.loai === loaiTrinh && item.trang_thai === 'C' && item.phe_duyet == 1);
        if (filterTrangThai.length > 0) {
          setFilterTrangThai(true);
        }
      }
      if (response.data_info.cau_hinh_email) {
        const cauHinhEmail = response.data_info.cau_hinh_email;
        setEmail(cauHinhEmail.email_nhan);
        setSendEmailType(cauHinhEmail.tu_dong);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layThongTinToTrinh = async () => {
    let params = {
      ma_mau_in: maMauIn,
      ma_doi_tac_ql: profileInfo?.ma_doi_tac_ql,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_THONG_TIN_TO_TRINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setUrlFile(response.data_info.url_file);
      setMaActionApi(response.data_info.ma_action_api);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layMauInPDF = async () => {
    setIsLoading(true);
    const params = {
      ma_mau_in: maMauIn,
      ma_doi_tac: profileInfo.ma_doi_tac,
      so_id: profileInfo?.so_id,
      url_file: urlFile,
      loai: loaiTrinh,
    };
    try {
      let response = await ESmartClaimEndpoint.exportPdfBase64(maActionApi, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let pdfData = {
        base64: response.data_info.base64_string,
        filePath: '',
      };
      setPDFData(pdfData);
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const handleTrinhBaoLanh = () => {
    if (buttonTitle === 'Trình bảo lãnh') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn muốn trình bảo lãnh không', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onActionTrinh()},
      ]);
    }
    if (buttonTitle === 'Trình từ chối') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn muốn trình từ chối lần bảo lãnh không', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onActionTrinh()},
      ]);
    }
    if (buttonTitle === 'Huỷ trình') {
      Alert.alert('Thông báo', 'Bạn có chắc chắn muốn huỷ trình bảo lãnh không', [
        {
          text: 'Huỷ',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {text: 'Đồng ý', onPress: () => onSubmitHuyTrinh()},
      ]);
    }
  };

  const onActionTrinh = () => {
    onSubmitTrinh();
  };

  // submit TRÌNH
  const onSubmitTrinh = async () => {
    setIsLoading(true);
    let params = {
      ma_doi_tac: profileInfo?.ma_doi_tac,
      so_id: profileInfo?.so_id,
      bt: null,
      nv: 'NG',
      lan: null,
      hanh_dong: null,
      loai: loaiTrinh,
      nd: noiDungTrinh,
      ma_dt_trinh: idLan,
      gui_email: sendEmailType,
      arr_nsd_duyet: arrNSDDuyet,
      arr_phe_duyet: arrPheDuyet,
      arr_stt_duyet: arrSTTDuyet,
      so_id_trinh_mau: itemSelected,
      arr_ma_doi_tac_duyet: arrMaDoiTac,
      arr_ma_chi_nhanh_duyet: arrCNDuyet,
      create_file: loaiTrinh === LOAI_TRINH_BL.TRINH_DUYET_BL ? 'ESCS_NG_TO_TRINH_DUYET_BAO_LANH' : 'ESCS_NG_TRINH_TU_CHOI_BLVP',
      remove_file: loaiTrinh === LOAI_TRINH_BL.TRINH_DUYET_BL ? 'ESCS_NG_TO_TRINH_DUYET_BAO_LANH' : 'ESCS_NG_TRINH_TU_CHOI_BLVP',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.TRINH_PHE_DUYET, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (loaiTrinh === LOAI_TRINH_BL.TRINH_TC) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Trình từ chối bảo lãnh thành công', 'success');
        NavigationUtil.pop();
      }
      if (loaiTrinh === LOAI_TRINH_BL.TRINH_DUYET_BL) {
        onSubmitLuuCauHinhGuiEmail();
        NavigationUtil.pop();
      }
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onSubmitLuuCauHinhGuiEmail = async () => {
    setIsLoading(true);
    let params = {
      so_id: profileInfo?.so_id,
      nv: 'NG',
      loai_trinh: loaiTrinh,
      tu_dong: sendEmailType,
      email_nhan: email,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LUU_CAU_HINH_EMAIL, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Trình bảo lãnh thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // submit HUỶ TRÌNH
  const onSubmitHuyTrinh = async () => {
    setIsLoading(true);
    let params = {
      ma_doi_tac: profileInfo?.ma_doi_tac,
      so_id: profileInfo?.so_id,
      bt: bt,
      ma_dt_trinh: null,
      remove_file: loaiTrinh === LOAI_TRINH_BL.TRINH_DUYET_BL ? 'ESCS_NG_TO_TRINH_DUYET_BAO_LANH' : 'ESCS_NG_TRINH_TU_CHOI_BLVP',
    };

    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_TRINH_BAO_LANH, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ trình hồ sơ bảo lãnh thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  /* RENDER */
  const renderPDFView = () => {
    if (!pdfData) return;
    return (
      <Pdf
        source={{
          uri: 'data:application/pdf;base64,' + pdfData.base64,
        }}
        onLoadComplete={(numberOfPages, filePath) => {
          let pdfDataTmp = pdfData;
          if (!pdfDataTmp) return;
          pdfDataTmp.filePath = filePath;
          setPDFData(pdfDataTmp);
        }}
        onPageChanged={(page, numberOfPages) => {}}
        onError={(error) => {
          Alert.alert('Thông báo', error);
          // console.log(error);
        }}
        onPressLink={(uri) => {
          // console.log(`Link presse: ${uri}`);
        }}
        style={styles.pdf}
      />
    );
  };

  const onPressRadioButton = (d) => {
    setSendEmailType(d.find((x) => x.selected).value);
  };

  const renderForm = () => {
    return (
      <KeyboardAwareScrollView flex={1} margin={10}>
        <Text style={styles.title} children="Chọn nhóm phê duyệt" />
        <DropdownPicker
          zIndex={8000}
          searchable={false}
          isOpen={isOpenDropdown}
          setOpen={setIsOpenDropdown}
          items={dropdownData}
          itemSelected={itemSelected}
          setItemSelected={setItemSelected}
          // onOpen={() => setOpenLoaiTaiSan(false)}
          // onChangeValue={onChangeValueDropdownPicker}
          // disabled={joinResolveData ? true : false}
          // containerStyle={{marginBottom: 10, marginHorizontal: 10}}
          // isRequired={true}
        />
        <>
          {nhomFilter.map((item, index) => {
            return (
              <View style={styles.item} key={index}>
                <View style={styles.row}>
                  <Text style={styles.label}>Tên người duyệt: </Text>
                  <Text style={styles.content}>{item?.ten_nsd_duyet}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Chức danh: </Text>
                  <Text style={styles.content}>{item?.ten_chuc_danh}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Tài khoản: </Text>
                  <Text style={[styles.content, styles.subContent]}>{item?.nsd_duyet}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Phân cấp phê duyệt: </Text>
                  <Text style={styles.content}>{item?.phan_cap}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Quyền: </Text>
                  <Text style={[styles.content, {color: colors.BLACK_03}]}>{item?.phe_duyet_chinh}</Text>
                </View>
              </View>
            );
          })}
        </>
        <TextInputOutlined
          editable={true}
          multiline={true}
          // isRequired={true}
          // error={inputErr[0]}
          value={noiDungTrinh}
          title="Nội dung trình"
          inputStyle={styles.noteInput}
          placeholder="Nhập nội dung trình"
          onChangeText={(t) => setNoiDungTrinh(t)}
        />
        <View marginTop={10} marginBottom={60}>
          <Text style={styles.title} children="Email nhận khi phê duyệt" />
          <RadioGroup radioButtons={radioData} onPress={(data) => onPressRadioButton(data)} layout={'row'} />
          {sendEmailType === 'C' && <TextInputOutlined editable={true} title="Email nhận" value={email} placeholder="Nhập email" onChangeText={(t) => setEmail(t)} />}
        </View>
      </KeyboardAwareScrollView>
    );
  };

  // const renderHistory = () => {
  //   return (
  //     <View margin={10}>
  //       <Text style={styles.title} children="Quá trình trình duyệt" />
  //       <View>
  //         <ScrollView>
  //           {lsTrinhDuyet.map((item, index) => {
  //             return (
  //               <View style={styles.item} key={index}>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Loại trình: </Text>
  //                   <Text style={styles.content}>{item?.loai_ten}</Text>
  //                 </View>
  //                 <Text style={styles.subLabel}>Thông tin người duyệt</Text>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Tên: </Text>
  //                   <Text style={styles.content}>{item?.ten_nguoi_duyet}</Text>
  //                 </View>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Chi nhánh: </Text>
  //                   <Text style={styles.content}>{item?.ten_chi_nhanh_duyet}</Text>
  //                 </View>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Quyền: </Text>
  //                   <Text style={styles.content}>{item?.phan_cap}</Text>
  //                 </View>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Trạng thái duyệt: </Text>
  //                   <Text style={styles.content}>{item?.trang_thai_ten}</Text>
  //                 </View>
  //                 <Text style={styles.subLabel}>Thông tin người trình</Text>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Tên người trình: </Text>
  //                   <Text style={styles.content}>{item?.ten_nguoi_trinh}</Text>
  //                 </View>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Chi nhánh trình: </Text>
  //                   <Text style={styles.content}>{item?.ten_chi_nhanh_trinh}</Text>
  //                 </View>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Ngày trình: </Text>
  //                   <Text style={styles.content}>{item?.ngay_trinh}</Text>
  //                 </View>
  //                 <View style={styles.row}>
  //                   <Text style={styles.label}>Nội dung: </Text>
  //                   <Text style={styles.content}>{item?.nd}</Text>
  //                 </View>
  //               </View>
  //             );
  //           })}
  //         </ScrollView>
  //       </View>
  //     </View>
  //   );
  // };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={isLoading}
      headerTitle="Trình bảo lãnh"
      renderView={
        <SafeAreaView flex={1}>
          <ScrollableTabView
            showsVerticalScrollIndicator={false}
            ref={tabViewRef}
            style={styles.container}
            initialPage={0}
            onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))}
            renderTabBar={() => <CustomTabBar />}>
            <View tabLabel="Thông tin tờ trình" style={styles.container}>
              {renderPDFView()}
            </View>
            <ScrollView tabLabel="Trình phê duyệt" style={styles.container}>
              {renderForm()}
            </ScrollView>
            {/* <ScrollView tabLabel="Lịch sử" style={styles.centerView}>
              {renderHistory()}
            </ScrollView> */}
          </ScrollableTabView>
          {+btnTabActive === 1 && (
            <View style={styles.footerView}>
              <ButtonLinear loading={isLoading} disabled={isLoading} linearStyle={styles.footerBtn} onPress={() => handleTrinhBaoLanh()} title={buttonTitle} />
            </View>
          )}
        </SafeAreaView>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
  categoryImage: state.categoryImage.data,
});

const mapDispatchToProps = {};

const TrinhBaoLanhScreenConnect = connect(mapStateToProps, mapDispatchToProps)(TrinhBaoLanhScreenComponent);
export const TrinhBaoLanhScreen = memo(TrinhBaoLanhScreenConnect, isEqual);
