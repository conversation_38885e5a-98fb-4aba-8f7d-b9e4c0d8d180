import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {HealthEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Icon, ScreenComponent, StepIndicatorComp, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useMemo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './BaoLanhVienPhiStyle';
import {BaoLanhVienPhiBuoc1Screen, BaoLanhVienPhiBuoc2Screen, BaoLanhVienPhiBuoc3Screen} from './ScreenComponents';
import moment from 'moment';

let timer;
const BaoLanhVienPhiScreenComponent = (props) => {
  console.log('BaoLanhVienPhiScreenComponent');
  const route = useRoute();
  const {page, info, title} = route?.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(page || 0);

  const [selectedItemHoSo, setSelectedItemHoSo] = useState(null);

  // thông tin người liên hệ
  const [thongTinNguoiLienHe, setThongTinNguoiLienHe] = useState({
    ten: info?.nguoi_lh || '',
    email: info?.email_nguoi_lh || '',
    d_thoai: info?.dthoai_nguoi_lh || '',
  });
  //thông tin người được hưởng
  const [thongtinNguoiDuocHuong, setThongtinNguoiDuocHuong] = useState({});

  //step 1
  const [chiTietHS, setChiTietHS] = useState(info);
  const [dataHD, setDataHD] = useState([]);

  // step 2
  const [maBV, setMaBV] = useState(info?.benh_vien || '');
  const [maNhaThuoc, setMaNhaThuoc] = useState(info?.nha_thuoc || '');
  const [nameBenhVien, setNameBenhVien] = useState(info?.benh_vien_ten || '');
  const [nameNhaThuoc, setNameNhaThuoc] = useState(info?.nha_thuoc_ten || '');
  const [tenNguoiThongBao, setTenNguoiThongBao] = useState(info?.nguoi_tb || '');
  const [emailNguoiThongBao, setEmailNguoiThongBao] = useState(info?.email_nguoi_tb || '');
  const [soDienThoaiNguoiThongBao, setSoDienThoaiNguoiThongBao] = useState(info?.dthoai_nguoi_tb || '');

  //step 3
  const [moiQuanHe, setMoiQuanHe] = useState('');
  const [maMoiQuanHe, setMaMoiQuanHe] = useState(info?.nguoi_lhla || '');
  const [isChecked, setIsChecked] = useState(false);

  const [gioThongBao, setGioThongBao] = useState(null);
  const [ngayThongBao, setNgayThongBao] = useState(null);

  const so_id = info?.so_id || '';

  useEffect(() => {
    return () => {
      if (timer) clearTimeout(timer);
    };
  });

  useEffect(() => {
    setThongtinNguoiDuocHuong(selectedItemHoSo);
  }, [selectedItemHoSo]);

  useEffect(() => {
    if (isChecked) {
      setThongTinNguoiLienHe(selectedItemHoSo);
    }
  }, [isChecked]);

  const onChangeText = (field, value) => {
    setThongTinNguoiLienHe((prev) => ({...prev, [field]: value}));
  };

  const onCapNhatHoso = async () => {
    if (!ngayThongBao) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn ngày thông báo');
    setDialogLoading(true);
    let params = {
      so_id: so_id, // thêm mới: ''/ sửa: 'so_id'
      so_hs: '',
      nguon: 'MOBILE_BLVP', // default
      loai: 'BLVP', // BLVP - default
      //b1
      so_id_dt: chiTietHS?.so_id_dt,
      so_id_hd: chiTietHS?.so_id_hd,
      so_id_hd_d: chiTietHS?.so_id_hd_d,
      ma_doi_tac_ql: chiTietHS?.ma_doi_tac_ql,
      ma_chi_nhanh_ql: chiTietHS?.ma_chi_nhanh_ql,
      //b2
      nguoi_tbla: '',
      benh_vien: maBV,
      nha_thuoc: maNhaThuoc,
      nguoi_tb: tenNguoiThongBao,
      email_nguoi_tb: emailNguoiThongBao,
      dthoai_nguoi_tb: soDienThoaiNguoiThongBao,
      //b3
      nguoi_lh: thongTinNguoiLienHe?.ten,
      email_nguoi_lh: thongTinNguoiLienHe?.email,
      dthoai_nguoi_lh: thongTinNguoiLienHe?.d_thoai,
      nguoi_lhla: isChecked ? 'BAN_THAN' : maMoiQuanHe,
      ghi_chu_nb: '', //null
      trang_thai_out: '', //null
      gio_tb: gioThongBao ? moment(gioThongBao).format('HH:mm') : '',
      ngay_tb: moment(ngayThongBao).format('YYYYMMDD'),
    };
    try {
      let response = await HealthEndpoint.submitFormThemMoiHoSoBaoLanh(AxiosConfig.ACTION_CODE.LUU_HO_SO_BAO_LANH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (params.so_id !== '') {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật hồ sơ bảo lãnh thành công', 'success');
        NavigationUtil.pop();
      } else {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm mới hồ sơ bảo lãnh thành công', 'success');
        NavigationUtil.pop();
      }
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressNextStep = () => {
    if (currentPage === 2) {
      onCapNhatHoso();
    } else setCurrentPage(currentPage + 1);
  };
  const onPressBack = () => {
    // setSelectedItemHoSo(null);
    setCurrentPage(currentPage - 1);
  };

  const memoDisableBtnNext = useMemo(() => {
    if (currentPage === 0) return selectedItemHoSo === null;
    if (currentPage === 1) return nameBenhVien === '';
  }, [nameBenhVien, selectedItemHoSo, currentPage]);

  const onPressCheckBox = () => {
    setIsChecked(!isChecked);
  };

  //render

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={title ? title : 'Bảo lãnh viện phí'}
      renderView={
        <SafeAreaView style={styles.container}>
          <StepIndicatorComp currentPosition={currentPage} labels={['Người được BH', 'Cơ sở y tế', 'Người liên hệ']} stepCount={3} />
          <KeyboardAwareScrollView>
            {currentPage === 0 && <BaoLanhVienPhiBuoc1Screen data={dataHD} setData={setDataHD} profileInfo={info} setChiTietHS={setChiTietHS} setSelectedItemHoSo={setSelectedItemHoSo} />}
            {currentPage === 1 && (
              <BaoLanhVienPhiBuoc2Screen
                maBV={maBV}
                setMaBV={setMaBV}
                profileInfo={info}
                nameBenhVien={nameBenhVien}
                nameNhaThuoc={nameNhaThuoc}
                setMaNhaThuoc={setMaNhaThuoc}
                setNameBenhVien={setNameBenhVien}
                setNameNhaThuoc={setNameNhaThuoc}
                tenNguoiThongBao={tenNguoiThongBao}
                emailNguoiThongBao={emailNguoiThongBao}
                setTenNguoiThongBao={setTenNguoiThongBao}
                setEmailNguoiThongBao={setEmailNguoiThongBao}
                soDienThoaiNguoiThongBao={soDienThoaiNguoiThongBao}
                setSoDienThoaiNguoiThongBao={setSoDienThoaiNguoiThongBao}
              />
            )}
            {currentPage === 2 && (
              <BaoLanhVienPhiBuoc3Screen
                profileInfo={info}
                isChecked={isChecked}
                moiQuanHe={moiQuanHe}
                setMoiQuanHe={setMoiQuanHe}
                onChangeText={onChangeText}
                setMaMoiQuanHe={setMaMoiQuanHe}
                onPressCheckBox={onPressCheckBox}
                thongTinNguoiLienHe={thongTinNguoiLienHe}
                thongtinNguoiDuocHuong={thongtinNguoiDuocHuong}
                setThongTinNguoiLienHe={setThongTinNguoiLienHe}
                gioThongBao={gioThongBao}
                setGioThongBao={setGioThongBao}
                ngayThongBao={ngayThongBao}
                setNgayThongBao={setNgayThongBao}
              />
            )}
          </KeyboardAwareScrollView>

          {title !== undefined && page !== 0 ? (
            <ButtonLinear title="Lưu" onPress={onCapNhatHoso} linearStyle={styles.saveBtn} />
          ) : (
            <View style={styles.footerView}>
              <TouchableOpacity activeOpacity={0.5} onPress={onPressBack} style={styles.btnBack}>
                {currentPage > 0 && (
                  <>
                    <View style={styles.iconLeftBtnView}>
                      <Icon.Ionicons name="arrow-back" size={25} color={colors.WHITE} style={styles.iconLeftBtn} />
                    </View>
                    <Text style={styles.txtBtnBottom}>Trước</Text>
                  </>
                )}
              </TouchableOpacity>
              {/* NÚT TIẾP TỤC - HOÀN THÀNH   */}

              <TouchableOpacity
                disabled={memoDisableBtnNext}
                activeOpacity={0.5}
                onPress={onPressNextStep}
                style={[styles.btnNext, {backgroundColor: memoDisableBtnNext ? colors.GRAY3 : colors.PRIMARY_08}]}>
                {<Text style={styles.txtBtnBottom}>{currentPage == 2 ? 'Hoàn thành' : 'Tiếp'}</Text>}
                <View style={[styles.iconRightBtnView, {backgroundColor: memoDisableBtnNext ? colors.GRAY10 : colors.PRIMARY_DARK_08}]}>
                  <Icon.Ionicons name={currentPage == 2 ? 'checkmark-sharp' : 'arrow-forward'} size={25} style={styles.iconRightBtn} color={colors.WHITE} />
                </View>
              </TouchableOpacity>
            </View>
          )}
        </SafeAreaView>
      }
    />
  );
};

export const BaoLanhVienPhiScreen = memo(BaoLanhVienPhiScreenComponent, isEqual);
