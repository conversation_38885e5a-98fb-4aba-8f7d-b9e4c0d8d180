import { IS_PROD, SCREEN_ROUTER_APP } from '@app/commons/Constant';
import { colors } from '@app/commons/Theme';
import { Icon, ScreenComponent, Text } from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import { dimensions, FontSize, scale, spacing, vScale } from '@app/theme';
import { FlashMessageHelper } from '@app/utils/FlashMessageHelper';
import React, { memo, useCallback } from 'react';
import isEqual from 'react-fast-compare';
import { SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { HOME_MENU_ACTION_TITLE } from './Constants';
const HomeActionsScreenComponent = props => {
  console.log('HomeActionsScreenComponent');
  const onPressIcon = (screenName, titleCha) => {
    if (screenName !== '') {
      let params = {};
      if (screenName === SCREEN_ROUTER_APP.PROFILE) {
        params.profileTitle = '<PERSON><PERSON> sơ cá nhân';
        if (titleCha === 'Giám định/bồi thường xe cơ giới') params.loaiHoSo = 'TAT_CA';
      }
      NavigationUtil.navigate(screenName, params);
    } else {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tính năng đang phát triển!');
    }
  };

  const renderItemScreen = useCallback((title, screenName, iconName, iconColor, iconType, titleCha?) => {
    let styleView = [styles.actionItem];

    return (
      <TouchableOpacity style={styleView} onPress={() => onPressIcon(screenName, titleCha)} activeOpacity={0.7}>
        {iconType === 'Ionicons' && <Icon.Ionicons name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'MaterialCommunityIcons' && <Icon.MaterialCommunityIcons name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'FontAwesome5' && <Icon.FontAwesome5 name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'Foundation' && <Icon.Foundation name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'Feather' && <Icon.Feather name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'FontAwesome' && <Icon.FontAwesome name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'MaterialIcons' && <Icon.MaterialIcons name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        {iconType === 'Fontisto' && <Icon.Fontisto name={iconName} size={25} color={iconColor} style={styles.iconActionItem} />}
        <Text style={styles.txtActionTitle}>{title}</Text>
      </TouchableOpacity>
    );
  }, []);

  return (
    <ScreenComponent
      headerBack
      headerTitle="Tính năng - Tiện ích"
      renderView={
        <SafeAreaView style={styles.container}>
          <ScrollView>
            {/* Tiện ích */}
            <View>
              <View style={styles.headerTitleView}>
                <Text style={styles.headerTitle}>Tiện ích</Text>
              </View>
              <>
                <View style={styles.listActionView}>
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[0], SCREEN_ROUTER_APP.LICH_GIAM_DINH, 'calendar-alt', colors.NEON, 'FontAwesome5')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[4], SCREEN_ROUTER_APP.PHONE_BOOK, 'phone', colors.RED3, 'FontAwesome5')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[5], SCREEN_ROUTER_APP.ANNUAL_LEAVE, 'calendar-refresh-outline', colors.NEON, 'MaterialCommunityIcons')}
                  {/* {renderItemScreen(HOME_MENU_ACTION_TITLE[6], SCREEN_ROUTER_APP.BO_SUNG_Y_KIEN, 'clipboard-pencil', colors.ORANGE1, 'Foundation')} */}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[1], SCREEN_ROUTER_APP.APPROVAL, 'check', colors.BLUE1, 'FontAwesome5')}
                </View>
                <View style={styles.listActionView}>
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[2], SCREEN_ROUTER_APP.GARA, 'car-connected', colors.GREEN2, 'MaterialCommunityIcons')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[19], SCREEN_ROUTER_APP.TRA_CUU_DICH_VU_Y_TE, 'clinic-medical', colors.RED1, 'FontAwesome5')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[3], SCREEN_ROUTER_APP.SEARCH, 'search', colors.BLUE1, 'FontAwesome5')}
                  {!IS_PROD && renderItemScreen(HOME_MENU_ACTION_TITLE[28], SCREEN_ROUTER_APP.DANH_SACH_HO_SO_GIAM_DINH_OFFLINE, 'cloud-offline-outline', colors.GRAY, 'Ionicons')}
                  {IS_PROD && renderItemScreen('', '', '', '', '')}
                </View>
              </>
            </View>
            {/* Giám định/bồi thường xe cơ giới */}
            <View>
              <View style={styles.headerTitleView}>
                <Text style={styles.headerTitle}>Giám định/bồi thường xe cơ giới</Text>
              </View>
              <View style={styles.listActionView}>
                {renderItemScreen(HOME_MENU_ACTION_TITLE[7], SCREEN_ROUTER_APP.DS_HO_SO_KHAI_BAO_TON_THAT, 'file-plus-outline', colors.GREEN, 'MaterialCommunityIcons')}
                {renderItemScreen(HOME_MENU_ACTION_TITLE[20], SCREEN_ROUTER_APP.PROFILE, 'file-tray', colors.ORANGE, 'Ionicons', 'Giám định/bồi thường xe cơ giới')}
                {renderItemScreen(HOME_MENU_ACTION_TITLE[10], SCREEN_ROUTER_APP.DS_HS_LAP_PHUONG_AN, 'file-edit-outline', colors.GREEN, 'MaterialCommunityIcons')}
                {renderItemScreen(HOME_MENU_ACTION_TITLE[11], SCREEN_ROUTER_APP.DS_HS_TINH_TOAN_BOI_THUONG, 'calculator-variant', colors.BLUE1, 'MaterialCommunityIcons')}
              </View>
              {/* <View style={styles.listActionView}>
                {renderItemScreen(HOME_MENU_ACTION_TITLE[29], SCREEN_ROUTER_APP.DS_HS_KHIEU_NAI, 'file-alert-outline', colors.RED1, 'MaterialCommunityIcons')}
                {renderItemScreen('', '', '', '', '')}
                {renderItemScreen('', '', '', '', '')}
                {renderItemScreen('', '', '', '', '')}
              </View> */}
            </View>

            {/* Bồi thường con người */}
            {!IS_PROD && (
              <View>
                <View style={styles.headerTitleView}>
                  <Text style={styles.headerTitle}>Bồi thường con người</Text>
                </View>
                <View style={styles.listActionView}>
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[7], SCREEN_ROUTER_APP.DS_HS_TIEP_NHAN_CON_NG, 'file-plus-outline', colors.GREEN, 'MaterialCommunityIcons')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[8], SCREEN_ROUTER_APP.DS_HO_SO_BAO_BAO_LANH, 'user-shield', colors.VIOLET1, 'FontAwesome5')}
                  {/* {renderItemScreen(HOME_MENU_ACTION_TITLE[20], SCREEN_ROUTER_APP.PROFILE, 'file-tray', colors.ORANGE, 'Ionicons', 'Bồi thường con người')} */}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[11], SCREEN_ROUTER_APP.DS_HS_TINH_TOAN_BT_CON_NG, 'calculator-variant', colors.BLUE1, 'MaterialCommunityIcons')}
                  {renderItemScreen('', '', '', '', '')}
                </View>
              </View>
            )}

            {/* Tiện ích khác */}
            <View>
              <View style={styles.headerTitleView}>
                <Text style={styles.headerTitle}>Tiện ích khác</Text>
              </View>
              <>
                {/* <View style={styles.listActionView}>
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[9], SCREEN_ROUTER_APP.DANH_GIA_RUI_RO, 'checkbox-multiple-marked-outline', colors.RED1, 'MaterialCommunityIcons')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[21], SCREEN_ROUTER_APP.DS_HS_CHO_THANH_TOAN_XE_OTO, 'file-check-outline', colors.GREEN, 'MaterialCommunityIcons')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[22], SCREEN_ROUTER_APP.DS_HS_CHO_THANH_TOAN_CON_NGUOI, 'file-account-outline', colors.VIOLET1, 'MaterialCommunityIcons')}
                </View> */}
                <View style={styles.listActionView}>
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[12], SCREEN_ROUTER_APP.DS_HS_GIAM_DINH, 'cash-marker', colors.BLUE1, 'MaterialCommunityIcons')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[15], SCREEN_ROUTER_APP.DS_HS_THU_DOI_NGUOI_THU_BA, 'file-tray-full', colors.RED1, 'Ionicons')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[16], SCREEN_ROUTER_APP.DS_HS_TL_THVT, 'legal', colors.GREEN, 'FontAwesome')}
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[17], SCREEN_ROUTER_APP.DS_HS_TAM_UNG_BOI_THUONG, 'account-cash-outline', colors.ORANGE1, 'MaterialCommunityIcons')}
                  {/* {renderItemScreen(HOME_MENU_ACTION_TITLE[23], SCREEN_ROUTER_APP.DS_HS_THANH_TOAN_BOI_THUONG, 'cash-outline', colors.VIOLET1, 'Ionicons')} */}
                </View>
                <View style={styles.listActionView}>
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[27], SCREEN_ROUTER_APP.DS_HS_DUYET_THAM_DINH, 'file-check-outline', colors.VIOLET, 'MaterialCommunityIcons')}
                  {renderItemScreen('', '', '', '', '')}
                  {renderItemScreen('', '', '', '', '')}
                  {renderItemScreen('', '', '', '', '')}
                  {/* {renderItemScreen(HOME_MENU_ACTION_TITLE[24], SCREEN_ROUTER_APP.DS_HS_THU_DOI_NGUOI_THU_BA, 'perm-data-setting', colors.BLUE1, 'MaterialIcons')} */}
                  {/* {renderItemScreen('', '', '', '', '')}
                  {renderItemScreen('', '', '', '', '')}
                  {renderItemScreen('', '', '', '', '')} */}
                </View>
              </>
            </View>

            {/* Contact center */}
            <View>
              <View style={styles.headerTitleView}>
                <Text style={styles.headerTitle}>Tổng đài tiếp nhận</Text>
              </View>
              <>
                <View style={styles.listActionView}>
                  {renderItemScreen(HOME_MENU_ACTION_TITLE[25], SCREEN_ROUTER_APP.DS_HO_SO_TIEP_NHAN_TON_THAT_XE_CO_GIOI, 'headphone', colors.GREEN, 'Fontisto')}
                  {/* {renderItemScreen(HOME_MENU_ACTION_TITLE[26], '', 'house-damage', colors.GREEN, 'FontAwesome5')} */}
                  {/* {renderItemScreen(HOME_MENU_ACTION_TITLE[26], SCREEN_ROUTER_APP.DS_HS_CHO_THANH_TOAN_XE_OTO, 'house-damage', colors.GREEN, 'FontAwesome5')} */}
                  {renderItemScreen('', '', '', '', '')}
                  {renderItemScreen('', '', '', '', '')}
                  {renderItemScreen('', '', '', '', '')}
                </View>
              </>
            </View>
          </ScrollView>
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  actionItem: {
    alignItems: 'center',
    borderColor: colors.GRAY,
    width: dimensions.width / 4,
    paddingVertical: vScale(15),
    borderRightWidth: 0.3,
  },
  iconActionItem: {
    marginBottom: vScale(4),
  },
  listActionView: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignContent: 'space-between',
    flexWrap: 'wrap',
    width: dimensions.width,
    alignSelf: 'center',
    borderBottomWidth: 0.3,
    borderTopWidth: 0.3,
    borderColor: colors.GRAY2,
  },
  container: {
    flex: 1,
  },
  txtActionTitle: {
    textAlign: 'center',
    fontSize: FontSize.size12,
    paddingHorizontal: spacing.tiny,
  },
  headerTitle: {
    fontSize: FontSize.size14,
    marginHorizontal: scale(spacing.small),
    marginVertical: vScale(spacing.smaller),
  },
  headerTitleView: {
    backgroundColor: colors.WHITE1,
  },
});
export const HomeActionsScreen = memo(HomeActionsScreenComponent, isEqual);
