import R from '@R';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {default as AxiosConfig, default as axiosConfig} from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {AsyncStorageProvider, logErrorTryCatch} from '@app/utils';
import {getImageNameFromUriCamera, getPhotos, saveToCameraRoll} from '@app/utils/CameraProvider';
import {cloneObject, getAllHangMucTonThat, laySttAnhLonNhatTheoMaHangMuc, laySttHangMucAnhLonNhat, laySttHangMucAnhTheoHangMucId} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {requestCameraPermissions, requestLibraryPermissions} from '@app/utils/PermisstionProvider';
import {DropdownPicker, Icon, ScreenComponent, StepIndicatorComp, SwitchComp, Text} from '@component';
import {ANH_HO_SO_GIAY_TO, APP_NAME, CATEGORY_COMMON_KEY, DATA_CONSTANT, SCREEN_ROUTER_APP, isIOS} from '@constant';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {useForm} from 'react-hook-form';
import {ActivityIndicator, Alert, BackHandler, Image, ScrollView, TouchableOpacity, View} from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import ImageResizer from 'react-native-image-resizer';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import styles from './GiamDinhChiTietOtoOfflineStyles';
import {ChupAnhOfflineOtoStep1, ChupAnhOfflineOtoStep2, ChupAnhOfflineOtoStep3, ModalCameraGiamDinhOffileOto, ModalHangMuc, ModalXemChiTietAnh, ModalXemLaiAnh, ThongKeUpload} from './components';
import {GIAY_TO_VE_XE_OTO, MAX_SO_LUONG_ANH_CHUP_HANG_MUC_KHAC, MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC} from './constant';

const GiamDinhChiTietOtoOfflineScreenComponent = (props) => {
  console.log('GiamDinhChiTietOtoOfflineScreenComponent');
  const {route, navigation, categoryCommon, appSettings} = props;
  const {hangMucAnh, loaiAnh, doiTuongDuocChupAnh, profileData} = route.params;
  let inset = useSafeAreaInsets();
  let categoryImage = profileData.nhom_hang_muc ? profileData.nhom_hang_muc : props.categoryImage;
  const isDoiTuongTaiSanXeMay = doiTuongDuocChupAnh?.loai === 'XE_MAY' && doiTuongDuocChupAnh?.nhom === 'TAI_SAN';
  const [switchOffline, setSwitchOffline] = useState(false);

  let refModalHangMuc = useRef(null);

  const [isLoading, setIsLoading] = useState(false);
  const [chiTietHoSo, setChiTietHoSo] = useState(profileData);
  const [anhHoSo, setAnhHoSo] = useState(route.params.imagesData || []);
  const [listAnhLuuTrongMay, setListAnhLuuTrongMay] = useState([]);
  const [currentPosition, setCurrentPosition] = useState(null);
  const [currentPage, setCurrentPage] = useState(loaiAnh === 'ANH_TON_THAT' ? 1 : loaiAnh === 'ANH_HO_SO' ? 2 : 0);

  const [toggleLoading, setToggleLoading] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ

  const [openOptionDoiTuongGiongDoiTuongChupAnh, setOpenOptionDoiTuongGiongDoiTuongChupAnh] = useState(false);
  const [listDoiTuongGiongDoiTuongDuocChupAnh, setListDoiTuongGiongDoiTuongDuocChupAnh] = useState([]); //lưu đôi tượng có phân loại giống đối tượng được chọn để chụp ảnh
  const [doiTuongGiongDoiTuongDuocChupAnhSelected, setDoiTuongGiongDoiTuongDuocChupAnhSelected] = useState(null); //lưu đôi tượng có phân loại giống đối tượng được chọn để chụp ảnh
  const [disableOptionDoiTuongGiongDoiTuongChupAnh, setDisableOptionDoiTuongGiongDoiTuongChupAnh] = useState(false);

  const [imageDataStep1, setImageDataStep1] = useState([]);
  const [imageDataStep2, setImageDataStep2] = useState([]); //dữ liệu kiểu : [{//thông tin menu,images}]
  const [imageDataStep3, setImageDataStep3] = useState([]); //dữ liệu kiểu : [{//thông tin menu,images}]
  const [menuImageStep1Selected, setMenuImageStep1Selected] = useState({}); //danh mục ảnh hiển thị step2 ở  pickermodal
  const [menuImageStep2Selected, setMenuImageStep2Selected] = useState({}); //danh mục ảnh hiển thị step3 ở  pickermodal
  const [menuImageStep2, setMenuImageStep2] = useState([]); //Danh mục Ảnh tổn thất

  //data của modal camera
  const [giayToDuocChon, setGiayToDuocChon] = useState(null);

  // modal chọn hạng mục
  const [timeoutId, setTimeoutId] = useState(null);
  const [hangMucTonThatRoot, setHangMucTonThatRoot] = useState(null);

  //data step 4
  const [listGaraRoot, setListGaraRoot] = useState([]);
  const [listGara, setListGara] = useState([]);
  const [dsNghiepVu, setDsNghiepVu] = useState([]);

  const [dialogLoading, setDialogLoading] = useState(false);

  const [totalImagelUpload, setTotalImageUpload] = useState([0, 0, 0]); //số lượng ảnh cần upload ở bước 1,2,3
  const [imageUploaded, setImageUploaded] = useState([0, 0, 0]); //số lượng ảnh đã upload ở bước 1,2,3

  const [mucDoSelected, setMucDoSelected] = useState(null);

  const [soLanUploadLai, setSoLanUploadLai] = useState(0); //số lần upload lại
  let refModalCameraWithVideo = useRef(null);

  let refModalXemChiTietAnh = useRef(null);
  let refModalXemLaiAnh = useRef(null);
  let toggleLoadingRef = useRef(false); //xử lý dừng upload ảnh, phải dùng useRef vì biến useState sẽ không update sau mỗi lần re-render

  let refModalAIData = useRef(null);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      lh_nv: '', //loại hình nghiệp vụ
      danh_gia_gdv: '', //đánh giá sau khi giám định
      y_kien: '', //kiến nghị giải quyết
      gara: '', //Gara
      y_kien_kh: '', //ý kiến của khách hàng
      uoc_ton_that: 0, //ước tổn thất
    },
    mode: 'onChange',
  });

  /* FUNCTION  */
  useEffect(() => {
    try {
      if (route.params.anhDaPhanLoai) {
        let {anhDaPhanLoai} = route.params;
        let imageDataStep2Tmp = cloneObject(imageDataStep2);
        let viTriCu,
          viTriMoi = -1;
        for (let i = 0; i < imageDataStep2Tmp.length; i++) {
          if (imageDataStep2Tmp[i].ma === anhDaPhanLoai[0].nhom.ma) viTriCu = i; //hạng mục cũ
          if (imageDataStep2Tmp[i].ma === anhDaPhanLoai[0].nhomMoi.ma) viTriMoi = i; //hạng mục mới
        }
        //nếu hạng mục ảnh mới chưa có trong imageDataStep2 -> push mới vào
        if (viTriMoi === -1) {
          imageDataStep2Tmp[viTriCu].images = [{path: ''}]; //xoá ảnh ở vị trí cũ
          let hangMucMoi = {};
          anhDaPhanLoai.map((item) => (item.nhom = item.nhomMoi));
          Object.assign(hangMucMoi, anhDaPhanLoai[0].nhomMoi); //lưu lại nhóm đã chọn
          hangMucMoi.images = anhDaPhanLoai;
          hangMucMoi.anhDaUpload = [];
          hangMucMoi.images.push({path: ''});
          if (hangMucMoi.tenAlias) hangMucMoi.tenAlias = hangMucMoi.ten;
          imageDataStep2Tmp.push(hangMucMoi);
        }
        //nếu không thay đổi hạng mục ảnh
        else if (viTriCu === viTriMoi) {
          imageDataStep2Tmp[viTriCu].images = anhDaPhanLoai;
          imageDataStep2Tmp[viTriCu].images.push({path: ''});
        } else if (viTriCu !== viTriMoi && viTriMoi !== -1) {
          imageDataStep2Tmp.splice(viTriCu, 1); //xoá ảnh ở vị trí cũ
          anhDaPhanLoai.map((item) => (item.nhom = item.nhomMoi)); //cập nhật lại hạng mục ảnh
          imageDataStep2Tmp[viTriMoi].images.pop(); //bỏ thằng cuối cùng
          imageDataStep2Tmp[viTriMoi].images = imageDataStep2Tmp[viTriMoi].images.concat(anhDaPhanLoai); //gộp 2 thằng vào.
          imageDataStep2Tmp[viTriMoi].images.push({path: ''}); //thêm lại thằng cuối
        }
        imageDataStep2Tmp = imageDataStep2Tmp.filter((item) => item.images.length > 1 || item.anhDaUpload.length > 0);
        setImageDataStep2([...imageDataStep2Tmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  }, [route.params]);

  useEffect(() => {
    soLanUploadLai === 1 && onPressNext();
  }, [soLanUploadLai]);

  useEffect(() => {
    if (doiTuongGiongDoiTuongDuocChupAnhSelected) {
      initData();
    }
  }, [doiTuongGiongDoiTuongDuocChupAnhSelected]);

  useEffect(() => {
    requestPermistion(); //request quyền truy cập ảnh, thư viện...
    initData();
    initHangMucTonThatBuoc2();
    //request quyền truy cập vị trí hiện tại

    //xử lý nút Back
    let backHandler;
    navigation.addListener('focus', () => (backHandler = BackHandler.addEventListener('hardwareBackPress', backAction)));
    navigation.addListener('blur', () => backHandler?.remove());
  }, []);

  const reloadInitData = () => {
    if (!switchOffline) return;
    initData();
    initHangMucTonThatBuoc2();
  };

  const initAnhLuuTrongMay = async () => {
    try {
      let fromTime = moment().startOf('day').valueOf();
      let toTime = moment().endOf('day').valueOf();
      let hoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay);
      if (hoSo && hoSo.dataAnhGiamDinh) {
        // fromTime = moment(hoSo.dataAnhGiamDinh.ngayChupAnh || moment())
        //   .startOf('day')
        //   .valueOf();
        // toTime = moment(hoSo.dataAnhGiamDinh.ngayChupAnh || moment())
        //   .startOf('day')
        //   .valueOf();
        console.log('fromTime', fromTime);
        console.log('toTime', toTime);
      }

      //lấy tất cả ảnh trong ngày từ thư viện
      setDialogLoading(true);
      let arrAnhTrongNgay = await getPhotos({first: 1000, fromTime: moment().startOf('day').valueOf(), toTime: moment().endOf('day').valueOf()});
      setDialogLoading(false);
      arrAnhTrongNgay = arrAnhTrongNgay.edges;
      if (arrAnhTrongNgay) arrAnhTrongNgay = arrAnhTrongNgay.map((item) => item.node.image);
      setListAnhLuuTrongMay([...arrAnhTrongNgay]);
      return arrAnhTrongNgay;
    } catch (error) {
      Alert.alert('Thông báo', error.code);
      logErrorTryCatch(error);
    }
  };

  const initData = async () => {
    let listAnhLuuTrongMay = [];
    if (!isIOS) listAnhLuuTrongMay = await initAnhLuuTrongMay();
    let chiTietHoSo = null;
    if (switchOffline) chiTietHoSo = await getChiTietHoSoGiamDinhOto();
    await getThumbnailDocument(chiTietHoSo, listAnhLuuTrongMay);
    // await getGaraData(null); //lấy dữ liệu data ở bước ĐÁNH GIÁ
  };
  const requestPermistion = async () => {
    await requestCameraPermissions();
    await requestQuyenTruyCapLuuAnh();

    requestCurrentLocation(
      (position) => setCurrentPosition(position),
      (error) => logErrorTryCatch(error),
    );
  };

  const requestQuyenTruyCapLuuAnh = async () => {
    let response = await requestLibraryPermissions();
    if (!response) {
      Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập quyền sử dụng thư viện để sử dụng tính năng này', [
        {
          text: 'Đồng ý',
          onPress: () => requestQuyenTruyCapLuuAnh(),
        },
      ]);
    }
  };

  const initDataBuoc4 = async () => {
    try {
      setDialogLoading(true);
      let params = {
        so_id: profileData?.ho_so?.so_id,
        so_id_doi_tuong: doiTuongDuocChupAnh?.so_id_doi_tuong,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_NGHIEP_VU_CUA_HS_BOI_THUONG, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return setDialogLoading(false); //bỏ setDialogLoading trong này do ở dưới còn gọi hàm nữa
      if (response.data_info.length === 0) return setDialogLoading(false);
      setDsNghiepVu(response.data_info);
      let filter = response.data_info.filter((e) => e.chon === 1);
      if (filter.length > 0) {
        setValue('lh_nv', filter[0].ma, {shouldValidate: true});
        getDataDanhGia(filter[0].ma); //khởi tạo dữ liệu bước ĐÁNH GIÁ
      } else {
        // setValue('lh_nv', response.data_info[0].ma, {shouldValidate: true});
        getDataDanhGia(response.data_info[0].ma); //khởi tạo dữ
      }
      if (response.data_info.length === 1) setValue('lh_nv', response.data_info[0].ma);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDataDanhGia = async (loaiHinhDuocChon) => {
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        lh_nv: loaiHinhDuocChon,
        so_id_doi_tuong: doiTuongDuocChupAnh?.so_id_doi_tuong,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_THONG_TIN_DANH_GIA_THEO_LOAI, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setValue('danh_gia_gdv', response.data_info.danh_gia_gdv, {shouldValidate: true});
      setValue('y_kien', response.data_info.kien_nghi_gq, {shouldValidate: true});
      setValue('y_kien_kh', response.data_info.y_kien_khach_hang, {shouldValidate: true});
      setValue('uoc_ton_that', response.data_info.uoc_ton_that, {shouldValidate: true});
      setValue('gara', response.data_info.ma_gara);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeSearchTextHangMuc = (textSearch) => {
    try {
      if (!textSearch.trim()) {
        setMenuImageStep2([...hangMucTonThatRoot]);
        clearTimeout(timeoutId);
        return;
      }
      clearTimeout(timeoutId);
      let timeoutIdTmp = setTimeout(() => {
        let result = [];
        /*search by text*/
        // for (let i = 0; i < categoryCommon.type1.length; i++) {
        //   if (categoryCommon.type1[i].label.toUpperCase().indexOf(textSearch.toUpperCase()) > -1) result.push(categoryCommon.type1[i]);
        // }

        // let arrTextSearch = textSearch.trim().split(' ');
        // arrTextSearch = arrTextSearch.filter((item) => item !== '');
        if (categoryCommon.type1.length === 0) Alert.alert('Thông báo', 'Danh sách hạng mục rỗng');
        let nhomHangMucFilter = [];
        if (doiTuongDuocChupAnh.nhom === 'TAI_SAN' && doiTuongDuocChupAnh.loai === 'XE_MAY') nhomHangMucFilter = categoryCommon?.listHangMucXeMay.filter((item) => item.loai === 'CHINH');
        else if (doiTuongDuocChupAnh.hang_muc === 'XE') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.loai === 'CHINH' || item.loai === 'BT_TOAN_BO');
        else if (doiTuongDuocChupAnh.hang_muc === 'TAI_SAN' || doiTuongDuocChupAnh.hang_muc === 'HANG_HOA') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.nhom === 'TAI_SAN');
        else nhomHangMucFilter = categoryCommon.type1.filter((item) => item.ma === doiTuongDuocChupAnh.hang_muc);

        const lowerCaseSearchText = textSearch?.toLowerCase();
        result = nhomHangMucFilter.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));

        // for (let i = 0; i < nhomHangMucFilter.length; i++) {
        //   if (nhomHangMucFilter[i].loai !== 'CHINH' && nhomHangMucFilter[i].loai !== 'BT_TOAN_BO') continue;
        //   let arrTenHangMuc = nhomHangMucFilter[i].ten.split(' ');
        //   let tonTai = 0; //nếu tonTai===(arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
        //   let arrTenHangMucVietTat = nhomHangMucFilter[i].ten_alias?.split(' ') || [];
        //   let tonTaiVietTat = 0; //nếu tonTai===(arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
        //   for (let j = 0; j < arrTextSearch.length; j++) {
        //     for (let k = 0; k < arrTenHangMuc.length; k++) {
        //       /*
        //       j + 1 !== tonTai : để loại trường hợp chuỗi tên hạng mục có 2 từ giống nhau
        //       ví dụ :
        //       tên hạng mục : tôi là tôi
        //       từ cần tìm : tôi là
        //       -> khi duyệt từ 'tôi' ở từ cần tìm -> 'tôi' sẽ được tính 2 lần ->  dẫn đến sai kết quả của biến tonTai
        //       //có cách khác là remove duplicate ở cả 2 mảng arrTenHangMuc và arrTextSearch r tìm -> sẽ không bị dính trường hợp trên =))
        //       */
        //       if (arrTenHangMuc[k].toUpperCase() === arrTextSearch[j].toUpperCase() && j + 1 !== tonTai) {
        //         tonTai = tonTai + 1;
        //         break;
        //       }
        //     }
        //     for (let k = 0; k < arrTenHangMucVietTat.length; k++) {
        //       if (arrTenHangMucVietTat[k].toUpperCase() === arrTextSearch[j].toUpperCase() && j + 1 !== tonTaiVietTat) {
        //         tonTaiVietTat = tonTaiVietTat + 1;
        //         break;
        //       }
        //     }
        //   }
        //   if (tonTai === arrTextSearch.length || tonTaiVietTat === arrTextSearch.length) result.push(nhomHangMucFilter[i]);
        // }
        // // console.log('result', result);
        // let soViTriDoi = 0;
        // for (let i = 0; i < result.length; i++) {
        //   let arrResultItem = result[i].ten.trim().split(' ');
        //   let soTuGiong = 0;
        //   for (let j = 0; j < arrTextSearch.length; j++) {
        //     if (j < arrResultItem.length) if (arrTextSearch[j].toUpperCase() === arrResultItem[j].toUpperCase()) soTuGiong = soTuGiong + 1;
        //   }
        //   if (soTuGiong === arrTextSearch.length && soViTriDoi < result.length) {
        //     [result[soViTriDoi], result[i]] = [result[i], result[soViTriDoi]];
        //     soViTriDoi = soViTriDoi + 1;
        //   }
        // }
        // console.log('result', result);
        setMenuImageStep2([...result]);
      }, 500);
      setTimeoutId(timeoutIdTmp);
    } catch (error) {
      Alert.alert('Thông báo tìm kiếm hạng mục', JSON.stringify(error));
    }
  };

  //GET GARA DATA
  const getGaraData = async (searchInput) => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LIST_GARA, {ten: searchInput});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const listGaraFilter = response.data_info.filter((e) => e.nv === 'XE');
      setListGara([...listGaraFilter.slice(0, 50)]);
      if (!searchInput) setListGaraRoot([...response.data_info.filter((e) => e.nv === 'XE')]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // CHECK ẢNH CHƯA UPLOAD
  const checkAnhChuaUpload = (listAnhStep) => {
    let tonTaiAnhChuaUpload = false;
    listAnhStep.forEach((itemHangMuc) => {
      itemHangMuc.images.forEach((itemAnh) => {
        if (itemAnh.path && !itemAnh.uploadThanhCong) tonTaiAnhChuaUpload = true;
      });
    });
    return tonTaiAnhChuaUpload;
  };

  //xử lý nút quay lại ở màn Chụp ảnh
  const backAction = () => {
    try {
      if (refModalCameraWithVideo?.current?.getVisible()) return refModalCameraWithVideo?.current?.hide();

      toggleLoadingRef.current = false;
      let arrCheckAnhChuaUpload = [checkAnhChuaUpload(imageDataStep1), checkAnhChuaUpload(imageDataStep2), checkAnhChuaUpload(imageDataStep3)];

      if (arrCheckAnhChuaUpload.includes(true)) {
        let detail = 'Có';
        arrCheckAnhChuaUpload[0] && (detail += ' ẢNH TOÀN CẢNH');
        arrCheckAnhChuaUpload[1] && (detail += (detail.includes('ẢNH TOÀN CẢNH') ? ',' : '') + ' ẢNH TỔN THẤT');
        arrCheckAnhChuaUpload[2] && (detail += (detail.includes('ẢNH TOÀN CẢNH') || detail.includes('ẢNH TỔN THẤT') ? ',' : '') + ' ẢNH HỒ SƠ');
        detail += ' chưa tải lên hệ thống. Bạn có muốn thoát Giám định chi tiết?';
        Alert.alert('Thông báo', detail, [
          {
            text: 'Huỷ',
          },
          {
            text: 'Đồng ý',
            onPress: () => NavigationUtil.pop(),
          },
        ]);
      } else NavigationUtil.pop();
      return true;
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      return true;
    }
  };

  //lấy ảnh thumbnail của hồ sơ
  const getThumbnailDocument = async (chiTietHoSo, listAnhLuuTrongMay) => {
    try {
      //nếu đang offline
      if (!switchOffline) {
        initDuLieuAnhBuoc1([], listAnhLuuTrongMay);
        initDuLieuAnhBuoc2([], chiTietHoSo, listAnhLuuTrongMay);
        initDuLieuAnhBuoc3([], listAnhLuuTrongMay);
        return;
      }
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, {so_id: chiTietHoSo.ho_so?.so_id});
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        setDialogLoading(false);
        initDuLieuAnhBuoc1([], listAnhLuuTrongMay);
        initDuLieuAnhBuoc2([], chiTietHoSo, listAnhLuuTrongMay);
        initDuLieuAnhBuoc3([], listAnhLuuTrongMay);
        return;
      }
      let imagesTmp = response.data_info.map((item) => {
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          ma: item.ma_file,
          ten: item.nhom_anh,
        };
        item.nhom = nhom;
        return item;
      });
      initDuLieuAnhBuoc1(imagesTmp, listAnhLuuTrongMay);
      initDuLieuAnhBuoc2(imagesTmp, chiTietHoSo, listAnhLuuTrongMay);
      initDuLieuAnhBuoc3(imagesTmp, listAnhLuuTrongMay);
      setAnhHoSo([...imagesTmp]);
      setDialogLoading(false);
    } catch (error) {
      setDialogLoading(false);
      initDuLieuAnhBuoc1([], listAnhLuuTrongMay);
      initDuLieuAnhBuoc2([], chiTietHoSo, listAnhLuuTrongMay);
      initDuLieuAnhBuoc3([], listAnhLuuTrongMay);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getChiTietHoSoGiamDinhOto = async () => {
    //lấy chi tiết hồ sơ
    let paramsProfileDetail = {
      ma_doi_tac: chiTietHoSo.ho_so?.ma_doi_tac,
      so_id: chiTietHoSo.ho_so?.so_id,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.PROFILE_DATA, paramsProfileDetail);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setChiTietHoSo(response.data_info);
      return response.data_info;
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getImageByMaHangMuc = (listAnhDaUploadLenHeThong, maHangMuc) => {
    let result = listAnhDaUploadLenHeThong.filter((item) => item.ma_file === maHangMuc && item.so_id_doi_tuong == +doiTuongDuocChupAnh.so_id_doi_tuong).sort((a, b) => b.stt - a.stt);
    return result;
  };

  const getAnhLuuTrongMayTheoMa = (listHangMucDaLuuTrongMay, maHangMuc) => {
    let result = listHangMucDaLuuTrongMay.find((item) => {
      if (item.ten !== 'Số khung xe ô tô, Số máy') return item.ma === maHangMuc || item.nhom?.ma === maHangMuc;
      else if ((maHangMuc === 'SO_KHUNG' || maHangMuc === 'SO_MAY') && item.ma[0] === 'SO_KHUNG') return true;
      return false;
    });
    return result;
  };

  const mapThuocTinhAnhLuuTrongMayVsItemAnh = (itemAnh, itemLuuTrongMay, listAnhLuuTrongMay) => {
    if (isIOS) itemAnh.path = itemLuuTrongMay.keyMapLibrary;
    else {
      //nếu là ảnh được chọn từ thư viện -> thì lấy luôn keyMapLibrary vì nó là path trỏ đến file ảnh rồi
      if (itemLuuTrongMay.keyMapLibrary.includes('file')) itemAnh.path = itemLuuTrongMay.keyMapLibrary;
      //nếu không thì tìm theo name
      else itemAnh.path = listAnhLuuTrongMay.find((itemAnhLuuTrongMay) => itemAnhLuuTrongMay.uri.includes(itemLuuTrongMay.name))?.uri || '';
    }
    itemAnh.name = itemLuuTrongMay.name;
    itemAnh.keyMapLibrary = itemLuuTrongMay.keyMapLibrary;
    return itemAnh;
  };

  const luuAnhGiamDinhVaoStorage = async () => {
    try {
      let data = {};
      let imageData = [];
      let loai = '';
      if (currentPage === 0) {
        imageData = cloneObject(imageDataStep1);
        loai = CATEGORY_COMMON_KEY.ANH_TOAN_CANH;
      } else if (currentPage === 1) {
        imageData = cloneObject(imageDataStep2);
        loai = CATEGORY_COMMON_KEY.ANH_TON_THAT;
      } else if (currentPage === 2) {
        imageData = cloneObject(imageDataStep3);
        loai = CATEGORY_COMMON_KEY.ANH_TAI_LIEU;
      }
      imageData = imageData.map((itemHangMuc) => {
        //chỉ quan tâm đến keyMapLibrary nên xoá bớt các thông tin thừa đi để cho data nhẹ bớt
        itemHangMuc.anhDaUpload = [];
        delete itemHangMuc.Code;
        delete itemHangMuc.Id;
        delete itemHangMuc.Name;
        delete itemHangMuc.Value;
        itemHangMuc.images = itemHangMuc.images.map((itemAnhChup) => {
          itemAnhChup.preView = null;
          itemAnhChup.path = '';
          return itemAnhChup;
        });
        return itemHangMuc;
      });
      data = {
        ngay: moment().toDate(),
        soId: chiTietHoSo.ho_so.so_id,
        soIdHoSoLuuTrongMay: chiTietHoSo.ho_so.soIdHoSoLuuTrongMay,
        loai: loai,
        data: imageData,
        soIdDoiTuong: doiTuongDuocChupAnh.so_id_doi_tuong || null,
        soIdDoiTuongLuuTrongMay: doiTuongDuocChupAnh.soIdDoiTuongLuuTrongMay || null,
      };
      await AsyncStorageProvider.luuDataGiamDinh(data);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  //khởi tạo ảnh step 1
  const initDuLieuAnhBuoc1 = async (listAnhDaUploadLenHeThong, listAnhLuuTrongMay) => {
    try {
      if (categoryImage.length === 0) return;
      let imageDataStep1Tmp = [];
      //Ảnh BLX, ĐKX, ĐK, Giấy chứng nhận bảo hiểm
      let temDangKiem = null;
      let anhGDV = null;
      let gcnBH = null;
      let thongBaoTaiNanVaYeuCauBoiThuong = null;
      let listGiayToConLai = [];
      let anhToanCanhDaLuuTrongStorage = await getAnhDaLuuVaoMay(0);
      for (let i = 0; i < categoryImage.length; i++) {
        if (categoryImage[i].nhom === CATEGORY_COMMON_KEY.ANH_TAI_LIEU) {
          let tmp = {...categoryImage[i]};
          let imageData = {
            path: '',
            name: '',
            nhom: {...categoryImage[i]},
          };
          tmp.images = [{...imageData}, {...imageData}];
          let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.ma);
          let anhDaLuuTrongMay = getAnhLuuTrongMayTheoMa(anhToanCanhDaLuuTrongStorage, tmp.ma);
          tmp.anhDaUpload = anhDaUploadLenHeThong;
          tmp.isShowAnhDaUpload = false;
          if (tmp.nhom_hang_muc === 'ANH_GDV') {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.icons.ic_gallery;
            } else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            anhGDV = tmp;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_1_1;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_1_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
              tmp.images[1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[1], anhDaLuuTrongMay.images[1], listAnhLuuTrongMay);
              tmp.images[1].preView = R.images.img_take_photo_step4_1_2;
            } else tmp.images[1].preView = R.images.img_take_photo_step4_1_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_SAU;
            imageDataStep1Tmp.push(tmp);
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_2_1;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_2_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
              tmp.images[1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[1], anhDaLuuTrongMay.images[1], listAnhLuuTrongMay);
              tmp.images[1].preView = R.images.img_take_photo_step4_2_2;
            } else tmp.images[1].preView = R.images.img_take_photo_step4_2_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_SAU;
            imageDataStep1Tmp.push(tmp);
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM && !isDoiTuongTaiSanXeMay) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_3_1;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_3_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
              tmp.images[1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[1], anhDaLuuTrongMay.images[1], listAnhLuuTrongMay);
              tmp.images[1].preView = R.images.img_take_photo_step4_3_2;
            } else tmp.images[1].preView = R.images.img_take_photo_step4_3_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_SAU;
            imageDataStep1Tmp.push(tmp);
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images = anhDaLuuTrongMay.images.map((item) => {
                if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
                else item.preView = R.icons.ic_gallery;
                return item;
              });
              tmp.images.push({...imageData});
            } else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            gcnBH = tmp;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM && !isDoiTuongTaiSanXeMay) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_4_3;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_4_3;
            tmp.images.pop();
            temDangKiem = tmp;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.THONG_BAO_TAI_NAN) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images = anhDaLuuTrongMay.images.map((item) => {
                if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
                else item.preView = R.icons.ic_gallery;
                return item;
              });
              tmp.images.push({...imageData});
            } else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            thongBaoTaiNanVaYeuCauBoiThuong = tmp;
          } else if (tmp.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && tmp.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images = anhDaLuuTrongMay.images.map((item) => {
                if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
                else item.preView = R.icons.ic_gallery;
                return item;
              });
              tmp.images.push({...imageData});
            } else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop();
            listGiayToConLai.push(tmp);
          }
        }
      }

      //xử lý ảnh toàn cảnh trước
      for (let i = 0; i < categoryImage.length; i++) {
        if (categoryImage[i].nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
          let tmp = {};
          tmp.images = [];
          tmp.nhom = {...categoryImage[i]};
          tmp.ten = 'Ảnh toàn cảnh';
          tmp.isShowAnhDaUpload = false;
          let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.nhom.ma, listAnhLuuTrongMay);
          let anhDaLuuTrongMay = getAnhLuuTrongMayTheoMa(anhToanCanhDaLuuTrongStorage, tmp.nhom.ma, listAnhLuuTrongMay);
          tmp.anhDaUpload = anhDaUploadLenHeThong;
          if (!isDoiTuongTaiSanXeMay) {
            for (let j = 0; j < 7; j++) tmp.images.push({path: '', name: '', nhom: tmp.nhom});
            //ẢNH TRƯỚC
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step2_1;
            } else tmp.images[0].preView = R.images.img_take_photo_step2_1;
            //ẢNH BÊN PHỤ XE TRƯỚC
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
              tmp.images[1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[1], anhDaLuuTrongMay.images[1], listAnhLuuTrongMay);
              tmp.images[1].preView = R.images.img_take_photo_step2_4_truoc;
            } else tmp.images[1].preView = R.images.img_take_photo_step2_4_truoc;
            //ẢNH BÊN PHỤ XE SAU
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[2]) tmp.images[2].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[2].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[2].keyMapLibrary) {
              tmp.images[2] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[2], anhDaLuuTrongMay.images[2], listAnhLuuTrongMay);
              tmp.images[2].preView = R.images.img_take_photo_step2_4_sau;
            } else tmp.images[2].preView = R.images.img_take_photo_step2_4_sau;
            //ẢNH SAU XE
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[3]) tmp.images[3].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[3].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[3].keyMapLibrary) {
              tmp.images[3] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[3], anhDaLuuTrongMay.images[3], listAnhLuuTrongMay);
              tmp.images[3].preView = R.images.img_take_photo_step2_2;
            } else tmp.images[3].preView = R.images.img_take_photo_step2_2;
            //ẢNH BÊN LÁI XE SAU
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[4]) tmp.images[4].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[4].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[4].keyMapLibrary) {
              tmp.images[4] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[4], anhDaLuuTrongMay.images[4], listAnhLuuTrongMay);
              tmp.images[4].preView = R.images.img_take_photo_step2_3_sau;
            } else tmp.images[4].preView = R.images.img_take_photo_step2_3_sau;
            //ẢNH BÊN LÁI XE TRƯỚC
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[5]) tmp.images[5].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[5].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[5].keyMapLibrary) {
              tmp.images[5] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[5], anhDaLuuTrongMay.images[5], listAnhLuuTrongMay);
              tmp.images[5].preView = R.images.img_take_photo_step2_3_truoc;
            } else tmp.images[5].preView = R.images.img_take_photo_step2_3_truoc;
            //ẢNH MỞ RỘNG
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[6]) tmp.images[6].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[6].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[6].keyMapLibrary) {
              tmp.images.pop(); //xoá thằng cuối cùng đi
              let arrAnhToanCanhChupThem = anhDaLuuTrongMay.images.slice(6);
              arrAnhToanCanhChupThem = arrAnhToanCanhChupThem.map((item) => {
                if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
                else item.preView = R.icons.ic_gallery;
                return item;
              });
              tmp.images = tmp.images.concat(arrAnhToanCanhChupThem);
            } else tmp.images[6].preView = R.icons.ic_gallery;
          } else {
            if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images = anhDaLuuTrongMay.images.map((item) => {
                if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
                else item.preView = R.images.img_anh_toan_canh_xe_may;
                return item;
              });
            } else {
              tmp.images.push({path: '', name: '', nhom: tmp.nhom});
              tmp.images[0].preView = R.images.img_anh_toan_canh_xe_may;
            }
          }

          imageDataStep1Tmp.push(tmp);
          break;
        }
      }

      let soKhungSoMayGrouped = {
        ten: '',
        ma: [],
        nhom: [],
        nhom_hang_muc: [],
        arrTen: [],
        images: [],
        isShowAnhDaUpload: false,
        anhDaUpload: [],
      };

      //xử lý số khung - số máy
      let khungMayTemTmp = categoryImage.filter(
        (item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG || item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY,
      );
      let anhSoKhungUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG);
      soKhungSoMayGrouped.anhDaUpload = anhSoKhungUploadLenHeThong;
      let anhDaLuuTrongMay = getAnhLuuTrongMayTheoMa(anhToanCanhDaLuuTrongStorage, DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG);
      let anhSoMayUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY);
      soKhungSoMayGrouped.anhDaUpload = soKhungSoMayGrouped.anhDaUpload.concat(anhSoMayUploadLenHeThong);
      khungMayTemTmp.map((item, index) => {
        soKhungSoMayGrouped.ten = soKhungSoMayGrouped.ten + item.ten + (index === 0 ? ', ' : '');
        soKhungSoMayGrouped.ma.push(item.ma);
        soKhungSoMayGrouped.nhom.push(item.nhom);
        soKhungSoMayGrouped.nhom_hang_muc.push(item.nhom_hang_muc);
        soKhungSoMayGrouped.arrTen.push(item.ten);
        soKhungSoMayGrouped.images.push({
          name: '',
          path: '',
          nhom: {
            loai: item.loai,
            ma: item.ma,
            ma_ct: item.ma_ct,
            nhom: item.nhom,
            nhom_doi_tuong: item.nhom_doi_tuong,
            nhom_hang_muc: item.nhom_hang_muc,
            stt: item.stt,
            ten: item.ten,
          },
          preView: '',
        });
        if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG) {
          if (anhSoKhungUploadLenHeThong && anhSoKhungUploadLenHeThong[0])
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = `data:image/gif;base64,${anhSoKhungUploadLenHeThong[0].duong_dan}`;
          else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(
              soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1],
              anhDaLuuTrongMay.images[0],
              listAnhLuuTrongMay,
            );
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = R.images.img_take_photo_step4_4_1;
          } else soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = R.images.img_take_photo_step4_4_1;
        } else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY) {
          if (anhSoMayUploadLenHeThong && anhSoMayUploadLenHeThong[0])
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = `data:image/gif;base64,${anhSoMayUploadLenHeThong[0].duong_dan}`;
          else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(
              soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1],
              anhDaLuuTrongMay.images[1],
              listAnhLuuTrongMay,
            );
            soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = R.images.img_take_photo_step4_4_2;
          } else soKhungSoMayGrouped.images[soKhungSoMayGrouped.images.length - 1].preView = R.images.img_take_photo_step4_4_2;
        }
      });
      imageDataStep1Tmp.push(soKhungSoMayGrouped);
      temDangKiem && imageDataStep1Tmp.push(temDangKiem); // TEM ĐĂNG KIỂM
      thongBaoTaiNanVaYeuCauBoiThuong && imageDataStep1Tmp.push(thongBaoTaiNanVaYeuCauBoiThuong); //THÔNG BÁO TAI NẠN VÀ YÊU CẦU BỒI THƯỜNG
      if (doiTuongDuocChupAnh.kieu_dt === 'BH') imageDataStep1Tmp = imageDataStep1Tmp.concat(listGiayToConLai);
      anhGDV && imageDataStep1Tmp.unshift(anhGDV);
      gcnBH && imageDataStep1Tmp.splice(4, 0, gcnBH);
      setImageDataStep1(imageDataStep1Tmp);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //khởi tạo danh mục chụp ảnh bước Ảnh Tổn Thất
  const initHangMucTonThatBuoc2 = () => {
    try {
      if (categoryImage.length === 0) return;
      let nhomHangMucFilter = [];
      if (doiTuongDuocChupAnh.hang_muc === 'XE') {
        if (profileData.nhom_hang_muc) {
          nhomHangMucFilter = profileData.nhom_hang_muc.filter(
            (item) =>
              (item.nhom_doi_tuong === doiTuongDuocChupAnh.nhom || (item.nhom_doi_tuong === 'XE' && doiTuongDuocChupAnh.nhom === 'TAI_SAN' && doiTuongDuocChupAnh.loai === 'XE')) &&
              item.nhom === CATEGORY_COMMON_KEY.ANH_TON_THAT,
          );
        } else nhomHangMucFilter = cloneObject(categoryCommon.type1.filter((item) => item.loai === 'CHINH' || item.loai === 'BT_TOAN_BO'));

        let indexAnhTonThatKhac = nhomHangMucFilter.findIndex((item) => item.ma === 'ANH_KHAC');
        if (indexAnhTonThatKhac !== -1) {
          nhomHangMucFilter[indexAnhTonThatKhac].tenAlias = nhomHangMucFilter[indexAnhTonThatKhac].ten;
          [nhomHangMucFilter[indexAnhTonThatKhac], nhomHangMucFilter[0]] = [nhomHangMucFilter[0], nhomHangMucFilter[indexAnhTonThatKhac]];
        }
        nhomHangMucFilter = nhomHangMucFilter.slice(0, 50);
      } else if (doiTuongDuocChupAnh.loai === 'XE_MAY') {
        nhomHangMucFilter = cloneObject(categoryCommon.listHangMucXeMay.filter((item) => item.loai === 'CHINH'));
        let indexAnhTonThatKhac = nhomHangMucFilter.findIndex((item) => item.ma === 'ANH_KHAC_XM');
        if (indexAnhTonThatKhac !== -1) {
          nhomHangMucFilter[indexAnhTonThatKhac].tenAlias = nhomHangMucFilter[indexAnhTonThatKhac].ten;
          [nhomHangMucFilter[indexAnhTonThatKhac], nhomHangMucFilter[0]] = [nhomHangMucFilter[0], nhomHangMucFilter[indexAnhTonThatKhac]];
        }
      } else {
        if (doiTuongDuocChupAnh.hang_muc === 'TAI_SAN' || doiTuongDuocChupAnh.hang_muc === 'HANG_HOA') {
          nhomHangMucFilter = cloneObject(categoryCommon.type1.filter((item) => item.nhom === 'TAI_SAN' || item.loai === 'TAI_SAN'));
          let indexAnhTonThatKhac = nhomHangMucFilter.findIndex((item) => item.ma === 'ANH_TAI_SAN_KHAC');
          if (indexAnhTonThatKhac !== -1) {
            nhomHangMucFilter[indexAnhTonThatKhac].tenAlias = nhomHangMucFilter[indexAnhTonThatKhac].ten;
            [nhomHangMucFilter[indexAnhTonThatKhac], nhomHangMucFilter[0]] = [nhomHangMucFilter[0], nhomHangMucFilter[indexAnhTonThatKhac]];
          }
        } else nhomHangMucFilter = categoryCommon.type1.filter((item) => item.ma === doiTuongDuocChupAnh.hang_muc);
      }
      setHangMucTonThatRoot(cloneObject(nhomHangMucFilter));
      setMenuImageStep2(cloneObject(nhomHangMucFilter));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // khởi tạo hạng mục ảnh tổn thất đã chụp
  const initDuLieuAnhBuoc2 = async (listAnhDaUploadLenHeThong, chiTietHoSo, listAnhLuuTrongMay) => {
    try {
      let categoryImageStep3Tmp = [];
      let anhTonThatDaLuuTrongStorage = await getAnhDaLuuVaoMay(1);
      if (chiTietHoSo) {
        let hangMucTonThat = chiTietHoSo.hang_muc_chup.filter((item) => item.loai === 'TT' && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
        //xử lý các danh mục còn lại
        for (let i = 0; i < hangMucTonThat.length; i++) {
          let tmp = {
            loai: hangMucTonThat[i].loai_hang_muc,
            ma: hangMucTonThat[i].hang_muc,
            nhom: 'ANH_TON_THAT',
            nhom_doi_tuong: hangMucTonThat[i].doi_tuong,
            ten: hangMucTonThat[i].ten_hang_muc,
            anhDaUpload: [],
          };
          let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.ma);
          let anhDaLuuTrongMay = getAnhLuuTrongMayTheoMa(anhTonThatDaLuuTrongStorage, tmp.ma);
          let imageData = {
            path: '',
            name: '',
            nhom: {
              loai: hangMucTonThat[i].loai_hang_muc,
              ma: hangMucTonThat[i].hang_muc,
              nhom_doi_tuong: hangMucTonThat[i].doi_tuong,
              ten: hangMucTonThat[i].ten_hang_muc,
            },
            muc_do: anhDaUploadLenHeThong.length > 0 ? anhDaUploadLenHeThong[0].muc_do : null,
          };
          tmp.images = [{...imageData}];
          tmp.anhDaUpload = anhDaUploadLenHeThong;
          if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
            tmp.images = anhDaLuuTrongMay.images.map((item) => {
              if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
              else item.preView = R.icons.ic_gallery;
              return item;
            });
          }
          tmp.isShowAnhDaUpload = false;
          categoryImageStep3Tmp.push(tmp);
        }
      }

      for (let i = 0; i < anhTonThatDaLuuTrongStorage.length; i++) {
        let itemHangMucDaLuuTrongStorage = anhTonThatDaLuuTrongStorage[i];
        let hangMuc = categoryImageStep3Tmp.find((itemHangMuc) => itemHangMuc.ma === itemHangMucDaLuuTrongStorage.ma);
        //nếu chưa có hạng mục
        if (!hangMuc) {
          itemHangMucDaLuuTrongStorage.images = itemHangMucDaLuuTrongStorage.images.map((item) => {
            if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
            else item.preView = R.icons.ic_gallery;
            return item;
          });
          categoryImageStep3Tmp.push(itemHangMucDaLuuTrongStorage);
        }
      }
      setImageDataStep2([...categoryImageStep3Tmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //khởi tạo categoryImage step 3
  const initDuLieuAnhBuoc3 = async (listAnhDaUploadLenHeThong, listAnhLuuTrongMay) => {
    try {
      if (categoryImage.length === 0) return;
      let setImageDataStep3Tmp = [];
      let anhGiayToDaLuuTrongStorage = await getAnhDaLuuVaoMay(2);

      //xử lý các danh mục còn lại
      for (let i = 0; i < categoryImage.length; i++) {
        if (categoryImage[i].nhom === CATEGORY_COMMON_KEY.ANH_TAI_LIEU) {
          let tmp = {...categoryImage[i]};
          let imageData = {
            path: '',
            name: '',
            nhom: {...categoryImage[i]},
          };
          tmp.images = [{...imageData}, {...imageData}];
          let anhDaUploadLenHeThong = getImageByMaHangMuc(listAnhDaUploadLenHeThong, tmp.ma);
          let anhDaLuuTrongMay = getAnhLuuTrongMayTheoMa(anhGiayToDaLuuTrongStorage, tmp.ma);
          tmp.anhDaUpload = anhDaUploadLenHeThong;
          tmp.isShowAnhDaUpload = false;
          if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_1_1;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_1_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
              tmp.images[1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[1], anhDaLuuTrongMay.images[1], listAnhLuuTrongMay);
              tmp.images[1].preView = R.images.img_take_photo_step4_1_2;
            } else tmp.images[1].preView = R.images.img_take_photo_step4_1_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.GPLX_MAT_SAU;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_2_1;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_2_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
              tmp.images[1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[1], anhDaLuuTrongMay.images[1], listAnhLuuTrongMay);
              tmp.images[1].preView = R.images.img_take_photo_step4_2_2;
            } else tmp.images[1].preView = R.images.img_take_photo_step4_2_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KY_MAT_SAU;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_3_1;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_3_1;
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[1]) tmp.images[1].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[1].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[1].keyMapLibrary) {
              tmp.images[1] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[1], anhDaLuuTrongMay.images[1], listAnhLuuTrongMay);
              tmp.images[1].preView = R.images.img_take_photo_step4_3_2;
            } else tmp.images[1].preView = R.images.img_take_photo_step4_3_2;
            tmp.images[0].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_TRUOC;
            tmp.images[1].loai = ANH_HO_SO_GIAY_TO.DANG_KIEM_MAT_SAU;
          } else if (tmp.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM) {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images[0] = mapThuocTinhAnhLuuTrongMayVsItemAnh(tmp.images[0], anhDaLuuTrongMay.images[0], listAnhLuuTrongMay);
              tmp.images[0].preView = R.images.img_take_photo_step4_4_3;
            } else tmp.images[0].preView = R.images.img_take_photo_step4_4_3;
            tmp.images.pop();
          } else {
            if (anhDaUploadLenHeThong && anhDaUploadLenHeThong[0]) tmp.images[0].preView = `data:image/gif;base64,${anhDaUploadLenHeThong[0].duong_dan}`;
            else if (anhDaLuuTrongMay?.images && anhDaLuuTrongMay.images[0].keyMapLibrary) {
              tmp.images = anhDaLuuTrongMay.images;
              tmp.images.push({...imageData});
              tmp.images = tmp.images.map((item) => {
                if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
                else item.preView = R.icons.ic_gallery;
                return item;
              });
            } else tmp.images[0].preView = R.icons.ic_gallery;
            tmp.images.pop(); //do bên trên để 2 thằng, nên phải pop ra để hiển thị 1 thằng thôi
          }
          setImageDataStep3Tmp.push(tmp);
        }
      }

      //lọc để bỏ đi SO_KHUNG, SO_MAY (số khung, số máy ở Ảnh toàn cảnh rồi)
      setImageDataStep3Tmp = setImageDataStep3Tmp.filter(
        (item) => item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY,
      );
      setImageDataStep3Tmp.map((item, index) => {
        if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE)
          [setImageDataStep3Tmp[0], setImageDataStep3Tmp[index]] = [setImageDataStep3Tmp[index], setImageDataStep3Tmp[0]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE)
          [setImageDataStep3Tmp[1], setImageDataStep3Tmp[index]] = [setImageDataStep3Tmp[index], setImageDataStep3Tmp[1]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM)
          [setImageDataStep3Tmp[2], setImageDataStep3Tmp[index]] = [setImageDataStep3Tmp[index], setImageDataStep3Tmp[2]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BAO_HIEM)
          [setImageDataStep3Tmp[3], setImageDataStep3Tmp[index]] = [setImageDataStep3Tmp[index], setImageDataStep3Tmp[3]];
        else if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM)
          [setImageDataStep3Tmp[4], setImageDataStep3Tmp[index]] = [setImageDataStep3Tmp[index], setImageDataStep3Tmp[4]];
      });

      if (isDoiTuongTaiSanXeMay)
        setImageDataStep3Tmp = setImageDataStep3Tmp.filter(
          (item) => item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM && item.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM,
        );
      //sort lại theo đúng stt
      setImageDataStep3Tmp.sort(function (a, b) {
        return a.stt - b.stt;
      });
      setImageDataStep3(cloneObject(setImageDataStep3Tmp));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //xử lý ảnh khi chụp
  //type :  0 - camera ; type : 1 - library
  const handleImage = async (imageData, menuImageDataSelected, indexOpened, type) => {
    const isAnhChonTuThuVien = type === 1;
    try {
      let imageName = '';
      let keyMapLibrary = ''; //khoá để map với ảnh đã lưu trong thư viện, IOS / ANDROID là khác nhau
      if (imageData.length === undefined) imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri
      if (!isAnhChonTuThuVien) {
        let responseSaveImage = await saveToCameraRoll(imageData.path);
        keyMapLibrary = isIOS ? responseSaveImage : imageName;
      } else keyMapLibrary = isIOS ? imageData.sourceURL : imageData.path;
      //ẢNH TOÀN CẢNH
      if (currentPage === 0) {
        let imagesTmp = imageDataStep1;
        let viTriChup = imagesTmp.findIndex((item) => item && item.ma === menuImageDataSelected.hangMucChup.ma);
        if (viTriChup === -1) {
          if (menuImageDataSelected.hangMucChup.nhom_hang_muc === 'TOAN_CANH') viTriChup = imagesTmp.findIndex((item) => item && item.nhom.nhom_hang_muc === 'TOAN_CANH');
          else if (menuImageDataSelected.hangMucChup.nhom_hang_muc === 'SO_MAY' || menuImageDataSelected.hangMucChup.nhom_hang_muc === 'SO_KHUNG')
            viTriChup = imagesTmp.findIndex((item) => item && item.ten === 'Số khung xe ô tô, Số máy');
        }
        //nếu là ảnh toàn cảnh
        if (menuImageDataSelected.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
          if (!isDoiTuongTaiSanXeMay) {
            if (menuImageDataSelected.indexOpened <= 5) {
              imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].path = imageData.path;
              imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].name = imageName;
              imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].keyMapLibrary = keyMapLibrary;
            } else {
              imagesTmp[viTriChup].images.splice(imagesTmp[viTriChup].images.length - 1, 0, {
                path: imageData.path,
                name: imageName,
                nhom: menuImageDataSelected.hangMucChup,
                keyMapLibrary,
              });
            }
          } else {
            imagesTmp[viTriChup].images.splice(imagesTmp[viTriChup].images.length - 1, 0, {
              path: imageData.path,
              name: imageName,
              nhom: menuImageDataSelected.hangMucChup,
              keyMapLibrary,
            });
          }
        }
        //nếu là ẢNH GIÁM ĐỊNH VIÊN, phải đặt ở trên điều kiện GCN BẢOH HIỂM, đặt ở dưới thì sẽ vào ĐK GDN BẢO HIỂM TRƯỚC
        else if (menuImageDataSelected.hangMucChup.nhom_hang_muc === 'ANH_GDV') {
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].path = imageData.path;
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].name = imageName;
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].keyMapLibrary = keyMapLibrary;
        }
        //nếu là GIẤY CHỨNG NHẬN BẢO HIỂM
        else if (!GIAY_TO_VE_XE_OTO.includes(menuImageDataSelected.hangMucChup.nhom_hang_muc)) {
          viTriChup = imagesTmp.findIndex((item) => item.ma === menuImageDataSelected.hangMucChup.ma);
          if (!isAnhChonTuThuVien) {
            imagesTmp[viTriChup].images.splice(imagesTmp[viTriChup].images.length - 1, 0, {
              path: imageData.path,
              name: imageName,
              nhom: menuImageDataSelected.hangMucChup,
              keyMapLibrary,
            });
          } else {
            imageData.map((image) => {
              imageName = getImageNameFromUriCamera(image.path); //lấy ra tên ảnh từ uri
              keyMapLibrary = isIOS ? image.sourceURL : image.path;
              imagesTmp[viTriChup].images.splice(imagesTmp[viTriChup].images.length - 1, 0, {
                path: image.path,
                name: imageName,
                nhom: menuImageDataSelected.hangMucChup,
                keyMapLibrary,
              });
            });
          }
        }
        //nếu là số khung - số máy - bằng lái xe, đăng ký xe, dăng kiểm
        else {
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].path = imageData.path;
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].name = imageName;
          imagesTmp[viTriChup].images[menuImageDataSelected.indexOpened].keyMapLibrary = keyMapLibrary;
        }
        setImageDataStep1([...imagesTmp]);
      }
      //ẢNH TỔN THẤT
      else if (currentPage === 1) {
        let imageDataStep2Tmp = imageDataStep2;
        let daTonTai = false;
        let tenAlias = null; //tên alias của hạng mục khác
        if (menuImageStep2Selected.ma.includes('ANH_KHAC') || menuImageStep2Selected.ma.includes('ANH_TAI_SAN_KHAC')) tenAlias = menuImageStep2Selected.tenAlias || menuImageStep2Selected.ten;
        imageDataStep2Tmp = imageDataStep2Tmp.map((item, index) => {
          if (
            (!menuImageDataSelected.ma.includes('ANH_KHAC') && !menuImageDataSelected.ma.includes('ANH_TAI_SAN_KHAC') && item.ma === menuImageDataSelected.ma) || // trường hợp hạng mục THUỘC BỘ MÃ
            ((item.ma.includes('ANH_KHAC') || item.ma.includes('ANH_TAI_SAN_KHAC')) && item.tenAlias === menuImageStep2Selected.tenAlias && item.ma === menuImageStep2Selected.ma) //trường hợp hạng mục khác
          ) {
            daTonTai = true;
            if (!item.images) item.images = []; //nếu chưa có mảng image thì khởi tạo
            if (item.isAIData && item.images.length === 2 && item.images[0].path === '') {
              item.images = [
                {
                  ...item.images[0],
                  path: imageData.path,
                  name: imageName,
                  nhom: {...menuImageDataSelected},
                  tenAlias,
                  keyMapLibrary,
                },
                {
                  ...item.images[0],
                  path: '',
                },
              ];
            } else {
              let anhDaUpload = getAnhDaUploadTheoMaHangMuc(item.ma);
              if ((item.ma.includes('ANH_KHAC') || item.ma.includes('ANH_TAI_SAN_KHAC')) && item.images.length - 1 >= MAX_SO_LUONG_ANH_CHUP_HANG_MUC_KHAC - anhDaUpload.length) {
                Alert.alert('Thông báo', 'Hạng mục này chỉ chụp tối đa ' + MAX_SO_LUONG_ANH_CHUP_HANG_MUC_KHAC + ' ảnh');
                return item;
              }
              if (item.images.length - 1 >= MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC - anhDaUpload.length) {
                Alert.alert('Thông báo', 'Hạng mục này chỉ chụp tối đa ' + MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC + ' ảnh');
                return item;
              }
              //push ảnh lên đầu
              item.images.unshift({
                path: imageData.path,
                name: imageName,
                nhom: {...menuImageDataSelected},
                tenAlias,
                keyMapLibrary,
              });
            }
            if (item.images.length > 0) {
              if (item.images[1].muc_do) item.images[0].muc_do = item.images[1].muc_do;
            } else if (!item.images[0].muc_do && mucDoSelected) item.images[0].muc_do = mucDoSelected?.ma || null;
          }
          return item;
        });
        if (!daTonTai) {
          let hangMucMoi = {...menuImageDataSelected};
          hangMucMoi.images = [
            {
              path: imageData.path,
              name: imageName,
              nhom: {...menuImageDataSelected},
              tenAlias,
              keyMapLibrary,
            },
            {path: '', nhom: {...menuImageDataSelected}, name: '', muc_do: mucDoSelected ? mucDoSelected.ma : null},
          ];
          hangMucMoi.anhDaUpload = [];
          hangMucMoi.isShowAnhDaUpload = false;
          if (mucDoSelected) hangMucMoi.images[0].muc_do = mucDoSelected.ma;
          imageDataStep2Tmp.push(hangMucMoi);
        }
        setImageDataStep2([...imageDataStep2Tmp]);
      }
      //ẢNH HỒ SƠ, GIẤY TỜ
      else if (currentPage === 2) {
        let imagesTmp = imageDataStep3;
        imagesTmp.map((item) => {
          if (item.ma === menuImageDataSelected.ma) {
            if (
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
              item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM
            ) {
              item.images[indexOpened].path = imageData.path;
              item.images[indexOpened].name = imageName;
              item.images[indexOpened].keyMapLibrary = keyMapLibrary;
              setImageDataStep3([...imagesTmp]);
            } else {
              if (!isAnhChonTuThuVien)
                item.images.unshift({
                  path: imageData.path,
                  name: imageName,
                  nhom: menuImageDataSelected,
                  keyMapLibrary,
                });
              //nếu là chọn nhiều ảnh từ thư viện
              else
                imageData.map((image) => {
                  imageName = getImageNameFromUriCamera(image.path); //lấy ra tên ảnh từ uri
                  item.images.unshift({
                    path: image.path,
                    name: imageName,
                    nhom: menuImageDataSelected,
                    keyMapLibrary,
                  });
                });
            }
          }
          return item;
        });
        setImageDataStep3([...imagesTmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      logErrorTryCatch(error);
    }
  };

  //xoá ảnh
  const removeImage = (imageData) => {
    Alert.alert('Xoá ảnh', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Xoá',
        onPress: () => {
          try {
            //ẢNH TOÀN CẢNH
            if (currentPage === 0) {
              let imagesStep1Tmp = imageDataStep1;
              const GIAY_TO_KHONG_SPLICE = [
                DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.ANH_GIAM_DINH_VIEN,
                DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE,
                DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE,
                DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM,
                DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM,
                DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG,
                DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY,
                'ANH_GDV',
              ];
              let viTriXoa = imagesStep1Tmp.findIndex((item) => item && item.ma === imageData.item.nhom.ma);
              if (viTriXoa === -1) {
                if (imageData.item.nhom.nhom_hang_muc === 'TOAN_CANH') viTriXoa = imagesStep1Tmp.findIndex((item) => item && item.nhom.nhom_hang_muc === 'TOAN_CANH');
                else if (imageData.item.nhom.nhom_hang_muc === 'SO_MAY' || imageData.item.nhom.nhom_hang_muc === 'SO_KHUNG')
                  viTriXoa = imagesStep1Tmp.findIndex((item) => item && item.ten === 'Số khung xe ô tô, Số máy');
              }
              //nếu là ảnh toàn cảnh
              if (imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
                if (!isDoiTuongTaiSanXeMay) {
                  if (viTriXoa !== -1 && imageData.index <= 5) {
                    imagesStep1Tmp[viTriXoa].images[imageData.index].path = '';
                    imagesStep1Tmp[viTriXoa].images[imageData.index].uploadThanhCong = false;
                    imagesStep1Tmp[viTriXoa].images[imageData.index].uploadThatBai = false;
                    imagesStep1Tmp[viTriXoa].images[imageData.index].sttAnh = undefined;
                    imagesStep1Tmp[viTriXoa].images[imageData.index].keyMapLibrary = undefined;
                    imagesStep1Tmp[viTriXoa].images[imageData.index].rootPath ? (imagesStep1Tmp[viTriXoa].images[imageData.index].rootPath = undefined) : '';
                  } else imagesStep1Tmp[viTriXoa].images.splice(imageData.index, 1);
                } else imagesStep1Tmp[viTriXoa].images.splice(imageData.index, 1);
              }
              // nếu là các giấy tờ còn lại
              else {
                if (!GIAY_TO_KHONG_SPLICE.includes(imageData.item.nhom.nhom_hang_muc)) imagesStep1Tmp[viTriXoa].images.splice(imageData.index, 1);
                else {
                  imagesStep1Tmp[viTriXoa].images[imageData.index].path = ''; //nếu là số khung - số máy
                  imagesStep1Tmp[viTriXoa].images[imageData.index].uploadThanhCong = false;
                  imagesStep1Tmp[viTriXoa].images[imageData.index].uploadThatBai = false;
                  imagesStep1Tmp[viTriXoa].images[imageData.index].sttAnh = undefined;
                  imagesStep1Tmp[viTriXoa].images[imageData.index].keyMapLibrary = undefined;
                  imagesStep1Tmp[viTriXoa].images[imageData.index].rootPath ? (imagesStep1Tmp[viTriXoa].images[imageData.index].rootPath = undefined) : '';
                }
              }
              setImageDataStep1([...imagesStep1Tmp]);
            }
            //ẢNH TỔN THẤT
            else if (currentPage === 1) {
              let tmpimageDataStep2 = imageDataStep2; //lấy dữ liệu tmp
              let indexHangMucXoa = tmpimageDataStep2.findIndex((item) => item.ma === imageData.item.nhom.ma);
              if (indexHangMucXoa !== -1) {
                tmpimageDataStep2[indexHangMucXoa].images.splice(imageData.index, 1); //xoá ảnh đi
                //nếu là hạng mục chưa upload lên server -> kiểm tra xem có xoá hết ảnh của hạng mục không, nếu xoá hết ảnh của hạng mục -> xoá hạng mục khỏi LIST
                if (!chiTietHoSo.hang_muc_chup.find((hangMucDaUpload) => hangMucDaUpload.hang_muc === imageData.item.nhom.ma) && tmpimageDataStep2[indexHangMucXoa].images.length === 1)
                  tmpimageDataStep2.splice(indexHangMucXoa, 1); //nếu mà đã xoá hết ảnh -> XOÁ LUÔN HẠNG MỤC
              }
              setImageDataStep2([...tmpimageDataStep2]);
            }
            //ẢNH HỒ SƠ, GIẤY TỜ
            else if (currentPage === 2) {
              let tmpimageDataStep3 = imageDataStep3; //lấy dữ liệu tmp
              //nếu là ảnh BẰNG LÁI, ĐĂNG KÝ XE, ĐĂNG KIỂM XE
              tmpimageDataStep3 = tmpimageDataStep3.map((itemImgData) => {
                if (itemImgData.ma === imageData.item.nhom.ma) {
                  if (
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                    itemImgData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM
                  ) {
                    itemImgData.images[imageData.index].path = '';
                    itemImgData.images[imageData.index].base64 = '';
                    itemImgData.images[imageData.index].uploadThanhCong = false;
                    itemImgData.images[imageData.index].uploadThatBai = false;
                    itemImgData.images[imageData.index].sttAnh = false;
                    itemImgData.images[imageData.index].keyMapLibrary = false;
                    itemImgData.images[imageData.index].rootPath ? (itemImgData.images[imageData.index].rootPath = undefined) : '';
                  } else itemImgData.images.splice(imageData.index, 1);
                }
                return itemImgData;
              });
              setImageDataStep3([...tmpimageDataStep3]);
            }
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  //xoay ảnh
  const rotationImage = (imageData, rotationType) => {
    try {
      imageData.item.rootPath = !imageData.item.rootPath ? imageData.item.path : imageData.item.rootPath; //lưu lại ảnh gốc
      /**
       * direction = 1 : ảnh gốc quay 0 độ
       * direction = 2 : ảnh quay phải 90 độ
       * direction = 3 : ảnh quay phải 180 độ
       * direction = 4 : ảnh quay phải 270 độ
       */
      imageData.item.direction = imageData.item.direction ? imageData.item.direction : 1; //
      let newDirection = 0;
      if (rotationType === 0) newDirection = imageData.item.direction === 4 ? 1 : imageData.item.direction + 1;
      else if (rotationType === 1) newDirection = imageData.item.direction === 1 ? 4 : imageData.item.direction - 1;
      let gocQuay = 0;
      if (newDirection === 1) gocQuay = 0;
      else if (newDirection === 2) gocQuay = 90;
      else if (newDirection === 3) gocQuay = 180;
      else if (newDirection === 4) gocQuay = 270;

      Image.getSize(imageData.item.rootPath, async (imageWidth, imageHeight) => {
        try {
          let response = await ImageResizer.createResizedImage(imageData.item.rootPath, imageWidth, imageHeight, 'PNG', 100, gocQuay);
          imageData.item.direction = newDirection;
          if (currentPage === 0) {
            let imagesStep2Tmp = imageDataStep1;
            imagesStep2Tmp = imagesStep2Tmp.map((itemImgData) => {
              if (
                itemImgData.ma === imageData.item.nhom.ma || //giấy tờ khác
                (itemImgData.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH &&
                  imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) || //ảnh toàn cảnh
                //số khung, số máy
                (itemImgData.nhom_hang_muc &&
                  ((itemImgData.nhom_hang_muc[0] === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG &&
                    imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG) || //số khung
                    (itemImgData.nhom_hang_muc[1] === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY && imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY))) //số máy
              )
                itemImgData.images[imageData.index].path = response.uri;
              return itemImgData;
            });
            setImageDataStep1([...imagesStep2Tmp]);
          }
          //ẢNH TỔN THẤT
          else if (currentPage === 1) {
            let tmpimageDataStep2 = imageDataStep2;
            tmpimageDataStep2 = tmpimageDataStep2.map((itemImgData) => {
              if (itemImgData.ma === imageData.item.nhom.ma) itemImgData.images[imageData.index].path = response.uri;
              return itemImgData;
            });
            setImageDataStep2([...tmpimageDataStep2]);
          }
          //ẢNH HỒ SƠ, GIẤY TỜ
          else if (currentPage === 2) {
            let tmpimageDataStep3 = imageDataStep3;
            tmpimageDataStep3 = tmpimageDataStep3.map((itemImgData) => {
              if (itemImgData.ma === imageData.item.nhom.ma) itemImgData.images[imageData.index].path = response.uri;
              return itemImgData;
            });
            setImageDataStep3([...tmpimageDataStep3]);
          }
        } catch (error) {
          Alert.alert('Có lỗi khi xoay ảnh', JSON.stringify(error));
        }
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //XỬ LÝ ẢNH UPLOAD THẤT BẠI
  const xuLyAnhUploadThatBai = (anhThatBai) => {
    try {
      let imageTmp = [];
      if (currentPage === 0) imageTmp = imageDataStep1;
      else if (currentPage === 1) imageTmp = imageDataStep2;
      else if (currentPage === 2) imageTmp = imageDataStep3;
      imageTmp.map((itemHangMuc) => {
        itemHangMuc.images.map((itemAnh) => {
          if (itemAnh.path === anhThatBai.path) {
            itemAnh.uploadThatBai = true;
            itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
          }
          return itemAnh;
        });
        return itemHangMuc;
      });
      if (currentPage === 0) setImageDataStep1([...imageTmp]);
      else if (currentPage === 1) setImageDataStep2([...imageTmp]);
      else if (currentPage === 2) setImageDataStep3([...imageTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //XỬ LÝ ẢNH UPLOAD THÀNH CÔNG
  const xuLyAnhUploadThanhCong = (anhThanhCong) => {
    try {
      let imageTmp = [];
      if (currentPage === 0) imageTmp = imageDataStep1;
      else if (currentPage === 1) imageTmp = imageDataStep2;
      else if (currentPage === 2) imageTmp = imageDataStep3;
      imageTmp.map((itemHangMuc) => {
        itemHangMuc.images.map((itemAnh) => {
          if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
          return itemAnh;
        });
        return itemHangMuc;
      });
      if (currentPage === 0) setImageDataStep1([...imageTmp]);
      else if (currentPage === 1) setImageDataStep2([...imageTmp]);
      else if (currentPage === 2) setImageDataStep3([...imageTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //XOÁ HẠNG MỤC
  const onPressXoaHangMuc = (data) => {
    Alert.alert('Xoá hạng mục', 'Bạn có chắc muốn xoá hạng mục ' + data.item.ten, [
      {
        text: 'Để sau',
      },
      {
        text: 'Xoá',
        onPress: () => {
          let imageDataStep2Tmp = imageDataStep2;
          imageDataStep2Tmp.splice(data.index, 1);
          setImageDataStep2([...imageDataStep2Tmp]);
        },
      },
    ]);
  };

  //ấn nút TRƯỚC
  const onPressBack = () => {
    if (currentPage === 0) return;
    setCurrentPage(currentPage - 1);
  };
  // ấn nút TIẾP
  const onPressNext = async () => {
    try {
      if (toggleLoading && switchOffline) {
        return Alert.alert('Thông báo', 'Ứng dụng đang tải ảnh lên hệ thống, Bạn có chắc muốn dừng tải ảnh lên', [
          {
            text: 'Dừng tải lên',
            style: 'destructive',
            onPress: () => {
              setToggleLoading(false);
              toggleLoadingRef.current = false;
            },
          },
          {
            text: 'Tiếp tục tải lên',
          },
        ]);
      }

      let imagesUploadToServer = [];
      let imageUploadedTmp = [0, 0, 0];
      soLanUploadLai === 0 && setImageUploaded([...imageUploadedTmp]);
      //nếu không phải là bước đánh giá
      if (currentPage !== 3) {
        //kiểm tra điều kiện trước khi upload ảnh
        if (currentPage === 0) {
          imageDataStep1.map((imageItem, index) => {
            if (imageItem.images && imageItem.images.length > 0) {
              /* XỬ LÝ STT ẢNH */
              let sttAnhLonNhat = -1;
              let anhHangMucDaChup = [];
              // anhHangMucDaChup = anhHoSo.filter(
              //   (item) =>
              //     (item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH && item.ma_file === imageItem.images[0].nhom.ma && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong) ||
              //     item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
              //     item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
              //     item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
              //     item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM,
              // );
              sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[0].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
              let sttAnhTang = 1;
              for (let i = imageItem.images.length - 1; i >= 0; i--) {
                //xử lý số khung - số máy
                if (index === 5) {
                  sttAnhTang = 1;
                  anhHangMucDaChup = anhHoSo.filter(
                    (item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU && item.ma_file === imageItem.images[i].nhom.ma && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
                  );
                  sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[i].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
                }
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  imageItem.images[i].sttAnh = sttAnhLonNhat + sttAnhTang;
                  sttAnhTang += 1;
                }
              }
              /*END XỬ LÝ STT ẢNH */

              for (let i = 0; i < imageItem.images.length; i++) {
                if (imageItem.images[i].path) !imageItem.images[i].uploadThanhCong && !imageItem.images[i].uploadThatBai && imagesUploadToServer.push(imageItem.images[i]);
              }
            }
          });
          setImageDataStep1([...imageDataStep1]);
          soLanUploadLai === 0 && switchOffline && setTotalImageUpload([imagesUploadToServer.length, 0, 0]);
        } else if (currentPage === 1) {
          let anhTonThatDaChup = [];
          anhTonThatDaChup = anhHoSo.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TON_THAT);
          let sttMax = laySttHangMucAnhLonNhat(anhTonThatDaChup); //lấy stt hạng mục lớn nhất của ảnh tổn thất
          imageDataStep2.map((imageItem, index) => {
            let chuaTonTaiHangMuc = false;
            if (imageItem.images && imageItem.images.length > 1) {
              /* XỬ LÝ STT ẢNH */
              let sttAnhLonNhat = -1;
              let anhHangMucDaChup = [];
              anhHangMucDaChup = anhHoSo.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TON_THAT && item.ma_file === imageItem.images[0].nhom.ma);
              sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[0].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
              let sttAnhTang = 1;
              for (let i = imageItem.images.length - 1; i >= 0; i--) {
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  imageItem.images[i].sttAnh = sttAnhLonNhat + sttAnhTang;
                  sttAnhTang += 1;
                }
              }
              /* END XỬ LÝ STT ẢNH */

              /* XỬ LÝ STT HẠNG MỤC ẢNH */
              for (let i = 0; i < imageItem.images.length; i++)
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  let sttHangMucAnh = laySttHangMucAnhTheoHangMucId(anhTonThatDaChup, imageItem.images[i].nhom.ma); //lấy stt hạng mục hiện tại của ảnh
                  if (sttHangMucAnh !== -1) imageItem.images[i].sttHangMuc = sttHangMucAnh;
                  //nếu hạng muc đã up r -> thì lấy stt cũ
                  else {
                    imageItem.images[i].sttHangMuc = sttMax + 1;
                    chuaTonTaiHangMuc = true;
                  } //nếu không -> stt sẽ là stt tiếp theo
                  if (!imageItem.images[i].uploadThatBai) imagesUploadToServer.push(imageItem.images[i]);
                }
            }
            /* END XỬ LÝ STT HẠNG MỤC ẢNH */
            if (chuaTonTaiHangMuc) sttMax += 1;
          });
          setImageDataStep2([...imageDataStep2]);
          soLanUploadLai === 0 && switchOffline && setTotalImageUpload([0, imagesUploadToServer.length, 0]);
        } else if (currentPage === 2) {
          imageDataStep3.map((imageItem) => {
            if (imageItem.images) {
              let sttAnhLonNhat = -1;
              let anhHangMucDaChup = [];
              anhHangMucDaChup = anhHoSo.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU && item.ma_file === imageItem.images[0].nhom.ma);
              sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, imageItem.images[0].nhom.ma); //lấy stt hạng mục lớn nhất của ảnh tổn thất
              let sttAnhTang = 1;
              for (let i = imageItem.images.length - 1; i >= 0; i--) {
                if (imageItem.images[i].path && !imageItem.images[i].uploadThanhCong) {
                  imageItem.images[i].sttAnh = sttAnhLonNhat + sttAnhTang;
                  sttAnhTang += 1;
                  if (!imageItem.images[i].uploadThatBai) imagesUploadToServer.push(imageItem.images[i]);
                }
              }
            }
          });
          setImageDataStep3([...imageDataStep3]);
          soLanUploadLai === 0 && switchOffline && setTotalImageUpload([0, 0, imagesUploadToServer.length]);
        }
        //nếu k có ảnh nào thì k cho upload ảnh
        if (imagesUploadToServer.length === 0) {
          //nếu là cập nhật hồ sơ giấy tờ -> pop ra luôn
          if ((currentPage === 1 || currentPage === 2) && (route.params.loaiAnh || hangMucAnh)) {
            if (currentPage === 1) await xoaAnhDaLuuVaoMay('ANH_TON_THAT');
            else if (currentPage === 2) await xoaAnhDaLuuVaoMay('ANH_TAI_LIEU');
            return NavigationUtil.pop();
          }

          if (currentPage === 0) {
            if (profileData.ho_so.nv_ma === 'TN' || (profileData.ho_so.nv_ma === 'BB' && doiTuongDuocChupAnh.kieu_dt === 'BH')) {
              //kiểm tra tiếp đến ảnh từ server
              let daTonTaiAnh = [false, false, false, false, false]; //ảnh đã upload trên server : index : 0 - ảnh toàn cảnh; index : 1 - ảnh số khung; index : 2 - ảnh tem đăng kiểm; index : 3 - số máy, index : 4 - ảnh giám định viên
              let imgsTmp = anhHoSo.filter(
                (item) =>
                  item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH &&
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH &&
                  item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
              );
              if (imgsTmp.length > 0) daTonTaiAnh[0] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[1] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[2] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[3] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.ANH_GIAM_DINH_VIEN && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[4] = true;
              if (daTonTaiAnh.includes(false)) {
                let messageWarning = 'Vui lòng chụp';
                if (!daTonTaiAnh[0]) messageWarning += ' ít nhất 1 ảnh Toàn Cảnh';
                if (!daTonTaiAnh[1] && !daTonTaiAnh[3]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Số khung hoặc Số máy';
                // if (!daTonTaiAnh[2]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Tem đăng kiểm';
                // if (!daTonTaiAnh[4]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Ảnh giám định viên';
                if (messageWarning !== 'Vui lòng chụp') return FlashMessageHelper.showFlashMessage('Thông báo', messageWarning);
              }
            }
          }
          if (currentPage === 0) {
            await xoaAnhDaLuuVaoMay('ANH_TOAN_CANH');
            initHangMucTonThatBuoc2();
          }
          if (currentPage === 2) {
            await xoaAnhDaLuuVaoMay('ANH_TAI_LIEU');
            NavigationUtil.pop(1);
          }
          if (currentPage === 1) await xoaAnhDaLuuVaoMay('ANH_TON_THAT');

          if (doiTuongDuocChupAnh.kieu_dt === 'TT') setCurrentPage(currentPage + 1);
          else if (doiTuongDuocChupAnh.kieu_dt === 'BH') NavigationUtil.pop(1);
          // if (currentPage === 2) initDataBuoc4();
          return;
        }
        //kiểm tra hết điều kiện xong -> upload ảnh
        setToggleLoading(true);
        toggleLoadingRef.current = true;

        // CODE CŨ
        // let response = await Promise.all(
        //   imagesUploadToServer.map(async (item, index) => {
        //     return uploadImageToServer(
        //       [item],
        //       (anhThanhCong) => {
        //         imageUploadedTmp[currentPage] = imageUploadedTmp[currentPage] + 1;
        //         setImageUploaded([...imageUploadedTmp]);
        //         xuLyAnhUploadThanhCong(anhThanhCong);
        //       },
        //       (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
        //       index,
        //     );
        //   }),
        // );

        // CODE MỚI
        let response = [];
        try {
          //nếu hệ thống đang offline -> lưu data vào storage
          if (!switchOffline) {
            await luuAnhGiamDinhVaoStorage();
            FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu ảnh vào bộ nhớ máy thành công', 'success');
          }
          //nếu hệ thống online -> upload lên bình thường
          else {
            if (!profileData.ho_so.so_id) {
              setToggleLoading(false);
              return Alert.alert('Thông báo', 'Hồ sơ TẠO OFFLINE, chưa có dữ liệu từ hệ thống. Vui lòng ĐỒNG BỘ DỮ LIỆU từ hệ thống để tiếp tục', [
                // {
                //   text: 'Để sau',
                // },
                // {text: 'Đồng bộ', onPress: () => onPressTimKiemsHoSo()},
              ]);
            }
            if (!doiTuongDuocChupAnh.so_id_doi_tuong) {
              Alert.alert('Thông báo', 'Vui lòng tải dữ liệu hồ sơ từ hệ thống để tiếp tục');
              setToggleLoading(false);
              return;
            }
            for (let i = 0; i < imagesUploadToServer.length; i++) {
              if (!toggleLoadingRef.current) return;
              const item = imagesUploadToServer[i];
              response.push(
                await uploadImageToServer(
                  [item],
                  (anhThanhCong) => {
                    setImageUploaded((prevImageUploaded) => {
                      let prevImageUploadedTmp = cloneObject(prevImageUploaded);
                      prevImageUploadedTmp[currentPage] = prevImageUploadedTmp[currentPage] + 1;
                      return [...prevImageUploadedTmp];
                    });
                    xuLyAnhUploadThanhCong(anhThanhCong);
                  },
                  (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
                  i,
                ),
              );
            }
          }
        } catch (error) {
          console.log('error', error);
        }
        //END CODE MỚI

        // CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR
        if (soLanUploadLai === 0 && response.includes('"Network Error"')) {
          setToggleLoading(false);
          setSoLanUploadLai(1); //nếu thay đổi soLanUpload -> useEffect ở trên sẽ check -> gọi lại hàm onPressNext
          return;
        }
        setSoLanUploadLai(0); //reset lại số lần upload về 0. để xử lý cho các bước tiếp theo
        // END CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR

        if (response.includes('Không có vụ tổn thất nào thuộc phạm vi bảo hiểm')) {
          Alert.alert('Thông báo', 'Không có vụ tổn thất nào thuộc phạm vi bảo hiểm! Vui lòng chọn "Cập nhật" để chỉnh sửa thông tin vụ tổn thất.', [
            {
              text: 'OK',
            },
            {
              text: 'Cập nhật',
              onPress: () => NavigationUtil.push(SCREEN_ROUTER_APP.DS_CAC_VU_TON_THAT, {profileData}),
            },
          ]);
        }

        let haveErr = '';
        response = response.filter((item) => item !== true);
        //bỏ đi các thông tin trùng
        let uniqueChars = response.filter((element, index) => {
          return response.indexOf(element) === index;
        });
        if (uniqueChars.length > 0) haveErr = uniqueChars.join(', ');

        //thực hiện xong hết thì mới reset lại data
        setToggleLoading(false);
        toggleLoadingRef.current = false;

        if (haveErr) return FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi xảy ra khi tải ảnh lên hệ thống \n' + haveErr, 'info');
        if (switchOffline) {
          if (currentPage === 0) await xoaAnhDaLuuVaoMay('ANH_TOAN_CANH');
          else if (currentPage === 1) await xoaAnhDaLuuVaoMay('ANH_TON_THAT');
          else if (currentPage === 2) await xoaAnhDaLuuVaoMay('ANH_TAI_LIEU');
          let chiTietHoSo = await getChiTietHoSoGiamDinhOto();
          await getThumbnailDocument(chiTietHoSo, listAnhLuuTrongMay);
        }

        if (currentPage === 0) {
          // getAIData();
          // KIỂM TRA ĐIỀU KIỆN BẮT BUỘC CHỤP ẢNH TOÀN CẢNH, SỐ KHUNG, TEM ĐĂNG KIỂM
          if (profileData.ho_so.nv_ma === 'TN' || (profileData.ho_so.nv_ma === 'BB' && doiTuongDuocChupAnh.kieu_dt === 'BH')) {
            //kiểm tra ảnh vừa chụp trước
            let daChupAnh = [false, false, false, false, false]; //ảnh mới chụp : index : 0 - ảnh toàn cảnh; index : 1 - ảnh số khung; index : 2 - ảnh tem đăng kiểm; index : 3 - số máy, index : 4 - ảnh giám định viên
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH)) daChupAnh[0] = true;
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG)) daChupAnh[1] = true;
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM)) daChupAnh[2] = true;
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY)) daChupAnh[3] = true;
            if (imagesUploadToServer.find((item) => item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.ANH_GIAM_DINH_VIEN)) daChupAnh[4] = true;
            //nếu anhDaChup không có 1 trong 3 thằng kia
            if (daChupAnh.includes(false)) {
              //kiểm tra tiếp đến ảnh từ server
              let daTonTaiAnh = [false, false, false, false, false]; //ảnh đã upload trên server : index : 0 - ảnh toàn cảnh; index : 1 - ảnh số khung; index : 2 - ảnh tem đăng kiểm, 3 - số máy, index : 4 - ảnh giám định viên
              let imgsTmp = anhHoSo.filter(
                (item) =>
                  item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH &&
                  item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH &&
                  item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
              );
              if (imgsTmp.length > 0) daTonTaiAnh[0] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[1] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[2] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[3] = true;
              imgsTmp = anhHoSo.filter((item) => item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.ANH_GIAM_DINH_VIEN && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
              if (imgsTmp.length > 0) daTonTaiAnh[4] = true;
              if (daTonTaiAnh.includes(false)) {
                let messageWarning = 'Vui lòng chụp';
                if (!daTonTaiAnh[0] && !daChupAnh[0]) messageWarning += ' ít nhất 1 ảnh Toàn Cảnh';
                if (!daTonTaiAnh[1] && !daChupAnh[1] && !daTonTaiAnh[3] && !daChupAnh[3]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Số khung hoặc Số máy';
                // if (!daTonTaiAnh[2] && !daChupAnh[2]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Tem đăng kiểm';
                // if (!daTonTaiAnh[4] && !daChupAnh[4]) messageWarning += (messageWarning !== 'Vui lòng chụp' ? ', ' : ' ') + 'Ảnh giám định viên';
                if (messageWarning !== 'Vui lòng chụp') return FlashMessageHelper.showFlashMessage('Thông báo', messageWarning);
              }
            }
          }
          //nếu là đối tượng TỔN THẤT
          if (doiTuongDuocChupAnh.kieu_dt === 'TT') setCurrentPage(currentPage + 1);
          else if (doiTuongDuocChupAnh.kieu_dt === 'BH') NavigationUtil.pop(1); //nếu là đối tượng BẢO HIỂM
          initHangMucTonThatBuoc2();
          if (switchOffline) FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
        } else if (currentPage === 1) {
          if (route.params.loaiAnh) NavigationUtil.pop();
          else setCurrentPage(currentPage + 1);
          if (route.params.anhDaPhanLoai) navigation.setParams({anhDaPhanLoai: undefined});
          if (switchOffline) FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
        } else if (currentPage === 2) {
          if (route.params.loaiAnh || hangMucAnh) {
            NavigationUtil.pop();
            if (switchOffline) return FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
          }
          // initDataBuoc4();
          // setCurrentPage(currentPage + 1);
          NavigationUtil.pop();
          if (switchOffline) FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressHoanThanh = async (data) => {
    try {
      setDialogLoading(true);
      let params = {
        so_id: profileData.ho_so.so_id,
        ...data,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHI_DINH_GARA, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        if (response.state_info.message_body === 'Không xác định được các hạng mục/đối tượng tổn thất theo loại hình nghiệp vụ')
          return Alert.alert('Thông báo', response.state_info.message_body + '. Vui lòng quay lại "Bước 2 : Ảnh tổn thất" để chụp ảnh hạng mục', [
            {
              text: 'Để sau',
            },
            {
              text: 'Đồng ý',
              onPress: () => setCurrentPage(1),
            },
          ]);
        return;
      }
      FlashMessageHelper.showFlashMessage('Thông báo', 'Đánh giá thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  //nhóm ảnh
  const groupBy = (xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  };

  //ấn nút mở CAMERA (step 1 - step 2), mở modal (step 3 - step 4), mở modalCAMERA (giấy tờ bước 4)
  //type : 0 - camera ; type : 1 - lib
  const onPressOpenCamera = (indexOpened, menuImageData, type) => {
    try {
      //mở camera
      if (currentPage === 0) {
        let menuImageDataSelected = {
          hangMucChup: menuImageData,
          indexOpened: indexOpened,
        };

        if (type === 1) {
          openCamera(indexOpened, menuImageDataSelected, type);
          return setMenuImageStep1Selected(menuImageDataSelected);
        }
        if (menuImageData) {
          setGiayToDuocChon({
            menuImageData: menuImageData,
            indexOpened: indexOpened,
          });
          refModalCameraWithVideo.current.show();
        }
        setMenuImageStep1Selected(menuImageDataSelected);
        refModalCameraWithVideo.current.show();
      }
      //mở camera
      else if (currentPage === 1) {
        if (menuImageStep2.length === 0) {
          FlashMessageHelper.showFlashMessage('Thông báo', 'Không có hạng mục cho Ảnh tổn thất.');
          return initHangMucTonThatBuoc2();
        }
        setMenuImageStep2([...hangMucTonThatRoot]);
        refModalHangMuc.current.show();
      }
      //mở modal chọn danh mục
      else {
        if (type === 1) return openCamera(indexOpened, menuImageData, type);
        if (menuImageData) {
          setGiayToDuocChon({
            indexOpened: indexOpened,
            menuImageData: menuImageData,
          });
          refModalCameraWithVideo.current.show();
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //mở IMAGE CROP CAMERA
  //type : 0 - camera ; type : 1 - library
  const openCamera = async (indexOpened, menuImageData, type) => {
    try {
      let imgCropOpts = {
        mediaType: 'photo', //mặc định là chụp ảnh từ camera
        cropping: false,
        enableRotationGesture: true,
        compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
        showCropGuidelines: true, //tắt khung 3x3 đi
        compressImageQuality: 0.5,
        useFrontCamera: false,
        includeExif: true,
      };
      if (type === 1) {
        let nhomHangMuc = '';
        let maHangMuc = '';
        nhomHangMuc = menuImageData.hangMucChup ? menuImageData.hangMucChup.nhom_hang_muc : menuImageData.nhom_hang_muc;
        maHangMuc = menuImageData.hangMucChup ? menuImageData.hangMucChup.ma : menuImageData.ma;
        imgCropOpts.multiple = GIAY_TO_VE_XE_OTO.includes(nhomHangMuc) || maHangMuc === 'ANH_GDV' || nhomHangMuc === 'TOAN_CANH' ? false : true;
        imgCropOpts.maxFiles = GIAY_TO_VE_XE_OTO.includes(nhomHangMuc) || maHangMuc === 'ANH_GDV' || nhomHangMuc === 'TOAN_CANH' ? 1 : 10;
        let data = await ImageCropPicker.openPicker(imgCropOpts);
        return handleImage(data, menuImageData, indexOpened, type);
      }
      //Open Camera
      let data = await ImageCropPicker.openCamera(imgCropOpts);
      handleImage(data, menuImageData, indexOpened, type);
    } catch (error) {
      if (error.code !== 'E_PICKER_CANCELLED') Alert.alert('Thông báo', error.message);
    }
  };

  const onPressStep = (step) => {
    __DEV__ && setCurrentPage(step);
  };

  //mở modal camera
  const openCameraModal = (hangMuc, maMucDoTonThat) => {
    if (currentPage === 0) {
      setMenuImageStep1Selected(hangMuc);
      refModalCameraWithVideo.current.show();
    } else if (currentPage === 1) {
      let mucDoTonThat = categoryCommon.levelLost.find((item) => item.ma === maMucDoTonThat);
      if (mucDoTonThat) setMucDoSelected(mucDoTonThat);
      else setMucDoSelected(null);
      //check xem là hạng mục có trên hệ thống chưa, nếu có rồi thì k cho phép edit tên nữa
      imageDataStep2.forEach((item) => {
        if (item.ma === hangMuc.ma) {
          if (item.anhDaUpload.length > 0) hangMuc.daCoTrenHeThong = true;
        }
      });
      setMenuImageStep2Selected(hangMuc);
      refModalCameraWithVideo.current.show();
    }
  };

  //upload ảnh lên server
  const uploadImageToServer = (imagesData, cbSuccess, cbErr, indexImage) => {
    return new Promise(
      async (resolve) => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            nhom: e.nhom.ma,
            x: currentPosition?.coords?.latitude,
            y: currentPosition?.coords?.longitude,
            stt_hang_muc: e.sttHangMuc,
            ten_file: e.tenAlias || e.name,
          };
          file.stt = e.sttAnh !== undefined ? e.sttAnh : 0;
          if (e.nhomMoi) {
            file.loai = e.loai;
            file.vu_tt = e.vu_tt;
            file.lh_nv = e.lh_nv;
            file.hang_muc = e.hang_muc;
            file.muc_do = e.muc_do;
            file.thay_the_sc = e.thay_the_sc;
            file.chinh_hang = e.chinh_hang;
            file.thu_hoi = e.thu_hoi;
            file.tien_tu_dong = e.tien_tu_dong;
            file.tien_gd = e.tien_gd;
            file.ghi_chu = e.ghi_chu;
          } else {
            if (e.muc_do) file.muc_do = e.muc_do;
            if (e.chinh_hang) file.chinh_hang = e.chinh_hang;
            if (e.thay_the_sc) file.thay_the_sc = e.thay_the_sc;
            if (e.thu_hoi) file.thu_hoi = e.thu_hoi;
            if (e.tien_gd !== null) file.tien_gd = e.tien_gd;
            if (e.tien_tu_dong !== null) file.tien_tu_dong = e.tien_tu_dong;
          }
          files.push(file);
        });
        imagesData = imagesData.map((item) => {
          item = cloneObject(item);
          delete item.preView;
          delete item.uploadThanhCong;
          return item;
        });
        let params = {
          images: imagesData,
          so_id: profileData.ho_so.so_id,
          pm: 'GD',
          ma_doi_tac: profileData.ho_so.ma_doi_tac,
          files: files,
          ung_dung: 'MOBILE_BT',
          so_id_doi_tuong: doiTuongDuocChupAnh.so_id_doi_tuong,
        };
        setToggleLoading(true);
        //CODE GIÁ LẬP LỖI NETWORK ERROR, UPLOAD > 4 cái ảnh
        // if ((indexImage === 3 || indexImage === 2) && soLanUploadLai === 0) {
        //   imagesData[0].lyDoLoi = JSON.stringify('Network Error');
        //   cbErr(imagesData[0]);
        //   resolve(JSON.stringify('Network Error'));
        //   return;
        // }
        try {
          let response = await ESmartClaimEndpoint.uploadFile(AxiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          if (response && response.state_info && response.state_info.status === AxiosConfig.SERVER_RESPONSE_STATUS.NOT_OK) {
            resolve(response.state_info.message_body);
            imagesData[0].lyDoLoi = response.state_info.message_body;
            cbErr(imagesData[0]);
            return;
          } else if (response && response.state_info && response.state_info.status === AxiosConfig.SERVER_RESPONSE_STATUS.OK) {
            cbSuccess(imagesData[0]);
            resolve(true);
          } else {
            imagesData[0].lyDoLoi = JSON.stringify(response?.message || response || '');
            cbErr(imagesData[0]);
            resolve(JSON.stringify(response?.message || response || ''));
          }
        } catch (error) {
          Alert.alert('Thông báo tải ảnh lên hệ thống', JSON.stringify(error?.message || error || ''));
          resolve(false);
        }
      },
      (reject) => reject(),
    );
  };

  //xem chi tiết ảnh đã chụp
  const onPressXemLaiAnh = (imageData) => {
    refModalXemChiTietAnh.current.show({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
  };

  //nút phân loại ảnh
  const onPressPhanLoaiAnh = (danhMucAnh) => {
    try {
      let danhMucAnhDuocPhanLoai = imageDataStep2.find((item) => item.ma === danhMucAnh.ma);
      let anhCanPhanLoai = danhMucAnhDuocPhanLoai.images.slice(0, danhMucAnhDuocPhanLoai.images.length - 1);
      if (danhMucAnhDuocPhanLoai.images.length === 1) return FlashMessageHelper.showFlashMessage('Thông báo', `Vui lòng chụp ảnh ${danhMucAnhDuocPhanLoai.ten} và đánh giá`);
      NavigationUtil.push(SCREEN_ROUTER_APP.CLASSIFY, {
        imagesClassify: anhCanPhanLoai,
        profileData,
        prevScreen: SCREEN_ROUTER_APP.GIAM_DINH_CHI_TIET,
        doiTuongDuocPhanLoai: doiTuongDuocChupAnh,
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressXemChiTietAnhDaUpload = (index) => {
    if (currentPage === 0) {
      let imageDataStep1Tmp = [...imageDataStep1];
      imageDataStep1Tmp[index].isShowAnhDaUpload = !imageDataStep1Tmp[index].isShowAnhDaUpload;
      setImageDataStep1([...imageDataStep1Tmp]);
    } else if (currentPage === 1) {
      let imageDataStep2Tmp = [...imageDataStep2];
      imageDataStep2Tmp[index].isShowAnhDaUpload = !imageDataStep2Tmp[index].isShowAnhDaUpload;
      setImageDataStep2([...imageDataStep2Tmp]);
    } else if (currentPage === 2) {
      let imageDataStep3Tmp = [...imageDataStep3];
      imageDataStep3Tmp[index].isShowAnhDaUpload = !imageDataStep3Tmp[index].isShowAnhDaUpload;
      setImageDataStep3([...imageDataStep3Tmp]);
    }
  };

  const onPressLayDataHangMuc = async () => {
    await getAllHangMucTonThat();
  };

  const onHangMucSelected = (dataHangMucSelected) => {
    // VỚI HẠNG MỤC LÀ ẢNH KHÁC -> cho AUTO TĂNG ANH_KHAC, và tên ANH_KHAC lên
    if (dataHangMucSelected?.ma.includes('ANH_KHAC') || dataHangMucSelected?.ma.includes('ANH_TAI_SAN_KHAC')) {
      let sttHangMucCuoiCung = 0;
      imageDataStep2.forEach((item, index) => {
        if (item.ma.includes('ANH_KHAC') || item.ma.includes('ANH_TAI_SAN_KHAC')) {
          let stt = 0;
          let arrMaAnhKhac = item.ma.split('_');
          if (arrMaAnhKhac.length >= 3) stt = arrMaAnhKhac[arrMaAnhKhac.length - 1];
          if (stt > +sttHangMucCuoiCung) sttHangMucCuoiCung = +stt;
        }
      });
      let ma = 'ANH_KHAC';
      if (dataHangMucSelected.ma.includes('ANH_KHAC_XM')) ma = 'ANH_KHAC_XM';
      else if (dataHangMucSelected.ma.includes('ANH_TAI_SAN_KHAC')) ma = 'ANH_TAI_SAN_KHAC';
      setMenuImageStep2Selected({...dataHangMucSelected, tenAlias: dataHangMucSelected.tenAlias + ' ' + (sttHangMucCuoiCung + 1), ma: ma + '_' + (sttHangMucCuoiCung + 1)});
    } else setMenuImageStep2Selected(dataHangMucSelected);
  };

  const onPressXoaAnhDaUpload = async ({item, index}) => {
    try {
      Alert.alert('Xoá ảnh', 'Bạn có chắc muốn xoá ảnh đã tải lên hệ thống này', [
        {
          text: 'Để sau',
        },
        {
          text: 'Xoá',
          style: 'destructive',
          onPress: async () => {
            let bt = [];
            bt.push(item.bt);
            let params = {
              so_id: item?.so_id,
              bt: bt,
              nv: 'XE',
              pm: 'GD',
              so_id_dt: 0,
            };
            setDialogLoading(true);
            let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_ANH, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            if (currentPage === 1) {
              let imageDataStep2Tmp = [...imageDataStep2];
              imageDataStep2Tmp.map((itemHangMuc) => {
                if (itemHangMuc.ma === item.ma_file) itemHangMuc.anhDaUpload.splice(index, 1);
                return itemHangMuc;
              });
              setImageDataStep2([...imageDataStep2Tmp]);
            }
          },
        },
      ]);
    } catch (error) {
      setDialogLoading(false);
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const getAnhDaUploadTheoMaHangMuc = (maHangMuc) => {
    try {
      let hangMuc = imageDataStep2.find((itemHangMuc) => itemHangMuc.ma === maHangMuc);
      if (hangMuc && hangMuc.anhDaUpload) return hangMuc.anhDaUpload;
      return [];
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
      return [];
    }
  };

  const getAnhDaLuuVaoMay = async (currentPage) => {
    try {
      let hoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay);
      if (hoSo && hoSo.dataAnhGiamDinh) {
        // Nếu là điền ảnh đã lưu ở máy vào bước 1
        let anhTheoDoiTuong = hoSo.dataAnhGiamDinh.find(
          (itemDoiTuong) =>
            (itemDoiTuong.soIdDoiTuong && itemDoiTuong.soIdDoiTuong === doiTuongDuocChupAnh.so_id_doi_tuong) ||
            (itemDoiTuong.soIdDoiTuongLuuTrongMay && itemDoiTuong.soIdDoiTuongLuuTrongMay === doiTuongDuocChupAnh.soIdDoiTuongLuuTrongMay),
        );
        //nếu có list đối tượng được khởi tạo ở local -> so sánh theo các thuộc tính để tìm ra đối tượng lưu ở local tương ứng với đối tượng trên hệ thống
        // if (!anhTheoDoiTuong && hoSo.listDoiTuong) {
        //   let listDoiTuongGiongDoiTuongDuocChupAnhFilter = hoSo.listDoiTuong.filter(
        //     (itemDoiTuong) => itemDoiTuong.kieu_dt === doiTuongDuocChupAnh.kieu_dt && itemDoiTuong.nhom === doiTuongDuocChupAnh.nhom,
        //   );
        //   //nếu có biến loai -> filter thêm theo biến loai
        //   if (doiTuongDuocChupAnh.loai)
        //     listDoiTuongGiongDoiTuongDuocChupAnhFilter = listDoiTuongGiongDoiTuongDuocChupAnhFilter.filter((itemDoiTuong) => itemDoiTuong.loai === doiTuongDuocChupAnh.loai);
        //   //nếu có biến hang_muc -> filter thêm theo biến hang_muc
        //   if (doiTuongDuocChupAnh.hang_muc)
        //     listDoiTuongGiongDoiTuongDuocChupAnhFilter = listDoiTuongGiongDoiTuongDuocChupAnhFilter.filter((itemDoiTuong) => itemDoiTuong.hang_muc === doiTuongDuocChupAnh.hang_muc);
        //   console.log('listDoiTuongGiongDoiTuongDuocChupAnhFilter', listDoiTuongGiongDoiTuongDuocChupAnhFilter);
        //   //nếu tìm ra 1 đối tượng thôi -> thì lấy ra theo luôn đối tượng đấy
        //   if (listDoiTuongGiongDoiTuongDuocChupAnhFilter.length === 1) {
        //     anhTheoDoiTuong = hoSo.dataAnhGiamDinh.find(
        //       (itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay && itemDoiTuong.soIdDoiTuongLuuTrongMay === listDoiTuongGiongDoiTuongDuocChupAnhFilter[0].soIdDoiTuongLuuTrongMay,
        //     );
        //   } else {
        //     console.log('doiTuongGiongDoiTuongDuocChupAnhSelected', listDoiTuongGiongDoiTuongDuocChupAnhFilter, doiTuongGiongDoiTuongDuocChupAnhSelected);
        //     //nếu chưa có listDoiTuongGiongDoiTuongDuocChupAnh thì sẽ update vào listDoiTuongGiongDoiTuongDuocChupAnh
        //     if (listDoiTuongGiongDoiTuongDuocChupAnh.length === 0) setListDoiTuongGiongDoiTuongDuocChupAnh(listDoiTuongGiongDoiTuongDuocChupAnhFilter);
        //     //nếu có rồi và có 1 option được chọn thì lấy dữ liệu theo option đấy để fill data ảnh đã chụp vào
        //     else if (listDoiTuongGiongDoiTuongDuocChupAnh.length > 0 && doiTuongGiongDoiTuongDuocChupAnhSelected)
        //       anhTheoDoiTuong = hoSo.dataAnhGiamDinh.find((itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay && itemDoiTuong.soIdDoiTuongLuuTrongMay === doiTuongGiongDoiTuongDuocChupAnhSelected);
        //   }
        // }
        if (currentPage === 0 && anhTheoDoiTuong && anhTheoDoiTuong.anhToanCanh) return anhTheoDoiTuong?.anhToanCanh?.data || [];
        else if (currentPage === 1 && anhTheoDoiTuong && anhTheoDoiTuong.anhTonThat) return anhTheoDoiTuong?.anhTonThat?.data || [];
        else if (currentPage === 2 && anhTheoDoiTuong && anhTheoDoiTuong.anhTaiLieu) return anhTheoDoiTuong?.anhTaiLieu?.data || [];
        return [];
      } else {
        console.log('Không tìm thấy dữ liệu lưu trong máy thuộc hồ sơ này');
        return [];
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      logErrorTryCatch(error);
    }
  };

  const xoaAnhDaLuuVaoMay = async (type) => {
    try {
      if (listDoiTuongGiongDoiTuongDuocChupAnh.length > 0) setDisableOptionDoiTuongGiongDoiTuongChupAnh(true); //xoá đi để không cho chọn lại -> tránh nhầm lẫn dữ liệu
      let hoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay);
      if (hoSo && hoSo.dataAnhGiamDinh)
        await AsyncStorageProvider.xoaDataGiamDinh(
          profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay,
          doiTuongDuocChupAnh.so_id_doi_tuong || doiTuongDuocChupAnh.soIdDoiTuongLuuTrongMay,
          type,
        );
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      logErrorTryCatch(error);
    }
  };

  const onLoadImageError = (imageData) => {
    try {
      if (currentPage === 0) {
        let imagesStep1Tmp = imageDataStep1;
        imagesStep1Tmp = imagesStep1Tmp.map((itemImgData) => {
          if (
            itemImgData.ma === imageData.item.nhom.ma || //giấy tờ khác
            (itemImgData.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH && imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) || //ảnh toàn cảnh
            //số khung, số máy
            (itemImgData.nhom_hang_muc &&
              ((itemImgData.nhom_hang_muc[0] === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG && imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG) || //số khung
                (itemImgData.nhom_hang_muc[1] === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY && imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY))) //số máy
          ) {
            itemImgData.images[imageData.index].uploadThatBai = true;
            itemImgData.images[imageData.index].lyDoLoi = 'Không tìm thấy ảnh trong thư viện';
          }
          return itemImgData;
        });
        setImageDataStep1([...imagesStep1Tmp]);
      }
      //ẢNH TỔN THẤT
      else if (currentPage === 1) {
        let tmpimageDataStep2 = imageDataStep2;
        tmpimageDataStep2 = tmpimageDataStep2.map((itemImgData) => {
          if (itemImgData.ma === imageData.item.nhom.ma) {
            itemImgData.images[imageData.index].uploadThatBai = true;
            itemImgData.images[imageData.index].lyDoLoi = 'Không tìm thấy ảnh trong thư viện';
          }
          return itemImgData;
        });
        setImageDataStep2([...tmpimageDataStep2]);
      }
      //ẢNH HỒ SƠ, GIẤY TỜ
      else if (currentPage === 2) {
        let tmpimageDataStep3 = imageDataStep3;
        tmpimageDataStep3 = tmpimageDataStep3.map((itemImgData) => {
          if (itemImgData.ma === imageData.item.nhom.ma) {
            itemImgData.images[imageData.index].uploadThatBai = true;
            itemImgData.images[imageData.index].lyDoLoi = 'Không tìm thấy ảnh trong thư viện';
          }
          return itemImgData;
        });
        setImageDataStep3([...tmpimageDataStep3]);
      }
    } catch (error) {
      Alert.alert('Có lỗi khi xoay ảnh', JSON.stringify(error));
    }
  };

  /* RENDER  */
  // các nút ở footer
  const renderFooter = () => {
    return (
      <View style={[styles.footerView, {marginBottom: inset.bottom === 0 ? spacing.smaller : 0}]}>
        <TouchableOpacity activeOpacity={0.5} onPress={onPressBack} style={styles.btnBack}>
          {currentPage !== 0 && !route.params.loaiAnh && !hangMucAnh && (
            <>
              <View style={styles.iconLeftBtnView}>
                <Icon.Ionicons name="arrow-back" size={25} color={colors.WHITE} style={styles.iconLeftBtn} />
              </View>
              <Text style={styles.txtBtnBottom} font="bold14" children="Trước" />
            </>
          )}
        </TouchableOpacity>
        {/* NÚT TIẾP TỤC - HOÀN THÀNH   */}
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={handleSubmit(currentPage === 3 ? onPressHoanThanh : onPressNext)}
          style={styles.btnNext}
          //  disabled={toggleLoading}
        >
          {!toggleLoading ? (
            <Text style={styles.txtBtnBottom} font="bold14" children={currentPage === 3 || doiTuongDuocChupAnh.kieu_dt === 'BH' ? 'Hoàn thành' : 'Tiếp'} />
          ) : (
            <ActivityIndicator size="large" color={colors.GRAY} style={{flex: 1}} />
          )}
          <View style={styles.iconRightBtnView}>
            <Icon.Ionicons name={currentPage === 3 || doiTuongDuocChupAnh.kieu_dt === 'BH' ? 'checkmark-sharp' : 'arrow-forward'} size={25} style={styles.iconRightBtn} color={colors.WHITE} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderTakeImageView = () => (
    <>
      {doiTuongDuocChupAnh?.so_id_doi_tuong !== '' && listDoiTuongGiongDoiTuongDuocChupAnh.length > 0 && (
        <DropdownPicker
          zIndex={10000}
          isOpen={openOptionDoiTuongGiongDoiTuongChupAnh}
          setOpen={setOpenOptionDoiTuongGiongDoiTuongChupAnh}
          searchable={false}
          items={listDoiTuongGiongDoiTuongDuocChupAnh}
          itemSelected={doiTuongGiongDoiTuongDuocChupAnhSelected}
          setItemSelected={setDoiTuongGiongDoiTuongDuocChupAnhSelected}
          placeholder="Chọn đối tượng"
          containerStyle={{marginBottom: spacing.small, marginHorizontal: spacing.default}}
          isRequired={true}
          disabled={false}
          // disabled={disableOptionDoiTuongGiongDoiTuongChupAnh}
          schema={{
            label: 'ten_doi_tuong',
            value: 'soIdDoiTuongLuuTrongMay',
          }}
        />
      )}

      <StepIndicatorComp
        currentPosition={currentPage}
        onPress={(position) => onPressStep(position)}
        stepCount={doiTuongDuocChupAnh.kieu_dt === 'TT' ? 3 : 1}
        labels={doiTuongDuocChupAnh.kieu_dt === 'TT' ? ['Ảnh toàn cảnh', 'Ảnh tổn thất', 'Ảnh giấy tờ'] : ['Ảnh toàn cảnh, giấy tờ']}
      />
      <ThongKeUpload totalImagelUpload={totalImagelUpload} imageUploaded={imageUploaded} doiTuongDuocChupAnh={doiTuongDuocChupAnh} />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {currentPage === 0 && (
          <View style={styles.scrollView}>
            <ChupAnhOfflineOtoStep1
              key={1}
              imagesData={imageDataStep1}
              removeImage={removeImage}
              onPressOpenCamera={onPressOpenCamera}
              onPressXemLaiAnh={onPressXemLaiAnh}
              openCameraModal={openCameraModal}
              profileData={profileData}
              rotationImage={rotationImage}
              // anhHoSo={anhHoSo}
              doiTuongDuocChupAnh={doiTuongDuocChupAnh}
              onPressShowDuLieuAI={() => refModalAIData.current.show()}
              onPressXemChiTietAnhDaUpload={onPressXemChiTietAnhDaUpload}
              onLoadImageError={onLoadImageError}
            />
          </View>
        )}

        {currentPage === 1 && (
          <>
            <ChupAnhOfflineOtoStep2
              key={2}
              imagesData={imageDataStep2}
              removeImage={removeImage}
              onPressOpenCamera={onPressOpenCamera}
              openCamera={openCamera}
              onPressXemLaiAnh={onPressXemLaiAnh}
              onPressPhanLoaiAnh={onPressPhanLoaiAnh}
              profileData={profileData}
              openCameraModal={openCameraModal}
              rotationImage={rotationImage}
              onPressXoaHangMuc={onPressXoaHangMuc}
              onPressXemChiTietAnhDaUpload={onPressXemChiTietAnhDaUpload}
              onPressXoaAnhDaUpload={onPressXoaAnhDaUpload}
              onLoadImageError={onLoadImageError}
            />
          </>
        )}
        {currentPage === 2 && (
          <ChupAnhOfflineOtoStep3
            key={3}
            hangMucAnh={hangMucAnh}
            profileData={profileData}
            imagesData={imageDataStep3}
            removeImage={removeImage}
            onPressOpenCamera={onPressOpenCamera}
            openCamera={openCamera}
            onPressXemLaiAnh={onPressXemLaiAnh}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
            rotationImage={rotationImage}
            // anhHoSo={anhHoSo}
            onPressXemChiTietAnhDaUpload={onPressXemChiTietAnhDaUpload}
            onLoadImageError={onLoadImageError}
          />
        )}
        {/* {currentPage === 3 && (
          <DanhGiaStep5
            control={control}
            errors={errors}
            setValue={setValue}
            watch={watch}
            listGaraRoot={listGaraRoot}
            listGara={listGara}
            getGaraData={getGaraData}
            dsNghiepVu={dsNghiepVu}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
          />
        )} */}
      </ScrollView>
    </>
  );
  const renderOfflineSwitchView = () => (
    <View style={styles.offlineSwitchView}>
      <SwitchComp
        value={switchOffline}
        onValueChange={setSwitchOffline}
        title="Hệ thống"
        activeText="ONL"
        inActiveText="OFF"
        backgroundActive={colors.GREEN}
        backgroundInactive="gray"
        circleActiveColor="#FFF"
        containerStyle={{flex: 1, borderBottomWidth: 0, borderRightWidth: 0}}
      />
      {/* <TouchableOpacity style={[styles.reloadView, !switchOffline && {backgroundColor: 'gray'}]} onPress={reloadInitData} disabled={!switchOffline}>
        <Icon.Ionicons name="reload" size={20} color={'#FFF'} />
      </TouchableOpacity> */}
    </View>
  );

  const renderChonDoiTuongDaChupView = () => {};
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      isLoading={isLoading}
      headerBack
      headerTitle="Giám định chi tiết OFFLINE"
      headerSubTitle={doiTuongDuocChupAnh.ten_doi_tuong}
      onPressBack={backAction}
      renderView={
        <>
          {renderOfflineSwitchView()}
          {renderChonDoiTuongDaChupView()}
          {renderTakeImageView()}
          {renderFooter()}

          {/* modal hiển thị ảnh trong phần hướng dẫn */}
          <ModalXemChiTietAnh key="ModalXemChiTietAnh" ref={refModalXemChiTietAnh} />
          <ModalHangMuc
            ref={refModalHangMuc}
            profileData={profileData}
            data={menuImageStep2}
            clearSearch={() => setMenuImageStep2([...hangMucTonThatRoot])}
            onChangeSearchTextHangMuc={onChangeSearchTextHangMuc}
            onHangMucSelected={(data) => {
              onHangMucSelected(data);
              // setMenuImageStep2Selected(data);
              setDialogLoading(true);
              setTimeout(
                () => {
                  setDialogLoading(false);
                  refModalCameraWithVideo.current.show();
                },
                !isIOS ? 100 : 1000,
              );
            }}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
            categoryCommon={categoryCommon}
            setMucDoSelected={setMucDoSelected}
            mucDoSelected={mucDoSelected}
            onPressLayDataHangMuc={onPressLayDataHangMuc}
            getAnhDaUploadTheoMaHangMuc={getAnhDaUploadTheoMaHangMuc}
          />

          <ModalXemLaiAnh
            key="ModalXemLaiAnh"
            ref={refModalXemLaiAnh}
            showModalXemChiTietAnh={(imageData) => {
              isIOS && refModalXemLaiAnh.current.hide();
              setTimeout(() => refModalXemChiTietAnh.current.show(imageData), isIOS ? 400 : 0);
            }}
            currentPage={currentPage}
          />

          <ModalCameraGiamDinhOffileOto
            ref={refModalCameraWithVideo}
            key="ModalCameraWithVideo"
            giayToDuocChon={giayToDuocChon}
            handleImage={handleImage}
            setDialogLoading={setDialogLoading}
            currentPage={currentPage}
            imageDataStep2={imageDataStep2}
            menuImageStep2Selected={menuImageStep2Selected}
            setMenuImageStep2Selected={setMenuImageStep2Selected}
            menuImageStep1Selected={menuImageStep1Selected}
            onPressPhanLoaiAnh={onPressPhanLoaiAnh}
            mucDoSelected={mucDoSelected}
            categoryCommon={categoryCommon}
            cauHinh={profileData.cau_hinh}
            currentPosition={currentPosition}
            profileData={profileData}
            doiTuongDuocChupAnh={doiTuongDuocChupAnh}
            getAnhDaUploadTheoMaHangMuc={getAnhDaUploadTheoMaHangMuc}
          />
        </>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  categoryImage: state.categoryImage.data,
  categoryCommon: state.categoryCommon.data,
  // appSettings: state.appSetting,
});

const GiamDinhChiTietOtoOfflineScreenConnect = connect(mapStateToProps, {})(GiamDinhChiTietOtoOfflineScreenComponent);
export const GiamDinhChiTietOtoOfflineScreen = memo(GiamDinhChiTietOtoOfflineScreenConnect, isEqual);
