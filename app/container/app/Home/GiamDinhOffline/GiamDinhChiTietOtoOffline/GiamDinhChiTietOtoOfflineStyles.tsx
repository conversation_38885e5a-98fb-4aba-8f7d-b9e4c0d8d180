import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {},
  stepIndicator: {
    marginVertical: spacing.smaller,
  },
  stepLabel: {
    textAlign: 'center',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    textAlign: 'center',
    color: colors.GREEN,
  },
  footerView: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: spacing.smaller,
  },
  btnHint: {
    borderRadius: 40,
    flex: 0,
  },
  btnBack: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
    flexDirection: 'row',
    borderRadius: 30,
    marginHorizontal: spacing.smaller,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconRightBtnView: {
    backgroundColor: colors.BUTTON.LIGHT.SECONDARY,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconRightBtn: {
    flex: 0,
    paddingVertical: spacing.smaller,
    paddingHorizontal: spacing.smaller,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconLeftBtnView: {
    backgroundColor: colors.BUTTON.LIGHT.SECONDARY,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconLeftBtn: {
    flex: 0,
    paddingVertical: spacing.smaller,
    paddingHorizontal: spacing.smaller,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  btnNext: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
    flexDirection: 'row',
    borderRadius: 30,
    marginHorizontal: spacing.smaller,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  txtBtnBottom: {
    flex: 1,
    textAlign: 'center',
    color: colors.WHITE,
    // fontWeight: 'bold',
  },
  btnRowContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.smaller,
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
    flexDirection: 'row',
    marginHorizontal: spacing.small,
    borderRadius: 10,
    marginTop: spacing.tiny,
  },
  btnEdit: {
    // borderBottomWidth: 2,
    borderRadius: 8,
    marginVertical: spacing.smaller,
    paddingVertical: spacing.smaller,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.smaller,
    justifyContent: 'center',
    flex: 1,
  },
  txtTab: {
    marginLeft: spacing.tiny,
    color: colors.BLACK_03,
  },
  videoView: {
    flex: 1,
  },
  btnLuuView: {
    paddingHorizontal: spacing.small,
    justifyContent: 'center',
    flex: 1,
  },
  switchManChupAnhBuoc1: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginBottom: spacing.small,
    marginHorizontal: spacing.small,
    marginTop: spacing.smaller,
  },
  btnSwitchBuoc1: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 3,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.smaller,
    flex: 1,
  },
  txtbtnSwitch: {
    color: colors.PRIMARY,
    paddingLeft: spacing.small,
  },
  txtHangMucAI: {color: colors.PRIMARY},
  btnHangMucAIView: {
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: colors.PRIMARY,
    marginBottom: spacing.tiny,
  },
  reloadView: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.default,
    borderRadius: 8,
    marginLeft: spacing.tiny,
    backgroundColor: colors.PRIMARY,
  },
  offlineSwitchView: {
    flexDirection: 'row',
    marginVertical: spacing.tiny,
    paddingRight: spacing.tiny,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    paddingBottom: spacing.tiny,
  },
});
