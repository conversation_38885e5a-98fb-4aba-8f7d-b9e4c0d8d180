import {ANH_HO_SO_GIAY_TO, DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import R from '@R';

export const button = [
  {
    title: 'Ảnh',
    iconName: 'image',
  },
  {
    title: 'Video',
    iconName: 'video-camera',
  },
];

export const GIAY_TO_VE_XE_OTO = [
  DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE,
  DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE,
  DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM,
  DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG,
  DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY,
  DATA_CONSTANT.IMA<PERSON>_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM,
];

const BACKGROUND_ANH_TOAN_CANH = {
  [0]: R.images.img_take_photo_step2_1,
  [1]: R.images.img_take_photo_step2_4,
  [2]: R.images.img_take_photo_step2_4,
  [3]: R.images.img_take_photo_step2_2,
  [4]: R.images.img_take_photo_step2_3,
  [5]: R.images.img_take_photo_step2_3,
  [6]: R.icons.ic_gallery,
};

export const MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC = 6;
export const MAX_SO_LUONG_ANH_CHUP_HANG_MUC_KHAC = 6;
