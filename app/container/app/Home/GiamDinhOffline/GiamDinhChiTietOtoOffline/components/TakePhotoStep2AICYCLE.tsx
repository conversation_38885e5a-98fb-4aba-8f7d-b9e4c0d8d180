import R from '@R';
import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Icon, Text} from '@app/components';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {getImageNameFromUriCamera} from '@app/utils/CameraProvider';
import {getCauHinhHoSoByMa, laySttAnhLonNhatTheoMaHangMuc} from '@app/utils/DataProvider';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import ImageCropPicker from 'react-native-image-crop-picker';
import ModalCameraAICYCLE from './ModalCameraAICYCLE';
import {ModalAIData} from './ModalAIData';

const ANH_CHI_TIET_TITLE = ['Trước', 'Phải - Trước', 'Phải - Sau', 'Sau', 'Trái - Sau', 'Trái - Trước'];

const TakePhotoStep2AICYCLEComponent = ({profileData, xemChiTietAnh, currentPosition, doiTuongDuocChupAnh, anhHoSo, getThumbnailDocument, dataAI, setDataAI, getAIData}) => {
  const [chonAnhTuThuVien, setChonAnhTuThuVien] = useState(false);
  const [listAnhTongQuan, setListAnhTongQuan] = useState([]);
  const [listAnhChiTiet, setListAnhChiTiet] = useState([{}, {}, {}, {}, {}, {}]);

  console.log('dataAI', dataAI);
  let refModalCameraAICYCLE = useRef(null);
  let refModalAIData = useRef(null);

  useEffect(() => {
    if (profileData && profileData.cau_hinh) {
      let cauHinhChonAnhTuThuVien = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.CHON_ANH_THU_VIEN, profileData.cau_hinh);
      if (cauHinhChonAnhTuThuVien?.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) setChonAnhTuThuVien(true);
    }
  }, []);

  const handleImage = async (imageData, loaiAnh, viTriChup) => {
    if (loaiAnh === 'ANH_TONG_QUAN') {
      let listAnhTongQuanTmp = listAnhTongQuan;
      imageData.dangUpload = true;
      listAnhTongQuanTmp.unshift(imageData);
      setListAnhTongQuan([...listAnhTongQuanTmp]);
      let response = await uploadImageToServer([imageData], loaiAnh, 0);
      if (response !== true) Alert.alert('Tải ảnh lên hệ thống không thành công', response);
    } else if (loaiAnh === 'ANH_CHI_TIET') {
      let listAnhChiTietTmp = listAnhChiTiet;
      imageData.dangUpload = true;
      listAnhChiTietTmp[viTriChup] = imageData;
      setListAnhChiTiet([...listAnhChiTietTmp]);
      let response = await uploadImageToServer([imageData], loaiAnh, viTriChup);
      if (response !== true) Alert.alert('Tải ảnh lên hệ thống không thành công', response);
    }
  };
  const onPressRemoveAnhToanCanh = (index) => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Đồng ý',
        onPress: () => {
          let tmpListAnhTongQuan = listAnhTongQuan; //lấy dữ liệu tmp
          tmpListAnhTongQuan.splice(index, 1);
          setListAnhTongQuan([...tmpListAnhTongQuan]);
        },
      },
    ]);
  };
  const xuLyAnhUploadThanhCong = (imagesData, loaiAnh, indexImage) => {
    if (loaiAnh === 'ANH_TONG_QUAN') {
      let listAnhTongQuanTmp = listAnhTongQuan;
      listAnhTongQuanTmp[0].dangUpload = false;
      listAnhTongQuanTmp[0].uploadThanhCong = true;
      setListAnhTongQuan([...listAnhTongQuanTmp]);
    } else if (loaiAnh === 'ANH_CHI_TIET') {
      let listAnhChiTietTmp = listAnhChiTiet;
      listAnhChiTietTmp[indexImage].dangUpload = false;
      listAnhChiTietTmp[indexImage].uploadThanhCong = true;
      setListAnhChiTiet([...listAnhChiTietTmp]);
    }
    getAIData();
  };

  const xuLyAnhUploadThatBai = (imagesData, loaiAnh, indexImage) => {
    if (loaiAnh === 'ANH_TONG_QUAN') {
      let listAnhTongQuanTmp = listAnhTongQuan;
      listAnhTongQuanTmp[0].dangUpload = false;
      listAnhTongQuanTmp[0].uploadThanhCong = false;
      setListAnhTongQuan([...listAnhTongQuanTmp]);
    } else if (loaiAnh === 'ANH_CHI_TIET') {
      let listAnhChiTietTmp = listAnhChiTiet;
      listAnhChiTietTmp[indexImage].dangUpload = false;
      listAnhChiTietTmp[indexImage].uploadThanhCong = false;
      setListAnhChiTiet([...listAnhChiTietTmp]);
    }
  };

  const uploadImageToServer = (imagesData, loaiAnh, indexImage) => {
    return new Promise(
      async (resolve) => {
        //xử lý STT ẢNH
        let files = [];
        let sttAnhLonNhat = -1;
        let anhHangMucDaChup = anhHoSo.filter(
          (item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH && item.ma_file === 'TC0001' && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong,
        );
        sttAnhLonNhat = laySttAnhLonNhatTheoMaHangMuc(anhHangMucDaChup, 'TC0001'); //lấy stt hạng mục lớn nhất của ảnh tổn thất
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            nhom: 'TC0001',
            x: currentPosition.coords?.latitude,
            y: currentPosition.coords?.longitude,
            stt: sttAnhLonNhat + 1,
          };
          files.push(file);
        });
        imagesData[0].sttAnh = sttAnhLonNhat + 1;
        imagesData[0].path = imagesData[0].uri;
        let params = {
          images: imagesData,
          so_id: profileData.ho_so.so_id,
          pm: 'GD',
          ma_doi_tac: profileData.ho_so.ma_doi_tac,
          files: files,
          ung_dung: 'MOBILE_BT',
          so_id_doi_tuong: doiTuongDuocChupAnh.so_id_doi_tuong,
        };
        try {
          let response = await ESmartClaimEndpoint.uploadFile(axiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          if (response && response.state_info && response.state_info.status === axiosConfig.SERVER_RESPONSE_STATUS.NOT_OK) {
            resolve(response.state_info.message_body);
            imagesData[0].lyDoLoi = response.state_info.message_body;
            xuLyAnhUploadThatBai(imagesData[0], loaiAnh, indexImage);
            return;
          } else if (response && response.state_info && response.state_info.status === axiosConfig.SERVER_RESPONSE_STATUS.OK) {
            getThumbnailDocument();
            xuLyAnhUploadThanhCong(imagesData[0], loaiAnh, indexImage);
            resolve(true);
          } else {
            imagesData[0].lyDoLoi = JSON.stringify(response?.message || response || '');
            xuLyAnhUploadThatBai(imagesData[0], loaiAnh, indexImage);
            resolve(JSON.stringify(response?.message || response || ''));
          }
        } catch (error) {
          Alert.alert('Thông báo tải ảnh lên hệ thống', JSON.stringify(error?.message || error || ''));
          resolve(false);
        }
      },
      (reject) => reject(),
    );
  };

  const onPressChonAnhTuThuVien = async (viTriChup) => {
    let imgCropOpts = {
      mediaType: 'photo', //mặc định là chụp ảnh từ camera
      cropping: false,
      enableRotationGesture: true,
      compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
      showCropGuidelines: true, //tắt khung 3x3 đi
      compressImageQuality: 0.5,
      useFrontCamera: false,
      includeExif: true,
    };
    try {
      let dataImage = await ImageCropPicker.openPicker(imgCropOpts);
      dataImage.uri = dataImage.path;
      dataImage.name = getImageNameFromUriCamera(dataImage.path); //lấy ra tên ảnh từ uri
      handleImage(dataImage, 'ANH_CHI_TIET', viTriChup);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  // RENDER
  const renderItemListAnhTongQuan = ({item, index}) => {
    return (
      <View style={styles.itemListAnhTongQuan}>
        <FastImage source={{uri: item.uri}} style={styles.imgAnhTongQuan} resizeMode="cover" />
        {item.dangUpload && (
          <View style={styles.uploadingAnhTongQuanView}>
            <View style={styles.iconLoadingAnhTongQuanView}>
              <ActivityIndicator size="large" />
            </View>
            <View style={styles.iconLoadingAnhTongQuanView}>
              <Icon.Ionicons name="cloud-upload-outline" size={20} color="#FFF" />
            </View>
          </View>
        )}
        {item.uploadThanhCong === true && (
          <View style={styles.uploadThanhCongAnhTongQuanView}>
            <Icon.AntDesign name="checkcircle" size={25} color={colors.GREEN} />
          </View>
        )}
        {item.uploadThanhCong === false && (
          <TouchableOpacity style={styles.uploadThanhCongAnhTongQuanView} onPress={() => Alert.alert('Thông báo', item.lyDoLoi)}>
            <Icon.MaterialCommunityIcons name="upload-off-outline" size={25} color={colors.GREEN} />
          </TouchableOpacity>
        )}
        <TouchableOpacity style={styles.iconRemoveItemAnhTongQuan} onPress={() => onPressRemoveAnhToanCanh(index)}>
          <Icon.AntDesign name="closecircle" color={colors.RED1} size={25} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderHeaderListTongQuan = () => (
    <View style={styles.headerListAnhTongQuan}>
      <View style={{flex: 1}}>
        <Text children={`Tổng quan (${listAnhTongQuan.length})`} style={styles.txtTongQuan} font="bold14" />
      </View>
      <TouchableOpacity onPress={() => refModalCameraAICYCLE.current.show(null)}>
        <Text children="Chụp ảnh" font="regular14" style={styles.txtChupAnh} />
      </TouchableOpacity>
    </View>
  );
  const renderAnhTongQuanView = () => (
    <>
      {renderHeaderListTongQuan()}
      <FlatList
        data={listAnhTongQuan}
        renderItem={renderItemListAnhTongQuan}
        keyExtractor={(item, index) => index.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{marginLeft: spacing.small, marginTop: spacing.small}}
        ListEmptyComponent={
          <View>
            <Text children="Chưa có ảnh, vui lòng chụp thêm" style={styles.txtListAnhTongQuanEmpty} />
          </View>
        }
      />
    </>
  );

  const renderItemChupAnhXungQuanh = (viTri) => {
    if (!listAnhChiTiet[viTri].uri)
      return (
        <TouchableOpacity style={styles.itemChupAnhXungQuanh} onPress={() => refModalCameraAICYCLE.current.show(viTri)}>
          <View style={styles.titleItemChupAnhXungQuanhView}>
            <Text children={ANH_CHI_TIET_TITLE[viTri]} style={styles.txtTitleAnhXungQuanh} />
          </View>
          <View style={styles.iconItemChupAnhXungQuanh}>
            <Icon.MaterialCommunityIcons name="camera" size={40} color={colors.PRIMARY} />
          </View>
        </TouchableOpacity>
      );
    else {
      return (
        <TouchableOpacity style={styles.itemChupAnhXungQuanh} onPress={() => xemChiTietAnh({title: ANH_CHI_TIET_TITLE[viTri], imageData: {uri: listAnhChiTiet[viTri].uri}})}>
          <View style={styles.titleItemChupAnhXungQuanhView}>
            <Text children={ANH_CHI_TIET_TITLE[viTri]} style={styles.txtTitleAnhXungQuanh} />
          </View>
          <View style={styles.iconItemChupAnhXungQuanh}>
            <FastImage source={{uri: listAnhChiTiet[viTri].uri}} style={styles.imgItemAnhXungQuanh} resizeMode="stretch" />

            {listAnhChiTiet[viTri].dangUpload && (
              <View style={styles.uploadingAnhTongQuanView}>
                <View style={styles.iconLoadingAnhTongQuanView}>
                  <ActivityIndicator size="large" />
                </View>
                <View style={styles.iconLoadingAnhTongQuanView}>
                  <Icon.Ionicons name="cloud-upload-outline" size={20} color="#FFF" />
                </View>
              </View>
            )}
            {listAnhChiTiet[viTri].uploadThanhCong === true && (
              <View style={styles.uploadThanhCongAnhTongQuanView}>
                <Icon.AntDesign name="checkcircle" size={25} color={colors.GREEN} />
              </View>
            )}
            {listAnhChiTiet[viTri].uploadThanhCong === false && (
              <TouchableOpacity style={styles.uploadThanhCongAnhTongQuanView} onPress={() => Alert.alert('Thông báo', listAnhChiTiet[viTri].lyDoLoi)}>
                <Icon.MaterialCommunityIcons name="upload-off-outline" size={25} color={colors.GREEN} />
              </TouchableOpacity>
            )}

            <View style={styles.btnItemAnhXungQuanh}>
              <TouchableOpacity style={[styles.iconCameraView, {marginRight: spacing.tiny}]} onPress={() => refModalCameraAICYCLE.current.show(viTri)}>
                <Icon.MaterialCommunityIcons name="camera" size={25} color="#FFF" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.iconCameraView} onPress={() => onPressChonAnhTuThuVien(viTri)}>
                <Icon.MaterialCommunityIcons name="image-plus" size={25} color="#FFF" />
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      );
    }
  };
  const renderAnhChiTietView = () => (
    <View style={styles.anhChiTietView}>
      <View style={styles.headerListAnhTongQuan}>
        <View style={{flex: 1}}>
          <Text children="Chi tiết" style={styles.txtTongQuan} font="bold14" />
        </View>
      </View>

      <View style={styles.chupAnhXungQuanhOtoView}>
        <View style={styles.columnChupAnhXungQuanhOto}>
          {renderItemChupAnhXungQuanh(5)}
          {renderItemChupAnhXungQuanh(4)}
        </View>
        <View style={styles.columnChupAnhXungQuanhOto}>
          {renderItemChupAnhXungQuanh(0)}
          <FastImage source={R.images.img_car_background_ai} style={styles.imgCarBackgroundAI} resizeMode="contain" />
          {renderItemChupAnhXungQuanh(3)}
        </View>
        <View style={styles.columnChupAnhXungQuanhOto}>
          {renderItemChupAnhXungQuanh(1)}
          {renderItemChupAnhXungQuanh(2)}
        </View>
      </View>
    </View>
  );
  const renderThongTinAI = () => (
    <View
      style={[styles.headerListAnhTongQuan, {marginBottom: spacing.small, paddingVertical: spacing.smaller, paddingHorizontal: spacing.smaller, borderRadius: 10, backgroundColor: colors.PRIMARY}]}>
      <View style={{flex: 1}}>
        <Text children={dataAI.length === 0 ? 'Chưa có hạng mục tổn thất nào' : 'Số hạng mục tổn thất (' + dataAI.length + ')'} style={[styles.txtTongQuan, {color: '#FFF'}]} font="bold14" />
      </View>
      <TouchableOpacity onPress={() => refModalAIData.current.show()}>
        <Text children="Xem chi tiết" font="regular14" style={[styles.txtChupAnh, {color: '#FFF'}]} />
      </TouchableOpacity>
    </View>
  );
  return (
    <View style={styles.contentView}>
      {renderThongTinAI()}
      {renderAnhTongQuanView()}
      {renderAnhChiTietView()}

      <ModalCameraAICYCLE ref={refModalCameraAICYCLE} handleImage={handleImage} />
      <ModalAIData ref={refModalAIData} dataAI={dataAI} setDataAI={setDataAI} />
    </View>
  );
};
const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
    marginBottom: spacing.huge,
  },
  imgAnhTongQuan: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    borderRadius: 5,
  },
  headerListAnhTongQuan: {
    flexDirection: 'row',
    flex: 1,
    marginHorizontal: spacing.small,
  },
  txtTongQuan: {
    color: 'gray',
  },
  txtChupAnh: {
    color: colors.PRIMARY,
  },
  itemListAnhTongQuan: {
    marginRight: spacing.small,
  },
  iconRemoveItemAnhTongQuan: {
    position: 'absolute',
    right: spacing.tiny,
    top: spacing.tiny,
    backgroundColor: '#FFF',
    borderRadius: 30,
  },
  anhChiTietView: {
    marginTop: spacing.small,
  },
  txtListAnhTongQuanEmpty: {
    color: 'gray',
  },
  chupAnhXungQuanhOtoView: {
    flexDirection: 'row',
  },
  imgItemChupAnhCacGoc: {
    width: dimensions.width / 5,
    height: dimensions.width / 5,
  },
  itemChupAnhXungQuanh: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.huge,
  },
  iconItemChupAnhXungQuanh: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    borderRadius: 10,
    backgroundColor: '#f1f1f1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtTitleAnhXungQuanh: {},
  titleItemChupAnhXungQuanhView: {
    paddingVertical: spacing.tiny,
    paddingHorizontal: spacing.tiny,
    backgroundColor: '#f1f1f1',
    marginBottom: spacing.tiny,
    borderRadius: 5,
  },
  columnChupAnhXungQuanhOto: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imgCarBackgroundAI: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    borderRadius: 10,
    marginBottom: spacing.huge,
  },
  imgItemAnhXungQuanh: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    borderRadius: 10,
  },
  iconCameraView: {
    backgroundColor: colors.PRIMARY_08,
    borderRadius: 30,
    width: dimensions.width / 10,
    height: dimensions.width / 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnItemAnhXungQuanh: {
    position: 'absolute',
    bottom: -dimensions.width / 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  iconLoadingAnhTongQuanView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.6,
  },
  uploadingAnhTongQuanView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  uploadThanhCongAnhTongQuanView: {
    position: 'absolute',
    right: spacing.tiny,
    bottom: spacing.tiny,
  },
});

const TakePhotoStep2AICYCLEMemo = memo(TakePhotoStep2AICYCLEComponent, isEqual);
export const TakePhotoStep2AICYCLE = TakePhotoStep2AICYCLEMemo;
