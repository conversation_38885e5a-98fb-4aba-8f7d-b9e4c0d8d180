import {colors} from '@app/commons/Theme';
import {DropdownPicker, TextInputOutlined} from '@app/components';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller} from 'react-hook-form';
import {StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalLuaChonGara} from './ModalLuaChonGara';

const inputTitle = ['Đánh giá sau khi giám định', 'Kiến nghị giải quyết', 'Lựa chọn Gara', 'Kiến nghị của Khách hàng', 'Ước tổn thất'];
const ruleRequired = {
  required: true,
};
const DanhGiaStep5MemoComponent = ({listGara, listGaraRoot, dsNghiepVu, getGaraData, doiTuongDuocChupAnh, control, errors, setValue}) => {
  let kienNghiGiaiQuyetRef = useRef(null);
  let kienNghiKhachHangRef = useRef(null);
  let uocTonThatRef = useRef(null);
  let refModalLuaChonGara = useRef(null);

  const [timeoutSearchGara, setTimeoutSearchGara] = useState(null);

  const getGaraNameByMa = (maGara) => {
    let garaName = '';
    listGaraRoot.map((item) => {
      if (item.ma === maGara) {
        garaName = item.ten;
        return garaName;
      }
    });
    return garaName;
  };

  const searchGara = (searchInput) => {
    clearTimeout(timeoutSearchGara);
    let timeoutIdTmp = setTimeout(() => {
      getGaraData(searchInput);
    }, 500);
    setTimeoutSearchGara(timeoutIdTmp);
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    else {
      if (errType === 'min' && inputName === 'uoc_ton_that') return 'Ước tổn thất phải lớn hơn 0';
    }
  };

  // RENDER
  const renderContent = () => {
    return (
      <>
        <Controller
          control={control}
          name="lh_nv"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              zIndex={8000}
              searchable={false}
              isOpen={false}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              items={dsNghiepVu}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              containerStyle={{marginBottom: spacing.default}}
              isRequired={true}
              title="Loại hình nghiệp vụ"
              placeholder="Chọn loại hình nghiệp vụ"
              error={errors.lh_nv && getErrMessage('lh_nv', errors.lh_nv.type)}
              onPress={() => {
                if (!value) FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa xác định loại hình nghiệp vụ theo đối tượng');
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="danh_gia_gdv"
          rules={ruleRequired}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              title={inputTitle[0]}
              value={value}
              isRequired
              onChangeText={onChange}
              error={errors.danh_gia_gdv && getErrMessage('danh_gia_gdv', errors.danh_gia_gdv.type)}
              placeholder={inputTitle[0]}
              onSubmitEditing={() => kienNghiGiaiQuyetRef?.focus()}
              blurOnSubmit={false}
              returnKeyType={'next'}
            />
          )}
        />
        <Controller
          control={control}
          name="y_kien"
          rules={ruleRequired}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              title={inputTitle[1]}
              value={value}
              isRequired
              onChangeText={onChange}
              error={errors.y_kien && getErrMessage('y_kien', errors.y_kien.type)}
              placeholder={inputTitle[1]}
              getRef={(ref) => (kienNghiGiaiQuyetRef = ref)}
              onSubmitEditing={() => kienNghiKhachHangRef?.focus()}
              blurOnSubmit={false}
              returnKeyType={'next'}
            />
          )}
        />

        {doiTuongDuocChupAnh?.nhom === 'XE' && (
          <Controller
            control={control}
            name="gara"
            render={({field: {value}}) => (
              <TextInputOutlined
                title="Lựa chọn Gara"
                value={getGaraNameByMa(value)}
                isTouchableOpacity
                onPress={() => refModalLuaChonGara?.current?.show()}
                placeholder="Chọn gara"
                isDropdown
                editable={false}
              />
            )}
          />
        )}

        <Controller
          control={control}
          name="y_kien_kh"
          rules={ruleRequired}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              title={inputTitle[3]}
              value={value}
              isRequired
              onChangeText={onChange}
              error={errors.y_kien_kh && getErrMessage('y_kien_kh', errors.y_kien_kh.type)}
              placeholder={inputTitle[3]}
              getRef={(ref) => (kienNghiKhachHangRef = ref)}
              onSubmitEditing={() => uocTonThatRef?.focus()}
              blurOnSubmit={false}
              returnKeyType={'next'}
            />
          )}
        />

        <Controller
          control={control}
          name="uoc_ton_that"
          rules={{...ruleRequired, min: 0}}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              title={inputTitle[4]}
              value={value}
              isRequired
              onChangeText={onChange}
              error={errors.uoc_ton_that && getErrMessage('uoc_ton_that', errors.uoc_ton_that.type)}
              placeholder={inputTitle[4]}
              keyboardType="numeric"
              getRef={(ref) => (uocTonThatRef = ref)}
            />
          )}
        />

        <ModalLuaChonGara
          ref={refModalLuaChonGara}
          setModalSelectedData={(garaSelected) => setValue('gara', garaSelected.ma)} // lấy UocTonThat theo gara và set gara luôn
          listGara={listGara}
          listGaraRoot={listGaraRoot}
          searchGara={searchGara}
        />
      </>
    );
  };
  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        contentContainerStyle={{flex: 1}}
        resetScrollToCoords={{x: 0, y: 0}} //tọa độ này sẽ được xử dụng để reset scroll khi keyboard hide
        showsVerticalScrollIndicator={false}>
        {renderContent()}
      </KeyboardAwareScrollView>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    marginHorizontal: spacing.default,
  },
  textInputType: {
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.GRAY,
    paddingHorizontal: 15,
    color: colors.BLACK,
    backgroundColor: colors.WHITE,
    flexDirection: 'row',
  },
  iconBtnTopRightView: {
    alignSelf: 'center',
  },
  txtHeaderInput: {
    marginBottom: 8,
    fontWeight: 'bold',
  },
});

const DanhGiaStep5Memo = memo(DanhGiaStep5MemoComponent, isEqual);
export const DanhGiaStep5 = DanhGiaStep5Memo;
