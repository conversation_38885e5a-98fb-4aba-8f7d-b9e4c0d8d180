import React, {memo} from 'react';
import {View, StyleSheet} from 'react-native';
import isEqual from 'react-fast-compare';
import {Text} from '@component';

const ThongKeUploadComponent = ({imageUploaded, totalImagelUpload, doiTuongDuocChupAnh}) => {
  const isDoiTuongTonThat = doiTuongDuocChupAnh.kieu_dt === 'TT';
  const renderThongKeChiTiet = (dangUpload, tongSo) => {
    return <View style={styles.thongKeChiTietView}>{dangUpload !== undefined && tongSo !== 0 && <Text children={dangUpload + '/' + tongSo} style={styles.txtThongKe} />}</View>;
  };
  return (
    <View style={styles.container}>
      {renderThongKeChiTiet(imageUploaded[0], totalImagelUpload[0])}
      {isDoiTuongTonThat && renderThongKeChiTiet(imageUploaded[1], totalImagelUpload[1])}
      {isDoiTuongTonThat && renderThongKeChiTiet(imageUploaded[2], totalImagelUpload[2])}
      {/* {isDoiTuongTonThat && renderThongKeChiTiet()} */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  thongKeChiTietView: {
    flex: 1,
  },
  txtThongKe: {
    textAlign: 'center',
  },
});

const ThongKeUploadMemo = memo(ThongKeUploadComponent, isEqual);
export const ThongKeUpload = ThongKeUploadMemo;
