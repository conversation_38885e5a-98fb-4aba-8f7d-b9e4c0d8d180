import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, spacing} from '@app/theme';
import {Empty, Icon, ImageComp, Text} from '@component';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';

const ChupAnhOfflineOtoStep1Component = ({
  profileData,
  removeImage,
  rotationImage,
  onPressOpenCamera,
  onPressXemLaiAnh,
  imagesData,
  doiTuongDuocChupAnh,
  onPressXemChiTietAnhDaUpload,
  onLoadImageError,
}) => {
  const [chonAnhTuThuVien, setChonAnhTuThuVien] = useState(false);
  // useEffect(() => {
  //   if (profileData && profileData.cau_hinh) {
  //     let cauHinhChonAnhTuThuVien = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.CHON_ANH_THU_VIEN, profileData.cau_hinh);
  //     if (cauHinhChonAnhTuThuVien?.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) setChonAnhTuThuVien(true);
  //   }
  // }, []);
  // const checkDaChupAnh = (maHangMuc) => {
  //   let anhHangMuc = anhHoSo.find((item) => item.ma_file === maHangMuc && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
  //   if (anhHangMuc) return true;
  //   return false;
  // };
  const renderImageItem = (imageData) => {
    //nếu là GIẤY CHỨNG NHẬN BẢO HIỂM đã chụp dc 2 cái ảnh, thì ẩn cái nút mở camera đi (nút có index = 2)
    // if (imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM && imageData.index === 2) return;
    return (
      <ImageComp
        imageData={imageData}
        removeImage={removeImage}
        rotationImage={rotationImage}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        onPressOpenCamera={onPressOpenCamera}
        onPressXemLaiAnh={onPressXemLaiAnh}
        uploadFromLib={chonAnhTuThuVien}
        useImageComponent={imageData?.item?.keyMapLibrary === imageData?.item?.path ? true : false}
        onLoadImageError={onLoadImageError}
      />
    );
  };

  const renderItemAnhDaUpload = ({item, index}, extraData) => (
    <TouchableOpacity
      onPress={() => {
        NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
          currentImageData: {item, index},
          imagesData: extraData.listAnhHangMuc,
        });
      }}>
      <FastImage style={styles.imageProcess} source={{uri: `data:image/gif;base64,${item.duong_dan}`}} resizeMode={'contain'} />
    </TouchableOpacity>
  );

  //render ra 1 item của menu
  const renderItemMenuImage = ({item, index}) => {
    let hienThiBoSungThongTin = false;
    if (item.nhom_hang_muc === 'BANG_LAI' || item.nhom_hang_muc === 'DANG_KIEM') hienThiBoSungThongTin = true;
    let daCoTrenHeThong = false;
    let txtChuaCoTrenHeThong = 'Chưa có trên hệ thống';
    //nếu là mục SỐ KHUNG / SỐ MÁY
    if (item.nhom_hang_muc && typeof item.nhom_hang_muc === 'object') {
      let anhSoKhung = item.anhDaUpload.find((itemImage) => itemImage.nhom_hang_muc === 'SO_KHUNG');
      let anhSoMay = item.anhDaUpload.find((itemImage) => itemImage.nhom_hang_muc === 'SO_MAY');
      if (anhSoKhung && anhSoMay) daCoTrenHeThong = true;
      else if (!anhSoKhung || !anhSoMay) {
        txtChuaCoTrenHeThong = '';
        if (!anhSoKhung) txtChuaCoTrenHeThong = 'Số khung';
        if (!anhSoMay) txtChuaCoTrenHeThong += (txtChuaCoTrenHeThong === 'Số khung' ? ', ' : ' ') + 'Số máy';
        txtChuaCoTrenHeThong += ' chưa có trên hệ thống';
      }
    } else daCoTrenHeThong = item.anhDaUpload.length > 0;
    // const isAnhToanCanh = item.nhom.nhom === 'ANH_TOAN_CANH';
    return (
      <View style={[styles.itemMenuView, index % 2 === 0 && {backgroundColor: colors.WHITE8}]}>
        <View style={styles.imageCategoryTitleView}>
          <View style={styles.imageCategoryTitleView}>
            <View style={styles.menuImageTitleView}>
              <View>
                <Text style={[styles.imageCategoryTitle, !hienThiBoSungThongTin && {flex: 1}]}>{item.ten}</Text>
                <Text font="regular12" style={[styles.txtTrangThaiAnh, {color: daCoTrenHeThong ? colors.GREEN : colors.PRIMARY}]}>
                  {daCoTrenHeThong ? 'Đã có trên hệ thống' : 'Chưa có trên hệ thống'}
                </Text>
              </View>
            </View>
            <View style={styles.menuImageActionView}>
              {/* {hienThiBoSungThongTin && (
              <TouchableOpacity
                style={styles.thongTinChiTietView}
                onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.BO_SUNG_THONG_TIN_GIAY_TO, {thongTinGiayTo: item, profileData, doiTuongDuocChupAnh})}>
                <Text children="Thông tin chi tiết" style={[styles.imageCategoryTitle, {marginRight: spacing.tiny, color: colors.PRIMARY}]} />
                <Icon.FontAwesome name={'angle-down'} size={18} color={colors.PRIMARY} />
              </TouchableOpacity>
            )} */}
              <TouchableOpacity style={styles.thongTinChiTietView} onPress={() => onPressXemChiTietAnhDaUpload(index)}>
                <Text children={`Ảnh đã tải lên (${item.anhDaUpload.length})`} color={colors.PRIMARY} style={{marginRight: spacing.tiny}} />
                <Icon.FontAwesome name={item.isShowAnhDaUpload ? 'angle-down' : 'angle-up'} size={18} color={colors.PRIMARY} />
              </TouchableOpacity>
            </View>
          </View>
          {/* {isAnhToanCanh && (
            <TouchableOpacity style={[styles.thongTinChiTietView]} onPress={onPressShowDuLieuAI} disabled={dataAI.length === 0}>
              <Text children={`${dataAI.length > 0 ? dataAI.length : ''} hạng mục [AI]`} style={[styles.imageCategoryTitle, {color: dataAI.length === 0 ? colors.GRAY6 : colors.PRIMARY}]} />
              <Icon.Entypo name="chevron-small-right" size={20} color={dataAI.length === 0 ? colors.GRAY6 : colors.PRIMARY} />
            </TouchableOpacity>
          )} */}
        </View>

        {item.isShowAnhDaUpload && (
          <View style={styles.anhDaUploadView}>
            <Text children="Ảnh đã tải lên hệ thống" style={[styles.imageCategoryTitle, {fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
            {item.anhDaUpload.length > 0 && (
              <FlatList
                keyExtractor={(item, index) => index.toString()}
                scrollEnabled={true}
                data={item.anhDaUpload}
                renderItem={(itemAnhDaUpload) => renderItemAnhDaUpload(itemAnhDaUpload, {listAnhHangMuc: item.anhDaUpload})}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
              />
            )}
            {item.anhDaUpload.length === 0 && (
              <View style={{marginBottom: spacing.default}}>
                <Empty imageStyle={{width: dimensions.width / 5, height: dimensions.width / 5}} />
              </View>
            )}
          </View>
        )}
        <View>
          {item.isShowAnhDaUpload && (
            <Text children="Ảnh chưa tải lên hệ thống" style={[styles.imageCategoryTitle, {marginLeft: spacing.default, fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
          )}

          <FlatList
            keyExtractor={(item, index) => index.toString()}
            scrollEnabled={true}
            data={item.images}
            renderItem={(itemAnh) => renderImageItem(itemAnh, {images: item.images})}
            numColumns={2}
            horizontal={false}
          />
        </View>
      </View>
    );
    // else return null;
  };

  return (
    <View style={{flex: 1, marginBottom: spacing.huge}}>
      <View style={styles.canhBaoChiTietHoSoView}>
        <Icon.Ionicons name="warning" color={colors.ORANGE} size={20} style={{marginRight: spacing.tiny}} />
        <Text children="Vui lòng không sửa tên, xoá ảnh đã lưu trong thư viện đẻ tính năng hoạt động chính xác" color={colors.ORANGE} />
      </View>
      <FlatList data={imagesData} renderItem={renderItemMenuImage} initialNumToRender={imagesData.length} keyExtractor={(item, index) => index.toString()} />
    </View>
  );
};
const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    // width: dimensions.width,
  },
  imageCategoryTitle: {
    // color: colors.BLACK,
    // fontWeight: 'bold',
    // marginLeft: spacing.smaller,
  },
  txtTrangThaiAnh: {
    // marginLeft: 10,
  },
  imageCategoryTitleView: {
    flexDirection: 'row',
    // borderBottomWidth: 1,
    borderColor: colors.GRAY,
    marginHorizontal: spacing.default,
    // paddingBottom: 5,
    marginTop: spacing.smaller,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemMenuView: {
    // marginVertical: 5,
    paddingBottom: spacing.smaller,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY11,
  },
  txtBoSungThongTin: {
    color: '#000',
  },
  menuImageTitleView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: spacing.default,
  },
  thongTinChiTietView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageProcess: {
    marginRight: spacing.default,
    marginVertical: spacing.default,
    borderRadius: 8,
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    resizeMode: 'cover',
    backgroundColor: colors.GRAY2,
    // borderWidth: 1,
  },
  menuImageActionView: {
    alignItems: 'flex-end',
  },
  anhDaUploadView: {
    borderTopWidth: 0.3,
    borderBottomWidth: 0.3,
    marginHorizontal: spacing.default,
    marginTop: spacing.smaller,
    paddingTop: spacing.smaller,
    marginBottom: spacing.default,
    borderColor: colors.GRAY3,
  },
  txtTaiLai: {
    textDecorationLine: 'underline',
  },
  canhBaoChiTietHoSoView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: spacing.default,
    marginBottom: spacing.default,
  },
});

const ChupAnhOfflineOtoStep1Memo = memo(ChupAnhOfflineOtoStep1Component, isEqual);
export const ChupAnhOfflineOtoStep1 = ChupAnhOfflineOtoStep1Memo;
