import R from '@R';

const MA_HANG_MUC_TRUOC_XE = {
  KINH_CHAN_GIO: '01588',
  GUONG_CHIEU_HAU_BEN_PHU: '01394',
  GUONG_CHIEU_HAU_BEN_LAI: '01392',
  NAP_CAPO: '01831',
  DEN_PHA_BEN_LAI: '01138',
  DEN_PHA_BEN_PHU: '01139',
  CAN_TRUOC: '00505',
  CAN_TRUOC_BEN_LAI: '00505',
  CAN_TRUOC_BEN_PHU: '00505',
  NOC_XE: '02008',
  LOP_TRUOC_BEN_LAI: '01698',
  LOP_TRUOC: '01697',
  CA_LANG: '00399',
  //các hạng mục có thể thêm : Ca lăng, ốp gi<PERSON>a cản trước, l<PERSON><PERSON><PERSON> tản nhi<PERSON>; ốp đèn gầm bl, bp; nắp che móc kéo, nẹp đèn pha; ốp cua lốp; ốp tai; cảm biến; ốp trên ca lăng
};

const MA_HANG_MUC_SAU_XE = {
  KINH_CHAN_GIO_HAU: '01589',
  GUONG_CHIEU_HAU_BEN_PHU: '01393',
  GUONG_CHIEU_HAU_BEN_LAI: '01392',
  DEN_BAO_PHANH: '01118',
  NOC_XE: '02008',
  LOP_SAU: '01696',
  COP_SAU: '00862',
  CAN_SAU: '00093',
  CAN_SAU_BEN_LAI: '00093',
  CAN_SAU_BEN_PHU: '00093',
};

const MA_HANG_MUC_BEN_LAI = {
  LOP_TRUOC_BEN_LAI: '01698',
  LOP_SAU: '01696',
  LOP_TRUOC: '01697',
  MAM_BANH_XE: '01724',
  CUA_TRUOC_BEN_LAI: '00916',
  CUA_SAU_BEN_LAI: '00534',
  TAY_NAM_CUA_TRUOC_BEN_LAI: '02704',
  KINH_CUA_TRUOC_BEN_LAI: '01604',
  KINH_CUA_SAU_BEN_LAI: '01602',
  DEN_BAO_PHANH: '01118',
  HONG_SAU_BEN_LAI: '01438',
  OP_LUON_DE_BEN_LAI: '02270',
  BO_NOC_BEN_LAI: '00301',
  TAI_XE_BEN_LAI: '02575',
};

const MA_HANG_MUC_BEN_PHU = {
  LOP_SAU: '01777',
  LOP_TRUOC: '01777',
  MAM_BANH_XE: '01724',
  DEN_BAO_PHANH: '01118',
  KINH_CUA_SAU_BEN_PHU: '01603',
  KINH_CUA_TRUOC_BEN_PHU: '01605',
  CUA_TRUOC_BEN_PHU: '00537',
  CUA_SAU_BEN_PHU: '00535',
  BO_NOC_BEN_PHU: '00302',
  TAI_XE_BEN_PHU: '02576',
  HONG_SAU_BEN_PHU: '01439',
  OP_LUON_DE_BEN_PHU: '02271',
};

export const anhXungQuanhXe = [
  {
    id: 0,
    image: R.images.img_hang_muc_truoc_xe_doc,
    imageNgang: R.images.img_hang_muc_truoc_xe_ngang,
    name: 'Toàn bộ',
    key: null,
  },
  {
    id: 1,
    image: R.images.img_hang_muc_truoc_xe_doc,
    imageNgang: R.images.img_hang_muc_truoc_xe_ngang,
    name: 'Trước xe',
    key: 'TRUOC',
  },
  {id: 2, image: R.images.img_hang_muc_ben_phu_doc, imageNgang: R.images.img_hang_muc_ben_phu_ngang, name: 'Bên phụ', key: 'PHAI'},
  {id: 3, image: R.images.img_hang_muc_sau_xe_doc, imageNgang: R.images.img_hang_muc_sau_xe_ngang, name: 'Sau xe', key: 'SAU'},
  {
    id: 4,
    image: R.images.img_hang_muc_ben_lai_doc,
    imageNgang: R.images.img_hang_muc_ben_lai_ngang,
    name: 'Bên lái',
    key: 'TRAI',
  },
];

/* 
    DO THUẬT TOÁN ÁP DỤNG CHỈ DÀNH CHO ĐA GIÁC LỒI -> XÁC ĐỊNH CÁC ĐIỂM CỦA HÌNH ĐỂ TẠO THÀNH ĐA GIÁC LỒI
    NẾU CẦN CHI TIẾT THÌ TÁCH THÀNH CÁC ĐA GIÁC LỒI BÉ 
  */
export const TOA_DO_ANH_TRUOC_XE = {
  /* KÍNH CHẮN GIÓ */
  KINH_CHAN_GIO_DOC: {
    nhomHangMuc: 'KINH_CHAN_GIO_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.KINH_CHAN_GIO,
    TOA_DO: [
      {
        x: 191,
        y: 185,
      },
      {
        x: 385,
        y: 173,
      },
      {
        x: 606,
        y: 184,
      },
      {
        x: 655,
        y: 297,
      },
      {
        x: 146,
        y: 296,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 400, // x > x1
      y: 239, // y > y1
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  KINH_CHAN_GIO_NGANG: {
    nhomHangMuc: 'KINH_CHAN_GIO_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.KINH_CHAN_GIO,
    TOA_DO: [
      {
        x: 614,
        y: 335,
      },
      {
        x: 622,
        y: 699,
      },
      {
        x: 615,
        y: 1070,
      },
      {
        x: 502,
        y: 1163,
      },
      {
        x: 503,
        y: 257,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 557, // x > x1
      y: 726, // y > y1
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* NẮP CAPO */
  NAP_CABO_DOC: {
    nhomHangMuc: 'NAP_CABO_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.NAP_CAPO,
    TOA_DO: [
      {
        x: 127,
        y: 315,
      },
      {
        x: 395,
        y: 305,
      },
      {
        x: 671,
        y: 315,
      },
      {
        x: 664,
        y: 385,
      },
      {
        x: 400,
        y: 389,
      },
      {
        x: 137,
        y: 384,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 400, // x > x1
      y: 353, // y > y1
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  NAP_CABO_DOC_NGANG: {
    nhomHangMuc: 'NAP_CABO_DOC_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.NAP_CAPO,
    TOA_DO: [
      {
        x: 487,
        y: 216,
      },
      {
        x: 493,
        y: 706,
      },
      {
        x: 495,
        y: 1193,
      },
      {
        x: 414,
        y: 1173,
      },
      {
        x: 401,
        y: 732,
      },
      {
        x: 418,
        y: 240,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 444, // x > x1
      y: 707, // y > y1
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CA LĂNG */
  CA_LANG_DOC: {
    nhomHangMuc: 'CA_LANG_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.CA_LANG,
    TOA_DO: [
      {
        x: 259,
        y: 402,
      },
      {
        x: 393,
        y: 390,
      },
      {
        x: 534,
        y: 398,
      },
      {
        x: 532,
        y: 451,
      },
      {
        x: 262,
        y: 450,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 400,
      y: 420,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CA_LANG_NGANG: {
    nhomHangMuc: 'CA_LANG_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.CA_LANG,
    TOA_DO: [
      {
        x: 400,
        y: 460,
      },
      {
        x: 409,
        y: 701,
      },
      {
        x: 400,
        y: 952,
      },
      {
        x: 346,
        y: 944,
      },
      {
        x: 350,
        y: 467,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 375,
      y: 700,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* ĐÈN PHA BÊN PHỤ*/
  DEN_PHA_BEN_PHU_DOC: {
    nhomHangMuc: 'DEN_PHA_BEN_PHU_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.DEN_PHA_BEN_PHU,
    TOA_DO: [
      {
        x: 149,
        y: 384,
      },
      {
        x: 230,
        y: 394,
      },
      {
        x: 237,
        y: 437,
      },
      {
        x: 144,
        y: 433,
      },
      {
        x: 135,
        y: 411,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 182,
      y: 412,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  DEN_PHA_BEN_PHU_NGANG: {
    nhomHangMuc: 'DEN_PHA_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.DEN_PHA_BEN_PHU,
    TOA_DO: [
      {
        x: 414,
        y: 258,
      },
      {
        x: 407,
        y: 405,
      },
      {
        x: 361,
        y: 422,
      },
      {
        x: 373,
        y: 258,
      },
      {
        x: 391,
        y: 240,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 390,
      y: 323,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* ĐÈN PHA BÊN LÁI*/
  DEN_PHA_BEN_LAI_DOC: {
    nhomHangMuc: 'DEN_PHA_BEN_LAI_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.DEN_PHA_BEN_LAI,
    TOA_DO: [
      {
        x: 567,
        y: 395,
      },
      {
        x: 645,
        y: 347,
      },
      {
        x: 660,
        y: 408,
      },
      {
        x: 650,
        y: 434,
      },
      {
        x: 558,
        y: 437,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 607,
      y: 414,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  DEN_PHA_BEN_LAI_NGANG: {
    nhomHangMuc: 'DEN_PHA_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.DEN_PHA_BEN_LAI,
    TOA_DO: [
      {
        x: 400,
        y: 1004,
      },
      {
        x: 415,
        y: 1140,
      },
      {
        x: 388,
        y: 1172,
      },
      {
        x: 367,
        y: 1155,
      },
      {
        x: 360,
        y: 990,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 386,
      y: 1080,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CẢN TRƯỚC */
  CAN_TRUOC_DOC: {
    nhomHangMuc: 'CAN_TRUOC_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC,
    TOA_DO: [
      {
        x: 265,
        y: 468,
      },
      {
        x: 531,
        y: 468,
      },
      {
        x: 508,
        y: 567,
      },
      {
        x: 285,
        y: 566,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 403,
      y: 522,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_TRUOC_NGANG: {
    nhomHangMuc: 'CAN_TRUOC_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC,
    TOA_DO: [
      {
        x: 328,
        y: 437,
      },
      {
        x: 329,
        y: 939,
      },
      {
        x: 229,
        y: 898,
      },
      {
        x: 230,
        y: 512,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 281,
      y: 709,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* HÌNH CẢN TRƯỚC BÊN PHỤ DO PHỨC TẠP NÊN PHẢI TÁCH THÀNH 2 HÌNH A B*/
  /* CẢN TRƯỚC BÊN PHỤ A */
  CAN_TRUOC_BEN_PHU_DOC_A: {
    nhomHangMuc: 'CAN_TRUOC_BEN_PHU_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 96,
        y: 464,
      },
      {
        x: 112,
        y: 467,
      },
      {
        x: 125,
        y: 543,
      },
      {
        x: 104,
        y: 544,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 107,
      y: 504,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_TRUOC_BEN_PHU_NGANG_A: {
    nhomHangMuc: 'CAN_TRUOC_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 336,
        y: 168,
      },
      {
        x: 337,
        y: 200,
      },
      {
        x: 254,
        y: 223,
      },
      {
        x: 258,
        y: 187,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 295,
      y: 193,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CẢN TRƯỚC BÊN PHỤ B */
  CAN_TRUOC_BEN_PHU_DOC_B: {
    nhomHangMuc: 'CAN_TRUOC_BEN_PHU_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 117,
        y: 145,
      },
      {
        x: 267,
        y: 480,
      },
      {
        x: 279,
        y: 569,
      },
      {
        x: 148,
        y: 565,
      },
      {
        x: 124,
        y: 544,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 200,
      y: 530,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_TRUOC_BEN_PHU_NGANG_B: {
    nhomHangMuc: 'CAN_TRUOC_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 323,
        y: 208,
      },
      {
        x: 318,
        y: 457,
      },
      {
        x: 230,
        y: 502,
      },
      {
        x: 232,
        y: 287,
      },
      {
        x: 257,
        y: 227,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 280,
      y: 370,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* HÌNH CẢN TRƯỚC BÊN LÁI DO PHỨC TẠP NÊN PHẢI TÁCH THÀNH 2 HÌNH A B*/
  /* CẢN TRƯỚC BÊN LÁI A */
  CAN_TRUOC_BEN_LAI_DOC_A: {
    nhomHangMuc: 'CAN_TRUOC_BEN_LAI_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 683,
        y: 465,
      },
      {
        x: 700,
        y: 464,
      },
      {
        x: 690,
        y: 544,
      },
      {
        x: 670,
        y: 544,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 686,
      y: 507,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_TRUOC_BEN_LAI_NGANG_A: {
    nhomHangMuc: 'CAN_TRUOC_BEN_LAI_NGANG_A',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 331,
        y: 1209,
      },
      {
        x: 336,
        y: 1241,
      },
      {
        x: 254,
        y: 1225,
      },
      {
        x: 253,
        y: 1184,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 319,
      y: 1215,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CẢN TRƯỚC BÊN LÁI B */
  CAN_TRUOC_BEN_LAI_DOC_B: {
    nhomHangMuc: 'CAN_TRUOC_BEN_LAI_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 528,
        y: 480,
      },
      {
        x: 678,
        y: 477,
      },
      {
        x: 666,
        y: 545,
      },
      {
        x: 637,
        y: 568,
      },
      {
        x: 516,
        y: 567,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 590,
      y: 531,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_TRUOC_BEN_LAI_NGANG_B: {
    nhomHangMuc: 'CAN_TRUOC_BEN_LAI_NGANG_B',
    ma: MA_HANG_MUC_TRUOC_XE.CAN_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 318,
        y: 953,
      },
      {
        x: 319,
        y: 1205,
      },
      {
        x: 253,
        y: 1184,
      },
      {
        x: 231,
        y: 1137,
      },
      {
        x: 229,
        y: 910,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 277,
      y: 1068,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* KÍNH CHIẾU HẬU BÊN PHỤ */
  GUONG_CHIEU_HAU_BEN_PHU_DOC: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_PHU_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.GUONG_CHIEU_HAU_BEN_PHU,
    TOA_DO: [
      {
        x: 52,
        y: 272,
      },
      {
        x: 114,
        y: 262,
      },
      {
        x: 113,
        y: 311,
      },
      {
        x: 39,
        y: 305,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 74,
      y: 284,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  GUONG_CHIEU_HAU_BEN_PHU_NGANG: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.GUONG_CHIEU_HAU_BEN_PHU,
    TOA_DO: [
      {
        x: 528,
        y: 87,
      },
      {
        x: 538,
        y: 203,
      },
      {
        x: 283,
        y: 198,
      },
      {
        x: 496,
        y: 68,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 513,
      y: 137,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* KÍNH CHIẾU HẬU BÊN LÁI */
  GUONG_CHIEU_HAU_BEN_LAI_DOC: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_LAI_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.GUONG_CHIEU_HAU_BEN_LAI,
    TOA_DO: [
      {
        x: 685,
        y: 264,
      },
      {
        x: 749,
        y: 272,
      },
      {
        x: 762,
        y: 304,
      },
      {
        x: 681,
        y: 314,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 720,
      y: 286,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  GUONG_CHIEU_HAU_BEN_LAI_NGANG: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.GUONG_CHIEU_HAU_BEN_LAI,
    TOA_DO: [
      {
        x: 538,
        y: 1217,
      },
      {
        x: 532,
        y: 1334,
      },
      {
        x: 479,
        y: 1354,
      },
      {
        x: 486,
        y: 1213,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 511,
      y: 1332,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* NÓC XE */
  NOC_XE_DOC: {
    nhomHangMuc: 'NOC_XE_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.NOC_XE,
    TOA_DO: [
      {
        x: 199,
        y: 151,
      },
      {
        x: 388,
        y: 138,
      },
      {
        x: 595,
        y: 151,
      },
      {
        x: 612,
        y: 175,
      },
      {
        x: 184,
        y: 177,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 406,
      y: 156,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  NOC_XE_NGANG: {
    nhomHangMuc: 'NOC_XE_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.NOC_XE,
    TOA_DO: [
      {
        x: 647,
        y: 354,
      },
      {
        x: 659,
        y: 707,
      },
      {
        x: 646,
        y: 1056,
      },
      {
        x: 617,
        y: 1085,
      },
      {
        x: 618,
        y: 326,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 646,
      y: 703,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* LỐP TRƯỚC */
  LOP_TRUOC_DOC: {
    nhomHangMuc: 'LOP_TRUOC_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.LOP_TRUOC,
    TOA_DO: [
      {
        x: 82,
        y: 483,
      },
      {
        x: 180,
        y: 589,
      },
      {
        x: 159,
        y: 658,
      },
      {
        x: 80,
        y: 658,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 120,
      y: 620,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  LOP_TRUOC_NGANG: {
    nhomHangMuc: 'LOP_TRUOC_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.LOP_TRUOC,
    TOA_DO: [
      {
        x: 320,
        y: 138,
      },
      {
        x: 210,
        y: 278,
      },
      {
        x: 138,
        y: 278,
      },
      {
        x: 140,
        y: 140,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 177,
      y: 211,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* LỐP TRƯỚC BÊN LÁI*/
  LOP_TRUOC_BEN_LAI_DOC: {
    nhomHangMuc: 'LOP_TRUOC_BEN_LAI_DOC',
    ma: MA_HANG_MUC_TRUOC_XE.LOP_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 717,
        y: 483,
      },
      {
        x: 719,
        y: 657,
      },
      {
        x: 637,
        y: 658,
      },
      {
        x: 636,
        y: 589,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 674,
      y: 621,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  LOP_TRUOC_BEN_LAI_NGANG: {
    nhomHangMuc: 'LOP_TRUOC_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_TRUOC_XE.LOP_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 319,
        y: 1271,
      },
      {
        x: 141,
        y: 1274,
      },
      {
        x: 140,
        y: 1136,
      },
      {
        x: 207,
        y: 1130,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 184,
      y: 1205,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
};
export const TOA_DO_ANH_BEN_PHU_XE = {
  /* KÍNH CỬA SAU BÊN PHỤ */
  KINH_CUA_SAU_BEN_PHU_DOC: {
    nhomHangMuc: 'KINH_CUA_SAU_BEN_PHU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.KINH_CUA_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 208,
        y: 292,
      },
      {
        x: 294,
        y: 286,
      },
      {
        x: 296,
        y: 341,
      },
      {
        x: 157,
        y: 337,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 233,
      y: 319,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  KINH_CUA_SAU_BEN_PHU_NGANG: {
    nhomHangMuc: 'KINH_CUA_SAU_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.KINH_CUA_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 501,
        y: 414,
      },
      {
        x: 509,
        y: 589,
      },
      {
        x: 456,
        y: 591,
      },
      {
        x: 459,
        y: 311,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 482, // x > x1
      y: 484, // y > y1
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* KÍNH CỬA TRƯỚC BÊN PHỤ */
  KINH_CUA_TRUOC_BEN_PHU_DOC: {
    nhomHangMuc: 'KINH_CUA_TRUOC_BEN_PHU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.KINH_CUA_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 323,
        y: 287,
      },
      {
        x: 417,
        y: 296,
      },
      {
        x: 478,
        y: 328,
      },
      {
        x: 479,
        y: 354,
      },
      {
        x: 329,
        y: 347,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 390,
      y: 323,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  KINH_CUA_TRUOC_BEN_PHU_NGANG: {
    nhomHangMuc: 'KINH_CUA_TRUOC_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.KINH_CUA_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 512,
        y: 640,
      },
      {
        x: 496,
        y: 875,
      },
      {
        x: 469,
        y: 859,
      },
      {
        x: 444,
        y: 957,
      },
      {
        x: 449,
        y: 659,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 479, // x > x1
      y: 767, // y > y1
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CỬA SAU BÊN PHỤ */
  CUA_SAU_BEN_PHU_DOC: {
    nhomHangMuc: 'CUA_SAU_BEN_PHU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.CUA_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 150,
        y: 345,
      },
      {
        x: 315,
        y: 350,
      },
      {
        x: 323,
        y: 355,
      },
      {
        x: 239,
        y: 454,
      },
      {
        x: 157,
        y: 379,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 170,
      y: 353,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CUA_SAU_BEN_PHU_NGANG: {
    nhomHangMuc: 'CUA_SAU_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.CUA_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 450,
        y: 299,
      },
      {
        x: 445,
        y: 626,
      },
      {
        x: 345,
        y: 647,
      },
      {
        x: 346,
        y: 473,
      },
      {
        x: 418,
        y: 318,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 410,
      y: 500,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CỬA SAU BÊN PHỤ */
  CUA_TRUOC_BEN_PHU_DOC: {
    nhomHangMuc: 'CUA_TRUOC_BEN_PHU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.CUA_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 312,
        y: 349,
      },
      {
        x: 527,
        y: 361,
      },
      {
        x: 537,
        y: 456,
      },
      {
        x: 323,
        y: 454,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 430,
      y: 400,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CUA_TRUOC_BEN_PHU_NGANG: {
    nhomHangMuc: 'CUA_TRUOC_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.CUA_TRUOC_BEN_PHU,
    TOA_DO: [
      {
        x: 451,
        y: 615,
      },
      {
        x: 442,
        y: 1059,
      },
      {
        x: 341,
        y: 1071,
      },
      {
        x: 347,
        y: 649,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 390,
      y: 827,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  // VỚI HÌNH TRÒN THÌ SO SÁNH THEO KIỂU TÂM - CHIỀU DÀI BÁN KÍNH
  /* MÂM BÁNH XE PHỤ SAU */
  MAM_BANH_XE_BEN_PHU_SAU_DOC: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_PHU_SAU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.MAM_BANH_XE,
    TOA_DO: [
      // TOẠ ĐỘ TÂM
      {
        x: 155,
        y: 468,
      },
      // TOẠ ĐỘ ĐIỂM Ở ĐƯỜNG TRÒN
      {
        x: 197,
        y: 468,
      },
    ],
    type: 'DOC', //hình đang nằm doc
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  MAM_BANH_XE_BEN_PHU_SAU_NGANG: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_PHU_SAU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.MAM_BANH_XE,
    TOA_DO: [
      {
        x: 330,
        y: 310,
      },
      {
        x: 383,
        y: 310,
      },
    ],
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  /* MÂM BÁNH XE PHỤ TRUOC */
  MAM_BANH_XE_BEN_PHU_TRUOC_DOC: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_PHU_TRUOC_DOC',
    ma: MA_HANG_MUC_BEN_PHU.MAM_BANH_XE,
    TOA_DO: [
      // TOẠ ĐỘ TÂM
      {
        x: 621,
        y: 469,
      },
      // TOẠ ĐỘ ĐIỂM Ở ĐƯỜNG TRÒN
      {
        x: 663,
        y: 469,
      },
    ],
    type: 'DOC', //hình đang nằm doc
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  MAM_BANH_XE_BEN_PHU_TRUOC_NGANG: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_PHU_TRUOC_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.MAM_BANH_XE,
    TOA_DO: [
      {
        x: 330,
        y: 1242,
      },
      {
        x: 378,
        y: 1242,
      },
    ],
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  /* HÔNG SAU BÊN PHỤ */
  HONG_SAU_BEN_PHU_DOC_A: {
    nhomHangMuc: 'HONG_SAU_BEN_PHU_DOC_A',
    ma: MA_HANG_MUC_BEN_PHU.HONG_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 99,
        y: 346,
      },
      {
        x: 147,
        y: 344,
      },
      {
        x: 148,
        y: 380,
      },
      {
        x: 89,
        y: 384,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 120,
      y: 366,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  HONG_SAU_BEN_PHU_NGANG_A: {
    nhomHangMuc: 'HONG_SAU_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.HONG_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 452,
        y: 191,
      },
      {
        x: 457,
        y: 292,
      },
      {
        x: 418,
        y: 295,
      },
      {
        x: 414,
        y: 176,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 433,
      y: 245,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  HONG_SAU_BEN_PHU_DOC_B: {
    nhomHangMuc: 'HONG_SAU_BEN_PHU_DOC_B',
    ma: MA_HANG_MUC_BEN_PHU.HONG_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 38,
        y: 384,
      },
      {
        x: 133,
        y: 385,
      },
      {
        x: 77,
        y: 459,
      },
      {
        x: 22,
        y: 444,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 66,
      y: 407,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  HONG_SAU_BEN_PHU_NGANG_B: {
    nhomHangMuc: 'HONG_SAU_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.HONG_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 415,
        y: 75,
      },
      {
        x: 412,
        y: 240,
      },
      {
        x: 341,
        y: 146,
      },
      {
        x: 362,
        y: 39,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 400,
      y: 123,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* ỐP LƯỜN DÈ BÊN PHỤ */
  OP_LUON_DE_BEN_PHU_DOC: {
    nhomHangMuc: 'OP_LUON_DE_BEN_PHU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.OP_LUON_DE_BEN_PHU,
    TOA_DO: [
      {
        x: 223,
        y: 466,
      },
      {
        x: 537,
        y: 470,
      },
      {
        x: 538,
        y: 477,
      },
      {
        x: 222,
        y: 476,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 351,
      y: 469,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  OP_LUON_DE_BEN_PHU_NGANG: {
    nhomHangMuc: 'OP_LUON_DE_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.OP_LUON_DE_BEN_PHU,
    TOA_DO: [
      {
        x: 325,
        y: 444,
      },
      {
        x: 324,
        y: 1103,
      },
      {
        x: 308,
        y: 1105,
      },
      {
        x: 308,
        y: 445,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 317,
      y: 481,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* ĐÈN BÁO PHANH */
  DEN_BAO_PHANH_BEN_PHU_DOC: {
    nhomHangMuc: 'DEN_BAO_PHANH_BEN_PHU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 41,
        y: 358,
      },
      {
        x: 93,
        y: 357,
      },
      {
        x: 89,
        y: 377,
      },
      {
        x: 39,
        y: 374,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 62,
      y: 368,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  DEN_BAO_PHANH_BEN_PHU_NGANG: {
    nhomHangMuc: 'DEN_BAO_PHANH_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 442,
        y: 80,
      },
      {
        x: 441,
        y: 181,
      },
      {
        x: 422,
        y: 169,
      },
      {
        x: 424,
        y: 77,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 429,
      y: 137,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* TAI XE BÊN PHỤ */
  TAI_XE_BEN_PHU_DOC_A: {
    nhomHangMuc: 'TAI_XE_BEN_PHU_DOC_A',
    ma: MA_HANG_MUC_BEN_PHU.TAI_XE_BEN_PHU,
    TOA_DO: [
      {
        x: 527,
        y: 348,
      },
      {
        x: 623,
        y: 357,
      },
      {
        x: 534,
        y: 445,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 553,
      y: 377,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  TAI_XE_BEN_PHU_NGANG_A: {
    nhomHangMuc: 'TAI_XE_BEN_PHU_NGANG_A',
    ma: MA_HANG_MUC_BEN_PHU.TAI_XE_BEN_PHU,
    TOA_DO: [
      {
        x: 441,
        y: 1073,
      },
      {
        x: 428,
        y: 1026,
      },
      {
        x: 336,
        y: 1077,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 415,
      y: 1112,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  TAI_XE_BEN_PHU_DOC_B: {
    nhomHangMuc: 'TAI_XE_BEN_PHU_DOC_B',
    ma: MA_HANG_MUC_BEN_PHU.TAI_XE_BEN_PHU,
    TOA_DO: [
      {
        x: 624,
        y: 368,
      },
      {
        x: 708,
        y: 389,
      },
      {
        x: 697,
        y: 427,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 684,
      y: 393,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  TAI_XE_BEN_PHU_NGANG_B: {
    nhomHangMuc: 'TAI_XE_BEN_PHU_NGANG_B',
    ma: MA_HANG_MUC_BEN_PHU.TAI_XE_BEN_PHU,
    TOA_DO: [
      {
        x: 429,
        y: 1214,
      },
      {
        x: 406,
        y: 1417,
      },
      {
        x: 368,
        y: 1395,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 406,
      y: 1374,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* BO NÓC XE BÊN PHỤ */
  BO_NOC_XE_BEN_PHU_DOC: {
    nhomHangMuc: 'BO_NOC_XE_BEN_PHU_DOC',
    ma: MA_HANG_MUC_BEN_PHU.BO_NOC_BEN_PHU,
    TOA_DO: [
      {
        x: 153,
        y: 290,
      },
      {
        x: 210,
        y: 284,
      },
      {
        x: 143,
        y: 342,
      },
      {
        x: 71,
        y: 343,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 139,
      y: 317,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  BO_NOC_XE_BEN_PHU_NGANG: {
    nhomHangMuc: 'BO_NOC_XE_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_BEN_PHU.BO_NOC_BEN_PHU,
    TOA_DO: [
      {
        x: 499,
        y: 296,
      },
      {
        x: 510,
        y: 398,
      },
      {
        x: 455,
        y: 294,
      },
      {
        x: 456,
        y: 143,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 477,
      y: 276,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
};
export const TOA_DO_ANH_SAU_XE = {
  /* KÍNH CHẮN GIÓ HẬU */
  KINH_CHAN_GIO_HAU_DOC: {
    nhomHangMuc: 'KINH_CHAN_GIO_HAU_DOC',
    ma: MA_HANG_MUC_SAU_XE.KINH_CHAN_GIO_HAU,
    TOA_DO: [
      {
        x: 210,
        y: 185,
      },
      {
        x: 400,
        y: 173,
      },
      {
        x: 588,
        y: 185,
      },
      {
        x: 623,
        y: 277,
      },
      {
        x: 178,
        y: 278,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 395,
      y: 231,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  KINH_CHAN_GIO_HAU_NGANG: {
    nhomHangMuc: 'KINH_CHAN_GIO_HAU_NGANG',
    ma: MA_HANG_MUC_SAU_XE.KINH_CHAN_GIO_HAU,
    TOA_DO: [
      {
        x: 612,
        y: 375,
      },
      {
        x: 616,
        y: 1046,
      },
      {
        x: 520,
        y: 1106,
      },
      {
        x: 524,
        y: 314,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 572, // x > x1
      y: 707, // y > y1
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* GƯƠNG CHIẾU HẬU BÊN PHỤ */
  GUONG_CHIEU_HAU_BEN_PHU_DOC: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_PHU_DOC',
    ma: MA_HANG_MUC_SAU_XE.GUONG_CHIEU_HAU_BEN_PHU,
    TOA_DO: [
      {
        x: 685,
        y: 264,
      },
      {
        x: 747,
        y: 274,
      },
      {
        x: 756,
        y: 303,
      },
      {
        x: 694,
        y: 311,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 720,
      y: 285,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  GUONG_CHIEU_HAU_BEN_PHU_NGANG: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_SAU_XE.GUONG_CHIEU_HAU_BEN_PHU,
    TOA_DO: [
      {
        x: 532,
        y: 1210,
      },
      {
        x: 535,
        y: 1326,
      },
      {
        x: 493,
        y: 1344,
      },
      {
        x: 485,
        y: 1239,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 508,
      y: 1275,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* GƯƠNG CHIẾU HẬU BÊN PHỤ */
  GUONG_CHIEU_HAU_BEN_LAI_DOC: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_LAI_DOC',
    ma: MA_HANG_MUC_SAU_XE.GUONG_CHIEU_HAU_BEN_LAI,
    TOA_DO: [
      {
        x: 111,
        y: 263,
      },
      {
        x: 100,
        y: 313,
      },
      {
        x: 38,
        y: 301,
      },
      {
        x: 48,
        y: 274,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 76,
      y: 289,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  GUONG_CHIEU_HAU_BEN_LAI_NGANG: {
    nhomHangMuc: 'GUONG_CHIEU_HAU_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_SAU_XE.GUONG_CHIEU_HAU_BEN_LAI,
    TOA_DO: [
      {
        x: 535,
        y: 195,
      },
      {
        x: 487,
        y: 178,
      },
      {
        x: 497,
        y: 68,
      },
      {
        x: 527,
        y: 79,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 512,
      y: 134,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* NÓC XE */
  NOC_XE_DOC: {
    nhomHangMuc: 'NOC_XE_DOC',
    ma: MA_HANG_MUC_SAU_XE.NOC_XE,
    TOA_DO: [
      {
        x: 207,
        y: 151,
      },
      {
        x: 391,
        y: 138,
      },
      {
        x: 194,
        y: 152,
      },
      {
        x: 586,
        y: 172,
      },
      {
        x: 216,
        y: 173,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 404,
      y: 150,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  NOC_XE_NGANG: {
    nhomHangMuc: 'NOC_XE_NGANG',
    ma: MA_HANG_MUC_SAU_XE.NOC_XE,
    TOA_DO: [
      {
        x: 648,
        y: 365,
      },
      {
        x: 662,
        y: 174,
      },
      {
        x: 645,
        y: 1053,
      },
      {
        x: 530,
        y: 1040,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 650,
      y: 740,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* ĐÈN BÁO PHANH */
  DEN_BAO_PHANH_DOC_A: {
    nhomHangMuc: 'DEN_BAO_PHANH_DOC_A',
    ma: MA_HANG_MUC_SAU_XE.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 121,
        y: 320,
      },
      {
        x: 211,
        y: 319,
      },
      {
        x: 217,
        y: 354,
      },
      {
        x: 116,
        y: 354,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 169,
      y: 334,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  DEN_BAO_PHANH_NGANG_A: {
    nhomHangMuc: 'DEN_BAO_PHANH_NGANG_A',
    ma: MA_HANG_MUC_SAU_XE.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 480,
        y: 212,
      },
      {
        x: 485,
        y: 376,
      },
      {
        x: 446,
        y: 380,
      },
      {
        x: 452,
        y: 210,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 462,
      y: 303,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  DEN_BAO_PHANH_DOC_B: {
    nhomHangMuc: 'DEN_BAO_PHANH_DOC_B',
    ma: MA_HANG_MUC_SAU_XE.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 590,
        y: 315,
      },
      {
        x: 676,
        y: 318,
      },
      {
        x: 686,
        y: 352,
      },
      {
        x: 586,
        y: 349,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 630,
      y: 336,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  DEN_BAO_PHANH_NGANG_B: {
    nhomHangMuc: 'DEN_BAO_PHANH_NGANG_B',
    ma: MA_HANG_MUC_SAU_XE.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 487,
        y: 1046,
      },
      {
        x: 483,
        y: 1207,
      },
      {
        x: 447,
        y: 1219,
      },
      {
        x: 450,
        y: 1038,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 466,
      y: 1115,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CỐP SAU */
  COP_SAU_DOC: {
    nhomHangMuc: 'COP_SAU_DOC',
    ma: MA_HANG_MUC_SAU_XE.COP_SAU,
    TOA_DO: [
      {
        x: 207,
        y: 279,
      },
      {
        x: 592,
        y: 281,
      },
      {
        x: 574,
        y: 416,
      },
      {
        x: 225,
        y: 417,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 402,
      y: 361,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  COP_SAU_NGANG: {
    nhomHangMuc: 'COP_SAU_NGANG',
    ma: MA_HANG_MUC_SAU_XE.COP_SAU,
    TOA_DO: [
      {
        x: 522,
        y: 373,
      },
      {
        x: 522,
        y: 1044,
      },
      {
        x: 387,
        y: 1017,
      },
      {
        x: 391,
        y: 402,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 444,
      y: 691,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CỐP SAU */
  LOP_SAU_DOC_A: {
    nhomHangMuc: 'LOP_SAU_DOC_A',
    ma: MA_HANG_MUC_SAU_XE.LOP_SAU,
    TOA_DO: [
      {
        x: 90,
        y: 501,
      },
      {
        x: 165,
        y: 572,
      },
      {
        x: 166,
        y: 656,
      },
      {
        x: 86,
        y: 656,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 130,
      y: 614,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  LOP_SAU_NGANG_A: {
    nhomHangMuc: 'LOP_SAU_NGANG_A',
    ma: MA_HANG_MUC_SAU_XE.LOP_SAU,
    TOA_DO: [
      {
        x: 305,
        y: 153,
      },
      {
        x: 226,
        y: 293,
      },
      {
        x: 140,
        y: 291,
      },
      {
        x: 143,
        y: 153,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 186,
      y: 215,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  LOP_SAU_DOC_B: {
    nhomHangMuc: 'LOP_SAU_DOC_B',
    ma: MA_HANG_MUC_SAU_XE.LOP_SAU,
    TOA_DO: [
      {
        x: 701,
        y: 503,
      },
      {
        x: 712,
        y: 656,
      },
      {
        x: 632,
        y: 657,
      },
      {
        x: 634,
        y: 573,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 660,
      y: 604,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  LOP_SAU_NGANG_B: {
    nhomHangMuc: 'LOP_SAU_NGANG_B',
    ma: MA_HANG_MUC_SAU_XE.LOP_SAU,
    TOA_DO: [
      {
        x: 304,
        y: 1263,
      },
      {
        x: 143,
        y: 1266,
      },
      {
        x: 141,
        y: 1126,
      },
      {
        x: 226,
        y: 1123,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 194,
      y: 1193,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CẢN SAU BÊN LÁI */
  CAN_SAU_BEN_LAI_DOC: {
    nhomHangMuc: 'CAN_SAU_BEN_LAI_DOC',
    ma: MA_HANG_MUC_SAU_XE.CAN_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 113,
        y: 372,
      },
      {
        x: 219,
        y: 369,
      },
      {
        x: 235,
        y: 478,
      },
      {
        x: 118,
        y: 512,
      },
      {
        x: 100,
        y: 414,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 170,
      y: 437,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_SAU_BEN_LAI_NGANG: {
    nhomHangMuc: 'CAN_SAU_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_SAU_XE.CAN_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 427,
        y: 193,
      },
      {
        x: 432,
        y: 387,
      },
      {
        x: 315,
        y: 415,
      },
      {
        x: 280,
        y: 210,
      },
      {
        x: 380,
        y: 175,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 380,
      y: 311,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CẢN SAU */
  CAN_SAU_DOC: {
    nhomHangMuc: 'CAN_SAU_DOC',
    ma: MA_HANG_MUC_SAU_XE.CAN_SAU,
    TOA_DO: [
      {
        x: 228,
        y: 424,
      },
      {
        x: 572,
        y: 423,
      },
      {
        x: 565,
        y: 485,
      },
      {
        x: 236,
        y: 485,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 390,
      y: 454,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_SAU_NGANG: {
    nhomHangMuc: 'CAN_SAU_NGANG',
    ma: MA_HANG_MUC_SAU_XE.CAN_SAU,
    TOA_DO: [
      {
        x: 375,
        y: 403,
      },
      {
        x: 373,
        y: 1011,
      },
      {
        x: 313,
        y: 999,
      },
      {
        x: 318,
        y: 420,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 339,
      y: 711,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CẢN SAU BÊN PHỤ */
  CAN_SAU_BEN_PHU_DOC: {
    nhomHangMuc: 'CAN_SAU_BEN_PHU_DOC',
    ma: MA_HANG_MUC_SAU_XE.CAN_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 691,
        y: 369,
      },
      {
        x: 703,
        y: 414,
      },
      {
        x: 682,
        y: 519,
      },
      {
        x: 567,
        y: 486,
      },
      {
        x: 582,
        y: 370,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 630,
      y: 422,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CAN_SAU_BEN_PHU_NGANG: {
    nhomHangMuc: 'CAN_SAU_BEN_PHU_NGANG',
    ma: MA_HANG_MUC_SAU_XE.CAN_SAU_BEN_PHU,
    TOA_DO: [
      {
        x: 435,
        y: 1223,
      },
      {
        x: 482,
        y: 1242,
      },
      {
        x: 279,
        y: 1215,
      },
      {
        x: 429,
        y: 1035,
      },
      {
        x: 313,
        y: 1008,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 367,
      y: 1115,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
};
export const TOA_DO_ANH_BEN_LAI_XE = {
  /* KÍNH CỬA SAU BÊN LÁI */
  KINH_CUA_SAU_BEN_LAI_DOC: {
    nhomHangMuc: 'KINH_CUA_SAU_BEN_LAI_DOC',
    ma: MA_HANG_MUC_BEN_LAI.KINH_CUA_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 508,
        y: 291,
      },
      {
        x: 588,
        y: 293,
      },
      {
        x: 642,
        y: 336,
      },
      {
        x: 505,
        y: 341,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 550,
      y: 316,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  KINH_CUA_SAU_BEN_LAI_NGANG: {
    nhomHangMuc: 'KINH_CUA_SAU_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.KINH_CUA_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 503,
        y: 1010,
      },
      {
        x: 504,
        y: 1180,
      },
      {
        x: 458,
        y: 1278,
      },
      {
        x: 460,
        y: 1009,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 480,
      y: 1095,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* KÍNH CỬA TRƯỚC BÊN LÁI  */
  KINH_CUA_TRUOC_BEN_LAI_DOC: {
    nhomHangMuc: 'KINH_CUA_TRUOC_BEN_LAI_DOC',
    ma: MA_HANG_MUC_BEN_LAI.KINH_CUA_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 476,
        y: 288,
      },
      {
        x: 470,
        y: 346,
      },
      {
        x: 319,
        y: 355,
      },
      {
        x: 317,
        y: 330,
      },
      {
        x: 392,
        y: 293,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 400,
      y: 320,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  KINH_CUA_TRUOC_BEN_LAI_NGANG: {
    nhomHangMuc: 'KINH_CUA_TRUOC_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.KINH_CUA_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 516,
        y: 952,
      },
      {
        x: 452,
        y: 937,
      },
      {
        x: 447,
        y: 642,
      },
      {
        x: 473,
        y: 639,
      },
      {
        x: 502,
        y: 777,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 480, // x > x1
      y: 840, // y > y1
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CỬA SAU BÊN LÁI */
  CUA_SAU_BEN_LAI_DOC: {
    nhomHangMuc: 'CUA_SAU_BEN_LAI_DOC',
    ma: MA_HANG_MUC_BEN_LAI.CUA_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 494,
        y: 350,
      },
      {
        x: 648,
        y: 345,
      },
      {
        x: 640,
        y: 381,
      },
      {
        x: 560,
        y: 452,
      },
      {
        x: 478,
        y: 453,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 540,
      y: 400,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CUA_SAU_BEN_LAI_NGANG: {
    nhomHangMuc: 'CUA_SAU_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.CUA_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 449,
        y: 985,
      },
      {
        x: 453,
        y: 1294,
      },
      {
        x: 418,
        y: 1280,
      },
      {
        x: 346,
        y: 1123,
      },
      {
        x: 344,
        y: 954,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 400,
      y: 1063,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* CỬA TRƯỚC BÊN LÁI */
  CUA_TRUOC_BEN_LAI_DOC: {
    nhomHangMuc: 'CUA_TRUOC_BEN_LAI_DOC',
    ma: MA_HANG_MUC_BEN_LAI.CUA_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 270,
        y: 361,
      },
      {
        x: 483,
        y: 351,
      },
      {
        x: 476,
        y: 455,
      },
      {
        x: 262,
        y: 457,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 370,
      y: 407,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  CUA_TRUOC_BEN_LAI_NGANG: {
    nhomHangMuc: 'CUA_TRUOC_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.CUA_TRUOC_BEN_LAI,
    TOA_DO: [
      {
        x: 438,
        y: 537,
      },
      {
        x: 447,
        y: 972,
      },
      {
        x: 345,
        y: 950,
      },
      {
        x: 341,
        y: 528,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 390,
      y: 692,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  // VỚI HÌNH TRÒN THÌ SO SÁNH THEO KIỂU TÂM - CHIỀU DÀI BÁN KÍNH
  /* MÂM BÁNH XE LAI SAU */
  MAM_BANH_XE_BEN_LAI_SAU_DOC: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_LAI_SAU_DOC',
    ma: MA_HANG_MUC_BEN_LAI.MAM_BANH_XE,
    TOA_DO: [
      // TOẠ ĐỘ TÂM
      {
        x: 643,
        y: 470,
      },
      // TOẠ ĐỘ ĐIỂM Ở ĐƯỜNG TRÒN
      {
        x: 687,
        y: 471,
      },
    ],
    type: 'DOC', //hình đang nằm doc
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  MAM_BANH_XE_BEN_LAI_SAU_NGANG: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_LAI_SAU_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.MAM_BANH_XE,
    TOA_DO: [
      {
        x: 332,
        y: 1285,
      },
      {
        x: 378,
        y: 1285,
      },
    ],
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  /* MÂM BÁNH XE LÁI TRƯỚC */
  MAM_BANH_XE_BEN_LAI_TRUOC_DOC: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_LAI_TRUOC_DOC',
    ma: MA_HANG_MUC_BEN_LAI.MAM_BANH_XE,
    TOA_DO: [
      {
        x: 178,
        y: 468,
      },
      {
        x: 220,
        y: 468,
      },
    ],
    type: 'DOC', //hình đang nằm doc
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  MAM_BANH_XE_BEN_LAI_TRUOC_NGANG: {
    nhomHangMuc: 'MAM_BANH_XE_BEN_LAI_TRUOC_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.MAM_BANH_XE,
    TOA_DO: [
      {
        x: 330,
        y: 354,
      },
      {
        x: 379,
        y: 353,
      },
    ],
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'HINH_TRON', // là hình tròn
  },
  /* HÔNG SAU BÊN LÁI */
  HONG_SAU_BEN_LAI_DOC_A: {
    nhomHangMuc: 'HONG_SAU_BEN_LAI_DOC_A',
    ma: MA_HANG_MUC_BEN_LAI.HONG_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 700,
        y: 343,
      },
      {
        x: 655,
        y: 344,
      },
      {
        x: 650,
        y: 377,
      },
      {
        x: 711,
        y: 383,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 675,
      y: 365,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  HONG_SAU_BEN_LAI_NGANG_A: {
    nhomHangMuc: 'HONG_SAU_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.HONG_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 453,
        y: 1396,
      },
      {
        x: 454,
        y: 1307,
      },
      {
        x: 418,
        y: 1294,
      },
      {
        x: 417,
        y: 1418,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 433,
      y: 1358,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  HONG_SAU_BEN_LAI_DOC_B: {
    nhomHangMuc: 'HONG_SAU_BEN_LAI_DOC_B',
    ma: MA_HANG_MUC_BEN_LAI.HONG_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 763,
        y: 382,
      },
      {
        x: 668,
        y: 382,
      },
      {
        x: 723,
        y: 456,
      },
      {
        x: 778,
        y: 442,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 748,
      y: 429,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  HONG_SAU_BEN_LAI_NGANG_B: {
    nhomHangMuc: 'HONG_SAU_BEN_LAI_NGANG_B',
    ma: MA_HANG_MUC_BEN_LAI.HONG_SAU_BEN_LAI,
    TOA_DO: [
      {
        x: 413,
        y: 1523,
      },
      {
        x: 411,
        y: 1374,
      },
      {
        x: 345,
        y: 1453,
      },
      {
        x: 358,
        y: 1551,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 373,
      y: 1476,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* ỐP LƯỜN DÈ BÊN LÁI */
  OP_LUON_DE_BEN_LAI_DOC: {
    nhomHangMuc: 'OP_LUON_DE_BEN_LAI_DOC',
    ma: MA_HANG_MUC_BEN_LAI.OP_LUON_DE_BEN_LAI,
    TOA_DO: [
      {
        x: 264,
        y: 469,
      },
      {
        x: 576,
        y: 465,
      },
      {
        x: 578,
        y: 475,
      },
      {
        x: 264,
        y: 479,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 228,
      y: 470,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  OP_LUON_DE_BEN_LAI_NGANG: {
    nhomHangMuc: 'OP_LUON_DE_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.OP_LUON_DE_BEN_LAI,
    TOA_DO: [
      {
        x: 333,
        y: 527,
      },
      {
        x: 336,
        y: 1154,
      },
      {
        x: 321,
        y: 1155,
      },
      {
        x: 314,
        y: 530,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 326,
      y: 830,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* ĐÈN BÁO PHANH */
  DEN_BAO_PHANH_BEN_LAI_DOC: {
    nhomHangMuc: 'DEN_BAO_PHANH_BEN_LAI_DOC',
    ma: MA_HANG_MUC_BEN_LAI.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 708,
        y: 361,
      },
      {
        x: 761,
        y: 359,
      },
      {
        x: 764,
        y: 376,
      },
      {
        x: 713,
        y: 376,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 732,
      y: 368,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  DEN_BAO_PHANH_BEN_LAI_NGANG: {
    nhomHangMuc: 'DEN_BAO_PHANH_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.DEN_BAO_PHANH,
    TOA_DO: [
      {
        x: 437,
        y: 1420,
      },
      {
        x: 441,
        y: 1520,
      },
      {
        x: 422,
        y: 1524,
      },
      {
        x: 424,
        y: 1430,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 429,
      y: 1470,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* TAI XE BÊN LÁI */
  TAI_XE_BEN_LAI_DOC_A: {
    nhomHangMuc: 'TAI_XE_BEN_LAI_DOC_A',
    ma: MA_HANG_MUC_BEN_LAI.TAI_XE_BEN_LAI,
    TOA_DO: [
      {
        x: 267,
        y: 360,
      },
      {
        x: 260,
        y: 456,
      },
      {
        x: 167,
        y: 373,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 235,
      y: 379,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  TAI_XE_BEN_LAI_NGANG_A: {
    nhomHangMuc: 'TAI_XE_BEN_LAI_NGANG_A',
    ma: MA_HANG_MUC_BEN_LAI.TAI_XE_BEN_LAI,
    TOA_DO: [
      {
        x: 442,
        y: 534,
      },
      {
        x: 350,
        y: 522,
      },
      {
        x: 430,
        y: 329,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 419,
      y: 478,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  TAI_XE_BEN_LAI_DOC_B: {
    nhomHangMuc: 'TAI_XE_BEN_LAI_DOC_B',
    ma: MA_HANG_MUC_BEN_LAI.TAI_XE_BEN_LAI,
    TOA_DO: [
      {
        x: 168,
        y: 376,
      },
      {
        x: 107,
        y: 429,
      },
      {
        x: 94,
        y: 391,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 110,
      y: 400,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  TAI_XE_BEN_LAI_NGANG_B: {
    nhomHangMuc: 'TAI_XE_BEN_LAI_NGANG_B',
    ma: MA_HANG_MUC_BEN_LAI.TAI_XE_BEN_LAI,
    TOA_DO: [
      {
        x: 429,
        y: 382,
      },
      {
        x: 373,
        y: 199,
      },
      {
        x: 413,
        y: 197,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 400,
      y: 241,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  /* BO NÓC XE BÊN PHỤ */
  BO_NOC_XE_BEN_LAI_DOC: {
    nhomHangMuc: 'BO_NOC_XE_BEN_LAI_DOC',
    ma: MA_HANG_MUC_BEN_LAI.BO_NOC_BEN_LAI,
    TOA_DO: [
      {
        x: 595,
        y: 286,
      },
      {
        x: 651,
        y: 294,
      },
      {
        x: 726,
        y: 343,
      },
      {
        x: 655,
        y: 339,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 660,
      y: 320,
    },
    type: 'DOC', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
  BO_NOC_XE_BEN_LAI_NGANG: {
    nhomHangMuc: 'BO_NOC_XE_BEN_LAI_NGANG',
    ma: MA_HANG_MUC_BEN_LAI.BO_NOC_BEN_LAI,
    TOA_DO: [
      {
        x: 510,
        y: 1200,
      },
      {
        x: 509,
        y: 1295,
      },
      {
        x: 457,
        y: 1454,
      },
      {
        x: 460,
        y: 1310,
      },
    ],
    TOA_DO_NAM_TRONG: {
      x: 480,
      y: 1330,
    },
    type: 'NGANG', //hình đang nằm ngang
    hinhDang: 'DA_GIAC', // là hình đa giác
  },
};
