import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, spacing} from '@app/theme';
import {Empty, Icon, ImageComp, SearchBar, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Image, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';

const ChupAnhOfflineOtoStep3Component = ({profileData, rotationImage, removeImage, onPressOpenCamera, onPressXemLaiAnh, imagesData, hangMucAnh, onPressXemChiTietAnhDaUpload, onLoadImageError}) => {
  const [chonAnhTuThuVien, setChonAnhTuThuVien] = useState(false);
  const [dataImages, setDataImages] = useState([]);
  const [searchText, setSearchText] = useState('');
  // useFocusEffect(() => {
  //   if (profileData && profileData.cau_hinh) {
  //     let cauHinhChonAnhTuThuVien = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.CHON_ANH_THU_VIEN, profileData.cau_hinh);
  //     if (cauHinhChonAnhTuThuVien?.gia_tri == DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) setChonAnhTuThuVien(true);
  //   }
  // }, []);
  useEffect(() => {
    if (hangMucAnh === 'ANH_NGHIEM_THU') {
      let data = imagesData.filter((e) => e.ma === 'ANH_NGHIEM_THU');
      setDataImages(data);
    } else if (hangMucAnh === 'ANH_THVT') {
      let data = imagesData.filter((e) => e.ma === 'ANH_THVT');
      setDataImages(data);
    } else if (hangMucAnh === 'XMHT') {
      let data = imagesData.filter((e) => e.ma === 'XMHT');
      setDataImages(data);
    } else if (hangMucAnh === 'XMPHI') {
      let data = imagesData.filter((e) => e.ma === 'XMPHI');
      setDataImages([...data]);
    } else setDataImages(imagesData);
  }, [imagesData]);

  // const checkDaChupAnh = (maHangMuc) => {
  //   let anhHangMuc = anhHoSo.find((item) => item.ma_file === maHangMuc && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
  //   if (anhHangMuc) return true;
  //   return false;
  // };

  //render ra từng ảnh trong menu
  const renderImageItem = (imageData, extraData) => {
    let widthImg = undefined,
      heightImg = undefined;
    widthImg = dimensions.width / 2 - 30;
    heightImg = dimensions.width / 2 - 30;
    // if (
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BIEN_BAN_GIAM_DINH ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.THONG_BAO_TAI_NAN ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM ||
    //   imageData.item.nhom.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM
    // ) {
    //   widthImg = width / 2 - 30;
    //   heightImg = width / 2 - 30;
    // }
    // if (
    //   imageData.index == extraData.images.length - 1 &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BIEN_BAN_GIAM_DINH &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.THONG_BAO_TAI_NAN &&
    //   imageData.item.nhom.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM
    // )
    //   return renderCameraButton(imageData);
    return (
      <ImageComp
        imageData={imageData}
        removeImage={() => removeImage(imageData)}
        rotationImage={rotationImage}
        width={widthImg}
        height={heightImg}
        onPressOpenCamera={onPressOpenCamera}
        onPressXemLaiAnh={onPressXemLaiAnh}
        uploadFromLib={chonAnhTuThuVien}
        useImageComponent={imageData?.item?.keyMapLibrary === imageData?.item?.path ? true : false}
        onLoadImageError={onLoadImageError}
      />
    );
  };

  const renderItemAnhDaUpload = ({item, index}, extraData) => (
    <TouchableOpacity
      onPress={() => {
        NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
          currentImageData: {item, index},
          imagesData: extraData.listAnhHangMuc,
        });
      }}>
      <Image style={styles.imageProcess} source={{uri: `data:image/gif;base64,${item.duong_dan}`}} resizeMode={'contain'} />
    </TouchableOpacity>
  );

  //render ra 1 item của menu
  const renderItemMenuImage = ({item, index}) => {
    let hienThiBoSungThongTin = false;
    if (item.nhom_hang_muc === 'BANG_LAI' || item.nhom_hang_muc === 'DANG_KIEM') hienThiBoSungThongTin = true;
    // if (
    //   data.item.images.length == 1 &&
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BIEN_BAN_GIAM_DINH &&
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.THONG_BAO_TAI_NAN &&
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM &&
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM
    // )
    //   return null;
    let numColumns = 2;
    // if (
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BIEN_BAN_GIAM_DINH &&
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.THONG_BAO_TAI_NAN &&
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM &&
    //   data.item.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM
    // )
    //   numColumns = 3;
    // numColumns = data.item.nhom_hang_muc && typeof data.item.nhom_hang_muc != 'string' ? 2 : 3;
    if (!item.ten) return;
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      if (!item.ten.toLowerCase()?.includes(lowerCaseSearchText) && !item.ma.toLowerCase()?.includes(lowerCaseSearchText)) return null;
    }
    const daCoTrenHeThong = item.anhDaUpload.length > 0;
    return (
      <View style={[styles.itemMenuView, index % 2 == 0 && {backgroundColor: colors.WHITE8}]}>
        <View style={styles.imageCategoryTitleView}>
          <View style={styles.menuImageTitleView}>
            <View>
              <Text style={[styles.imageCategoryTitle, !hienThiBoSungThongTin && {flex: 1}]}>{item.ten}</Text>
              <Text font="regular12" style={[styles.txtTrangThaiAnh, {color: daCoTrenHeThong ? colors.GREEN : colors.PRIMARY}]}>
                {daCoTrenHeThong ? 'Đã có trên hệ thống' : 'Chưa có trên hệ thống'}
              </Text>
            </View>
          </View>
          <View style={styles.menuImageActionView}>
            {/* {hienThiBoSungThongTin && (
              <TouchableOpacity
                style={styles.thongTinChiTietView}
                onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.BO_SUNG_THONG_TIN_GIAY_TO, {thongTinGiayTo: item, profileData, doiTuongDuocChupAnh})}>
                <Text children="Thông tin chi tiết" style={[styles.imageCategoryTitle, {marginRight: spacing.tiny, color: colors.PRIMARY}]} />
                <Icon.FontAwesome name={'angle-down'} size={18} color={colors.PRIMARY} />
              </TouchableOpacity>
            )} */}
            <TouchableOpacity style={styles.thongTinChiTietView} onPress={() => onPressXemChiTietAnhDaUpload(index)}>
              <Text children={`Ảnh đã tải lên (${item.anhDaUpload.length})`} color={colors.PRIMARY} style={{marginRight: spacing.tiny}} />
              <Icon.FontAwesome name={item.isShowAnhDaUpload ? 'angle-down' : 'angle-up'} size={18} color={colors.PRIMARY} />
            </TouchableOpacity>
          </View>
        </View>

        {item.isShowAnhDaUpload && (
          <View style={styles.anhDaUploadView}>
            <Text children="Ảnh đã tải lên hệ thống" style={[styles.imageCategoryTitle, {fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
            {item.anhDaUpload.length > 0 && (
              <FlatList
                keyExtractor={(item, index) => index.toString()}
                scrollEnabled={true}
                data={item.anhDaUpload}
                renderItem={(itemAnhDaUpload) => renderItemAnhDaUpload(itemAnhDaUpload, {listAnhHangMuc: item.anhDaUpload})}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
              />
            )}
            {item.anhDaUpload.length === 0 && (
              <View style={{marginBottom: spacing.default}}>
                <Empty imageStyle={{width: dimensions.width / 5, height: dimensions.width / 5}} />
              </View>
            )}
          </View>
        )}
        {item.isShowAnhDaUpload && (
          <Text children="Ảnh chưa tải lên hệ thống" style={[styles.imageCategoryTitle, {marginLeft: spacing.default, fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
        )}

        <FlatList
          scrollEnabled={true}
          data={item.images}
          renderItem={(itemAnh) => renderImageItem(itemAnh, {images: item.images})}
          numColumns={numColumns} //trường hợp này là khi group SO_KHUNG SO_MAY
          horizontal={false}
        />
      </View>
    );
  };

  return (
    <ScrollView style={{flex: 1, marginBottom: spacing.huge}} showsVerticalScrollIndicator={false}>
      <View style={styles.canhBaoChiTietHoSoView}>
        <Icon.Ionicons name="warning" color={colors.ORANGE} size={20} style={{marginRight: spacing.tiny}} />
        <Text children="Vui lòng không sửa tên, xoá ảnh đã lưu trong thư viện đẻ tính năng hoạt động chính xác" color={colors.ORANGE} />
      </View>
      <SearchBar onTextChange={setSearchText} />
      <FlatList data={dataImages} renderItem={renderItemMenuImage} initialNumToRender={imagesData.length} keyExtractor={(item, index) => index.toString()} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    // width: dimensions.width,
  },
  imageCategoryTitle: {
    // color: colors.BLACK,
    // fontWeight: 'bold',
    // marginLeft: spacing.smaller,
  },
  txtTrangThaiAnh: {
    // marginLeft: 10,
  },
  imageCategoryTitleView: {
    flexDirection: 'row',
    // borderBottomWidth: 1,
    borderColor: colors.GRAY,
    marginHorizontal: spacing.default,
    // paddingBottom: 5,
    marginTop: spacing.smaller,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemMenuView: {
    // marginVertical: 5,
    paddingBottom: spacing.smaller,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY11,
  },
  txtBoSungThongTin: {
    color: '#000',
  },
  menuImageTitleView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: spacing.default,
  },
  thongTinChiTietView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageProcess: {
    marginRight: spacing.default,
    marginVertical: spacing.default,
    borderRadius: 8,
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    resizeMode: 'cover',
    backgroundColor: colors.GRAY2,
    // borderWidth: 1,
  },
  menuImageActionView: {
    alignItems: 'flex-end',
  },
  anhDaUploadView: {
    borderTopWidth: 0.3,
    borderBottomWidth: 0.3,
    marginHorizontal: spacing.default,
    marginTop: spacing.smaller,
    paddingTop: spacing.smaller,
    marginBottom: spacing.default,
    borderColor: colors.GRAY3,
  },
  txtTaiLai: {
    textDecorationLine: 'underline',
  },
  canhBaoChiTietHoSoView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: spacing.default,
    marginBottom: spacing.default,
  },
});

const ChupAnhOfflineOtoStep3Memo = memo(ChupAnhOfflineOtoStep3Component, isEqual);
export const ChupAnhOfflineOtoStep3 = ChupAnhOfflineOtoStep3Memo;
