import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import {APP_NAME, isIOS} from '@constant';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import R from '@R';
import FastImage from 'react-native-fast-image';
import {getImageNameFromUriCamera} from '@app/utils/CameraProvider';
const ANH_CHI_TIET_TITLE = ['Trước', 'Phải - Trước', 'Phải - Sau', 'Sau', 'Trái - Sau', 'Trái - Trước'];
const ANH_CAR_BACKGROUND = {
  KIA: [R.images.img_kia_truoc, R.images.img_kia_phai_truoc, R.images.img_kia_phai_sau, R.images.img_kia_sau, R.images.img_kia_trai_sau, R.images.img_kia_trai_truoc],
};

const ModalCameraAICYCLEComponent = forwardRef(({handleImage}, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: (viTriGocXe) => {
        setViTriGocXeDuocChup(viTriGocXe);
        setIsVisible(true);
      },
      hide: onPressTatCameraModal,
    }),
    [],
  );
  let cameraRef;
  const insect = useSafeAreaInsets();
  const [viTriGocXeDuocChup, setViTriGocXeDuocChup] = useState(null);
  const [flashData, setFlashType] = useState({
    flashIcon: 'flash-off',
    flashMode: RNCamera.Constants.FlashMode.off,
  });
  const [isVisible, setIsVisible] = useState(false);
  const [cameraType, setCameraType] = useState(RNCamera.Constants.Type.back);
  const [countPicture, setCountPicture] = useState(0);
  const [enableCameraBtn, setEnableCameraBtn] = useState(false);

  //xử lý khi icon FLash được click
  const onPressChangeFlash = () => {
    //Nếu đang auto -> flash
    if (flashData.flashIcon === 'flash-auto') {
      setFlashType({
        flashIcon: 'flash',
        flashMode: RNCamera.Constants.FlashMode.on,
      });
    }
    //nếu đang flash -> tắt
    else if (flashData.flashIcon === 'flash') {
      setFlashType({
        flashIcon: 'flash-off',
        flashMode: RNCamera.Constants.FlashMode.off,
      });
    }
    //nếu đang tắt flash -> flash-auto
    else if (flashData.flashIcon === 'flash-off') {
      setFlashType({
        flashIcon: 'flash-auto',
        flashMode: RNCamera.Constants.FlashMode.auto,
      });
    }
  };
  //chuyển camera trước - sau
  const onPressSwapCamera = () => {
    if (cameraType === RNCamera.Constants.Type.back) setCameraType(RNCamera.Constants.Type.front);
    else if (cameraType === RNCamera.Constants.Type.front) setCameraType(RNCamera.Constants.Type.back);
  };
  //ẩn modal camera
  const onPressTatCameraModal = () => {
    setIsVisible(false);
    setFlashType({
      flashIcon: 'flash-off',
      flashMode: RNCamera.Constants.FlashMode.off,
    });
    setCameraType(RNCamera.Constants.Type.back);
    setCountPicture(0);
    setViTriGocXeDuocChup(null);
  };

  const getAnhTitle = () => {
    if (viTriGocXeDuocChup !== null) return ANH_CHI_TIET_TITLE[viTriGocXeDuocChup];
    return 'Ảnh toàn cảnh';
  };

  const onPressChupAnh = async () => {
    if (cameraRef) {
      let cameraOptions = {fixOrientation: true};
      let dataImage = await cameraRef.takePictureAsync(cameraOptions);
      dataImage.name = getImageNameFromUriCamera(dataImage.uri); //lấy ra tên ảnh từ uri
      setCountPicture(countPicture + 1);
      if (viTriGocXeDuocChup === null) handleImage(dataImage, 'ANH_TONG_QUAN');
      else {
        handleImage(dataImage, 'ANH_CHI_TIET', viTriGocXeDuocChup);
        onPressTatCameraModal();
      }
    }
  };

  /* RENDER */
  return (
    <Modal isVisible={isVisible} swipeDirection={'down'} style={styles.modal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          <View style={{flex: 1}}>
            <RNCamera
              ref={(ref) => (cameraRef = ref)}
              //   playSoundOnCapture={appSetting.amThanhKhiChup}
              style={styles.cameraPreview}
              type={cameraType}
              flashMode={flashData.flashMode}
              captureAudio={false}
              zoom={0}
              useNativeZoom={true}
              androidCameraPermissionOptions={{
                title: 'Cho phép ' + APP_NAME + ' truy cập camera của bạn',
                message: APP_NAME + ' muốn truy cập camera của bạn để chụp ảnh',
                buttonPositive: 'Đồng ý',
                buttonNegative: 'Để sau',
              }}>
              <View style={[styles.topViewCamera, {marginTop: isIOS ? insect.top : spacing.small}]}>
                <TouchableOpacity onPress={onPressTatCameraModal} style={styles.btnCloseCamera}>
                  <Icon.AntDesign name="arrowleft" size={40} color={colors.WHITE} />
                </TouchableOpacity>
              </View>
              <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                {viTriGocXeDuocChup != null && <FastImage source={ANH_CAR_BACKGROUND.KIA[viTriGocXeDuocChup]} resizeMode="contain" style={styles.imgCarBackground} />}
              </View>
              <View>
                <View>
                  <View style={styles.anhTitleView}>
                    <Text children={getAnhTitle()} style={styles.txtTitleAnh} />
                  </View>
                  <View style={styles.countPictureView}>
                    <Text children={countPicture} style={styles.txtCountPicture} />
                  </View>
                </View>
                <View style={styles.btnsCameraView}>
                  <TouchableOpacity onPress={onPressChangeFlash} style={styles.btnCameraView}>
                    <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color={colors.WHITE} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.btnCameraView} onPress={onPressChupAnh} disabled={enableCameraBtn}>
                    <Icon.MaterialCommunityIcons name={'circle-slice-8'} size={100} color={colors.WHITE} />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
                    <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
                  </TouchableOpacity>
                </View>
              </View>
            </RNCamera>
          </View>
        </View>
      </View>
    </Modal>
  );
});

export default memo(ModalCameraAICYCLEComponent, isEqual);

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  btnsCameraView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  topViewCamera: {
    // position: 'absolute',
    // left: 0,
    // right: 0,
    // borderWidth: 1,
    // borderColor: '#FFF',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    flexDirection: 'row',
  },
  countPictureView: {
    position: 'absolute',
    right: spacing.small,
    paddingBottom: spacing.small,
  },
  txtCountPicture: {
    fontSize: 40,
    color: '#FFF',
    fontWeight: 'bold',
  },
  anhTitleView: {},
  txtTitleAnh: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
    paddingHorizontal: 30,
    textAlign: 'center',
  },
  imgCarBackground: {
    width: dimensions.width - 20,
    height: dimensions.width - 20,
    transform: [{rotate: '90deg'}],
    // borderWidth: 1,
    // borderColor: '#FFF',
  },
});
