import axiosConfig from '@app/services/axiosConfig';
import {spacing} from '@app/theme';
import {AsyncStorageProvider, logErrorTryCatch} from '@app/utils';
import {ButtonLinear, DropdownPicker, ScreenComponent, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {LIST_LOAI_GCN, LIST_NGHIEP_VU} from './Constant';
import styles from './TaoHoSoGiamDinhOfflineStyles';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import NavigationUtil from '@app/navigation/NavigationUtil';

const TaoHoSoGiamDinhOfflineScreenComponent = ({route}) => {
  console.log('TaoHoSoGiamDinhOfflineScreenComponent');
  const [openOptionNghiepVu, setOpenOptionNghiepVu] = useState(false);
  const [openOptionLoaiGCN, setOpenOptionLoaiGCN] = useState(false);
  const getDefaultFormValue = () => {
    const dataHoSoOffline = route.params?.dataHoSoOffline || null;
    return {doi_tuong: !dataHoSoOffline ? '' : dataHoSoOffline.doi_tuong, nghiep_vu: 'XE', loai_gcn: !dataHoSoOffline ? '' : dataHoSoOffline.nv};
  };

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });
  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onPressTaoHoSo = async (data) => {
    const dataHoSoOffline = route.params?.dataHoSoOffline || null;
    try {
      let paramHoSo = {
        doi_tuong: data.doi_tuong,
        hien_truong: 'D',
        loai: 'DANG_GIAM_DINH',
        ma_doi_tac: axiosConfig.CONFIG_SERVER.MA_DOI_TAC,
        ngay_tb: dataHoSoOffline ? dataHoSoOffline.ngay_tb : moment().format('HH:mm DD/MM/YYYY'),
        nghiep_vu: data.nghiep_vu,
        nv: data.loai_gcn,
        so_hs: ' ',
        so_id: null,
        soIdHoSoLuuTrongMay: dataHoSoOffline ? dataHoSoOffline.soIdHoSoLuuTrongMay : moment().valueOf(),
        trang_thai_ten: 'HỒ SƠ TẠO OFFLINE',
      };

      await AsyncStorageProvider.luuListHoSoDangGiamDinh([paramHoSo]);
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tạo hồ sơ offline thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  /**RENDER  */
  const renderForm = () => {
    return (
      <>
        <Controller
          control={control}
          name="nghiep_vu"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title="Nghiệp vụ"
              placeholder="Chọn nghiệp vụ"
              zIndex={10000}
              isOpen={openOptionNghiepVu}
              setOpen={setOpenOptionNghiepVu}
              items={LIST_NGHIEP_VU}
              isRequired
              searchable={false}
              // onChangeValue={onChangeTinhThanh}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              // containerStyle={{marginBottom: openTinhThanh ? 300 : spacing.smaller, flex: 1, marginTop: 0}}
              // onOpen={() => onOpenDropdown(0)}
              inputErr={errors.nghiep_vu && getErrMessage('nghiep_vu', errors.nghiep_vu.type)}
            />
          )}
        />
        <Controller
          control={control}
          name="loai_gcn"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title="Loại hình"
              placeholder="Chọn loại hình"
              zIndex={9000}
              isOpen={openOptionLoaiGCN}
              setOpen={setOpenOptionLoaiGCN}
              items={LIST_LOAI_GCN}
              isRequired
              searchable={false}
              // onChangeValue={onChangeTinhThanh}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              // containerStyle={{marginBottom: openTinhThanh ? 300 : spacing.smaller, flex: 1, marginTop: 0}}
              // onOpen={() => onOpenDropdown(0)}
              inputErr={errors.loai_gcn && getErrMessage('loai_gcn', errors.loai_gcn.type)}
            />
          )}
        />
        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="doi_tuong"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Đối tượng"
              placeholder="Nhập tên đối tượng"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.doi_tuong && getErrMessage('doi_tuong', errors.doi_tuong.type)}
            />
          )}
        />
      </>
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle={!route.params?.dataHoSoOffline ? 'Tạo hồ sơ giám định OFFLINE' : 'Cập nhật hồ sơ giám định OFFLINE'}
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={{flex: 1, marginHorizontal: spacing.default}}>{renderForm()}</KeyboardAwareScrollView>
        </View>
      }
      footer={<ButtonLinear title={!route.params?.dataHoSoOffline ? 'Tạo hồ sơ' : 'Cập nhật'} onPress={handleSubmit(onPressTaoHoSo)} />}
    />
  );
};

export const TaoHoSoGiamDinhOfflineScreen = memo(TaoHoSoGiamDinhOfflineScreenComponent, isEqual);
