import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {AsyncStorageProvider, logErrorTryCatch} from '@app/utils';
import {getImageNameFromUriCamera, getPhotos, saveToCameraRoll} from '@app/utils/CameraProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {requestCameraPermissions, requestLibraryPermissions} from '@app/utils/PermisstionProvider';
import {Icon, ScreenComponent, SwitchComp, Text} from '@component';
import {APP_NAME, DATA_CONSTANT, isIOS} from '@constant';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, BackHandler, Image, TouchableOpacity, View} from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import ImageResizer from 'react-native-image-resizer';
import {connect} from 'react-redux';
import {ChupAnhHienTruongOtoOfflineStep1, ModalCamera, ModalXemChiTietAnh, ModalXemLaiAnh, ThongKeUpload} from './Components';
import styles from './GiamDinhHienTruongOtoOfflineStyles';

const GiamDinhHienTruongOtoOfflineScreenComponent = ({route, navigation, userInfo, appSettings}) => {
  console.log('GiamDinhHienTruongOtoOfflineScreenComponent');
  const {profileData, doiTuongDuocChupAnh} = route.params;
  const categoryImage = profileData.nhom_hang_muc ? profileData.nhom_hang_muc : [];

  const [dialogLoading, setDialogLoading] = useState(false);
  const [switchOffline, setSwitchOffline] = useState(false);
  const [listAnhLuuTrongMay, setListAnhLuuTrongMay] = useState([]);
  const [currentPosition, setCurrentPosition] = useState({});
  const [toggleLoading, setToggleLoading] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ
  const [imageDataStep1, setImageDataStep1] = useState([]);

  let refModalCamera = useRef(null);
  let refModalXemChiTietAnh = useRef(null);
  let refModalXemLaiAnh = useRef(null);

  const [soLanUploadLai, setSoLanUploadLai] = useState(0); //số lần upload lại

  const [totalImagelUpload, setTotalImageUpload] = useState(0); //số lượng ảnh cần upload
  const [imageUploaded, setImageUploaded] = useState(0); //số lượng ảnh đã upload
  let toggleLoadingRef = useRef(false);

  /* FUNCTION  */
  useEffect(() => {
    initImageDataStep1();
    requestPermistion();
    initData();
  }, []);

  useEffect(() => {
    soLanUploadLai === 1 && onPressNext();
  }, [soLanUploadLai]);
  const initData = async () => {
    let listAnhLuuTrongMay = [];
    if (!isIOS) listAnhLuuTrongMay = await initAnhLuuTrongMay();
    initDuLieuAnhBuoc1(listAnhLuuTrongMay);
  };

  //xử lý nút Back
  useEffect(() => {
    let backHandler;
    navigation.addListener('focus', () => {
      backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
      setSoLanUploadLai(0);
    });
    navigation.addListener('blur', () => {
      backHandler?.remove();
      setTotalImageUpload(0);
      setImageUploaded(0);
    });
  }, []);

  //xử lý nút quay lại ở màn Chụp ảnh
  const backAction = () => {
    toggleLoadingRef.current = false;
    let arrUploadThanhCong = imageDataStep1
      .slice(0, imageDataStep1.length - 1) //trừ đi thằng cuối cùng
      .map((item) => item.uploadThanhCong); //lấy ra thuộc tính uploadThanhCong
    if (arrUploadThanhCong.every((item) => item === true)) NavigationUtil.pop();
    else
      Alert.alert('Thông báo', 'Tồn tại ảnh chưa tải lên hệ thống. Bạn có muốn thoát Giám định hiện trường', [
        {
          text: 'Huỷ',
          style: 'cancel',
        },
        {
          text: 'Đồng ý',
          onPress: () => NavigationUtil.pop(),
        },
      ]);
    return true;
  };

  const requestPermistion = async () => {
    await requestQuyenTruyCapLuuAnh();
    await requestCameraPermissions();
    requestCurrentLocation(
      (position) => setCurrentPosition(position),
      (err) => console.log(err),
    );
  };

  const requestQuyenTruyCapLuuAnh = async () => {
    let response = await requestLibraryPermissions();
    if (!response) {
      Alert.alert('Thông báo', 'Cho phép ' + APP_NAME + ' truy cập quyền sử dụng thư viện để sử dụng tính năng này', [
        {
          text: 'Đồng ý',
          onPress: () => requestQuyenTruyCapLuuAnh(),
        },
      ]);
    }
  };

  const initDuLieuAnhBuoc1 = async (listAnhLuuTrongMay) => {
    try {
      let anhToanCanhDaLuuTrongStorage = await getAnhDaLuuVaoMay();
      if (anhToanCanhDaLuuTrongStorage.length > 0 && anhToanCanhDaLuuTrongStorage[0].keyMapLibrary) {
        let imageDataStep1Tmp = anhToanCanhDaLuuTrongStorage.map((item) => {
          if (item.keyMapLibrary) item = mapThuocTinhAnhLuuTrongMayVsItemAnh(item, item, listAnhLuuTrongMay);
          else item.preView = R.icons.ic_gallery;
          return item;
        });
        setImageDataStep1([...imageDataStep1Tmp]);
      }
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  const initAnhLuuTrongMay = async () => {
    try {
      //lấy tất cả ảnh trong ngày từ thư viện
      setDialogLoading(true);
      let arrAnhTrongNgay = await getPhotos({first: 1000, fromTime: moment().startOf('day').valueOf(), toTime: moment().endOf('day').valueOf()});
      setDialogLoading(false);
      arrAnhTrongNgay = arrAnhTrongNgay.edges;
      if (arrAnhTrongNgay) arrAnhTrongNgay = arrAnhTrongNgay.map((item) => item.node.image);
      setListAnhLuuTrongMay([...arrAnhTrongNgay]);
      return arrAnhTrongNgay;
    } catch (error) {
      Alert.alert('Thông báo', error.code);
      logErrorTryCatch(error);
    }
  };

  //khởi tạo ảnh step 1
  const initImageDataStep1 = () => {
    let imageDataStep1Tmp = [{path: '', preView: R.icons.ic_gallery}];
    setImageDataStep1(imageDataStep1Tmp);
  };

  //xử lý ảnh khi chụp
  //type :  0 - camera ; type : 1 - library
  const handleImage = async (imageData, menuImageDataSelected, indexOpened, type) => {
    const isAnhChonTuThuVien = type === 1;
    try {
      let keyMapLibrary = ''; //khoá để map với ảnh đã lưu trong thư viện, IOS / ANDROID là khác nhau
      let imageName = '';

      let imagesTmp = imageDataStep1;
      let nhom = {};
      categoryImage.map((item) => {
        if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.HIEN_TRUONG) nhom = item;
      });
      //nếu là chọn từ thư viện ảnh -> xử ly chọn nhiều ảnh
      if (isAnhChonTuThuVien) {
        imageData.map((image) => {
          imageName = getImageNameFromUriCamera(image.path); //lấy ra tên ảnh từ uri
          keyMapLibrary = isIOS ? image.sourceURL : image.path;
          imagesTmp.unshift({
            path: image.path,
            nhom: nhom,
            name: imageName,
            keyMapLibrary,
          });
        });
      }
      //nếu là chụp ảnh từ camera-> xử lý 1 ảnh
      else {
        let responseSaveImage = await saveToCameraRoll(imageData.path);
        imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri
        keyMapLibrary = isIOS ? responseSaveImage : imageName;
        imagesTmp.unshift({
          path: imageData.path,
          nhom: nhom,
          name: imageName,
          keyMapLibrary,
        });
      }
      setImageDataStep1([...imagesTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      logErrorTryCatch(error);
    }
  };

  //xoá ảnh
  const removeImage = (imageData) => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Đồng ý',
        onPress: () => {
          let tmp = imageDataStep1;
          tmp.splice(imageData.index, 1);
          setImageDataStep1([...tmp]);
        },
      },
    ]);
  };
  const xuLyAnhUploadThatBai = (anhThatBai) => {
    let imageDataStep1Tmp = imageDataStep1;
    imageDataStep1Tmp.map((itemAnh) => {
      if (itemAnh.path === anhThatBai.path) {
        itemAnh.uploadThatBai = true;
        itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
      }
      return itemAnh;
    });
    setImageDataStep1([...imageDataStep1Tmp]);
  };
  const xuLyAnhUploadThanhCong = (anhThanhCong) => {
    let imageDataStep1Tmp = imageDataStep1;
    imageDataStep1Tmp.map((itemAnh) => {
      if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
      return itemAnh;
    });
    setImageDataStep1([...imageDataStep1Tmp]);
  };

  const rotationImage = (imageData, rotationType) => {
    imageData.item.rootPath = !imageData.item.rootPath ? imageData.item.path : imageData.item.rootPath; //lưu lại ảnh gốc
    /**
     * direction = 1 : ảnh gốc quay 0 độ
     * direction = 2 : ảnh quay phải 90 độ
     * direction = 3 : ảnh quay phải 180 độ
     * direction = 4 : ảnh quay phải 270 độ
     */
    imageData.item.direction = imageData.item.direction ? imageData.item.direction : 1; //
    let newDirection = 0;
    if (rotationType === 0) newDirection = imageData.item.direction === 4 ? 1 : imageData.item.direction + 1;
    else if (rotationType === 1) newDirection = imageData.item.direction === 1 ? 4 : imageData.item.direction - 1;
    let gocQuay = 0;
    if (newDirection === 1) gocQuay = 0;
    else if (newDirection === 2) gocQuay = 90;
    else if (newDirection === 3) gocQuay = 180;
    else if (newDirection === 4) gocQuay = 270;
    Image.getSize(imageData.item.rootPath, async (imageWidth, imageHeight) => {
      try {
        let response = await ImageResizer.createResizedImage(imageData.item.rootPath, imageWidth, imageHeight, 'PNG', 100, gocQuay);
        imageData.item.direction = newDirection;
        let tmp = imageDataStep1;
        tmp[imageData.index].path = response.uri;
        setImageDataStep1([...tmp]);
      } catch (error) {
        Alert.alert('Có lỗi khi xoay ảnh', JSON.stringify(error));
      }
    });
  };

  // ấn nút NEXT
  const onPressNext = async () => {
    if (toggleLoading && switchOffline) {
      Alert.alert('Thông báo', 'Ứng dụng đang tải ảnh lên hệ thống, Bạn có chắc muốn dừng tải ảnh lên', [
        {
          text: 'Dừng tải lên',
          style: 'destructive',
          onPress: () => {
            setToggleLoading(false);
            toggleLoadingRef.current = false;
          },
        },
        {
          text: 'Tiếp tục tải lên',
        },
      ]);
      return;
    }

    //ẢNH HIỆN TRƯỜNG
    let imagesUploadToServer = [];
    imagesUploadToServer = imageDataStep1.filter((imageItem) => imageItem.path !== '' && !imageItem.uploadThanhCong);
    if (imagesUploadToServer.length === 0) {
      await xoaAnhDaLuuVaoMay('ANH_HIEN_TRUONG');
      return NavigationUtil.pop();
    }

    if (soLanUploadLai === 0 && switchOffline) setTotalImageUpload(imagesUploadToServer.length); //số lượng ảnh cần upload
    setToggleLoading(true);
    toggleLoadingRef.current = true;

    // UPLOAD CŨ
    // let response = await Promise.all(
    //   imagesUploadToServer.map(async (item) => {
    //     return uploadImageToServer([item]);
    //   }),
    // );

    // UPLOAD MỚI
    let response = [];
    try {
      if (!switchOffline) {
        await luuAnhGiamDinhVaoStorage();
        FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu ảnh vào bộ nhớ máy thành công', 'success');
        return NavigationUtil.pop();
      }
      if (!profileData.ho_so.so_id) {
        setToggleLoading(false);
        return Alert.alert('Thông báo', 'Hồ sơ TẠO OFFLINE, chưa có dữ liệu từ hệ thống. Vui lòng ĐỒNG BỘ DỮ LIỆU từ hệ thống để tiếp tục', [
          // {
          //   text: 'Để sau',
          // },
          // {text: 'Đồng bộ', onPress: () => onPressTimKiemsHoSo()},
        ]);
      }
      if (!doiTuongDuocChupAnh.so_id_doi_tuong) {
        Alert.alert('Thông báo', 'Vui lòng tải dữ liệu chi tiết hồ sơ từ hệ thống để tiếp tục');
        setToggleLoading(false);
        return;
      }

      for (let i = 0; i < imagesUploadToServer.length; i++) {
        if (!toggleLoadingRef.current) return;
        const item = imagesUploadToServer[i];
        response.push(
          await uploadImageToServer(
            [item],
            (anhThanhCong) => {
              setImageUploaded((prevValue) => prevValue + 1);
              xuLyAnhUploadThanhCong(anhThanhCong);
            },
            (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
            i,
          ),
        );
      }
    } catch (error) {
      console.log('error', error);
    }

    // END CODE MỚI

    // CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR
    if (soLanUploadLai === 0 && response.includes('"Network Error"')) {
      setToggleLoading(false);
      setSoLanUploadLai(1); //nếu thay đổi soLanUpload -> useEffect ở trên sẽ check -> gọi lại hàm onPressNext
      return;
    }
    // END CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR

    let haveErr = '';
    response = response.filter((item) => item !== true);
    //bỏ đi các thông tin trùng
    let uniqueChars = response.filter((element, index) => {
      return response.indexOf(element) === index;
    });
    if (uniqueChars.length > 0) haveErr = uniqueChars.join(', ');
    //thực hiện xong hết thì mới reset lại data
    setToggleLoading(false);
    if (haveErr) return FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi xảy ra khi tải ảnh lên hệ thống \n' + haveErr, 'info');
    await xoaAnhDaLuuVaoMay('ANH_HIEN_TRUONG');
    FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
    NavigationUtil.pop();
  };

  //ấn nút mở CAMERA (step 1 - step 2), mở modal (step 3 - step 4), mở modalCAMERA (giấy tờ bước 4)
  //type : 0 - camera ; type : 1 - lib
  const onPressOpenCamera = (indexOpened, menuImageData, type) => {
    if (type === 0) refModalCamera.current.show();
    else if (type === 1) openCamera(indexOpened, menuImageData, type);
  };

  //mở IMAGE CROP CAMERA
  //type : 0 - camera ; type : 1 - library
  const openCamera = (indexOpened, menuImageData, type) => {
    let imgCropOpts = {
      mediaType: 'photo', //mặc định là chụp ảnh từ camera
      cropping: false,
      enableRotationGesture: true,
      compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
      showCropGuidelines: true, //tắt khung 3x3 đi
      compressImageQuality: 0.5,
      useFrontCamera: false,
    };

    if (type === 1) {
      imgCropOpts.multiple = true;
      imgCropOpts.maxFiles = 10;
      ImageCropPicker.openPicker(imgCropOpts)
        .then(async (data) => {
          // COMMMENT TẠM CHỖ NÀY. NẾU UPLOAD TỪ THƯ VIỆN THÌ KHÔNG CHÈN THÔNG TIN TỪ APP LÊN
          // for (let i = 0; i < data.length; i++) {
          //   let imageAddText = await chenThongTinLenAnh(data[i]);
          //   data[i].path = (!isIOS ? 'file://' : '') + imageAddText;
          // }
          handleImage(data, menuImageData, indexOpened, type);
        })
        .catch((err) => console.log('err', err));
      return;
    }
    //Open Camera
    ImageCropPicker.openCamera(imgCropOpts)
      .then((data) => handleImage(data, menuImageData, indexOpened, type))
      .catch((err) => console.log('err', err));
  };

  //upload ảnh lên server
  const uploadImageToServer = (imagesData, cbSuccess, cbErr, indexImage) => {
    return new Promise(
      async (resolve) => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            nhom: e.nhom.ma,
            x: currentPosition.coords?.latitude,
            y: currentPosition.coords?.longitude,
          };
          files.push(file);
        });
        let params = {
          images: imagesData,
          so_id: profileData.ho_so.so_id,
          pm: 'GD',
          ma_doi_tac: profileData.ho_so.ma_doi_tac,
          files: files,
          ung_dung: 'MOBILE_BT',
          so_id_doi_tuong: doiTuongDuocChupAnh.so_id_doi_tuong,
        };

        // CODE GIÁ LẬP LỖI NETWORK ERROR, UPLOAD > 4 cái ảnh
        // if ((indexImage === 3 || indexImage === 2) && soLanUploadLai === 0) {
        //   imagesData[0].lyDoLoi = JSON.stringify('Network Errorxx');
        //   cbErr(imagesData[0]);
        //   resolve(JSON.stringify('Network Errorxx'));
        //   return;
        // }

        try {
          let response = await ESmartClaimEndpoint.uploadFile(axiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          if (response?.state_info?.status === 'NotOK') {
            imagesData[0].lyDoLoi = response.state_info.message_body;
            cbErr(imagesData[0]);
            resolve(response.state_info.message_body);
          } else if (response?.state_info?.status === 'OK') {
            cbSuccess(imagesData[0]);
            resolve(true);
          } else {
            imagesData[0].lyDoLoi = JSON.stringify(response?.message || response);
            cbErr(imagesData[0]);
            resolve(JSON.stringify(response?.message || response));
          }
        } catch (error) {
          Alert.alert('Thông báo tải ảnh lên hệ thống', JSON.stringify(error?.message || error));
          resolve(JSON.stringify(error?.message || error));
        }
      },
      (reject) => reject(),
    );
  };

  //xem chi tiết ảnh đã chụp
  const onPressXemLaiAnh = (imageData) => {
    refModalXemChiTietAnh.current.show({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
  };

  const getAnhDaLuuVaoMay = async () => {
    try {
      let hoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay);
      if (hoSo && hoSo.dataAnhGiamDinh) {
        // Nếu là điền ảnh đã lưu ở máy vào bước 1
        let anhTheoDoiTuong = hoSo.dataAnhGiamDinh.find(
          (itemDoiTuong) =>
            (itemDoiTuong.soIdDoiTuong && itemDoiTuong.soIdDoiTuong === doiTuongDuocChupAnh.so_id_doi_tuong) ||
            (itemDoiTuong.soIdDoiTuongLuuTrongMay && itemDoiTuong.soIdDoiTuongLuuTrongMay === doiTuongDuocChupAnh.soIdDoiTuongLuuTrongMay),
        );
        if (!anhTheoDoiTuong && hoSo.listDoiTuong) {
          let listDoiTuongGiongDoiTuongDuocChupAnh = hoSo.listDoiTuong.filter((itemDoiTuong) => itemDoiTuong.kieu_dt === doiTuongDuocChupAnh.kieu_dt && itemDoiTuong.nhom === doiTuongDuocChupAnh.nhom);
          //nếu có biến loai -> filter thêm theo biến loai
          if (doiTuongDuocChupAnh.loai) listDoiTuongGiongDoiTuongDuocChupAnh = listDoiTuongGiongDoiTuongDuocChupAnh.filter((itemDoiTuong) => itemDoiTuong.loai === doiTuongDuocChupAnh.loai);
          //nếu có biến hang_muc -> filter thêm theo biến hang_muc
          if (doiTuongDuocChupAnh.hang_muc)
            listDoiTuongGiongDoiTuongDuocChupAnh = listDoiTuongGiongDoiTuongDuocChupAnh.filter((itemDoiTuong) => itemDoiTuong.hang_muc === doiTuongDuocChupAnh.hang_muc);
          //nếu tìm ra 1 đối tượng thôi -> thì lấy ra theo luôn đối tượng đấy
          if (listDoiTuongGiongDoiTuongDuocChupAnh.length === 1) {
            anhTheoDoiTuong = hoSo.dataAnhGiamDinh.find(
              (itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay && itemDoiTuong.soIdDoiTuongLuuTrongMay === listDoiTuongGiongDoiTuongDuocChupAnh[0].soIdDoiTuongLuuTrongMay,
            );
          }
        }
        if (anhTheoDoiTuong && anhTheoDoiTuong.anhHienTruong) return anhTheoDoiTuong?.anhHienTruong?.data || [];
        return [];
      } else {
        console.log('Không tìm thấy dữ liệu lưu trong máy thuộc hồ sơ này');
        return [];
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      logErrorTryCatch(error);
    }
  };

  const xoaAnhDaLuuVaoMay = async (type) => {
    try {
      let hoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay);
      if (hoSo && hoSo.dataAnhGiamDinh)
        await AsyncStorageProvider.xoaDataGiamDinh(
          profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay,
          doiTuongDuocChupAnh.so_id_doi_tuong || doiTuongDuocChupAnh.soIdDoiTuongLuuTrongMay,
          type,
        );
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      logErrorTryCatch(error);
    }
  };

  const luuAnhGiamDinhVaoStorage = async () => {
    try {
      let data = {};
      let imageData = [];

      imageData = imageDataStep1.map((itemAnhChup) => {
        itemAnhChup.preView = null;
        itemAnhChup.path = '';
        return itemAnhChup;
      });
      data = {
        ngay: moment().toDate(),
        soId: profileData.ho_so.so_id,
        soIdHoSoLuuTrongMay: profileData.ho_so.soIdHoSoLuuTrongMay,
        loai: 'ANH_HIEN_TRUONG',
        data: imageData,
        soIdDoiTuong: doiTuongDuocChupAnh.so_id_doi_tuong,
        soIdDoiTuongLuuTrongMay: doiTuongDuocChupAnh.soIdDoiTuongLuuTrongMay || null,
      };
      await AsyncStorageProvider.luuDataGiamDinh(data);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  const mapThuocTinhAnhLuuTrongMayVsItemAnh = (itemAnh, itemLuuTrongMay, listAnhLuuTrongMay) => {
    if (isIOS) itemAnh.path = itemLuuTrongMay.keyMapLibrary;
    else {
      //nếu là ảnh được chọn từ thư viện -> thì lấy luôn keyMapLibrary vì nó là path trỏ đến file ảnh rồi
      if (itemLuuTrongMay.keyMapLibrary.includes('file')) itemAnh.path = itemLuuTrongMay.keyMapLibrary;
      //nếu không thì tìm theo name
      else itemAnh.path = listAnhLuuTrongMay.find((itemAnhLuuTrongMay) => itemAnhLuuTrongMay.uri.includes(itemLuuTrongMay.name))?.uri || '';
    }
    itemAnh.name = itemLuuTrongMay.name;
    itemAnh.keyMapLibrary = itemLuuTrongMay.keyMapLibrary;
    return itemAnh;
  };

  /* RENDER  */
  // các nút ở footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <View style={styles.btnBack} />
        {/* NÚT TIẾP TỤC - HOÀN THÀNH   */}
        <TouchableOpacity activeOpacity={0.5} onPress={onPressNext} style={styles.btnNext}>
          {!toggleLoading ? <Text style={styles.txtBtnBottom}>Hoàn thành</Text> : <ActivityIndicator size="large" color={colors.GRAY} style={{flex: 1}} />}
          <View style={styles.iconRightBtnView}>
            <Icon.Ionicons name={'checkmark-sharp'} size={25} style={styles.iconRightBtn} color={colors.WHITE} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderOfflineSwitchView = () => (
    <View style={styles.offlineSwitchView}>
      <SwitchComp
        value={switchOffline}
        onValueChange={setSwitchOffline}
        title="Hệ thống"
        activeText="ONL"
        inActiveText="OFF"
        backgroundActive={colors.GREEN}
        backgroundInactive="gray"
        circleActiveColor="#FFF"
        containerStyle={{flex: 1, borderBottomWidth: 0, borderRightWidth: 0.5}}
      />
    </View>
  );

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Giám định hiện trường OFFLINE"
      headerSubTitle={doiTuongDuocChupAnh.ten_doi_tuong}
      onPressBack={backAction}
      renderView={
        <>
          {renderOfflineSwitchView()}
          <ThongKeUpload totalImagelUpload={totalImagelUpload} imageUploaded={imageUploaded} toggleLoading={toggleLoading} />
          <ChupAnhHienTruongOtoOfflineStep1
            key={0}
            rotationImage={rotationImage}
            imagesData={imageDataStep1}
            removeImage={removeImage}
            onPressOpenCamera={onPressOpenCamera}
            onPressXemLaiAnh={onPressXemLaiAnh}
            profileData={profileData}
          />

          {/* render phần nút dưới cùng */}
          {renderFooter()}

          {/* modal camera  */}
          <ModalCamera key="ModalCamera" ref={refModalCamera} handleImage={handleImage} currentPosition={currentPosition} />
          {/* modal hiển thị ảnh trong phần hướng dẫn */}
          <ModalXemChiTietAnh key="ModalXemChiTietAnh" ref={refModalXemChiTietAnh} />
          {/* modal hiển thị lại ảnh đã chụp */}
          <ModalXemLaiAnh
            key="ModalXemLaiAnh"
            ref={refModalXemLaiAnh}
            showModalXemChiTietAnh={(imageData) => {
              isIOS && refModalXemLaiAnh.current.hide();
              setTimeout(() => refModalXemChiTietAnh.current.show(imageData), isIOS ? 400 : 0);
            }}
          />
        </>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  categoryImage: state.categoryImage.data,
  categoryCommon: state.categoryCommon.data,
  userInfo: state.user.data,
  appSettings: state.appSetting,
});
const mapDispatchToProps = {};
const GiamDinhHienTruongOtoOfflineScreenConnect = connect(mapStateToProps, mapDispatchToProps)(GiamDinhHienTruongOtoOfflineScreenComponent);
export const GiamDinhHienTruongOtoOfflineScreen = memo(GiamDinhHienTruongOtoOfflineScreenConnect, isEqual);
