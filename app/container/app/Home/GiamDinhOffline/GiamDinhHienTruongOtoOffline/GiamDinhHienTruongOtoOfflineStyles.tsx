import {spacing} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';
import {colors} from '../../../../../commons/Theme';
const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    width: width,
    flex: 1,
  },

  stepIndicator: {
    marginVertical: 20,
  },
  page: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GREEN,
  },
  footerView: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: 20,
    marginBottom: 10,
  },
  btnCamera: {
    backgroundColor: colors.BLUE6,
    borderRadius: 40,
    paddingVertical: 15,
    marginBottom: 5,
    flex: 0,
  },
  btnHint: {
    borderRadius: 40,
    flex: 0,
  },
  btnBack: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: colors.PRIMARY_08,
    flexDirection: 'row',
    borderRadius: 30,
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconRightBtnView: {
    backgroundColor: colors.BUTTON.LIGHT.SECONDARY,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconRightBtn: {
    flex: 0,
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconLeftBtnView: {
    backgroundColor: colors.PRIMARY_DARK_08,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  iconLeftBtn: {
    flex: 0,
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  btnNext: {
    alignItems: 'center',
    flex: 1,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
    flexDirection: 'row',
    borderRadius: 30,
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  txtBtnBottom: {
    flex: 1,
    textAlign: 'center',
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  txtIllustration: {
    color: colors.PRIMARY_DARK_08,
    textDecorationLine: 'underline',
  },
  btnClose: {
    position: 'absolute',
    right: 10,
    top: 0,
  },
  btnHintView: {
    position: 'absolute',
    bottom: 70,
    right: 10,
  },
  btnImageView: {
    position: 'absolute',
    bottom: 70,
    left: 10,
  },
  successTopView: {
    width: width - 30,
    height: height / 5,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  successCenterView: {
    width: width - 30,
    height: height / 4,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
  },
  btnSuccessView: {
    height: height / 20,
    width: width - 50,
    // borderWidth: 1,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
  },
  btnFormKetThucGiamDinh: {
    height: height / 20,
    width: (width - 100) / 2,
    // borderWidth: 1,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 5,
  },
  txtSuccess: {
    textAlign: 'center',
    paddingHorizontal: 10,
    color: colors.WHITE,
    fontSize: 16,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnsCameraView: {
    flexDirection: 'row',
    backgroundColor: colors.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnCloseCamera: {
    position: 'absolute',
    left: 10,
    top: 10,
  },
  khungCatAnhCamera: {
    borderWidth: 3,
    borderStyle: 'dashed',
    borderRadius: 20,
  },
  imageDocument: {
    width: width / 4,
    height: width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tieuDeDanhMucAnhXemLai: {
    marginLeft: 10,
    fontWeight: 'bold',
  },
  btnRowContainer: {
    marginBottom: spacing.small,
    alignItems: 'center',
    paddingHorizontal: spacing.smaller,
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
    flexDirection: 'row',
    marginHorizontal: spacing.small,
    borderRadius: 10,
    marginTop: spacing.small,
  },
  btnEdit: {
    // borderBottomWidth: 2,
    borderRadius: 8,
    marginVertical: spacing.smaller,
    paddingVertical: spacing.smaller,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.smaller,
    justifyContent: 'center',
    flex: 1,
  },
  txtTab: {
    marginLeft: spacing.tiny,
    color: colors.BLACK_03,
  },
  videoView: {
    flex: 1,
  },
  btnLuuView: {
    paddingHorizontal: spacing.small,
    justifyContent: 'center',
    flex: 1,
  },
  reloadView: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.default,
    borderRadius: 8,
    marginLeft: spacing.tiny,
    backgroundColor: colors.PRIMARY,
  },
  offlineSwitchView: {
    flexDirection: 'row',
    marginVertical: spacing.tiny,
    paddingRight: spacing.tiny,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    paddingBottom: spacing.tiny,
  },
});
