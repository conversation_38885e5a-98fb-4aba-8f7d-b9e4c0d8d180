import {Text} from '@component';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, View} from 'react-native';

const ThongKeUploadComponent = ({imageUploaded, totalImagelUpload}) => {
  const renderThongKeChiTiet = (dangUpload, tongSo) => {
    return <View style={styles.thongKeChiTietView}>{dangUpload !== undefined && tongSo !== 0 && <Text children={dangUpload + '/' + tongSo} style={[styles.txtThongKe]} />}</View>;
  };
  return <View style={styles.container}>{renderThongKeChiTiet(imageUploaded, totalImagelUpload)}</View>;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  thongKeChiTietView: {
    flex: 1,
  },
  txtThongKe: {
    textAlign: 'center',
    fontWeight: 'bold',
  },
});

const ThongKeUploadMemo = memo(ThongKeUploadComponent, isEqual);
export const ThongKeUpload = ThongKeUploadMemo;
