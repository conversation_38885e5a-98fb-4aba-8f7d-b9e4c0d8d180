import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {Icon, ImageComp, Text} from '@component';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, View} from 'react-native';

const ChupAnhHienTruongOtoOfflineStep1Component = ({rotationImage, imagesData, removeImage, onPressOpenCamera, onPressXemLaiAnh, profileData}) => {
  const [chonAnhTuThuVien, setChonAnhTuThuVien] = useState(false);

  // useEffect(() => {
  //   if (profileData && profileData.cau_hinh) {
  //     let cauHinhChonAnhTuThuVien = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.CHON_ANH_THU_VIEN, profileData.cau_hinh);
  //     if (cauHinhChonAnhTuThuVien?.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) setChonAnhTuThuVien(true);
  //   }
  // }, []);

  const renderImageItem = (imageData) => {
    return (
      <ImageComp
        imageData={imageData}
        removeImage={removeImage}
        onPressOpenCamera={onPressOpenCamera}
        onPressXemLaiAnh={onPressXemLaiAnh}
        uploadFromLib={chonAnhTuThuVien}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        rotationImage={rotationImage}
        useImageComponent={imageData?.item?.keyMapLibrary === imageData?.item?.path ? true : false}
      />
    );
  };
  return (
    <View style={{flex: 1}}>
      <View style={styles.canhBaoChiTietHoSoView}>
        <Icon.Ionicons name="warning" color={colors.ORANGE} size={20} style={{marginRight: spacing.tiny}} />
        <Text children="Vui lòng không sửa tên, xoá ảnh đã lưu trong thư viện đẻ tính năng hoạt động chính xác" color={colors.ORANGE} />
      </View>
      <FlatList scrollEnabled={true} data={imagesData} renderItem={renderImageItem} numColumns={2} horizontal={false} keyExtractor={(item, index) => index + ''} />
    </View>
  );
};

const styles = StyleSheet.create({
  txtTaiLai: {
    textDecorationLine: 'underline',
  },
  canhBaoChiTietHoSoView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: spacing.default,
    marginBottom: spacing.default,
  },
});

const ChupAnhHienTruongOtoOfflineStep1Memo = memo(ChupAnhHienTruongOtoOfflineStep1Component, isEqual);
export const ChupAnhHienTruongOtoOfflineStep1 = ChupAnhHienTruongOtoOfflineStep1Memo;
