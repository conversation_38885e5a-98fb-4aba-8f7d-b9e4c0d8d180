import {colors} from '@app/commons/Theme';
import {getAppSetting} from '@app/redux/slices/AppSettingSlice';
import {selectUser} from '@app/redux/slices/UserSlice';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import {APP_NAME, isIOS} from '@constant';
import moment from 'moment';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Image, StyleSheet, TouchableOpacity, Vibration, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import DeviceInfo from 'react-native-device-info';
import ImageMarker, {Position} from 'react-native-image-marker';
import ImageResizer from 'react-native-image-resizer';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';

const ModalCameraComponent = forwardRef(({handleImage, currentPosition}, ref) => {
  // console.log('ModalCamera', props);
  const userInfo = useSelector(selectUser);
  let cameraRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const inset = useSafeAreaInsets();
  useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
    },
    hide: () => setVisible(false),
    getVisible: () => {
      return visible;
    },
  }));

  const [flashData, setFlashType] = useState({
    flashIcon: 'flash-off',
    flashMode: RNCamera.Constants.FlashMode.off,
  });
  const [cameraType, setCameraType] = useState(RNCamera.Constants.Type.back);
  const appSetting = useSelector(getAppSetting);
  const [countPicture, setCountPicture] = useState(0);

  // const prepareRatio = async () => {
  //   if (Platform.OS === 'android' && cameraRef) {
  //     const ratios = await cameraRef.getSupportedRatiosAsync();
  //     // See if the current device has your desired ratio, otherwise get the maximum supported one
  //     // Usually the last element of "ratios" is the maximum supported ratio
  //     // const ratio = ratios.find((ratio) => ratio === DESIRED_RATIO) || ratios[ratios.length - 1];
  //     // console.log(ratios);
  //     // setRatio(ratio);
  //   }
  // };

  //xử lý khi icon FLash được click
  const onPressChangeFlash = () => {
    //Nếu đang auto -> flash
    if (flashData.flashIcon == 'flash-auto') {
      setFlashType({
        flashIcon: 'flash',
        flashMode: RNCamera.Constants.FlashMode.on,
      });
    }
    //nếu đang flash -> tắt
    else if (flashData.flashIcon == 'flash') {
      setFlashType({
        flashIcon: 'flash-off',
        flashMode: RNCamera.Constants.FlashMode.off,
      });
    }
    //nếu đang tắt flash -> flash-auto
    else if (flashData.flashIcon == 'flash-off') {
      setFlashType({
        flashIcon: 'flash-auto',
        flashMode: RNCamera.Constants.FlashMode.auto,
      });
    }
  };
  //chuyển camera trước - sau
  const onPressSwapCamera = () => {
    if (cameraType == RNCamera.Constants.Type.back) {
      setCameraType(RNCamera.Constants.Type.front);
    } else if (cameraType == RNCamera.Constants.Type.front) {
      setCameraType(RNCamera.Constants.Type.back);
    }
  };
  //ẩn modal camera
  const onPressTatCameraModal = () => {
    setVisible(false);
    setFlashType({
      flashIcon: 'flash-off',
      flashMode: RNCamera.Constants.FlashMode.off,
    });
    setCameraType(RNCamera.Constants.Type.back);
    setCountPicture(0);
  };

  const onPressChupAnh = async () => {
    if (cameraRef) {
      const cameraOptions = {quality: isIOS ? 0.3 : 0.5, width: 1800, fixOrientation: true};
      let dataImageCameraResponse = await cameraRef.takePictureAsync(cameraOptions);
      if (dataImageCameraResponse?.pictureOrientation !== 3 && dataImageCameraResponse?.pictureOrientation !== 4 && !__DEV__) {
        Alert.alert('Thông báo', 'Vui lòng chụp ảnh toàn cảnh theo chiều ngang');
        return;
      }
      let dataImage = dataImageCameraResponse;
      if (!dataImageCameraResponse) return;
      if (appSetting.rung) Vibration.vibrate(200);
      setCountPicture(countPicture + 1);

      try {
        if (dataImage.pictureOrientation !== 1 && dataImage.deviceOrientation !== 1) dataImage = await xoayDocAnh(dataImage);
      } catch (error) {
        dataImage = dataImageCameraResponse;
      }
      dataImage.path = dataImage.uri;

      let imageAddText = await chenThongTinLenAnh(dataImage);
      dataImage.path = (!isIOS ? 'file://' : '') + imageAddText;
      handleImage(dataImage, null, null, 0);
    }
  };

  const xoayDocAnh = async dataImage => {
    try {
      let gocQuay = 0;
      if (dataImage.pictureOrientation === 3 && dataImage.deviceOrientation === 3) gocQuay = 90;
      else if (dataImage.pictureOrientation === 2 && dataImage.deviceOrientation === 2) gocQuay = 180;
      else if (dataImage.pictureOrientation === 4 && dataImage.deviceOrientation === 4) gocQuay = 270;
      if (gocQuay > 0)
        Image.getSize(dataImage.uri, async (imageWidth, imageHeight) => {
          let response = await ImageResizer.createResizedImage(dataImage.uri, imageWidth, imageHeight, 'PNG', 100, gocQuay);
          dataImage.uri = response.uri;
          return dataImage;
        });

      return dataImage;
    } catch (error) {
      console.log('error', error);
      return dataImage;
    }
  };

  const chenThongTinLenAnh = async dataImage => {
    //ngày giờ / toạ độ / người / thông tin máy
    let txtChen = '';
    txtChen += userInfo.nguoi_dung.nsd;
    let tenMay = await DeviceInfo.getBrand();
    txtChen += '\n' + moment().format('HH:mm DD/MM/YYYY') + ' ';
    txtChen += tenMay;
    if (currentPosition && currentPosition.coords) txtChen += '\n' + currentPosition.coords.longitude.toFixed(3) + ' ; ' + currentPosition.coords.latitude.toFixed(3) + ' ';

    let imageAddText = await ImageMarker.markText({
      backgroundImage: {
        src: dataImage.path,
        scale: 1,
      },
      watermarkTexts: [
        {
          text: txtChen,
          positionOptions: {
            position: Position.bottomRight,
          },
          style: {
            color: '#FFF',
            fontSize: isIOS ? 30 : 30,
          },
        },
      ],
      quality: 100,
    }).catch(err => {
      Alert.alert('Chèn toạ độ không thành công', err.message);
      return '';
    });
    return imageAddText;
  };

  /* RENDER */
  return (
    <Modal isVisible={visible} swipeDirection={'down'} style={styles.modal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          <View style={{flex: 1}}>
            <RNCamera
              ref={ref => (cameraRef = ref)}
              playSoundOnCapture={appSetting.amThanhKhiChup}
              // onCameraReady={prepareRatio}
              // ratio={'4:3'} //nếu đang chụp ảnh HỒ SƠ, GIẤY TỜ
              style={styles.cameraPreview}
              type={cameraType}
              flashMode={flashData.flashMode}
              captureAudio={false}
              zoom={0}
              useNativeZoom={true}
              androidCameraPermissionOptions={{
                title: 'Cho phép ' + APP_NAME + ' truy cập camera của bạn',
                message: APP_NAME + ' muốn truy cập camera của bạn để chụp ảnh',
                buttonPositive: 'Đồng ý',
                buttonNegative: 'Để sau',
              }}>
              <View style={[styles.btnCloseCamera, {top: inset.top}]}>
                <TouchableOpacity onPress={onPressTatCameraModal}>
                  <Icon.AntDesign name="arrowleft" size={40} color={colors.WHITE} />
                </TouchableOpacity>
              </View>
              <View style={styles.anhTitleView}>
                <Text children="Chụp ảnh ngang" style={styles.txtTitleAnh} />
              </View>
              <View style={styles.countPicture}>
                <Text children={countPicture} style={styles.txtCountPicture} />
              </View>
            </RNCamera>
          </View>
          <View style={styles.btnsCameraView}>
            <TouchableOpacity onPress={onPressChangeFlash} style={styles.btnCameraView}>
              <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressChupAnh}>
              <Icon.MaterialCommunityIcons name={'circle-slice-8'} size={100} color={colors.WHITE} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
              <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnsCameraView: {
    flexDirection: 'row',
    backgroundColor: colors.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    height: 120,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnCloseCamera: {
    position: 'absolute',
    left: 10,
    // top: !isIOS ? 10 : 30,
  },
  countPicture: {
    position: 'absolute',
    right: 15,
    bottom: 5,
  },
  txtCountPicture: {
    fontSize: 40,
    color: '#FFF',
    fontWeight: 'bold',
  },
  khungCatAnhCamera: {
    borderWidth: 3,
    borderStyle: 'dashed',
    borderRadius: 20,
  },
  //kích thước BANG_LAI_XE
  sizeBLX: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.63,
  },
  //kích thước DANG_KIEM
  sizeDK: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.67,
  },
  anhTitleView: {
    position: 'absolute',
    bottom: 10,
  },
  txtTitleAnh: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
    paddingHorizontal: 30,
    textAlign: 'center',
  },
});
const ModalCameraMemo = memo(ModalCameraComponent, isEqual);
export const ModalCamera = ModalCameraMemo;
