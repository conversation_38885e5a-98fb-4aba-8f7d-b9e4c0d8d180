import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalXemChiTietAnhComponent = forwardRef(({}, ref) => {
  const [visible, setVisible] = useState(false);
  const [imageData, setImageData] = useState(null);
  useImperativeHandle(ref, () => ({
    show: (imageData) => {
      setVisible(true);
      setImageData(imageData);
    },
    hide: onPressHideModal,
    getVisible: () => {
      return visible;
    },
  }));

  const onPressHideModal = () => {
    setVisible(false);
    setImageData(null);
  };

  return (
    <Modal isVisible={visible} onSwipeComplete={onPressHideModal} onBackdropPress={onPressHideModal} swipeDirection={['down']} propagateSwipe={true} style={styles.modal}>
      <View style={styles.modalImageView}>
        <View style={styles.modalImageContentView}>
          <View style={styles.modalTitleView}>
            <Text style={styles.modalTitle}>{imageData?.title}</Text>
            <TouchableOpacity style={styles.closeView} onPress={onPressHideModal}>
              <Icon.AntDesign
                name="closecircle"
                color="red"
                size={25}
                style={{
                  backgroundColor: colors.WHITE,
                  borderRadius: 20,
                }}
              />
            </TouchableOpacity>
          </View>
          {imageData && (
            <Image
              source={
                imageData.base64
                  ? {
                      uri: `data:image/gif;base64,${imageData.imageData}`,
                    }
                  : imageData.imageData
              }
              style={styles.imageModal}
              resizeMode={'contain'}
            />
          )}
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalImageView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: dimensions.height,
    width: dimensions.width,
  },
  modalImageContentView: {
    backgroundColor: colors.WHITE,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  closeView: {
    marginRight: 15,
  },
  imageModal: {
    width: dimensions.width - 30,
    height: dimensions.width - 30,
    borderRadius: 20,
    margin: 10,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 10,
    flex: 1,
    marginLeft: 30,
    // borderWidth: 1,
  },
});

const ModalXemChiTietAnhMemo = memo(ModalXemChiTietAnhComponent, isEqual);
export const ModalXemChiTietAnh = ModalXemChiTietAnhMemo;
