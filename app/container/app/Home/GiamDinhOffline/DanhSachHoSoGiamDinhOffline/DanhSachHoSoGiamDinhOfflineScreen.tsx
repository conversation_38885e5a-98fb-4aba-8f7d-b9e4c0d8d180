import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import {AsyncStorageProvider} from '@app/utils';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Empty, Icon, ScreenComponent, SearchBar, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Keyboard, View} from 'react-native';
import {useSelector} from 'react-redux';
import {ProfileOfflineItem} from './Components';
import styles from './DanhSachHoSoGiamDinhOfflineStyle';
import moment from 'moment';
import {trangThaiHoSoOfflineDropdown} from './Constant';
import {spacing} from '@app/theme';

const DanhSachHoSoGiamDinhOfflineComponent = ({navigation}) => {
  console.log('DanhSachHoSoGiamDinhOfflineComponent');
  const userInfo = useSelector(selectUser);

  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState([]);
  const [searchInput, setSearchInput] = useState('');

  const [openDropdownTrangThaiHoSo, setOpenDropdownTrangThaiHoSo] = useState(false);
  const [trangThaiHoSoSelected, setTrangThaiHoSoSelected] = useState('0');

  useEffect(() => {
    navigation.addListener('focus', async () => {
      let listHoSo = await getDanhSachHoSo();
      if (listHoSo.find((item) => item.ngayConLaiTruocKhiBiXoa <= 3 && item.tonTaiAnhChuaUploadLenHeThong)) {
        Alert.alert('Thông báo', 'Có hồ sơ CHƯA UPLOAD ảnh giám định, SẮP BỊ XOÁ khỏi thiết bị', [
          {
            text: 'Để sau',
          },
          {text: 'Xem', onPress: () => setTrangThaiHoSoSelected(trangThaiHoSoOfflineDropdown[1].value)},
        ]);
      }
    });
  }, []);

  useEffect(() => {
    getDanhSachHoSo();
  }, [trangThaiHoSoSelected]);

  //lấy danh sách hồ sơ giám định
  const getDanhSachHoSo = async (textSearch = '') => {
    try {
      setDialogLoading(true);
      let listHoSoTmp = await AsyncStorageProvider.getListHoSoDangGiamDinh();
      listHoSoTmp = listHoSoTmp.filter((item) => item.taiKhoan === userInfo.nguoi_dung.nsd).filter((item) => item.nghiep_vu !== 'XE_MAY');
      if (textSearch) listHoSoTmp = listHoSoTmp.filter((item) => item.so_hs?.includes(textSearch) || item.doi_tuong?.includes(textSearch));
      listHoSoTmp.map((itemHoSo) => {
        let tonTaiAnhChuaUploadLenHeThong = false; //check xem nếu có ảnh giám định chưa tải lên - sẽ cảnh báo là có ảnh chưa tải lên
        //nếu có data ảnh giám định
        if (itemHoSo.dataAnhGiamDinh) {
          itemHoSo.dataAnhGiamDinh.forEach((itemAnhGiamDinh) => {
            if (itemAnhGiamDinh.anhHienTruong || itemAnhGiamDinh.anhTaiLieu || itemAnhGiamDinh.anhToanCanh || itemAnhGiamDinh.anhTonThat) tonTaiAnhChuaUploadLenHeThong = true;
          });
        }
        if (tonTaiAnhChuaUploadLenHeThong) itemHoSo.tonTaiAnhChuaUploadLenHeThong = true;
        let ngayBiXoa = moment(itemHoSo.capNhanLanCuoi, 'HH:mm DD/MM/YYYY').add(8, 'days');
        itemHoSo.ngayConLaiTruocKhiBiXoa = ngayBiXoa.diff(moment(), 'day');
        return itemHoSo;
      });
      //sort hồ sơ có ảnh chưa upload và sắp bị xoá
      listHoSoTmp = listHoSoTmp.sort((a, b) => {
        if (b.tonTaiAnhChuaUploadLenHeThong && b.ngayConLaiTruocKhiBiXoa <= 3) return 1;
        return -1;
      });
      //sort hồ sơ có ảnh chưa upload
      listHoSoTmp = listHoSoTmp.sort((a, b) => {
        if (b.tonTaiAnhChuaUploadLenHeThong) return 1;
        return -1;
      });
      if (trangThaiHoSoSelected === trangThaiHoSoOfflineDropdown[1].value) listHoSoTmp = listHoSoTmp.filter((item) => item.ngayConLaiTruocKhiBiXoa <= 3 && item.tonTaiAnhChuaUploadLenHeThong);
      else if (trangThaiHoSoSelected === trangThaiHoSoOfflineDropdown[2].value) listHoSoTmp = listHoSoTmp.filter((item) => item.tonTaiAnhChuaUploadLenHeThong);
      else if (trangThaiHoSoSelected === trangThaiHoSoOfflineDropdown[3].value) listHoSoTmp = listHoSoTmp.filter((item) => !item.so_id);
      setProfileData([...listHoSoTmp]);
      setDialogLoading(false);
      return listHoSoTmp;
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeTextSearch = (value) => {
    setSearchInput(value);
    if (!value.trim()) getDanhSachHoSo('');
  };
  //search hồ sơ giám định
  const onPressSearch = () => {
    Keyboard.dismiss();
    if (!searchInput.trim()) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập từ khoá', 'info');
    getDanhSachHoSo(searchInput);
  };

  const onPressItem = (item) => {
    if (item.nghiep_vu === 'XE_MAY') {
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY, {
        profileDetail: item,
      });
    } else {
      NavigationUtil.push(SCREEN_ROUTER_APP.CHI_TIET_HO_SO_O_TO_OFFLINE, {
        profileDetail: item,
      });
    }
  };

  const onPressXoaItemHoSo = async (indexXoa) => {
    let tonTaiAnhChuaUploadLenHeThong = false; //check xem nếu có ảnh giám định chưa tải lên - sẽ cảnh báo là có ảnh chưa tải lên
    let dataHoSo = profileData[indexXoa];
    //nếu có data ảnh giám định
    if (dataHoSo.dataAnhGiamDinh) {
      dataHoSo.dataAnhGiamDinh.forEach((itemAnhGiamDinh) => {
        if (itemAnhGiamDinh.anhHienTruong || itemAnhGiamDinh.anhTaiLieu || itemAnhGiamDinh.anhToanCanh || itemAnhGiamDinh.anhTonThat) tonTaiAnhChuaUploadLenHeThong = true;
      });
    }
    Alert.alert(
      'Xoá hồ sơ',
      tonTaiAnhChuaUploadLenHeThong
        ? 'Hồ sơ này CÓ ẢNH OFFLINE CHƯA TẢI LÊN HỆ THỐNG.\n Bạn có chắc muốn xoá hồ sơ này trên thiết bị. Dữ liệu hồ sơ vẫn lưu trên hệ thống'
        : 'Hồ sơ này sẽ chỉ bị xoá trên thiết bị. Vẫn lưu trên hệ thống.',
      [
        {
          text: 'Để sau',
          style: 'cancel',
        },
        {
          text: 'Xoá',
          style: 'destructive',
          onPress: async () => {
            await AsyncStorageProvider.xoaHoSo(profileData[indexXoa].so_id || profileData[indexXoa].soIdHoSoLuuTrongMay);
            getDanhSachHoSo();
          },
        },
      ],
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Danh sách hồ sơ offline"
      renderView={
        <View style={styles.container}>
          <View style={styles.warningView}>
            <Icon.AntDesign name="warning" color={colors.ORANGE} size={20} />
            <View>
              <Text children={'LƯU Ý: DỮ LIỆU CHỈ ĐƯỢC LƯU TRỮ TRONG ĐIỆN THOẠI. CHƯA ĐƯỢC TẢI LÊN HỆ THỐNG.'} color={colors.ORANGE} style={styles.txtWarning} font="regular12" />
              {/* <Text children={'HỒ SƠ SẼ TỰ ĐỘNG XOÁ SAU 7 NGÀY KỂ TỪ LẦN HOẠT ĐỘNG CUÔI CÙNG'} color={colors.RED1} style={styles.txtWarning} font="regular12" /> */}
            </View>
          </View>
          <SearchBar
            placeholder="Biển số xe, số hồ sơ"
            onTextChange={(value) => onChangeTextSearch(value)}
            onPressSearch={onPressSearch}
            value={searchInput}
            onSubmitEditing={onPressSearch}
            containerStyle={{shadowColor: '#FFF'}}
          />
          <DropdownPicker
            searchable={false}
            zIndex={10000}
            isOpen={openDropdownTrangThaiHoSo}
            setOpen={setOpenDropdownTrangThaiHoSo}
            items={trangThaiHoSoOfflineDropdown}
            itemSelected={trangThaiHoSoSelected}
            setItemSelected={setTrangThaiHoSoSelected}
            containerStyle={{marginTop: 0, marginHorizontal: spacing.default}}
          />
          <FlatList
            data={profileData || []}
            renderItem={(data) => <ProfileOfflineItem data={data} onPressItem={() => onPressItem(data.item)} dataType="HS_CN" onPressXoaItemHoSo={onPressXoaItemHoSo} />}
            keyExtractor={(item, index) => item.so_id + index.toString()}
            ListEmptyComponent={<Empty />}
          />
        </View>
      }
      footer={<ButtonLinear title="Tạo hồ sơ OFFLINE" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TAO_HO_SO_GIAM_DINH_OFFLINE)} />}
    />
  );
};

export const DanhSachHoSoGiamDinhOfflineScreen = memo(DanhSachHoSoGiamDinhOfflineComponent, isEqual);
