import R from '@app/assets/R';
import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {Icon, Text} from '@component';
import moment from 'moment';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';
interface Props {
  data: any;
  dataType: string;
  onPressItem?: (item) => void;
  onPressXoaItemHoSo: (index: number) => void;
}
let {TEN_TRANG_THAI_HO_SO} = DATA_CONSTANT;
const ProfileOfflineItemComponent = (props: Props) => {
  const {data, dataType, onPressItem, onPressXoaItemHoSo} = props;

  let isHoSoKhac = false;
  const {item, index} = data;

  if (
    dataType === 'HS_CN' &&
    item.ten_trang_thai !== TEN_TRANG_THAI_HO_SO.CHO_CHI_DINH_GDV &&
    item.ten_trang_thai !== TEN_TRANG_THAI_HO_SO.DANG_GIAM_DINH &&
    item.ten_trang_thai !== TEN_TRANG_THAI_HO_SO.YEU_CAU_GIAM_DINH
  )
    isHoSoKhac = true;
  if (
    (dataType === 'HS_HOME' || dataType === 'HS_DIA_BAN') &&
    item.trang_thai_ten !== TEN_TRANG_THAI_HO_SO.CHO_CHI_DINH_GDV &&
    item.trang_thai_ten !== TEN_TRANG_THAI_HO_SO.DANG_GIAM_DINH &&
    item.trang_thai_ten !== TEN_TRANG_THAI_HO_SO.YEU_CAU_GIAM_DINH
  )
    isHoSoKhac = true;
  const icColor = item.nghiep_vu === 'XE_MAY' ? colors.VIOLET1 : colors.PRIMARY;
  const icName = item.nghiep_vu === 'XE_MAY' ? 'motorcycle' : 'automobile';
  const icSize = item.nghiep_vu === 'XE_MAY' ? 16 : 15;
  const xeKhongOHienTruong = item.hien_truong === 'K';
  const soHoSo = item.so_hs.trim() == '' ? 'Hồ sơ chưa lấy số' : item.so_hs;
  const soHoSoTextColor = item.so_hs.trim() == '' ? colors.ORANGE : colors.BLACK;
  return (
    <TouchableOpacity
      key={index}
      onPress={() => onPressItem(item)}
      style={[styles.profileItemView, item.tonTaiAnhChuaUploadLenHeThong && item.ngayConLaiTruocKhiBiXoa <= 3 && {borderColor: colors.RED1}]}>
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemLinearView}>
        <View style={styles.profileItemCenterView}>
          {item.ngayConLaiTruocKhiBiXoa <= 3 && (
            <View style={{flexDirection: 'row', alignItems: 'center', marginRight: spacing.default}}>
              <Icon.MaterialIcons name="error" size={20} color={colors.RED1} />
              <Text
                style={[styles.profileTxtHoSo, {color: colors.RED1, marginBottom: 0}]}
                children={'Hồ sơ sẽ được tự động xoá trên thiết bị sau ' + item.ngayConLaiTruocKhiBiXoa + ' ngày'}
                font="bold14"
              />
            </View>
          )}
          <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
            <Text style={[styles.profileTxtHoSo, {color: colors.PRIMARY}]} children={'Hoạt động lần cuối cùng: ' + item.capNhanLanCuoi} />
          </View>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Icon.Ionicons name="cloud-offline-outline" size={20} />
            <Text style={[styles.profileTxtHoSo, {color: soHoSoTextColor}]} children={soHoSo} />
          </View>
          <View style={styles.profileItemDetail}>
            <View style={styles.profileTimeView}>
              <FastImage source={R.images.img_clock} style={styles.profileImgClock} />
              <Text style={[{color: colors.GRAY5}]} font="bold12" children={item.ngay_tb} />
            </View>
            <View flexDirection="row">
              <Icon.FontAwesome name={icName} size={icSize} style={styles.iconItem} color={icColor} />
              <Text style={[{color: colors.GRAY6}]}>{item.doi_tuong}</Text>
            </View>
          </View>
          <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
            <View style={styles.profileTimeView}>
              <Text style={[{color: colors.GRAY5}]}>Trạng thái: </Text>
              <Text style={[{color: isHoSoKhac ? colors.GREEN : colors.GRAY6}]}>{item.trang_thai_ten || item.ten_trang_thai}</Text>
            </View>
          </View>
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
              <View style={styles.profileTimeView}>
                <Text style={[{color: colors.GRAY5}]}>Nghiệp vụ: </Text>
                <Text style={[{color: colors.GRAY6}]}>{item.nv === 'TN' ? 'Tự nguyện' : 'Bắt buộc'}</Text>
              </View>
            </View>
            <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
              <View style={styles.profileTimeView}>
                <Icon.MaterialCommunityIcons name={xeKhongOHienTruong ? 'garage' : 'map-marker-radius'} size={FontSize.size18} style={styles.iconItem} color={colors.BLUE3} />
                <Text style={[{color: colors.GRAY6}]}>{xeKhongOHienTruong ? 'Xe không ở hiện trường' : 'Xe đang ở hiện trường'}</Text>
              </View>
            </View>
          </View>
          {item.gdh && item.gdh?.trim() !== '' && (
            <View style={[styles.profileItemDetail, {marginTop: spacing.tiny}]}>
              <View style={styles.profileTimeView}>
                <Text style={[{color: colors.GREEN}]}>{item.gdh}</Text>
              </View>
            </View>
          )}
          {item.tonTaiAnhChuaUploadLenHeThong && (
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Icon.Ionicons name="warning-outline" size={20} color={colors.ORANGE} />
              <Text style={[styles.profileTxtHoSo, {color: colors.ORANGE, marginBottom: 0}]} children={'Hồ sơ có ảnh giám định OFFLINE chưa tải lên'} />
            </View>
          )}
          {item.soIdHoSoLuuTrongMay && (
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Icon.Ionicons name="information-circle-outline" size={20} color={colors.PRIMARY} />
              <Text style={[styles.profileTxtHoSo, {color: colors.PRIMARY, marginBottom: 0}]} children={'Hồ sơ tạo OFFLINE, ' + (item.so_id ? 'đã' : 'chưa') + ' đồng bộ dữ liệu'} />
            </View>
          )}
        </View>
        <View style={styles.profileItemRightView}>
          <Icon.MaterialIcons name="keyboard-arrow-right" size={FontSize.size25} style={styles.iconRightItem} color={colors.BLUE1} />
        </View>
        <View style={styles.actionView}>
          <TouchableOpacity style={styles.removeIconView} onPress={() => onPressXoaItemHoSo(index)}>
            <Icon.AntDesign name="closecircle" size={18} color={colors.RED1} />
          </TouchableOpacity>
          {/* {!item.so_id && (
            <TouchableOpacity onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TAO_HO_SO_GIAM_DINH_OFFLINE, {dataHoSoOffline: data.item})}>
              <Icon.AntDesign name="edit" size={18} color={colors.PRIMARY} />
            </TouchableOpacity>
          )} */}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export const ProfileOfflineItem = memo(ProfileOfflineItemComponent, isEqual);

const styles = StyleSheet.create({
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: vScale(spacing.tiny),
    paddingRight: scale(spacing.tiny),
  },
  profileTxtHoSo: {
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    marginBottom: vScale(spacing.tiny),
    marginLeft: spacing.tiny,
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImgClock: {
    marginRight: spacing.tiny,
    width: FontSize.size15,
    height: FontSize.size15,
    opacity: 0.8,
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemLinearView: {
    flex: 1,
    flexDirection: 'row',
    paddingLeft: scale(spacing.medium),
    paddingRight: scale(spacing.smaller),
    borderRadius: 10,
  },
  profileItemView: {
    backgroundColor: '#FFF',
    shadowColor: colors.GRAY3,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,

    flexDirection: 'row',

    borderRadius: 10,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
    marginHorizontal: scale(spacing.smaller),
    marginVertical: scale(spacing.tiny),
  },

  viewBottomBtn: {
    marginVertical: vScale(spacing.medium),
  },
  txtBottomBtn: {
    fontSize: FontSize.size16,
    textAlign: 'center',
    color: colors.PRIMARY,
    textDecorationLine: 'underline',
  },
  iconItem: {
    marginBottom: 2,
    marginRight: spacing.tiny,
    opacity: 0.6,
  },
  iconRightItem: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  actionView: {
    position: 'absolute',
    top: spacing.tiny,
    right: spacing.small,
    flexDirection: 'row',
  },
  removeIconView: {
    marginBottom: spacing.default,
  },
});
