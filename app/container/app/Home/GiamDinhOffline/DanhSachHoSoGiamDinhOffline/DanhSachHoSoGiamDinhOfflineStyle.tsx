import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  contentView: {
    marginTop: 10,
  },
  profileItemLinearView: {
    flexDirection: 'row',
    marginVertical: 5,
    paddingLeft: 20,
    paddingRight: 10,
    borderRadius: 10,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
    marginHorizontal: 10,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: 5,
    paddingRight: 5,
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileTxtHoSo: {
    marginBottom: 4,
    color: colors.BLACK,
    fontSize: 14,
  },
  profileImgClock: {
    marginRight: 5,
    width: 14,
    height: 14,
    opacity: 0.8,
  },
  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  radioView: {
    marginLeft: spacing.tiny,
    marginTop: spacing.smaller,
    // marginBottom: 15,
  },
  dropDownTitle: {
    marginLeft: spacing.smaller,
    fontWeight: 'bold',
    marginBottom: spacing.smaller,
  },
  profileItemView: {
    shadowColor: colors.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  btn: {
    backgroundColor: colors.GRAY,
    padding: 12,
  },
  warningView: {
    flexDirection: 'row',
    marginHorizontal: spacing.default,
    alignItems: 'center',
    marginTop: spacing.small,
    marginBottom: spacing.tiny,
  },
  txtWarning: {
    marginLeft: spacing.tiny,
  },
});
