import {spacing} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  contentView: {
    flex: 1,
    marginHorizontal: spacing.small,
  },
  footerView: {
    flex: 1,
    flexDirection: 'row',
    // borderTopWidth: 0.2,
  },
  footerBtn: {
    marginHorizontal: spacing.small,
  },
  itemPhanLoaiNhanhView: {
    paddingVertical: spacing.small,
    borderBottomWidth: 0.5,
    borderColor: '#CCC',
  },
  headerItemHangMucView: {
    paddingHorizontal: spacing.small,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitleView: {
    flex: 1,
    justifyContent: 'space-between',
    // flexDirection: 'row',
  },
});
