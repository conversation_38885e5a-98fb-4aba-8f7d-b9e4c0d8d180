import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {spacing} from '@app/theme';
import {AsyncStorageProvider, logErrorTryCatch} from '@app/utils';
import {luuDoiTuongGiamDinh, luuListHoSoDangGiamDinh} from '@app/utils/AsyncStorageProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './PhanLoaiDoiTuongOfflineStyles';

const PhanLoaiDoiTuongOfflineScreenComponent = ({route, navigation}) => {
  console.log('PhanLoaiDoiTuongOfflineScreenComponent');
  const {profileData} = route.params;
  const [listDoiTuongChuaPhanLoai, setListDoiTuongChuaPhanLoai] = useState([]);
  const [listDoiTuongDaPhanLoai, setListDoiTuongDaPhanLoai] = useState([]);
  const [dataAnhGiamDinh, setDataAnhGiamDinh] = useState([]);

  //dropdown hạng mục
  const [listDoiTuongServer, setListDoiTuongServer] = useState([]);
  const [indexDropdownDoiTuongOpen, setIndexDropdownDoiTuongOpen] = useState(null);
  const [doiTuongDropdownSelected, setDoiTuongDropdownSelected] = useState(null);

  const [daLuu, setDaLuu] = useState(null);

  useEffect(() => {
    initDataListDoiTuongPhanLoai();
  }, []);

  //hạng mục dropdown
  useEffect(() => {
    try {
      if (doiTuongDropdownSelected && indexDropdownDoiTuongOpen !== null) {
        let doiTuongDaChon = listDoiTuongChuaPhanLoai.find((item) => item.so_id_doi_tuong === doiTuongDropdownSelected);
        if (doiTuongDaChon) {
          setDoiTuongDropdownSelected(null);
          return FlashMessageHelper.showFlashMessage('Thông báo', 'Đối tượng này đã được chọn cho đối tượng ' + doiTuongDaChon.ten_doi_tuong);
        }
        let listDoiTuongChuaPhanLoaiTmp = listDoiTuongChuaPhanLoai;
        listDoiTuongChuaPhanLoaiTmp[indexDropdownDoiTuongOpen].so_id_doi_tuong = doiTuongDropdownSelected;
        setListDoiTuongChuaPhanLoai([...listDoiTuongChuaPhanLoaiTmp]);
        setDoiTuongDropdownSelected(null);
        if (daLuu !== false) setDaLuu(false);
      }
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  }, [doiTuongDropdownSelected, indexDropdownDoiTuongOpen]);

  useEffect(() => {
    if (doiTuongDropdownSelected) setIndexDropdownDoiTuongOpen(null);
  }, [doiTuongDropdownSelected]);
  //end hạng mục dropdown

  const initDataListDoiTuongPhanLoai = async () => {
    try {
      let chiTietHoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay);
      if (chiTietHoSo.listDoiTuong) {
        let listDoiTuongChuaPhanLoai = chiTietHoSo.listDoiTuong.filter((item) => !item.so_id_doi_tuong);
        let listDoiTuongDaPhanLoai = chiTietHoSo.listDoiTuong.filter((item) => item.so_id_doi_tuong);
        setListDoiTuongChuaPhanLoai(listDoiTuongChuaPhanLoai);
        setListDoiTuongDaPhanLoai(listDoiTuongDaPhanLoai);
      }
      if (chiTietHoSo.dataAnhGiamDinh) setDataAnhGiamDinh(chiTietHoSo.dataAnhGiamDinh);
      if (chiTietHoSo.chi_tiet_ho_so?.ds_doi_tuong) setListDoiTuongServer(chiTietHoSo.chi_tiet_ho_so?.ds_doi_tuong);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressLuuPhanLoai = async () => {
    try {
      let coThayDoi = false;
      let chiTietHoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id || profileData.ho_so.soIdHoSoLuuTrongMay);
      for (let i = 0; i < listDoiTuongChuaPhanLoai.length; i++) {
        let itemDoiTuongLocal = listDoiTuongChuaPhanLoai[i];
        if (itemDoiTuongLocal.so_id_doi_tuong) {
          await luuDoiTuongGiamDinh(itemDoiTuongLocal); //lưu vào storage
          let indexDoiTuongLocalGiong = chiTietHoSo.listDoiTuong.findIndex((itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay === itemDoiTuongLocal.soIdDoiTuongLuuTrongMay);
          //xử lý so_id_doi_tuong của dữ liệu dataAnhGiamDinh
          if (chiTietHoSo.dataAnhGiamDinh) {
            let indexDataAnhGiamDinhGiong = chiTietHoSo.dataAnhGiamDinh.findIndex((itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay === itemDoiTuongLocal.soIdDoiTuongLuuTrongMay);
            //cập nhật so_id_doi_tuong của dối tượng từ server vào dataAnhGiamDinh lưu ở local
            if (indexDataAnhGiamDinhGiong > -1 && !chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].soIdDoiTuong) {
              coThayDoi = true;
              chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].soIdDoiTuong = itemDoiTuongLocal.so_id_doi_tuong;
              if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhHienTruong)
                chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhHienTruong.soIdDoiTuong = itemDoiTuongLocal.so_id_doi_tuong;
              if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTaiLieu) chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTaiLieu.soIdDoiTuong = itemDoiTuongLocal.so_id_doi_tuong;
              if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhToanCanh) chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhToanCanh.soIdDoiTuong = itemDoiTuongLocal.so_id_doi_tuong;
              if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTonThat) chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTonThat.soIdDoiTuong = itemDoiTuongLocal.so_id_doi_tuong;
            }
          }
          //cập nhật so_id_doi_tuong của dối tượng từ server vào đối tượng lưu ở local
          if (indexDoiTuongLocalGiong > -1 && !chiTietHoSo.listDoiTuong[indexDoiTuongLocalGiong].so_id_doi_tuong) {
            coThayDoi = true;
            chiTietHoSo.listDoiTuong[indexDoiTuongLocalGiong].so_id_doi_tuong = itemDoiTuongLocal.so_id_doi_tuong;
          }
        }
      }
      if (coThayDoi) await luuListHoSoDangGiamDinh([chiTietHoSo]);
      NavigationUtil.pop();
      FlashMessageHelper.showFlashMessage('Thành công', 'Phân loại thành công', 'success');
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressDropdownHangMuc = (index) => {
    setIndexDropdownDoiTuongOpen(index === indexDropdownDoiTuongOpen ? (indexDropdownDoiTuongOpen === null ? index : null) : index);
  };

  const getListDoiTuongServerGiongDoiTuongDangChon = (dataItemDangChon) => {
    try {
      let arrDoiTuongGiong = [];
      //tìm ra list đối tượng trên server có thuộc tính giống đối tượng đang mở dropdown
      arrDoiTuongGiong = listDoiTuongServer.filter((item) => item.kieu_dt === dataItemDangChon.kieu_dt && item.nhom === dataItemDangChon.nhom);
      if (dataItemDangChon.loai) arrDoiTuongGiong = arrDoiTuongGiong.filter((item) => item.loai === dataItemDangChon.loai);
      if (dataItemDangChon.hang_muc) arrDoiTuongGiong = arrDoiTuongGiong.filter((item) => item.hang_muc === dataItemDangChon.hang_muc);
      //filter bỏ đi những đối tượng server đã được map vào đối tượng local
      arrDoiTuongGiong = arrDoiTuongGiong.filter((itemDoiTuongServer) => {
        if (listDoiTuongDaPhanLoai.find((itemDoiTuongLocal) => itemDoiTuongLocal.so_id_doi_tuong === itemDoiTuongServer.so_id_doi_tuong)) return false;
        return true;
      });
      //filter bỏ đi những đối tượng server đã có dữ liệu chụp ảnh
      if (dataAnhGiamDinh.length > 0) {
        arrDoiTuongGiong = arrDoiTuongGiong.filter((itemDoiTuongServer) => {
          if (dataAnhGiamDinh.find((itemAnhGiamDinh) => itemAnhGiamDinh.soIdDoiTuong === itemDoiTuongServer.so_id_doi_tuong)) return false;
          return true;
        });
      }
      return arrDoiTuongGiong;
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
      return [];
    }
  };

  /**RENDER  */
  const renderItemPhanLoaiDoiTuong = ({item, index}) => {
    return (
      <View style={[{backgroundColor: index % 2 == 0 ? colors.WHITE8 : '#FFF'}, styles.itemPhanLoaiNhanhView]}>
        <View style={styles.headerItemHangMucView}>
          <View style={styles.headerTitleView}>
            <TouchableOpacity style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text style={styles.headerSubTitle} font="medium14" color={'#000'} children={index + 1 + '. ' + item.ten_doi_tuong} />
            </TouchableOpacity>
            {/* {profileData.ds_doi_tuong.length > 1 && (
              <TouchableOpacity onPress={() => onPressToggleExpandHangMuc(index)}>
                <Text style={styles.txtDoiTuong} font="regular12" children={item.tenDoiTuong} />
              </TouchableOpacity>
            )} */}
          </View>

          {/* <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TouchableOpacity onPress={() => onPressToggleExpandHangMuc(index)} style={{paddingHorizontal: spacing.small}}>
              <Icon.MaterialIcons name={'photo'} size={20} color={colors.PRIMARY} />
            </TouchableOpacity>
          </View> */}
        </View>

        <View style={{flexDirection: 'row', marginHorizontal: spacing.default}}>
          <DropdownPicker
            zIndex={6000}
            items={getListDoiTuongServerGiongDoiTuongDangChon(item)}
            itemSelected={item.so_id_doi_tuong}
            setItemSelected={setDoiTuongDropdownSelected}
            isOpen={index === indexDropdownDoiTuongOpen ? true : false}
            placeholder="Đối tượng"
            maxHeight={100}
            schema={{
              label: 'ten_doi_tuong',
              value: 'so_id_doi_tuong',
            }}
            onPress={() => onPressDropdownHangMuc(index)}
            containerStyle={{marginRight: spacing.default, marginBottom: index === indexDropdownDoiTuongOpen ? 100 : spacing.default, flex: 1.7}}
            searchable={false}
          />
        </View>
        {/* CHỈ HIỂN THỊ LIST ẢNH VỚI TRƯỜNG HỢP item.expanded(ẢNH HỒ SƠ, ẢNH TỔN THẤT CÓ PROS NÀY) VÀ CÁC LOẠI ẢNH KHÁC ANH_TON_THAT VÀ ANH_HO_SO */}
        {/* {item.expanded && (
          <FlatList
            data={item.images}
            renderItem={(itemImage) => renderItemAnh(itemImage, {listImage: item.images})}
            keyExtractor={(itemImage) => itemImage.bt.toString()}
            horizontal={true}
            style={{marginLeft: spacing.default}}
          />
        )} */}
      </View>
    );
  };
  const renderPhanLoaiDoiTuongView = () => {
    return (
      <FlatList
        data={listDoiTuongChuaPhanLoai}
        renderItem={renderItemPhanLoaiDoiTuong}
        initialNumToRender={50}
        ListEmptyComponent={<Empty />}
        contentContainerStyle={{paddingBottom: 200}}
        style={{marginBottom: spacing.default}}
      />
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle="Phân loại đối tượng OFFLINE"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={{flex: 1}}>{renderPhanLoaiDoiTuongView()}</KeyboardAwareScrollView>
        </View>
      }
      footer={<ButtonLinear title="Lưu phân loại" onPress={onPressLuuPhanLoai} />}
    />
  );
};

export const PhanLoaiDoiTuongOfflineScreen = memo(PhanLoaiDoiTuongOfflineScreenComponent, isEqual);
