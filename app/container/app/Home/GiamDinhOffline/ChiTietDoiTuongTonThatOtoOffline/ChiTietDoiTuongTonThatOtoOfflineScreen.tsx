import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {spacing} from '@app/theme';
import {AsyncStorageProvider, logErrorTryCatch} from '@app/utils';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Icon, ScreenComponent, Text, TextInputOutlined} from '@component';
import {DATA_CONSTANT} from '@constant';
import moment from 'moment';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Keyboard, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './ChiTietDoiTuongTonThatOtoOfflineStyle';
import {listKieuDoiTuong, listLoaiDoiTuong, listLoaiTaiSan, listNhomDoiTuong, titleInput} from './Constant';

const ChiTietDoiTuongTonThatOtoOfflineScreenComponent = ({route}) => {
  console.log('ChiTietDoiTuongTonThatOtoOfflineScreenComponent');
  const {profileData, doiTuongTonThat} = route.params;
  const isHoSoTN = profileData.ho_so.nv_ma === 'TN';
  // DROPDOWN NHÓM ĐỐI TƯỢNG
  const [openNhomDoiTuong, setOpenNhomDoiTuong] = useState(false);
  // DROPDOWN LOẠI TÀI SẢN - dành riêng cho nhóm đối tượng là tài sản
  const [openLoaiTaiSan, setOpenLoaiTaiSan] = useState(false);
  // DROPDOWN LOẠI ĐỐI TƯỢNG - dành riêng cho nhóm đối tượng là con người
  const [openLoaiDoiTuong, setOpenLoaiDoiTuong] = useState(false);

  const [openKieuDoiTuong, setOpenKieuDoiTuong] = useState(false);

  const getDefaultFormValue = () => {
    let kieuDoiTuong = '';
    if (doiTuongTonThat && doiTuongTonThat.kieu_dt) kieuDoiTuong = doiTuongTonThat.kieu_dt;
    else if (isHoSoTN) kieuDoiTuong = 'TT';
    let nhomDoiTuong = '';
    if (doiTuongTonThat && doiTuongTonThat.nhom) nhomDoiTuong = doiTuongTonThat.nhom;
    else if (isHoSoTN) nhomDoiTuong = 'XE';
    return {
      kieuDoiTuong: kieuDoiTuong,
      nhomDoiTuong: nhomDoiTuong,
      loaiTaiSan: doiTuongTonThat && doiTuongTonThat.nhom === 'TAI_SAN' ? doiTuongTonThat.loai : '',
      loaiDoiTuong: doiTuongTonThat && doiTuongTonThat.nhom === 'NGUOI' ? doiTuongTonThat.loai : '',
      tenDoiTuong: doiTuongTonThat ? doiTuongTonThat.ten_doi_tuong : '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });
  const watchNhomDoiTuong = watch('nhomDoiTuong');
  const watchKieuDoiTuong = watch('kieuDoiTuong');

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const closeDropdown = () => {
    openNhomDoiTuong && setOpenNhomDoiTuong(false);
    openLoaiDoiTuong && setOpenLoaiDoiTuong(false);
    openKieuDoiTuong && setOpenKieuDoiTuong(false);
  };

  const onPressSave = async (data) => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      so_id: profileData.ho_so.so_id,
      soIdHoSoLuuTrongMay: profileData.ho_so.soIdHoSoLuuTrongMay,
      so_id_doi_tuong: doiTuongTonThat ? doiTuongTonThat.so_id_doi_tuong : '',
      soIdDoiTuongLuuTrongMay: doiTuongTonThat ? doiTuongTonThat.soIdDoiTuongLuuTrongMay : moment().valueOf(),
      kieu_dt: data.kieuDoiTuong,
      nhom: data.nhomDoiTuong,
      loai: data.nhomDoiTuong === 'TAI_SAN' ? data.loaiTaiSan : data.loaiDoiTuong,
      ten_doi_tuong: data.tenDoiTuong,
      hang_muc: '',
    };
    if (data.kieuDoiTuong === 'BH' && profileData.ds_doi_tuong?.find((item) => item.kieu_dt === 'BH')) return FlashMessageHelper.showFlashMessage('Thông báo', 'Đã tồn tại đối tượng được bảo hiểm');
    if (params.nhom === 'XE') params.hang_muc = 'XE';
    else if (params.nhom === 'HANG_HOA') params.hang_muc = 'HANG_HOA';
    else if (params.nhom === 'TAI_SAN' && params.loai === 'XE') params.hang_muc = 'XE';
    else if (params.nhom === 'TAI_SAN' && params.loai === 'KHAC') params.hang_muc = 'TAI_SAN';
    try {
      //nếu không có danh sách đối tượng
      await AsyncStorageProvider.luuDoiTuongGiamDinh(params);
      NavigationUtil.pop();
      FlashMessageHelper.showFlashMessage('Thành công', 'Tạo đối tượng tổn thất thành công', 'success');
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressXoa = () => {
    Alert.alert('Thông báo', 'Bạn có muốn xoá đối tượng này', [
      {
        text: 'Để sau',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            await AsyncStorageProvider.xoaDoiTuongGiamDinh(doiTuongTonThat);
            NavigationUtil.pop();
            FlashMessageHelper.showFlashMessage('Thành công', 'Xoá đối tượng tổn thất thành công', 'success');
          } catch (error) {
            logErrorTryCatch(error);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const getListNhomDoiTuong = () => {
    try {
      let nhomDoiTuong = [];
      //NẾU LÀ HỒ SƠ TỰ NGUYỆN - VẬT CHẤT XE
      if (isHoSoTN) return listNhomDoiTuong.filter((item) => item.value === 'XE');
      //NẾU LÀ HỒ SƠ BẮT BUỘC - TRÁCH NHIỆM DÂN SỰ
      else {
        if (watchKieuDoiTuong === 'BH') return listNhomDoiTuong.filter((item) => item.value === 'XE');
        else return listNhomDoiTuong.filter((item) => item.value !== 'XE');
      }
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
      return [];
    }
  };

  /**RENDER  */

  return (
    <ScreenComponent
      headerBack
      headerTitle="Đối tượng tổn thất"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView showsVerticalScrollIndicator={false} scrollEnabled={true} keyboardShouldPersistTaps="handled">
            <View style={styles.contentView}>
              <View style={styles.canhBaoChiTietHoSoView}>
                <Icon.Ionicons name="warning" color={colors.ORANGE} size={20} style={{marginRight: spacing.tiny}} />
                <Text children="Đối tượng OFFLINE được lưu ở máy. Chưa tải lên hệ thống." color={colors.ORANGE} />
              </View>
              <Controller
                control={control}
                name="kieuDoiTuong"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title={titleInput[8]}
                    zIndex={10000}
                    isOpen={openKieuDoiTuong}
                    setOpen={(value) => {
                      Keyboard.dismiss();
                      setOpenKieuDoiTuong(value);
                    }}
                    searchable={false}
                    items={isHoSoTN ? listKieuDoiTuong.filter((item) => item.value === 'TT') : listKieuDoiTuong}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    placeholder="Chọn kiểu đối tượng"
                    containerStyle={{marginBottom: spacing.small}}
                    isRequired={true}
                    inputErr={errors.kieuDoiTuong && getErrMessage('kieuDoiTuong', errors.kieuDoiTuong.type)}
                  />
                )}
              />

              <Controller
                control={control}
                name="nhomDoiTuong"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title={titleInput[0]}
                    zIndex={9000}
                    searchable={false}
                    isOpen={openNhomDoiTuong}
                    setOpen={(value) => {
                      Keyboard.dismiss();
                      setOpenNhomDoiTuong(value);
                    }}
                    items={getListNhomDoiTuong()}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    onOpen={() => openLoaiTaiSan && setOpenLoaiTaiSan(false)}
                    // disabled={doiTuongTonThat ? true : false}
                    placeholder="Chọn nhóm đối tượng"
                    containerStyle={{marginBottom: openNhomDoiTuong ? 80 : spacing.small}}
                    isRequired={true}
                    inputErr={errors.nhomDoiTuong && getErrMessage('nhomDoiTuong', errors.nhomDoiTuong.type)}
                  />
                )}
              />

              {watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma && (
                <Controller
                  control={control}
                  name="loaiTaiSan"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title={titleInput[4]}
                      zIndex={8000}
                      searchable={false}
                      isOpen={openLoaiTaiSan}
                      setOpen={setOpenLoaiTaiSan}
                      items={listLoaiTaiSan}
                      itemSelected={value}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      placeholder="Chọn loại tài sản"
                      containerStyle={{marginBottom: openLoaiTaiSan ? listLoaiTaiSan.length * 40 : spacing.small}}
                      isRequired={true}
                      inputErr={errors.loaiTaiSan && getErrMessage('loaiTaiSan', errors.loaiTaiSan.type)}
                    />
                  )}
                />
              )}

              {watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma && (
                <Controller
                  control={control}
                  name="loaiDoiTuong"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title={titleInput[5]}
                      zIndex={7000}
                      searchable={false}
                      isOpen={openLoaiDoiTuong}
                      setOpen={setOpenLoaiDoiTuong}
                      items={listLoaiDoiTuong}
                      itemSelected={value}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      placeholder="Chọn loại đối tượng"
                      containerStyle={{marginBottom: openLoaiDoiTuong ? listLoaiDoiTuong.length * 40 : spacing.small}}
                      isRequired={true}
                      inputErr={errors.loaiDoiTuong && getErrMessage('loaiDoiTuong', errors.loaiDoiTuong.type)}
                    />
                  )}
                />
              )}

              {/* tên đối tượng */}
              <View zIndex={5000}>
                <Controller
                  control={control}
                  name="tenDoiTuong"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      title={titleInput[1]}
                      value={value}
                      onChangeText={onChange}
                      placeholder={titleInput[1]}
                      onFocus={closeDropdown}
                      isRequired={true}
                      error={errors.tenDoiTuong && getErrMessage('tenDoiTuong', errors.tenDoiTuong.type)}
                    />
                  )}
                />
              </View>
            </View>
          </KeyboardAwareScrollView>
        </View>
      }
      footer={
        <View style={styles.footerView}>
          {doiTuongTonThat && !doiTuongTonThat.so_id_doi_tuong && (
            <ButtonLinear title="Xoá OFFLINE" onPress={onPressXoa} linearColors={[colors.GRAY, colors.GRAY]} linearStyle={{marginRight: spacing.default}} />
          )}
          <ButtonLinear title="Lưu OFFLINE" onPress={handleSubmit(onPressSave)} />
        </View>
      }
    />
  );
};

export const ChiTietDoiTuongTonThatOtoOfflineScreen = memo(ChiTietDoiTuongTonThatOtoOfflineScreenComponent, isEqual);
