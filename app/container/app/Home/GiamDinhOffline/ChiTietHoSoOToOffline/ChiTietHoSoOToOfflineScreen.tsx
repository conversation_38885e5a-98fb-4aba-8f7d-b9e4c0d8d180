import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {AsyncStorageProvider, logErrorTryCatch} from '@app/utils';
import {cloneObject, getCauHinhHoSoByMa} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CustomTabBar, ScreenComponent} from '@component';
import {DATA_CONSTANT, SCREEN_ROUTER_APP} from '@constant';
import React, {createRef, memo, useCallback, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, ScrollView, View} from 'react-native';
import ActionSheet from 'react-native-actionsheet';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {connect} from 'react-redux';
import styles from './ChiTietHoSoOToOfflineStyles';
import {TabTaiLieuBoiThuongOTo, TabThongTinHoSoOTo} from './Components';
import {chupAnhIndex, extensionsImage} from './Constant';
import {luuDoiTuongGiamDinh, luuListHoSoDangGiamDinh} from '@app/utils/AsyncStorageProvider';

const ChiTietHoSoOToOfflineComponent = ({route, navigation}) => {
  console.log('ChiTietHoSoOToOfflineComponent');
  const {} = route?.params;

  let scrollViewRef = useRef(null);
  let tabViewRef = useRef(null);
  let actionSheetChupAnhRef = createRef(null);
  let actionSheetChuyenThongTinHienTruongRef = createRef(null);

  let refTabTaiLieuBoiThuongOTo = useRef(null);
  let refModalHuyHoSo = useRef(null);
  let refModalTuChoiBoiThuong = useRef(null);

  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [imageDataStep1, setImageDataStep1] = useState([]); //ẢNH HIỆN TRƯỜNG
  const [imageDataStep2, setImageDataStep2] = useState([]); //ẢNH TOÀN CẢNH
  const [imageDataStep3, setImageDataStep3] = useState([]); //ẢNH TỔN THẤT dữ liệu kiểu : [{//thông tin menu,images}]
  const [imageDataStep4, setImageDataStep4] = useState([]); //ẢNH GIẤY TỜ dữ liệu kiểu : [{//thông tin menu,images}]
  const [anhDanhGiaRuiRo, setAnhDanhGiaRuiRo] = useState([]); //ẢNH ĐÁNH GIÁ RỦI RO dữ liệu kiểu : [{//thông tin menu,images}]
  const [anhNghiemThu, setAnhNghiemThu] = useState([]); //ẢNH NGHIỆM THU
  const [anhThuHoiVatTu, setAnhThuHoiVatTu] = useState([]); //ẢNH THU HỒI VẬT TƯ
  const [anhXacMinhHienTruong, setAnhXacMinhHienTruong] = useState([]); //ẢNH XÁC MINH HIỆN TRƯỜNG
  const [listTaiLieuPdf, setListTaiLieuPdf] = useState([]); //ẢNH XÁC MINH HIỆN TRƯỜNG
  const [dataAnhCapDon, setDataAnhCapDon] = useState([]);
  const [switchImgView, setSwitchImgView] = useState(false); //chuyển đổi chế độ XEM - CHỌN ảnh
  const [xemTaiLieuSelected, setXemTaiLieuSelected] = useState('ANH_TON_THAT');
  const [btnTabActive, setBtnTabActive] = useState(0);
  const [doiTuongDuocChon, setDoiTuongDuocChon] = useState(null); //đối tượng tổn thất được chọn để chụp ảnh
  const [toggleModalChonDoiTuong, setToggleModalChonDoiTuong] = useState(false); //đối tượng tổn thất được chọn để chụp ảnh
  const [toggleModalChonDoiTuongHoSo, setToggleModalChonDoiTuongHoSo] = useState(false); //đối tượng tổn thất được chọn để chụp ảnh úc bổ sung hồ sơ, giấy tờ
  const [danhGiaHienTruong, setDanhGiaHienTruong] = useState([]);
  const [actionSheetMauInData, setActionSheetMauInData] = useState([]); //data của actionSheetMauIN
  const [actionSheetMauInXacNhanBienBanData, setActionSheetMauInXacNhanBienBanData] = useState([]); //data của actionSheeXacNhanBienBan
  const [listCanhBaoHoSo, setListCanhBaoHoSo] = useState([]);

  useEffect(() => {
    navigation.addListener('focus', () => initScreenDatas());
    navigation.addListener('blur', () => setAnhDanhGiaRuiRo([]));
  }, []);

  const initScreenDatas = async () => {
    setDoiTuongDuocChon(null);
    setToggleModalChonDoiTuong(false);
    initChiTietHoSoGiamDinhOto();
  };

  useEffect(() => {
    try {
      if (doiTuongDuocChon) {
        if (btnTabActive == '0') openActionSheetChupAnh();
        else {
          setToggleModalChonDoiTuongHoSo(false);
          setToggleModalChonDoiTuong(false);
          if (btnTabActive == '1') {
            if (xemTaiLieuSelected === 'ANH_HO_SO') onPressChonLoaiGiamDinh(1, xemTaiLieuSelected);
            else if (xemTaiLieuSelected === 'ANH_HIEN_TRUONG') onPressTakePicture(0, xemTaiLieuSelected);
            else onPressChupAnhChiTiet(xemTaiLieuSelected);
          }
        }
        return;
        if (profileData.ho_so.hien_truong === 'K') {
          if (btnTabActive == '0') onPressChupAnhChiTiet();
          else if (btnTabActive == '1') onPressChupAnhChiTiet(xemTaiLieuSelected);
          setToggleModalChonDoiTuong(false);
          setToggleModalChonDoiTuongHoSo(false);
        } else if (profileData.ho_so.hien_truong === 'D') {
          if (btnTabActive == '0') openActionSheetChupAnh();
          else {
            setToggleModalChonDoiTuongHoSo(false);
            setToggleModalChonDoiTuong(false);
            if (btnTabActive == '1') {
              if (xemTaiLieuSelected === 'ANH_HO_SO') onPressChonLoaiGiamDinh(1, xemTaiLieuSelected);
              else if (xemTaiLieuSelected === 'ANH_HIEN_TRUONG') onPressTakePicture(0, xemTaiLieuSelected);
              else onPressChupAnhChiTiet(xemTaiLieuSelected);
            }
          }
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  }, [doiTuongDuocChon]);

  // EVENT khi chọn vào tài liệu bồi thường, khi profileData hoặc xemTaiLieuSelected thay đổi
  // useEffect(() => {
  //   if (btnTabActive == '1') {
  //     return;
  //     try {
  //       if (xemTaiLieuSelected === 'ANH_HIEN_TRUONG') initAnhHienTruong();
  //       else if (xemTaiLieuSelected === 'ANH_TON_THAT') initAnhTonThat();
  //       else if (xemTaiLieuSelected === 'ANH_TOAN_CANH') initAnhToanCanh();
  //       else if (xemTaiLieuSelected === 'ANH_CAP_DON') initAnhCapDon();
  //       else if (xemTaiLieuSelected === 'ANH_DANH_GIA_RUI_RO') initAnhDanhGiaRuiRo();
  //       else if (xemTaiLieuSelected === 'ANH_NGHIEM_THU') initAnhNghiemThu();
  //       else if (xemTaiLieuSelected === 'ANH_THVT') initAnhThuHoiVatTu();
  //       else if (xemTaiLieuSelected === 'XMHT') initAnhXacMinhHienTruong();
  //       else if (xemTaiLieuSelected === 'ANH_HO_SO') initAnhHoSoGiayTo();
  //       else if (xemTaiLieuSelected === 'FILE_PDF') initTaiLieuPDF();
  //     } catch (error) {
  //       Alert.alert('Thông báo', error.message);
  //     }
  //   }
  // }, [btnTabActive, profileData, xemTaiLieuSelected]);

  const initChiTietHoSoGiamDinhOto = async () => {
    //lấy chi tiết hồ sơ
    try {
      let chiTietHoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(route.params.profileDetail.so_id || route.params.profileDetail.soIdHoSoLuuTrongMay);
      chiTietHoSo = cloneObject(chiTietHoSo);
      if (chiTietHoSo.chi_tiet_ho_so) {
        let coThayDoi = false;
        let listDoiTuongLocalChuaMap = [];
        //nếu có listDoiTuong được tạo ở local -> so sánh thuộc tính để map list đối tượng được tạo ở local với list đối tượng từ trên server
        if (chiTietHoSo.listDoiTuong?.length > 0 && chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong?.length > 0) {
          //duyệt list đối tượng lưu trong local
          for (let i = 0; i < chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong.length; i++) {
            let itemDoiTuongServer = chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong[i];
            //filter để tìm ra đối tượng được lưu trong server có thuộc tính giống với đối tượng lưu ở local
            let listDoiTuongLocalGiongFilter = chiTietHoSo.listDoiTuong.filter(
              (itemDoiTuongLocal) => itemDoiTuongLocal.kieu_dt === itemDoiTuongServer.kieu_dt && itemDoiTuongLocal.nhom === itemDoiTuongServer.nhom,
            );
            let listDoiTuongServerGiongFilter = chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong.filter(
              (itemDoiTuongLocal) => itemDoiTuongLocal.kieu_dt === itemDoiTuongServer.kieu_dt && itemDoiTuongLocal.nhom === itemDoiTuongServer.nhom,
            );

            if (itemDoiTuongServer.loai) listDoiTuongLocalGiongFilter = listDoiTuongLocalGiongFilter.filter((itemDoiTuong) => itemDoiTuong.loai === itemDoiTuongServer.loai);
            if (itemDoiTuongServer.hang_muc) listDoiTuongLocalGiongFilter = listDoiTuongLocalGiongFilter.filter((itemDoiTuong) => itemDoiTuong.hang_muc === itemDoiTuongServer.hang_muc);
            //nếu là 1 đối tượng local - 1 đôi tượng server -> thì map luôn vào nhau
            if (listDoiTuongLocalGiongFilter.length === 1 && listDoiTuongServerGiongFilter.length === 1) {
              let indexDoiTuongLocalGiong = chiTietHoSo.listDoiTuong.findIndex((itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay === listDoiTuongLocalGiongFilter[0].soIdDoiTuongLuuTrongMay);
              //xử lý so_id_doi_tuong của dữ liệu dataAnhGiamDinh
              if (chiTietHoSo.dataAnhGiamDinh) {
                let indexDataAnhGiamDinhGiong = chiTietHoSo.dataAnhGiamDinh.findIndex(
                  (itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay === listDoiTuongLocalGiongFilter[0].soIdDoiTuongLuuTrongMay,
                );
                //cập nhật so_id_doi_tuong của dối tượng từ server vào dataAnhGiamDinh lưu ở local
                if (indexDataAnhGiamDinhGiong > -1 && !chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].soIdDoiTuong) {
                  coThayDoi = true;
                  chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].soIdDoiTuong = itemDoiTuongServer.so_id_doi_tuong;
                  if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhHienTruong)
                    chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhHienTruong.soIdDoiTuong = itemDoiTuongServer.so_id_doi_tuong;
                  if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTaiLieu)
                    chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTaiLieu.soIdDoiTuong = itemDoiTuongServer.so_id_doi_tuong;
                  if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhToanCanh)
                    chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhToanCanh.soIdDoiTuong = itemDoiTuongServer.so_id_doi_tuong;
                  if (chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTonThat)
                    chiTietHoSo.dataAnhGiamDinh[indexDoiTuongLocalGiong].anhTonThat.soIdDoiTuong = itemDoiTuongServer.so_id_doi_tuong;
                }
              }
              //cập nhật so_id_doi_tuong của dối tượng từ server vào đối tượng lưu ở local
              if (indexDoiTuongLocalGiong > -1 && !chiTietHoSo.listDoiTuong[indexDoiTuongLocalGiong].so_id_doi_tuong) {
                coThayDoi = true;
                chiTietHoSo.listDoiTuong[indexDoiTuongLocalGiong].so_id_doi_tuong = itemDoiTuongServer.so_id_doi_tuong;
              }
            }
            //nếu list đối tượng > 1
            else {
              for (let j = 0; j < listDoiTuongLocalGiongFilter.length; j++) {
                if (
                  !listDoiTuongLocalChuaMap.find((itemDoiTuong) => itemDoiTuong.soIdDoiTuongLuuTrongMay === listDoiTuongLocalGiongFilter[j].soIdDoiTuongLuuTrongMay) &&
                  !listDoiTuongLocalGiongFilter[j].so_id_doi_tuong
                )
                  listDoiTuongLocalChuaMap.push(listDoiTuongLocalGiongFilter[j]);
              }
            }
          }
        }
        if (coThayDoi) await luuListHoSoDangGiamDinh([chiTietHoSo]);
        //check xem có đối tượng nào chưa map vào không.
        if (listDoiTuongLocalChuaMap.length > 0) {
          for (let i = 0; i < listDoiTuongLocalChuaMap.length; i++) chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong.unshift(listDoiTuongLocalChuaMap[i]);
        }
        //kiểm tra xem đối tượng có ảnh đã upload lên hay chưa. nếu chưa có thì cảnh báo ở đối tượng
        if (chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong?.length > 0) {
          for (let i = 0; i < chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong.length; i++) {
            let itemDoiTuongServer = chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong[i];
            let anhGiamDinhTheoDoiTuong = chiTietHoSo.dataAnhGiamDinh?.find(
              (itemAnhGiamDinh) =>
                (itemDoiTuongServer.so_id_doi_tuong && itemDoiTuongServer.so_id_doi_tuong === itemAnhGiamDinh.soIdDoiTuong) ||
                (itemDoiTuongServer.soIdDoiTuongLuuTrongMay && itemDoiTuongServer.soIdDoiTuongLuuTrongMay === itemAnhGiamDinh.soIdDoiTuongLuuTrongMay),
            );
            if (anhGiamDinhTheoDoiTuong) {
              let message = '';
              if (anhGiamDinhTheoDoiTuong.anhHienTruong) message = 'Ảnh hiện trường';
              if (anhGiamDinhTheoDoiTuong.anhTaiLieu) message += message === '' ? 'Ảnh tài liệu' : ', ảnh tài liệu';
              if (anhGiamDinhTheoDoiTuong.anhToanCanh) message += message === '' ? 'Ảnh toàn cảnh' : ', ảnh toàn cảnh';
              if (anhGiamDinhTheoDoiTuong.anhTonThat) message += message === '' ? 'Ảnh tổn thất' : ', ảnh tổn thất';
              if (message) message += ' chưa tải lên hệ thống';
              chiTietHoSo.chi_tiet_ho_so.ds_doi_tuong[i].canhBao = message;
            }
          }
        }
        setProfileData(chiTietHoSo.chi_tiet_ho_so);
      } else {
        let profileDataTmp = {};
        let ho_so = {
          ...chiTietHoSo,
          nv_ma: chiTietHoSo.nv,
          trang_thai: chiTietHoSo.trang_thai_ten,
        };
        profileDataTmp = {
          ho_so,
        };
        //nếu có list đối tượng được tạo ở local
        if (chiTietHoSo.listDoiTuong) profileDataTmp.ds_doi_tuong = chiTietHoSo.listDoiTuong;
        //kiểm tra xem đối tượng có ảnh đã upload lên hay chưa. nếu chưa có thì cảnh báo ở đối tượng
        if (profileDataTmp.ds_doi_tuong && profileDataTmp.ds_doi_tuong?.length > 0) {
          for (let i = 0; i < profileDataTmp.ds_doi_tuong.length; i++) {
            let itemDoiTuongServer = profileDataTmp.ds_doi_tuong[i];
            let anhGiamDinhTheoDoiTuong = chiTietHoSo.dataAnhGiamDinh?.find(
              (itemAnhGiamDinh) =>
                (itemDoiTuongServer.so_id_doi_tuong && itemDoiTuongServer.so_id_doi_tuong === itemAnhGiamDinh.soIdDoiTuong) ||
                (itemDoiTuongServer.soIdDoiTuongLuuTrongMay && itemDoiTuongServer.soIdDoiTuongLuuTrongMay === itemAnhGiamDinh.soIdDoiTuongLuuTrongMay),
            );
            if (anhGiamDinhTheoDoiTuong) {
              let message = '';
              if (anhGiamDinhTheoDoiTuong.anhHienTruong) message = 'Ảnh hiện trường';
              if (anhGiamDinhTheoDoiTuong.anhTaiLieu) message += message === '' ? 'Ảnh tài liệu' : ', ảnh tài liệu';
              if (anhGiamDinhTheoDoiTuong.anhToanCanh) message += message === '' ? 'Ảnh toàn cảnh' : ', ảnh toàn cảnh';
              if (anhGiamDinhTheoDoiTuong.anhTonThat) message += message === '' ? 'Ảnh tổn thất' : ', ảnh tổn thất';
              if (message) message += ' chưa tải lên hệ thống';
              profileDataTmp.ds_doi_tuong[i].canhBao = message;
            }
          }
        }
        setProfileData(profileDataTmp);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  // GET CHI TIẾT HỒ SƠ Ô TÔ
  const getChiTietHoSoGiamDinhOto = async () => {
    setDialogLoading(true);
    //lấy chi tiết hồ sơ
    try {
      let paramsProfileDetail = {
        ma_doi_tac: profileData.ho_so?.ma_doi_tac,
        so_id: profileData.ho_so?.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.PROFILE_DATA, paramsProfileDetail);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const dataInfo = response.data_info;
      setProfileData(dataInfo);
      await AsyncStorageProvider.luuHoSoDangGiamDinh(dataInfo); //cập nhật lại data chi tiết vào storage
      await initChiTietHoSoGiamDinhOto(); //khởi tạo lại data
      let actionSheetTmp = [];
      dataInfo.mau_in.map((item) => actionSheetTmp.push(item.ten));
      actionSheetTmp.push('Để sau');
      setActionSheetMauInData(actionSheetTmp);
      actionSheetTmp = [];
      dataInfo.mau_in.map((item) => {
        if (item.ma_mau_in === 'ESCS_BBGD_HIEN_TRUONG' || item.ma_mau_in === 'ESCS_BBGD_XAC_DINH_THIET_HAI_XCG') actionSheetTmp.push(item.ten);
      });
      actionSheetTmp.push('Để sau');
      setActionSheetMauInXacNhanBienBanData(actionSheetTmp);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  // GET ẢNH TÀI LIỆU THEO MÃ
  const getTaiLieuBoiThuong = async (ma_file) => {
    return new Promise(async (resolve, reject) => {
      try {
        //lấy thông tin của ảnh giám định
        let params = {so_id: profileData.ho_so.so_id, ma_file};
        let responseFileThumbnail = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
        setDialogLoading(false);
        if (!responseFileThumbnail || !responseFileThumbnail.state_info || responseFileThumbnail.state_info.status !== 'OK') return resolve([]);
        resolve(responseFileThumbnail.data_info);
      } catch (error) {
        setDialogLoading(false);
        Alert.alert('Thông báo', error.message);
        resolve([]);
      }
    });
  };

  //KHỞI TẠO ẢNH HIỆN TRƯỜNG
  const initAnhHienTruong = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('HT0001');
    if (response.length > 0) setImageDataStep1([...response]);
  };
  //KHỞI TẠO ẢNH TOÀN CẢNH
  const initAnhToanCanh = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('TC0001');
    let imagesTmp = response.map((item) => {
      item.checked = false;
      item.path = item.duong_dan;
      item.name = item.ten_file;
      let nhom = {
        checked: false,
        ma: item.ma_file,
        ten: item.nhom_anh,
        nhom_hang_muc: item.nhom_hang_muc,
      };
      item.nhom = nhom;
      return item;
    });
    let hangMucMoi = null;
    hangMucMoi = {
      checked: false,
      expanded: false,
      hang_muc: {
        ten_hang_muc: 'Ảnh toàn cảnh',
        loai: 'TC',
        ma_file: 'TC0001',
      },
      images: imagesTmp,
      ma: 'TC0001',
      ten: 'Ảnh toàn cảnh',
    };
    setImageDataStep2([hangMucMoi]);
  };
  //KHỞI TẠO ẢNH TỔN THẤT
  const initAnhTonThat = () => {
    let listHangMuc = [];
    let hangMucTonThat = profileData.hang_muc_chup.filter((item) => item.loai === 'TT');
    // for theo hang_muc
    for (let i = 0; i < hangMucTonThat.length; i++) {
      //tìm hạng mục i trong imageDataStep3
      let hangMucCu = imageDataStep3.find((item) => item.ma === hangMucTonThat[i].hang_muc && item.hang_muc.so_id_doi_tuong === hangMucTonThat[i].so_id_doi_tuong);
      let hangMucMoi = null; //hạng mục mới để push vào listHangMuc
      //nếu có hạng mục cũ -> chỉ fill mới lại hang_muc
      if (hangMucCu) {
        hangMucMoi = {...hangMucCu};
        hangMucMoi.hang_muc = hangMucTonThat[i];
      }
      //nếu hang_muc[i] chưa có thì tạo mới
      else {
        hangMucMoi = {
          checked: false,
          expanded: false,
          hang_muc: hangMucTonThat[i],
          images: [],
          ma: hangMucTonThat[i].hang_muc,
          ten: hangMucTonThat[i].ten_hang_muc,
          tenDoiTuong: profileData.ds_doi_tuong.find((itemDoiTuong) => itemDoiTuong.so_id_doi_tuong === hangMucTonThat[i].so_id_doi_tuong)?.ten_doi_tuong || '',
        };
      }
      listHangMuc.push(hangMucMoi); //push vào mới
    }
    //nếu có > 2 đối tượng -> thì sort theo đối tượng cho dễ xem
    // if (profileData.ds_doi_tuong.length > 1) listHangMuc = listHangMuc.sort((a, b) => a.hang_muc.so_id_doi_tuong > b.hang_muc.so_id_doi_tuong);
    setImageDataStep3([...listHangMuc]);
  };

  //KHỞI TẠO ẢNH HỒ SƠ GIẤY TỜ
  const initAnhHoSoGiayTo = () => {
    let listHangMuc = [];
    let hangMucGiayTo = profileData.hang_muc_chup.filter(
      (item) => item.loai === 'TL' && item.loai_hang_muc !== 'PDF' && item.hang_muc !== 'ANH_THVT' && item.hang_muc !== 'XMHT' && item.hang_muc !== 'ANH_NGHIEM_THU',
    );
    // for theo hang_muc
    for (let i = 0; i < hangMucGiayTo.length; i++) {
      //tìm hạng mục i trong imageDataStep3
      let hangMucCu = imageDataStep4.find((item) => item.ma === hangMucGiayTo[i].hang_muc && item.hang_muc.so_id_doi_tuong === hangMucGiayTo[i].so_id_doi_tuong);
      let hangMucMoi = null; //hạng mục mới để push vào listHangMuc
      //nếu có hạng mục cũ -> chỉ fill mới lại hang_muc
      if (hangMucCu) {
        hangMucMoi = {...hangMucCu};
        hangMucMoi.hang_muc = hangMucGiayTo[i];
      }
      //nếu hang_muc[i] chưa có thì tạo mới
      else {
        hangMucMoi = {
          checked: false,
          expanded: false,
          hang_muc: hangMucGiayTo[i],
          images: [],
          ma: hangMucGiayTo[i].hang_muc,
          ten: hangMucGiayTo[i].ten_hang_muc,
          tenDoiTuong: profileData.ds_doi_tuong.find((itemDoiTuong) => itemDoiTuong.so_id_doi_tuong === hangMucGiayTo[i].so_id_doi_tuong)?.ten_doi_tuong || '',
        };
      }
      listHangMuc.push(hangMucMoi); //push vào mới
    }
    if (profileData.ds_doi_tuong.length > 1) listHangMuc = listHangMuc.sort((a, b) => a.hang_muc.so_id_doi_tuong > b.hang_muc.so_id_doi_tuong);
    setImageDataStep4([...listHangMuc]);
  };

  const initAnhNghiemThu = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('ANH_NGHIEM_THU');
    setAnhNghiemThu([...response]);
  };
  const initAnhThuHoiVatTu = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('ANH_THVT');
    setAnhThuHoiVatTu([...response]);
  };
  const initAnhXacMinhHienTruong = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('XMHT');
    setAnhXacMinhHienTruong([...response]);
  };
  //INIT ẢNH ĐÁNH GIÁ RỦI RO
  const initAnhDanhGiaRuiRo = async () => {
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id_hd: profileData.ho_so.so_id_hd,
        so_id_dt: profileData.ho_so.so_id_dt,
      };
      let response = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_ANH_DA_UPLOAD, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setAnhDanhGiaRuiRo(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //INIT ẢNH CẤP ĐƠN
  const initAnhCapDon = async () => {
    setDialogLoading(true);
    try {
      const params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh_ql,
        so_id: profileData.ho_so.so_id_hd,
        so_id_dt: profileData.ho_so.so_id_dt,
        so_gcn: profileData.ho_so.gcn,
        pm: 'API',
      };
      let response = await PartnerEndpoint.xemAnhCapDon(AxiosConfig.ACTION_CODE.LAY_ANH_CAP_DON, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response?.data_info) setDataAnhCapDon(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  // INIT FILE TÀI LIỆU PDF
  const initTaiLieuPDF = () => {
    try {
      if (listTaiLieuPdf.length > 0) return;
      let listPdf = profileData.hang_muc_chup.filter((item) => item.loai === 'TL' && item.loai_hang_muc === 'PDF');
      listPdf.map((item) => {
        item.extension = '.pdf';
        item.ma_file = item.hang_muc;
      });
      setListTaiLieuPdf([...listPdf]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //nhóm ảnh
  const groupBy = useCallback((xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  }, []);

  //click vào 1 ảnh trong Tài liệu bồi thường
  const onPressOpenImageView = (currentImageData, listAnhXem) => {
    try {
      if (currentImageData.item.extension === '.pdf') {
        NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
          profileData,
          prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
          dataPDF: currentImageData.item,
        });
      } else if (extensionsImage.includes(currentImageData.item.extension)) {
        //nếu đang ở chế độ CHỌN ẢNH ĐẺ PHÂN LOẠI và đang ở ẢNH TOÀN CẢNH, ẢNH TỔN THẤT, ẢNH HỒ SƠ -> thì k xem dc chi tiết ảnh
        if (switchImgView && (xemTaiLieuSelected === 'ANH_TOAN_CANH' || xemTaiLieuSelected === 'ANH_TON_THAT' || xemTaiLieuSelected === 'ANH_HO_SO')) return;
        NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
          currentImageData,
          imagesData: listAnhXem,
        });
      } else if (xemTaiLieuSelected === 'ANH_CAP_DON') {
        NavigationUtil.push(SCREEN_ROUTER_APP.XEM_ANH_CAP_DON, {
          imagesData: dataAnhCapDon,
          currentImageData,
        });
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //NÚT CHỌN TẤT CẢ - BỎ CHỌN
  //type : 0 - Bỏ chọn ; 1 - Chọn tất cả
  const onPressToggleCheckAll = async (type, imageData, viTriHangMuc) => {
    try {
      let newCheckedValue;
      if (type === 0) newCheckedValue = false;
      else if (type === 1) newCheckedValue = true;

      let imageDataTmp = [];
      let setImageData = null;
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        imageDataTmp = imageDataStep3;
        setImageData = setImageDataStep3;
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        imageDataTmp = imageDataStep4;
        setImageData = setImageDataStep4;
      } else if (xemTaiLieuSelected === 'ANH_TOAN_CANH') {
        imageDataTmp = imageDataStep2;
        setImageData = setImageDataStep2;
      }

      //nếu hạng mục đấy chưa tải ảnh + checkAll
      if (imageData.length === 0) {
        if (newCheckedValue) {
          setDialogLoading(true);
          let response = await getTaiLieuBoiThuong(imageDataTmp[viTriHangMuc].ma);
          response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +imageDataTmp[viTriHangMuc].hang_muc.so_id_doi_tuong);
          let imagesTmp = response.map((item) => {
            item.checked = true;
            item.path = item.duong_dan;
            item.name = item.ten_file;
            let nhom = {
              checked: true,
              ma: item.ma_file,
              ten: item.nhom_anh,
              nhom_hang_muc: item.nhom_hang_muc,
            };
            item.nhom = nhom;
            return item;
          });
          setImageData((prevValue) => {
            prevValue[viTriHangMuc].images = imagesTmp;
            prevValue[viTriHangMuc].expanded = !prevValue[viTriHangMuc].expanded;
            return [...prevValue];
          });
        }
      } else {
        for (let i = 0; i < imageDataTmp.length; i++) {
          if (imageDataTmp[i].ma === imageData[0].nhom.ma) {
            imageDataTmp[i].checked = newCheckedValue;
            for (let j = 0; j < imageDataTmp[i].images.length; j++) imageDataTmp[i].images[j].checked = newCheckedValue;
            return setImageData([...imageDataTmp]);
          }
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //1 ảnh được check
  const onPressImageCheck = (imageData) => {
    try {
      // ẢNH TOÀN CẢNH
      if (imageData.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
        let tmp = imageDataStep2.map((item) => {
          if (item.bt === imageData.bt) item.checked = !item.checked;
          return item;
        });
        setImageDataStep2(tmp);
      } else {
        //  ẢNH TỔN THẤT
        let imageDataTmp = imageDataStep3;
        for (let i = 0; i < imageDataTmp.length; i++) {
          let images = imageDataTmp[i].images;
          for (let j = 0; j < images.length; j++)
            if (images[j].bt === imageData.bt) {
              images[j].checked = !images[j].checked;
              return setImageDataStep3([...imageDataTmp]);
            }
        }
        //ẢNH HỒ SƠ, GIẤY TỜ
        imageDataTmp = imageDataStep4;
        for (let i = 0; i < imageDataTmp.length; i++) {
          let images = imageDataTmp[i].images;
          for (let j = 0; j < images.length; j++)
            if (images[j].bt === imageData.bt) {
              images[j].checked = !images[j].checked;
              return setImageDataStep4([...imageDataTmp]);
            }
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //nút PHÂN LOẠI
  const onPressDanhGia = () => {
    try {
      let allImgChecked = [];
      imageDataStep1.map((itemImg) => {
        if (itemImg.checked) allImgChecked.push(itemImg);
      });
      imageDataStep2.map((itemImgStep2) => {
        itemImgStep2.images.map((itemImg) => {
          if (itemImg.checked) allImgChecked.push(itemImg);
        });
      });
      imageDataStep3.map((itemImgStep3) => {
        itemImgStep3.images.map((itemImg) => {
          if (itemImg.checked) allImgChecked.push(itemImg);
        });
      });
      imageDataStep4.map((itemImgStep4) => {
        itemImgStep4.images.map((itemImg) => {
          if (itemImg.checked) allImgChecked.push(itemImg);
        });
      });
      if (allImgChecked.length === 0) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn ảnh đánh giá', 'info');

      NavigationUtil.push(SCREEN_ROUTER_APP.CLASSIFY, {
        imagesClassify: allImgChecked,
        profileData,
        xemTaiLieuSelected,
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  // ẤN VÀO 1 HẠNG MỤC CHƯA ĐÁNH GIÁ -> CHUYỂN MÀN ĐÁNH GIÁ
  const onPressDanhGiaHangMucTheoTen = async (hangMucPhanLoai) => {
    try {
      if (hangMucPhanLoai.images.length > 0)
        NavigationUtil.push(SCREEN_ROUTER_APP.CLASSIFY, {
          imagesClassify: hangMucPhanLoai.images,
          profileData,
          xemTaiLieuSelected,
        });
      else {
        setDialogLoading(true);
        let response = await getTaiLieuBoiThuong(hangMucPhanLoai.ma);
        if (response.length === 0) return FlashMessageHelper.showFlashMessage('Thông báo', 'Hạng mục phân loại không có ảnh');
        response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +hangMucPhanLoai.hang_muc.so_id_doi_tuong);
        let imagesTmp = response.map((item) => {
          item.path = item.duong_dan;
          item.name = item.ten_file;
          let nhom = {
            ma: item.ma_file,
            ten: item.nhom_anh,
            nhom_hang_muc: item.nhom_hang_muc,
          };
          item.nhom = nhom;
          return item;
        });
        setImageDataStep3((preValue) => {
          preValue.map((hangMuc) => {
            if (hangMuc.ma === hangMucPhanLoai.ma) hangMuc.images = imagesTmp;
            return hangMuc;
          });
          return [...preValue];
        });
        NavigationUtil.push(SCREEN_ROUTER_APP.CLASSIFY, {
          imagesClassify: imagesTmp,
          profileData,
          xemTaiLieuSelected,
        });
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // CHECK LỖI THÔNG BÁO ĐỂ CHUYỂN MÀN
  const checkThongBaoLoiDeChuyenMan = (messageBody) => {
    let navScreen = '';
    let navParams = {profileData};
    let txtActionButton = 'Đồng ý';
    let subTitle = '';
    if (messageBody === 'Chưa nhập đầy đủ thông tin bên tham gia giám định') {
      navScreen = SCREEN_ROUTER_APP.JOIN_RESOLVE;
      subTitle = '. Vui lòng bổ sung bên tham gia giám định!';
    }
    //Đổi đối tượng
    else if (messageBody === 'Hồ sơ chưa xác định lại đối tượng!' || messageBody === 'Chưa cập nhật lại đối tượng tổn thất') {
      navScreen = SCREEN_ROUTER_APP.DOI_DOI_TUONG;
      subTitle = '. Vui lòng xác định lại đối tượng!';
      txtActionButton = 'Đổi đối tượng';
      navParams = {profileInfo: profileData?.ho_so};
    }
    //lấy số hs
    else if (messageBody === 'Hồ sơ chưa lấy số!') {
      navScreen = 'LAY_SO_HS';
      subTitle = '. Vui lòng lấy số hồ sơ trước khi kết thúc lần giám định';
    }
    // chuyển màn đánh giá hiện trường
    else if (messageBody === 'Xe có ở hiện trường nhưng chưa thực hiện đánh giá hiện trường' || messageBody === 'Bạn chưa nộp báo cáo hiện trường.') {
      navScreen = SCREEN_ROUTER_APP.DANH_GIA_HIEN_TRUONG;
      subTitle = ' Vui lòng đi đến đánh giá hiện trường!';
      navParams = {profileData, danhGiaHienTruong};
    }
    //chuyển màn báo cáo giám định
    else if (messageBody === 'Bạn chưa thực hiện lập báo cáo giám định' || messageBody === 'Bạn chưa thực hiện trình duyệt báo cáo giám định') {
      navScreen = SCREEN_ROUTER_APP.BAO_CAO_GIAM_DINH;
      subTitle = messageBody === 'Bạn chưa thực hiện lập báo cáo giám định' ? '. Vui lòng đi đến lập báo cáo giám định!' : '. Vui lòng đi đến trình duyệt báo cáo giám định!';
    }
    //Chuyển màn đánh giá hiện trường
    else if (messageBody === 'Xe có ở hiện trường nhưng chưa đánh giá hiện trường') {
      navScreen = SCREEN_ROUTER_APP.DANH_GIA_HIEN_TRUONG;
      subTitle = '. Vui lòng đánh giá hiện trường để chụp ảnh Giám định chi tiết';
      navParams = {profileData, danhGiaHienTruong};
    }
    //chuyển màn đặt lịch giám định
    else if (messageBody === 'Bạn chưa đặt lịch giám định với khách hàng.') {
      navScreen = SCREEN_ROUTER_APP.ASSESSMENT_SCHEDULE_PROFILE;
      subTitle = ' Vui lòng đi đến đặt lịch giám định!';
    }
    //chuyển màn nhập ước
    else if (messageBody === 'Ước tổn thất phải lớn hơn 0' || messageBody.startsWith('[UOC_NOTFOUND]')) {
      navScreen = SCREEN_ROUTER_APP.NHAP_UOC_TON_THAT_CHO_HS_CHUA_LAY_SO;
      subTitle = '. Vui lòng cập nhật lại ước tổn thất trước khi lấy số hồ sơ!';
    }
    //chuyển màn ĐỔI THÔNG TIN BIỂN SỐ XE
    else if (messageBody === 'Vui lòng cập nhật thông tin biển xe trước khi lấy số hồ sơ') {
      navScreen = SCREEN_ROUTER_APP.DOI_THONG_TIN_BSX;
      navParams = {profileInfo: profileData?.ho_so};
    }
    //chuyển màn ĐỔI THÔNG TIN BIỂN SỐ XE
    else if (messageBody === 'Chưa nhập vụ tổn thất hoặc chưa có vụ tổn thất nào thuộc phạm vi.') {
      navScreen = SCREEN_ROUTER_APP.DS_CAC_VU_TON_THAT;
      navParams = {profileData: {ho_so: profileData?.ho_so}};
      txtActionButton = 'Kiểm tra';
    }
    if (navScreen !== '') {
      setTimeout(() => {
        Alert.alert('Thông báo', messageBody + subTitle, [
          {
            text: 'Để sau',
            style: 'destructive',
          },
          {
            text: txtActionButton,
            onPress: () => {
              if (navScreen !== 'LAY_SO_HS') NavigationUtil.push(navScreen, navParams);
              else laySoHoSo();
            },
          },
        ]);
      }, 200);
    }
    //trường hợp có hạng mục chưa đánh giá mức độ tổn thất
    else if (messageBody === 'Tồn tại hạng mục chưa đánh giá tổn thất (mức độ, thay thế/sửa chữa, thu hồi)') {
      Alert.alert('Thông báo', messageBody, [
        {
          text: 'Để sau',
          style: 'destructive',
        },
        {
          text: 'Xem hạng mục chưa đánh giá',
          onPress: () => tabViewRef.current?.goToPage(1),
        },
      ]);
    } else Alert.alert('Thông báo', messageBody);
  };

  //nút CHỤP ẢNH GIÁM ĐỊNH
  const onPressTakePicture = (index, loaiAnh) => {
    try {
      let paramsRoute = {
        profileData,
        doiTuongDuocChupAnh: doiTuongDuocChon,
        danhGiaHienTruong,
      };
      if (loaiAnh === 'ANH_HO_SO' || loaiAnh === 'ANH_NGHIEM_THU' || loaiAnh === 'ANH_THVT' || loaiAnh === 'XMHT') paramsRoute.loaiAnh = 'ANH_HO_SO';
      if (loaiAnh === 'ANH_HO_SO' || loaiAnh === 'ANH_NGHIEM_THU' || loaiAnh === 'ANH_THVT' || loaiAnh === 'XMHT') paramsRoute.hangMucAnh = loaiAnh;
      if (loaiAnh === 'ANH_HIEN_TRUONG') paramsRoute.loaiAnh = loaiAnh;
      if (loaiAnh === 'ANH_TON_THAT') paramsRoute.loaiAnh = loaiAnh;
      if (index === chupAnhIndex.CHUP_ANH_GIAM_DINH) NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_HIEN_TRUONG_O_TO_OFFLINE, paramsRoute);
      else if (index === chupAnhIndex.CHUP_ANH_CHI_TIET) NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_CHI_TIET_O_TO_OFFLINE, paramsRoute);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressBoSungAnh = (type) => {
    try {
      if (type === 0) {
        if (profileData.ds_doi_tuong.length === 1) setDoiTuongDuocChon({...profileData.ds_doi_tuong[0]});
        else setToggleModalChonDoiTuongHoSo(true);
      } else {
        if (profileData.ds_doi_tuong.length === 1) setDoiTuongDuocChon({...profileData.ds_doi_tuong[0]});
        else setToggleModalChonDoiTuongHoSo(true);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressChonDoiTuongChupAnh = () => {
    if (profileData.ho_so.nv_ma === 'BB' && profileData.ds_doi_tuong && profileData.ds_doi_tuong.length === 1) setToggleModalChonDoiTuong(true);
    else if (profileData.ds_doi_tuon && profileData.ds_doi_tuong.length === 1) setDoiTuongDuocChon({...profileData.ds_doi_tuong[0]});
    else setToggleModalChonDoiTuong(true);
  };

  const onPressChupAnhChiTiet = async (loaiAnh) => {
    onPressTakePicture(chupAnhIndex.CHUP_ANH_CHI_TIET, loaiAnh);
    // try {
    //   setDialogLoading(true);
    //   let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHECK_GIAM_DINH_CHI_TIET, {so_id: profileData.ho_so.so_id});
    //   setDialogLoading(false);
    //   if (!response || !response.state_info || response.state_info.status !== 'OK') return checkThongBaoLoiDeChuyenMan(response.state_info.message_body);
    //   onPressTakePicture(chupAnhIndex.CHUP_ANH_CHI_TIET, loaiAnh);
    // } catch (error) {
    //   setDialogLoading(false);
    //   Alert.alert('Thông báo', error.message);
    // }
  };

  //CHỌN LOẠI GIÁM ĐỊNH HIỆN TRƯỜNG / GIÁM ĐỊNH CHI TIẾT
  const onPressChonLoaiGiamDinh = async (index, loaiAnh = '') => {
    if (index === 2) return;
    //nếu là CHỤP ẢNH CHI TIẾT
    else if (index === chupAnhIndex.CHUP_ANH_CHI_TIET) onPressChupAnhChiTiet(loaiAnh);
    //chụp ảnh HIỆN TRƯỜNG
    else if (index === chupAnhIndex.CHUP_ANH_GIAM_DINH) {
      onPressTakePicture(chupAnhIndex.CHUP_ANH_GIAM_DINH, 'ANH_HIEN_TRUONG');
      // try {
      //   setDialogLoading(true);
      //   let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.BAT_DAU_CHUP_ANH_HIEN_TRUONG, {so_id: profileData.ho_so.so_id, ma_doi_tac: profileData.ho_so.ma_doi_tac});
      //   setDialogLoading(false);
      //   if (!response || !response.state_info || response.state_info.status !== 'OK') return checkThongBaoLoiDeChuyenMan(response.state_info.message_body);
      //   onPressTakePicture(chupAnhIndex.CHUP_ANH_GIAM_DINH);
      // } catch (error) {
      //   setDialogLoading(false);
      //   Alert.alert('Thông báo', error.message);
      // }
    }
    setToggleModalChonDoiTuong(false);
  };

  //GỠ HUỶ HỒ SƠ
  const onPressGoHuyHoSo = () => {
    Alert.alert('Gỡ huỷ hồ sơ', 'Bạn có chắc chắn muốn gỡ huỷ hồ sơ không?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Gỡ huỷ',
        onPress: async () => {
          try {
            const params = {
              so_id: profileData?.ho_so?.so_id, //id hồ sơ
              ma_doi_tac: profileData?.ho_so.ma_doi_tac,
            };
            setDialogLoading(true);
            let response = await CarClaimEndpoint.goHuyHoSoGiamDinhOTo(AxiosConfig.ACTION_CODE.GO_HUY_HO_SO_GIAM_DINH_OTO, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Gỡ huỷ hồ sơ giám định thành công!', 'success');
            NavigationUtil.pop();
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const openActionSheetChupAnh = () => actionSheetChupAnhRef?.show();

  // ICON XOÁ ẢNH
  const onPressRemoveAnh = (imageDataDeleted, indexRemove) => {
    if (prevScreenIsHoSoDiaBan) return;
    Alert.alert('Xoá ảnh', 'Bạn có chắc chắn muốn xoá ảnh này?', [
      {
        text: 'Huỷ',
        style: 'destructive',
      },
      {text: 'Xoá', onPress: () => onRemoveAnh(imageDataDeleted, indexRemove)},
    ]);
  };
  //XOÁ ẢNH
  const onRemoveAnh = async (imageDataDeleted, indexRemove) => {
    setDialogLoading(true);
    try {
      let bt = [];
      bt.push(imageDataDeleted.bt);
      let params = {
        so_id: imageDataDeleted?.so_id,
        bt,
        nv: 'XE',
        pm: 'GD',
        so_id_dt: 0,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_ANH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (xemTaiLieuSelected === 'ANH_HIEN_TRUONG') initAnhHienTruong();
      else if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        let indexHangMucAnh = imageDataStep3.findIndex((item) => item.ma === imageDataDeleted.ma_file);
        if (indexHangMucAnh > -1) {
          let imageDataStep3Tmp = [...imageDataStep3];
          imageDataStep3Tmp[indexHangMucAnh].images.splice(indexRemove, 1);
          if (imageDataStep3Tmp[indexHangMucAnh].images.length === 0) {
            imageDataStep3Tmp.splice(indexHangMucAnh, 1);
          }
          setImageDataStep3([...imageDataStep3Tmp]);
        }
      } else if (xemTaiLieuSelected === 'ANH_TOAN_CANH') initAnhToanCanh();
      else if (xemTaiLieuSelected === 'ANH_CAP_DON') initAnhCapDon();
      else if (xemTaiLieuSelected === 'ANH_NGHIEM_THU') initAnhNghiemThu();
      else if (xemTaiLieuSelected === 'ANH_THVT') initAnhThuHoiVatTu();
      else if (xemTaiLieuSelected === 'XMHT') initAnhXacMinhHienTruong();
      else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        let indexHangMucAnh = imageDataStep4.findIndex((item) => item.ma === imageDataDeleted.ma_file);
        if (indexHangMucAnh > -1) {
          let imageDataStep4Tmp = [...imageDataStep4];
          imageDataStep4Tmp[indexHangMucAnh].images.splice(indexRemove, 1);
          // nếu hết sạch ảnh thì gọi API lấy lại chi tiết hồ sơ để check lại hạng mục
          if (imageDataStep4Tmp[indexHangMucAnh].images.length === 0) {
            imageDataStep4Tmp.splice(indexHangMucAnh, 1);
          }
          setImageDataStep4([...imageDataStep4Tmp]);
        }
      }
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  //ĐÓNG / MỞ 1 HẠNG MỤC
  const onPressToggleExpandHangMuc = async (index) => {
    try {
      let imageDataTmp = [];
      let setImageData = null;
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        imageDataTmp = imageDataStep3;
        setImageData = setImageDataStep3;
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        imageDataTmp = imageDataStep4;
        setImageData = setImageDataStep4;
      }
      //nếu có ảnh thì expand ra luôn nhưng vẫn gọi lại API để lấy ảnh
      let daExpand = false; //đã expand rồi thì bên dưới k cần gọi expand nữa
      let newValueExpand = null;
      if (imageDataTmp[index].images.length > 0) {
        daExpand = true;
        setImageData((prevValue) => {
          prevValue[index].expanded = !prevValue[index].expanded; //cập nhật giá trị expand mới
          newValueExpand = prevValue[index].expanded;
          return [...prevValue];
        });
      }
      //nếu là thu lại thì k gọi API
      if (newValueExpand === false) return;
      refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(index);
      let response = await getTaiLieuBoiThuong(imageDataTmp[index].ma);
      response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +imageDataTmp[index].hang_muc.so_id_doi_tuong);
      refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
      let imagesTmp = response.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
          nhom_hang_muc: item.nhom_hang_muc,
        };
        item.nhom = nhom;
        return item;
      });
      setImageData((prevValue) => {
        prevValue[index].images = imagesTmp;
        if (!daExpand) prevValue[index].expanded = !prevValue[index].expanded; //bên trên chưa expand thì mới cần expand ra
        return [...prevValue];
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressExpandAllHangMuc = async (expandAllHangMuc) => {
    try {
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        let imageDataStep3Tmp = [...imageDataStep3];
        for (let i = 0; i < imageDataStep3Tmp.length; i++) {
          let hangMucAnh = imageDataStep3Tmp[i];
          if (expandAllHangMuc === true) {
            if (hangMucAnh.images.length === 0) {
              refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(i);
              let response = await getTaiLieuBoiThuong(hangMucAnh.ma);
              response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +hangMucAnh.hang_muc.so_id_doi_tuong);
              let imagesTmp = response.map((item) => {
                item.checked = false;
                item.path = item.duong_dan;
                item.name = item.ten_file;
                let nhom = {
                  checked: false,
                  ma: item.ma_file,
                  ten: item.nhom_anh,
                  nhom_hang_muc: item.nhom_hang_muc,
                };
                item.nhom = nhom;
                return item;
              });
              imageDataStep3Tmp[i].images = imagesTmp;
            }
            imageDataStep3Tmp[i].expanded = true;
          } else if (expandAllHangMuc === false) imageDataStep3Tmp[i].expanded = false;
        }
        refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
        setImageDataStep3([...imageDataStep3Tmp]);
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        let imageDataStep4Tmp = [...imageDataStep4];
        for (let i = 0; i < imageDataStep4Tmp.length; i++) {
          let hangMucAnh = imageDataStep4Tmp[i];
          if (expandAllHangMuc === true) {
            if (hangMucAnh.images.length === 0) {
              refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(i);
              let response = await getTaiLieuBoiThuong(hangMucAnh.ma);
              response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +hangMucAnh.hang_muc.so_id_doi_tuong);
              let imagesTmp = response.map((item) => {
                item.checked = false;
                item.path = item.duong_dan;
                item.name = item.ten_file;
                let nhom = {
                  checked: false,
                  ma: item.ma_file,
                  ten: item.nhom_anh,
                  nhom_hang_muc: item.nhom_hang_muc,
                };
                item.nhom = nhom;
                return item;
              });
              imageDataStep4Tmp[i].images = imagesTmp;
            }
            imageDataStep4Tmp[i].expanded = true;
          } else if (expandAllHangMuc === false) imageDataStep4Tmp[i].expanded = false;
        }
        refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
        setImageDataStep4([...imageDataStep4Tmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const themDoiTuong = async (dataDoiTuong) => {
    if (!profileData.mau_in)
      return Alert.alert('Tải dữ liệu hồ sơ', 'Vui lòng tải dữ liệu hồ sơ từ hệ thống để tiếp tục', [
        {
          text: 'Để sau',
        },
        {text: 'Tải', onPress: () => getChiTietHoSoGiamDinhOto()},
      ]);
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      so_id: profileData.ho_so.so_id,
      so_id_doi_tuong: '',
      nhom: dataDoiTuong.nhom,
      ten_doi_tuong: dataDoiTuong.ten_doi_tuong,
      loai: dataDoiTuong.loai,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LUU_DOI_TUONG_TON_THAT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.out_value.so_id_doi_tuong) {
        let soIdDoiTuongMoi = response.out_value.so_id_doi_tuong;
        dataDoiTuong.so_id_doi_tuong = soIdDoiTuongMoi;
        await luuDoiTuongGiamDinh(dataDoiTuong); //cập nhật so_id_doi_tuong từ server vào đối tượng lưu ở local
        let chiTietHoSo = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.so_id);
        //cập nhật so_id_doi_tuong từ server vào data giám định ở local
        if (chiTietHoSo.dataAnhGiamDinh) {
          let indexDataGiamDinhCuaDoiTuong = chiTietHoSo.dataAnhGiamDinh.findIndex((itemDataGiamDinh) => itemDataGiamDinh.soIdDoiTuongLuuTrongMay === dataDoiTuong.soIdDoiTuongLuuTrongMay);
          if (indexDataGiamDinhCuaDoiTuong > -1) {
            chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].soIdDoiTuong = soIdDoiTuongMoi;
            if (chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhHienTruong) chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhHienTruong.soIdDoiTuong = soIdDoiTuongMoi;
            if (chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhTaiLieu) chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhTaiLieu.soIdDoiTuong = soIdDoiTuongMoi;
            if (chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhToanCanh) chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhToanCanh.soIdDoiTuong = soIdDoiTuongMoi;
            if (chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhTonThat) chiTietHoSo.dataAnhGiamDinh[indexDataGiamDinhCuaDoiTuong].anhTonThat.soIdDoiTuong = soIdDoiTuongMoi;
          }
        }
        await luuListHoSoDangGiamDinh([chiTietHoSo]);
      }
      FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm đối tượng tổn thất thành công', 'success');
      getChiTietHoSoGiamDinhOto();
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  /** RENDER */
  const renderActionSheetGiamDinh = () => (
    <ActionSheet
      cancelButtonIndex={2}
      destructiveButtonIndex={2}
      title={'Chọn loại giám định'}
      ref={(o) => (actionSheetChupAnhRef = o)}
      onPress={(index) => onPressChonLoaiGiamDinh(index)}
      options={['Giám định Hiện trường', 'Giám định Chi tiết', 'Để sau']}
    />
  );

  //render ra nút giám định
  const renderNutGiamDinh = () => {
    if (route.params.prevScreen === SCREEN_ROUTER_APP.HO_SO_DIA_BAN) return;
    let hienThiNutDanhGia = false;
    let hienThiNutChupAnhHienTruong = false;
    let hienThiNutChupAnhHoSo = false;
    let cauHinhPhanLoai;
    if (!profileData || !profileData.ho_so) return;
    if (profileData.cau_hinh) cauHinhPhanLoai = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.PHAN_LOAI_DANH_GIA, profileData.cau_hinh);
    if (btnTabActive == 1 && +profileData.ho_so.hien_thi_button != 1 && cauHinhPhanLoai && cauHinhPhanLoai.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO && xemTaiLieuSelected === 'ANH_TON_THAT')
      hienThiNutDanhGia = true;
    else if (btnTabActive == 1 && profileData.ho_so.hien_truong === 'D' && xemTaiLieuSelected === 'ANH_HIEN_TRUONG' && +profileData.ho_so.hien_thi_button != 1) hienThiNutChupAnhHienTruong = true;
    else if (
      btnTabActive == 1 &&
      (xemTaiLieuSelected === 'ANH_HO_SO' || xemTaiLieuSelected === 'ANH_NGHIEM_THU' || xemTaiLieuSelected === 'ANH_THVT' || xemTaiLieuSelected === 'XMHT') &&
      +profileData.ho_so.hien_thi_button != 1
    )
      hienThiNutChupAnhHoSo = true;

    //nút phân loại
    // if (hienThiNutDanhGia)
    //   return (
    //     <View style={[styles.footerView, styles.doubleBtn]}>
    //       <ButtonLinear title="Bổ sung ảnh tổn thất" onPress={() => onPressBoSungAnh(2)} linearStyle={styles.btnStyles} />
    //     </View>
    //   );
    // if (hienThiNutChupAnhHienTruong) return <ButtonLinear title="Bổ sung ảnh hiện trường" onPress={() => onPressBoSungAnh(0)} linearStyle={styles.btnStyles} />;
    // if (hienThiNutChupAnhHoSo)
    //   return (
    //     <View style={[styles.footerView, styles.doubleBtn]}>
    //       <ButtonLinear title={xemTaiLieuSelected === 'XMHT' ? 'Bổ sung hình ảnh' : 'Bổ sung giấy tờ'} onPress={() => onPressBoSungAnh(1)} linearStyle={styles.btnStyles} />
    //     </View>
    //   );
    if (btnTabActive == 0) return <ButtonLinear title="Chụp ảnh giám định" onPress={onPressChonDoiTuongChupAnh} linearStyle={styles.btnStyles} />;
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Hồ sơ ô tô OFFLINE"
      renderView={
        <View style={styles.container}>
          <ScrollableTabView ref={tabViewRef} style={styles.centerView} initialPage={0} onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))} renderTabBar={() => <CustomTabBar />}>
            <ScrollView tabLabel="Thông tin hồ sơ" scrollEnabled={true} ref={scrollViewRef} showsVerticalScrollIndicator={false}>
              <TabThongTinHoSoOTo
                onPressTuChoiBoiThuong={() => {
                  refModalTuChoiBoiThuong.current.show();
                }}
                profileData={profileData}
                toggleModalChonDoiTuong={toggleModalChonDoiTuong}
                setToggleModalChonDoiTuong={setToggleModalChonDoiTuong}
                setDoiTuongDuocChon={setDoiTuongDuocChon}
                danhGiaHienTruong={danhGiaHienTruong}
                actionSheetMauInData={actionSheetMauInData}
                actionSheetMauInXacNhanBienBanData={actionSheetMauInXacNhanBienBanData}
                // setActionSheetXacNhanBienBan={setActionSheetXacNhanBienBan}
                listCanhBaoHoSo={listCanhBaoHoSo}
                onPressHuyHoSo={() => refModalHuyHoSo.current.show()}
                onPressGoHuyHoSo={onPressGoHuyHoSo}
                openActionSheetChuyenHienTruongXe={() => actionSheetChuyenThongTinHienTruongRef.show()}
                getChiTietHoSoGiamDinhOto={getChiTietHoSoGiamDinhOto}
                themDoiTuong={themDoiTuong}
              />
            </ScrollView>
            <View tabLabel="Tài liệu bồi thường" style={styles.centerView}>
              <TabTaiLieuBoiThuongOTo
                ref={refTabTaiLieuBoiThuongOTo}
                onPressRemoveAnh={onPressRemoveAnh}
                profileData={profileData}
                imageDataStep1={imageDataStep1}
                imageDataStep2={imageDataStep2}
                imageDataStep3={imageDataStep3}
                setImageDataStep3={setImageDataStep3}
                imageDataStep4={imageDataStep4}
                anhDanhGiaRuiRo={anhDanhGiaRuiRo}
                anhNghiemThu={anhNghiemThu}
                anhThuHoiVatTu={anhThuHoiVatTu}
                listTaiLieuPdf={listTaiLieuPdf}
                anhXacMinhHienTruong={anhXacMinhHienTruong}
                onPressOpenImageView={onPressOpenImageView}
                // xử lý CHỌN ẢNH
                switchImgView={switchImgView}
                setSwitchImgView={() => setSwitchImgView(!switchImgView)}
                onPressToggleExpandHangMuc={onPressToggleExpandHangMuc}
                onPressDanhGiaHangMucTheoTen={onPressDanhGiaHangMucTheoTen}
                // xử lý hiển thị Thông tin phân loại
                onPressToggleCheckAll={onPressToggleCheckAll}
                onPressImageCheck={onPressImageCheck}
                // dropdown ảnh tài liệu
                setXemTaiLieuSelected={setXemTaiLieuSelected}
                xemTaiLieuSelected={xemTaiLieuSelected}
                //modal chọn đối tượng
                toggleModalChonDoiTuongHoSo={toggleModalChonDoiTuongHoSo}
                setToggleModalChonDoiTuongHoSo={setToggleModalChonDoiTuongHoSo}
                setDoiTuongDuocChon={setDoiTuongDuocChon}
                dataAnhCapDon={dataAnhCapDon}
                onPressExpandAllHangMuc={onPressExpandAllHangMuc}
              />
            </View>
          </ScrollableTabView>
          {renderActionSheetGiamDinh()}
          <View style={styles.footerView}>{profileData && renderNutGiamDinh()}</View>
        </View>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  user: state.user.data,
});
const mapDispatchToProps = {};
const ChiTietHoSoOToOfflineConnect = connect(mapStateToProps, mapDispatchToProps)(ChiTietHoSoOToOfflineComponent);
export const ChiTietHoSoOToOffline = memo(ChiTietHoSoOToOfflineConnect, isEqual);
