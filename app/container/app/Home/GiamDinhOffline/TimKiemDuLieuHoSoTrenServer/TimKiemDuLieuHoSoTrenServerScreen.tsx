import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {AsyncStorageProvider, logErrorTryCatch} from '@app/utils';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Empty, Icon, ProfileItem, ScreenComponent, SearchBar} from '@component';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Keyboard, View} from 'react-native';
import styles from './TimKiemDuLieuHoSoTrenServerStyles';

const TimKiemDuLieuHoSoTrenServerScreenComponent = ({route}) => {
  console.log('TimKiemDuLieuHoSoTrenServerScreenComponent');
  const {profileData} = route.params;
  const [listHoSo, setListHoSo] = useState([]);
  const [searchInput, setSearchInput] = useState(profileData.ho_so?.doi_tuong || '');
  const [dialogLoading, setDialogLoading] = useState(false);
  const [indexSelected, setIndexSelected] = useState(null);

  const onPressXacNhanHoSo = async () => {
    if (!indexSelected) {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tìm kiếm và chọn hồ sơ cần đồng bộ');
      return;
    }
    try {
      setDialogLoading(true);
      //chi tiết hồ sơ local
      let chiTietHoSoLocal = await AsyncStorageProvider.getHoSoDangGiamDinhTheoId(profileData.ho_so.soIdHoSoLuuTrongMay);
      chiTietHoSoLocal = {...chiTietHoSoLocal, ...listHoSo[indexSelected]};
      let soIdHoSoServer = listHoSo[indexSelected].so_id;
      let paramsProfileDetail = {
        ma_doi_tac: listHoSo[indexSelected].ma_doi_tac,
        so_id: listHoSo[indexSelected].so_id,
      };
      //lấy ra chi tiết hồ sơ
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.PROFILE_DATA, paramsProfileDetail);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      chiTietHoSoLocal.chi_tiet_ho_so = response.data_info;
      if (chiTietHoSoLocal.dataAnhGiamDinh) {
        chiTietHoSoLocal.dataAnhGiamDinh = chiTietHoSoLocal.dataAnhGiamDinh.map((itemAnhGiamDinh) => {
          if (itemAnhGiamDinh.anhHienTruong) itemAnhGiamDinh.anhHienTruong.soId = soIdHoSoServer;
          if (itemAnhGiamDinh.anhToanCanh) itemAnhGiamDinh.anhToanCanh.soId = soIdHoSoServer;
          if (itemAnhGiamDinh.anhTaiLieu) itemAnhGiamDinh.anhTaiLieu.soId = soIdHoSoServer;
          if (itemAnhGiamDinh.anhTonThat) itemAnhGiamDinh.anhTonThat.soId = soIdHoSoServer;
          return itemAnhGiamDinh;
        });
      }
      if (chiTietHoSoLocal.listDoiTuong) {
        chiTietHoSoLocal.listDoiTuong = chiTietHoSoLocal.listDoiTuong.map((itemDoiTuong) => {
          itemDoiTuong.so_id = soIdHoSoServer;
          return itemDoiTuong;
        });
      }
      await AsyncStorageProvider.luuListHoSoDangGiamDinh([chiTietHoSoLocal]);
      // FlashMessageHelper.showFlashMessage('Thông báo', '', 'success');
      NavigationUtil.pop();
      setDialogLoading(false);
    } catch (error) {
      setDialogLoading(false);
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  //lấy danh sách hồ sơ giám định
  const getListHoSoGiamDinh = async (type, trang = 1, so_dong = 100, bien_xe = '') => {
    let params = {
      loai: type,
      so_dong: so_dong,
      trang: trang,
      bien_xe: bien_xe,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DANH_MUC_THEO_LOAI_HS, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info.filter((item) => item.loai === type);
      if (arrData.length === 1) setIndexSelected(0);
      else setIndexSelected(null);
      setListHoSo(arrData);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeTextSearch = (value) => setSearchInput(value);

  //search hồ sơ giám định
  const onPressSearch = () => {
    Keyboard.dismiss();
    if (!searchInput.trim()) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập từ khoá', 'info');
    getListHoSoGiamDinh('DANG_GIAM_DINH', 1, 100, searchInput);
  };

  /**RENDER  */

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Đồng bộ dữ liệu hồ sơ"
      renderView={
        <View style={styles.container}>
          <SearchBar
            placeholder="Biển số xe, tên khách hàng, số hồ sơ"
            onTextChange={(value) => onChangeTextSearch(value)}
            onPressSearch={onPressSearch}
            value={searchInput}
            onSubmitEditing={onPressSearch}
          />
          <FlatList
            data={listHoSo || []}
            renderItem={(data) => (
              <View>
                <ProfileItem data={data} dataType="HS_CN" onPressItem={() => setIndexSelected(data.index)} />
                {data.index === indexSelected && (
                  <View style={styles.actionView}>
                    <View style={styles.removeIconView}>
                      <Icon.AntDesign name="checkcircle" size={18} color={colors.GREEN} />
                    </View>
                  </View>
                )}
              </View>
            )}
            keyExtractor={(item, index) => item.so_id + index.toString()}
            ListEmptyComponent={<Empty />}
          />
        </View>
      }
      footer={<ButtonLinear title="Xác nhận" onPress={onPressXacNhanHoSo} />}
    />
  );
};

export const TimKiemDuLieuHoSoTrenServerScreen = memo(TimKiemDuLieuHoSoTrenServerScreenComponent, isEqual);
