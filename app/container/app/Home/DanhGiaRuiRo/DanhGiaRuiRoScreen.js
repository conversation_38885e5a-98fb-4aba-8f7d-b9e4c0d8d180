import R from '@app/assets/R';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, ScreenComponent, SearchBar, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, RefreshControl, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';
import {useDebouncedCallback} from 'use-debounce';
import {ModalQuetMaQR} from './Components/ModalQuetMaQR';
import styles from './DanhGiaRuiRoStyles';
const TRANG_THAI_DANH_GIA = {
  ['DXN']: 'Đã xác nhận đánh giá',
  ['C']: 'Chưa đánh giá',
  ['D']: 'Đang đánh giá',
};

const DanhGiaRuiRoScreenComponent = (props) => {
  console.log('DanhGiaRuiRoScreenComponent');
  const {route, navigation} = props;
  const keySearch = route?.params?.keySearch;
  const [refreshing, setRefreshing] = useState(false);
  const [isReviewed, setIsReviewed] = useState(false);

  const [data, setData] = useState([]);
  const [hangMucAnh, setHangMucAnh] = useState([]);
  const [hopDong, setHopDong] = useState({});
  const [selectedItem, setSelectedItem] = useState(null);
  const [indexSelected, setIndexSelected] = useState(-1);
  const [textSearch, setTextSearch] = useState('');

  let refModalQuetMaQR = useRef(null);

  useEffect(() => {
    getDataSearch(textSearch);
  }, [textSearch]);

  useEffect(() => {
    getDataSearch(keySearch);
  }, [route.params]);

  const onPressBack = () => {
    NavigationUtil.pop();
  };

  const getData = async (dataInfo) => {
    if (dataInfo) setTextSearch('');

    setRefreshing(true);
    try {
      let params = {
        so_id: dataInfo.so_id,
        ma_doi_tac: dataInfo.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_GCN_BY_QR_CODE, params);

      setRefreshing(false);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      // console.log('response.data_info', response.data_info);
      setSelectedItem(response.data_info.length === 1 ? response.data_info[0] : null);
      setIndexSelected(response.data_info.length === 1 ? 1 : -1);
      setData(response.data_info.gcn);
      setHangMucAnh(response.data_info.hinh_anh);
      setHopDong(response.data_info.hd);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDataSearch = async (tim = '') => {
    if (tim === '') return setData([]);
    setRefreshing(true);
    let params = {
      tim: tim,
      ngay_xr: moment().format('YYYYMMDD'),
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_GCN_BY_SEARCH, params);
      // console.log('getDataSearch', response);
      setRefreshing(false);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      setSelectedItem(response.data_info.length === 1 ? response.data_info[0] : null);
      setIndexSelected(response.data_info.length === 1 ? 1 : -1);
      setData(response.data_info);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressSelectItem = (item, idx) => {
    setSelectedItem(item);
    setIndexSelected(idx);
    if (item.danh_gia === 'DXN') setIsReviewed(true);
    else setIsReviewed(false);
  };

  const debounced = useDebouncedCallback((value) => {
    setTextSearch(value);
  }, 500);

  /* RENDER  */
  const renderProfileItem = ({item, index}) => {
    const lastItem = data.length - 1;
    const linearBackground = index !== indexSelected ? [colors.WHITE, colors.WHITE] : [colors.BLUE_LIGHT, colors.BLUE_LIGHT];
    const borderColor = index !== indexSelected ? colors.GRAY : colors.PRIMARY;
    return (
      <TouchableOpacity style={{marginBottom: lastItem === index ? 60 : 0}} onPress={() => onPressSelectItem(item, index)}>
        <LinearGradient colors={linearBackground} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.profileItemView, {borderColor: borderColor}]}>
          <View style={styles.profileItemCenterView}>
            <View style={styles.rowView}>
              <Text style={styles.profileTxtThoiGian}>
                Biển số: <Text style={styles.txtValue}>{item.bien_xe}</Text>
              </Text>
              {item.danh_gia === 'C' ? <Text style={{color: colors.RED1}}>{TRANG_THAI_DANH_GIA[item.danh_gia]}</Text> : <Text style={{color: colors.GREEN}}>{TRANG_THAI_DANH_GIA[item.danh_gia]}</Text>}
            </View>
            <View style={styles.rowView}>
              <Text style={styles.profileTxtThoiGian}>
                Số hợp đồng: <Text style={styles.detail}>{item.so_hd}</Text>
              </Text>
            </View>
            <View style={styles.rowView}>
              <Text style={styles.profileTxtThoiGian}>
                Số GCN: <Text style={styles.detail}>{item.gcn}</Text>
              </Text>
            </View>
            <View style={styles.rowView}>
              <Text style={styles.profileTxtThoiGian}>
                Đơn vị cấp đơn: <Text style={styles.detail}>{item.ten_dvi_cap_don}</Text>
              </Text>
            </View>

            <View style={styles.rowView}>
              <Text style={styles.profileTxtThoiGian}>
                Ngày hiệu lực: <Text style={styles.detail}>{item.hieu_luc}</Text>
              </Text>
            </View>
            <View style={styles.rowView}>
              <Text style={styles.profileTxtThoiGian}>
                Tên chủ xe: <Text style={styles.detail} children={item.ten} />
              </Text>
            </View>
            <View style={styles.rowView}>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Hãng xe: <Text style={styles.detail}>{item.hang_xe}</Text>
                </Text>
              </View>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Hiệu xe: <Text style={styles.detail}>{item.hieu_xe}</Text>
                </Text>
              </View>
            </View>

            <View style={styles.rowView}>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Số khung: <Text style={styles.detail}>{item.so_khung}</Text>
                </Text>
              </View>
            </View>
            <View style={styles.rowView}>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Số máy: <Text style={styles.detail}>{item.so_may}</Text>
                </Text>
              </View>
            </View>
            <View style={styles.rowView}>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Số chỗ: <Text style={styles.detail}>{item.so_cho_hthi}</Text>
                </Text>
              </View>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Trọng tải: <Text style={styles.detail}>{item.trong_tai_hthi}</Text>
                </Text>
              </View>
            </View>
            <View style={styles.rowView}>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Năm sản xuất: <Text style={styles.detail}>{item.nam_sx}</Text>
                </Text>
              </View>
              <View style={styles.profileTimeView}>
                <Text style={styles.profileTxtThoiGian}>
                  Giá trị xe: <NumericFormat value={item.gia_tri} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.detail}>{value}</Text>} />
                </Text>
              </View>
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  const rendeHeader = () => {
    return (
      <View style={styles.renderHeader}>
        <View flex={1}>
          <SearchBar placeholder="Tìm kiếm biển số xe, tên chủ xe" onTextChange={debounced} onPressSearch={() => getDataSearch(textSearch || keySearch)} />
        </View>
      </View>
    );
  };

  // footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {isReviewed ? (
          <ButtonLinear title="Xem chi tiết" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.CHUP_ANH_DANH_GIA, {hangMucAnh: hangMucAnh, hopDong: hopDong, gcnChiTiet: selectedItem})} />
        ) : (
          <ButtonLinear
            disabled={!selectedItem}
            linearColors={!selectedItem ? [colors.GRAY, colors.GRAY] : [colors.PRIMARY_08, colors.PRIMARY]}
            textStyle={{color: !selectedItem ? colors.BLACK_03 : colors.WHITE}}
            title="Chụp ảnh / Quay video ĐGRR"
            onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.CHUP_ANH_DANH_GIA, {hangMucAnh: hangMucAnh, hopDong: hopDong, gcnChiTiet: selectedItem})}
          />
        )}
      </View>
    );
  };
  const renderRightHeader = () => {
    return (
      <TouchableOpacity onPress={() => refModalQuetMaQR.current.show()} style={styles.btnLuuView}>
        <Icon.MaterialCommunityIcons name="qrcode-scan" size={24} color={colors.WHITE} style={{alignItems: 'center'}} />
      </TouchableOpacity>
    );
  };
  return (
    <ScreenComponent
      renderRightHeader={renderRightHeader}
      headerBack
      dialogLoading={refreshing}
      headerTitle="Đánh giá rủi ro"
      renderView={
        <View style={styles.container}>
          {rendeHeader()}
          <FlatList
            data={data}
            removeClippedSubviews={true}
            renderItem={renderProfileItem}
            keyExtractor={(item, index) => index.toString()}
            onEndReachedThreshold={0.5}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={false} />}
            ListEmptyComponent={
              <View style={styles.noDataView}>
                <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
                <Text style={{flex: 1}}>Danh sách trống. Vui lòng quét mã QR hoặc tìm kiếm!</Text>
              </View>
            }
          />
          {renderFooter()}
          <ModalQuetMaQR ref={refModalQuetMaQR} getData={(val) => getData(val)} onBackPress={() => refModalQuetMaQR.current.hide()} />
        </View>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  userInfo: state.user.data,
});

const mapDispatchToProps = {};

const DanhGiaRuiRoScreenConnect = connect(mapStateToProps, mapDispatchToProps)(DanhGiaRuiRoScreenComponent);
export const DanhGiaRuiRoScreen = memo(DanhGiaRuiRoScreenConnect, isEqual);
