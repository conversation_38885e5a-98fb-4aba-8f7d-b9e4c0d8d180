import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },
  footerView: {
    borderTopWidth: 0.2,
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  profileItemView: {
    borderWidth: 1,
    borderRadius: 10,
    padding: scale(10),
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginHorizontal: scale(10),
    marginVertical: vScale(spacing.tiny),
  },
  profileItemCenterView: {
    flex: 1,
  },

  profileTxtThoiGian: {
    fontWeight: '500',
    fontSize: FontSize.size14,
    color: 'rgba(0, 0, 0, 0.6)',
  },
  noDataView: {
    alignItems: 'center',
    marginHorizontal: scale(10),
  },
  txtValue: {
    lineHeight: 20,
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  renderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detail: {
    lineHeight: 20,
    fontWeight: '500',
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
  },
  rowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: vScale(spacing.tiny),
  },
  profileTimeView: {
    flex: 1,
  },
  btnLuuView: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: scale(spacing.small),
  },
});
