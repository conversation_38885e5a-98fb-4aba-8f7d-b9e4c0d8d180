import {FIRST_INDICATOR_STYLES_DATA} from '@app/commons/Constant';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, View} from 'react-native';
import StepIndicator from 'react-native-step-indicator';
import styles from './ChupAnhDanhGiaStyles';
import {DanhGiaChiTiet, DanhGiaChung, DanhGiaSoBo, DanhGiaXacNhan} from './Components';

const ChupAnhDanhGiaScreenComponent = ({route}) => {
  console.log('ChupAnhDanhGiaScreen');

  const [gcnChiTiet, setChiTiet] = useState(route.params.gcnChiTiet);
  const [currentPage, setCurrentPage] = useState(0);
  const [currentPosition, setCurrentPosition] = useState({});

  useEffect(() => {
    requestCurrentLocation(
      (position) => setCurrentPosition(position),
      (err) => console.log(err),
    );
  }, []);
  const backAction = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn thoát đánh giá rủi ro?', [
      {
        text: 'Huỷ',
      },
      {
        text: 'Đồng ý',
        onPress: () => NavigationUtil.pop(),
      },
    ]);
    return true;
  };
  const updateGCN = (gcn) => setChiTiet(gcn);

  /** RENDER */

  return (
    <ScreenComponent
      headerTitle="Đánh giá rủi ro"
      headerBack
      onPressBack={backAction}
      renderView={
        <SafeAreaView style={styles.container}>
          <View style={styles.stepIndicator}>
            <StepIndicator
              customStyles={FIRST_INDICATOR_STYLES_DATA}
              currentPosition={currentPage}
              labels={['Ảnh / Video', 'Đánh giá sơ bộ', 'Đánh giá chi tiết', 'Xác nhận']}
              renderLabel={({position, label, currentPosition}) => <Text style={position === currentPosition ? styles.stepLabelSelected : styles.stepLabel} children={label} />}
              stepCount={4}
              onPress={(step) => setCurrentPage(step)}
            />
            {/* <ThongKeUpload totalImagelUpload={totalImagelUpload} imageUploaded={imageUploaded} /> */}
          </View>

          <View style={{flex: 1}}>
            {currentPage === 0 && <DanhGiaChung gcnChiTiet={gcnChiTiet} onPressPage={(page) => setCurrentPage(page)} currentPosition={currentPosition} />}
            {currentPage === 1 && <DanhGiaSoBo gcnChiTiet={gcnChiTiet} onPressPage={(page) => setCurrentPage(page)} updateGCN={updateGCN} />}
            {currentPage === 2 && <DanhGiaChiTiet gcnChiTiet={gcnChiTiet} onPressPage={(page) => setCurrentPage(page)} currentPosition={currentPosition} />}
            {currentPage === 3 && <DanhGiaXacNhan gcnChiTiet={gcnChiTiet} onPressPage={(page) => setCurrentPage(page)} updateGCN={updateGCN} />}
          </View>
        </SafeAreaView>
      }
    />
  );
};

export const ChupAnhDanhGiaScreen = memo(ChupAnhDanhGiaScreenComponent, isEqual);
