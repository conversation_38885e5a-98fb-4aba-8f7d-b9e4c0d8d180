import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Icon, ScreenComponent} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, View} from 'react-native';
import ActionButton from 'react-native-action-button';
import Pdf from 'react-native-pdf';
import {ModalGuiMailXacNhanDanhGiaRuiRo} from './Components';
import styles from './BienBanDanhGiaRuiRoStyles';
import {isIOS} from '@app/commons/Constant';
import Share from 'react-native-share';

const BienBanDanhGiaRuiRoScreenComponent = ({route}) => {
  console.log('BienBanDanhGiaRuiRoScreenComponent', route);
  const {gcnChiTiet} = route.params;

  const [pdfData, setPdfData] = useState(null);
  const [isLoadingPdf, setIsLoadingPdf] = useState(false);
  let refGuiMailXacNhanDanhGiaRuiRo = useRef(null);
  useEffect(() => {
    getBienBanXacNhan();
  }, []);
  const getBienBanXacNhan = async () => {
    setIsLoadingPdf(true);
    let params = {
      ma_mau_in: 'ESCS_BIEN_BAN_DGRR',
      ma_doi_tac_ql: gcnChiTiet.ma_doi_tac,
    };
    let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_THONG_TIN_TO_TRINH, params);
    if (!response || !response.state_info || response.state_info.status !== 'OK') {
      setIsLoadingPdf(false);
      return;
    }
    let paramsExportPdf = {
      ma_mau_in: response.data_info.ma,
      ma_doi_tac: gcnChiTiet.ma_doi_tac,
      so_id_hd: gcnChiTiet.so_id_hd,
      so_id_dt: gcnChiTiet.so_id_dt,
      so_id_lan: gcnChiTiet.so_id_lan_dgrr,
      url_file: response.data_info.url_file,
    };
    let responsePdf = await ESmartClaimEndpoint.exportPdfBase64(response.data_info.ma_action_api, paramsExportPdf);
    console.log('responsePdf', responsePdf);
    setIsLoadingPdf(false);
    if (!responsePdf || !responsePdf.state_info || responsePdf.state_info.status !== 'OK') return;

    setPdfData(responsePdf.data_info);
  };
  const onPressGuiXacNhanKhachHang = async () => {
    const params = {
      ma_doi_tac: gcnChiTiet.ma_doi_tac, //ma_doi_tac
      so_id: gcnChiTiet.so_id_hd,
      nv: 'XE',
      loai: 'TEMPLATE_EMAIL_XAC_NHAN_DGRR',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.DANH_SACH_MAIL_NGUOI_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.out_value.email_nhan) refGuiMailXacNhanDanhGiaRuiRo.current.show(response.out_value.email_nhan);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const onPressSharePDF = () => {
    // if (!pdfData.filePath) {
    //   FlashMessageHelper.showFlashMessage('Thông báo', 'File chưa được tải về máy. Vui lòng thử lại', 'info');
    //   return;
    // }
    let options = {
      url: 'data:application/pdf;base64,' + pdfData.base64,
    };
    Share.open(options)
      .then((res) => {})
      .catch((err) => {});
  };

  /** RENDER */
  const renderActionButton = () => {
    if (!pdfData) return;
    return (
      <ActionButton
        buttonColor={colors.BLUE3_08}
        renderIcon={() => <Icon.SimpleLineIcons name="options-vertical" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />}
        offsetY={80}
        offsetX={10}
        size={55}
        degrees={0}>
        <ActionButton.Item buttonColor={colors.RED3} title="Chia sẻ" textContainerStyle={styles.actionButtonTextContainer} onPress={onPressSharePDF}>
          <Icon.FontAwesome name="share-alt" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
        </ActionButton.Item>
      </ActionButton>
    );
  };
  const renderContent = () => {
    return (
      <View style={{flex: 1}}>
        {pdfData && (
          <Pdf
            source={{
              uri: 'data:application/pdf;base64,' + pdfData.base64_string,
            }}
            onLoadComplete={(numberOfPages, filePath) => {
              // setPdfData((prevData) => ({
              //   ...prevData,
              //   filePath: filePath,
              // }));
            }}
            onPageChanged={(page, numberOfPages) => {}}
            onError={(error) => {
              Alert.alert('Thông báo', error);
            }}
            onPressLink={(uri) => {
              // console.log(`Link presse: ${uri}`);
            }}
            style={styles.pdf}
          />
        )}
      </View>
    );
  };
  return (
    <ScreenComponent
      headerTitle={'Biên bản đánh giá rủi ro'}
      headerBack
      isLoading={isLoadingPdf}
      renderView={
        <SafeAreaView style={styles.container}>
          {renderContent()}
          {renderActionButton()}
          <View style={styles.footerView}>
            <ButtonLinear title="Gửi xác nhận khách hàng" onPress={onPressGuiXacNhanKhachHang} />
          </View>
          <ModalGuiMailXacNhanDanhGiaRuiRo ref={refGuiMailXacNhanDanhGiaRuiRo} gcnChiTiet={gcnChiTiet} />
        </SafeAreaView>
      }
    />
  );
};

export const BienBanDanhGiaRuiRoScreen = memo(BienBanDanhGiaRuiRoScreenComponent, isEqual);
