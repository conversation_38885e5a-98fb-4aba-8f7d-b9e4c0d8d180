import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  pdf: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
  },
  footerView: {
    position: 'absolute',
    bottom: isIOS ? spacing.mediumPlush : 0,
    left: 0,
    right: 0,
    marginHorizontal: spacing.small,
  },
});
