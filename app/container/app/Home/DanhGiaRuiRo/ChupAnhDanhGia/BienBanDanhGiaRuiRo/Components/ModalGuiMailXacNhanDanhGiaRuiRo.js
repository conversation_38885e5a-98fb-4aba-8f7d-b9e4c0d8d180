import R from '@app/assets/R';
import {isIOS, REGUlAR_EXPRESSION} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Image, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalGuiMailXacNhanDanhGiaRuiRoComponent = forwardRef(({gcnChiTiet}, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: showModal,
      hide: hideModal,
    }),
    [],
  );
  const [isVisible, setIsVisible] = useState(false);
  let refMailInput = useRef(null);
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    setError,
    reset,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: {
      email: '',
    },
    mode: 'all',
  });
  const getErrMessage = (inputName, errType) => {
    if (inputName === 'email') {
      if (errType === 'required') return 'Thông tin bắt buộc';
      else if (errType === 'pattern') return 'Email sai định dạng';
    }
    return '';
  };

  const showModal = (emailInput) => {
    setIsVisible(true);
    setValue('email', emailInput);
  };
  const hideModal = () => {
    setIsVisible(false);
    reset({email: ''});
  };
  const onPressGuiMail = async (data) => {
    let params = {
      ma_doi_tac: gcnChiTiet.ma_doi_tac, //ma_doi_tac
      so_id: gcnChiTiet.so_id_hd,
      nv: 'XE',
      loai: 'TEMPLATE_EMAIL_XAC_NHAN_DGRR',
      email_nhan: data.email,
      create_file: 'ESCS_BIEN_BAN_DGRR',
      create_file_sign: 'ESCS_BIEN_BAN_DGRR',
      remove_file: 'ESCS_BIEN_BAN_DGRR',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.GUI_EMAIL_XAC_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      hideModal();
      FlashMessageHelper.showFlashMessage('Thông báo', 'Gửi email xác nhận thành công', 'success');
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  // RENDER
  return (
    <Modal isVisible={isVisible} style={styles.modal} onBackdropPress={hideModal}>
      <View style={styles.modalSuccessView}>
        <View style={styles.successTopView}>
          <Image source={R.images.img_mail_user} style={styles.imgMailUser} resizeMode={'contain'} />
          <Text style={styles.successTitle}>Xác nhận người nhận mail</Text>
        </View>
        <View style={styles.successCenterView}>
          <View style={styles.addMailView}>
            <Controller
              control={control}
              rules={{
                required: true,
                pattern: REGUlAR_EXPRESSION.REG_EMAIL,
              }}
              name="email"
              render={({field: {onChange, value}}) => (
                <TextInput ref={refMailInput} value={value} onChangeText={onChange} style={styles.inputMail} keyboardType="email-address" placeholderTextColor="#CCC" />
              )}
            />
          </View>
          {errors.email && <Text children={getErrMessage('email', errors.email.type)} style={{color: colors.RED1, alignSelf: 'flex-start', marginLeft: 20}} />}

          <View style={{flexDirection: 'row', marginTop: 10}}>
            <TouchableOpacity
              style={{
                ...styles.btnSuccessView,
                backgroundColor: colors.PRIMARY_08,
              }}
              onPress={hideModal}>
              <View style={styles.buttonModalView}>
                <Icon.AntDesign name="close" color={colors.WHITE} size={20} />
                <Text style={styles.txtSuccess} children="Đóng" />
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                ...styles.btnSuccessView,
                backgroundColor: 'green',
              }}
              onPress={handleSubmit(onPressGuiMail)}>
              <View style={styles.buttonModalView}>
                <Icon.MaterialCommunityIcons name="email-send-outline" color={colors.WHITE} size={20} />
                <Text style={styles.txtSuccess} children="Gửi mail" />
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
});
export const ModalGuiMailXacNhanDanhGiaRuiRo = memo(ModalGuiMailXacNhanDanhGiaRuiRoComponent, isEqual);

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'center',
    margin: 0,
    // flex: 1,
    // backgroundColor: colors.WHITE,
    paddingVertical: 10,
    paddingTop: isIOS ? 60 : 10,
  },
  modalSuccessView: {
    justifyContent: 'center',
    alignItems: 'center',
    // flex: 1,
  },
  successTopView: {
    width: dimensions.width - 30,
    height: dimensions.height / 6,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  successCenterView: {
    width: dimensions.width - 30,
    paddingBottom: 10,
    backgroundColor: colors.WHITE,
    justifyContent: 'space-around',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  addMailView: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: dimensions.width - 60,
  },
  inputMail: {
    borderBottomWidth: 0.5,
    paddingLeft: 20,
    borderWidth: 0.5,
    flex: 1,
    borderRadius: 30,
    height: 50,
  },
  iconAddMail: {
    borderRadius: 10,
    borderWidth: 1,
    padding: 10,
  },
  btnSuccessView: {
    height: 40,
    width: dimensions.width / 2 - 40,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 10,
  },
  buttonModalView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtSuccess: {
    textAlign: 'center',
    paddingHorizontal: 10,
    color: colors.WHITE,
    fontSize: 16,
  },
  imgMailUser: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});
