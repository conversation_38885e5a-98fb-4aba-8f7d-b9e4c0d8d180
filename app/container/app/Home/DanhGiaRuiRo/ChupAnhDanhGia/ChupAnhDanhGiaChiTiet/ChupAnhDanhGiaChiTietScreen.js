import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {getImageNameFromUriCamera} from '@app/utils/CameraProvider';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {ButtonLinear, ImageComp, ScreenComponent} from '@component';
import R from '@R';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, SafeAreaView, View} from 'react-native';
import styles from './ChupAnhDanhGiaChiTietStyles';
import {ModalCamera} from './Components';

const ChupAnhDanhGiaChiTietScreenComponent = ({route}) => {
  console.log('ChupAnhDanhGiaChiTietScreen', route);
  const {hangMucDuocChon, gcnChiTiet} = route.params;
  const [currentPosition, setCurrentPosition] = useState({});
  const [toggleLoading, setToggleLoading] = useState(false);

  //list ảnh upload lên server
  const [listAnhUpload, setListAnhUpload] = useState([
    {
      preView: R.icons.ic_gallery,
      name: '',
    },
  ]);

  let refModalCamera = useRef(null);

  useEffect(() => {
    requestCurrentLocation(
      (position) => setCurrentPosition(position),
      (err) => console.log(err),
    );
  }, []);
  const backAction = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn thoát chụp ảnh ?', [
      {
        text: 'Huỷ',
      },
      {
        text: 'Đồng ý',
        onPress: () => NavigationUtil.pop(),
      },
    ]);
    return true;
  };

  const handleImage = (dataImage) => {
    const imageName = getImageNameFromUriCamera(dataImage.path); //lấy ra tên ảnh từ uri
    let listAnhUploadTmp = listAnhUpload;
    listAnhUploadTmp.unshift({
      path: dataImage.path,
      name: imageName,
    });
    setListAnhUpload([...listAnhUploadTmp]);
  };

  const removeImage = (imageData) => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: () => {
          let listAnhUploadTmp = listAnhUpload;
          listAnhUploadTmp.splice(imageData.index, 1);
          setListAnhUpload([...listAnhUploadTmp]);
        },
      },
    ]);
  };

  const uploadImage = async () => {
    let imagesUploadToServer = [];
    for (let i = 0; i < listAnhUpload.length; i++) {
      if (listAnhUpload[i].path && !listAnhUpload[i].uploadThanhCong) imagesUploadToServer.push(listAnhUpload[i]);
    }
    if (imagesUploadToServer.length === 0) {
      NavigationUtil.pop();
      return;
    }
    setToggleLoading(true);
    let response = await Promise.all(
      imagesUploadToServer.map(async (item) => {
        return uploadImageToServer(
          [item],
          (anhThanhCong) => xuLyAnhUploadThanhCong(anhThanhCong),
          (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
        );
      }),
    );

    // CHECK LỖI CŨ
    // let haveErr = false;
    // response.map((item) => {
    //   if (item !== true) haveErr = true;
    // });
    // END CHECK LỖI CŨ

    let haveErr = '';
    response = response.filter((item) => item !== true);
    //bỏ đi các thông tin trùng
    let uniqueChars = response.filter((element, index) => {
      return response.indexOf(element) === index;
    });
    if (uniqueChars.length > 0) haveErr = uniqueChars.join(', ');

    //thực hiện xong hết thì mới reset lại data
    setToggleLoading(false);
    if (haveErr) {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi xảy ra khi tải ảnh lên hệ thống \n' + haveErr, 'info');
      return;
    }
    FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
    NavigationUtil.pop();
  };
  const xuLyAnhUploadThatBai = (anhThatBai) => {
    let listAnhUploadTmp = listAnhUpload;
    listAnhUploadTmp.map((itemAnh) => {
      if (itemAnh.path === anhThatBai.path) {
        itemAnh.uploadThatBai = true;
        itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
      }
      return itemAnh;
    });

    setListAnhUpload([...listAnhUploadTmp]);
  };
  const xuLyAnhUploadThanhCong = (anhThanhCong) => {
    let listAnhUploadTmp = listAnhUpload;
    listAnhUploadTmp.map((itemAnh) => {
      if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
      return itemAnh;
    });
    setListAnhUpload([...listAnhUploadTmp]);
  };
  const uploadImageToServer = (imagesData, cbSuccess, cbErr) => {
    return new Promise(
      async (resolve) => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            x: currentPosition.coords?.latitude,
            y: currentPosition.coords?.longitude,
            nhom: hangMucDuocChon.hang_muc,
          };
          files.push(file);
        });
        imagesData = imagesData.map((item) => {
          item = cloneObject(item);
          delete item.preView;
          delete item.uploadThanhCong;
          return item;
        });
        let params = {
          ung_dung: 'MOBILE',
          pm: 'BH',
          files: files,
          so_id_doi_tuong: 0,
          images: imagesData,
          so_id: gcnChiTiet.so_id_hd,
          so_id_dt: gcnChiTiet.so_id_dt,
          ma_doi_tac: gcnChiTiet.ma_doi_tac,
        };
        setToggleLoading(true);
        try {
          let response = await ESmartClaimEndpoint.uploadFile(axiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          if (response?.state_info?.status === axiosConfig.SERVER_RESPONSE_STATUS.NOT_OK) {
            resolve(response.state_info.message_body);
            imagesData[0].lyDoLoi = response.state_info.message_body;
            cbErr(imagesData[0]);
            return;
          } else if (response?.state_info?.status === axiosConfig.SERVER_RESPONSE_STATUS.OK) {
            cbSuccess(imagesData[0]);
            resolve(true);
          } else {
            imagesData[0].lyDoLoi = JSON.stringify(response?.message || response);
            cbErr(imagesData[0]);
            resolve(JSON.stringify(response?.message || response));
          }
        } catch (error) {
          Alert.alert('Thông báo', error.message);
          resolve(false);
        }
        return;
      },
      (reject) => reject(),
    );
  };

  /** RENDER */
  const renderImageUpload = (imageData) => {
    return (
      <ImageComp
        imageData={imageData}
        removeImage={removeImage}
        // rotationImage={rotationImage}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        onPressOpenCamera={() => refModalCamera.current.show()}
        // onPressXemLaiAnh={onPressXemLaiAnh}
        // uploadFromLib={chonAnhTuThuVien}
      />
    );
  };
  return (
    <ScreenComponent
      headerTitle={'Chụp ảnh'}
      headerBack
      onPressBack={backAction}
      renderView={
        <SafeAreaView style={styles.container}>
          <View style={{flex: 1}}>
            <View style={{paddingBottom: spacing.massive}}>
              <FlatList data={listAnhUpload} keyExtractor={(item, index) => index.toString()} scrollEnabled={true} renderItem={renderImageUpload} numColumns={2} horizontal={false} />
            </View>
            <View style={styles.footerView}>
              <ButtonLinear title="Tải lên" onPress={uploadImage} loading={toggleLoading} />
            </View>
          </View>
          <ModalCamera ref={refModalCamera} currentPosition={currentPosition} handleImage={handleImage} gcnChiTiet={gcnChiTiet} />
        </SafeAreaView>
      }
    />
  );
};

export const ChupAnhDanhGiaChiTietScreen = memo(ChupAnhDanhGiaChiTietScreenComponent, isEqual);
