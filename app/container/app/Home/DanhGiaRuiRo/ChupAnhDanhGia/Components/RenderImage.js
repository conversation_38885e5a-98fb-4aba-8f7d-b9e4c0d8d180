import R from '@app/assets/R';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, spacing} from '@app/theme';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import Progress from 'react-native-progress/Circle';
import {Text} from '@component';
const groupBy = (xs, key) => {
  return xs.reduce((rv, x) => {
    (rv[x[key]] = rv[x[key]] || []).push(x);
    return rv;
  }, {});
};

const RenderImageComponent = ({listAnhDaUpload, refreshing, onRefresh}) => {
  const [anhHoSo, setAnhHoSo] = useState([]); //ảnh hồ sơ sau khi được group theo hạng mục

  useEffect(() => {
    if (listAnhDaUpload.length > 0) {
      let imgsGroup = groupBy(listAnhDaUpload, 'ma_file');
      let listAnhHoSo = [];
      for (const property in imgsGroup) {
        listAnhHoSo.push({
          images: imgsGroup[property],
          ten: imgsGroup[property][0].nhom_anh,
        });
      }
      setAnhHoSo([...listAnhHoSo]);
    }
  }, [listAnhDaUpload]);
  const onPressOpenImageView = (currentDocumentData) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
      currentImageData: currentDocumentData,
      imagesData: listAnhDaUpload,
    });
  };

  const renderItemAnhHangMuc = (data) => {
    let item = data.item;
    return (
      <View marginBottom={spacing.medium}>
        <TouchableOpacity onPress={() => onPressOpenImageView(data)}>
          <ImageProcess
            source={{uri: `data:image/gif;base64,${item.duong_dan}`}}
            indicator={Progress.Circle}
            style={styles.imageDocument}
            imageStyle={{borderRadius: 4}}
            renderError={() => <Image source={R.images.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />}
          />
        </TouchableOpacity>
      </View>
    );
  };
  const renderNoData = () => (
    <View style={styles.noDataView}>
      <Image source={R.images.img_no_data} style={styles.imageNoData} resizeMode={'contain'} />
      <Text style={{color: colors.GRAY7}}>Chưa có dữ liệu. Vui lòng tải ảnh lên để đánh giá rủi ro!</Text>
    </View>
  );

  const renderHangMuc = ({item}) => {
    return (
      <FlatList
        scrollEnabled={false}
        data={item.images}
        renderItem={renderItemAnhHangMuc}
        keyExtractor={(itemAnh) => itemAnh.bt.toString()}
        numColumns={2}
        horizontal={false}
        style={{marginBottom: spacing.small}}
        ListEmptyComponent={renderNoData()}
        ListHeaderComponent={
          <View style={styles.headerView}>
            <View style={styles.headerSubTitleView}>
              <Text style={styles.headerTitle}>{item.ten}</Text>
            </View>
          </View>
        }
      />
    );
  };

  return (
    <View style={styles.container}>
      <FlatList data={anhHoSo} renderItem={renderHangMuc} initialNumToRender={50} ListEmptyComponent={renderNoData()} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, marginRight: spacing.small, marginTop: spacing.small},
  imageDocument: {
    width: dimensions.width / 2 - spacing.small * 2,
    height: dimensions.width / 2 - spacing.small * 2,
    marginLeft: spacing.small,
  },
  headerTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  headerView: {
    marginBottom: spacing.small,
    flexDirection: 'row',
    marginHorizontal: spacing.small,
    justifyContent: 'space-between',
  },
  headerSubTitle: {
    flex: 1,
    fontSize: 14,
    marginRight: spacing.tiny,
  },
  headerSubTitleView: {
    flex: 1,
  },
  noDataView: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageNoData: {
    width: dimensions.width / 5,
    height: dimensions.width / 5,
  },
});

const RenderImageMemo = memo(RenderImageComponent, isEqual);
export const RenderImage = RenderImageMemo;
