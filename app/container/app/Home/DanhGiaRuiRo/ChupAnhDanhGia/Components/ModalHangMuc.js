import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {selectType1} from '@app/redux/slices/CategoryCommonSlice';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Keyboard, ScrollView, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSelector} from 'react-redux';

const ModalHangMucComponent = forwardRef(({onHangMucSelected}, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  let scrollViewModalRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const hangMucTonThatXeRoot = useSelector(selectType1).filter((item) => item.loai === 'CHINH' && item.loai !== 'BT_TOAN_BO');
  const [hangMucTonThatXe, setHangMucTonThatXe] = useState(hangMucTonThatXeRoot.slice(0, 100) || []);
  const [timeoutId, setTimeoutId] = useState(null);
  const [searchInput, setSearchInput] = useState('');
  const onChangeSearchTextHangMuc = (textSearch) => {
    try {
      if (!textSearch.trim()) {
        setHangMucTonThatXe(hangMucTonThatXeRoot.slice(0, 100));
        clearTimeout(timeoutId);
        return;
      }
      clearTimeout(timeoutId);
      let timeoutIdTmp = setTimeout(() => {
        let result = [];
        /*search by text*/
        // for (let i = 0; i < categoryCommon.type1.length; i++) {
        //   if (categoryCommon.type1[i].label.toUpperCase().indexOf(textSearch.toUpperCase()) > -1) result.push(categoryCommon.type1[i]);
        // }

        let arrTextSearch = textSearch.trim().split(' ');
        arrTextSearch = arrTextSearch.filter((item) => item !== '');
        for (let i = 0; i < hangMucTonThatXeRoot.length; i++) {
          if (hangMucTonThatXeRoot[i].loai !== 'CHINH' && hangMucTonThatXeRoot[i].loai !== 'BT_TOAN_BO') continue;
          let arrTenHangMuc = hangMucTonThatXeRoot[i].ten.split(' ');
          let tonTai = 0; //nếu tonTai===(arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
          for (let j = 0; j < arrTextSearch.length; j++) {
            for (let k = 0; k < arrTenHangMuc.length; k++) {
              /*
              j + 1 !== tonTai : để loại trường hợp chuỗi tên hạng mục có 2 từ giống nhau
              ví dụ : 
              tên hạng mục : tôi là tôi 
              từ cần tìm : tôi là 
              -> khi duyệt từ 'tôi' ở từ cần tìm -> 'tôi' sẽ được tính 2 lần ->  dẫn đến sai kết quả của biến tonTai
              //có cách khác là remove duplicate ở cả 2 mảng arrTenHangMuc và arrTextSearch r tìm -> sẽ không bị dính trường hợp trên =))
              */
              if (arrTenHangMuc[k].toUpperCase() === arrTextSearch[j].toUpperCase() && j + 1 !== tonTai) {
                tonTai = tonTai + 1;
                break;
              }
            }
          }
          if (tonTai === arrTextSearch.length) result.push(hangMucTonThatXeRoot[i]);
        }
        let soViTriDoi = 0;
        for (let i = 0; i < result.length; i++) {
          let arrResultItem = result[i].ten.trim().split(' ');
          let soTuGiong = 0;
          for (let j = 0; j < arrTextSearch.length; j++) {
            if (j < arrResultItem.length) if (arrTextSearch[j].toUpperCase() === arrResultItem[j].toUpperCase()) soTuGiong = soTuGiong + 1;
          }
          if (soTuGiong === arrTextSearch.length && soViTriDoi < result.length) {
            [result[soViTriDoi], result[i]] = [result[i], result[soViTriDoi]];
            soViTriDoi = soViTriDoi + 1;
          }
        }
        console.log('result', result);
        setHangMucTonThatXe([...result]);
      }, 500);
      setTimeoutId(timeoutIdTmp);
    } catch (error) {
      Alert.alert('Thông báo tìm kiếm hạng mục', error.message);
    }
  };
  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <TouchableOpacity style={styles.backBtn} onPress={() => setIsVisible(false)}>
          <Icon.AntDesign name="arrowleft" size={20} style={styles.iconBack} />
        </TouchableOpacity>
        <TextInput
          style={styles.searchInput}
          value={searchInput}
          placeholder="Tìm kiếm..."
          placeholderTextColor={colors.BLACK}
          onChangeText={(value) => {
            setSearchInput(value);
            onChangeSearchTextHangMuc(value);
          }}
          onFocus={() => Keyboard.o}
        />

        <TouchableOpacity
          style={styles.backBtn}
          onPress={() => {
            setHangMucTonThatXe(hangMucTonThatXeRoot.slice(0, 100));
            setSearchInput('');
          }}>
          <Icon.MaterialCommunityIcons name="filter-remove" size={20} style={styles.iconBack} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItemHangMuc = (item, index) => (
    <TouchableOpacity
      key={index}
      style={styles.itemHangMucView}
      onPress={() => {
        onHangMucSelected(item);
        setIsVisible(false);
        setHangMucTonThatXe(hangMucTonThatXeRoot.slice(0, 100));
        setSearchInput('');
      }}>
      <Text children={item.ten} style={{marginLeft: spacing.tiny}} />
    </TouchableOpacity>
  );

  const renderContent = () => {
    return (
      <View style={{flex: 1}}>
        <ScrollView style={styles.scrollView} ref={scrollViewModalRef} scrollEventThrottle={16} showsVerticalScrollIndicator={false}>
          <View>{hangMucTonThatXe.map((item, index) => renderItemHangMuc(item, index))}</View>
        </ScrollView>
      </View>
    );
  };
  return (
    <Modal isVisible={isVisible} style={styles.modal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          {renderHeader()}
          {renderContent()}
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  scrollView: {
    paddingHorizontal: spacing.small,
    marginVertical: spacing.small,
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
    paddingTop: isIOS ? 60 : 10,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingLeft: 20,
    flex: 1,
    borderRadius: 5,
    height: 50,
  },
  headerView: {
    flexDirection: 'row',
  },
  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mucDoTonThatView: {
    position: 'absolute',
    right: 0,
    top: 20,
    backgroundColor: '#FFF',
    paddingHorizontal: 10,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
});
export const ModalHangMuc = memo(ModalHangMucComponent, isEqual);
