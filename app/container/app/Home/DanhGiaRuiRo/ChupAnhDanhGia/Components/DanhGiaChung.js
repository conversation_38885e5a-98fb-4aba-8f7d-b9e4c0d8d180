import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, ImageComp, Text} from '@app/components';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {getImageNameFromUriCamera} from '@app/utils/CameraProvider';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import R from '@R';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import ImageCropPicker from 'react-native-image-crop-picker';
import ImageMarker, {Position} from 'react-native-image-marker';
import {useSelector} from 'react-redux';
import ModalXemChiTietAnh from '../../Components/ModalXemChiTietAnh';
import {ModalCamera} from './ModalCamera';
import {RenderImage} from './RenderImage';
import {TakeVideo} from './TakeVideo';
const button = [
  {
    title: 'Chụp Ảnh',
    iconName: 'camera',
  },
  {
    title: 'Quay Video',
    iconName: 'video-camera',
  },
  {
    title: 'Xem ảnh',
    iconName: 'photo',
  },
];

const TRANG_THAI_DANH_GIA = {
  ['DXN']: 'Đã xác nhận đánh giá',
  ['C']: 'Chưa đánh giá',
  ['D']: 'Đã đánh giá',
};

const DanhGiaChungComponent = ({gcnChiTiet, onPressPage, currentPosition}) => {
  console.log('DanhGiaChungComponent');
  const [imagesData, setImagesData] = useState([]); //ảnh đang chờ upload
  const isDaXacNhanDanhGia = gcnChiTiet.danh_gia === 'DXN';
  const [indexView, setIndexView] = useState(isDaXacNhanDanhGia ? 1 : 0);
  const [indexOpened, setIndexOpened] = useState(-1);
  const [toggleLoading, setToggleLoading] = useState(false);
  const [modalImageSelectedData, setModalImageSelectedData] = useState(null);
  const [toggleModalImage, setToggleModalImage] = useState(false);
  // const [isEnableBtnDanhGiaRuiRo, setIsEnableBtnDanhGiaRuiRo] = useState(false);
  const [listAnhDaUpload, setListAnhDaUpload] = useState([]); //list ảnh đã upload lên server

  const userInfo = useSelector(selectUser);
  const [listVideo, setListVideo] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  let refComponentTakeVideo = useRef(null);
  let refModalCamera = useRef(null);

  useEffect(() => {
    getAnhDaChup();
    getListVideo();
    layDsAnhDanhGiaRuiRo();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    getListVideo();
    getAnhDaChup();
  };

  const getListVideo = async () => {
    try {
      let params = {so_id: gcnChiTiet.so_id_hd, so_id_dt: gcnChiTiet.so_id_dt};
      let response = await CarClaimEndpoint.getListVideo(axiosConfig.ACTION_CODE.GET_LIST_VIDEO, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListVideo([...response.data_info]);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const layDsAnhDanhGiaRuiRo = async () => {
    let params = {
      ma_doi_tac: gcnChiTiet?.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_ANH_DANH_GIA_RUI_RO, params);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      initImageData(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getAnhDaChup = async () => {
    try {
      let params = {
        ma_doi_tac: gcnChiTiet?.ma_doi_tac,
        so_id_hd: gcnChiTiet?.so_id_hd,
        so_id_dt: gcnChiTiet?.so_id_dt,
      };
      let response = await ESmartClaimEndpoint.getFileThumbnail(axiosConfig.ACTION_CODE.LIST_ANH_DA_UPLOAD, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListAnhDaUpload(response.data_info);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const initImageData = hangMucAnh => {
    if (hangMucAnh.length === 0) return;
    let imageDataTmp = [];
    for (let i = 0; i < hangMucAnh.length; i++) {
      let tmp = {};
      tmp.images = [];
      for (let j = 0; j < hangMucAnh.length; j++) tmp.images.push({stt: j, path: '', name: '', ten: hangMucAnh[j].ten, nhom: hangMucAnh[j].ma, preView: R.icons.ic_gallery});
      imageDataTmp.push(tmp);
      break;
    }
    setImagesData(imageDataTmp);
  };

  const handleImage = (dataImage, idxOpened, type) => {
    const imageName = getImageNameFromUriCamera(dataImage.path); //lấy ra tên ảnh từ uri
    let imagesTmp = imagesData;
    imagesTmp[0].images[idxOpened].path = dataImage.path;
    imagesTmp[0].images[idxOpened].name = imageName;
    setImagesData([...imagesTmp]);
    if (idxOpened === imagesData[0].images.length - 1) refModalCamera.current.hide();
  };

  const chenThongTinLenAnh = async dataImage => {
    //ngày giờ / toạ độ / người / thông tin máy
    let txtChen = '';
    txtChen += userInfo.nguoi_dung.nsd;
    let tenMay = await DeviceInfo.getBrand();
    txtChen += '\n' + moment().format('HH:mm DD/MM/YYYY') + ' ';
    txtChen += tenMay;
    if (currentPosition && currentPosition.coords) txtChen += '\n' + currentPosition.coords.longitude.toFixed(3) + ' ; ' + currentPosition.coords.latitude.toFixed(3) + ' ';

    let imageAddText = await ImageMarker.markText({
      backgroundImage: {
        src: dataImage.path,
        scale: 1,
      },
      watermarkTexts: [
        {
          text: txtChen,
          positionOptions: {
            position: Position.bottomRight,
          },
          style: {
            color: '#FFF',
            fontSize: isIOS ? 30 : 30,
          },
        },
      ],
      quality: 100,
    }).catch(err => {
      Alert.alert('Chèn toạ độ không thành công', err.message);
      return '';
    });
    return imageAddText;
  };

  const openCamera = (idxOpened, menuImageData, type) => {
    let imgCropOpts = {
      mediaType: 'photo', //mặc định là chụp ảnh từ camera
      cropping: false,
      enableRotationGesture: true,
      compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
      showCropGuidelines: true, //tắt khung 3x3 đi
      compressImageQuality: 0.5,
      useFrontCamera: false,
      includeExif: true,
    };
    if (type === 1) {
      ImageCropPicker.openPicker(imgCropOpts)
        .then(async data => {
          let imageAddText = await chenThongTinLenAnh(data);
          data.path = (!isIOS ? 'file://' : '') + imageAddText;
          handleImage(data, idxOpened, type);
        })
        .catch(err => console.log('err', err));
      return;
    }
    //Open Camera
    ImageCropPicker.openCamera(imgCropOpts)
      .then(data => handleImage(data, idxOpened, type))
      .catch(err => console.log('err', err));
  };
  const removeImage = dataImage => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Đồng ý',
        onPress: () => {
          let imagesStep2Tmp = imagesData;
          imagesStep2Tmp[0].images[dataImage.index].path = '';
          imagesStep2Tmp[0].images[dataImage.index].uploadThanhCong = false;
          imagesStep2Tmp[0].images[dataImage.index].uploadThatBai = false;
          imagesStep2Tmp[0].images[dataImage.index].sttAnh = undefined;
          imagesStep2Tmp[0].images[dataImage.index].rootPath ? (imagesStep2Tmp[0].images[dataImage.index].rootPath = undefined) : '';
          setImagesData([...imagesStep2Tmp]);
        },
      },
    ]);
  };
  const onPressOpenCamera = (idxOpened, menuImageData, type) => {
    setIndexOpened(idxOpened);
    if (type === 1) {
      openCamera(idxOpened, null, type);
      return;
    }
    refModalCamera.current.show();
  };

  const onPressXemLaiAnh = imageData => {
    setModalImageSelectedData({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
    setToggleModalImage(true);
  };
  const xuLyAnhUploadThatBai = anhThatBai => {
    let imageDataStep2Tmp = imagesData;
    imageDataStep2Tmp.map(itemHangMuc => {
      itemHangMuc.images.map(itemAnh => {
        if (itemAnh.path === anhThatBai.path) {
          itemAnh.uploadThatBai = true;
          itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
        }
        return itemAnh;
      });
      return itemHangMuc;
    });
    setImagesData([...imageDataStep2Tmp]);
  };
  const xuLyAnhUploadThanhCong = anhThanhCong => {
    let imageDataTmp = imagesData;
    imageDataTmp.map(itemHangMuc => {
      itemHangMuc.images.map(itemAnh => {
        if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
        return itemAnh;
      });
      return itemHangMuc;
    });
    setImagesData([...imageDataTmp]);
  };

  const onPressUpload = async () => {
    if (toggleLoading) return;
    let imagesUploadToServer = [];
    imagesData.map((imageItem, index) => {
      if (imageItem.images) {
        for (let i = 0; i < imageItem.images.length; i++) {
          if (imageItem.images[i].path) {
            !imageItem.images[i].uploadThanhCong && imagesUploadToServer.push(imageItem.images[i]);
          }
        }
      }
    });
    if (imagesUploadToServer.length === 0) {
      onPressPage(1);
      return;
    }
    setToggleLoading(true);
    let response = await Promise.all(
      imagesUploadToServer.map(async item => {
        return uploadImageToServer(
          [item],
          anhThanhCong => {
            xuLyAnhUploadThanhCong(anhThanhCong);
          },
          anhThatBai => xuLyAnhUploadThatBai(anhThatBai),
        );
      }),
    );

    // CHECK LỖI CŨ
    // let haveErr = false;
    // response.map((item) => {
    //   if (item !== true) haveErr = true;
    // });
    // END CHECK LỖI CŨ

    let haveErr = '';
    response = response.filter(item => item !== true);
    //bỏ đi các thông tin trùng
    let uniqueChars = response.filter((element, index) => {
      return response.indexOf(element) === index;
    });
    if (uniqueChars.length > 0) haveErr = uniqueChars.join(', ');

    //thực hiện xong hết thì mới reset lại data
    setToggleLoading(false);
    getAnhDaChup();
    if (haveErr) {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi xảy ra khi tải ảnh lên hệ thống', 'info');
      return;
    }
    if (imagesUploadToServer.length === imagesData[0].images.length) onPressPage(1);
    FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
  };

  const uploadImageToServer = (imagesData, cbSuccess, cbErr) => {
    return new Promise(
      async resolve => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            x: currentPosition.coords?.latitude,
            y: currentPosition.coords?.longitude,
            nhom: imagesData[0].nhom,
            stt: e.stt,
          };
          // file.stt = e.sttAnh != undefined ? e.sttAnh : 0;
          files.push(file);
        });
        imagesData = imagesData.map(item => {
          item = cloneObject(item);
          delete item.preView;
          delete item.uploadThanhCong;
          return item;
        });
        let params = {
          ung_dung: 'MOBILE',
          pm: 'BH',
          files: files,
          so_id_doi_tuong: 0,
          images: imagesData,
          so_id: gcnChiTiet.so_id_hd,
          so_id_dt: gcnChiTiet.so_id_dt,
          ma_doi_tac: gcnChiTiet.ma_doi_tac,
        };
        setToggleLoading(true);
        try {
          let response = await ESmartClaimEndpoint.uploadFile(axiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          if (response?.state_info?.status === axiosConfig.SERVER_RESPONSE_STATUS.NOT_OK) {
            resolve(response.state_info.message_body);
            imagesData[0].lyDoLoi = response.state_info.message_body;
            cbErr(imagesData[0]);
            return;
          } else if (response?.state_info?.status === axiosConfig.SERVER_RESPONSE_STATUS.OK) {
            cbSuccess(imagesData[0]);
            resolve(true);
          } else {
            imagesData[0].lyDoLoi = JSON.stringify(response?.message || response);
            cbErr(imagesData[0]);
            resolve(JSON.stringify(response?.message || response));
          }
        } catch (error) {
          Alert.alert('Thông báo', error.message);
          resolve(false);
        }
        return;
      },
      reject => reject(),
    );
  };

  const getDisableTaiAnh = () => {
    if (imagesData.length === 0) return false;
    let disabled = false;
    imagesData[0].images.forEach(itemImage => {
      let daCo = false;
      listAnhDaUpload.map(itemAnhDaUpload => {
        if (itemImage.nhom === itemAnhDaUpload.ma_file) daCo = true;
      });
      if (!daCo) disabled = true;
    });
    return disabled;
  };

  const onPressTiepTuc = () => onPressPage(1);

  /* RENDER */
  const renderBtnSwitchView = () => (
    <View style={styles.btnRowContainer}>
      {button.map((item, index) => {
        let selected = indexView === index;
        if (index === 0 && isDaXacNhanDanhGia) return;
        return (
          <TouchableOpacity
            key={index}
            style={[styles.btnEdit, {backgroundColor: selected ? colors.PRIMARY : '#FFF'}, (index === 1 || index === 2) && {marginLeft: spacing.smaller}]}
            onPress={() => setIndexView(index)}>
            <Icon.FontAwesome name={item.iconName} size={16} color={selected ? colors.WHITE : colors.PRIMARY} />
            <Text style={[styles.txtTab, {color: selected ? colors.WHITE : colors.PRIMARY}]}>{item.title}</Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
  const renderImageItem = data => {
    return (
      <View style={styles.imageComponentRow}>
        <Text style={styles.label}>{data.item.ten}</Text>
        <ImageComp
          imageData={data}
          removeImage={removeImage}
          //   rotationImage={props.rotationImage}
          width={dimensions.width / 2 - 30}
          height={dimensions.width / 2 - 30}
          onPressOpenCamera={onPressOpenCamera}
          onPressXemLaiAnh={onPressXemLaiAnh}
          uploadFromLib={true}
        />
      </View>
    );
  };
  const renderChupAnhView = () => {
    return (
      <View tabLabel="Chụp ảnh" style={{flex: 1}}>
        {getDisableTaiAnh() && (
          <View style={styles.warningView}>
            <Icon.AntDesign name="warning" size={15} color={colors.ORANGE1} />
            <Text children="Vui lòng chụp ảnh hạng mục đánh giá" style={styles.txtWarning} />
          </View>
        )}

        <FlatList
          keyExtractor={(item, index) => index.toString()}
          data={imagesData[0]?.images || []}
          numColumns={2}
          renderItem={item => renderImageItem(item, {images: imagesData[0]?.images || []})}
          horizontal={false}
          style={styles.listAnh}
        />
        <View style={styles.footerView}>
          {isDaXacNhanDanhGia ? (
            <ButtonLinear
              disabled={getDisableTaiAnh()}
              linearColors={getDisableTaiAnh() ? [colors.GRAY, colors.GRAY] : [colors.PRIMARY_08, colors.PRIMARY]}
              textStyle={getDisableTaiAnh() ? {color: colors.BLACK_03} : {color: colors.WHITE}}
              title="Tiếp tục"
              onPress={onPressTiepTuc}
            />
          ) : (
            <ButtonLinear
              loading={toggleLoading}
              // linearStyle={{marginRight: spacing.small}}
              title="Tải ảnh"
              onPress={onPressUpload}
            />
          )}

          {/* <ButtonLinear
            disabled={getDisableTaiAnh()}
            linearColors={getDisableTaiAnh() ? [colors.GRAY, colors.GRAY] : [colors.PRIMARY_08, colors.PRIMARY]}
            textStyle={getDisableTaiAnh() ? {color: colors.BLACK_03} : {color: colors.WHITE}}
            title="Tiếp tục"
            onPress={onPressTiepTuc}
          /> */}
        </View>
        <ModalCamera
          ref={refModalCamera}
          indexOpened={indexOpened}
          setIndexOpened={setIndexOpened}
          handleImage={handleImage}
          imagesData={imagesData}
          currentPosition={currentPosition}
          menuImageStep2Selected={'ANH_DANH_GIA_RUI_RO'}
          currentPage={0}
        />
        <ModalXemChiTietAnh key="ModalXemChiTietAnh" toggleModalImage={toggleModalImage} setToggleModalImage={setToggleModalImage} modalImageSelectedData={modalImageSelectedData} />
      </View>
    );
  };
  const renderQuayVideoView = () => {
    return (
      <View style={{flex: 1}}>
        <TakeVideo
          ref={refComponentTakeVideo}
          profileData={{
            ho_so: {ma_doi_tac: gcnChiTiet?.ma_doi_tac, so_id: gcnChiTiet?.so_id_hd, so_id_dt: gcnChiTiet?.so_id_dt},
          }}
          listVideo={listVideo}
          getListVideo={getListVideo}
          onRefresh={onRefresh}
          refreshing={refreshing}
        />
        <View style={styles.footerView}>
          {!isDaXacNhanDanhGia && (
            <ButtonLinear loading={toggleLoading} linearStyle={{marginRight: spacing.small}} title="Quay video" onPress={() => refComponentTakeVideo.current.showModalQuayVideo()} />
          )}
          <ButtonLinear title="Tiếp tục" onPress={onPressTiepTuc} />
        </View>
      </View>
    );
  };
  const renderXemLaiAnhView = () => {
    return (
      <>
        <RenderImage listAnhDaUpload={listAnhDaUpload} imageData={listAnhDaUpload} refreshing={refreshing} onRefresh={onRefresh} />
        <View style={styles.footerView}>
          <ButtonLinear title="Tiếp tục" onPress={onPressTiepTuc} />
        </View>
      </>
    );
  };
  return (
    <View style={styles.container}>
      {renderBtnSwitchView()}
      {indexView === 0 && !isDaXacNhanDanhGia && renderChupAnhView()}
      {indexView === 1 && renderQuayVideoView()}
      {indexView === 2 && renderXemLaiAnhView()}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {flex: 1},
  btnRowContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
    flexDirection: 'row',
    borderRadius: 10,
    marginTop: vScale(spacing.tiny),
    marginHorizontal: scale(spacing.small),
    paddingHorizontal: scale(spacing.smaller),
  },
  warningView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: vScale(spacing.small),
  },
  txtWarning: {
    color: colors.ORANGE1,
    fontSize: FontSize.size14,
  },
  btnEdit: {
    flex: 1,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: vScale(spacing.smaller),
    paddingVertical: vScale(spacing.smaller),
    paddingHorizontal: scale(spacing.smaller),
  },
  txtTab: {
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    marginLeft: scale(spacing.tiny),
  },
  listAnh: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    marginTop: vScale(spacing.small),
  },
  footerView: {
    borderTopWidth: 0.2,
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },
  imageComponentRow: {
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    paddingBottom: vScale(spacing.medium),
  },
  label: {
    fontWeight: '600',
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    marginTop: vScale(spacing.small),
    marginLeft: scale(spacing.small),
  },
});

const DanhGiaChungMemo = memo(DanhGiaChungComponent, isEqual);
export const DanhGiaChung = DanhGiaChungMemo;
