import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, TextInputOutlined, Text} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalInputComponent = forwardRef(({onBackPress, onPressLuu, value, gcnChiTiet}, ref) => {
  const [isVisible, setIsVisible] = useState(false);
  const isDaXacNhanDanhGia = gcnChiTiet.danh_gia === 'DXN';
  const [text, setText] = useState('');
  const [dataHangMuc, setDataHangMuc] = useState(null);
  let refInput = useRef();

  useImperativeHandle(ref, () => ({
    show: (data) => {
      setIsVisible(true);
      setDataHangMuc(data);
    },
    hide: () => {
      setIsVisible(false);
      setDataHangMuc(null);
    },
  }));

  const initModalData = () => {
    if (value && value !== null) setText(value);
    else setText('');
    refInput?.focus();
  };

  const _onPressLuu = (val) => {
    onBackPress && onBackPress();
    onPressLuu && onPressLuu(val);
  };

  /* RENDER */

  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children={dataHangMuc ? dataHangMuc.ten : ''} />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onModalShow={initModalData}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      style={styles.modal}
      avoidKeyboard={true}>
      <View style={styles.modalContent}>
        {renderHeader()}
        <View marginHorizontal={10} marginTop={10}>
          <TextInputOutlined
            multiline
            value={text}
            onChangeText={setText}
            keyboardType="default"
            inputStyle={styles.inputStyle}
            getRef={(ref) => (refInput = ref)}
            placeholder={'Nhập ghi chú'}
            editable={!isDaXacNhanDanhGia ? true : false}
          />
        </View>
        <View style={styles.btnView}>
          <ButtonLinear title="Đóng" linearStyle={styles.btnLuu} onPress={() => setIsVisible(false)} linearColors={[colors.GRAY, colors.GRAY]} textStyle={{color: colors.BLACK_03}} />
          {!isDaXacNhanDanhGia && <ButtonLinear title={'Lưu'} linearStyle={[styles.btnLuu, {marginLeft: spacing.smaller}]} onPress={() => _onPressLuu(text)} />}
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    margin: 0,
  },
  btnView: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 30,
    left: 10,
    right: 10,
  },
  modalContent: {
    height: dimensions.height * 0.4,
    backgroundColor: '#FFF',
    // borderWidth: 1,
    width: dimensions.width,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // paddingHorizontal: spacing.small,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    width: dimensions.width / 5,
    height: 7,
    // borderWidth: 1,
    backgroundColor: colors.GRAY,
    borderRadius: 20,
  },
  btnLuu: {
    // marginTop: spacing.smaller,
  },
  inputContainer: {
    // flex: 1,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
    // paddingBottom :
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.RED1,
    marginVertical: 10,
  },
  errText: {
    color: colors.RED1,
    marginBottom: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  inputStyle: {
    height: 80,
    textAlignVertical: 'top',
  },
});
export const ModalInput = memo(ModalInputComponent, isEqual);
