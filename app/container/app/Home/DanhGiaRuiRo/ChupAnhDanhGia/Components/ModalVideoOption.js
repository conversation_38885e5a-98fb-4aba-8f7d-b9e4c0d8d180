import {colors} from '@app/commons/Theme';
import {Icon, Text} from '@app/components';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import prompt from 'react-native-prompt-android';

const ModalVideoOptionComponent = forwardRef(({getListVideo}, ref) => {
  console.log('ModalVideoOptionComponent');
  const [visible, setVisible] = useState(false);
  const [videoData, setVideoData] = useState(null);

  useImperativeHandle(ref, () => ({
    show: (data) => {
      setVisible(true);
      setVideoData(data);
    },
    hide: () => setVisible(false),
  }));
  const closeModal = () => {
    setVisible(false);
    setVideoData(null);
  };
  const onPressSuaTen = async () => {
    prompt(
      'Tên video',
      'Vui lòng nhập tên video',
      [
        {text: 'Để sau'},
        {
          text: 'Đồng ý',
          onPress: async (tenVideo) => {
            try {
              let params = {
                so_id: videoData.so_id,
                bt: videoData.bt,
                ten: tenVideo,
              };
              let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.UPDATE_VIDEO_NAME, params);
              closeModal();
              if (!response || !response.state_info || response.state_info.status !== 'OK') return;
              FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật thành công', 'success');
              getListVideo();
            } catch (error) {
              Alert.alert('Thông báo', error.message);
            }
          },
        },
      ],
      {
        defaultValue: '',
        placeholder: 'Tên video',
      },
    );
  };
  const onPressTaiXuong = () => {
    // setIsLoadingVideo(true);
    // let fetchBlodResponse = await ReactNativeBlobUtil.fetch('GET', axiosConfig.CONFIG_SERVER.BASE_URL_API.substring(0, axiosConfig.CONFIG_SERVER.BASE_URL_API.length - 1) + videoData.link_video);
    // // // let fetchBlodResponse = await ReactNativeBlobUtil.fs.stat(axiosConfig.CONFIG_SERVER.BASE_URL_API.substring(0, axiosConfig.CONFIG_SERVER.BASE_URL_API.length - 1) + videoData.link_video);
    // // console.log('fetchBlodResponse', fetchBlodResponse);
    // // //ghi file vào bộ nhớ
    // const path = `file://${RNFS.DocumentDirectoryPath}/demo.mp4`;
    // await RNFS.writeFile(path, fetchBlodResponse.data, 'base64');
    // setIsLoadingVideo(false);
    // videoData.urlFileDownload = `file://${RNFS.DocumentDirectoryPath}/demo.mp4`;
    // setVideoData(videoData);
  };
  /* RENDER */
  return (
    <Modal isVisible={visible} swipeDirection={'down'} style={styles.modal} onBackdropPress={closeModal} onSwipeCancel={closeModal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalHeader}>
          <View style={styles.headerRow} />
        </View>
        {/* <View style={styles.topViewCamera}>
          <TouchableOpacity onPress={() => closeModal()} style={styles.btnCloseCamera}>
            <Icon.AntDesign name="arrowleft" size={40} color={'#000'} />
          </TouchableOpacity>
        </View> */}
        <TouchableOpacity style={styles.actionView} onPress={onPressSuaTen}>
          <Icon.AntDesign name="edit" size={25} />
          <Text children="Sửa tên" style={styles.txtAction} />
        </TouchableOpacity>
        {/* <TouchableOpacity style={styles.actionView} onPress={onPressTaiXuong}>
          <Icon.AntDesign name="download" size={25} />
          <Text children="Tải xuống" style={styles.txtAction} />
        </TouchableOpacity> */}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    // flex: 1,
  },
  modalCameraView: {
    justifyContent: 'flex-end',
    // height: dimension.height,
    // width: dimension.width,
    // flex: 1,
    backgroundColor: '#FFF',
    paddingBottom: spacing.small,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalHeader: {
    height: 20,
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    width: dimensions.width / 5,
    height: 7,
    // borderWidth: 1,
    backgroundColor: colors.GRAY,
    borderRadius: 20,
  },
  topViewCamera: {
    position: 'absolute',
    left: 0,
    top: 30,
    right: 0,
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    flexDirection: 'row',
  },
  actionView: {
    flexDirection: 'row',
    paddingHorizontal: spacing.mediumPlush,
    paddingVertical: spacing.small,
    // justifyContent: 'center',
    alignItems: 'center',
  },
  txtAction: {
    paddingRight: spacing.small,
    paddingLeft: spacing.mediumPlush,
  },
});

export default memo(ModalVideoOptionComponent, isEqual);
