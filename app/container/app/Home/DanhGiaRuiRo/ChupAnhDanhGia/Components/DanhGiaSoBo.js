import {colors} from '@app/commons/Theme';
import {ButtonLinear, CheckboxComp, Icon, Text} from '@app/components';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, StyleSheet, TouchableOpacity, View} from 'react-native';
import {ModalInput} from './ModalInput';

const DanhGiaSoBoComponent = ({gcnChiTiet, onPressPage, updateGCN}) => {
  console.log('DanhGiaSoBoComponent');
  const isDaXacNhanDanhGia = gcnChiTiet.danh_gia === 'DXN';
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [itemIndex, setItemIndex] = useState(-1);
  const [valueGhiChu, setValueGhiChu] = useState('');

  let refModalNhapGhiChu = useRef(null);

  useEffect(() => {
    layChiTietDanhGia();
  }, []);

  const layChiTietDanhGia = async () => {
    try {
      let params = {
        ma_doi_tac: gcnChiTiet.ma_doi_tac,
        so_id_hd: gcnChiTiet.so_id_hd,
        so_id_dt: gcnChiTiet.so_id_dt,
        so_id_lan: gcnChiTiet.so_id_lan_dgrr,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DANH_GIA_RR, params);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      initDataDaDanhGia(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const initDataDaDanhGia = (hangMucAnh) => {
    if (hangMucAnh.length === 0) return;
    let dataHangMuc = hangMucAnh.map((e) => {
      e.hang_muc = e.ma;
      e.ghi_chu = e.ghi_chu;
      if (e.danh_gia === 'BT') {
        e.bt = true;
        e.khac = false;
      } else {
        e.bt = false;
        e.khac = true;
      }
      return e;
    });
    setData(dataHangMuc);
  };

  const handleDanhGia = async () => {
    if (isDaXacNhanDanhGia) {
      onPressPage(2);
      return;
    }
    setIsLoading(true);
    let arr = [];
    data.map((e) => {
      let json = {
        hang_muc: e.hang_muc,
        danh_gia: e.bt === true ? 'BT' : 'KHAC',
        ghi_chu: e.ghi_chu,
      };
      arr.push(json);
    });
    try {
      let params = {
        ma_doi_tac: gcnChiTiet.ma_doi_tac,
        so_id_hd: gcnChiTiet.so_id_hd,
        so_id_lan: gcnChiTiet.so_id_lan_dgrr,
        tinh_trang: 'D',
        so_id_dt: gcnChiTiet.so_id_dt,
        arr: arr,
        nguon: 'MOBILE',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DANH_GIA_RUI_RO, params);
      setIsLoading(false);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      updateGCN({
        ...gcnChiTiet,
        so_id_lan_dgrr: response.out_value.so_id_lan,
      });
      FlashMessageHelper.showFlashMessage('Thông báo', 'Đánh giá sơ bộ thành công', 'success');
      onPressPage(2);
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressBt = (idx, value) => {
    let newArr = data;
    if (!value) {
      newArr[idx].bt = !value;
      newArr[idx].khac = value;
    }
    setData([...newArr]);
  };
  const onPressKhac = (idx, value) => {
    let newArr = data;
    if (!value) {
      newArr[idx].khac = !value;
      newArr[idx].bt = value;
    }
    setData([...newArr]);
  };

  const onOpenModalGhiChu = (idx, val, item) => {
    setItemIndex(idx);
    setValueGhiChu(val);
    refModalNhapGhiChu.current.show(item);
  };

  const onSetInputValue = (val) => {
    let newArr = data;
    newArr[itemIndex].ghi_chu = val;
    setData([...newArr]);
  };
  /* RENDER */

  const renderItemHangMuc = ({item, index}) => {
    return (
      <View style={styles.item}>
        <View marginHorizontal={10}>
          <Text style={styles.label}>{item.ten}</Text>
          <View style={styles.itemRow}>
            <TouchableOpacity style={styles.checkBoxView} onPress={() => onPressBt(index, item.bt)} disabled={isDaXacNhanDanhGia}>
              <CheckboxComp
                checkboxStyle={{marginHorizontal: spacing.small}}
                disabled
                value={item.bt}
                onFillColor={isDaXacNhanDanhGia ? colors.GRAY : colors.PRIMARY}
                onTintColor={isDaXacNhanDanhGia ? colors.GRAY : colors.PRIMARY}
                tintColor={isDaXacNhanDanhGia ? colors.GRAY : colors.PRIMARY}
              />
              <Text>Bình thường</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.checkBoxView} onPress={() => onPressKhac(index, item.khac)} disabled={isDaXacNhanDanhGia}>
              <CheckboxComp
                checkboxStyle={{marginHorizontal: spacing.small}}
                disabled
                value={item.khac}
                onFillColor={isDaXacNhanDanhGia ? colors.GRAY : colors.PRIMARY}
                onTintColor={isDaXacNhanDanhGia ? colors.GRAY : colors.PRIMARY}
                tintColor={isDaXacNhanDanhGia ? colors.GRAY : colors.PRIMARY}
              />
              <Text>Có tổn thất</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.checkBoxView} onPress={() => onOpenModalGhiChu(index, item.ghi_chu, item)}>
              <Icon.Feather name="file-text" size={20} color={item.ghi_chu ? colors.PRIMARY : colors.GRAY10} style={{marginHorizontal: 10}} />
              <Text>Ghi chú</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View flex={1}>
        <FlatList data={data} keyExtractor={(item, index) => index.toString()} renderItem={renderItemHangMuc} refreshControl={<RefreshControl refreshing={isLoading} onRefresh={false} />} />
        <View style={styles.footerView}>
          <ButtonLinear title="Quay lại" onPress={() => onPressPage(0)} linearStyle={{marginRight: spacing.small}} />
          <ButtonLinear title="Tiếp tục" onPress={handleDanhGia} loading={isLoading} />
        </View>
      </View>
      <ModalInput onPressLuu={(val) => onSetInputValue(val)} value={valueGhiChu} ref={refModalNhapGhiChu} onBackPress={() => refModalNhapGhiChu.current.hide()} gcnChiTiet={gcnChiTiet} />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {flex: 1},
  footerView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },
  label: {
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
    marginBottom: vScale(spacing.small),
  },
  checkBoxView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  item: {
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    marginVertical: vScale(10),
    paddingVertical: vScale(10),
  },
  content: {
    flex: 1,
    marginHorizontal: scale(spacing.small),
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtPolicy: {
    flex: 1,
    color: colors.GRAY6,
    marginLeft: scale(10),
  },
  checkPolicyView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 0.2,
    paddingTop: vScale(10),
    borderColor: colors.GRAY,
    paddingHorizontal: scale(spacing.medium),
  },
});

const DanhGiaSoBoMemo = memo(DanhGiaSoBoComponent, isEqual);
export const DanhGiaSoBo = DanhGiaSoBoMemo;
