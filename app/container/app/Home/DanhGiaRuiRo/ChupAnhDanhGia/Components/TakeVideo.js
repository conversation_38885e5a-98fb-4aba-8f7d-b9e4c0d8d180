import {Empty, Icon, ModalQuayVideo, ModalVideo, Text} from '@app/components';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {getImageNameFromUriCamera} from '@app/utils/CameraProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import R from '@R';
import React, {forwardRef, memo, useImperativeHandle, useRef} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import prompt from 'react-native-prompt-android';
import ModalVideoOption from './ModalVideoOption';

const TakeVideoComponent = forwardRef(({profileData, listVideo, getListVideo, onRefresh, refreshing}, ref) => {
  useImperativeHandle(ref, () => ({
    showModalQuayVideo: () => refModalQuayVideo.current.show(),
  }));

  let refModalQuayVideo = useRef(null);
  let refModalOptionVideo = useRef(null);
  let refModalVideo = useRef(null);

  const uploadVideo = async (videoData) => {
    // console.log('videoData', videoData);
    prompt(
      'Tên video',
      'Vui lòng nhập tên video',
      [
        {text: 'Để sau'},
        {
          text: 'Đồng ý',
          onPress: async (tenVideo) => {
            refModalQuayVideo.current.showUploading();
            try {
              videoData.name = getImageNameFromUriCamera(videoData.uri);
              let files = [
                {
                  key_file: 'file0',
                },
              ];
              let videosData = [videoData];

              let params = {
                videos: videosData,
                so_id: profileData.ho_so.so_id,
                pm: 'GD',
                ma_doi_tac: profileData.ho_so.ma_doi_tac,
                so_id_dt: profileData.ho_so.so_id_dt,
                files: files,
                ung_dung: 'MOBILE_BT',
                ten: tenVideo,
              };
              // console.log('params', params);
              let response = await ESmartClaimEndpoint.uploadVideo(axiosConfig.ACTION_CODE.UPLOAD_VIDEO, params);
              refModalQuayVideo.current.hideUploading();
              // if (!response && !response.state_info) Alert.alert('Thông báo', response.message);
              if (!response || !response.state_info || response.state_info.status !== 'OK') {
                if (response && response.state_info && response.state_info.status !== 'OK') Alert.alert('Thông báo', response.state_info.message_body);
                else Alert.alert('Thông báo', 'Tải video không thành công');
                return;
              }
              refModalQuayVideo.current.hide();
              FlashMessageHelper.showFlashMessage('Thông báo', 'Tải video thành công', 'success');
              getListVideo();
            } catch (error) {
              Alert.alert('Thông báo', error.message);
            }
          },
        },
      ],
      {
        defaultValue: 'Video đánh giá rủi ro',
        placeholder: 'Tên video',
      },
    );
  };

  /* RENDER */
  const renderVideoItem = ({item}) => {
    return (
      <TouchableOpacity style={styles.videoItemView} onPress={() => refModalVideo.current.show(item)}>
        <View style={styles.imgBackgroundView}>
          <FastImage source={R.images.img_logo_with_name} style={styles.imgVideoBackground} resizeMode="contain" />
          <View style={styles.blurView}>
            <Icon.Ionicons name="play" size={50} color="#FFF" />
          </View>
        </View>
        <Text children={item.ten} style={styles.txtVideoName} />
        <TouchableOpacity onPress={() => refModalOptionVideo.current.show(item)}>
          <Icon.Entypo name="dots-three-vertical" size={20} />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };
  return (
    <View style={styles.contentView}>
      <FlatList
        scrollEnabled={true}
        data={listVideo}
        renderItem={renderVideoItem}
        keyExtractor={(item, index) => index + ''}
        ListEmptyComponent={<Empty />}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
        style={styles.listVideo}
      />
      <ModalVideoOption ref={refModalOptionVideo} getListVideo={getListVideo} />
      <ModalVideo ref={refModalVideo} />
      <ModalQuayVideo ref={refModalQuayVideo} uploadVideo={uploadVideo} />
    </View>
  );
});

const styles = StyleSheet.create({
  contentView: {
    flex: 1,
  },

  btnVideoBorderView: {
    borderWidth: 3,
    borderColor: '#000',
    borderRadius: 50,
  },
  footerView: {
    position: 'absolute',
    bottom: 10,
    right: 0,
    left: 0,
    paddingVertical: 10,
    marginHorizontal: spacing.small,
  },
  videoItemView: {
    flexDirection: 'row',
    marginBottom: spacing.small,
  },
  imgVideoBackground: {
    width: dimensions.width * 0.35,
    height: dimensions.width * 0.35 * 0.6,
    opacity: 0.3,
  },
  imgBackgroundView: {
    // borderWidth: 1,
    paddingHorizontal: spacing.small,
    borderRadius: 10,
  },
  txtVideoName: {
    marginLeft: spacing.small,
    flex: 1,
    fontWeight: '600',
    paddingTop: spacing.smaller,
  },
  blurView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listVideo: {marginHorizontal: spacing.small, paddingTop: spacing.small, flex: 1, marginBottom: 70, marginTop: spacing.small, borderTopLeftRadius: 10, borderTopRightRadius: 10},
});

const TakeVideoMemo = memo(TakeVideoComponent, isEqual);
export const TakeVideo = TakeVideoMemo;
