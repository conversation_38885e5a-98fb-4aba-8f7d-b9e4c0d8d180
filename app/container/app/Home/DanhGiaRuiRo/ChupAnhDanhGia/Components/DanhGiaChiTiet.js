import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, CustomActionSheet, Icon, Loading, TextInputOutlined, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectMucDoTonThat} from '@app/redux/slices/CategoryCommonSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useFieldArray, useForm} from 'react-hook-form';
import {Alert, FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';
import {ModalHangMuc} from '../Components';

const listDanhGia = [
  {id: 0, dgia: 'BT', dgia_ten: 'Bình thường'},
  {id: 1, dgia: 'CD1', dgia_ten: 'Mức độ nhẹ'},
  {id: 2, dgia: 'CD2', dgia_ten: 'Mức độ trung bình'},
  {id: 3, dgia: 'CD3', dgia_ten: 'Mức độ nặng'},
];
const getDanhGiaOptions = () => {
  let listTmp = [];
  if (listDanhGia.length > 0) listDanhGia.forEach((item) => listTmp.push(item.dgia_ten));
  return listTmp;
};
const getDanhGiaTitleById = (id) => listDanhGia.find((item) => item.dgia === id).dgia_ten;
const getDanhGiaIdByTitle = (ten) => listDanhGia.find((item) => item.dgia_ten === ten).dgia;

const DanhGiaChiTietComponent = ({gcnChiTiet, onPressPage, currentPosition}) => {
  console.log('DanhGiaChiTietComponent');
  const isDaXacNhanDanhGia = gcnChiTiet.danh_gia === 'DXN';
  const [disableBtnTiepTuc, setDisableBtnTiepTuc] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const refModalHangMuc = useRef(null);
  const mucDoTonThat = useSelector(selectMucDoTonThat).filter((item) => item.nhom === 'XE');
  const refFlatList = useRef(null);
  const refCustomActionSheetMucDoTonThat = useRef(null);
  const refCustomActionSheetDanhGia = useRef(null);
  const [hangMucSelected, setHangMucSelected] = useState(null);

  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors},
  } = useForm({
    // defaultValues: {}; you can populate the fields by this attribute
  });

  const {fields, append, remove, update} = useFieldArray({
    control,
    name: 'formHangMucDanhGiaAI',
  });
  const [listHangMucDanhGiaAI, setListHangMucDanhGiaAI] = useState([]);
  useEffect(() => {
    layDSHangMucDanhGiaAI();
  }, []);

  //set lại value vào form khi có dữ liệu
  const initFormData = (data) => {
    data.map((item, index) => {
      update(index, {
        mucDoTonThat: item.muc_do,
        ghiChu: item.ghi_chu,
        danhGia: item.dgia,
      });
    });
  };

  const layDSHangMucDanhGiaAI = async () => {
    setIsLoadingData(true);
    let params = {
      so_id_hd: gcnChiTiet?.so_id_hd,
      so_id_dt: gcnChiTiet?.so_id_dt,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HANG_MUC_DANH_GIA_AI, params);
      setIsLoadingData(false);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      response.data_info = response.data_info.map((item) => {
        item.isExpand = false;
        item.isMucDoExpand = false;
        item.isDanhGiaExpand = false;
        return item;
      });
      // LƯU Ý LƯU Ý
      initFormData(response.data_info); //INIT FORM TRƯỚC
      setListHangMucDanhGiaAI(response.data_info); //UPDATE LIST SAU nếu không thì LIST SẼ K UPDATE GIÁ TRỊ TỪ FORM
    } catch (error) {
      setIsLoadingData(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const luuDanhGiaHangMucAI = async (data) => {
    if (isDaXacNhanDanhGia) {
      onPressPage(3);
      return;
    }
    if (data.formHangMucDanhGiaAI.length === 0) {
      onPressPage(3);
      return;
    }
    try {
      let arrHangMuc = [],
        arrHangMucTen = [],
        arrMucDo = [],
        arrMucDoTen = [],
        arrDanhGia = [],
        arrGhiChu = [];
      data.formHangMucDanhGiaAI.forEach((item, index) => {
        arrHangMuc.push(listHangMucDanhGiaAI[index].hang_muc);
        arrHangMucTen.push(listHangMucDanhGiaAI[index].hang_muc_ten);
        arrMucDo.push(item.mucDoTonThat);
        arrMucDoTen.push(item.mucDoTonThat ? getMucDoTitleById(item.mucDoTonThat) : '');
        arrDanhGia.push(item.danhGia);
        arrGhiChu.push(item.ghiChu);
      });
      let params = {
        so_id_hd: gcnChiTiet?.so_id_hd,
        so_id_dt: gcnChiTiet?.so_id_dt,
        arr_hang_muc: arrHangMuc,
        arr_hang_muc_ten: arrHangMucTen,
        arr_muc_do: arrMucDo,
        arr_muc_do_ten: arrMucDoTen,
        arr_dgia: arrDanhGia,
        arr_ghi_chu: arrGhiChu,
        so_id_lan: gcnChiTiet.so_id_lan_dgrr,
      };
      setDisableBtnTiepTuc(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DANH_GIA_HANG_MUC, params);
      setDisableBtnTiepTuc(false);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Đánh giá chi tiết thành công', 'success');
      onPressPage(3);
    } catch (error) {
      setDisableBtnTiepTuc(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const getMucDoTitleById = (id) => mucDoTonThat.find((item) => item.ma === id).ten;
  const getMucDoIdByTitle = (ten) => mucDoTonThat.find((item) => item.ten === ten).ma;

  const onToggleDropDownMucDo = (index) => {
    if (isDaXacNhanDanhGia) return;
    setHangMucSelected(index);
    refCustomActionSheetMucDoTonThat.current.show();
  };

  const onToggleDropDownDanhGia = (index) => {
    if (isDaXacNhanDanhGia) return;
    setHangMucSelected(index);
    refCustomActionSheetDanhGia.current.show();
  };

  const removeDanhGiaItem = (item, index) => {
    Alert.alert('Thông báo', 'Bạn có muốn xoá ' + item.hang_muc_ten + '?', [
      {
        text: 'Đồng ý',
        onPress: () => {
          let listHangMucDanhGiaAITmp = listHangMucDanhGiaAI;
          listHangMucDanhGiaAITmp.splice(index, 1);
          setListHangMucDanhGiaAI([...listHangMucDanhGiaAITmp]);
          remove(index);
        },
      },
      {
        text: 'Để sau',
        style: 'destructive',
      },
    ]);
  };
  const onPressThemHangMuc = () => refModalHangMuc.current.show();
  const addHangMucMoi = (hangMucMoi) => {
    let listHangMucDanhGiaAITmp = listHangMucDanhGiaAI;
    if (listHangMucDanhGiaAITmp.findIndex((item) => item.hang_muc === hangMucMoi.ma) !== -1) return; //kiểm tra xem hạng mục đấy đã có chưa
    listHangMucDanhGiaAITmp.push({
      hang_muc: hangMucMoi.ma,
      hang_muc_ten: hangMucMoi.ten,
      isDanhGiaExpand: false,
      isExpand: false,
      isMucDoExpand: false,
      dgia: '',
      dgia_ten: '',
      ghi_chu: '',
      muc_do: '',
      muc_do_ten: '',
    });
    append({
      mucDoTonThat: '',
      ghiChu: '',
      danhGia: '',
    });
    setListHangMucDanhGiaAI([...listHangMucDanhGiaAITmp]);
    setTimeout(() => {
      refFlatList.current.scrollToEnd({animation: true});
    }, 100);
  };
  const getMucDoTonThatOptions = () => {
    let listTmp = [];
    if (mucDoTonThat.length > 0) mucDoTonThat.forEach((item) => listTmp.push(item.ten));
    return listTmp;
  };

  const onSelectMucDo = (item, index) => setValue('formHangMucDanhGiaAI.' + hangMucSelected + '.mucDoTonThat', getMucDoIdByTitle(item));
  const onSelectDanhGia = (item, index) => setValue('formHangMucDanhGiaAI.' + hangMucSelected + '.danhGia', getDanhGiaIdByTitle(item));
  const xemChiTietAnhHangMuc = async (hangMucDuocChon) => {
    try {
      let params = {
        ma_doi_tac: gcnChiTiet.ma_doi_tac,
        so_id_hd: gcnChiTiet.so_id_hd,
        so_id_dt: gcnChiTiet.so_id_dt,
      };
      let response = await ESmartClaimEndpoint.getFileThumbnail(axiosConfig.ACTION_CODE.LIST_ANH_DA_UPLOAD, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let anhHangMuc = response.data_info.filter((item) => item.ma_file === hangMucDuocChon.hang_muc);
      NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
        currentImageData: {
          item: anhHangMuc[0],
          index: 0,
        },
        imagesData: anhHangMuc,
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  /* RENDER */
  const renderHangMucDanhGiaItem = ({item, index}) => {
    return (
      <View style={styles.itemHangMucView}>
        <View style={{paddingLeft: spacing.small}}>
          <View style={styles.danhGiaItemTitleView}>
            <TouchableOpacity style={styles.tenHangMucView}>
              <Text children={item.hang_muc_ten} style={styles.txtHangMuc} />
              {/* <Icon.Entypo name="chevron-thin-down" size={20} /> */}
            </TouchableOpacity>
            <View style={styles.danhGiaItemBtnView}>
              {!isDaXacNhanDanhGia && (
                <TouchableOpacity
                  style={styles.btnIconImageView}
                  onPress={() =>
                    NavigationUtil.push(SCREEN_ROUTER_APP.CHUP_ANH_DANH_GIA_CHI_TIET, {
                      gcnChiTiet: gcnChiTiet,
                      hangMucDuocChon: item,
                    })
                  }>
                  <Icon.MaterialIcons name="add-a-photo" size={20} color={colors.PRIMARY} />
                </TouchableOpacity>
              )}
              <TouchableOpacity style={styles.btnIconImageView} onPress={() => xemChiTietAnhHangMuc(item)}>
                <Icon.EvilIcons name="image" size={25} color={colors.PRIMARY} />
              </TouchableOpacity>
              {!isDaXacNhanDanhGia && (
                <TouchableOpacity onPress={() => removeDanhGiaItem(item, index)}>
                  <Icon.EvilIcons name="trash" size={25} color={colors.RED1} />
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View style={{flexDirection: 'row', flex: 1, marginTop: spacing.small}}>
            <View style={{flex: 1}}>
              <Controller
                control={control}
                name={'formHangMucDanhGiaAI.' + index + '.mucDoTonThat'}
                render={({field: {onChange, value}}) => {
                  return (
                    <View style={styles.mucDoTonThatView}>
                      <Text children="Mức độ tổn thất" style={styles.txtMucDoTitle} />
                      <View style={[styles.dropdownView]}>
                        <TouchableOpacity style={[styles.mucDoTitleView]} onPress={() => onToggleDropDownMucDo(index)}>
                          <Text children={value ? getMucDoTitleById(value) : 'Chọn mức độ tổn thất'} />
                          <Icon.Entypo name="chevron-small-down" size={20} />
                        </TouchableOpacity>
                      </View>
                      {/* <View
                        style={[
                          styles.dropdownView,
                          // errors && errors.formHangMucDanhGiaAI && errors.formHangMucDanhGiaAI[index]?.mucDoTonThat && {borderColor: colors.RED1}
                        ]}>
                        <TouchableOpacity style={[styles.mucDoTitleView, item.isMucDoExpand && styles.mucDoTitleActiveView]} onPress={() => onToggleDropDownMucDo(index)}>
                          <Text children={value ? getMucDoTitleById(value) : 'Chọn mức độ tổn thất'} />
                          <Icon.Entypo name="chevron-small-down" size={20} />
                        </TouchableOpacity>

                        {item.isMucDoExpand &&
                          mucDoTonThat.map((itemMucDo, indexMucDo) => {
                            return (
                              <TouchableOpacity
                                key={indexMucDo}
                                style={[styles.btnMucDoView]}
                                onPress={() => {
                                  onChange(itemMucDo.ma);
                                  onToggleDropDownMucDo(index);
                                }}>
                                <Text children={itemMucDo.ten} style={{color: value === itemMucDo.ma ? colors.PRIMARY : '#000'}} />
                                <Icon.Feather name="check" size={20} color={value === itemMucDo.ma ? colors.PRIMARY : 'transparent'} />
                              </TouchableOpacity>
                            );
                          })}
                      </View> */}
                      {/* {errors && errors.formHangMucDanhGiaAI && errors.formHangMucDanhGiaAI[index]?.mucDoTonThat && <Text children={'Vui lòng chọn mức độ tổn thất'} style={{color: colors.RED1}} />} */}
                    </View>
                  );
                }}
              />
            </View>
            <View style={{flex: 1}}>
              <Controller
                control={control}
                name={'formHangMucDanhGiaAI.' + index + '.danhGia'}
                render={({field: {onChange, value}}) => (
                  <View style={styles.mucDoTonThatView}>
                    <Text children="Đánh giá" style={styles.txtMucDoTitle} />
                    <View style={[styles.dropdownView]}>
                      <TouchableOpacity style={[styles.mucDoTitleView]} onPress={() => onToggleDropDownDanhGia(index)}>
                        <Text children={value ? getDanhGiaTitleById(value) : 'Chọn đánh giá'} />
                        <Icon.Entypo name="chevron-small-down" size={20} />
                      </TouchableOpacity>
                    </View>
                    {/* <View style={[styles.dropdownView]}>
                      <TouchableOpacity style={[styles.mucDoTitleView, item.isDanhGiaExpand && styles.mucDoTitleActiveView]} onPress={() => onToggleDropDownDanhGia(index)}>
                        <Text children={value ? getDanhGiaTitleById(value) : 'Chọn đánh giá'} />
                        <Icon.Entypo name="chevron-small-down" size={20} />
                      </TouchableOpacity>

                      {item.isDanhGiaExpand &&
                        listDanhGia.map((itemDanhGia, indexDanhGia) => {
                          return (
                            <TouchableOpacity
                              key={indexDanhGia}
                              style={[styles.btnMucDoView]}
                              onPress={() => {
                                onChange(itemDanhGia.dgia);
                                onToggleDropDownDanhGia(index);
                              }}>
                              <Text children={itemDanhGia.dgia_ten} style={{color: value === itemDanhGia.dgia ? colors.PRIMARY : '#000'}} />
                              <Icon.Feather name="check" size={20} color={value === itemDanhGia.dgia ? colors.PRIMARY : 'transparent'} />
                            </TouchableOpacity>
                          );
                        })}
                    </View> */}
                  </View>
                )}
              />
            </View>
          </View>

          <Controller
            control={control}
            name={'formHangMucDanhGiaAI.' + index + '.ghiChu'}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                containerStyle={[styles.inputView, {marginRight: spacing.small}]}
                title="Ghi chú"
                placeholder="Ghi chú"
                value={value}
                onChangeText={onChange}
                returnKeyType={'next'}
                blurOnSubmit={false}
                titleStyle={{fontWeight: '700'}}
                editable={!isDaXacNhanDanhGia}
              />
            )}
          />
        </View>
        {index !== listHangMucDanhGiaAI.length - 1 && <View style={styles.spacingView} />}
      </View>
    );
  };
  // const viewConfigRef = React.useRef({viewAreaCoveragePercentThreshold: 3});
  // const onViewCallBack = React.useCallback((viewableItems) => {
  //   console.log(viewableItems);
  //   // Use viewable items in state or as intended
  // }, []); // any dependencies that require the function to be "redeclared"

  return (
    <View style={styles.container}>
      {isLoadingData ? (
        <Loading />
      ) : (
        <View style={{flex: 1}}>
          <KeyboardAwareScrollView contentContainerStyle={{flex: 1}}>
            <FlatList
              ref={refFlatList}
              data={listHangMucDanhGiaAI}
              keyExtractor={(item) => item.hang_muc}
              renderItem={renderHangMucDanhGiaItem}
              style={[styles.listHangMuc]}
              removeClippedSubviews={false} //INPUT TRONG FLATLIST PHẢI THÊM THUỘC TÍNH NÀY NẾU KHÔNG BÀN PHÍM SẼ TỰ ĐỘNG DISMISS
              ListFooterComponent={
                <>
                  {!isDaXacNhanDanhGia && (
                    <TouchableOpacity style={styles.themHangMucView} onPress={onPressThemHangMuc}>
                      <Icon.AntDesign name="plussquare" size={15} color={colors.PRIMARY} />
                      <Text children="Thêm hạng mục" style={styles.txtThemHangMuc} />
                    </TouchableOpacity>
                  )}
                </>
              }
              // onViewableItemsChanged={onViewCallBack}
              // viewabilityConfig={viewConfigRef.current}
            />
            <View style={styles.footerView}>
              <ButtonLinear title="Quay lại" onPress={() => onPressPage(1)} linearStyle={{marginRight: spacing.small}} />
              <ButtonLinear title="Tiếp tục" onPress={handleSubmit(luuDanhGiaHangMucAI)} loading={disableBtnTiepTuc} />
            </View>
          </KeyboardAwareScrollView>
        </View>
      )}
      <ModalHangMuc ref={refModalHangMuc} onHangMucSelected={addHangMucMoi} />
      {/* <ModalCameraDanhGiaChiTiet ref={refModalCameraDanhGiaChiTiet} listAnhDaUpload={listAnhDaUpload} /> */}
      {/* <ModalAnhDanhGiaChiTiet ref={refModalAnhDanhGiaChiTiet} listAnhDaUpload={listAnhDaUpload} gcnChiTiet={gcnChiTiet} /> */}
      <CustomActionSheet
        title={'Mức độ tổn thất ' + (hangMucSelected !== null ? listHangMucDanhGiaAI[hangMucSelected].hang_muc_ten : '')}
        ref={refCustomActionSheetMucDoTonThat}
        onSelected={(item, index) => onSelectMucDo(item, index)}
        options={getMucDoTonThatOptions()}
      />
      <CustomActionSheet
        title={'Đánh giá ' + (hangMucSelected !== null ? listHangMucDanhGiaAI[hangMucSelected].hang_muc_ten : '')}
        ref={refCustomActionSheetDanhGia}
        onSelected={(item, index) => onSelectDanhGia(item, index)}
        options={getDanhGiaOptions()}
      />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {flex: 1},
  footerView: {
    backgroundColor: '#FFF',
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: colors.GRAY,
    borderTopWidth: 0.2,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },
  listHangMuc: {},
  mucDoTonThatView: {
    marginRight: scale(spacing.small),
  },
  mucDoTonThatViewButton: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: vScale(spacing.tiny),
  },
  txtMucDoTitle: {
    fontWeight: '700',
    fontSize: FontSize.size14,
    marginBottom: vScale(spacing.tiny),
  },
  spacingView: {
    height: 2,
    backgroundColor: colors.GRAY2,
  },
  itemHangMucView: {
    backgroundColor: '#FFF',
    marginBottom: vScale(spacing.small),
  },
  tenHangMucView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
  dropdownView: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.GRAY,
    paddingHorizontal: scale(spacing.small),
  },
  btnMucDoView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: vScale(spacing.small),
  },
  mucDoTitleView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: vScale(10),
    justifyContent: 'space-between',
  },
  mucDoTitleActiveView: {
    borderBottomWidth: 0.5,
    marginBottom: vScale(spacing.small),
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  themHangMucView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: scale(spacing.small),
  },
  txtThemHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
    marginLeft: scale(spacing.tiny),
  },
  danhGiaItemTitleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: scale(spacing.small),
  },
  danhGiaItemBtnView: {
    flexDirection: 'row',
    flex: 0,
  },
  btnIconImageView: {
    marginRight: scale(spacing.small),
  },
});

const DanhGiaChiTietMemo = memo(DanhGiaChiTietComponent, isEqual);
export const DanhGiaChiTiet = DanhGiaChiTietMemo;
