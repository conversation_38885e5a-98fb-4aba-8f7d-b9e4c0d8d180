import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, CheckboxComp, Icon, Text} from '@app/components';
import Loading from '@app/components/Loading';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';

const DanhGiaXacNhanComponent = ({gcnChiTiet, onPressPage, updateGCN}) => {
  console.log('DanhGiaXacNhanComponent');
  const isDaXacNhanDanhGia = gcnChiTiet.danh_gia === 'DXN';
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [listHangMucDanhGiaAI, setListHangMucDanhGiaAI] = useState([]);
  const [danhGiaSoBoData, setDanhGiaSoBoData] = useState([]);
  const [anhHoSo, setAnhHoSo] = useState([]);
  const [isCheckPolicy, setIsCheckPolicy] = useState(false);
  useEffect(() => {
    layDSHangMucDanhGiaAI();
    layChiTietDanhGia();
    // getAnhDaChup();
  }, []);
  //set lại value vào form khi có dữ liệu
  const getAnhDaChup = async () => {
    try {
      let params = {
        ma_doi_tac: gcnChiTiet.ma_doi_tac,
        so_id_hd: gcnChiTiet.so_id_hd,
        so_id_dt: gcnChiTiet.so_id_dt,
      };
      let response = await ESmartClaimEndpoint.getFileThumbnail(axiosConfig.ACTION_CODE.LIST_ANH_DA_UPLOAD, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let imgsGroup = groupBy(response.data_info, 'ma_file'); //return object
      let menuImage = [];
      for (const property in imgsGroup) {
        menuImage.push({
          checked: false,
          images: imgsGroup[property],
          ma: imgsGroup[property][0].ma_file,
          ten: imgsGroup[property][0].nhom_anh,
          bt: imgsGroup[property][0].bt,
        });
      }
      // setImageData(response.data_info);
      let imagesTmp = response.data_info.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
        };
        item.nhom = nhom;
        return item;
      });
      setAnhHoSo([...imagesTmp]);
    } catch (error) {
      //   setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const groupBy = (xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  };
  const onPressGuiXacNhanBB = () => {
    // refModalGuiBBXacNhanDanhGiaRuiRo.current.show();
    NavigationUtil.push(SCREEN_ROUTER_APP.BIEN_BAN_DANH_GIA_RUI_RO, {gcnChiTiet: gcnChiTiet});
  };
  const layDSHangMucDanhGiaAI = async () => {
    setIsLoadingData(true);
    let params = {
      so_id_hd: gcnChiTiet.so_id_hd,
      so_id_dt: gcnChiTiet.so_id_dt,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HANG_MUC_DANH_GIA_AI, params);
      setIsLoadingData(false);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      response.data_info = response.data_info.map((item) => {
        item.isExpand = false;
        item.isMucDoExpand = false;
        item.isDanhGiaExpand = false;
        return item;
      });
      setListHangMucDanhGiaAI(response.data_info);
    } catch (error) {
      setIsLoadingData(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const layChiTietDanhGia = async () => {
    try {
      let params = {
        ma_doi_tac: gcnChiTiet.ma_doi_tac,
        so_id_hd: gcnChiTiet.so_id_hd,
        so_id_dt: gcnChiTiet.so_id_dt,
        so_id_lan: gcnChiTiet.so_id_lan_dgrr,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DANH_GIA_RR, params);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      initDataDaDanhGia(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const initDataDaDanhGia = (hangMucAnh) => {
    if (hangMucAnh.length === 0) return;
    let dataHangMuc = hangMucAnh.map((e) => {
      e.hang_muc = e.ma;
      e.ghi_chu = e.ghi_chu;
      if (e.danh_gia === 'BT') {
        e.bt = true;
        e.khac = false;
      } else {
        e.bt = false;
        e.khac = true;
      }
      return e;
    });
    setDanhGiaSoBoData(dataHangMuc);
  };

  const onPressXacNhan = async () => {
    setIsLoading(true);
    let params = {
      ma_doi_tac: gcnChiTiet.ma_doi_tac,
      ma_doi_tac_ql: gcnChiTiet.ma_doi_tac,
      so_id: gcnChiTiet.so_id_hd,
      so_id_hd: gcnChiTiet.so_id_hd,
      so_id_dt: gcnChiTiet.so_id_dt,
      so_id_lan: gcnChiTiet.so_id_lan_dgrr,
      create_file: 'ESCS_BIEN_BAN_DGRR',
      remove_file: 'ESCS_BIEN_BAN_DGRR',
      pm: 'BH',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XAC_NHAN_DANH_GIA_RUI_RO, params);
      setIsLoading(false);
      if (!response || !response.data_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Đánh giá rủi ro thành công', 'success');
      updateGCN({
        ...gcnChiTiet,
        danh_gia: 'DXN',
      });
      // NavigationUtil.pop();
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  /* RENDER */
  const renderHangMucDanhGiaItem = ({item, index}) => {
    return (
      <View style={[styles.itemHangMucView, index === listHangMucDanhGiaAI.length - 1 && {borderBottomWidth: 0}]}>
        <View style={{paddingLeft: spacing.small}}>
          <View style={styles.danhGiaItemTitleView}>
            <TouchableOpacity style={styles.tenHangMucView}>
              <Text children={item.hang_muc_ten} style={styles.txtHangMuc} />
              {/* <Icon.Entypo name="chevron-thin-down" size={20} /> */}
            </TouchableOpacity>
            <View style={styles.danhGiaItemBtnView}>
              <TouchableOpacity style={styles.btnIconImageView}>
                <Icon.Ionicons name="image" size={20} color={colors.PRIMARY} />
              </TouchableOpacity>
            </View>
          </View>
          <View style={{flex: 1, marginTop: spacing.small, flexDirection: 'row'}}>
            <View style={styles.mucDoTonThatView}>
              <Text children="Mức độ tổn thất: " />
              <Text children={item.dgia && item.muc_do_ten} style={styles.txtMucDoTitle} />
            </View>

            <View style={[styles.mucDoTonThatView, {borderLeftWidth: 1, paddingLeft: spacing.small}]}>
              <Text children="Đánh giá: " />
              <Text children={item.dgia && item.dgia_ten} style={styles.txtMucDoTitle} />
            </View>
          </View>
          <View style={{flex: 1, marginTop: spacing.small}}>
            <View style={[styles.mucDoTonThatView, {flexDirection: 'column'}]}>
              <Text children={'Ghi chú: ' + (item.ghi_chu || '')} />
            </View>
          </View>
        </View>
        {/* {index !== listHangMucDanhGiaAI.length - 1 && <View style={styles.spacingView} />} */}
      </View>
    );
  };
  const renderItemDanhGiaSoBo = ({item, index}) => {
    return (
      <View style={[styles.itemHangMucView, index === danhGiaSoBoData.length - 1 && {borderBottomWidth: 0}]}>
        <View marginHorizontal={10}>
          <View style={styles.danhGiaItemTitleView}>
            <TouchableOpacity style={styles.tenHangMucView}>
              <Text children={item.ten} style={styles.txtHangMuc} />
            </TouchableOpacity>
            <View style={styles.danhGiaItemBtnView}>
              <TouchableOpacity style={styles.btnIconImageView}>
                <Icon.Ionicons name="image" size={20} color={colors.PRIMARY} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={{flexDirection: 'row', marginTop: spacing.small}}>
            <Text children={'Tình trạng: '} />
            <Text children={item.bt ? 'Bình thường' : 'Có tổn thất'} style={styles.txtMucDoTitle} />
          </View>
          <View style={[styles.mucDoTonThatView, {marginTop: spacing.small}]}>
            <Text children={'Ghi chú: ' + (item.ghi_chu || '')} />
          </View>
        </View>
      </View>
    );
  };
  return (
    <View style={styles.container}>
      {isLoadingData ? (
        <Loading />
      ) : (
        <View flex={1}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <FlatList
              data={listHangMucDanhGiaAI}
              keyExtractor={(item) => item.hang_muc}
              renderItem={renderHangMucDanhGiaItem}
              style={styles.listHangMuc}
              scrollEnabled={false}
              ListHeaderComponent={<Text children="Thông tin hạng mục tổn thất" style={styles.txtListHeader} />}
            />
            <FlatList
              data={danhGiaSoBoData}
              keyExtractor={(item) => item.stt + ''}
              renderItem={renderItemDanhGiaSoBo}
              scrollEnabled={false}
              ListHeaderComponent={<Text children="Thông tin chung" style={styles.txtListHeader} />}
            />
          </ScrollView>
          {!isDaXacNhanDanhGia && (
            <TouchableOpacity style={styles.checkPolicyView} onPress={() => setIsCheckPolicy((prevValue) => !prevValue)}>
              <CheckboxComp value={isCheckPolicy} disabled />
              <Text style={styles.txtPolicy} children="Tôi cam kết các thông tin khai báo trên là trung thực" />
            </TouchableOpacity>
          )}

          <View style={styles.footerView}>
            <ButtonLinear title="Quay lại" onPress={() => onPressPage(2)} />
            {!isDaXacNhanDanhGia && (
              <ButtonLinear
                disabled={!isCheckPolicy}
                linearColors={!isCheckPolicy ? [colors.GRAY, colors.GRAY] : [colors.GREEN_LIGHT, colors.GREEN]}
                textStyle={!isCheckPolicy ? {color: colors.BLACK_03} : {color: colors.WHITE}}
                linearStyle={{marginLeft: spacing.small}}
                title="Xác nhận"
                onPress={onPressXacNhan}
                loading={isLoading}
              />
            )}
            {isDaXacNhanDanhGia && <ButtonLinear linearStyle={{marginLeft: spacing.small}} title="Gửi xác nhận BB" onPress={onPressGuiXacNhanBB} />}
          </View>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {flex: 1},
  footerView: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },
  listHangMuc: {},
  mucDoTonThatView: {
    flex: 1,
    flexDirection: 'row',
    marginRight: scale(spacing.small),
  },
  txtListHeader: {
    color: '#FFF',
    fontWeight: 'bold',
    backgroundColor: colors.PRIMARY,
    paddingLeft: scale(spacing.smaller),
    marginBottom: vScale(spacing.smaller),
    paddingVertical: vScale(spacing.smaller),
  },
  mucDoTonThatViewButton: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    marginTop: vScale(spacing.tiny),
  },
  txtMucDoTitle: {
    fontWeight: '700',
    fontSize: FontSize.size14,
    marginBottom: vScale(spacing.tiny),
  },
  spacingView: {
    height: 2,
    backgroundColor: colors.GRAY2,
    marginTop: vScale(spacing.small),
  },
  itemHangMucView: {
    backgroundColor: '#FFF',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    marginBottom: vScale(spacing.small),
    paddingBottom: vScale(spacing.small),
  },
  tenHangMucView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  danhGiaItemTitleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: scale(spacing.small),
  },
  danhGiaItemBtnView: {
    flexDirection: 'row',
  },
  btnIconImageView: {
    // marginRight: spacing.small,
  },
  txtPolicy: {
    flex: 1,
    color: colors.GRAY6,
    marginLeft: scale(10),
    fontSize: FontSize.size14,
  },
  checkPolicyView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    paddingTop: vScale(spacing.tiny),
    paddingHorizontal: scale(spacing.small),
  },
});

const DanhGiaXacNhanMemo = memo(DanhGiaXacNhanComponent, isEqual);
export const DanhGiaXacNhan = DanhGiaXacNhanMemo;
