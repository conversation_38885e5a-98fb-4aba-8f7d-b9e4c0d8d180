import {spacing} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';
import {colors} from '@app/commons/Theme';

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  stepIndicator: {
    marginVertical: spacing.small,
  },
  stepLabelSelected: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GREEN,
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GRAY10,
  },

  inforView: {
    flex: 1,
    paddingLeft: 16,
    paddingVertical: 13,
    borderColor: colors.GRAY4,
    borderBottomWidth: 0.5,
    backgroundColor: colors.WHITE,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: width - 20,
  },
  txtTitle: {
    marginBottom: 4,
    fontSize: 12,
  },
  txtDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    paddingRight: 10,
    flexShrink: 1,
  },
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
  },
  iconBtnTopLeftView: {
    marginRight: 15,
    // borderWidth: 1,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    marginHorizontal: 15,
    alignSelf: 'center',
  },
  centerView: {
    marginTop: 5,
    // marginBottom: 50,
    flex: 1,
  },
  titleBangView: {
    flexDirection: 'row',
    borderColor: colors.GRAY1,
  },
  titleBang: {
    flex: 1,
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 14,
    paddingVertical: 10,
  },
  itemThanhToanView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 10,
    borderRadius: 20,
  },
  txtThanhToan: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 10,
    paddingHorizontal: 3,
    // flexShrink: 1,
  },
  txtTongCong: {
    fontWeight: 'bold',
    fontSize: 14,
    flex: 2,
    textAlign: 'center',
    paddingVertical: 10,
  },
  valueTongCong: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 10,
  },
  tongCongView: {
    flexDirection: 'row',
    // borderTopWidth: 1,
    borderColor: colors.GRAY1,
    paddingRight: 10,
  },
  rowThanhToan: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: 10,
  },
  lhvnRow: {
    flexDirection: 'row',
  },
  lhnvValue: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  lhnvTitle: {
    flex: 1,
    // textAlignVertical: 'center',
  },
  lhnvItemView: {
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    // borderBottomLeftRadius: 3,
    borderColor: colors.GRAY,
    paddingLeft: 20,
    paddingRight: 10,
  },
  lhnvTongTienValue: {
    // fontWeight: 'bold',
    fontSize: 16,
    flex: 1,
    textAlign: 'right',
    color: colors.PRIMARY,
  },
  lhnvTongTienTitle: {
    fontSize: 16,
    flex: 1,
    color: colors.PRIMARY,
  },
  lhnvStyles: {
    paddingRight: 0,
  },
  footerView: {
    paddingVertical: 10,
    borderTopWidth: 0.2,
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingHorizontal: spacing.medium,
  },
});
