import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@component';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {SafeAreaView, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';
import styles from './ChiTietGiayChungNhanStyle';

const profileHeaderTitle = ['Thông tin giấy chứng nhận', 'Tình trạng thanh toán phí', 'Lo<PERSON>i hình nghiệp vụ tham gia'];
const iconHeader = ['list-ol', 'money', 'eercast', 'list-ol', 'file-text-o', 'user-circle-o', 'list-ol', 'legal', 'car', 'empire', 'random', 'tasks', 'money'];

const ChiTietGiayChungNhanScreenComponent = (props) => {
  console.log('ChiTietGiayChungNhanScreenComponent');
  const {route, navigation} = props;
  const {gcnChiTiet, hangMucAnh, hopDong} = route?.params;
  // const [gcnChiTiet, setGcnChiTiet] = useState(null);

  /** RENDER */
  const renderThongTinGCN = () => {
    if (!gcnChiTiet) return;
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View style={{flex: 1, backgroundColor: colors.WHITE}}>
          {renderProfileInformationHeader(profileHeaderTitle[0])}
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Tên khách hàng</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.ten || ''}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Biển xe</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.bien_xe || ''}</Text>
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Chủ xe</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.ten_chu_xe || ''}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Hãng xe - Hiệu xe</Text>
              <Text style={styles.txtDetail}>{(gcnChiTiet?.hang_xe || '') + ' - ' + (gcnChiTiet?.hieu_xe || '')}</Text>
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số khung</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.so_khung || ''}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số máy</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.so_may || ''}</Text>
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Năm sản xuất</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.nam_sx || ''}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Giá trị xe</Text>
              <NumericFormat value={gcnChiTiet?.gia_tri} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.txtDetail} children={value} />} />
              {/* <Text style={styles.txtDetail}>{gcnChiTiet?.gia_tri || ''}</Text> */}
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Loại xe</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.loai_xe || ''}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Mục đích sử dụng</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.muc_dich_hthi || ''}</Text>
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số chỗ - Trọng tải</Text>
              <Text style={styles.txtDetail}>{(gcnChiTiet?.so_cho_hthi || '') + ' - ' + (gcnChiTiet?.trong_tai_hthi || '')}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số hợp đồng</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.so_hd || ''}</Text>
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Số giấy chứng nhận</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.gcn || ''}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Tổng phí</Text>
              <NumericFormat value={gcnChiTiet?.tong_phi} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.txtDetail} children={value} />} />
              {/* <Text style={styles.txtDetail}>{gcnChiTiet?.tong_phi_hthi || ''}</Text> */}
            </View>
          </View>
          <View style={{flexDirection: 'row'}}>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Loại hình bảo hiểm</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.loai_bh || ''}</Text>
            </View>
            <View style={styles.inforView}>
              <Text style={styles.txtTitle}>Hiệu lực bảo hiểm</Text>
              <Text style={styles.txtDetail}>{gcnChiTiet?.hieu_luc_gcn || ''}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderProfileInformationHeader = (title, data) => {
    let indexIcon = profileHeaderTitle.findIndex((item) => item == title);
    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <View style={[styles.inforHeaderView]}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name={iconHeader[indexIcon]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>{title + ' ' + dataLength}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle={'Giấy chứng nhận'}
      renderView={
        <SafeAreaView style={styles.container}>
          <View flex={1}>
            {renderThongTinGCN()}
            <View style={styles.footerView}>
              <ButtonLinear title="Chụp ảnh" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.CHUP_ANH_DANH_GIA, {hangMucAnh: hangMucAnh, hopDong: hopDong, gcnChiTiet: gcnChiTiet})} />
            </View>
          </View>
        </SafeAreaView>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  user: state.user.data,
  notificationFirebase: state.notificationFirebase.data,
});
const mapDispatchToProps = {};
const ChiTietGiayChungNhanScreenConnect = connect(mapStateToProps, mapDispatchToProps)(ChiTietGiayChungNhanScreenComponent);
export const ChiTietGiayChungNhanScreen = memo(ChiTietGiayChungNhanScreenConnect, isEqual);
