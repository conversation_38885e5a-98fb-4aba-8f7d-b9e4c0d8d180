import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import {APP_NAME, isIOS} from '@constant';
import moment from 'moment';
import React, {memo, useState, useRef} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, StyleSheet, TouchableOpacity, Vibration, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import DeviceInfo from 'react-native-device-info';
import ImageMarker, {Position} from 'react-native-image-marker';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

function ModalCamera({toggleModalCamera, tatCameraModal, handleImage, indexOpened, setIndexOpened, appSetting, imagesData, currentPosition, userInfo}) {
  // console.log('ModalCamera', props);
  let cameraRef = useRef(null);
  const [flashData, setFlashType] = useState({
    flashIcon: 'flash-off',
    flashMode: RNCamera.Constants.FlashMode.off,
  });
  const [cameraType, setCameraType] = useState(RNCamera.Constants.Type.back);
  const [countPicture, setCountPicture] = useState(0);
  const [viTriAnhChup, setViTriAnhChup] = useState(0);

  const initModalData = () => {
    setViTriAnhChup(indexOpened);
  };

  //xử lý khi icon FLash được click
  const onPressChangeFlash = () => {
    //Nếu đang auto -> flash
    if (flashData.flashIcon === 'flash-auto') {
      setFlashType({
        flashIcon: 'flash',
        flashMode: RNCamera.Constants.FlashMode.on,
      });
    }
    //nếu đang flash -> tắt
    else if (flashData.flashIcon === 'flash') {
      setFlashType({
        flashIcon: 'flash-off',
        flashMode: RNCamera.Constants.FlashMode.off,
      });
    }
    //nếu đang tắt flash -> flash-auto
    else if (flashData.flashIcon === 'flash-off') {
      setFlashType({
        flashIcon: 'flash-auto',
        flashMode: RNCamera.Constants.FlashMode.auto,
      });
    }
  };
  //chuyển camera trước - sau
  const onPressSwapCamera = () => {
    if (cameraType === RNCamera.Constants.Type.back) {
      setCameraType(RNCamera.Constants.Type.front);
    } else if (cameraType === RNCamera.Constants.Type.front) {
      setCameraType(RNCamera.Constants.Type.back);
    }
  };
  //ẩn modal camera
  const onPressTatCameraModal = () => {
    tatCameraModal();
    setFlashType({
      flashIcon: 'flash-off',
      flashMode: RNCamera.Constants.FlashMode.off,
    });
    setCameraType(RNCamera.Constants.Type.back);
    setCountPicture(0);
  };

  const getAnhTitle = () => (imagesData.length > 0 ? imagesData[0].images[viTriAnhChup].ten : '');

  const onPressChupAnh = async () => {
    if (cameraRef) {
      let cameraOptions = {fixOrientation: true, quality: 0.5, width: 1800};
      let dataImage = await cameraRef.takePictureAsync(cameraOptions);
      if (appSetting.rung) Vibration.vibrate(200);
      setCountPicture(countPicture + 1);
      //nếu đang chụp ảnh HỒ SƠ, GIẤY TỜ
      dataImage.path = dataImage.uri;
      let imageAddText = await chenThongTinLenAnh(dataImage);
      dataImage.path = (!isIOS ? 'file://' : '') + imageAddText;
      handleImage(dataImage, viTriAnhChup, 0);
      if (viTriAnhChup < imagesData[0].images.length - 1) {
        setIndexOpened(indexOpened + 1);
        setViTriAnhChup(indexOpened + 1);
      }
    }
  };

  const chenThongTinLenAnh = async dataImage => {
    //ngày giờ / toạ độ / người / thông tin máy
    let txtChen = '';
    txtChen += userInfo.nguoi_dung.nsd;
    let tenMay = await DeviceInfo.getBrand();
    txtChen += '\n' + moment().format('HH:mm DD/MM/YYYY') + ' ';
    txtChen += tenMay;
    if (currentPosition && currentPosition.coords) txtChen += '\n' + currentPosition.coords.longitude.toFixed(3) + ' ; ' + currentPosition.coords.latitude.toFixed(3) + ' ';

    let imageAddText = await ImageMarker.markText({
      backgroundImage: {
        src: dataImage.path,
        scale: 1,
      },
      watermarkTexts: [
        {
          text: txtChen,
          positionOptions: {
            position: Position.bottomRight,
          },
          style: {
            color: '#FFF',
            fontSize: isIOS ? 30 : 30,
          },
        },
      ],
      quality: 100,
    }).catch(err => {
      Alert.alert('Chèn toạ độ không thành công', err.message);
      return '';
    });
    return imageAddText;
  };

  /* RENDER */
  return (
    <Modal onModalShow={initModalData} isVisible={toggleModalCamera} swipeDirection={'down'} style={styles.modal}>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          <View style={{flex: 1}}>
            <RNCamera
              ref={ref => (cameraRef = ref)}
              playSoundOnCapture={appSetting.amThanhKhiChup}
              // onCameraReady={prepareRatio}
              style={styles.cameraPreview}
              type={cameraType}
              flashMode={flashData.flashMode}
              captureAudio={false}
              zoom={0}
              useNativeZoom={true}
              androidCameraPermissionOptions={{
                title: 'Cho phép ' + APP_NAME + ' truy cập camera của bạn',
                message: APP_NAME + ' muốn truy cập camera của bạn để chụp ảnh',
                buttonPositive: 'Đồng ý',
                buttonNegative: 'Để sau',
              }}>
              <View style={styles.topViewCamera}>
                <TouchableOpacity onPress={onPressTatCameraModal} style={styles.btnCloseCamera}>
                  <Icon.AntDesign name="arrowleft" size={40} color={colors.WHITE} />
                </TouchableOpacity>
                <TouchableOpacity onPress={onPressTatCameraModal} style={styles.btnCloseCamera}>
                  <Icon.MaterialCommunityIcons name="check" size={40} color={colors.WHITE} />
                </TouchableOpacity>
              </View>
              <View style={styles.anhTitleView}>
                <Text children={getAnhTitle()} style={styles.txtTitleAnh} />
              </View>
              {/* <View style={styles.countPicture}>
                <Text children={countPicture} style={styles.txtCountPicture} />
              </View> */}
            </RNCamera>
          </View>
          <View style={styles.btnsCameraView}>
            <TouchableOpacity onPress={onPressChangeFlash} style={styles.btnCameraView}>
              <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color={colors.WHITE} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.btnCameraView} onPress={onPressChupAnh}>
              <Icon.MaterialCommunityIcons name={'circle-slice-8'} size={100} color={colors.WHITE} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
              <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnsCameraView: {
    flexDirection: 'row',
    backgroundColor: colors.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    height: 120,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  topViewCamera: {
    position: 'absolute',
    left: 0,
    top: isIOS ? 20 : 50,
    right: 0,
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    flexDirection: 'row',
  },
  btnCloseCamera: {},
  countPicture: {
    position: 'absolute',
    right: 15,
    bottom: 5,
  },
  txtCountPicture: {
    fontSize: 40,
    color: '#FFF',
    fontWeight: 'bold',
  },
  anhTitleView: {
    position: 'absolute',
    bottom: 10,
  },
  txtTitleAnh: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
    paddingHorizontal: 30,
    textAlign: 'center',
  },
});

const mapStateToProps = state => ({
  appSetting: state.appSetting,
  userInfo: state.user.data,
});

// const mapDispatchToProps = {};

// export default connect(mapStateToProps, mapDispatchToProps)(ModalCamera);
const ModalCameraConnect = connect(mapStateToProps, {})(ModalCamera);
export default memo(ModalCameraConnect, isEqual);
