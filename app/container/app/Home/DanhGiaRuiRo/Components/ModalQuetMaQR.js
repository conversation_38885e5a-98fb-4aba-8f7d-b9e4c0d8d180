import {colors} from '@app/commons/Theme';
import {Icon, Text} from '@app/components';
import {ContractEndPoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import Modal from 'react-native-modal';
import QRCodeScanner from 'react-native-qrcode-scanner';

const ModalQuetMaQRComponent = forwardRef(({onBackPress, getData}, ref) => {
  const [isVisible, setIsVisible] = useState(false);
  const [cameraFlashMode, setCameraFlashMode] = useState(RNCamera.Constants.FlashMode.off);

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const readQRCodeDanhGiaRuiRo = async (apiKey) => {
    const KEY = 'hash=';
    let url = '';
    const index = apiKey.indexOf(KEY);
    if (index === -1) {
      url = apiKey;
    } else {
      const hashIndex = index + KEY.length;
      url = apiKey.slice(hashIndex);
    }
    let params = {
      api_key: url,
    };
    try {
      let response = await ContractEndPoint.handleReadQRCode(null, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info;
      getData && getData(data);
      onBackPress && onBackPress();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onSuccess = (e) => {
    readQRCodeDanhGiaRuiRo(e.data);
  };

  /* RENDER */
  const onChangeFlashMode = () => {
    if (cameraFlashMode === RNCamera.Constants.FlashMode.off) {
      setCameraFlashMode(RNCamera.Constants.FlashMode.torch);
    }
    if (cameraFlashMode === RNCamera.Constants.FlashMode.torch) {
      setCameraFlashMode(RNCamera.Constants.FlashMode.off);
    }
  };

  const customMarker = () => {
    return (
      <View>
        <View style={styles.topOverlay}>
          <View flexDirection="row" justifyContent="space-between" paddingHorizontal={15} marginTop={20}>
            <TouchableOpacity onPress={onBackPress}>
              <Icon.AntDesign name="closecircle" color={colors.GRAY} size={25} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => onChangeFlashMode()}>
              {cameraFlashMode === RNCamera.Constants.FlashMode.off ? (
                <Icon.MaterialCommunityIcons name="flashlight" color={colors.GRAY} size={30} />
              ) : (
                <Icon.MaterialCommunityIcons name="flashlight-off" color={colors.BLUE1} size={30} />
              )}
            </TouchableOpacity>
          </View>
          <Text style={styles.centerText}>Di chuyển camera đến chỗ mã QR để quét</Text>
        </View>
        <View flexDirection="row">
          {/* <View style={styles.leftAndRightOverlay} /> */}
          <View height={dimensions.height / 2} />
          {/* <View flexDirection="row" justifyContent="center" flex={1}>
            <Image source={R.images.qr_scanner_frame} resizeMode={'contain'} style={{width: width - 60, height: width - 60}} />
          </View> */}
          {/* <View style={styles.leftAndRightOverlay} /> */}
        </View>
        <View style={styles.topOverlay} />
        {__DEV__ && (
          <TouchableOpacity
            style={styles.buttonTouchable}
            onPress={() => readQRCodeDanhGiaRuiRo('https://abc.escs.vn/danh-gia-rui-ro?hash=95KkRqYYaxQtNWatZTcCstzFh34IMjFV2c6QWYxTydNKHbT7jId07%2brrSobdQfa8tj6RHB%2biXVU%3d')}>
            <Text style={styles.buttonText}>OK. Got it!</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      propagateSwipe={true}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onBackButtonPress={onBackPress}
      onBackdropPress={onBackPress}
      style={styles.modal}
      avoidKeyboard={true}>
      <SafeAreaView style={styles.container}>
        <QRCodeScanner
          ///
          reactivate
          showMarker
          onRead={onSuccess}
          reactivateTimeout={5000}
          flashMode={cameraFlashMode}
          cameraStyle={{height: dimensions.height}}
          customMarker={customMarker()}
        />
      </SafeAreaView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    // justifyContent: 'flex-end',
  },
  centerText: {
    flex: 1,
    padding: 32,
    fontSize: 16,
    textAlign: 'center',
    color: colors.WHITE1,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textBold: {
    color: '#000',
    fontWeight: '500',
  },
  buttonText: {
    fontSize: 21,
    marginBottom: 100,
    color: colors.WHITE,
  },
  buttonTouchable: {
    padding: 16,
  },
  alertModal: {
    top: 100,
    padding: 10,
    borderRadius: 14,
    width: dimensions.width - 30,
    position: 'absolute',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: colors.WHITE,
    justifyContent: 'space-between',
  },
  btnOpen: {
    width: 50,
    padding: 10,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.GRAY2,
  },
  txtBtnOpen: {
    color: colors.PRIMARY,
    fontWeight: '600',
  },
  topOverlay: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.width,
    backgroundColor: 'rgba(0,0,0,0.6)',
    // alignItems: 'center',
  },
  leftAndRightOverlay: {
    height: dimensions.width * 0.65,
    width: dimensions.width,
    backgroundColor: 'rgba(0,0,0,0.6)',
  },
  box: {
    width: 300,
    height: 2,
    backgroundColor: 'tomato',
  },
  centerContentQrView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 1,
  },
  centerContentQrView2: {
    flex: 1,
    backgroundColor: '#000',
    opacity: 0.3,
  },
  scanningView: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  qrCamera: {
    width: dimensions.width,
    height: dimensions.height - 90 - 80,
  },
  imgCameraScanFrame: {
    width: dimensions.width - 50,
    height: dimensions.width - 50,
    marginVertical: 20,
  },
  scanning: {width: dimensions.width - 50, height: dimensions.width},
});
export const ModalQuetMaQR = memo(ModalQuetMaQRComponent, isEqual);
