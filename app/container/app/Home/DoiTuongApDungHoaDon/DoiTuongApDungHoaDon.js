import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ScreenComponent, Text} from '@app/components';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, SearchBar} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useDebouncedCallback} from 'use-debounce';

const DoiTuongApDungHoaDonScreenComponent = ({route}) => {
  console.log('DoiTuongApDungHoaDonScreenComponent');
  const {profileData} = route?.params;
  const [loading, setLoading] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [dsDoiTuong, setDsDoiTuong] = useState([]);
  const [dsDoiTuongRoot, setDsDoiTuongRoot] = useState([]);

  useEffect(() => {
    getChiTietHoSoGiamDinh(profileData);
  }, []);

  const getChiTietHoSoGiamDinh = async (profileDetail) => {
    let actionCode = '';
    if (profileData.ho_so.nghiep_vu === 'XE') actionCode = axiosConfig.ACTION_CODE.PROFILE_DATA;
    if (profileData.ho_so.nghiep_vu === 'XE_MAY') actionCode = axiosConfig.ACTION_CODE.LAY_CHI_TIET_HS_XM;
    return new Promise(async (resolve, reject) => {
      setDialogLoading(true);
      try {
        let paramsProfileDetail = {
          ma_doi_tac: profileDetail.ho_so?.ma_doi_tac || profileDetail.ma_doi_tac,
          so_id: profileDetail.ho_so?.so_id || profileDetail.so_id,
        };
        let response = await ESmartClaimEndpoint.execute(actionCode, paramsProfileDetail);
        setDialogLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') {
          resolve(false);
          return;
        }
        let arr = response.data_info.ds_doi_tuong;
        arr.map((e, i) => {
          arr[i].isChecked = false;
          if (e.ad_hoa_don === 'C') arr[i].isChecked = true;
        });
        setDsDoiTuong([...arr]);
        setDsDoiTuongRoot([...arr]);
      } catch (error) {
        setDialogLoading(false);
        Alert.alert('Thông báo', error.message);
        resolve(false);
      }
    });
  };

  // Lưu danh sách yc bổ sung
  const onPressLuu = async (item, index) => {
    setLoading(true);
    try {
      const params = {
        so_id: item.so_id,
        so_id_doi_tuong: item.so_id_doi_tuong,
        nv: profileData.ho_so.nghiep_vu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DOI_TUONG_AD_HOA_DON, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật thành công!', 'success');
      getChiTietHoSoGiamDinh(profileData);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 56');
    }
  };

  //
  const debounced = useDebouncedCallback((value) => {
    setSearchText(value);
  }, 300);

  useEffect(() => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = dsDoiTuongRoot.filter((item) => item?.ten_doi_tuong.toLowerCase()?.includes(lowerCaseSearchText));
      setDsDoiTuong(filter);
    } else if (searchText === '') {
      setDsDoiTuong(dsDoiTuongRoot);
    }
  }, [dsDoiTuongRoot, searchText]);

  /* RENDER */
  const renderItem = ({item, index}) => {
    return (
      <View>
        <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
          <View style={styles.leftCol}>
            <Text style={styles.txtTenHangMuc} children={item.ten_doi_tuong} />
          </View>
          <View style={styles.frame}>
            <TouchableOpacity disabled={loading} onPress={() => onPressLuu(item, index)}>
              <CheckboxComp disabled checkboxStyle={isIOS && styles.checkboxStyle} value={item.isChecked} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Đối tượng áp dụng hoá đơn"
      renderView={
        <View flex={1}>
          <SearchBar placeholder="Tìm kiếm đối tượng" onTextChange={debounced} />
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} />}>
            <View style={styles.tableTitleRow}>
              <View style={styles.leftCol} alignItems="center">
                <Text style={styles.txtHangMuc} children="Tên đối tượng" />
              </View>
              <View style={[styles.frame]}>
                <Text children="Áp dụng hoá đơn" style={styles.txtGroup} />
              </View>
            </View>
            <FlatList
              data={dsDoiTuong}
              scrollEnabled={false}
              renderItem={renderItem}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
        </View>
      }
    />
  );
};

const LINE_WIDTH = 0.5;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    marginHorizontal: 10,
  },
  inputRow: {
    borderWidth: LINE_WIDTH,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
  },
  checkboxStyle: {
    width: 18,
    height: 18,
  },
  txtTenHangMuc: {
    fontSize: FontSize.size13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: spacing.tiny,
  },
  checkBox: {
    paddingHorizontal: 10,
  },
  frame: {
    width: dimensions.width * 0.3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tableTitleRow: {
    borderWidth: LINE_WIDTH,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  txtGroup: {
    fontSize: FontSize.size12,
  },

  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
  leftCol: {
    flex: 1,
    justifyContent: 'center',
    borderRightWidth: LINE_WIDTH,
    paddingVertical: spacing.smaller,
    borderColor: colors.GRAY,
  },
});

export const DoiTuongApDungHoaDonScreen = memo(DoiTuongApDungHoaDonScreenComponent, isEqual);
