import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {ButtonLinear, Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

const DanhSachCanBoScreenComponent = (props) => {
  console.log('DanhSachCanBoScreenComponent');
  const {route, navigation} = props;
  const {profileData} = route.params;
  const [danhSachCanBo, setDanhSachCanBo] = useState([]);
  const [indexSelected, setIndexSelected] = useState(-1);
  const [dialogLoading, setDialogLoading] = useState(false);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDsCanBo();
    });
  }, []);

  const getDsCanBo = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id_knai,
        nv: 'KHIEU_NAI',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_CAN_BO_KHIEN_NAI, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDanhSachCanBo(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const handleSelect = (index) => {
    setIndexSelected(index);
  };

  const renderItem = ({item, index}) => {
    const renderLabel = (label, detail) => (
      <View style={styles.contentRow}>
        <Text style={{minWidth: dimensions.width * 0.3, color: colors.BLACK_03}} font="medium14">
          {label}
        </Text>
        <Text style={styles.detail} font="medium14">
          {detail}
        </Text>
      </View>
    );
    return (
      <TouchableOpacity onPress={() => handleSelect(index)}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.itemView, indexSelected === index && {borderColor: colors.BLUE1}]}>
          {renderLabel('Họ tên:', item.ten_can_bo)}
          {renderLabel('Chức danh:', item.chuc_danh)}
          {renderLabel('Đơn vị:', item.chi_nhanh)}
        </LinearGradient>
      </TouchableOpacity>
    );
  };
  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Danh sách cán bộ"
      renderView={
        <View style={styles.container}>
          <FlatList contentContainerStyle={{paddingTop: spacing.default}} data={danhSachCanBo} renderItem={renderItem} keyExtractor={(_, i) => i.toString()} ListEmptyComponent={<Empty />} />
        </View>
      }
      footer={
        <View style={styles.footerView}>
          <ButtonLinear title="Thêm người tham gia" linearStyle={{marginRight: spacing.default}} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_CAN_BO_KNAI, {profileData})} />
          <ButtonLinear title="Chuyển người xử lý" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.CHUYEN_NGUOI_XL_HS_KHIEU_NAI, {profileData})} />
        </View>
      }
    />
  );
};

export const DanhSachCanBoScreen = memo(DanhSachCanBoScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  itemView: {
    marginHorizontal: scale(spacing.smaller),
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingHorizontal: spacing.default,
    paddingVertical: spacing.smaller,
    borderRadius: 8,
    marginBottom: spacing.smaller,
  },
  detail: {
    color: colors.BLACK_03,
    marginLeft: scale(20),
    fontSize: FontSize.size14,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: vScale(spacing.tiny),
  },
  footerView: {
    flex: 1,
    flexDirection: 'row',
  },
});
