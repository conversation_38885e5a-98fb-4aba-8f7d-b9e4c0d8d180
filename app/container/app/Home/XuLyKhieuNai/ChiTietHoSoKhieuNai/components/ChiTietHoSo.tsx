import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {Linking, StyleSheet, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import {HEADER_TITLE, ICON_HEADER} from './Constant';

const ChiTietHoSoComponent = (props) => {
  const {profileData} = props;

  const onPressHeader = (title, screenName) => {
    const {ho_so} = profileData;
    NavigationUtil.push(screenName, {
      profileData,
    });
  };

  /* RENDER */
  // RENDER header các đầu mục
  const renderProfileInformationHeader = (title, data, screenName) => {
    let indexIcon = HEADER_TITLE.findIndex((item) => item === title);
    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => onPressHeader(title, screenName)}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View flexDirection="row">
              <Icon.FontAwesome name={ICON_HEADER[indexIcon]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>{title + ' ' + dataLength}</Text>
            </View>
          </View>
          {title !== HEADER_TITLE[0] && <Icon.SimpleLineIcons name={'arrow-right'} size={15} style={styles.iconBtnTopRightView} />}
        </TouchableOpacity>
        {title !== HEADER_TITLE[0] && (
          <View style={{backgroundColor: colors.WHITE5}}>
            <View style={styles.spacing} />
          </View>
        )}
      </View>
    );
  };
  const renderThongTinChungChidren = (title, data, containerStyle, subValue, style) => (
    <View style={[styles.inforView, containerStyle]}>
      <Text style={styles.txtTitle} children={title} />
      {typeof data === 'number' ? (
        <NumericFormat
          value={data}
          displayType={'text'}
          thousandSeparator={true}
          renderText={(value) => <Text style={styles.txtDetail} selectable children={value + (subValue ? ' (' + subValue + '%)' : '')} />}
        />
      ) : (
        <Text style={[styles.txtDetail, style]} selectable children={data} />
      )}
    </View>
  );
  //render ra thông tin hồ sơ
  const renderProfileInformation = () => {
    if (!profileData || !profileData.ho_so) return;
    const {ho_so, can_bo, qtxl, lan_knai} = profileData;
    const getStatusTextColor = (status) => {
      let color = status === 'HSBT_KHIEU_NAI_DANG_XLY' ? colors.BLUE1 : status === 'HSBT_KHIEU_NAI_DA_XLY' ? colors.GREEN : colors.BLACK;
      return color;
    };

    const getStatusText = (status) => {
      let text = '';
      text = status === 'HSBT_KHIEU_NAI_DANG_XLY' ? 'Đang xử lý' : status === 'HSBT_KHIEU_NAI_DA_XLY' ? 'Đã xử lý' : '';
      return text;
    };

    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View flex={1}>
          {renderProfileInformationHeader(HEADER_TITLE[0], null, null)}
          <View flexDirection="row">
            {renderThongTinChungChidren('Số hồ sơ', ho_so.so_hs || '', {borderTopLeftRadius: 20, backgroundColor: colors.WHITE})}
            {/* {renderThongTinChungChidren('Số hợp đồng', ho_so.so_hd)} */}
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Tên chủ xe', ho_so.ten_kh || '')}
            {renderThongTinChungChidren('Biển xe', ho_so.doi_tuong || '')}
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Tên người khiếu nại', ho_so.nguoi_knai)}
            {ho_so.dthoai_knai && (
              <View style={styles.inforView}>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${ho_so.dthoai_knai}`)}>
                  <Text style={styles.txtTitle}>Điện thoại</Text>
                  <View flexDirection="row" alignItems="center">
                    <Icon.Entypo name="old-phone" size={20} color={colors.PRIMARY} />
                    <Text style={[styles.txtDetail, styles.actionTxt]}>{ho_so.dthoai_knai || ''}</Text>
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Ngày khiếu nại', ho_so.ngay_gio_knai || '')}
            {renderThongTinChungChidren('Email', ho_so.email_knai)}
          </View>

          {renderThongTinChungChidren('Nội dung khiếu nại', ho_so.noi_dung_knai || '')}
          {renderThongTinChungChidren('Trạng thái', getStatusText(ho_so.trang_thai), null, null, {color: getStatusTextColor(ho_so.trang_thai)})}
        </View>

        {/* THông tin xử lý khiếu nại */}
        {renderProfileInformationHeader(HEADER_TITLE[1], null, SCREEN_ROUTER_APP.TT_XL_HS_KHIEU_NAI)}
        {/* Ds cán bộ */}
        {renderProfileInformationHeader(HEADER_TITLE[2], can_bo, SCREEN_ROUTER_APP.DS_CAN_BO_HS_KHIEU_NAI)}
        {/*  Ý kiến trao đổi*/}
        {renderProfileInformationHeader(HEADER_TITLE[3], null, SCREEN_ROUTER_APP.Y_KIEN_TRAO_DOI_HS_KNAI)}
        {/* Quá trình xử lý */}
        {renderProfileInformationHeader(HEADER_TITLE[4], qtxl, SCREEN_ROUTER_APP.QTXL_HS_KHIEU_NAI)}
      </View>
    );
  };

  return <>{renderProfileInformation()}</>;
};

export const ChiTietHoSo = memo(ChiTietHoSoComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    marginTop: 5,
    marginBottom: 60,
    flex: 1,
  },
  iconBtnTopLeftView: {
    marginRight: 15,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    marginHorizontal: 15,
    alignSelf: 'center',
  },
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
  },
  txtTitle: {
    marginBottom: 4,
    fontSize: 12,
  },
  txtDetail: {
    color: colors.GRAY6,
    paddingRight: 10,
    flexShrink: 1,
  },
  inforView: {
    flex: 1,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.smaller,
    borderColor: colors.GRAY4,
    borderBottomWidth: 0.5,
    backgroundColor: colors.WHITE,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: dimensions.width - 20,
  },
  headerTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 5,
    paddingTop: 20,
    marginLeft: 20,
    textDecorationLine: 'underline',
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionTxt: {
    marginLeft: 8,
    color: colors.PRIMARY,
  },
  spacing: {
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
});
