import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CustomTabBar, ScreenComponent} from '@component';
import {DATA_CONSTANT, SCREEN_ROUTER_APP} from '@constant';
import moment from 'moment';
import React, {createRef, memo, useCallback, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, RefreshControl, ScrollView, View} from 'react-native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {useSelector} from 'react-redux';
import styles from './Styles';
import {ChiTietHoSo, TabTai<PERSON>ieu} from './components';

import {chupAnhIndex, extensionsImage} from './Constants';
const ChiTietHoSoKhieuNaiScreenComponent = (props) => {
  console.log('ChiTietHoSoKhieuNaiScreenComponent');
  const {route, navigation, notificationFirebase} = props;
  const userInfo = useSelector(selectUser);
  const {prevScreen} = route?.params;

  const prevScreenIsHoSoDiaBan = prevScreen === SCREEN_ROUTER_APP.HO_SO_DIA_BAN;

  let scrollViewRef = useRef(null);
  let tabViewRef = useRef(null);
  // let refModalCanhBaoHoSoOto = useRef(null);
  let actionSheetChupAnhRef = createRef(null);
  // let actionSheetLapPhuongAnRef = createRef(null);
  // let actionSheetLapPhuongAnKhacRef = createRef(null);
  // let actionSheetTinhToanBoiThuongRef = createRef(null);
  // let actionSheetChuyenThongTinHienTruongRef = createRef(null);
  // let refModalChuyenHSVeDonViGoc = useRef(null);
  // let refModalChuyenBoPhanBoiThuong = useRef(null);
  // let refModalTraLaiGiamDinh = useRef(null);
  let refTabTaiLieuBoiThuongOTo = useRef(null);
  // let refModalHuyHoSo = useRef(null);
  // let refModalTuChoiBoiThuong = useRef(null);
  // let refModalChuyenThanhToan = useRef(null);

  // const [dialogLoading, setDialogLoading] = useState(false);
  // const [refreshing, setRefreshing] = useState(false);
  // const [profileData, setProfileData] = useState(null);
  const [imageDataStep1, setImageDataStep1] = useState([]); //ẢNH HIỆN TRƯỜNG
  const [imageDataStep2, setImageDataStep2] = useState([]); //ẢNH TOÀN CẢNH
  const [imageDataStep3, setImageDataStep3] = useState([]); //ẢNH TỔN THẤT dữ liệu kiểu : [{//thông tin menu,images}]
  const [imageDataStep4, setImageDataStep4] = useState([]); //ẢNH GIẤY TỜ dữ liệu kiểu : [{//thông tin menu,images}]
  const [anhDanhGiaRuiRo, setAnhDanhGiaRuiRo] = useState([]); //ẢNH ĐÁNH GIÁ RỦI RO dữ liệu kiểu : [{//thông tin menu,images}]
  const [anhNghiemThu, setAnhNghiemThu] = useState([]); //ẢNH NGHIỆM THU
  const [anhThuHoiVatTu, setAnhThuHoiVatTu] = useState([]); //ẢNH THU HỒI VẬT TƯ
  const [anhXacMinhHienTruong, setAnhXacMinhHienTruong] = useState([]); //ẢNH XÁC MINH HIỆN TRƯỜNG
  const [listTaiLieuPdf, setListTaiLieuPdf] = useState([]); //ẢNH XÁC MINH HIỆN TRƯỜNG
  const [dataAnhCapDon, setDataAnhCapDon] = useState([]);
  const [switchImgView, setSwitchImgView] = useState(false); //chuyển đổi chế độ XEM - CHỌN ảnh
  const [xemTaiLieuSelected, setXemTaiLieuSelected] = useState('ANH_TON_THAT');
  const [doiTuongDuocChon, setDoiTuongDuocChon] = useState(null); //đối tượng tổn thất được chọn để chụp ảnh
  const [toggleModalChonDoiTuong, setToggleModalChonDoiTuong] = useState(false); //đối tượng tổn thất được chọn để chụp ảnh
  const [toggleModalChonDoiTuongHoSo, setToggleModalChonDoiTuongHoSo] = useState(false); //đối tượng tổn thất được chọn để chụp ảnh úc bổ sung hồ sơ, giấy tờ
  const [danhGiaHienTruong, setDanhGiaHienTruong] = useState([]);
  // const [actionSheetMauInData, setActionSheetMauInData] = useState([]); //data của actionSheetMauIN
  // const [actionSheetMauInXacNhanBienBanData, setActionSheetMauInXacNhanBienBanData] = useState([]); //data của actionSheeXacNhanBienBan
  const [optionsLapPhuongAnKhac, setOptionsLapPhuongAnKhac] = useState([]);
  const [dsNghiepVuKhac, setDsNghiepVuKhac] = useState([]);
  const [dataNV, setDataNV] = useState([]);
  // const [ngayChuyenThanhToan, setNgayChuyenThanhToan] = useState(0);
  // const [listCanhBaoHoSo, setListCanhBaoHoSo] = useState([]);
  const [btnTabActive, setBtnTabActive] = useState(0);

  useEffect(() => {
    navigation.addListener('focus', () => initScreenDatas());
    navigation.addListener('blur', () => setAnhDanhGiaRuiRo([]));
    if (route.params.subScreen) {
      if (route.params.subScreen === SCREEN_ROUTER_APP.Y_KIEN_TRAO_DOI)
        NavigationUtil.push(SCREEN_ROUTER_APP.Y_KIEN_TRAO_DOI, {
          profileData: route.params.profileDetail,
        });
    }
    // return () => {
    //   if (timer) clearTimeout(timer);
    // };
  }, []);

  const initScreenDatas = async () => {
    setDoiTuongDuocChon(null);
    setToggleModalChonDoiTuong(false);
    // await getChiTietHoSoGiamDinhOto(route.params.profileDetail);
    getChiTietHoSo(route.params.profileDetail);
    // await getDataDanhGiaHienTruong(route.params.profileDetail);
    await getDSNghiepVu(route.params.profileDetail.so_id);
    // await getListCanhBao(route.params.profileDetail);
  };

  useEffect(() => {
    let profileDetail = notificationFirebase;
    profileDetail?.ho_so && getChiTietHoSo(profileDetail, 'props.notificationFirebase');
  }, [notificationFirebase]);

  // EVENT khi từ màn CLASSIFY phân loại thành công
  useEffect(() => {
    if (prevScreen === SCREEN_ROUTER_APP.CLASSIFY) loadLaiAnhCacHangMucVuaDanHGia();
  }, [prevScreen, imageDataStep3]);

  useEffect(() => {
    try {
      if (doiTuongDuocChon) {
        if (profileData.ho_so.hien_truong === 'K') {
          if (btnTabActive == '0') onPressChupAnhChiTiet();
          else if (btnTabActive == '1') onPressChupAnhChiTiet(xemTaiLieuSelected);
          setToggleModalChonDoiTuong(false);
          setToggleModalChonDoiTuongHoSo(false);
        } else if (profileData.ho_so.hien_truong === 'D') {
          if (btnTabActive == '0') openActionSheetChupAnh();
          else {
            setToggleModalChonDoiTuongHoSo(false);
            setToggleModalChonDoiTuong(false);
            if (btnTabActive == '1') {
              if (xemTaiLieuSelected === 'ANH_HO_SO') onPressChonLoaiGiamDinh(1, xemTaiLieuSelected);
              else if (xemTaiLieuSelected === 'ANH_HIEN_TRUONG' || xemTaiLieuSelected === 'XMHT') onPressTakePicture(0, xemTaiLieuSelected);
              else onPressChupAnhChiTiet(xemTaiLieuSelected);
            }
          }
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  }, [doiTuongDuocChon]);

  // EVENT khi chọn vào tài liệu bồi thường, khi profileData hoặc xemTaiLieuSelected thay đổi
  useEffect(() => {
    if (btnTabActive == '1') {
      try {
        if (xemTaiLieuSelected === 'ANH_HIEN_TRUONG') initAnhHienTruong();
        else if (xemTaiLieuSelected === 'ANH_TON_THAT') initAnhTonThat();
        else if (xemTaiLieuSelected === 'ANH_TOAN_CANH') initAnhToanCanh();
        else if (xemTaiLieuSelected === 'ANH_CAP_DON') initAnhCapDon();
        else if (xemTaiLieuSelected === 'ANH_DANH_GIA_RUI_RO') initAnhDanhGiaRuiRo();
        else if (xemTaiLieuSelected === 'ANH_NGHIEM_THU') initAnhNghiemThu();
        else if (xemTaiLieuSelected === 'ANH_THVT') initAnhThuHoiVatTu();
        else if (xemTaiLieuSelected === 'XMHT') initAnhXacMinhHienTruong();
        else if (xemTaiLieuSelected === 'ANH_HO_SO') initAnhHoSoGiayTo();
        else if (xemTaiLieuSelected === 'FILE_PDF') initTaiLieuPDF();
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    }
  }, [btnTabActive, profileData, xemTaiLieuSelected]);

  // const getListCanhBao = async (profileData) => {
  //   let params = {
  //     so_id: profileData.so_id,
  //     ma_doi_tac: profileData.ma_doi_tac,
  //   };
  //   try {
  //     let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_LIST_CANH_BAO_HO_SO_O_TO, params);
  //     if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //     setListCanhBaoHoSo([...response.data_info]);
  //   } catch (error) {
  //     Alert.alert('Thông báo', error.message);
  //   }
  // };

  // GET CHI TIẾT HỒ SƠ Ô TÔ
  // const getChiTietHoSoGiamDinhOto = async (profileDetail) => {
  //   setDialogLoading(true);
  //   //lấy chi tiết hồ sơ
  //   try {
  //     let paramsProfileDetail = {
  //       ma_doi_tac: profileDetail.ho_so?.ma_doi_tac || profileDetail.ma_doi_tac,
  //       so_id: profileDetail.ho_so?.so_id || profileDetail.so_id,
  //     };
  //     let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.PROFILE_DATA, paramsProfileDetail);
  //     setDialogLoading(false);
  //     if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //     const dataInfo = response.data_info;
  //     setProfileData(dataInfo);
  //     if (!IS_PROD) AsyncStorageProvider.luuHoSoDangGiamDinh(dataInfo);
  //     setNgayChuyenThanhToan(dataInfo.ho_so?.ngay_chuyen_tt || DEFAULT_DATE);
  //     let actionSheetTmp = [];
  //     dataInfo.mau_in.map((item) => actionSheetTmp.push(item.ten));
  //     actionSheetTmp.push('Để sau');
  //     setActionSheetMauInData(actionSheetTmp);
  //     actionSheetTmp = [];
  //     dataInfo.mau_in.map((item) => {
  //       if (item.ma_mau_in === 'ESCS_BBGD_HIEN_TRUONG' || item.ma_mau_in === 'ESCS_BBGD_XAC_DINH_THIET_HAI_XCG') actionSheetTmp.push(item.ten);
  //     });
  //     actionSheetTmp.push('Để sau');
  //     setActionSheetMauInXacNhanBienBanData(actionSheetTmp);
  //     if (
  //       dataInfo.ho_so?.ktra_chuyen_gdh === 'G' &&
  //       dataInfo.ho_so?.ngay_duyet_gia < DEFAULT_DATE &&
  //       dataInfo.ho_so.ngay_chuyen_hs >= DEFAULT_DATE &&
  //       dataInfo.ho_so?.gdh === 'C' &&
  //       dataInfo.ho_so?.bth === 'K'
  //     )
  //       return kiemTraHoSoTruocKhiKetThucLanGiamDinh(dataInfo);
  //   } catch (error) {
  //     setDialogLoading(false);
  //     Alert.alert('Thông báo', error.message);
  //   }
  // };

  // GET ẢNH TÀI LIỆU THEO MÃ
  const getTaiLieuBoiThuong = async (ma_file) => {
    return new Promise(async (resolve, reject) => {
      try {
        //lấy thông tin của ảnh giám định
        let params = {so_id: profileData.ho_so.so_id_hs, nv: profileData.ho_so, ma_file};
        let responseFileThumbnail = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
        setDialogLoading(false);
        if (!responseFileThumbnail || !responseFileThumbnail.state_info || responseFileThumbnail.state_info.status !== 'OK') return resolve([]);
        resolve(responseFileThumbnail.data_info);
      } catch (error) {
        setDialogLoading(false);
        Alert.alert('Thông báo', error.message);
        resolve([]);
      }
    });
  };

  //KHỞI TẠO ẢNH HIỆN TRƯỜNG
  const initAnhHienTruong = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('HT0001');
    if (response.length > 0) setImageDataStep1([...response]);
  };
  //KHỞI TẠO ẢNH TOÀN CẢNH
  const initAnhToanCanh = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('TC0001');
    let imagesTmp = response.map((item) => {
      item.checked = false;
      item.path = item.duong_dan;
      item.name = item.ten_file;
      let nhom = {
        checked: false,
        ma: item.ma_file,
        ten: item.nhom_anh,
        nhom_hang_muc: item.nhom_hang_muc,
      };
      item.nhom = nhom;
      return item;
    });
    let hangMucMoi = null;
    hangMucMoi = {
      checked: false,
      expanded: false,
      hang_muc: {
        ten_hang_muc: 'Ảnh toàn cảnh',
        loai: 'TC',
        ma_file: 'TC0001',
      },
      images: imagesTmp,
      ma: 'TC0001',
      ten: 'Ảnh toàn cảnh',
    };
    setImageDataStep2([hangMucMoi]);
  };
  //KHỞI TẠO ẢNH TỔN THẤT
  const initAnhTonThat = () => {
    let listHangMuc = [];
    let hangMucTonThat = profileData.hang_muc_chup.filter((item) => item.loai === 'TT');
    // for theo hang_muc
    for (let i = 0; i < hangMucTonThat.length; i++) {
      //tìm hạng mục i trong imageDataStep3
      let hangMucCu = imageDataStep3.find((item) => item.ma === hangMucTonThat[i].hang_muc && item.hang_muc.so_id_doi_tuong === hangMucTonThat[i].so_id_doi_tuong);
      // return console.log('🚀 ~ initAnhTonThat ~ hangMucCu:', hangMucCu);
      let hangMucMoi = null; //hạng mục mới để push vào listHangMuc
      //nếu có hạng mục cũ -> chỉ fill mới lại hang_muc
      if (hangMucCu) {
        hangMucMoi = {...hangMucCu};
        hangMucMoi.hang_muc = hangMucTonThat[i];
      }
      //nếu hang_muc[i] chưa có thì tạo mới
      else {
        hangMucMoi = {
          checked: false,
          expanded: false,
          hang_muc: hangMucTonThat[i],
          images: [],
          ma: hangMucTonThat[i].hang_muc,
          ten: hangMucTonThat[i].ten_hang_muc,
          tenDoiTuong: profileData.ds_doi_tuong.find((itemDoiTuong) => itemDoiTuong.so_id_doi_tuong === hangMucTonThat[i].so_id_doi_tuong)?.ten_doi_tuong || '',
        };
      }
      listHangMuc.push(hangMucMoi); //push vào mới
    }
    //nếu có > 2 đối tượng -> thì sort theo đối tượng cho dễ xem
    // if (profileData.ds_doi_tuong.length > 1) listHangMuc = listHangMuc.sort((a, b) => a.hang_muc.so_id_doi_tuong > b.hang_muc.so_id_doi_tuong);

    setImageDataStep3([...listHangMuc]);
  };

  //KHỞI TẠO ẢNH HỒ SƠ GIẤY TỜ
  const initAnhHoSoGiayTo = () => {
    let listHangMuc = [];
    let hangMucGiayTo = profileData.hang_muc_chup.filter(
      (item) => item.loai === 'TL' && item.loai_hang_muc !== 'PDF' && item.hang_muc !== 'ANH_THVT' && item.hang_muc !== 'XMHT' && item.hang_muc !== 'ANH_NGHIEM_THU',
    );
    // for theo hang_muc
    for (let i = 0; i < hangMucGiayTo.length; i++) {
      //tìm hạng mục i trong imageDataStep3
      let hangMucCu = imageDataStep4.find((item) => item.ma === hangMucGiayTo[i].hang_muc && item.hang_muc.so_id_doi_tuong === hangMucGiayTo[i].so_id_doi_tuong);
      let hangMucMoi = null; //hạng mục mới để push vào listHangMuc
      //nếu có hạng mục cũ -> chỉ fill mới lại hang_muc
      if (hangMucCu) {
        hangMucMoi = {...hangMucCu};
        hangMucMoi.hang_muc = hangMucGiayTo[i];
      }
      //nếu hang_muc[i] chưa có thì tạo mới
      else {
        hangMucMoi = {
          checked: false,
          expanded: false,
          hang_muc: hangMucGiayTo[i],
          images: [],
          ma: hangMucGiayTo[i].hang_muc,
          ten: hangMucGiayTo[i].ten_hang_muc,
          tenDoiTuong: profileData.ds_doi_tuong.find((itemDoiTuong) => itemDoiTuong.so_id_doi_tuong === hangMucGiayTo[i].so_id_doi_tuong)?.ten_doi_tuong || '',
        };
      }
      listHangMuc.push(hangMucMoi); //push vào mới
    }
    if (profileData.ds_doi_tuong.length > 1) listHangMuc = listHangMuc.sort((a, b) => a.hang_muc.so_id_doi_tuong > b.hang_muc.so_id_doi_tuong);
    setImageDataStep4([...listHangMuc]);
  };

  const initAnhNghiemThu = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('ANH_NGHIEM_THU');
    setAnhNghiemThu([...response]);
  };
  const initAnhThuHoiVatTu = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('ANH_THVT');
    setAnhThuHoiVatTu([...response]);
  };
  const initAnhXacMinhHienTruong = async () => {
    setDialogLoading(true);
    let response = await getTaiLieuBoiThuong('XMHT');
    setAnhXacMinhHienTruong([...response]);
  };
  //INIT ẢNH ĐÁNH GIÁ RỦI RO
  const initAnhDanhGiaRuiRo = async () => {
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id_hd: profileData.ho_so.so_id_hd,
        so_id_dt: profileData.ho_so.so_id_dt,
      };
      let response = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_ANH_DA_UPLOAD, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setAnhDanhGiaRuiRo(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //INIT ẢNH CẤP ĐƠN
  const initAnhCapDon = async () => {
    setDialogLoading(true);
    try {
      const params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh_ql,
        so_id: profileData.ho_so.so_id_hd,
        so_id_dt: profileData.ho_so.so_id_dt,
        so_gcn: profileData.ho_so.gcn,
        pm: 'API',
      };
      let response = await PartnerEndpoint.xemAnhCapDon(AxiosConfig.ACTION_CODE.LAY_ANH_CAP_DON, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response?.data_info) setDataAnhCapDon(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  // INIT FILE TÀI LIỆU PDF
  const initTaiLieuPDF = () => {
    try {
      if (listTaiLieuPdf.length > 0) return;
      let listPdf = profileData.hang_muc_chup.filter((item) => item.loai === 'TL' && item.loai_hang_muc === 'PDF');
      listPdf.map((item) => {
        item.extension = '.pdf';
        item.ma_file = item.hang_muc;
      });
      setListTaiLieuPdf([...listPdf]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const loadLaiAnhCacHangMucVuaDanHGia = async () => {
    try {
      let routes = navigation.getState().routes;
      NavigationUtil.updateParams(routes[routes.length - 2].key, {prevScreen: undefined}); //cập nhật tham số cho màn để prevScreen không phải là CLASSIFY nữa
      let imageDataStep3Tmp = [...imageDataStep3];
      /*
      chỉ xử lý những hạng mục có ảnh lẻ được đánh giá (ví dụ có 10 ảnh, chỉ đánh giá lại 2 ảnh)
      còn những hạng mục đánh gía lại tất cả các ảnh thì khi gọi chi tiết hồ sơ sẽ cập nhật lại theo hang_muc_chup
      */
      for (let i = 0; i < imageDataStep3Tmp.length; i++) {
        if ((!imageDataStep3Tmp[i].checked && imageDataStep3Tmp[i].images.find((item) => item.checked)) || imageDataStep3Tmp[i].expanded) {
          // setDialogLoading(true);
          refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(i);
          let response = await getTaiLieuBoiThuong(imageDataStep3Tmp[i].ma);
          response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +imageDataStep3Tmp[i].hang_muc.so_id_doi_tuong);
          let imagesTmp = response.map((item) => {
            item.checked = false;
            item.path = item.duong_dan;
            item.name = item.ten_file;
            let nhom = {
              checked: false,
              ma: item.ma_file,
              ten: item.nhom_anh,
              nhom_hang_muc: item.nhom_hang_muc,
            };
            item.nhom = nhom;
            return item;
          });
          imageDataStep3Tmp[i].images = imagesTmp;
        }
      }
      imageDataStep3Tmp = imageDataStep3Tmp.filter((item) => item.images.length > 0);
      refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
      setImageDataStep3([...imageDataStep3Tmp]);
    } catch (error) {
      refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
      Alert.alert('Thông báo', error.message);
    }
  };

  //nhóm ảnh
  const groupBy = useCallback((xs, key) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  }, []);

  //click vào 1 ảnh trong Tài liệu bồi thường
  const onPressOpenImageView = (currentImageData, listAnhXem) => {
    try {
      if (currentImageData.item.extension === '.pdf') {
        NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
          profileData,
          prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
          dataPDF: currentImageData.item,
        });
      } else if (extensionsImage.includes(currentImageData.item.extension)) {
        //nếu đang ở chế độ CHỌN ẢNH ĐẺ PHÂN LOẠI và đang ở ẢNH TOÀN CẢNH, ẢNH TỔN THẤT, ẢNH HỒ SƠ -> thì k xem dc chi tiết ảnh
        if (switchImgView && (xemTaiLieuSelected === 'ANH_TOAN_CANH' || xemTaiLieuSelected === 'ANH_TON_THAT' || xemTaiLieuSelected === 'ANH_HO_SO')) return;
        NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
          currentImageData,
          imagesData: listAnhXem,
        });
      } else if (xemTaiLieuSelected === 'ANH_CAP_DON') {
        //nếu là file PDF
        if (currentImageData.item?.pa_att_url?.includes('https://url.opes.com.vn') && currentImageData.item?.pa_att_url?.includes('.pdf')) {
          NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, {
            profileData,
            prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
            dataPDF: currentImageData.item,
          });
          return;
        }
        NavigationUtil.push(SCREEN_ROUTER_APP.XEM_ANH_CAP_DON, {
          imagesData: dataAnhCapDon,
          currentImageData,
        });
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //NÚT CHỌN TẤT CẢ - BỎ CHỌN
  //type : 0 - Bỏ chọn ; 1 - Chọn tất cả
  const onPressToggleCheckAll = async (type, imageData, viTriHangMuc) => {
    try {
      let newCheckedValue;
      if (type === 0) newCheckedValue = false;
      else if (type === 1) newCheckedValue = true;

      let imageDataTmp = [];
      let setImageData = null;
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        imageDataTmp = imageDataStep3;
        setImageData = setImageDataStep3;
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        imageDataTmp = imageDataStep4;
        setImageData = setImageDataStep4;
      } else if (xemTaiLieuSelected === 'ANH_TOAN_CANH') {
        imageDataTmp = imageDataStep2;
        setImageData = setImageDataStep2;
      }

      //nếu hạng mục đấy chưa tải ảnh + checkAll
      if (imageData.length === 0) {
        if (newCheckedValue) {
          setDialogLoading(true);
          let response = await getTaiLieuBoiThuong(imageDataTmp[viTriHangMuc].ma);
          response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +imageDataTmp[viTriHangMuc].hang_muc.so_id_doi_tuong);
          let imagesTmp = response.map((item) => {
            item.checked = true;
            item.path = item.duong_dan;
            item.name = item.ten_file;
            let nhom = {
              checked: true,
              ma: item.ma_file,
              ten: item.nhom_anh,
              nhom_hang_muc: item.nhom_hang_muc,
            };
            item.nhom = nhom;
            return item;
          });
          setImageData((prevValue) => {
            prevValue[viTriHangMuc].images = imagesTmp;
            prevValue[viTriHangMuc].expanded = !prevValue[viTriHangMuc].expanded;
            return [...prevValue];
          });
        }
      } else {
        for (let i = 0; i < imageDataTmp.length; i++) {
          if (imageDataTmp[i].ma === imageData[0].nhom.ma) {
            imageDataTmp[i].checked = newCheckedValue;
            for (let j = 0; j < imageDataTmp[i].images.length; j++) imageDataTmp[i].images[j].checked = newCheckedValue;
            return setImageData([...imageDataTmp]);
          }
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //1 ảnh được check
  const onPressImageCheck = (imageData) => {
    try {
      // ẢNH TOÀN CẢNH
      if (imageData.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH) {
        let tmp = imageDataStep2.map((item) => {
          if (item.bt === imageData.bt) item.checked = !item.checked;
          return item;
        });
        setImageDataStep2(tmp);
      } else {
        //  ẢNH TỔN THẤT
        let imageDataTmp = imageDataStep3;
        for (let i = 0; i < imageDataTmp.length; i++) {
          let images = imageDataTmp[i].images;
          for (let j = 0; j < images.length; j++)
            if (images[j].bt === imageData.bt) {
              images[j].checked = !images[j].checked;
              return setImageDataStep3([...imageDataTmp]);
            }
        }
        //ẢNH HỒ SƠ, GIẤY TỜ
        imageDataTmp = imageDataStep4;
        for (let i = 0; i < imageDataTmp.length; i++) {
          let images = imageDataTmp[i].images;
          for (let j = 0; j < images.length; j++)
            if (images[j].bt === imageData.bt) {
              images[j].checked = !images[j].checked;
              return setImageDataStep4([...imageDataTmp]);
            }
        }
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // ẤN VÀO 1 HẠNG MỤC CHƯA ĐÁNH GIÁ -> CHUYỂN MÀN ĐÁNH GIÁ
  const onPressDanhGiaHangMucTheoTen = async (hangMucPhanLoai) => {
    try {
      if (hangMucPhanLoai.images.length > 0)
        NavigationUtil.push(SCREEN_ROUTER_APP.CLASSIFY, {
          imagesClassify: hangMucPhanLoai.images,
          profileData,
          xemTaiLieuSelected,
        });
      else {
        setDialogLoading(true);
        let response = await getTaiLieuBoiThuong(hangMucPhanLoai.ma);
        if (response.length === 0) return FlashMessageHelper.showFlashMessage('Thông báo', 'Hạng mục phân loại không có ảnh');
        response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +hangMucPhanLoai.hang_muc.so_id_doi_tuong);
        let imagesTmp = response.map((item) => {
          item.path = item.duong_dan;
          item.name = item.ten_file;
          let nhom = {
            ma: item.ma_file,
            ten: item.nhom_anh,
            nhom_hang_muc: item.nhom_hang_muc,
          };
          item.nhom = nhom;
          return item;
        });
        setImageDataStep3((preValue) => {
          preValue.map((hangMuc) => {
            if (hangMuc.ma === hangMucPhanLoai.ma) hangMuc.images = imagesTmp;
            return hangMuc;
          });
          return [...preValue];
        });
        NavigationUtil.push(SCREEN_ROUTER_APP.CLASSIFY, {
          imagesClassify: imagesTmp,
          profileData,
          xemTaiLieuSelected,
        });
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // CHECK LỖI THÔNG BÁO ĐỂ CHUYỂN MÀN
  const checkThongBaoLoiDeChuyenMan = (messageBody) => {
    let navScreen = '';
    let navParams = {profileData};
    let txtActionButton = 'Đồng ý';
    let subTitle = '';
    if (messageBody === 'Chưa nhập đầy đủ thông tin bên tham gia giám định') {
      navScreen = SCREEN_ROUTER_APP.JOIN_RESOLVE;
      subTitle = '. Vui lòng bổ sung bên tham gia giám định!';
    }
    //Đổi đối tượng
    else if (messageBody === 'Hồ sơ chưa xác định lại đối tượng!' || messageBody === 'Chưa cập nhật lại đối tượng tổn thất') {
      navScreen = SCREEN_ROUTER_APP.DOI_DOI_TUONG;
      subTitle = '. Vui lòng xác định lại đối tượng!';
      txtActionButton = 'Đổi đối tượng';
      navParams = {profileInfo: profileData?.ho_so};
    }
    //lấy số hs
    else if (messageBody === 'Hồ sơ chưa lấy số!') {
      navScreen = 'LAY_SO_HS';
      subTitle = '. Vui lòng lấy số hồ sơ trước khi kết thúc lần giám định';
    }
    // chuyển màn đánh giá hiện trường
    else if (messageBody === 'Xe có ở hiện trường nhưng chưa thực hiện đánh giá hiện trường' || messageBody === 'Bạn chưa nộp báo cáo hiện trường.') {
      navScreen = SCREEN_ROUTER_APP.DANH_GIA_HIEN_TRUONG;
      subTitle = ' Vui lòng đi đến đánh giá hiện trường!';
      navParams = {profileData, danhGiaHienTruong};
    }
    //chuyển màn báo cáo giám định
    else if (messageBody === 'Bạn chưa thực hiện lập báo cáo giám định' || messageBody === 'Bạn chưa thực hiện trình duyệt báo cáo giám định') {
      navScreen = SCREEN_ROUTER_APP.BAO_CAO_GIAM_DINH;
      subTitle = messageBody === 'Bạn chưa thực hiện lập báo cáo giám định' ? '. Vui lòng đi đến lập báo cáo giám định!' : '. Vui lòng đi đến trình duyệt báo cáo giám định!';
    }
    //Chuyển màn đánh giá hiện trường
    else if (messageBody === 'Xe có ở hiện trường nhưng chưa đánh giá hiện trường') {
      navScreen = SCREEN_ROUTER_APP.DANH_GIA_HIEN_TRUONG;
      subTitle = '. Vui lòng đánh giá hiện trường để chụp ảnh Giám định chi tiết';
      navParams = {profileData, danhGiaHienTruong};
    }
    //chuyển màn đặt lịch giám định
    else if (messageBody === 'Bạn chưa đặt lịch giám định với khách hàng.') {
      navScreen = SCREEN_ROUTER_APP.ASSESSMENT_SCHEDULE_PROFILE;
      subTitle = ' Vui lòng đi đến đặt lịch giám định!';
    }
    //chuyển màn nhập ước
    else if (messageBody === 'Ước tổn thất phải lớn hơn 0' || messageBody.startsWith('[UOC_NOTFOUND]')) {
      navScreen = SCREEN_ROUTER_APP.NHAP_UOC_TON_THAT_CHO_HS_CHUA_LAY_SO;
      subTitle = '. Vui lòng cập nhật lại ước tổn thất trước khi lấy số hồ sơ!';
    }
    //chuyển màn ĐỔI THÔNG TIN BIỂN SỐ XE
    else if (messageBody === 'Vui lòng cập nhật thông tin biển xe trước khi lấy số hồ sơ') {
      navScreen = SCREEN_ROUTER_APP.DOI_THONG_TIN_BSX;
      navParams = {profileInfo: profileData?.ho_so};
    }
    //chuyển màn ĐỔI THÔNG TIN BIỂN SỐ XE
    else if (messageBody === 'Chưa nhập vụ tổn thất hoặc chưa có vụ tổn thất nào thuộc phạm vi.') {
      navScreen = SCREEN_ROUTER_APP.DS_CAC_VU_TON_THAT;
      navParams = {profileData: {ho_so: profileData?.ho_so}};
      txtActionButton = 'Kiểm tra';
    }
    if (navScreen !== '') {
      setTimeout(() => {
        Alert.alert('Thông báo', messageBody + subTitle, [
          {
            text: 'Để sau',
            style: 'destructive',
          },
          {
            text: txtActionButton,
            onPress: () => {
              if (navScreen !== 'LAY_SO_HS') NavigationUtil.push(navScreen, navParams);
              // else laySoHoSo();
            },
          },
        ]);
      }, 200);
    }
    //trường hợp có hạng mục chưa đánh giá mức độ tổn thất
    else if (messageBody === 'Tồn tại hạng mục chưa đánh giá tổn thất (mức độ, thay thế/sửa chữa, thu hồi)') {
      Alert.alert('Thông báo', messageBody, [
        {
          text: 'Để sau',
          style: 'destructive',
        },
        {
          text: 'Xem hạng mục chưa đánh giá',
          onPress: () => tabViewRef.current?.goToPage(1),
        },
      ]);
    } else Alert.alert('Thông báo', messageBody);
  };

  //LẤY DANH SÁCH NGHIỆP VỤ CỦA HỒ SƠ
  const getDSNghiepVu = async (id) => {
    let params = {
      so_id: id,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_NGHIEP_VU_CUA_HS, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const dataNv = response?.data_info?.lhnv;
      let arrOptions = dataNv.map((e) => {
        return e.ten;
      });
      setDataNV(response?.data_info);
      setDsNghiepVuKhac(dataNv);
      setOptionsLapPhuongAnKhac(arrOptions);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  //nút CHỤP ẢNH GIÁM ĐỊNH
  const onPressTakePicture = useCallback(
    (index, loaiAnh) => {
      try {
        let paramsRoute = {
          profileData,
          doiTuongDuocChupAnh: doiTuongDuocChon,
          danhGiaHienTruong,
        };
        if (loaiAnh === 'ANH_HO_SO' || loaiAnh === 'ANH_NGHIEM_THU' || loaiAnh === 'ANH_THVT') paramsRoute.loaiAnh = 'ANH_HO_SO';
        if (loaiAnh === 'ANH_HO_SO' || loaiAnh === 'ANH_NGHIEM_THU' || loaiAnh === 'ANH_THVT') paramsRoute.hangMucAnh = loaiAnh;
        if (loaiAnh === 'ANH_HIEN_TRUONG' || 'XMHT') paramsRoute.loaiAnh = loaiAnh;
        if (loaiAnh === 'ANH_TON_THAT') paramsRoute.loaiAnh = loaiAnh;
        if (index === chupAnhIndex.CHUP_ANH_GIAM_DINH) NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_HIEN_TRUONG, paramsRoute);
        else if (index === chupAnhIndex.CHUP_ANH_CHI_TIET) NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_CHI_TIET, paramsRoute);
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    },
    [profileData, doiTuongDuocChon, danhGiaHienTruong],
  );

  const onPressBoSungAnh = (type) => {
    try {
      if (type === 0) {
        if (profileData.ds_doi_tuong.length === 1) setDoiTuongDuocChon({...profileData.ds_doi_tuong[0]});
        else setToggleModalChonDoiTuongHoSo(true);
      } else {
        if (profileData.ds_doi_tuong.length === 1) setDoiTuongDuocChon({...profileData.ds_doi_tuong[0]});
        else setToggleModalChonDoiTuongHoSo(true);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const onPressChonDoiTuongChupAnh = useCallback(() => {
    if (profileData.ho_so.nv_ma === 'BB' && profileData.ds_doi_tuong.length === 1) setToggleModalChonDoiTuong(true);
    else if (profileData.ds_doi_tuong.length === 1) setDoiTuongDuocChon({...profileData.ds_doi_tuong[0]});
    else setToggleModalChonDoiTuong(true);
  }, [profileData]);

  const onPressChupAnhChiTiet = useCallback(
    async (loaiAnh) => {
      if (loaiAnh) return onPressTakePicture(chupAnhIndex.CHUP_ANH_CHI_TIET, loaiAnh);
      try {
        setDialogLoading(true);
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHECK_GIAM_DINH_CHI_TIET, {so_id: profileData.ho_so.so_id});
        setDialogLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return checkThongBaoLoiDeChuyenMan(response.state_info.message_body);
        onPressTakePicture(chupAnhIndex.CHUP_ANH_CHI_TIET, loaiAnh);
      } catch (error) {
        setDialogLoading(false);
        Alert.alert('Thông báo', error.message);
      }
    },
    [profileData, onPressTakePicture],
  );
  //CHỌN LOẠI GIÁM ĐỊNH HIỆN TRƯỜNG / GIÁM ĐỊNH CHI TIẾT
  const onPressChonLoaiGiamDinh = useCallback(
    async (index, loaiAnh = '') => {
      if (index === 2) return;
      //nếu là CHỤP ẢNH CHI TIẾT
      else if (index === chupAnhIndex.CHUP_ANH_CHI_TIET) onPressChupAnhChiTiet(loaiAnh);
      //chụp ảnh HIỆN TRƯỜNG
      else if (index === chupAnhIndex.CHUP_ANH_GIAM_DINH) {
        try {
          setDialogLoading(true);
          let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.BAT_DAU_CHUP_ANH_HIEN_TRUONG, {so_id: profileData.ho_so.so_id, ma_doi_tac: profileData.ho_so.ma_doi_tac});
          setDialogLoading(false);
          if (!response || !response.state_info || response.state_info.status !== 'OK') return checkThongBaoLoiDeChuyenMan(response.state_info.message_body);
          onPressTakePicture(chupAnhIndex.CHUP_ANH_GIAM_DINH);
        } catch (error) {
          setDialogLoading(false);
          Alert.alert('Thông báo', error.message);
        }
      }
      setToggleModalChonDoiTuong(false);
    },
    [profileData, onPressChupAnhChiTiet, onPressTakePicture],
  );

  //GỠ HUỶ HỒ SƠ
  const onPressGoHuyHoSo = () => {
    Alert.alert('Gỡ huỷ hồ sơ', 'Bạn có chắc chắn muốn gỡ huỷ hồ sơ không?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Gỡ huỷ',
        onPress: async () => {
          try {
            const params = {
              so_id: profileData?.ho_so?.so_id, //id hồ sơ
              ma_doi_tac: profileData?.ho_so.ma_doi_tac,
            };
            setDialogLoading(true);
            let response = await CarClaimEndpoint.goHuyHoSoGiamDinhOTo(AxiosConfig.ACTION_CODE.GO_HUY_HO_SO_GIAM_DINH_OTO, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Gỡ huỷ hồ sơ giám định thành công!', 'success');
            NavigationUtil.pop();
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const openActionSheetChupAnh = () => actionSheetChupAnhRef?.show();
  //XÁC NHẬN CHUYỂN THÀNH TOÁN

  //ĐÓNG / MỞ 1 HẠNG MỤC
  const onPressToggleExpandHangMuc = async (index) => {
    try {
      let imageDataTmp = [];
      let setImageData = null;
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        imageDataTmp = imageDataStep3;
        setImageData = setImageDataStep3;
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        imageDataTmp = imageDataStep4;
        setImageData = setImageDataStep4;
      }
      //nếu có ảnh thì expand ra luôn nhưng vẫn gọi lại API để lấy ảnh
      let daExpand = false; //đã expand rồi thì bên dưới k cần gọi expand nữa
      let newValueExpand = null;
      if (imageDataTmp[index].images.length > 0) {
        daExpand = true;
        setImageData((prevValue) => {
          prevValue[index].expanded = !prevValue[index].expanded; //cập nhật giá trị expand mới
          newValueExpand = prevValue[index].expanded;
          return [...prevValue];
        });
      }
      //nếu là thu lại thì k gọi API
      if (newValueExpand === false) return;
      refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(index);
      let response = await getTaiLieuBoiThuong(imageDataTmp[index].ma);
      response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +imageDataTmp[index].hang_muc.so_id_doi_tuong);
      refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
      let imagesTmp = response.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
          nhom_hang_muc: item.nhom_hang_muc,
        };
        item.nhom = nhom;
        return item;
      });
      setImageData((prevValue) => {
        prevValue[index].images = imagesTmp;
        if (!daExpand) prevValue[index].expanded = !prevValue[index].expanded; //bên trên chưa expand thì mới cần expand ra
        return [...prevValue];
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressExpandAllHangMuc = async (expandAllHangMuc) => {
    try {
      if (xemTaiLieuSelected === 'ANH_TON_THAT') {
        let imageDataStep3Tmp = [...imageDataStep3];
        for (let i = 0; i < imageDataStep3Tmp.length; i++) {
          let hangMucAnh = imageDataStep3Tmp[i];
          if (expandAllHangMuc === true) {
            if (hangMucAnh.images.length === 0) {
              refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(i);
              let response = await getTaiLieuBoiThuong(hangMucAnh.ma);
              response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +hangMucAnh.hang_muc.so_id_doi_tuong);
              let imagesTmp = response.map((item) => {
                item.checked = false;
                item.path = item.duong_dan;
                item.name = item.ten_file;
                let nhom = {
                  checked: false,
                  ma: item.ma_file,
                  ten: item.nhom_anh,
                  nhom_hang_muc: item.nhom_hang_muc,
                };
                item.nhom = nhom;
                return item;
              });
              imageDataStep3Tmp[i].images = imagesTmp;
            }
            imageDataStep3Tmp[i].expanded = true;
          } else if (expandAllHangMuc === false) imageDataStep3Tmp[i].expanded = false;
        }
        refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
        setImageDataStep3([...imageDataStep3Tmp]);
      } else if (xemTaiLieuSelected === 'ANH_HO_SO') {
        let imageDataStep4Tmp = [...imageDataStep4];
        for (let i = 0; i < imageDataStep4Tmp.length; i++) {
          let hangMucAnh = imageDataStep4Tmp[i];
          if (expandAllHangMuc === true) {
            if (hangMucAnh.images.length === 0) {
              refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(i);
              let response = await getTaiLieuBoiThuong(hangMucAnh.ma);
              response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +hangMucAnh.hang_muc.so_id_doi_tuong);
              let imagesTmp = response.map((item) => {
                item.checked = false;
                item.path = item.duong_dan;
                item.name = item.ten_file;
                let nhom = {
                  checked: false,
                  ma: item.ma_file,
                  ten: item.nhom_anh,
                  nhom_hang_muc: item.nhom_hang_muc,
                };
                item.nhom = nhom;
                return item;
              });
              imageDataStep4Tmp[i].images = imagesTmp;
            }
            imageDataStep4Tmp[i].expanded = true;
          } else if (expandAllHangMuc === false) imageDataStep4Tmp[i].expanded = false;
        }
        refTabTaiLieuBoiThuongOTo?.current?.setViTriHangMucDangTai(null);
        setImageDataStep4([...imageDataStep4Tmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [dataCacVuTonThat, setdataCacVuTonThat] = useState([]);

  // const validateBtnHuy = [
  //   +moment(profileData?.ho_so?.ngay_huy).format('YYYYMMDD') === null,
  //   +moment(profileData?.ho_so?.ngay_huy).format('YYYYMMDD') === 30000101,
  //   +moment(profileData?.ho_so?.ngay_huy).format('YYYYMMDD')?.toString().trim() === '',
  // ];
  // const validateAllBtn = [
  //   +moment(profileData?.ho_so?.ngay_chuyen).format('YYYYMMDD') === null,
  //   +moment(profileData?.ho_so?.ngay_chuyen).format('YYYYMMDD') === 30000101,
  //   +moment(profileData?.ho_so?.ngay_chuyen).format('YYYYMMDD')?.toString().trim() === '',
  // ];

  useEffect(() => {
    navigation.addListener('focus', () => {
      initData();
    });
  }, []);

  const initData = async () => {
    await getChiTietHoSo(route.params.profileDetail);
  };

  const onRefresh = useCallback(() => {
    getChiTietHoSo(profileData?.ho_so);
  }, [profileData]);

  const getChiTietHoSo = async (profileDetail) => {
    //lấy chi tiết hồ sơ
    try {
      let paramsProfileDetail = {
        ma_doi_tac: profileDetail?.ma_doi_tac || '',
        so_id_knai: profileDetail.so_id_knai,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_CT_HS_KHIEN_NAI, paramsProfileDetail);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setProfileData(response.data_info);
      setdataCacVuTonThat(response.data_info.dien_bien);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // const onPressHuyHoSo = () => {
  //   Alert.alert('Thông báo', 'Bạn có chắc chắn muốn huỷ hồ sơ tiếp nhận', [
  //     {
  //       text: 'Huỷ',
  //       style: 'cancel',
  //     },
  //     {text: 'Đồng ý', onPress: () => handleHuyHoSo()},
  //   ]);
  // };

  // const handleHuyHoSo = async () => {
  //   try {
  //     const params = {
  //       so_id: profileData?.ho_so?.so_id, //id hồ sơ
  //       ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
  //     };
  //     let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_HO_SO_TIEP_NHAN, params);
  //     if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //     FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ hồ sơ tiếp nhận thành công', 'success');
  //     if (prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1) NavigationUtil.pop(2);
  //     else NavigationUtil.pop();
  //   } catch (error) {
  //     Alert.alert('Thông báo', error.message);
  //   }
  // };

  /** RENDER */

  // const switchButton = () => {
  //   if (validateBtnHuy.indexOf(true) === -1) {
  //     return <ButtonLinear title="Gỡ huỷ hồ sơ" onPress={onPressGoHuyHoSo} linearStyle={styles.footerBtn} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />;
  //   } else {
  //     return <ButtonLinear title="Huỷ hồ sơ" onPress={onPressHuyHoSo} linearStyle={styles.footerBtn} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />;
  //   }
  // };

  // const renderFooter = () => {
  //   return (
  //     <View style={styles.footerView}>
  //       {btnTabActive == 0 && (
  //         <View style={styles.doubleBtn}>
  //           {switchButton()}
  //           {/* <ButtonLinear title="Huỷ hồ sơ" onPress={onPressHuyHoSo} linearStyle={styles.footerBtn} linearColors={[colors.GRAY, colors.GRAY]} textStyle={{color: colors.BLACK_03}} /> */}
  //           {validateBtnHuy.indexOf(true) !== -1 ? (
  //             <ButtonLinear title="Khai báo sự vụ" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT, {profileData: profileData})} linearStyle={styles.footerBtn} />
  //           ) : null}
  //         </View>
  //       )}
  //       {btnTabActive == 1 && validateBtnHuy.indexOf(true) !== -1 ? (
  //         <View style={styles.doubleBtn}>
  //           <ButtonLinear title="Khai báo sự vụ" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT, {profileData: profileData})} linearStyle={styles.footerBtn} />
  //           {dataCacVuTonThat?.length > 0 && (
  //             <ButtonLinear title="Tiếp theo" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.KHAI_BAO_TT_DIA_DIEM_GIAM_DINH, {profileData: profileData})} linearStyle={[styles.footerBtn]} />
  //           )}
  //         </View>
  //       ) : null}
  //     </View>
  //   );
  // };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      onPressBack={prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1 ? () => NavigationUtil.pop(2) : () => NavigationUtil.pop()}
      headerTitle={'Chi tiết hồ sơ khiếu nại'}
      renderView={
        <View style={styles.container}>
          <ScrollableTabView ref={tabViewRef} style={styles.centerView} initialPage={0} onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))} renderTabBar={() => <CustomTabBar />}>
            <ScrollView
              tabLabel="Thông tin hồ sơ"
              scrollEnabled={true}
              refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}
              ref={scrollViewRef}
              showsVerticalScrollIndicator={false}>
              <ChiTietHoSo profileData={profileData} />
            </ScrollView>
            <View tabLabel="Tài liệu khiếu nại" style={styles.centerView}>
              <TabTaiLieu
                ref={refTabTaiLieuBoiThuongOTo}
                // onPressRemoveAnh={onPressRemoveAnh}
                profileData={profileData}
                imageDataStep1={imageDataStep1}
                imageDataStep2={imageDataStep2}
                imageDataStep3={imageDataStep3}
                setImageDataStep3={setImageDataStep3}
                imageDataStep4={imageDataStep4}
                anhDanhGiaRuiRo={anhDanhGiaRuiRo}
                anhNghiemThu={anhNghiemThu}
                anhThuHoiVatTu={anhThuHoiVatTu}
                listTaiLieuPdf={listTaiLieuPdf}
                anhXacMinhHienTruong={anhXacMinhHienTruong}
                onPressOpenImageView={onPressOpenImageView}
                // xử lý CHỌN ẢNH
                switchImgView={switchImgView}
                setSwitchImgView={() => setSwitchImgView(!switchImgView)}
                onPressToggleExpandHangMuc={onPressToggleExpandHangMuc}
                onPressDanhGiaHangMucTheoTen={onPressDanhGiaHangMucTheoTen}
                // xử lý hiển thị Thông tin phân loại
                onPressToggleCheckAll={onPressToggleCheckAll}
                onPressImageCheck={onPressImageCheck}
                // dropdown ảnh tài liệu
                setXemTaiLieuSelected={setXemTaiLieuSelected}
                xemTaiLieuSelected={xemTaiLieuSelected}
                //modal chọn đối tượng
                toggleModalChonDoiTuongHoSo={toggleModalChonDoiTuongHoSo}
                setToggleModalChonDoiTuongHoSo={setToggleModalChonDoiTuongHoSo}
                setDoiTuongDuocChon={setDoiTuongDuocChon}
                prevScreen={route.params.prevScreen}
                dataAnhCapDon={dataAnhCapDon}
                onPressExpandAllHangMuc={onPressExpandAllHangMuc}
                prevScreenIsHoSoDiaBan={prevScreenIsHoSoDiaBan}
              />
            </View>
          </ScrollableTabView>
          {/* {validateAllBtn.indexOf(true) === -1 ? null : renderFooter()} */}
        </View>
      }
    />
  );
};

export const ChiTietHoSoKhieuNaiScreen = memo(ChiTietHoSoKhieuNaiScreenComponent, isEqual);
