import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    flex: 1,
  },
  btnTop: {
    // flex: 1,
    padding: 7,
    width: dimensions.width / 2,
    borderRightWidth: 1,
    flexDirection: 'row',
    borderBottomWidth: 1,
    alignItems: 'stretch',
    justifyContent: 'center',
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE4,
  },
  btnActive: {
    borderBottomWidth: 0,
    backgroundColor: colors.WHITE,
  },
  iconBtnTopView: {
    marginRight: 15,
    alignSelf: 'center',
  },
  btnTopView: {
    width: dimensions.width,
    flexDirection: 'row',
  },
  topView: {},
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    justifyContent: 'center',
    backgroundColor: colors.WHITE5,
    // borderWidth: 1,
    // justifyContent : 'space-between'
    // flex : 1
  },
  txtTitle: {
    fontSize: 12,
    marginBottom: 4,
  },
  txtDetail: {
    paddingRight: 10,
    color: colors.GRAY6,
    textAlign: 'justify',
  },

  txtAccidentDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    // fontSize: 14,
  },
  joinResolveDetailView: {
    flex: 1,
    paddingLeft: 16,
    paddingVertical: 3,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  joinResolveView: {
    flex: 1,
    marginTop: 5,
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
  },

  inforView: {
    flex: 1,
    paddingLeft: 16,
    paddingVertical: 13,
    borderTopWidth: 0.5,
    borderColor: colors.GRAY4,
  },
  inforHeaderView: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    justifyContent: 'space-between',
  },
  accidentHeader: {
    paddingVertical: 13,
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  txtTime: {
    fontSize: 12,
  },
  accidentView: {
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
  },
  txtLocation: {
    color: colors.GREEN2,
    fontSize: 12,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  accidentInforView: {
    paddingHorizontal: 16,
    paddingVertical: 13,
  },
  resolveAccidentInforView: {
    paddingHorizontal: 16,
    // paddingVertical: 13,
  },
  resolveAccidentView: {
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
  },
  btnReceivingRecords: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    // minHeight: 50,
    borderRadius: 10,
    flex: 1,
  },
  linearBtnView: {
    flex: 1,
    borderRadius: 30,
    marginHorizontal: 10,
    backgroundColor: colors.WHITE,
  },
  btnRequestEnd: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    minHeight: 50,
    flex: 1,
  },
  txtBtnReceivingRecords: {
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  txtBtnRequestEnd: {
    color: colors.BLACK,
    fontWeight: 'bold',
  },
  btnsFilterDocumentView: {
    flexDirection: 'row',
    // flex: 1,
    borderBottomColor: colors.GRAY,
    paddingBottom: 8,
    borderBottomWidth: 1,
    marginHorizontal: 10,
    // borderWidth : 1
  },
  btnFilterDocument: {
    borderWidth: 1,
    borderRadius: 20,
    padding: 8,
    borderColor: colors.GRAY,
    marginRight: 8,
  },
  btnFilterDocumentActive: {
    backgroundColor: colors.PRIMARY,
  },
  txtBtnFilterDocumentActive: {
    fontWeight: 'bold',
    color: colors.WHITE,
  },

  txtBtnFilterDocument: {
    color: colors.PRIMARY,
  },
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 10,
  },
  pdfComponent: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
  },
  headerTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 5,
    paddingTop: 20,
    marginLeft: 20,
    textDecorationLine: 'underline',
  },
  headerSubTitle: {
    fontSize: 14,
    marginBottom: 5,
    marginLeft: 20,
  },
  callView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  actionButtonIcon: {
    fontSize: 20,
    height: 22,
    color: 'white',
  },
  shadowStyle: {
    shadowOpacity: 0.35,
    // shadowOffset: {
    //   width: 0,
    //   height: 5,
    // },
    // shadowColor: colors.BLACK,
    // shadowRadius: 3,
    // elevation: 5,
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    borderWidth: 1,
    borderRadius: 35,
  },
  detailContent: {
    fontWeight: '500',
    color: colors.BLACK_03,
  },
  contentRow: {
    marginTop: 2,
    marginBottom: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contentCol: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  txtBtnThemChiPhi: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  subLabel: {
    fontSize: 16,
    marginVertical: 5,
    fontWeight: '700',
    color: colors.PRIMARY,
  },
  profileItemView: {
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: 5,
    paddingVertical: 5,
    marginHorizontal: 10,
    paddingHorizontal: 10,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  labelItem: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.GRAY6,
  },
  footerView: {
    width: dimensions.width,
    paddingVertical: 10,
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: 12,
  },
  doubleBtn: {
    width: dimensions.width,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rightIcon: {
    opacity: 0.6,
    alignSelf: 'center',
    position: 'absolute',
    right: 0,
  },
});
