import R from '@app/assets/R';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useSelector} from 'react-redux';

import styles from './Styles';
import ListProfilesHeader from '@app/components/ListProfilesHeader';
import {ModalTimKiemHoSoKhieuNai} from './Compontents';

let timer;
const startOfMonth = moment().startOf('month').format('YYYYMMDD');
const currentDate = moment(new Date()).format('YYYYMMDD');

const DanhSachHoSoKhieuNaiScreenComponent = ({navigation, route}) => {
  console.log('DanhSachHoSoKhieuNaiScreenComponent');
  const userInfo = useSelector(selectUser);
  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const user = userInfo?.nguoi_dung;

  let refModalTimKiemHoSo = useRef(null);

  const [objParams, setObjParams] = useState({
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    ma_doi_tac: user?.ma_doi_tac,
    // ma_chi_nhanh: user?.ma_chi_nhanh,
    ma_chi_nhanh: [],
    ma_chi_nhanh_ql: [],
    doi_tuong: '',
    trang_thai: '', // D, C, H đã chuyển / chưa chuyển / huỷ
    so_hs: '',
  });

  const [data, setData] = useState([]);
  const [total, setTotal] = useState(20);
  const [current, setCurrent] = useState(1);

  useEffect(() => {
    // navigation.addListener('focus', () => {
    //   getData(objParams);
    // });
    getData(objParams);
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getData = async (defaultObj, oldData = [], trang = 1, so_dong = 20, isSearch = false) => {
    setDialogLoading(true);
    let subObj = {
      trang: trang,
      so_dong: so_dong,
    };
    let params = {...subObj, ...defaultObj};
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HS_KHIEN_NAI, params);
      setRefreshing(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;

      if (response.data_info.data.length > 0) {
        refModalTimKiemHoSo.current.hide();
      } else if (isSearch) {
        Alert.alert('Thông báo', 'Không tìm thấy kết quả phù hợp!');
      }
      setTotal(response.data_info.tong_so_dong);
      let arrData = response.data_info.data;
      let mergeData = [...oldData, ...arrData];
      setData(mergeData);
    } catch (error) {
      setRefreshing(false);
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onRefresh = () => {
    setDialogLoading(true);
    setCurrent(1);
    getData(objParams);
  };

  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setCurrent(current + 1);
      getData(objParams, data, current + 1);
    }
  };

  const onPressSearch = (params) => {
    getData(params, [], 1, 20, true);
    setObjParams(params);
  };

  const renderProfileItem = ({item}) => {
    const statusColorText = item.trang_thai_ten === 'Đã chuyển' ? colors.GREEN : item.trang_thai_ten === 'Đang xử lý' ? colors.PRIMARY : item.trang_thai_ten === 'Đã hủy' ? colors.RED1 : null;
    const renderLabel = (title, value, style) => {
      return (
        <Text style={[styles.label]}>
          {title}: <Text style={[styles.detail, style]} children={value} />
        </Text>
      );
    };
    return (
      <TouchableOpacity onPress={() => NavigationUtil.navigate(SCREEN_ROUTER_APP.CT_HS_KHIEU_NAI, {profileDetail: item, prevScreen: route.name})}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            {renderLabel('Số hồ sơ', item.so_hs, styles.txtSoHS)}
            {renderLabel('Biển số xe', item.doi_tuong)}
            {renderLabel('Ngày khiếu nại', moment(item.ngay_ht, 'YYYYMMDD').format('DD/MM/YYYY'))}
            {renderLabel('Lần khiếu nại', item.lan_knai)}
            {renderLabel('Người khiếu nại', item.ten_kh)}
            {renderLabel('SĐT người khiếu nại', item.dthoai_knai)}
            {renderLabel('Người xử lý', item.ten_nsd)}
            {renderLabel('Trạng thái', item.trang_thai_ten, {color: statusColorText})}
          </View>
          <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  // footer

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Hồ sơ khiếu nại"
      renderView={
        <SafeAreaView style={styles.container}>
          <ListProfilesHeader ngayDau={objParams.ngay_d} ngayCuoi={objParams.ngay_c} onPressSearch={() => refModalTimKiemHoSo.current.show()} />
          <FlatList
            data={data}
            renderItem={renderProfileItem}
            keyExtractor={(_, index) => index.toString()}
            onEndReachedThreshold={0.5}
            onEndReached={handleLoadMore}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            ListEmptyComponent={
              <View style={styles.noDataView}>
                <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
                <Text>Chưa có dữ liệu</Text>
              </View>
            }
          />
          <ModalTimKiemHoSoKhieuNai
            ref={refModalTimKiemHoSo}
            userInfo={userInfo}
            initFormInput={objParams}
            setValue={setObjParams}
            onBackPress={() => refModalTimKiemHoSo.current.hide()}
            onPressSearch={(params) => onPressSearch(params)}
            loading={dialogLoading}
          />
        </SafeAreaView>
      }
    />
  );
};

export const DanhSachHoSoKhieuNaiScreen = memo(DanhSachHoSoKhieuNaiScreenComponent, isEqual);
