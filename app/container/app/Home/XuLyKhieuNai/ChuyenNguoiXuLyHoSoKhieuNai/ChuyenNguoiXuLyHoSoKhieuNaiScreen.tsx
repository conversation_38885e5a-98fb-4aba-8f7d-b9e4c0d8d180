import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';
import {ModalNguoiXuLy} from './Components';
import styles from './Styles';

const titleInput = ['Đơn vị xử lý', 'Ngư<PERSON>i xử lý', '<PERSON><PERSON> chú'];

const ChuyenNguoiXuLyHoSoKhieuNaiScreenComponent = ({route}) => {
  console.log('ChuyenNguoiXuLyHoSoKhieuNaiScreenComponent');
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const {profileData} = route.params;

  const [dialogLoading, setDialogLoading] = useState(false);
  const [openNguoiXuLy, setOpenNguoiXuLy] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);
  let refModalNguoiXuLy = useRef(null);

  useEffect(() => {
    getDsNguoiXuLy();
  }, []);

  const getDefaultFormValue = () => {
    try {
      return {
        donViXuLy: profileData?.ho_so?.ma_chi_nhanh || '',
        nguoiXuLy: null,
        ghiChu: '',
      };
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const nguoiXuLy = watch('nguoiXuLy');

  const getTenHienThi = (value, data = [], field) => {
    let name = '';
    if (field === 'donViXuLy') {
      data.map((e) => {
        if (e.ma_chi_nhanh === value) name = e.ten_chi_nhanh;
      });
    } else {
      data.map((e) => {
        if (e.ma === value) name = e.ten;
      });
    }
    return name;
  };

  const closeDropdown = () => {
    setOpenNguoiXuLy(false);
  };

  const onPressSave = async (data) => {
    Alert.alert('Chuyển người xử lý', 'Bạn có chắc chắn muốn chuyển người xử lý hồ sơ này không?', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          setDialogLoading(true);
          try {
            let donViNguoiXuLy = listNguoiXuLy.find((item) => item.ma === data.nguoiXuLy);
            let params = {
              ma_doi_tac: profileData.ho_so.ma_doi_tac,
              so_id_knai: profileData.ho_so.so_id_knai,
              nsd_moi: data.nguoiXuLy,
              ma_chi_nhanh_moi: donViNguoiXuLy ? donViNguoiXuLy.ma_chi_nhanh : '',
              ghi_chu: data.ghiChu,
            };
            let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHUYEN_NGUOI_XU_LY_HS_KHIEU_NAI, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển người xử lý thành công!', 'success');
            NavigationUtil.pop(2);
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  // Phải đủ cả biến title, items không là bị lỗi do hàm của Dropdown picker
  const getDsNguoiXuLy = async () => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh,
    };
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
      if (openNguoiXuLy) setOpenNguoiXuLy(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let giamDinhVien = response.data_info.map((item) => {
        item.value = item.ma;
        item.label = item.ten + (item.ten_chuc_danh ? ` (${item.ten_chuc_danh})` : '');
        return item;
      });
      setListNguoiXuLy([...giamDinhVien]);
      setValue('nguoiXuLy', '', {shouldValidate: true});
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  /**RENDER  */
  const renderContent = () => {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView scrollEnabled={true}>
          <View style={styles.contentView}>
            <Controller
              control={control}
              name="donViXuLy"
              rules={{
                required: true,
              }}
              render={({field: {value}}) => (
                <>
                  <TextInputOutlined
                    disabled
                    editable={false}
                    isDropdown
                    title="Đơn vị xử lý"
                    value={getTenHienThi(value, chiNhanhBaoHiemDangCay, 'donViXuLy')}
                    placeholder={titleInput[0]}
                    isRequired={true}
                    inputStyle={{color: colors.BLACK_03}}
                  />
                </>
              )}
            />

            <Controller
              control={control}
              name="nguoiXuLy"
              rules={{
                required: true,
              }}
              render={({field: {value}}) => (
                <TextInputOutlined
                  isRequired
                  isDropdown
                  cleared={value !== null && value !== ''}
                  isTouchableOpacity
                  editable={false}
                  title="Người xử lý"
                  placeholder="Chọn người xử lý"
                  inputStyle={{color: colors.BLACK}}
                  value={getTenHienThi(value, listNguoiXuLy, '')}
                  onPress={() => refModalNguoiXuLy.current.show()}
                  onPressClear={() => setValue('nguoiXuLy', '')}
                  error={errors.nguoiXuLy && getErrMessage('nguoiXuLy', errors.nguoiXuLy.type)}
                />
              )}
            />

            {/* lý do */}
            <Controller
              control={control}
              name="ghiChu"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined title={titleInput[2]} value={value} onChangeText={onChange} placeholder={'Nhập ghi chú'} onFocus={closeDropdown} multiline={true} numberOfLines={2} />
              )}
            />
          </View>
        </KeyboardAwareScrollView>

        <ModalNguoiXuLy
          value={nguoiXuLy}
          data={listNguoiXuLy}
          ref={refModalNguoiXuLy}
          onBackPress={() => refModalNguoiXuLy.current.hide()}
          setValue={(val) => setValue('nguoiXuLy', val.ma, {shouldValidate: true})}
        />
      </View>
    );
  };
  return (
    <ScreenComponent dialogLoading={dialogLoading} headerBack headerTitle="Chuyển người xử lý" renderView={renderContent()} footer={<ButtonLinear title="Lưu" onPress={handleSubmit(onPressSave)} />} />
  );
};

export const ChuyenNguoiXuLyHoSoKhieuNaiScreen = memo(ChuyenNguoiXuLyHoSoKhieuNaiScreenComponent, isEqual);
