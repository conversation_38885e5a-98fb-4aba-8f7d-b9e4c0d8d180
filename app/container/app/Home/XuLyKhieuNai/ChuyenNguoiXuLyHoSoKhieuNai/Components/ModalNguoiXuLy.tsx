import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing, vScale} from '@app/theme';
import {CheckboxComp, Empty, HeaderModal, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalNguoiXuLyComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, value, data} = props;

  const [dataGara, setDataGara] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  const [itemSelected, setItemSelected] = useState(false);
  const [searchInput, setSearchInput] = useState('');

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => {
        setIsVisible(false);
      },
    }),
    [],
  );

  useEffect(() => {
    initModalData();
  }, [data]);

  const initModalData = () => {
    let cloneData = data;
    cloneData.map((e, i) => {
      cloneData[i].isChecked = false;
      if (e.ma === value) cloneData[i].isChecked = true;
    });
    setDataGara([...cloneData]);
  };

  const onPressSearch = () => {};

  const onSave = () => {
    setValue && setValue(itemSelected);
    onBackPress && onBackPress();
  };

  const onChangeCheckBoxValue = (index) => {
    let cloneData = dataGara;
    cloneData.map((e, i) => {
      cloneData[i].isChecked = false;
      cloneData[index].isChecked = true;
    });
    setDataGara([...cloneData]);
    setItemSelected(cloneData[index]);
  };

  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 500);

  useEffect(() => {
    if (searchInput) {
      const lowerCaseSearchText = searchInput?.toLowerCase();
      const filter = data.filter((item) => item.ten.toLowerCase()?.includes(lowerCaseSearchText) || item.ma.toLowerCase()?.includes(lowerCaseSearchText));
      setDataGara(filter);
    }
    if (searchInput === '') {
      setDataGara(data);
    }
  }, [searchInput]);

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(index)}>
        <CheckboxComp disabled value={item.isChecked} checkboxStyle={styles.checkbox} />
        <View style={styles.txtView}>
          <Text>
            {item.ten}
            {item.ten_chuc_danh && (
              <Text style={styles.subValue} font="bol14">
                ({item.ten_chuc_danh})
              </Text>
            )}
          </Text>
          <Text>{item.ma}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal onModalWillShow={initModalData} style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal
          onPressRight={onSave}
          onBackPress={onBackPress}
          title="Chọn người xử lý"
          // centerComponent={<SearchBar placeholder="Tìm kiếm đơn vị giám định ..." onSubmitEditing={onPressSearch} onTextChange={onChangeText} onPressSearch={onPressSearch} />}
          rightComponent={<Text style={styles.txtBtnSave}>Chọn</Text>}
        />
        <SearchBar placeholder="Tìm kiếm giám định viên..." onSubmitEditing={onPressSearch} onTextChange={debounced} onPressSearch={onPressSearch} />
        <FlatList
          data={dataGara}
          extraData={dataGara}
          renderItem={renderItem}
          onEndReachedThreshold={0.3}
          style={styles.flStyles}
          keyExtractor={(item, index) => item + index.toString()}
          ListEmptyComponent={<Empty imageStyle={styles.emptyImage} />}
        />
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 20,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    // paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: vScale(6),
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginRight: 10,
  },
  txtView: {
    flex: 1,
    marginTop: !isIOS ? 4 : 2,
    // flexDirection: 'row',
  },
  flStyles: {
    // marginBottom: 60,
    paddingHorizontal: spacing.small,
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  subValue: {
    fontStyle: 'italic',
    color: colors.BLUE1,
  },
});

export const ModalNguoiXuLy = memo(ModalNguoiXuLyComponent, isEqual);
