import {colors} from '@app/commons/Theme';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet, Dimensions} from 'react-native';
const {width} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  inputStyle: {
    flex: 1,
    height: 60,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.GRAY,
    marginHorizontal: scale(spacing.small),
    paddingHorizontal: vScale(spacing.small),
    textAlignVertical: 'top',
  },
  item: {
    alignItems: 'flex-start',
    paddingTop: vScale(spacing.tiny),
  },
  content: {
    flex: 1,
    marginHorizontal: scale(spacing.small),
  },
  footerView: {
    width: width,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: scale(spacing.medium),
  },
  boxChat: {
    margin: scale(spacing.tiny),
    padding: scale(spacing.small),
    borderTopLeftRadius: 0,
    borderRadius: spacing.smaller,
    backgroundColor: colors.WHITE1,
    maxWidth: width * 0.7,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtTime: {
    lineHeight: 20,
    fontStyle: 'italic',
    color: colors.GRAY6,
    fontSize: FontSize.size10,
    marginRight: scale(spacing.smaller),
  },
  txtNoiDung: {
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
  },
  txtTitle: {
    lineHeight: 20,
    color: colors.BLACK_03,
    fontSize: FontSize.size12,
  },
  isUserBoxChatStyles: {
    borderTopRightRadius: 0,
    backgroundColor: '#E7F3FF',
    borderTopLeftRadius: spacing.smaller,
  },
  txtEmpty: {
    textAlign: 'center',
    marginVertical: vScale(10),
  },
  marginBottom: {
    marginBottom: vScale(70),
  },
});
