import {isAndroid, isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {Icon, ScreenComponent, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Keyboard, SafeAreaView, TextInput, TouchableOpacity, View} from 'react-native';
import {getStatusBarHeight, isIphoneX} from 'react-native-iphone-x-helper';
import styles from './Styles';

const YKienTraoDoiHSKhieuNaiScreenComponent = () => {
  console.log('YKienTraoDoiHSKhieuNaiScreenComponent');
  const route = useRoute();
  const {profileData} = route?.params;
  const [data, setData] = useState([]);
  const [text, setText] = useState('');
  let refContainer = useRef(null);
  const [bottomPosition, setBottomPosition] = useState(0);
  const [loading, setLoading] = useState(false);

  let keyboardDidShow;
  let keyboardDidHide;

  function onKeyboardDidShow(e) {
    if (!e.endCoordinates) return;
    setBottomPosition(e.endCoordinates.height - (isIOS && isIphoneX() ? getStatusBarHeight() : 0));

    // setIsShowKeyboard(true);
  }

  function onKeyboardDidHide() {
    setBottomPosition(0);
    // setIsShowKeyboard(false);
  }

  useEffect(() => {
    if (isAndroid) {
      keyboardDidShow = Keyboard.addListener('keyboardDidShow', onKeyboardDidShow);
      keyboardDidHide = Keyboard.addListener('keyboardDidHide', onKeyboardDidHide);
    } else {
      keyboardDidShow = Keyboard.addListener('keyboardDidShow', onKeyboardDidShow);
      keyboardDidHide = Keyboard.addListener('keyboardWillHide', onKeyboardDidHide);
    }
    return () => {
      if (isAndroid) {
        keyboardDidHide.remove();
        keyboardDidShow.remove();
      } else {
        keyboardDidHide.remove();
        keyboardDidShow.remove();
      }
    };
  }, []);

  useEffect(() => {
    getChats();
  }, []);

  useEffect(() => {
    if (data && data.length > 0) {
      const lastItemIndex = data.length - 1;
      setTimeout(() => {
        if (lastItemIndex > -1 && refContainer?.current && data?.length && lastItemIndex < data.length) {
          refContainer?.current?.scrollToIndex({animated: true, index: lastItemIndex});
        }
      }, 100);
    }
  }, [data]);

  const onSendChat = async () => {
    if (text === '') return;
    try {
      let params = {so_id: profileData?.ho_so?.so_id_knai, nv: 'KHIEU_NAI', nd: text, bt: ''};
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.NHAP_Y_KIEN_TRAO_DOI, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Gửi ý kiến thành công', 'success');
      getChats();
      setText('');
      Keyboard.dismiss();
      return;
    } catch (error) {
      Alert.alert('Thông báo', error.message + '- line - 83');
    }
  };
  const getChats = async () => {
    setLoading(true);
    try {
      setLoading(false);
      let params = {so_id: profileData?.ho_so?.so_id_knai, nv: 'KHIEU_NAI'};
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_Y_KIEN_TRAO_DOI, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setData(response.data_info);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 97');
    }
  };

  const renderItemHSGiayTo = ({item, index}) => {
    const isUser = item.nsd_chat === 1;
    const isLastItem = data.length - 1;
    return (
      <View style={[styles.item, isUser && {alignItems: 'flex-end'}, isLastItem === index && styles.marginBottom]}>
        {isUser ? (
          <View style={styles.titleRow}>
            <Text style={styles.txtTime}>{item.thoi_gian}</Text>
            <Text style={styles.txtTitle}>{item.ten_nsd}</Text>
          </View>
        ) : (
          <View style={styles.titleRow}>
            <Text style={styles.txtTitle}>{item.ten_nsd}</Text>
            <Text style={[styles.txtTime, {marginLeft: spacing.smaller}]}>{item.thoi_gian}</Text>
          </View>
        )}
        <View style={[styles.boxChat, isUser && styles.isUserBoxChatStyles]}>
          <Text style={styles.txtNoiDung}>{item.nd}</Text>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle="Ý kiến trao đổi"
      renderView={
        <SafeAreaView flex={1}>
          <View style={styles.content}>
            <FlatList
              data={data}
              ref={refContainer}
              scrollEnabled={true}
              onScrollToIndexFailed={(error) => {
                refContainer?.current?.scrollToOffset({offset: error.averageItemLength * error.index, animated: true});
                setTimeout(() => {
                  if (data.length !== 0 && refContainer !== null) {
                    refContainer?.current?.scrollToIndex({index: error.index, animated: true});
                  }
                }, 100);
              }}
              renderItem={renderItemHSGiayTo}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) => index.toString()}
              // refreshControl={<RefreshControl refreshing={false} onRefresh={getChats} />}
              ListEmptyComponent={<Text style={styles.txtEmpty}>Chưa có dữ liệu</Text>}
              onEndReached={getChats}
              onEndReachedThreshold={0}
            />
          </View>
          <View style={[styles.footerView, isIOS && {bottom: bottomPosition}]}>
            <TextInput value={text} blurOnSubmit={false} placeholder="Nhập nội dung" onChangeText={setText} style={styles.inputStyle} multiline />
            <TouchableOpacity onPress={onSendChat} style={{paddingRight: spacing.small}}>
              <Icon.FontAwesome name="paper-plane" size={24} color={colors.PRIMARY} />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      }
    />
  );
};

export const YKienTraoDoiHSKhieuNaiScreen = memo(YKienTraoDoiHSKhieuNaiScreenComponent, isEqual);
