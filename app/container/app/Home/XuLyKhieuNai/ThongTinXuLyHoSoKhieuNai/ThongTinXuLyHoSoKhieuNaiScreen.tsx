import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {ButtonLinear, ScreenComponent, TextInputOutlined} from '@component';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

const ThongTinXuLyHoSoKhieuNaiScreenComponent = ({route}) => {
  console.log('QuaTrinhXuLyHoSoKhieuNaiScreenComponent');
  const {profileData} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm({
    defaultValues: {
      nguyenNhanKhieuNai: '',
      phuongAnXuLy: '',
    },
    mode: 'onChange',
  });

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  //Lưu thông tin khiếu nại
  const handleLuuThongTin = async (data) => {
    try {
      let paramsProfileDetail = {
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac || '',
        so_id_knai: profileData?.ho_so?.so_id_knai,
        nguyen_nhan_knai: data.nguyenNhanKhieuNai,
        phuong_an_xly: data.phuongAnXuLy,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_THONG_TIN_KHIEN_NAI, paramsProfileDetail);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  //Đóng hồ sơ kn

  const handleDongKhieuNai = () => {
    Alert.alert('Đóng hồ sơ khiếu nại', 'Bạn có chắc chắn muốn đóng hồ sơ khiếu nại này không?', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          try {
            let params = {
              ma_doi_tac: profileData?.ho_so?.ma_doi_tac || '',
              so_id_knai: profileData?.ho_so?.so_id_knai,
            };
            setDialogLoading(true);
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.DONG_HS_KHIEN_NAI, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
          return;
        },
      },
    ]);
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Thông tin xử lý khiếu nại"
      renderView={
        <KeyboardAwareScrollView style={styles.container}>
          <Controller
            control={control}
            name="nguyenNhanKhieuNai"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                multiline
                value={value}
                title={'Nguyên nhân khiếu nại'}
                error={errors.nguyenNhanKhieuNai && getErrMessage('nguyenNhanKhieuNai', errors.nguyenNhanKhieuNai.type)}
                inputStyle={{color: colors.BLACK, minHeight: dimensions.height * 0.25}}
                onChangeText={onChange}
                placeholder="Nhập nguyên nhân dẫn đến khiếu nại"
                blurOnSubmit={false}
              />
            )}
          />
          <Controller
            control={control}
            name="phuongAnXuLy"
            rules={{
              required: true,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                isRequired
                multiline
                value={value}
                title={'Phương án xử lý'}
                error={errors.phuongAnXuLy && getErrMessage('phuongAnXuLy', errors.phuongAnXuLy.type)}
                inputStyle={{color: colors.BLACK, minHeight: dimensions.height * 0.25}}
                onChangeText={onChange}
                placeholder="Nhập phương án xử lý"
              />
            )}
          />
        </KeyboardAwareScrollView>
      }
      footer={
        <View style={styles.footerView}>
          <ButtonLinear title="Hoàn thành" linearStyle={{marginRight: spacing.default}} onPress={handleDongKhieuNai} />
          <ButtonLinear title="Lưu" onPress={handleSubmit(handleLuuThongTin)} />
        </View>
      }
    />
  );
};

export const ThongTinXuLyHoSoKhieuNaiScreen = memo(ThongTinXuLyHoSoKhieuNaiScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: spacing.default,
    paddingTop: spacing.default,
  },
  resolveItemView: {
    marginHorizontal: scale(spacing.smaller),
  },
  verticalLineStep: {
    width: 22,
    height: 22,
    borderWidth: 1,
    borderRadius: 20,
    alignItems: 'center',
    borderColor: 'white',
    justifyContent: 'center',
    backgroundColor: colors.PRIMARY,
  },
  contentColumn: {
    borderLeftWidth: 1,
    borderColor: colors.GRAY,
    marginLeft: scale(spacing.small),
    marginBottom: vScale(spacing.medium),
  },
  title: {
    marginBottom: 4,
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },

  date: {
    fontWeight: '400',
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
  },
  subLabel: {
    fontWeight: '400',
    color: colors.GRAY6,
    marginLeft: scale(20),
    fontSize: FontSize.size14,
  },

  titleView: {
    flex: 1,
    marginLeft: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: vScale(spacing.small),
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: vScale(spacing.tiny),
  },
  footerView: {
    flexDirection: 'row',
    flex: 1,
  },
});
