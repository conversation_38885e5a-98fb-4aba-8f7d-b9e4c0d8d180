import {colors} from '@app/commons/Theme';
import {scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    marginTop: vScale(spacing.smaller),
  },
  scrollView: {
    flex: 1,
  },
  contentView: {
    flex: 1,
    marginHorizontal: scale(spacing.small),
  },
  footerView: {
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
    paddingHorizontal: scale(spacing.small),
  },
  optionLoaiGD: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: vScale(spacing.smaller),
  },
});
