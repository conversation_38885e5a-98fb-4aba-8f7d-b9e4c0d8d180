import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';
import {ModalChiNhanh, ModalNguoiXuLy} from './Components';
import styles from './Styles';

const titleInput = ['Đơn vị xử lý', '<PERSON><PERSON><PERSON><PERSON> xử lý', '<PERSON><PERSON> chú'];
const ThemCanBoKhieuNaiScreenComponent = ({route}) => {
  console.log('TaoYeuCauGiamDinhBoiThuongHoScreenComponent');
  const {profileData, selectedItem} = route.params;
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [listNguoiXuLyRoot, setListNguoiXuLyRoot] = useState([]);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);
  const [listChiNhanh, setListChiNhanh] = useState([]);

  let refModalChiNhanh = useRef(null);
  let refModalNguoiXuLy = useRef(null);

  useEffect(() => {
    // initData();
    layDsNguoiSuDung();
    initChiNhanhBaoHiem();
  }, []);

  const getDefaultFormValue = () => {
    return {
      donViYeuCau: profileData?.ho_so?.ma_chi_nhanh,
      giamDinhVien: selectedItem ? selectedItem.ma_nsd_yc : '',
      ghiChu: selectedItem ? selectedItem.ghi_chu : '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    // setError,
    // watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const initChiNhanhBaoHiem = () => {
    if (listChiNhanh.length > 0) return;
    let chiNhanhBaoHiemQuanLySorted = JSON.parse(JSON.stringify(chiNhanhBaoHiemDangCay));
    chiNhanhBaoHiemQuanLySorted = chiNhanhBaoHiemQuanLySorted.sort((a, b) => a.stt - b.stt);
    let listChiNhanhMap = chiNhanhBaoHiemQuanLySorted.map((item) => {
      return {label: item.ten_chi_nhanh, value: item.ma_chi_nhanh};
    });
    setListChiNhanh(listChiNhanhMap);
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onPressSave = async (data) => {
    setDialogLoading(true);
    try {
      let params = {
        nv: 'KHIEU_NAI',
        so_id: profileData.ho_so.so_id_knai,
        ma_nsd: data.giamDinhVien,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.THEM_CAN_BO_KNAI, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm cán bộ thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDsNguoiSuDung = async () => {
    try {
      const params = {ma_doi_tac: profileData?.ho_so?.ma_doi_tac};
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_NSD, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info;
      data.map((item, index) => {
        data[index].label = item.ten;
        data[index].value = item.ma;
      });
      setListNguoiXuLyRoot(data);
      setListNguoiXuLy(data.filter((e) => e.ma_chi_nhanh === profileData?.ho_so?.ma_chi_nhanh));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getTenHienThi = (value, data) => {
    let name = '';
    data.map((e) => {
      if (e.ma_chi_nhanh === value) name = e.ten_chi_nhanh;
      if (e.ma === value) name = e.ten;
    });
    return name;
  };

  const onChangeDonViXuLy = (value) => {
    try {
      setDialogLoading(true);
      setListNguoiXuLy(listNguoiXuLyRoot.filter((e) => e.ma_chi_nhanh === value.value));
      setValue('donViYeuCau', value.value, {shouldValidate: true});
      setTimeout(() => {
        setDialogLoading(false);
      }, 300);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      setDialogLoading(false);
    }
  };

  /**RENDER  */
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Thêm cán bộ"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} scrollEnabled={true}>
            <View style={styles.contentView}>
              <Controller
                control={control}
                name="donViYeuCau"
                rules={{
                  required: false,
                }}
                render={({field: {value}}) => (
                  <>
                    <TextInputOutlined
                      isTouchableOpacity
                      editable={false}
                      isDropdown
                      title="Đơn vị xử lý"
                      value={getTenHienThi(value, chiNhanhBaoHiemDangCay)}
                      // error={errors.donViYeuCau && getErrMessage('donViYeuCau', errors.donViYeuCau.type)}
                      placeholder={titleInput[0]}
                      isRequired={true}
                      inputStyle={{color: colors.BLACK_03}}
                      onPress={() => refModalChiNhanh.current.show()}
                    />
                  </>
                )}
              />

              <Controller
                control={control}
                name="giamDinhVien"
                rules={{
                  required: true,
                }}
                render={({field: {value, onChange}}) => (
                  <TextInputOutlined
                    isRequired
                    isDropdown
                    isTouchableOpacity
                    editable={false}
                    title="Người xử lý"
                    placeholder="Chọn người xử lý"
                    inputStyle={{color: colors.BLACK}}
                    cleared={value !== null && value !== ''}
                    value={getTenHienThi(value, listNguoiXuLy)}
                    onPress={() => refModalNguoiXuLy.current.show()}
                    onPressClear={() => setValue('giamDinhVien', '')}
                    error={errors.giamDinhVien && getErrMessage('giamDinhVien', errors.giamDinhVien.type)}
                  />
                )}
              />
              <Controller
                control={control}
                name="ghiChu"
                // rules={{
                //   required: true,
                // }}
                render={({field: {value, onChange}}) => (
                  <TextInputOutlined multiline title="Ghi chú" onChangeText={onChange} placeholder="Nhập ghi chú" inputStyle={{color: colors.BLACK, maxHeight: 120}} value={value} />
                )}
              />
            </View>
          </KeyboardAwareScrollView>
          <ModalChiNhanh
            baseData={listChiNhanh}
            value={getValues('donViYeuCau')}
            ref={refModalChiNhanh}
            setValue={(val) => onChangeDonViXuLy(val)}
            onBackPress={() => refModalChiNhanh.current?.hide()}
          />
          <ModalNguoiXuLy
            value={getValues('giamDinhVien')}
            data={listNguoiXuLy}
            ref={refModalNguoiXuLy}
            onBackPress={() => refModalNguoiXuLy.current.hide()}
            setValue={(val) => setValue('giamDinhVien', val.ma, {shouldValidate: true})}
          />
        </View>
      }
      footer={<ButtonLinear title="Lưu" onPress={handleSubmit(onPressSave)} />}
    />
  );
};

export const ThemCanBoKhieuNaiScreen = memo(ThemCanBoKhieuNaiScreenComponent, isEqual);
