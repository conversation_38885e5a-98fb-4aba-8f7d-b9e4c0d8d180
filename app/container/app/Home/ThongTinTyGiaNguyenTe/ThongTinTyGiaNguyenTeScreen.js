import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, Text, TextInputOutlined} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Dimensions, FlatList, RefreshControl, SafeAreaView, View} from 'react-native';
import styles from './Styles';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import NavigationUtil from '@app/navigation/NavigationUtil';

const {width} = Dimensions.get('window');

const ThongTinTyGiaNguyenTeScreenComponent = (props) => {
  console.log('DanhSachChiTietHoSoGocScreenComponent');
  const route = useRoute();
  const {profileInfo, nguon, obj} = route?.params;

  const [data, setData] = useState([]);
  const [arrDS, setArrDS] = useState([]);
  const [dialogLoading, setDialogLoading] = useState(false);

  useEffect(() => {
    getDsTyGiaTheoHoSo();
    // getChiTietLanNhanHoSoGoc();
  }, []);

  // Lấy ds hs giấy tờ
  const getDsTyGiaTheoHoSo = async () => {
    try {
      setDialogLoading(true);
      let params = {
        ma_doi_tac: profileInfo.ma_doi_tac,
        so_id: profileInfo?.so_id,
        nguon: nguon,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_TY_GIA_THEO_HS, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let rs = response.data_info;
      setArrDS(rs);
      return;
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 87');
    }
  };

  const luuThongTinTyGia = async () => {
    if (arrDS.length === 1 && arrDS[0]?.nt_tien_yc === 'VND') return Alert.alert('Thông báo', 'Không được sửa tỷ giá là VND');
    try {
      setDialogLoading(true);
      let arr = [];
      arrDS.forEach((e) => {
        let json = {
          ty_gia: e.ty_gia,
          nt_tien_yc: e.nt_tien_yc,
        };
        arr.push(json);
      });
      const params = {
        so_id: profileInfo?.so_id || '',
        ma_doi_tac: profileInfo?.ma_doi_tac || '',
        nguon: nguon,
        arr: arr,
      };
      console.log('🚀 ~ file: ThongTinTyGiaNguyenTeScreen.js:83 ~ luuThongTinTyGia ~ params:', params);

      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DS_TY_GIA_THEO_HS, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu tỷ giá thành công!', 'success');
      getDsTyGiaTheoHoSo();
      if (obj) NavigationUtil.pop();
      return;
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 118');
    }
  };

  const onChangeInputValue = (idx, val) => {
    let listTyGia = arrDS;
    listTyGia[idx].ty_gia = val;
    setData([...listTyGia]);
  };

  const renderItemHSGiayTo = ({item, index}) => {
    return (
      <View style={styles.item}>
        <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
          <View width={width * 0.6} flexDirection="row" justifyContent="center">
            <Text style={styles.txtTenHangMuc}>{item.nt_tien_yc}</Text>
          </View>
          <View style={styles.rowStyles}>
            <View style={styles.frame}>
              <TextInputOutlined
                placeholder="0"
                editable={item.nt_tien_yc === 'VND' ? false : true}
                keyboardType="decimal-pad"
                inputStyle={styles.inputStyle}
                value={item.ty_gia.toString()}
                onChangeText={(value) => onChangeInputValue(index, value)}
                placeholderTextColor={colors.BLACK_03}
              />
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Bảng tỷ giá"
      renderView={
        <SafeAreaView flex={1}>
          {/* <SearchBar placeholder="Tìm kiếm giấy tờ" onTextChange={debounced} /> */}
          <KeyboardAwareScrollView>
            <View style={styles.content} marginTop={12}>
              <View style={styles.tableTitleRow}>
                <View width={width * 0.6} justifyContent="center" alignItems="center">
                  <Text style={styles.txtHangMuc} children="Nguyên tệ" />
                </View>
                <View style={[styles.rowStyles]}>
                  <View style={[styles.frame, styles.checkboxRow]}>
                    <Text children="Tỷ giá so với VND" style={styles.txtGroup} />
                  </View>
                </View>
              </View>
              <FlatList
                data={arrDS}
                scrollEnabled={true}
                renderItem={renderItemHSGiayTo}
                keyExtractor={(item, index) => index.toString()}
                refreshControl={<RefreshControl refreshing={false} onRefresh={getDsTyGiaTheoHoSo} />}
                ListEmptyComponent={<Text style={{marginVertical: 10, textAlign: 'center'}}>Chưa có dữ liệu</Text>}
              />
            </View>
          </KeyboardAwareScrollView>
          <View style={styles.footerView}>
            {/* <ButtonLinear
              loading={false}
              disabled={false}
              linearStyle={[styles.footerBtn, {marginRight: scale(spacing.small)}]}
              // onPress={xacNhan}
              title={'Xác nhận'}
              textStyle={{color: colors.BLACK_03}}
              linearColors={[colors.GRAY2, colors.GRAY2]}
            /> */}
            <ButtonLinear loading={false} disabled={false} linearStyle={styles.footerBtn} title={'Lưu'} onPress={luuThongTinTyGia} />
          </View>
        </SafeAreaView>
      }
    />
  );
};

export const ThongTinTyGiaNguyenTeScreen = memo(ThongTinTyGiaNguyenTeScreenComponent, isEqual);
