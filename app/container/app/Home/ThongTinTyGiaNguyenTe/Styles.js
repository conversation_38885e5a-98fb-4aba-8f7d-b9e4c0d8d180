import {colors} from '@app/commons/Theme';
import {FontSize, dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  content: {
    flex: 1,
    marginHorizontal: scale(10),
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: scale(spacing.tiny),
  },
  txtTenHangMuc: {
    fontWeight: '500',
    alignSelf: 'center',
    textAlign: 'center',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
    marginHorizontal: scale(2),
  },
  checkBox: {
    paddingHorizontal: 10,
  },
  checkAll: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: scale(spacing.smaller),
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
    paddingVertical: vScale(spacing.tiny),
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: scale(spacing.smaller),
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: vScale(6),
    backgroundColor: colors.GRAY2,
    paddingHorizontal: scale(spacing.small),
  },
  icBtn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtGroup: {
    fontSize: FontSize.size12,
  },
  footerView: {
    flexDirection: 'row',
    borderTopWidth: 0.2,
    width: dimensions.width,
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
    paddingHorizontal: scale(spacing.small),
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width * 0.2,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'right',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
});
