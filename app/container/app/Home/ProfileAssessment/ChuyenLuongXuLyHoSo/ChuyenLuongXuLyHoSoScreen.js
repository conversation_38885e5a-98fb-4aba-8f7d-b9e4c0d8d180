import {ButtonLinear, DropdownPicker, ScreenComponent} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import styles from './Styles';

function ChuyenLuongXuLyHoSoScreenComponent(props) {
  console.log('ChuyenLuongXuLyHoSoScreenComponent');
  const {route} = props;
  const {profileData} = route.params;
  const [loading, setLoading] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [dataLuongXuLy, setDataLuongXuLy] = useState([]);

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      luongXuLy: profileData ? profileData?.ho_so?.nv_xly : '',
    },
  });

  useEffect(() => {
    getDataLuongXuLy();
  }, []);

  const getDataLuongXuLy = async () => {
    setLoading(true);
    const params = {};
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DATA_LUONG_XU_LY, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let luongXuLyRespone = response.data_info;
      setDataLuongXuLy(luongXuLyRespone);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'phone') {
      if (errType === 'pattern') return 'Số điện thoại sai định dạng';
    }
    return '';
  };

  const onSubmit = async (data) => {
    if (data.luongXuLy === profileData?.ho_so?.nv_xly) return NavigationUtil.pop();
    try {
      Alert.alert('Thông báo', 'Đổi luồng xử lý thì tất cả các đối tượng tổn thất và hạng mục giám định sẽ bị hủy bỏ, bạn có chắc chắn muốn thay đổi luồng xử lý không', [
        {
          text: 'Đồng ý',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            const params = {
              so_id: profileData?.ho_so?.so_id,
              nv_xly: data.luongXuLy,
              nv: profileData?.ho_so?.nghiep_vu,
            };
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHUYEN_LUONG_XL_CUA_HS, params);
            setLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển luồng xử lý thành công!', 'success');
            NavigationUtil.pop();
          },
        },
        {
          text: 'Để sau',
        },
      ]);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  /**RENDER  */

  return (
    <ScreenComponent
      dialogLoading={loading}
      headerBack
      headerTitle="Chuyển luồng xử lý"
      renderView={
        <View style={styles.container}>
          <View margin={spacing.small}>
            <Controller
              control={control}
              name="luongXuLy"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  title={'Luồng xử lý'}
                  zIndex={9000}
                  searchable={false}
                  isOpen={openDropdown}
                  setOpen={setOpenDropdown}
                  items={dataLuongXuLy}
                  itemSelected={value}
                  placeholder="Chọn luồng xử lý"
                  // onSelectItem={onChangeValueDropdownPicker}
                  containerStyle={{marginBottom: 10}}
                  isRequired
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  inputErr={errors.luongXuLy && getErrMessage('luongXuLy', errors.luongXuLy.type)}
                  schema={{
                    label: 'ten',
                    value: 'ma',
                  }}
                />
              )}
            />
          </View>
        </View>
      }
      footer={<ButtonLinear onPress={handleSubmit(onSubmit)} title="Lưu" />}
    />
  );
}

export const ChuyenLuongXuLyHoSoScreen = memo(ChuyenLuongXuLyHoSoScreenComponent, isEqual);
