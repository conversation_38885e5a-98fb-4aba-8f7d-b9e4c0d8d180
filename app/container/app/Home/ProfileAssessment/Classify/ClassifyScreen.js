import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, Text, TextInputOutlined} from '@component';
import {DATA_CONSTANT, DATA_NHOM_TAI_LIEU, SCREEN_ROUTER_APP, isIOS} from '@constant';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, TouchableOpacity, View} from 'react-native';
import ImageZoom from 'react-native-image-pan-zoom';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import ParallaxScrollView from 'react-native-parallax-scroll-view';
import RadioGroup from 'react-native-radio-buttons-group';
import {connect} from 'react-redux';
import styles from './ClassifyStyles';
//xử lý form
const titleInput = [
  'Nhóm tài liệu',
  'Hạng mục sửa chữa, thay thế',
  'Loại hình nghiệp vụ',
  'Mức độ tổn thất',
  'Phương án khắc phục',
  'Nơi sửa chữa',
  'Thu hồi vật tư',
  'Chi phí tính toán tự động',
  'Chi phí do GDV xác định',
  'Vụ tổn thất',
  'Ghi chú',
  'Đối tượng tổn thất',
  'Số lượng',
];

const ClassifyScreenComponent = (props) => {
  console.log('ClassifyScreenComponent');
  const {route, navigation, categoryCommon, userInfo} = props;
  const {xemTaiLieuSelected} = route.params;
  let categoryType1 = categoryCommon.type1.map((item) => {
    item = cloneObject(item);
    item.value = item.ma;
    item.label = item.ten;
    return item;
  });

  let soLuongRef = useRef();
  let chiPhiGDVRef = useRef();
  let ghiChuRef = useRef();

  const parallaxRef = useRef(null);
  const [profileData, setProfileData] = useState(route.params.profileData);
  const [inputErr, setInputErr] = useState(['', '', '', '', '', '', '', '', '', '', '', '', '']);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const {doiTuongDuocPhanLoai} = route.params; //đối tượng được phân loại
  //xử lý images
  const [images, setImages] = useState(route.params.imagesClassify);
  const [imageSelected, setImageSelected] = useState(route.params.imagesClassify[0]);
  const [imageSelectedPosition, setImageSelectedPosition] = useState(-1);

  // DROPDOWN NHÓM TÀI LIỆU
  const [openDocumentGroup, setOpenDocumentGroup] = useState(false);
  const [documentSelected, setDocumentSelected] = useState(xemTaiLieuSelected === 'ANH_HO_SO' ? DATA_NHOM_TAI_LIEU[1].value : DATA_NHOM_TAI_LIEU[0].value);
  const documentGroupData = DATA_NHOM_TAI_LIEU;
  //DROPDOWN LOẠI HÌNH NGHIỆP VỤ
  const [openMajor, setOpenMajor] = useState(false);
  const [majorSelected, setMajorSelected] = useState(null);
  const [majorData, setMajorData] = useState([]);
  //DROPDOWN ĐỐI TƯỢNG TỔN THẤT
  const [openDoiTuongTT, setOpenDoiTuongTT] = useState(false);
  const [doiTuongTTSelected, setDoiTuongTTSelected] = useState(null);
  const [listDoiTuongTonThat, setListDoiTuongTonThat] = useState([]);
  //DROPDOWN HẠNG MỤC SỬA CHỮA, THAY THẾ
  const [openCategoryFix, setOpenCategoryFix] = useState(false);
  const [categoryFixSelected, setCategoryFixSelected] = useState(null); //hạng mục được chọn
  const [categoryFixDataDisplay, setCategoryFixDataDisplay] = useState([]); //dữ liệu hiển thị trên UI
  const [categoryFixData, setCategoryFixData] = useState([]); //dữ liệu gốc
  const [categoryFixFilter, setCategoryFixFilter] = useState([]); //dữ liệu filter theo

  const [timeoutId, setTimeoutId] = useState(null);
  //DROPDOWN MỨC ĐỔ TỔN THẤT
  const [openLevelLoss, setOpenLevelLoss] = useState(false);
  const [levelLossSelected, setLevelLossSelected] = useState(null);
  const [levelLossData, setLevelLossData] = useState([]);
  //RADIO PHƯƠNG ÁN KHẮC PHỤC
  const [recoveryPlantRadio, setRecoveryPlantRadio] = useState([
    {
      id: '1',
      label: DATA_CONSTANT.RECOVERY_PLANT.SUA_CHUA.label,
      value: DATA_CONSTANT.RECOVERY_PLANT.SUA_CHUA.value,
      containerStyle: {flex: 1},
      selected: true,
      size: 20,
    },
    {
      id: '2',
      label: DATA_CONSTANT.RECOVERY_PLANT.THAY_THE.label,
      value: DATA_CONSTANT.RECOVERY_PLANT.THAY_THE.value,
      containerStyle: {flex: 1},
      selected: false,
      size: 20,
    },
    {
      id: '3',
      label: DATA_CONSTANT.RECOVERY_PLANT.KHONG_XAC_DINH.label,
      value: DATA_CONSTANT.RECOVERY_PLANT.KHONG_XAC_DINH.value,
      containerStyle: {flex: 1},
      selected: false,
      size: 20,
    },
  ]);
  //RADIO NƠI SỬA CHỮA
  const [repairPlaceRadio, setRepairPlaceRadio] = useState([
    {
      id: '1',
      label: DATA_CONSTANT.REPAIR_PLACE.SUA_CHUA_NGOAI.label,
      value: DATA_CONSTANT.REPAIR_PLACE.SUA_CHUA_NGOAI.value,
      containerStyle: {flex: 1},
      selected: true,
      size: 20,
    },
    {
      id: '2',
      label: DATA_CONSTANT.REPAIR_PLACE.CHINH_HANG.label,
      value: DATA_CONSTANT.REPAIR_PLACE.CHINH_HANG.value,
      containerStyle: {flex: 1},
      selected: false,
      size: 20,
    },
  ]);
  //RADIO THU HỒI VẬT TƯ
  const [recoveryRadio, setRecoveryRadio] = useState([
    {
      id: '1',
      label: DATA_CONSTANT.RECOVERY.CO.label,
      value: DATA_CONSTANT.RECOVERY.CO.value,
      containerStyle: {flex: 1},
      selected: false,
      size: 20,
      disabled: false,
    },
    {
      id: '2',
      label: DATA_CONSTANT.RECOVERY.KHONG.label,
      value: DATA_CONSTANT.RECOVERY.KHONG.value,
      containerStyle: {flex: 1},
      selected: true,
      size: 20,
      disabled: false,
    },
  ]);
  const [soLuongInput, setSoLuongInput] = useState(route.params.imagesClassify[route.params.imagesClassify.length - 1].so_luong || '0');
  const [priceAutoInput, setPriceAutoInput] = useState(''); //INPUT CHI PHÍ TÍNH TOÁN TỰ ĐỘNG
  const [priceInput, setPriceInput] = useState(0); //INPUT CHI PHÍ DO GDV XÁC ĐỊNH
  const [noteInput, setNoteInput] = useState(route.params.imagesClassify[route.params.imagesClassify.length - 1].ghi_chu); //INPUT GHI CHÚ
  //DROPDOWN VỤ TỔN THẤT
  const [openAccident, setOpenAccident] = useState(false);
  const [accidentSelected, setAccidentSelected] = useState(null);
  const [accidentData, setAccidentData] = useState([]);

  const initVuTonThat = () => {
    let profileData = route.params.profileData;

    //LIST VỤ TỔN THẤT -> láy theo hồ sơ
    let accidentDataTmp = [];
    profileData.dien_bien.map((itemDienBien) => {
      accidentDataTmp.push({
        label: itemDienBien.gio_xr + ' ' + itemDienBien.ngay_xr + ' ' + itemDienBien.dia_diem,
        value: itemDienBien.vu_tt,
      });
    });
    setAccidentData(accidentDataTmp);

    //VỤ TỔN THẤT : nếu có 1 vụ tổn thât -> chọn mặc định VỤ TỔN THẤT
    if (accidentDataTmp.length === 1) setAccidentSelected(accidentDataTmp[0].value);
    else if (route.params.imagesClassify.length > 0) setAccidentSelected(route.params.imagesClassify[0].vu_tt);
  };

  const initLoaiHinhNghiepVu = () => {
    let anhCuoiCung = route.params.imagesClassify[route.params.imagesClassify.length - 1],
      {profileData} = route.params;

    //LIST LOẠI HÌNH NGHIỆP VỤ : lấy theo hồ sơ
    let majorDataTmp = [];
    profileData.lh_nv.map((item) => {
      //nếu từ màn CHỤP ẢNH sang -> sẽ có ĐỐI TƯỢNG được chọn -> lấy LOẠI HÌNH NGHIỆP VỤ theo ĐỐI TƯỢNG
      if (doiTuongDuocPhanLoai) {
        if (item.doi_tuong === doiTuongDuocPhanLoai.nhom) {
          item.label = item.ten;
          item.value = item.ma;
          majorDataTmp.push(item);
        }
      } else {
        item.label = item.ten;
        item.value = item.ma;
        majorDataTmp.push(item);
      }
    });
    setMajorData(majorDataTmp);

    // LOẠI HÌNH NGHIỆP VỤ ĐƯỢC CHỌN
    //nếu chỉ có 1 LOẠI HÌNH NGHIỆP VỤ -> cho chọn mặc định
    if (majorDataTmp.length === 1) setMajorSelected(majorDataTmp[0].value);
    //ảnh cuối cùng đã đánh giá (đã có loại hình nghiệp vụ) -> fill mặc định
    else if (anhCuoiCung.lh_nv?.trim()) setMajorSelected(anhCuoiCung.lh_nv);
    else {
      //nếu có 1 ảnh có nghiệp vụ -> fill mặc định nghiệp vụ đấy vào
      route.params.imagesClassify.map((image) => {
        if (image.lh_nv?.trim()) setMajorSelected(image.lh_nv);
      });
    }
  };

  const initDoiTuongTonThat = () => {
    let {profileData} = route.params,
      anhPhanLoai = route.params.imagesClassify,
      anhCuoiCung = route.params.imagesClassify[route.params.imagesClassify.length - 1];
    let loaiHinhNghiepVu;
    /* TÌM LOẠI HÌNH NGHIỆP VỤ ĐƯỢC CHỌN, rồi từ đấy LẤY ĐỐI TƯỢNG TỔN THẤT */
    //LIST LOẠI HÌNH NGHIỆP VỤ : lấy theo hồ sơ
    let listLoaiHinhNghiepVu = [];
    profileData.lh_nv.map((item) => {
      //nếu từ màn CHỤP ẢNH sang -> sẽ có ĐỐI TƯỢNG được chọn -> lấy LOẠI HÌNH NGHIỆP VỤ theo ĐỐI TƯỢNG
      if (doiTuongDuocPhanLoai) {
        if (item.doi_tuong === doiTuongDuocPhanLoai.nhom) {
          item.label = item.ten;
          item.value = item.ma;
          listLoaiHinhNghiepVu.push(item);
        }
      } else {
        item.label = item.ten;
        item.value = item.ma;
        listLoaiHinhNghiepVu.push(item);
      }
    });

    // LOẠI HÌNH NGHIỆP VỤ ĐƯỢC CHỌN
    //nếu chỉ có 1 LOẠI HÌNH NGHIỆP VỤ -> cho chọn mặc định
    if (listLoaiHinhNghiepVu.length === 1) loaiHinhNghiepVu = listLoaiHinhNghiepVu[0];
    //ảnh cuối cùng đã đánh giá (đã có loại hình nghiệp vụ) -> fill mặc định
    else if (anhCuoiCung.lh_nv?.trim()) loaiHinhNghiepVu = listLoaiHinhNghiepVu.filter((item) => item.ma === anhCuoiCung.lh_nv)[0];
    else {
      //nếu có 1 ảnh có nghiệp vụ -> fill mặc định nghiệp vụ đấy vào
      route.params.imagesClassify.map((image) => {
        if (image.lh_nv?.trim()) loaiHinhNghiepVu = listLoaiHinhNghiepVu.filter((item) => item.ma === anhCuoiCung.lh_nv)[0];
      });
    }

    /* filter đối tượng tổn thất */
    //LIST ĐỐI TƯỢNG TỔN THẤT
    let listDoiTuongTonThat = profileData.ds_doi_tuong.map((item) => {
      item.label = item.ten_doi_tuong;
      item.value = item.so_id_doi_tuong;
      return item;
    });
    if (loaiHinhNghiepVu) listDoiTuongTonThat = listDoiTuongTonThat.filter((doiTuongTonThat) => doiTuongTonThat.nhom === loaiHinhNghiepVu.doi_tuong);
    setListDoiTuongTonThat([...listDoiTuongTonThat]);

    // ĐỐI TƯỢNG TỔN THẤT ĐƯỢC CHỌN
    //nếu có 1 đối tượng -> chọn mặc định
    if (anhPhanLoai[0].nhomMoi) setDoiTuongTTSelected(anhPhanLoai[0].so_id_doi_tuong);
    else if (profileData.ds_doi_tuong.length === 1) setDoiTuongTTSelected(profileData.ds_doi_tuong[0].so_id_doi_tuong);
    else setDoiTuongTTSelected(anhPhanLoai[anhPhanLoai.length - 1].so_id_doi_tuong);
  };

  const initHangMucSuaChuaThayThe = () => {
    let categoryCommonType1 = categoryType1,
      anhCuoiCung = route.params.imagesClassify[route.params.imagesClassify.length - 1];
    let listHangMucSuaChuThayThe = [];
    setCategoryFixData(categoryType1); //hạng mục sửa chữa gốc
    let doiTuongDuocPhanLoai;
    if (profileData.ds_doi_tuong.length === 1) doiTuongDuocPhanLoai = profileData.ds_doi_tuong[0];
    else if (route.params.doiTuongDuocPhanLoai) doiTuongDuocPhanLoai = route.params.doiTuongDuocPhanLoai;
    else doiTuongDuocPhanLoai = getDoiTuongById(anhCuoiCung.so_id_doi_tuong);
    //VỚI CÁC ĐỐI TƯỢNG KHÔNG PHẢI XE MÁY HẠNG MỤC TỔN THẤT được chọn
    categoryCommonType1.map((item) => {
      if (item.ma === anhCuoiCung.nhom.ma) {
        listHangMucSuaChuThayThe = [item]; //chèn thằng được chọn lên đầu luôn
        setCategoryFixSelected(item.ma);
        return;
      }
    });
    if (!doiTuongDuocPhanLoai) return;
    let maDoiTuong = doiTuongDuocPhanLoai.nhom || doiTuongDuocPhanLoai.doi_tuong;
    //nếu đối tượng là XE -> thì lấy hạng mục sửa chữa thay thế theo CHINH
    if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) {
      categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH || item.loai === 'BT_TOAN_BO');
      setCategoryFixFilter([...categoryCommonType1]);
      let categoryFixDataDisplayTmp = categoryCommonType1.slice(0, 100); //lấy 100 thằng đầu để hiển thị thôi
      let hangMucDaTonTai = false;
      categoryFixDataDisplayTmp.map((item) => {
        if (item.ma === listHangMucSuaChuThayThe[0]?.ma) {
          //nếu tồn tại thì ghi đè lên mảng cũ luôn
          hangMucDaTonTai = true;
          setCategoryFixDataDisplay([...categoryFixDataDisplayTmp]);
        }
      });
      //nếu chưa tồn tại thì ghép 2 mảng với nhau
      if (!hangMucDaTonTai) setCategoryFixDataDisplay(cloneObject(listHangMucSuaChuThayThe.concat(categoryFixDataDisplayTmp))); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
    }
    //nếu đối tượng là HÀNG HOÁ -> filter theo HÀNG HOÁ
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) {
      // categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.HANG_HOA);
      categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === 'TAI_SAN');
      setCategoryFixDataDisplay([...categoryCommonType1]);
    }
    //nếu đối tượng là NGƯỜI -> filter theo NGƯỜI
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma) {
      categoryCommonType1 = categoryCommonType1.filter(
        (item) =>
          (doiTuongDuocPhanLoai.nhom === 'NGUOI' && item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.NNTX) ||
          (doiTuongDuocPhanLoai.nhom === 'TNDS' && item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TNDS_NG),
      );
      categoryCommonType1.length === 1 && setCategoryFixSelected(categoryCommonType1[0].value);
      setCategoryFixDataDisplay([...categoryCommonType1]);
    }
    //nếu nhóm đối tượng là TÀI SẢN
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) {
      //nếu đối tượng là NHÓM TÀI SẢN - LOẠI KHÁC
      if (doiTuongDuocPhanLoai?.loai === 'KHAC' || route.params.doiTuongDuocPhanLoai?.loai === 'KHAC') {
        // categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TNDS_TS);
        categoryCommonType1 = categoryCommonType1.filter((item) => item.nhom === 'TAI_SAN');
        setCategoryFixDataDisplay([...categoryCommonType1]);
      }
      // nếu đối tượng là NHÓM TÀI SẢN - LOẠI XE
      else if (doiTuongDuocPhanLoai?.loai === 'XE' || route.params.doiTuongDuocPhanLoai?.loai === 'XE') {
        categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH || item.loai === 'BT_TOAN_BO');
        let categoryFixDataDisplayTmp = categoryCommonType1.slice(0, 100); //lấy 100 thằng đầu để hiển thị thôi
        let hangMucDaTonTai = false;
        categoryFixDataDisplayTmp.map((item) => {
          if (item.ma === listHangMucSuaChuThayThe[0]?.ma) {
            //nếu tồn tại thì ghi đè lên mảng cũ luôn
            hangMucDaTonTai = true;
            setCategoryFixDataDisplay([...categoryFixDataDisplayTmp]);
          }
        });
        //nếu chưa tồn tại thì ghép 2 mảng với nhau
        if (!hangMucDaTonTai) setCategoryFixDataDisplay(cloneObject(listHangMucSuaChuThayThe.concat(categoryFixDataDisplayTmp))); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
      }
      // nếu đối tượng là NHÓM TÀI SẢN - LOẠI XE
      else if (doiTuongDuocPhanLoai?.loai === 'XE_MAY') {
        setCategoryFixData(categoryCommon.listHangMucXeMay);
        categoryCommonType1 = categoryCommon.listHangMucXeMay.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH).map((item) => ({...item, label: item.ten, value: item.ma}));
        let categoryFixDataDisplayTmp = categoryCommonType1.slice(0, 100); //lấy 100 thằng đầu để hiển thị thôi
        categoryFixDataDisplayTmp.map((item) => {
          if (item.ma === anhCuoiCung.nhom.ma) {
            listHangMucSuaChuThayThe = [item]; //chèn thằng được chọn lên đầu luôn
            setCategoryFixSelected(item.ma);
            return;
          }
        });
        let hangMucDaTonTai = false;
        categoryFixDataDisplayTmp.map((item) => {
          if (item.ma === listHangMucSuaChuThayThe[0]?.ma) {
            //nếu tồn tại thì ghi đè lên mảng cũ luôn
            hangMucDaTonTai = true;
            setCategoryFixDataDisplay([...categoryFixDataDisplayTmp]);
          }
        });
        //nếu chưa tồn tại thì ghép 2 mảng với nhau
        if (!hangMucDaTonTai) setCategoryFixDataDisplay(cloneObject(listHangMucSuaChuThayThe.concat(categoryFixDataDisplayTmp))); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
      }
    }
  };

  const initMucDoTonThat = () => {
    let {profileData, prevScreen} = route.params,
      anhCuoiCung = route.params.imagesClassify[route.params.imagesClassify.length - 1],
      anhDauTien = route.params.imagesClassify[0];
    //LIST MỨC ĐỘ TỔN THẤT
    let mucDoTonThatFilter = [];
    //FILTER THEO NGHIỆP VỤ
    let mucDoTonThatFilterNghiepVu = categoryCommon.levelLost.filter((item) => item.nv_hs === profileData.ho_so.nghiep_vu);
    let doiTuongDuocPhanLoai;
    if (profileData.ds_doi_tuong.length === 1) doiTuongDuocPhanLoai = profileData.ds_doi_tuong[0];
    else if (route.params.doiTuongDuocPhanLoai) doiTuongDuocPhanLoai = route.params.doiTuongDuocPhanLoai;
    else doiTuongDuocPhanLoai = getDoiTuongById(anhCuoiCung.so_id_doi_tuong);
    if (!doiTuongDuocPhanLoai) return;
    let maDoiTuong = doiTuongDuocPhanLoai.nhom || doiTuongDuocPhanLoai.doi_tuong;

    //NẾU ĐỐI TƯỢNG LÀ XE
    if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    // NẾU ĐỐI TƯỢNG LÀ HÀNG HOÁ
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = cloneObject(mucDoTonThatFilterNghiepVu[i]);
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.HANG_HOA) mucDoTonThatFilterNghiepVu.push(mucDo);
      }
    }
    // NẾU ĐỐI TƯỢNG LÀ NGƯỜI
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = cloneObject(mucDoTonThatFilterNghiepVu[i]);
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.NGUOI) mucDoTonThatFilter.push(mucDo);
      }
    }
    // NẾU ĐÓI TƯỢNG LÀ TÀI SẢN
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) {
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN KHÁC
      if (doiTuongDuocPhanLoai?.loai === 'KHAC' || route.params.doiTuongDuocPhanLoai?.loai === 'KHAC')
        mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === doiTuongDuocPhanLoai.nhom);
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN XE
      else if (doiTuongDuocPhanLoai?.loai === 'XE' || route.params.doiTuongDuocPhanLoai?.loai === 'XE' || doiTuongDuocPhanLoai?.loai === 'XE_MAY')
        mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    }
    setLevelLossData([...mucDoTonThatFilter]);

    //Mức độ tổn thất được chọn
    if (anhDauTien.nhomMoi) setLevelLossSelected(anhDauTien.muc_do);
    else {
      mucDoTonThatFilter.map((item) => {
        if (item.ma === anhCuoiCung.muc_do) {
          setLevelLossSelected(item.ma);
          return;
        }
      });
    }
    //trường hợp từ màn CHỤP ẢNH sang -> chỉ chọn MỨC ĐỘ -> xử lý để fill dữ liệu vào Phương Án Khắc Phục + Thu hồi vật tư
    if (anhCuoiCung.muc_do && prevScreen) {
      mucDoTonThatFilter.map((item) => {
        //điền PHƯƠNG ÁN KHẮC PHỤC + NƠI SỬA CHỮA từ MỨC ĐỘ TỔN THẤT được chọn
        if (item.value === anhCuoiCung.muc_do) {
          let recoveryPlantRadioTmp = cloneObject(recoveryPlantRadio); //PHƯƠNG ÁN KHẮC PHỤC
          let recoveryRadioTmp = cloneObject(recoveryRadio); //THU HỒI VẬT TƯ
          //nếu chưa chọn PHƯƠNG ÁN KHẮC PHỤC -> thì điền PHƯƠNG ÁN KHẮC PHỤC mặc định từ CẤU HÌNH MỨC ĐỘ TỔN THẤT
          if (!anhCuoiCung.thay_the_sc) {
            //PHƯƠNG ÁN KHẮC PHỤC
            recoveryPlantRadioTmp[0].selected = item.pa_khac_phuc === recoveryPlantRadio[0].value ? true : false; //SỬA CHỮA
            recoveryPlantRadioTmp[1].selected = item.pa_khac_phuc === recoveryPlantRadio[1].value ? true : false; //THAY THẾ
            recoveryPlantRadioTmp[2].selected = item.pa_khac_phuc === recoveryPlantRadio[2].value ? true : false; //CHƯA XÁC ĐỊNH
          }
          //nếu USER chọn PHƯƠNG ÁN KHẮC PHỤC -> thì điền PHƯƠNG ÁN KHẮC PHỤC theo USER chọn
          else {
            recoveryPlantRadioTmp[0].selected = anhCuoiCung.thay_the_sc === recoveryPlantRadioTmp[0].value ? true : false; //SỬA CHỮA
            recoveryPlantRadioTmp[1].selected = anhCuoiCung.thay_the_sc === recoveryPlantRadioTmp[1].value ? true : false; //THAY THẾ
            recoveryPlantRadioTmp[2].selected = anhCuoiCung.thay_the_sc === recoveryPlantRadioTmp[2].value ? true : false; //CHƯA XÁC ĐỊNH
          }
          // nếu MỨC ĐỘ là Chưa xác định hư hỏng : MD.008 -> disable PHƯƠNG ÁN KHẮC PHỤC
          if (item.value === 'MD.008') {
            recoveryPlantRadioTmp[0].disabled = true;
            recoveryPlantRadioTmp[1].disabled = true;
            recoveryPlantRadioTmp[2].disabled = true;
          }

          //nếu chưa chọn THU HỒI VẬT TƯ -> thì điền THU HỒI VẬT TƯ mặc định từ CẤU HÌNH MỨC ĐỘ TỔN THẤT
          if (!anhCuoiCung.thu_hoi) {
            recoveryRadioTmp[0].selected = item.pa_khac_phuc === recoveryPlantRadio[1].value ? true : false; // CÓ
            recoveryRadioTmp[1].selected = item.pa_khac_phuc !== recoveryPlantRadio[1].value ? true : false; // KHÔNG
          }
          //nếu USER chọn THU HỒI VẬT TƯ rồi -> thì điền PHƯƠNG ÁN KHẮC PHỤC theo USER chọn
          else {
            recoveryRadioTmp[0].selected = anhCuoiCung.thu_hoi === recoveryRadio[0].value ? true : false; // CÓ
            recoveryRadioTmp[1].selected = anhCuoiCung.thu_hoi === recoveryRadio[1].value ? true : false; // KHÔNG
          }
          //nếu PHƯƠNG ÁN KHẮC PHỤC không phải THAY THẾ -> DISABLE THU HỒI VẬT TƯ
          if (!recoveryPlantRadioTmp[1].selected) {
            recoveryRadioTmp[0].disabled = true;
            recoveryRadioTmp[1].disabled = true;
          }

          if (recoveryRadioTmp[0].selected && soLuongInput <= 0) setSoLuongInput('1');
          setRecoveryPlantRadio(recoveryPlantRadioTmp);
          setRecoveryRadio(recoveryRadioTmp);
        }
      });
    }
  };

  const initPhuongAnKhacPhuc = () => {
    let anhCuoiCung = route.params.imagesClassify[route.params.imagesClassify.length - 1];
    let anhDauTien = route.params.imagesClassify[0];

    let recoveryPlantRadioTmp = recoveryPlantRadio;
    if (anhDauTien.thay_the_sc) {
      recoveryPlantRadioTmp[0].selected = anhDauTien.thay_the_sc === DATA_CONSTANT.RECOVERY_PLANT.SUA_CHUA.value ? true : false;
      recoveryPlantRadioTmp[1].selected = anhDauTien.thay_the_sc === DATA_CONSTANT.RECOVERY_PLANT.THAY_THE.value ? true : false;
      recoveryPlantRadioTmp[2].selected = anhDauTien.thay_the_sc === DATA_CONSTANT.RECOVERY_PLANT.KHONG_XAC_DINH.value ? true : false;
    } else if (anhCuoiCung.thay_the_sc) {
      recoveryPlantRadioTmp[0].selected = recoveryPlantRadio[0].value === anhCuoiCung.thay_the_sc ? true : false; //sửa chữa
      recoveryPlantRadioTmp[1].selected = recoveryPlantRadio[1].value === anhCuoiCung.thay_the_sc ? true : false; //thay thế
      recoveryPlantRadioTmp[2].selected = recoveryPlantRadio[2].value === anhCuoiCung.thay_the_sc ? true : false; //chưa xác định
    }
    // nếu MỨC ĐỘ là Chưa xác định hư hỏng : MD.008 -> disable PHƯƠNG ÁN KHẮC PHỤC
    if (anhDauTien.muc_do === 'MD.008') {
      recoveryPlantRadioTmp[0].disabled = true;
      recoveryPlantRadioTmp[1].disabled = true;
      recoveryPlantRadioTmp[2].disabled = true;
    }
    setRecoveryPlantRadio([...recoveryPlantRadioTmp]);
  };

  const initThuHoiVatTu = () => {
    let anhCuoiCung = route.params.imagesClassify[route.params.imagesClassify.length - 1];
    let anhDauTien = route.params.imagesClassify[0];
    let recoveryRadioTmp = recoveryRadio;
    if (anhDauTien.thu_hoi === DATA_CONSTANT.RECOVERY.KHONG.value) {
      recoveryRadioTmp[1].selected = true; //không
      recoveryRadioTmp[0].selected = false; //có
    } else if (anhCuoiCung.thu_hoi) {
      recoveryRadioTmp[0].selected = recoveryRadio[0].value === anhCuoiCung.thu_hoi ? true : false;
      recoveryRadioTmp[1].selected = recoveryRadio[1].value === anhCuoiCung.thu_hoi ? true : false;
    }
    recoveryRadioTmp[0].disabled = anhDauTien.thay_the_sc === DATA_CONSTANT.RECOVERY_PLANT.THAY_THE.value ? false : true;
    recoveryRadioTmp[1].disabled = anhDauTien.thay_the_sc === DATA_CONSTANT.RECOVERY_PLANT.THAY_THE.value ? false : true;

    setRecoveryRadio([...recoveryRadioTmp]);
  };
  const getDoiTuongById = (idDoiTuong) => {
    let doiTuongFilter = route.params.profileData.ds_doi_tuong.filter((doiTuong) => doiTuong.so_id_doi_tuong === idDoiTuong);
    return doiTuongFilter[0];
  };

  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false); //chạy cái này để khi vào lần đầu. các biến không bị thay đổi theo nhau
    }, 1000);
    let anhDauTien = route.params.imagesClassify[0];
    setImages(route.params.imagesClassify); //LIST ẢNH ĐƯỢC PHÂN LOẠI
    setProfileData(route.params.profileData); //HỒ SƠ

    initVuTonThat();
    initLoaiHinhNghiepVu();
    initDoiTuongTonThat();
    initHangMucSuaChuaThayThe();
    initMucDoTonThat();

    if (!route.params.prevScreen) {
      initPhuongAnKhacPhuc();
      initThuHoiVatTu();
    }

    if (anhDauTien.nhomMoi) {
      setPriceInput(images[0].tien_gd); //chi phí do GDV xác định
      setSoLuongInput(images[0].so_luong); //số lượng
      setPriceAutoInput(images[0].tien_tu_dong); //chi phí tính toán tự động
      setNoteInput(images[0].ghi_chu); //ghi chú
    }
  }, [route.params]);

  useEffect(() => {
    if (xemTaiLieuSelected) onChangeDocumentGroupValue(xemTaiLieuSelected === 'ANH_HO_SO' ? DATA_NHOM_TAI_LIEU[1].value : DATA_NHOM_TAI_LIEU[0].value);
  }, [categoryFixData]);

  useEffect(() => {
    if (doiTuongTTSelected) {
      let doiTuongTonThatObj = getDoiTuongById(doiTuongTTSelected);
      let categoryCommonType1 = categoryType1;
      let mucDoTonThatFilter = [];
      // FILTER THEO NGHIỆP VỤ
      let mucDoTonThatFilterNghiepVu = categoryCommon.levelLost.filter((item) => item.nv_hs === profileData.ho_so.nghiep_vu);
      if (doiTuongTonThatObj.loai === 'KHAC') {
        //LẤY HẠNG MỤC SỬA CHỮA THAY THẾ THEO ĐỐI TƯỢNG
        // categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TNDS_TS);
        categoryCommonType1 = categoryCommonType1.filter((item) => item.nhom === 'TAI_SAN');
        setCategoryFixDataDisplay([...categoryCommonType1]);
        if (categoryCommonType1.length === 1) setCategoryFixSelected(categoryCommonType1[0].value);

        //LẤY MỨC ĐỘ TỔN THẤT THEO ĐỐI TƯỢNG
        for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
          let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
          if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.HANG_HOA) mucDoTonThatFilter.push(mucDo);
        }
        setLevelLossData([...mucDoTonThatFilter]);
      } else if (doiTuongTonThatObj.loai === 'XE') {
        categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH || item.loai === 'BT_TOAN_BO');
        let categoryFixDataDisplayTmp = categoryCommonType1.slice(0, 100); //lấy 100 thằng đầu để hiển thị thôi
        setCategoryFixDataDisplay(cloneObject(categoryFixDataDisplayTmp)); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
        //LẤY MỨC ĐỘ TỔN THẤT THEO ĐỐI TƯỢNG
        for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
          let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
          if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE) mucDoTonThatFilter.push(mucDo);
        }
        setLevelLossData([...mucDoTonThatFilter]);
      }
    }
  }, [doiTuongTTSelected]);

  //LOẠI HÌNH NGHIỆP VỤ thay đổi
  useEffect(() => {
    if (isLoading) return;
    if (!majorSelected) return;
    let majorDataSelected = {};
    majorData.map((item) => {
      if (item.ma === majorSelected) majorDataSelected = item;
    });
    let recoveryRadioTmp = recoveryRadio;

    recoveryRadioTmp[0].disabled = majorDataSelected.nhom !== 'VCX' ? false : true;
    recoveryRadioTmp[1].disabled = majorDataSelected.nhom !== 'VCX' ? false : true;
    setRecoveryRadio([...recoveryRadioTmp]);
    let loaiHinhNghiepVu; //Lấy obj LOẠI HÌNH NGHIỆP VỤ từ maLoaiHinhNghiepVu
    profileData.lh_nv.map((item) => {
      if (item.ma === majorSelected) {
        loaiHinhNghiepVu = item;
        return;
      }
    });

    //từ LOẠI HÌNH NGHIỆP VỤ -> lấy ĐỐI TƯỢNG
    let listDoiTuongTonThat = [];
    profileData.ds_doi_tuong.map((item) => {
      if (item.nhom === loaiHinhNghiepVu.doi_tuong) {
        let newItem = item;
        newItem.label = newItem.ten_doi_tuong;
        newItem.value = newItem.so_id_doi_tuong;
        listDoiTuongTonThat.push(newItem);
      }
    });
    setListDoiTuongTonThat([...listDoiTuongTonThat]);
    //đối tượng được chọn
    let doiTuongDuocPhanLoai = listDoiTuongTonThat.length > 0 ? listDoiTuongTonThat[0] : null;
    setDoiTuongTTSelected(doiTuongDuocPhanLoai?.value || null);

    /* từ LOẠI HÌNH NGHIỆP VỤ -> lấy HẠNG MỤC SỬA CHỮA, THAY THẾ */
    // let listHangMucSuaChuThayThe = [];
    let categoryCommonType1 = categoryType1;
    let maDoiTuong = doiTuongDuocPhanLoai?.nhom || doiTuongDuocPhanLoai?.doi_tuong || '';
    //nếu đối tượng là XE -> thì lấy hạng mục sửa chữa thay thế theo CHINH
    if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) {
      categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH || item.loai === 'BT_TOAN_BO');
      let categoryFixDataDisplayTmp = categoryCommonType1.slice(0, 100); //lấy 100 thằng đầu để hiển thị thôi
      setCategoryFixDataDisplay(cloneObject(categoryFixDataDisplayTmp)); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
      // let hangMucDaTonTai = false;
      // categoryFixDataDisplayTmp.map((item) => {
      //   if (item.ma === listHangMucSuaChuThayThe[0]?.ma) {
      //     //nếu tồn tại thì ghi đè lên mảng cũ luôn
      //     hangMucDaTonTai = true;
      //     setCategoryFixDataDisplay([...categoryCommonType1.slice(0, 100)]);
      //   }
      // });
      // if (!hangMucDaTonTai) setCategoryFixDataDisplay(cloneObject(listHangMucSuaChuThayThe.concat(categoryFixDataDisplayTmp))); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
    }
    //nếu đối tượng là HÀNG HOÁ -> filter theo HÀNG HOÁ
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) {
      categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.HANG_HOA);
      setCategoryFixDataDisplay(categoryCommonType1);
      if (categoryCommonType1.length === 1) setCategoryFixSelected(categoryCommonType1[0].value);
    }
    //nếu đối tượng là NGƯỜI -> filter theo NGƯỜI
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma) {
      categoryCommonType1 = categoryCommonType1.filter(
        (item) =>
          (doiTuongDuocPhanLoai.nhom === 'NGUOI' && item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.NNTX) ||
          (doiTuongDuocPhanLoai.nhom === 'TNDS' && item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TNDS_NG),
      );
      categoryCommonType1.length === 1 && setCategoryFixSelected(categoryCommonType1[0].value);
      setCategoryFixDataDisplay(categoryCommonType1);
    }
    //nếu nhóm đối tượng là TÀI SẢN
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) {
      //nếu đối tượng là NHÓM TÀI SẢN - LOẠI KHÁC
      if (doiTuongDuocPhanLoai?.loai === 'KHAC' || route.params.doiTuongDuocPhanLoai?.loai === 'KHAC') {
        categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TNDS_TS);
        setCategoryFixDataDisplay(categoryCommonType1);
      }
      // nếu đối tượng là NHÓM TÀI SẢN - LOẠI XE
      else if (doiTuongDuocPhanLoai?.loai === 'XE' || route.params.doiTuongDuocPhanLoai?.loai === 'XE') {
        categoryCommonType1 = categoryCommonType1.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH || item.loai === 'BT_TOAN_BO');
        let categoryFixDataDisplayTmp = categoryCommonType1.slice(0, 100); //lấy 100 thằng đầu để hiển thị thôi
        setCategoryFixDataDisplay([...categoryFixDataDisplayTmp]);
        // setCategoryFixDataDisplay(cloneObject(categoryFixDataDisplayTmp)); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
        // let hangMucDaTonTai = false;
        // categoryFixDataDisplayTmp.map((item) => {
        //   if (item.ma === listHangMucSuaChuThayThe[0]?.ma) {
        //     hangMucDaTonTai = true;
        //     setCategoryFixDataDisplay([...categoryCommonType1.slice(0, 100)]);
        //   }
        // });
        // if (!hangMucDaTonTai) setCategoryFixDataDisplay(cloneObject(listHangMucSuaChuThayThe.concat(categoryFixDataDisplayTmp))); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
      }
      // nếu đối tượng là NHÓM TÀI SẢN - LOẠI XE MÁY
      else if (doiTuongDuocPhanLoai?.loai === 'XE_MAY') {
        let categoryFixDataDisplayTmp = categoryCommon.listHangMucXeMay
          .filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH)
          .map((item) => ({...item, label: item.ten, value: item.ma})) //bổ sung thêm thuộc tính label, value cho dropdown
          .slice(0, 100); //lấy 100 thằng đầu để hiển thị thôi
        setCategoryFixDataDisplay([...categoryFixDataDisplayTmp]);
      }
    }
    /* từ LOẠI HÌNH NGHIỆP VỤ -> lấy MỨC ĐỘ TỔN THẤT */
    let mucDoTonThatFilter = [];
    // FILTER THEO NGHIỆP VỤ
    let mucDoTonThatFilterNghiepVu = categoryCommon.levelLost.filter((item) => item.nv_hs === profileData.ho_so.nghiep_vu);
    //NẾU ĐỐI TƯỢNG LÀ XE
    if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    // NẾU ĐỐI TƯỢNG LÀ HÀNG HOÁ
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.HANG_HOA) mucDoTonThatFilter.push(mucDo);
      }
    }
    // NẾU ĐỐI TƯỢNG LÀ NGƯỜI
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.NGUOI) mucDoTonThatFilter.push(mucDo);
      }
    }
    // NẾU ĐÓI TƯỢNG LÀ TÀI SẢN
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) {
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN KHÁC
      if (doiTuongDuocPhanLoai?.loai === 'KHAC' || route.params.doiTuongDuocPhanLoai?.loai === 'KHAC')
        mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === doiTuongDuocPhanLoai.nhom);
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN XE
      else if (doiTuongDuocPhanLoai?.loai === 'XE' || route.params.doiTuongDuocPhanLoai?.loai === 'XE' || route.params.doiTuongDuocPhanLoai?.loai === 'XE_MAY')
        mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    }
    setLevelLossData([...mucDoTonThatFilter]);

    //Mức độ tổn thất được chọn
    if (mucDoTonThatFilter.length <= 3) setLevelLossSelected(mucDoTonThatFilter[0]?.value || '');
  }, [majorSelected]); //LOẠI HÌNH NGHIỆP VỤ thay đổ

  //CHI PHÍ TÍNH TOÁN TỰ ĐỘNG
  useEffect(() => {
    let isTonThatSelected = documentSelected === documentGroupData[0].value;
    if (categoryFixSelected && levelLossSelected && isTonThatSelected) {
      //phương án khắc phục
      let thay_the_sc = '';
      recoveryPlantRadio.map((item) => (item.selected ? (thay_the_sc = item.value) : ''));
      //nơi sửa chữa
      let chinh_hang = '';
      repairPlaceRadio.map((item) => (item.selected ? (chinh_hang = item.value) : ''));
      let params = {
        so_id: profileData.ho_so.so_id,
        hang_muc: categoryFixSelected,
        muc_do: levelLossSelected,
        thay_the_sc: thay_the_sc,
        chinh_hang: 'K',
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
      };
      tinhToanChiPhiTuDong(params);
    }
  }, [
    //khi có đủ ngần này thì mới gọi API lấy giá tự động
    documentSelected,
    categoryFixSelected,
    levelLossSelected,
    repairPlaceRadio,
    recoveryPlantRadio,
  ]);

  //MỨC ĐỘ TỔN THẤT thay đổi
  useEffect(() => {
    if (isLoading) return; //nếu vào lần đầu -> sẽ không làm thay đổi các biến còn lại
    if (levelLossSelected) {
      levelLossData.map((item) => {
        //điền PHƯƠNG ÁN KHẮC PHỤC + NƠI SỬA CHỮA từ MỨC ĐỘ TỔN THẤT được chọn
        if (item.value === levelLossSelected) {
          let recoveryPlantRadioTmp = recoveryPlantRadio; //PHƯƠNG ÁN KHẮC PHỤC
          let repairPlaceRadioTmp = repairPlaceRadio; //NƠI SỬA CHỮA
          //PHƯƠNG ÁN KHẮC PHỤC
          recoveryPlantRadioTmp[0].selected = item.pa_khac_phuc === recoveryPlantRadio[0].value ? true : false;
          recoveryPlantRadioTmp[1].selected = item.pa_khac_phuc === recoveryPlantRadio[1].value ? true : false;
          recoveryPlantRadioTmp[2].selected = item.pa_khac_phuc === recoveryPlantRadio[2].value ? true : false;
          // chỗ này FIX CỨNG GIÁ TRỊ MD.008 (Mức độ tổn thất : Chưa xác định hư hỏng) để xử lý DISABLED Phương án khắc phục
          recoveryPlantRadioTmp[2].disabled = item.value !== 'MD.008' ? false : true;
          recoveryPlantRadioTmp[0].disabled = item.value !== 'MD.008' ? false : true;
          recoveryPlantRadioTmp[1].disabled = item.value !== 'MD.008' ? false : true;

          //NƠI SỬA CHỮA
          repairPlaceRadioTmp[0].selected = item.thay_the_sc === repairPlaceRadio[0].value ? true : false;
          repairPlaceRadioTmp[1].selected = item.thay_the_sc === repairPlaceRadio[1].value ? true : false;
          setRecoveryPlantRadio([...recoveryPlantRadioTmp]);
          setRepairPlaceRadio([...repairPlaceRadioTmp]);
          return;
        }
      });
    }
  }, [levelLossSelected]); //ĐỐI TƯỢNG TỔN THẤT thay đổi

  //PHƯƠNG ÁN KHẮC PHỤC THAY ĐỔI
  useEffect(() => {
    if (isLoading) return;
    let recoveryRadioTmp = cloneObject(recoveryRadio);
    //nếu Phương án khắc phục : Sửa chữa - hoặc Chưa xác định -> Thu hồi vật tư : chọn KHÔNG; Disable thu hồi vật tư; Số lượng = 0; số lượng k cho sửa
    if ((recoveryPlantRadio[0].selected || recoveryPlantRadio[2].selected) && majorSelected !== 'XE03') {
      setSoLuongInput('0');
      recoveryRadioTmp[0].selected = false;
      recoveryRadioTmp[1].selected = true;
      recoveryRadioTmp[0].disabled = true;
      recoveryRadioTmp[1].disabled = true;
      setRecoveryRadio(recoveryRadioTmp);
    }
    //nếu Phương án khắc phục : Thay thế -> Thu hồi vật tư : chọn CÓ; ENABLE thu hồi vật tư; Số lượng = 1; số lượng cho sửa
    else if (recoveryPlantRadio[1].selected && majorSelected !== 'XE03') {
      soLuongInput == '0' ? setSoLuongInput('1') : setSoLuongInput(soLuongInput);
      recoveryRadioTmp[0].selected = true;
      recoveryRadioTmp[0].disabled = false;
      recoveryRadioTmp[1].selected = false;
      recoveryRadioTmp[1].disabled = false;
      setRecoveryRadio(recoveryRadioTmp);
    }
  }, [recoveryPlantRadio]);

  useEffect(() => {
    if (isLoading) return;
    setSoLuongInput(recoveryRadio[0].selected ? '1' : '0');
  }, [recoveryRadio]);

  const onChangeChiPhiGDV = (value) => {
    let inputErrTmp = inputErr;
    inputErrTmp[8] = '';
    if (+value < 0) inputErrTmp[8] = 'Chi phí phải lớn hơn 0';
    setInputErr([...inputErrTmp]);
    if (!value.trim()) setPriceInput('0');
    else setPriceInput(value);
  };

  const tinhToanChiPhiTuDong = async (params) => {
    try {
      if (profileData.ho_so.nghiep_vu === 'XE_MAY') return;
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHI_PHI_TINH_TOAN_TU_DONG, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setPriceAutoInput(response.out_value.gia_tu_dong);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //lấy chi tiêt ảnh
  const getDetailImage = async (imageData, index) => {
    //nếu ảnh chưa lấy nét
    if (!imageData.isDetail && !route.params.prevScreen) {
      let params = {
        so_id: imageData.so_id,
        bt: imageData.bt,
      };
      try {
        let response = await ESmartClaimEndpoint.getFile(axiosConfig.ACTION_CODE.DOCUMENT_DETAIL, params);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        setImageSelected(response.data_info);
        if (images.length > 0) {
          let tmp = images;
          tmp[index] = response.data_info; //câpj nhật ảnh đã lấy nét
          tmp[index].isDetail = true; //lưu là đã lấy nét ảnh
          setImages(tmp);
        }
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    } else {
      setImageSelected(imageData);
    }
  };

  //khi open 1 dropdown -> thì sẽ ẩn các dropdown còn lại đi
  const closeDropdown = (title) => {
    if (title === titleInput[0]) {
      setOpenMajor(false);
      setOpenAccident(false);
      setOpenCategoryFix(false);
      setOpenLevelLoss(false);
      setOpenDoiTuongTT(false);
    } else if (title === titleInput[2]) {
      setOpenDocumentGroup(false);
      setOpenAccident(false);
      setOpenCategoryFix(false);
      setOpenLevelLoss(false);
      setOpenDoiTuongTT(false);
    } else if (title === titleInput[1]) {
      setOpenMajor(false);
      setOpenDocumentGroup(false);
      setOpenAccident(false);
      setOpenLevelLoss(false);
      setOpenDoiTuongTT(false);
    } else if (title === titleInput[3]) {
      setOpenMajor(false);
      setOpenDocumentGroup(false);
      setOpenAccident(false);
      setOpenCategoryFix(false);
      setOpenDoiTuongTT(false);
    } else if (title === titleInput[9]) {
      setOpenMajor(false);
      setOpenDocumentGroup(false);
      setOpenCategoryFix(false);
      setOpenLevelLoss(false);
      setOpenDoiTuongTT(false);
    } else if (title === titleInput[11]) {
      setOpenMajor(false);
      setOpenDocumentGroup(false);
      setOpenCategoryFix(false);
      setOpenLevelLoss(false);
      setOpenAccident(false);
    } else {
      setOpenAccident(false);
      setOpenMajor(false);
      setOpenDocumentGroup(false);
      setOpenCategoryFix(false);
      setOpenLevelLoss(false);
      setOpenDoiTuongTT(false);
    }
  };

  //sự kiện khi search ở dropdown
  const onChangeSearchText = (textSearch) => {
    clearTimeout(timeoutId);
    try {
      // let timeoutIdTmp = setTimeout(() => {
      if (!textSearch.trim()) {
        setCategoryFixDataDisplay([...categoryFixData.map((item) => ({...item, label: item.ten, value: item.ma})).slice(0, 100)]);
        setOpenCategoryFix(false);
        setOpenCategoryFix(true);
        // Keyboard.dismiss();
        clearTimeout(timeoutId);
        return;
      }
      let result = [];
      /*search by text*/
      // for (let i = 0; i < categoryFixFilter.length; i++) {
      //   if (categoryFixFilter[i].label.toUpperCase().indexOf(textSearch.toUpperCase()) > -1) result.push(categoryFixFilter[i]);
      // }

      /* search by word */
      let arrTextSearch = textSearch.trim().split(' ');
      arrTextSearch = arrTextSearch.filter((item) => item !== '');
      for (let i = 0; i < categoryFixData.length; i++) {
        let arrTenHangMuc = categoryFixData[i].ten.split(' ');
        let tonTai = 0; //nếu tonTai === (arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
        for (let j = 0; j < arrTextSearch.length; j++) {
          for (let k = 0; k < arrTenHangMuc.length; k++) {
            /*
            j + 1 !==tonTai : để loại trường hợp chuỗi tên hạng mục có 2 từ giống nhau
            ví dụ : 
            tên hạng mục : tôi là tôi 
            từ cần tìm : tôi là 
            -> khi duyệt từ 'tôi' ở từ cần tìm -> 'tôi' sẽ được tính 2 lần ->  dẫn đến sai kết quả của biến tonTai
            //có cách khác là remove duplicate ở cả 2 mảng arrTenHangMuc và arrTextSearch r tìm -> sẽ không bị dính trường hợp trên =))
            */
            if (arrTenHangMuc[k].toUpperCase() === arrTextSearch[j].toUpperCase() && j + 1 !== tonTai) {
              tonTai = tonTai + 1;
              break;
            }
          }
        }
        if (tonTai === arrTextSearch.length) result.push(categoryType1[i]);
      }
      // Keyboard.dismiss();
      let soViTriDoi = 0;
      for (let i = 0; i < result.length; i++) {
        let arrResultItem = result[i].ten.trim().split(' ');
        let soTuGiong = 0;
        for (let j = 0; j < arrTextSearch.length; j++) {
          if (j < arrResultItem.length) {
            if (arrTextSearch[j].toUpperCase() === arrResultItem[j].toUpperCase()) soTuGiong = soTuGiong + 1;
          }
        }
        if (soTuGiong === arrTextSearch.length && soViTriDoi < result.length) {
          [result[soViTriDoi], result[i]] = [result[i], result[soViTriDoi]];
          soViTriDoi = soViTriDoi + 1;
        }
      }
      setCategoryFixDataDisplay([...result]);
      setOpenCategoryFix(false);
      setOpenCategoryFix(true);
      // }, 500);
      // setTimeoutId(timeoutIdTmp);
    } catch (error) {}
  };

  // NÚT PHÂN LOẠI
  const onPressClassify = async () => {
    //phương án khắc phục
    let thay_the_sc = '';
    recoveryPlantRadio.map((item) => {
      if (item.selected) thay_the_sc = item.value;
    });
    //nơi sửa chữa
    // let chinh_hang = '';
    // repairPlaceRadio.map((item) => {
    //   if (item.selected) chinh_hang = item.value;
    // });
    //thu hồi vật tư
    let thu_hoi = '';
    recoveryRadio.map((item) => {
      if (item.selected) thu_hoi = item.value;
    });
    let loaiHinhSelected = '';
    profileData.lh_nv.map((item) => {
      if (item.ma === majorSelected) loaiHinhSelected = item;
    });

    let isTonThatSelected = documentSelected === documentGroupData[0].value;
    let haveErr = false;
    let inputErrTmp = inputErr;
    // if (!categoryFixSelected) {
    //   inputErrTmp[1] = 'Vui lòng chọn hạng mục sửa chữa, thay thế';
    //   haveErr = true;
    // }
    if (isTonThatSelected) {
      if (!levelLossSelected) {
        inputErrTmp[3] = 'Vui lòng chọn mức độ tổn thất';
        haveErr = true;
      }
      if (!majorSelected) {
        inputErrTmp[2] = 'Vui lòng chọn loại hình nghiệp vụ';
        haveErr = true;
      }
      // if (!priceInput.trim() && loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) {
      //   inputErrTmp[8] = 'Vui lòng nhập chi phí';
      //   haveErr = true;
      // }
      if (!accidentSelected) {
        inputErrTmp[9] = 'Vui lòng chọn vụ tổn thất';
        haveErr = true;
      }
      if (!doiTuongDuocPhanLoai && !doiTuongTTSelected) {
        inputErrTmp[11] = 'Vui lòng chọn đối tượng tổn thất';
        // else if(!majorSelected)inputErrTmp[11] = 'Vui lòng chọn đối tượng tổn thất';
        haveErr = true;
      }
    }
    if (haveErr) {
      setInputErr([...inputErrTmp]);
      return;
    }

    if (route.params.prevScreen) {
      let imagesTmp = images;
      let categoryFixSelectedTmp = categoryFixData.filter((item) => item.ma === categoryFixSelected);
      imagesTmp.map((item) => {
        item.loai = documentSelected; //nhóm tài liệu
        item.vu_tt = isTonThatSelected ? accidentSelected : ''; //vụ tổn thất - CÓ
        item.lh_nv = isTonThatSelected ? majorSelected : ''; //loại hình nghiệp vụ
        item.hang_muc = categoryFixSelected; //hạng mục sửa chữa, thay thế
        item.muc_do = isTonThatSelected ? levelLossSelected : ''; //mức độ tổn thất - CÓ
        item.thay_the_sc =
          isTonThatSelected && (loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma || loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) //nếu là XE thì có PHƯƠNG ÁN KHẮC PHỤC
            ? thay_the_sc
            : ''; //phương án, khắc phục
        item.chinh_hang = 'K';
        // isTonThatSelected && loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma //nếu là XE thì có NƠI SỬA CHỮA
        //   ? chinh_hang
        //   : ''; //nơi sửa chữa
        item.thu_hoi =
          isTonThatSelected &&
          (loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma || //nếu là XE thì có THU HỒI VẬT TƯ
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma || //nếu là HÀNG HOÁ thì có THU HỒI VẬT TƯ
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) ////nếu là TÀI SẢN thì có THU HỒI VẬT TƯ
            ? thu_hoi
            : ''; //thu hồi vật tư
        item.so_luong =
          isTonThatSelected &&
          (loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma || //nếu là XE thì có CHI PHÍ TÍNH TOÁN TỰ ĐỘNG
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma ||
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) //nếu là XE thì có CHI PHÍ TÍNH TOÁN TỰ ĐỘNG
            ? soLuongInput
            : 0;
        item.tien_tu_dong = 0;
        // isTonThatSelected && loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma //nếu là XE thì có CHI PHÍ TÍNH TOÁN TỰ ĐỘNG
        //   ? priceAutoInput
        //   : 0;
        item.tien_gd = isTonThatSelected ? priceInput : 0; // - CÓ
        // item.tien_gd = 0; // - CÓ
        item.ghi_chu = documentSelected !== documentGroupData[2].value ? noteInput : ''; // - CÓ
        //lưu nhóm mới của ảnh để so sánh và hiển thị lại
        item.nhomMoi = Object.assign(JSON.parse(JSON.stringify(item.nhom)), categoryFixSelectedTmp[0]); //lưu lại nhóm đã chọn
      });
      let routes = navigation.getState().routes;
      NavigationUtil.updateParams(routes[routes.length - 2].key, {anhDaPhanLoai: imagesTmp}); //cập nhật tham số cho màn Chụp ảnh
      NavigationUtil.pop();
    } else {
      let imageBt = []; //mảng các ảnh
      images.map((item) => imageBt.push(item.bt));
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac, //mã đối tác
        so_id: profileData.ho_so.so_id, //số id hồ sơ
        bt: imageBt, //list id ảnh phân loại
        loai: documentSelected, //nhóm tài liệu
        lh_nv: isTonThatSelected ? majorSelected : '', //loại hình nghiệp vụ
        so_id_doi_tuong: doiTuongTTSelected, //Đối tượng tổn thất
        hang_muc: categoryFixSelected, //hạng mục sửa chữa, thay thế
        muc_do: isTonThatSelected ? levelLossSelected : '', //mức độ tổn thất
        thay_the_sc:
          isTonThatSelected && (loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma || loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) //nếu là XE thì có PHƯƠNG ÁN KHẮC PHỤC
            ? thay_the_sc
            : '', //phương án, khắc phục
        chinh_hang: 'K', //nơi sửa chữa
        // isTonThatSelected && loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma //nếu là XE thì có NƠI SỬA CHỮA
        //   ? chinh_hang
        //   : '', //nơi sửa chữa
        thu_hoi:
          isTonThatSelected &&
          (loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma || //nếu là XE thì có THU HỒI VẬT TƯ
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma || //nếu là HÀNG HOÁ thì có THU HỒI VẬT TƯ
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) ////nếu là TÀI SẢN thì có THU HỒI VẬT TƯ
            ? thu_hoi
            : '', //thu hồi vật tư
        so_luong:
          isTonThatSelected &&
          (loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma || //nếu là XE thì có CHI PHÍ TÍNH TOÁN TỰ ĐỘNG
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma ||
            loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma)
            ? soLuongInput
            : 0,
        tien_tu_dong: 0,
        // isTonThatSelected && loaiHinhSelected.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma //nếu là XE thì có CHI PHÍ TÍNH TOÁN TỰ ĐỘNG
        //   ? priceAutoInput
        //   : 0,
        tien_gd: isTonThatSelected ? priceInput : 0,
        // tien_gd: 0,
        vu_tt: isTonThatSelected ? accidentSelected : '', //vụ tổn thất - CÓ
        ghi_chu: documentSelected !== documentGroupData[2].value ? noteInput : '', //ảnh hiện trường thì không có ghi chú
      };
      // console.log('params', params);

      try {
        let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CLASSIFY, params);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        FlashMessageHelper.showFlashMessage('Thông báo', 'Đánh giá thành công', 'success');
        let routes = navigation.getState().routes;
        NavigationUtil.updateParams(routes[routes.length - 2].key, {prevScreen: SCREEN_ROUTER_APP.CLASSIFY}); //cập nhật tham số cho màn Chụp ảnh
        NavigationUtil.pop();
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    }
  };

  //NHÓM TÀI LIỆU thay đổi giá trị
  const onChangeDocumentGroupValue = (value) => {
    if (isLoading) return; //nếu đang loading thì k chạy tiếp
    let categoryFixTmp = [];
    //nếu là tổn thất
    if (value === documentGroupData[0].value) {
      if (profileData.ho_so.nv_ma === DATA_CONSTANT.PROFILE_MA_NGHIEP_VU.TU_NGUYEN) {
        categoryFixTmp = categoryFixData.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.CHINH || item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.PHU);
      } else categoryFixTmp = categoryFixData.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TRACH_NHIEM_DAN_SU);
      setCategoryFixFilter([...categoryFixTmp]); //danh mục đã được filter
      setCategoryFixDataDisplay([...categoryFixTmp.slice(0, 100)]); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
    }
    //nếu là Giấy tờ, tài liệu
    else if (value === documentGroupData[1].value) {
      categoryFixTmp = categoryFixData.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TAI_LIEU);
      setCategoryFixFilter([...categoryFixTmp]); //danh mục đã được filter
      setCategoryFixDataDisplay([...categoryFixTmp]); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
    }
    //nếu là Toàn cảnh, hiện trường
    else if (value === documentGroupData[2].value) {
      categoryFixTmp = categoryFixData.filter((item) => item.loai === DATA_CONSTANT.CATEGORY_COMMON_TYPE.TOAN_CANH);
      setCategoryFixFilter([...categoryFixTmp]); //danh mục đã được filter
      setCategoryFixDataDisplay([...categoryFixTmp]); //chỉ hiển thị từ 0-100, muốn tìm thêm thì search
    }
  };

  const onChangeValueDropdown = (title, items, itemValueSelected) => {
    if (title === titleInput[0]) {
      onChangeDocumentGroupValue(itemValueSelected);
      let listDoiTuong = profileData.ds_doi_tuong.map((doiTuong) => {
        return {
          label: doiTuong.ten_doi_tuong,
          value: doiTuong.so_id_doi_tuong,
        };
      });
      setListDoiTuongTonThat([...listDoiTuong]);
    } else if (title === titleInput[1] || title === titleInput[3] || title === titleInput[2] || title === titleInput[11]) {
      let inputErrTmp = inputErr;
      if (title === titleInput[1] && itemValueSelected) inputErrTmp[1] = '';
      else if (title === titleInput[2] && itemValueSelected) inputErrTmp[2] = '';
      else if (title === titleInput[3] && itemValueSelected) inputErrTmp[3] = '';
      else if (title === titleInput[11] && itemValueSelected) inputErrTmp[11] = '';
      setInputErr([...inputErrTmp]);
    }
  };

  const onChangeSoLuong = (value) => {
    let inputErrTmp = inputErr;
    inputErrTmp[12] = '';
    if (value[0] === '0') {
      value = +value;
      value = value + '';
    }
    if (+value < 0) inputErrTmp[12] = 'Số lượng phải lớn hơn 0';
    setInputErr([...inputErrTmp]);
    if (!value.trim()) setSoLuongInput('0');
    else setSoLuongInput(value);
  };

  /**RENDER  */
  //view hiển thị khi chưa scroll
  const renderForeground = () => {
    let sourceAnh = {
      uri: imageSelected ? (imageSelected.duong_dan ? `data:image/gif;base64,${imageSelected.duong_dan}` : imageSelected.path) : '',
    };
    return (
      <View style={styles.foregroundView}>
        <View>
          <ImageZoom cropWidth={dimensions.width} cropHeight={dimensions.height / 3 + 50} imageWidth={dimensions.width} imageHeight={dimensions.height / 3 + 50}>
            <Image source={sourceAnh} style={styles.imgZoom} resizeMode={'contain'} />
          </ImageZoom>
        </View>
        <FlatList style={{marginVertical: spacing.small}} data={images} horizontal keyExtractor={(item) => item.bt?.toString() || item.name} renderItem={renderItemImage} />
      </View>
    );
  };
  //render ảnh trong flahlist
  const renderItemImage = ({item, index}) => {
    let sourceAnh = {uri: item.duong_dan ? `data:image/gif;base64,${item.duong_dan}` : item.path};
    return (
      <TouchableOpacity
        key={index}
        underlayColor={colors.GRAY}
        onPress={() => {
          setImageSelected(item);
          setImageSelectedPosition(index);
          getDetailImage(item, index);
        }}
        activeOpacity={0.7}>
        <View
          style={[
            styles.itemImageView,
            imageSelectedPosition === index && {
              borderColor: colors.BLUE6,
            },
          ]}>
          <Image source={sourceAnh} style={styles.itemImage} resizeMode={'cover'} />
        </View>
      </TouchableOpacity>
    );
  };
  //view được hiển thị khi scroll lên
  const renderStickyHeader = () => (
    <View style={{marginVertical: spacing.small}}>
      <FlatList data={images} horizontal keyExtractor={(item) => item.bt?.toString() || item.name} renderItem={renderItemImage} />
    </View>
  );
  //render ra nút radio
  const renderRadioInput = (title, radioButtons, onPressRadioButton) => {
    return (
      <>
        <Text style={styles.dropDownTitle}>{title}</Text>
        <RadioGroup radioButtons={radioButtons} onPress={onPressRadioButton} layout={'row'} />
      </>
    );
  };
  //render ra content
  const renderContent = () => {
    let togglePhuongAnKhacPhuc = false,
      toggleNoiSuaChua = false,
      toggleThuHoiVatTu = false,
      toggleChiPhiTinhToan = false,
      toggleHangMucSuaChua = true,
      toggleLoaiHinhNghiepVu = true;
    let soLuongEditable = true;
    let doiTuongTonThatSelected = getDoiTuongById(doiTuongTTSelected || doiTuongDuocPhanLoai?.so_id_doi_tuong);
    profileData.lh_nv.map((item) => {
      if (item.ma === majorSelected) {
        if (item.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) {
          togglePhuongAnKhacPhuc = true;
          toggleNoiSuaChua = true;
          toggleChiPhiTinhToan = true;
          toggleHangMucSuaChua = true;
        }
        if (item.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma && doiTuongTonThatSelected?.loai === 'XE') togglePhuongAnKhacPhuc = true;
        if (item.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma && doiTuongTonThatSelected?.loai === 'XE') toggleChiPhiTinhToan = true;
        if (item.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) toggleChiPhiTinhToan = true;
        if (
          item.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma ||
          item.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma ||
          item.doi_tuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma
        )
          toggleThuHoiVatTu = true;
      }
    });
    /* 
    nếu PHƯƠNG ÁN KHẮC PHỤC là : SỬA CHỮA / THAY THẾ
    HOẶC THU HỒI VẬT TƯ : KHÔNG
     -> DISABLE SỐ LƯỢNG + SỐ LƯỢNG = 0
    */
    if (togglePhuongAnKhacPhuc && (recoveryPlantRadio[0].selected || recoveryPlantRadio[2].selected || recoveryRadio[1].selected)) soLuongEditable = false;
    if (documentSelected !== documentGroupData[0].value) toggleHangMucSuaChua = true;
    return (
      <View style={styles.contentView}>
        {/* NHÓM TÀI LIỆU */}
        {!route.params.prevScreen && (
          <DropdownPicker
            title={titleInput[0]}
            zIndex={9000}
            searchable={false}
            items={documentGroupData}
            itemSelected={documentSelected}
            setItemSelected={setDocumentSelected}
            isOpen={openDocumentGroup}
            setOpen={setOpenDocumentGroup}
            placeholder="Chọn Nhóm tài liệu"
            onOpen={() => closeDropdown(titleInput[0])}
            onChangeValue={onChangeValueDropdown}
            containerStyle={{marginBottom: spacing.small}}
            isRequired={true}
          />
        )}
        {/* LOẠI HÌNH NGHIỆP VỤ */}
        {documentSelected === documentGroupData[0].value && (
          <DropdownPicker
            title={titleInput[2]}
            zIndex={8000}
            searchable={false}
            items={majorData}
            itemSelected={majorSelected}
            setItemSelected={setMajorSelected}
            isOpen={openMajor}
            setOpen={setOpenMajor}
            placeholder="Chọn Loại hình nghiệp vụ"
            onOpen={() => closeDropdown(titleInput[2])}
            inputErr={inputErr[2]}
            onChangeValue={onChangeValueDropdown}
            containerStyle={{marginBottom: spacing.small}}
            isRequired={true}
          />
        )}

        {/* ĐỐI TƯỢNG TỔN THẤT */}
        {/* nếu là từ màn chụp ảnh sang thì k hiển thị đối tượng tổn thất */}
        {!route.params.prevScreen && (
          <DropdownPicker
            title={titleInput[11]}
            zIndex={7000}
            searchable={false}
            items={listDoiTuongTonThat}
            itemSelected={doiTuongTTSelected}
            setItemSelected={setDoiTuongTTSelected}
            isOpen={openDoiTuongTT}
            setOpen={setOpenDoiTuongTT}
            placeholder="Chọn Đối tượng tổn thất"
            onOpen={() => closeDropdown(titleInput[11])}
            inputErr={inputErr[11]}
            onChangeValue={onChangeValueDropdown}
            containerStyle={{marginBottom: spacing.small}}
            isRequired={true}
          />
        )}

        {/*HẠNG MỤC SỬA CHỮA, THAY THẾ*/}
        {toggleHangMucSuaChua && (
          <DropdownPicker
            title={titleInput[1]}
            zIndex={6000}
            // searchable={doiTuongDuocPhanLoai?.nhom === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma ? false : true}
            items={categoryFixDataDisplay}
            itemSelected={categoryFixSelected}
            setItemSelected={setCategoryFixSelected}
            isOpen={openCategoryFix}
            setOpen={setOpenCategoryFix}
            placeholder="Chọn Hạng mục sửa chữa, thay thế"
            onOpen={() => closeDropdown(titleInput[1])}
            inputErr={inputErr[1]}
            onChangeValue={onChangeValueDropdown}
            disableLocalSearch={true}
            onChangeSearchText={onChangeSearchText}
            maxHeight={documentSelected !== documentGroupData[0].value ? 150 : 300}
            containerStyle={{marginBottom: spacing.small}}
            isRequired={true}
          />
        )}

        {documentSelected === documentGroupData[0].value && (
          <>
            {/*MỨC ĐỘ TỔN THẤT*/}
            {
              <DropdownPicker
                title={titleInput[3]}
                zIndex={5000}
                items={levelLossData}
                itemSelected={levelLossSelected}
                setItemSelected={setLevelLossSelected}
                isOpen={openLevelLoss}
                setOpen={setOpenLevelLoss}
                placeholder="Chọn Mức độ tổn thất"
                onOpen={() => {
                  // if (categoryCommon.levelLost.length === 0) {
                  //   FlashMessageHelper.showFlashMessage('Thông báo', 'Dữ liệu Mức độ tổn thất đang tải, vui lòng đợi trong giây lát');
                  //   getLevelLostCategory();
                  // } else if (levelLossData.length === 0) {
                  //   FlashMessageHelper.showFlashMessage('Thông báo', 'Mức độ tổn thất chưa có, vui lòng mở lại Mức độ tổn thất');
                  //   setLevelLossData([...categoryCommon.levelLost]);
                  // }
                  closeDropdown(titleInput[3]);
                }}
                inputErr={inputErr[3]}
                onChangeValue={onChangeValueDropdown}
                containerStyle={{marginBottom: spacing.small}}
                maxHeight={200}
                searchable={false}
                isRequired={true}
              />
            }
            {/* PHƯƠNG ÁN KHẮC PHỤC */}
            {togglePhuongAnKhacPhuc && renderRadioInput(titleInput[4], recoveryPlantRadio, (data) => setRecoveryPlantRadio(cloneObject(data)))}
            {/* NƠI SỬA CHỮA */}
            {/* {toggleNoiSuaChua && renderRadioInput(titleInput[5], repairPlaceRadio, (data) => setRepairPlaceRadio(cloneObject(data)))} */}
            {/* THU HỒI VẬT TƯ */}
            {toggleThuHoiVatTu && renderRadioInput(titleInput[6], recoveryRadio, (data) => setRecoveryRadio(cloneObject(data)))}
            {/* SỐ LƯỢNG */}
            {toggleChiPhiTinhToan && (
              <TextInputOutlined
                title={titleInput[12]}
                value={soLuongInput}
                error={inputErr[12]}
                onChangeText={onChangeSoLuong}
                placeholder="0"
                keyboardType="numeric"
                onFocus={closeDropdown}
                isRequired={true}
                getRef={(ref) => (soLuongRef = ref)}
                onSubmitEditing={() => chiPhiGDVRef?.focus()}
                blurOnSubmit={false}
                returnKeyType={'next'}
                editable={soLuongEditable}
                disabled={!soLuongEditable}
              />
            )}
            {/* CHI PHÍ TÍNH TOÁN TỰ ĐỘNG */}
            {toggleChiPhiTinhToan && profileData?.ho_so?.nghiep_vu !== 'XE_MAY' && (
              <TextInputOutlined
                title={titleInput[7]}
                editable={false}
                value={priceAutoInput}
                onChangeText={setPriceAutoInput}
                placeholder="0"
                keyboardType="numeric"
                onFocus={closeDropdown}
                // isRequired={true}
              />
            )}
            {/* CHI PHÍ DO GDV XÁC ĐỊNH */}
            <TextInputOutlined
              title={titleInput[8]}
              value={priceInput}
              onChangeText={onChangeChiPhiGDV}
              placeholder="0"
              keyboardType="numeric"
              onFocus={closeDropdown}
              error={inputErr[8]}
              // isRequired={true}
              getRef={(ref) => (chiPhiGDVRef = ref)}
              onSubmitEditing={() => ghiChuRef?.focus()}
              blurOnSubmit={false}
              returnKeyType={'next'}
            />

            {/*VỤ TỔN THẤT*/}
            <DropdownPicker
              searchable={false}
              title={titleInput[9]}
              zIndex={3000}
              items={accidentData}
              itemSelected={accidentSelected}
              setItemSelected={setAccidentSelected}
              isOpen={openAccident}
              setOpen={setOpenAccident}
              placeholder="Chọn Vụ tổn thất"
              onOpen={() => closeDropdown(titleInput[9])}
              inputErr={inputErr[9]}
              onChangeValue={onChangeValueDropdown}
              containerStyle={{marginBottom: spacing.small}}
              isRequired={true}
            />
          </>
        )}
        {documentSelected !== documentGroupData[2].value && (
          <TextInputOutlined
            title={titleInput[10]}
            value={noteInput}
            onChangeText={setNoteInput}
            onFocus={closeDropdown}
            multiline={true}
            numberOfLines={3}
            placeholder={titleInput[10]}
            getRef={(ref) => (ghiChuRef = ref)}
          />
        )}
        <ButtonLinear title="Xác nhận" onPress={onPressClassify} linearStyle={{marginBottom: spacing.small}} />
      </View>
    );
  };
  return (
    <ScreenComponent
      isLoading={isLoading}
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={'Đánh giá'}
      renderView={
        <View style={styles.container}>
          <ParallaxScrollView
            ref={parallaxRef}
            style={styles.parallaxScrollView}
            backgroundColor={colors.WHITE9}
            renderStickyHeader={renderStickyHeader} //view được hiển thị khi scroll lên
            stickyHeaderHeight={dimensions.width / 6 + 30}
            renderForeground={renderForeground} //view hiển thị khi chưa scroll
            parallaxHeaderHeight={dimensions.height / 2 + 5}
            contentContainerStyle={{
              flex: 1,
            }}>
            {/*  phần content bên trong - đang vướng scroll lúc có lúc không ở android và ios khi tìm kiếm hàng mục, bàn phím mở lên, đang bị lỗi */}
            {!isIOS ? (
              renderContent()
            ) : (
              <KeyboardAwareScrollView contentContainerStyle={{flex: 1}} showsVerticalScrollIndicator={false}>
                {renderContent()}
              </KeyboardAwareScrollView>
            )}
          </ParallaxScrollView>
        </View>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
  userInfo: state.user.data,
});
const ClassifyScreenConnect = connect(mapStateToProps, {})(ClassifyScreenComponent);
export const ClassifyScreen = memo(ClassifyScreenConnect, isEqual);
