import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: spacing.small,
  },
  contentView: {
    flex: 1,
    minHeight: dimensions.height / 2,
    marginHorizontal: spacing.small,
  },
  imgZoom: {
    width: dimensions.width,
    height: dimensions.height / 3 + 50,
  },
  itemImageView: {
    marginHorizontal: 3,
    borderColor: colors.WHITE,
    borderWidth: 2,
    borderRadius: 10,
  },
  itemImage: {
    width: dimensions.width / 6,
    height: dimensions.width / 6,
    borderRadius: 10,
  },
  foregroundView: {
    paddingTop: spacing.small,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    borderColor: colors.GRAY,
  },
  dropDownTitle: {
    marginBottom: spacing.tiny,
    fontWeight: 'bold',
  },
  parallaxScrollView: {
    overflow: 'hidden',
    flex: 1,
  },
});
