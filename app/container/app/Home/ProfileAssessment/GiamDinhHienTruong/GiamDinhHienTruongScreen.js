import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {getImageNameFromUriCamera, saveToCameraRoll} from '@app/utils/CameraProvider';
import {cloneObject, getCauHinhHoSoByMa} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {requestCameraPermissions, requestLibraryPermissions} from '@app/utils/PermisstionProvider';
import {Icon, ScreenComponent, Text} from '@component';
import {DATA_CONSTANT, SCREEN_ROUTER_APP, isIOS} from '@constant';
import NetInfo from '@react-native-community/netinfo';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, BackHandler, Image, TouchableOpacity, View} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import ImageCropPicker from 'react-native-image-crop-picker';
import ImageMarker, {Position} from 'react-native-image-marker';
import ImageResizer from 'react-native-image-resizer';
import {connect} from 'react-redux';
import {ModalCaiDatGiamDinhHienTruongOto, ModalCamera, ModalHuongDanChupAnh, ModalXemChiTietAnh, ModalXemLaiAnh, TakePhotoStep1, TakeVideo, ThongKeUpload} from './Components';
import styles from './GiamDinhHienTruongStyles';

const button = [
  {
    title: 'Ảnh',
    iconName: 'image',
  },
  {
    title: 'Video',
    iconName: 'video-camera',
  },
];

const GiamDinhHienTruongScreenComponent = ({route, navigation, userInfo, appSettings}) => {
  console.log('GiamDinhHienTruongScreenComponent');
  const profileData = route.params.profileData;
  const categoryImage = profileData.nhom_hang_muc ? profileData.nhom_hang_muc : categoryImage;

  const [anhHoSo, setAnhHoSo] = useState([]);
  const [currentPosition, setCurrentPosition] = useState({});
  const [toggleLoading, setToggleLoading] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ
  const [imageDataStep1, setImageDataStep1] = useState([]);
  const [dataAnhXMHT, setDataAnhXMHT] = useState([]);

  const [listVideo, setListVideo] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  let refModalCamera = useRef(null);
  let refModalXemChiTietAnh = useRef(null);
  let refModalXemLaiAnh = useRef(null);
  let refModalHuongDanChupAnh = useRef(null);
  let refModalCaiDatGiamDinhChiTietOto = useRef(null);

  const [indexView, setIndexView] = useState(0); //view ẢNH / VIDEO
  const [soLanUploadLai, setSoLanUploadLai] = useState(0); //số lần upload lại

  const [totalImagelUpload, setTotalImageUpload] = useState(0); //số lượng ảnh cần upload
  const [imageUploaded, setImageUploaded] = useState(0); //số lượng ảnh đã upload
  const [menuDataType, setMenuDataType] = useState(''); //số lượng ảnh đã upload
  let toggleLoadingRef = useRef(false);

  /* FUNCTION  */
  useEffect(() => {
    initImageDataStep1();
    requestPermistion();
    initData();
    requestCurrentLocation(position => {
      setCurrentPosition(position), err => console.log(err);
    });
  }, []);

  useEffect(() => {
    soLanUploadLai === 1 && onPressNext();
  }, [soLanUploadLai]);
  const initData = async () => {
    await getThumbnailDocument();
    getListVideo();
  };

  //xử lý nút Back
  useEffect(() => {
    let backHandler;
    navigation.addListener('focus', () => {
      backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
      setSoLanUploadLai(0);
    });
    navigation.addListener('blur', () => {
      backHandler?.remove();
      setTotalImageUpload(0);
      setImageUploaded(0);
    });
  }, []);

  //xử lý nút quay lại ở màn Chụp ảnh
  const backAction = () => {
    toggleLoadingRef.current = false;
    let arrUploadThanhCong = imageDataStep1
      .slice(0, imageDataStep1.length - 1) //trừ đi thằng cuối cùng
      .map(item => item.uploadThanhCong); //lấy ra thuộc tính uploadThanhCong
    if (arrUploadThanhCong.every(item => item === true)) NavigationUtil.pop();
    else
      Alert.alert('Thông báo', 'Tồn tại ảnh chưa tải lên hệ thống. Bạn có muốn thoát Giám định hiện trường', [
        {
          text: 'Huỷ',
          style: 'cancel',
        },
        {
          text: 'Đồng ý',
          onPress: () => NavigationUtil.pop(),
        },
      ]);
    return true;
  };
  const onRefresh = () => {
    setRefreshing(true);
    getListVideo();
  };
  const getListVideo = async () => {
    try {
      let params = {so_id: profileData.ho_so.so_id};
      let response = await CarClaimEndpoint.getListVideo(axiosConfig.ACTION_CODE.GET_LIST_VIDEO, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListVideo([...response.data_info]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const requestPermistion = async () => {
    await requestLibraryPermissions();
    requestCameraPermissions();
  };
  //lấy ảnh thumbnail của hồ sơ
  const getThumbnailDocument = async () => {
    try {
      let params = {so_id: profileData.ho_so?.so_id};
      let response = await ESmartClaimEndpoint.getFileThumbnail(axiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      imagesTmp = response.data_info.filter(item => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH && item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.HIEN_TRUONG);
      let imagesTmp = imagesTmp.map(item => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        return item;
      });
      // console.log('imagesTmp', imagesTmp);
      setAnhHoSo([...imagesTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //khởi tạo ảnh step 1
  const initImageDataStep1 = () => {
    let imageDataStep1Tmp = [{path: '', preView: R.icons.ic_gallery}];
    setImageDataStep1(imageDataStep1Tmp);
    setDataAnhXMHT(imageDataStep1Tmp);
  };

  //xử lý ảnh khi chụp
  //type :  0 - camera ; type : 1 - library
  const handleImage = (imageData, menuImageDataSelected, indexOpened, type) => {
    if (profileData && profileData.cau_hinh && type === 0) {
      let cauHinhLuuAnh = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.LUU_ANH_THU_VIEN, profileData.cau_hinh);
      if (appSettings.luuAnhGiamDinhKhiChup !== false && cauHinhLuuAnh.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) saveToCameraRoll(imageData.path);
    }
    let nhom = {};
    let imagesTmp = [];
    // nếu là ảnh xác minh hiện trường
    if (menuImageDataSelected === 'XMHT') {
      imagesTmp = cloneObject(dataAnhXMHT);
      categoryImage.map(item => {
        if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.HIEN_TRUONG && item.ma === 'XMHT') nhom = item;
      });
    }
    // nếu là ảnh hiện trường
    else {
      imagesTmp = cloneObject(imageDataStep1);
      categoryImage.map(item => {
        if (item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.HIEN_TRUONG) nhom = item;
      });
    }
    //nếu là chọn từ thư viện ảnh -> xử ly chọn nhiều ảnh
    if (type === 1) {
      imageData.map(image => {
        let imageName = getImageNameFromUriCamera(image.path); //lấy ra tên ảnh từ uri
        imagesTmp.unshift({
          path: image.path,
          nhom: nhom,
          name: imageName,
        });
      });
    }
    //nếu là chụp ảnh từ camera-> xử lý 1 ảnh
    else {
      let imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri
      imagesTmp.unshift({
        path: imageData.path,
        nhom: nhom,
        name: imageName,
      });
    }
    if (menuImageDataSelected === 'XMHT') setDataAnhXMHT([...imagesTmp]);
    else setImageDataStep1([...imagesTmp]);
    // console.log('imageData', imageData);
  };

  //xoá ảnh
  const removeImage = imageData => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Đồng ý',
        onPress: () => {
          let tmp = [];
          if (imageData.item.nhom.ma === 'XMHT') {
            tmp = dataAnhXMHT;
            tmp.splice(imageData.index, 1);
            setDataAnhXMHT([...tmp]);
          } else {
            tmp = imageDataStep1;
            tmp.splice(imageData.index, 1);
            setImageDataStep1([...tmp]);
          }
        },
      },
    ]);
  };
  const xuLyAnhUploadThatBai = anhThatBai => {
    let imageDataStep1Tmp = [];
    if (anhThatBai.nhom.ma === 'XMHT') {
      imageDataStep1Tmp = dataAnhXMHT;
    } else {
      imageDataStep1Tmp = imageDataStep1;
    }
    imageDataStep1Tmp.map(itemAnh => {
      if (itemAnh.path === anhThatBai.path) {
        itemAnh.uploadThatBai = true;
        itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
      }
      return itemAnh;
    });
    if (anhThatBai.nhom.ma === 'XMHT') {
      setDataAnhXMHT([...imageDataStep1Tmp]);
    } else setImageDataStep1([...imageDataStep1Tmp]);
  };
  const xuLyAnhUploadThanhCong = anhThanhCong => {
    let imageDataStep1Tmp = [];
    if (anhThanhCong.nhom.ma === 'XMHT') {
      imageDataStep1Tmp = dataAnhXMHT;
    } else {
      imageDataStep1Tmp = imageDataStep1;
    }
    imageDataStep1Tmp.map(itemAnh => {
      if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
      return itemAnh;
    });
    if (anhThanhCong.nhom.ma === 'XMHT') {
      setDataAnhXMHT([...imageDataStep1Tmp]);
    } else setImageDataStep1([...imageDataStep1Tmp]);
  };

  const rotationImage = (imageData, rotationType) => {
    imageData.item.rootPath = !imageData.item.rootPath ? imageData.item.path : imageData.item.rootPath; //lưu lại ảnh gốc
    /**
     * direction = 1 : ảnh gốc quay 0 độ
     * direction = 2 : ảnh quay phải 90 độ
     * direction = 3 : ảnh quay phải 180 độ
     * direction = 4 : ảnh quay phải 270 độ
     */
    imageData.item.direction = imageData.item.direction ? imageData.item.direction : 1; //
    let newDirection = 0;
    if (rotationType === 0) newDirection = imageData.item.direction === 4 ? 1 : imageData.item.direction + 1;
    else if (rotationType === 1) newDirection = imageData.item.direction === 1 ? 4 : imageData.item.direction - 1;
    let gocQuay = 0;
    if (newDirection === 1) gocQuay = 0;
    else if (newDirection === 2) gocQuay = 90;
    else if (newDirection === 3) gocQuay = 180;
    else if (newDirection === 4) gocQuay = 270;
    Image.getSize(imageData.item.rootPath, async (imageWidth, imageHeight) => {
      try {
        let response = await ImageResizer.createResizedImage(imageData.item.rootPath, imageWidth, imageHeight, 'PNG', 100, gocQuay);
        imageData.item.direction = newDirection;
        let tmp = [];
        if (imageData.item.nhom.ma === 'XMHT') {
          tmp = dataAnhXMHT;
          tmp[imageData.index].path = response.uri;
          setDataAnhXMHT([...tmp]);
        } else {
          tmp = imageDataStep1;
          tmp[imageData.index].path = response.uri;
          setImageDataStep1([...tmp]);
        }
      } catch (error) {
        Alert.alert('Có lỗi khi xoay ảnh', JSON.stringify(error));
      }
    });
  };

  // ấn nút NEXT
  const onPressNext = async () => {
    if (toggleLoading) {
      Alert.alert('Thông báo', 'Ứng dụng đang tải ảnh lên hệ thống, Bạn có chắc muốn dừng tải ảnh lên', [
        {
          text: 'Dừng tải lên',
          style: 'destructive',
          onPress: () => {
            setToggleLoading(false);
            toggleLoadingRef.current = false;
          },
        },
        {
          text: 'Tiếp tục tải lên',
        },
      ]);
      return;
    }

    //ẢNH HIỆN TRƯỜNG
    let imagesUploadToServer = [];
    let imagesUpload = [...imageDataStep1, ...dataAnhXMHT];
    imagesUploadToServer = imagesUpload.filter(imageItem => imageItem.path !== '' && !imageItem.uploadThanhCong);
    if (imagesUploadToServer.length === 0) {
      if (route.params.loaiAnh) NavigationUtil.pop();
      else
        NavigationUtil.push(SCREEN_ROUTER_APP.DANH_GIA_HIEN_TRUONG, {profileData: profileData, prevScreen: SCREEN_ROUTER_APP.GIAM_DINH_HIEN_TRUONG, danhGiaHienTruong: route.params.danhGiaHienTruong});
      return;
    }
    if (soLanUploadLai === 0) setTotalImageUpload(imagesUploadToServer.length); //số lượng ảnh cần upload
    setToggleLoading(true);
    toggleLoadingRef.current = true;

    // UPLOAD CŨ
    // let response = await Promise.all(
    //   imagesUploadToServer.map(async (item) => {
    //     return uploadImageToServer([item]);
    //   }),
    // );

    // UPLOAD MỚI
    let timeStart = moment().format('HH:mm:ss');
    let response = [];
    try {
      for (let i = 0; i < imagesUploadToServer.length; i++) {
        if (!toggleLoadingRef.current) return;
        const item = imagesUploadToServer[i];
        response.push(
          await uploadImageToServer(
            [item],
            anhThanhCong => {
              setImageUploaded(prevValue => prevValue + 1);
              xuLyAnhUploadThanhCong(anhThanhCong);
            },
            anhThatBai => xuLyAnhUploadThatBai(anhThatBai),
            i,
          ),
        );
      }
    } catch (error) {
      console.log('error', error);
    }
    let timeEnd = moment().format('HH:mm:ss');
    let content = '';
    if (userInfo) content = userInfo.nguoi_dung?.nsd || '';
    content += '-' + timeStart + '-' + timeEnd + '-' + JSON.stringify(response);
    try {
      let networkInfo = await NetInfo.fetch();
      content += '-' + JSON.stringify(networkInfo);
    } catch (error) {}

    let paramsWriteLog = {
      content: content,
    };
    // END CODE MỚI

    // CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR
    if (soLanUploadLai === 0 && response.includes('"Network Error"')) {
      setToggleLoading(false);
      setSoLanUploadLai(1); //nếu thay đổi soLanUpload -> useEffect ở trên sẽ check -> gọi lại hàm onPressNext
      return;
    }
    // END CODE RETRY 1 LẦN KHI DÍNH NETWORK ERROR

    let haveErr = '';
    response = response.filter(item => item !== true);
    //bỏ đi các thông tin trùng
    let uniqueChars = response.filter((element, index) => {
      return response.indexOf(element) === index;
    });
    if (uniqueChars.length > 0) haveErr = uniqueChars.join(', ');
    //thực hiện xong hết thì mới reset lại data
    setToggleLoading(false);
    getThumbnailDocument();
    if (haveErr) {
      FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi xảy ra khi tải ảnh lên hệ thống \n' + haveErr, 'info');
      return;
    }
    initImageDataStep1();
    if (route.params.loaiAnh) {
      NavigationUtil.pop();
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
      return;
    }
    NavigationUtil.push(SCREEN_ROUTER_APP.DANH_GIA_HIEN_TRUONG, {profileData: profileData, prevScreen: SCREEN_ROUTER_APP.GIAM_DINH_HIEN_TRUONG, danhGiaHienTruong: route.params.danhGiaHienTruong});
    FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
    toggleLoadingRef.current = false;
  };

  //mở ảnh đã chụp
  const onPressAnhDaChup = () => {
    let imgsTmp = [];
    imgsTmp = anhHoSo.filter(item => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH && item.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.HIEN_TRUONG);
    refModalXemLaiAnh.current.show(imgsTmp);
  };

  //ấn nút mở CAMERA (step 1 - step 2), mở modal (step 3 - step 4), mở modalCAMERA (giấy tờ bước 4)
  //type : 0 - camera ; type : 1 - lib
  const onPressOpenCamera = (indexOpened, menuImageData, type) => {
    setMenuDataType(menuImageData);

    if (type === 0) refModalCamera.current.show();
    else if (type === 1) openCamera(indexOpened, menuImageData, type);
  };

  //mở IMAGE CROP CAMERA
  //type : 0 - camera ; type : 1 - library
  const openCamera = (indexOpened, menuImageData, type) => {
    let imgCropOpts = {
      mediaType: 'photo', //mặc định là chụp ảnh từ camera
      cropping: false,
      enableRotationGesture: true,
      compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
      showCropGuidelines: true, //tắt khung 3x3 đi
      compressImageQuality: 0.5,
      useFrontCamera: false,
    };

    if (type === 1) {
      imgCropOpts.multiple = true;
      imgCropOpts.maxFiles = 10;
      ImageCropPicker.openPicker(imgCropOpts)
        .then(async data => {
          // COMMMENT TẠM CHỖ NÀY. NẾU UPLOAD TỪ THƯ VIỆN THÌ KHÔNG CHÈN THÔNG TIN TỪ APP LÊN
          // for (let i = 0; i < data.length; i++) {
          //   let imageAddText = await chenThongTinLenAnh(data[i]);
          //   data[i].path = (!isIOS ? 'file://' : '') + imageAddText;
          // }
          handleImage(data, menuImageData, indexOpened, type);
        })
        .catch(err => console.log('err', err));
      return;
    }
    //Open Camera
    ImageCropPicker.openCamera(imgCropOpts)
      .then(data => handleImage(data, menuImageData, indexOpened, type))
      .catch(err => console.log('err', err));
  };
  //mở hướng dẫn
  const onPressTutorial = () => refModalHuongDanChupAnh.current.show();
  const chenThongTinLenAnh = async dataImage => {
    //ngày giờ / toạ độ / người / thông tin máy
    let txtChen = '';
    txtChen += userInfo.nguoi_dung.nsd;
    let tenMay = await DeviceInfo.getBrand();
    txtChen += '\n' + moment().format('HH:mm DD/MM/YYYY') + ' ';
    txtChen += tenMay;
    if (currentPosition && currentPosition.coords) txtChen += '\n' + currentPosition.coords.longitude.toFixed(3) + ' ; ' + currentPosition.coords.latitude.toFixed(3) + ' ';
    let imageAddText = await ImageMarker.markText({
      backgroundImage: {
        src: dataImage.path,
        scale: 1,
      },
      watermarkTexts: [
        {
          text: txtChen,
          positionOptions: {
            position: Position.bottomRight,
          },
          style: {
            color: '#FFF',
            fontSize: isIOS ? 30 : 30,
          },
        },
      ],
      quality: 100,
    }).catch(err => {
      Alert.alert('Chèn toạ độ không thành công', err.message);
      return '';
    });
    return imageAddText;
  };

  //upload ảnh lên server
  const uploadImageToServer = (imagesData, cbSuccess, cbErr, indexImage) => {
    return new Promise(
      async resolve => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            nhom: e.nhom.ma,
            x: currentPosition.coords?.latitude,
            y: currentPosition.coords?.longitude,
          };
          files.push(file);
        });
        let params = {
          images: imagesData,
          so_id: profileData.ho_so.so_id,
          pm: 'GD',
          ma_doi_tac: profileData.ho_so.ma_doi_tac,
          files: files,
          ung_dung: 'MOBILE_BT',
          so_id_doi_tuong: route.params.doiTuongDuocChupAnh.so_id_doi_tuong,
        };
        console.log('🚀 ~ params:', params);

        // CODE GIÁ LẬP LỖI NETWORK ERROR, UPLOAD > 4 cái ảnh
        // if ((indexImage === 3 || indexImage === 2) && soLanUploadLai === 0) {
        //   imagesData[0].lyDoLoi = JSON.stringify('Network Errorxx');
        //   cbErr(imagesData[0]);
        //   resolve(JSON.stringify('Network Errorxx'));
        //   return;
        // }

        try {
          let response = await ESmartClaimEndpoint.uploadFile(axiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          if (response?.state_info?.status === 'NotOK') {
            imagesData[0].lyDoLoi = response.state_info.message_body;
            cbErr(imagesData[0]);
            resolve(response.state_info.message_body);
          } else if (response?.state_info?.status === 'OK') {
            cbSuccess(imagesData[0]);
            resolve(true);
          } else {
            imagesData[0].lyDoLoi = JSON.stringify(response?.message || response);
            cbErr(imagesData[0]);
            resolve(JSON.stringify(response?.message || response));
          }
        } catch (error) {
          Alert.alert('Thông báo tải ảnh lên hệ thống', JSON.stringify(error?.message || error));
          resolve(JSON.stringify(error?.message || error));
        }
      },
      reject => reject(),
    );
  };

  //xem chi tiết ảnh đã chụp
  const onPressXemLaiAnh = imageData => {
    refModalXemChiTietAnh.current.show({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
  };

  /* RENDER  */
  //nút gợi ý
  const renderBtnHint = () => {
    return (
      <TouchableOpacity style={{...styles.btnHint}} activeOpacity={0.5} onPress={onPressTutorial}>
        <Icon.AntDesign name={'questioncircle'} size={40} color={colors.PRIMARY_03} />
      </TouchableOpacity>
    );
  };
  //nút xem lại ảnh đã chụp
  const renderBtnImages = () => {
    return (
      <TouchableOpacity style={{...styles.btnHint}} activeOpacity={0.5} onPress={onPressAnhDaChup}>
        <Icon.Ionicons name={'image'} size={40} color={colors.PRIMARY_03} />
      </TouchableOpacity>
    );
  };
  // các nút ở footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <View style={styles.btnBack} />
        {/* NÚT TIẾP TỤC - HOÀN THÀNH   */}
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={onPressNext}
          style={styles.btnNext}
          // disabled={toggleLoading}
        >
          {!toggleLoading ? <Text style={styles.txtBtnBottom}>Hoàn thành</Text> : <ActivityIndicator size="large" color={colors.GRAY} style={{flex: 1}} />}
          <View style={styles.iconRightBtnView}>
            <Icon.Ionicons name={'checkmark-sharp'} size={25} style={styles.iconRightBtn} color={colors.WHITE} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  const renderBtnSwitchView = () => (
    <View style={styles.btnRowContainer}>
      {button.map((item, index) => {
        let selected = indexView === index;
        return (
          <TouchableOpacity
            key={index}
            style={[styles.btnEdit, {backgroundColor: selected ? colors.PRIMARY : '#FFF'}, index === 0 && {marginRight: spacing.smaller}]}
            flex={1}
            onPress={() => setIndexView(index)}>
            <Icon.FontAwesome name={item.iconName} size={16} color={selected ? colors.WHITE : colors.PRIMARY} />
            <Text style={[styles.txtTab, {color: selected ? colors.WHITE : colors.PRIMARY}]}>{item.title}</Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
  const renderRightHeader = () => {
    return (
      <TouchableOpacity style={styles.btnLuuView} onPress={() => refModalCaiDatGiamDinhChiTietOto.current.show()}>
        <Icon.AntDesign name="setting" color="#FFF" size={30} />
      </TouchableOpacity>
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle="Giám định hiện trường"
      onPressBack={backAction}
      renderRightHeader={renderRightHeader}
      renderView={
        <>
          {renderBtnSwitchView()}
          <ThongKeUpload totalImagelUpload={totalImagelUpload} imageUploaded={imageUploaded} toggleLoading={toggleLoading} />
          {indexView === 0 && (
            <TakePhotoStep1
              key={0}
              rotationImage={rotationImage}
              imagesData={imageDataStep1}
              removeImage={removeImage}
              onPressOpenCamera={onPressOpenCamera}
              onPressXemLaiAnh={onPressXemLaiAnh}
              profileData={profileData}
              dataAnhXMHT={dataAnhXMHT}
            />
          )}
          {indexView === 1 && <TakeVideo profileData={profileData} listVideo={listVideo} getListVideo={getListVideo} onRefresh={onRefresh} refreshing={refreshing} />}

          {/* render phần nút dưới cùng */}
          {indexView === 0 && renderFooter()}
          {/* nút hướng dẫn */}
          {indexView === 0 && <View style={styles.btnHintView}>{renderBtnHint()}</View>}
          {/* nút xem lại ảnh đã chụp */}
          {indexView === 0 && <View style={styles.btnImageView}>{renderBtnImages()}</View>}
          {/* modal hướng dẫn chụp ảnh */}
          <ModalHuongDanChupAnh
            key="ModalHuongDanChupAnh"
            ref={refModalHuongDanChupAnh}
            showModalXemChiTietAnh={imageData => {
              isIOS && refModalHuongDanChupAnh.current.hide();
              setTimeout(() => refModalXemChiTietAnh.current.show(imageData), isIOS ? 400 : 0);
            }}
          />
          {/* modal camera  */}
          <ModalCamera key="ModalCamera" ref={refModalCamera} handleImage={handleImage} menuDataType={menuDataType} currentPosition={currentPosition} />
          {/* modal hiển thị ảnh trong phần hướng dẫn */}
          <ModalXemChiTietAnh key="ModalXemChiTietAnh" ref={refModalXemChiTietAnh} />
          {/* modal hiển thị lại ảnh đã chụp */}
          <ModalXemLaiAnh
            key="ModalXemLaiAnh"
            ref={refModalXemLaiAnh}
            showModalXemChiTietAnh={imageData => {
              isIOS && refModalXemLaiAnh.current.hide();
              setTimeout(() => refModalXemChiTietAnh.current.show(imageData), isIOS ? 400 : 0);
            }}
          />
          <ModalCaiDatGiamDinhHienTruongOto ref={refModalCaiDatGiamDinhChiTietOto} appSettings={appSettings} />
        </>
      }
    />
  );
};
const mapStateToProps = state => ({
  categoryImage: state.categoryImage.data,
  categoryCommon: state.categoryCommon.data,
  userInfo: state.user.data,
  appSettings: state.appSetting,
});
const mapDispatchToProps = {};
const GiamDinhHienTruongScreenConnect = connect(mapStateToProps, mapDispatchToProps)(GiamDinhHienTruongScreenComponent);
export const GiamDinhHienTruongScreen = memo(GiamDinhHienTruongScreenConnect, isEqual);
