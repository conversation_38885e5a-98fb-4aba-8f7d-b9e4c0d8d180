import {ASYNC_STORAGE_KEY} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {updateLuuAnhGiamDinhKhiChup} from '@app/redux/slices/AppSettingSlice';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {Icon, Text} from '@component';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Alert, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {Switch} from 'react-native-switch';
import {useDispatch} from 'react-redux';

const ModalCaiDatGiamDinhHienTruongOtoComponent = forwardRef(({appSettings}, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: hideModal,
    }),
    [],
  );

  const [isVisible, setIsVisible] = useState(false);
  const dispatch = useDispatch();

  const hideModal = () => setIsVisible(false);

  const onChangeAppSettings = async (value, type) => {
    let newAppSetting = {...appSettings};
    if (!value)
      Alert.alert('Thông báo', 'Bạn đang tắt tính năng "Lưu ảnh giám định", ảnh sẽ không được lưu vào điện thoại trong quá trình giám định', [
        {
          text: 'Đồng ý',
          onPress: () => {
            dispatch(updateLuuAnhGiamDinhKhiChup(value));
            newAppSetting.luuAnhGiamDinhKhiChup = value;
          },
        },
        {text: 'Để sau', style: 'destructive'},
      ]);
    else {
      dispatch(updateLuuAnhGiamDinhKhiChup(value));
      newAppSetting.luuAnhGiamDinhKhiChup = value;
    }
    await AsyncStorage.setItem(ASYNC_STORAGE_KEY.APP_SETTINGS, JSON.stringify(newAppSetting));
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Cài đặt" />
        <TouchableOpacity style={styles.closeView} onPress={hideModal}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderSwitchImageViewButton = (title, value, onValueChange, activeText, inActiveText, switchLeftPx, switchRightPx, switchWidthMultiplier) => (
    <View style={styles.settingItemView}>
      <View style={styles.leftView}>
        <Icon.MaterialCommunityIcons name={value ? 'image-plus' : 'image-off'} size={25} color={colors.WHITE} />
      </View>
      <View style={styles.centerView}>
        <Text>{title}</Text>
      </View>
      <View style={styles.rightView}>
        <Switch
          value={value}
          onValueChange={onValueChange}
          disabled={false}
          activeText={activeText}
          inActiveText={inActiveText}
          circleSize={20}
          barHeight={25}
          circleBorderWidth={0}
          // màu khi active
          circleActiveColor={colors.WHITE}
          backgroundActive={colors.GREEN}
          //màu khi InActive
          circleInActiveColor={colors.WHITE}
          backgroundInactive={colors.GRAY}
          // renderInsideCircle={() => <CustomComponent />} // custom component to render inside the Switch circle (Text, Image, etc.)
          changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
          innerCircleStyle={styles.switch} // style for inner animated circle for what you (may) be rendering inside the circle
          outerCircleStyle={{}} // style for outer animated circle
          renderActiveText={true}
          renderInActiveText={true}
          switchLeftPx={switchLeftPx} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
          switchRightPx={switchRightPx} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
          switchWidthMultiplier={switchWidthMultiplier} // multipled by the `circleSize` prop to calculate total width of the Switch
          switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
        />
      </View>
    </View>
  );

  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSwitchImageViewButton(
          'Lưu ảnh giám định',
          appSettings.luuAnhGiamDinhKhiChup === undefined ? true : appSettings.luuAnhGiamDinhKhiChup,
          (value) => onChangeAppSettings(value, 5),
          'BẬT',
          'TẮT',
          2,
          2,
          4,
        )}
      </ScrollView>
    );
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} onSwipeComplete={hideModal} onBackdropPress={hideModal} onBackButtonPress={hideModal}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    height: dimensions.height / 6,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },

  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    textAlign: 'center',
    margin: scale(spacing.small),
  },
  closeView: {
    borderRadius: 25,
    backgroundColor: colors.GRAY2,
    marginRight: scale(spacing.small),
  },
  content: {
    margin: scale(spacing.small),
    paddingBottom: vScale(spacing.small),
    flex: 1,
  },
  settingItemView: {
    flexDirection: 'row',
  },
  leftView: {
    width: dimensions.width / 11,
    height: dimensions.width / 11,
    backgroundColor: colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 3,
    marginRight: spacing.small,
  },
  centerView: {
    flex: 1,
    justifyContent: 'center',
  },
  rightView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export const ModalCaiDatGiamDinhHienTruongOto = ModalCaiDatGiamDinhHienTruongOtoComponent;
