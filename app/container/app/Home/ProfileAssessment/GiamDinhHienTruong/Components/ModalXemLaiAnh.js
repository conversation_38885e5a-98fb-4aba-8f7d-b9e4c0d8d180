import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import R from '@R';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import Modal from 'react-native-modal';
import Progress from 'react-native-progress/Circle';

const ModalXemLaiAnhComponent = forwardRef(({showModalXemChiTietAnh}, ref) => {
  // console.log('ModalXemLaiAnh', props);
  let scrollViewModalRef = useRef(null);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [contentModalHeight, setContentModalHeight] = useState((dimensions.height / 3) * 4);
  const [anhXemLai, setAnhXemLai] = useState([]);
  const [visible, setVisible] = useState(false);
  useImperativeHandle(ref, () => ({
    show: (listAnh) => {
      setAnhXemLai([...listAnh]);
      setVisible(true);
    },
    hide: onPressHideModal,
    getVisible: () => {
      return visible;
    },
  }));
  const onPressHideModal = () => {
    setVisible(false);
    setAnhXemLai([]);
  };
  const onPressLayChiTietAnh = async (imageData) => {
    try {
      let params = {
        so_id: imageData.so_id,
        bt: imageData.bt,
      };
      let response = await ESmartClaimEndpoint.getFile(axiosConfig.ACTION_CODE.DOCUMENT_DETAIL, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      showModalXemChiTietAnh({
        title: 'Ảnh chi tiết',
        imageData: response.data_info.duong_dan,
        base64: true,
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  /* RENDER */

  //render ITEM ảnh xem lại
  const renderAnhXemLaiItem = (data) => {
    let imageData = data.item;
    return (
      <TouchableOpacity onPress={() => onPressLayChiTietAnh(imageData)}>
        <ImageProcess
          source={{
            uri: `data:image/gif;base64,${imageData.duong_dan}`,
          }}
          indicator={Progress.Circle}
          style={styles.imageDocument}
          imageStyle={{borderRadius: 20}}
          indicatorProps={{
            size: 70,
            borderWidth: 0,
            color: colors.PRIMARY,
            unfilledColor: colors.PRIMARY_LIGHT,
          }}
          renderError={() => (
            <View>
              <Image source={R.icons.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />
            </View>
          )}
        />
      </TouchableOpacity>
    );
  };
  const renderAnhXemLai = (listAnhXemLai) => {
    return <FlatList data={listAnhXemLai} renderItem={renderAnhXemLaiItem} keyExtractor={(item) => item.bt.toString()} numColumns={3} horizontal={false} style={{marginBottom: 10}} />;
  };

  return (
    <Modal
      isVisible={visible}
      onSwipeComplete={onPressHideModal}
      onBackdropPress={onPressHideModal}
      swipeDirection={['down']}
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
      scrollOffset={scrollOffSet}
      scrollOffsetMax={dimensions.height / 2 - contentModalHeight} // content height - ScrollView height
      propagateSwipe={true}
      style={styles.modal}>
      <View style={styles.modalView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle}>Ảnh hiện trường đã chụp</Text>
          <TouchableOpacity style={styles.closeView} onPress={onPressHideModal}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>
        <ScrollView
          ref={scrollViewModalRef}
          onScroll={(event) => {
            setScrollOffSet(event.nativeEvent.contentOffset.y);
          }}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}>
          {renderAnhXemLai(anhXemLai)}
        </ScrollView>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 10,
    flex: 1,
    marginLeft: 30,
    // borderWidth: 1,
  },
  closeView: {
    marginRight: 15,
    // position: 'absolute',
    // top: ,
    // right: 15,
  },
  tieuDeDanhMucAnhXemLai: {
    marginLeft: 10,
    fontWeight: 'bold',
  },
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 1,
  },
});

const ModalXemLaiAnhMemo = memo(ModalXemLaiAnhComponent, isEqual);
export const ModalXemLaiAnh = ModalXemLaiAnhMemo;
