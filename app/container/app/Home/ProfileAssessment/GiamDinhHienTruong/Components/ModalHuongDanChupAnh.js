import {DATA_HUONG_DAN_CHUP_ANH_GIAM_DINH} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalHuongDanChupAnhComponent = forwardRef(({showModalXemChiTietAnh}, ref) => {
  let scrollViewModalRef = useRef(null);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [contentModalHeight, setContentModalHeight] = useState((dimensions.height / 3) * 4);
  const modalData = DATA_HUONG_DAN_CHUP_ANH_GIAM_DINH[0];
  const [visible, setVisible] = useState(false);
  useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
    },
    hide: onPressHideModal,
    getVisible: () => {
      return visible;
    },
  }));
  const onPressHideModal = () => {
    setVisible(false);
  };

  return (
    <Modal
      isVisible={visible}
      onSwipeComplete={onPressHideModal}
      onBackdropPress={onPressHideModal}
      swipeDirection={['down']}
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
      scrollOffset={scrollOffSet}
      scrollOffsetMax={dimensions.height / 2 - contentModalHeight} // content height - ScrollView height
      propagateSwipe={true}
      style={styles.modal}>
      <View style={styles.modalView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle}>{modalData.title}</Text>
          <TouchableOpacity style={styles.closeView} onPress={onPressHideModal}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>

        <ScrollView
          ref={scrollViewModalRef}
          onScroll={(event) => {
            setScrollOffSet(event.nativeEvent.contentOffset.y);
          }}
          scrollEventThrottle={16}>
          <View onLayout={(event) => setContentModalHeight(event.nativeEvent.layout.height)} style={styles.modalContentView}>
            {modalData.content.map((item, index) => {
              return (
                <View
                  style={{
                    ...styles.modalTxt,
                    marginLeft: index == 0 || index == modalData.content.length - 1 || (modalData.title == 'Chụp ảnh giấy tờ liên quan' && index == 5) ? 0 : 20,
                  }}>
                  <Icon.Entypo name="dot-single" size={20} />
                  <View
                    style={{
                      flexDirection: 'column',
                    }}>
                    <Text>{item}</Text>
                    {modalData.imagesDetail[index] != '' && (
                      <TouchableOpacity
                        onPress={() => {
                          showModalXemChiTietAnh({
                            title: item,
                            imageData: modalData.imagesDetail[index],
                          });
                        }}>
                        <Text style={styles.txtIllustration}>Ảnh minh hoạ</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 10,
    flex: 1,
    marginLeft: 30,
  },
  closeView: {
    marginRight: 15,
  },
  modalContentView: {
    marginHorizontal: 10,
    marginTop: 10,
    flex: 1,
  },
  txtIllustration: {
    color: colors.PRIMARY_DARK_08,
    textDecorationLine: 'underline',
  },
  modalTxt: {
    flexDirection: 'row',
    paddingRight: 20,
    marginVertical: 5,
  },
});

const ModalHuongDanChupAnhMemo = memo(ModalHuongDanChupAnhComponent, isEqual);
export const ModalHuongDanChupAnh = ModalHuongDanChupAnhMemo;
