import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions} from '@app/theme';
import {CommonOutlinedTextFieldWithIcon, Icon, Text} from '@component';
import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

function ModalKetThucChupAnh(props) {
  // console.log('ModalKetThucChupAnh', props);

  return (
    <Modal
      isVisible={props.toggleModalSuccess} //toggleModalSuccess
      swipeDirection={'right'}
      style={styles.modal}>
      <View style={styles.modalSuccessView}>
        <View>
          <View style={styles.successTopView}>
            <Icon.Ionicons name="checkmark-circle" color={colors.GREEN6} size={80} />
            <Text style={styles.successTitle}><PERSON><PERSON><PERSON> thành chụp ảnh giám định</Text>
          </View>
          <View style={styles.successCenterView}>
            {!props.toggleFormKetThucGiamDinh ? (
              <>
                {/* <TouchableOpacity
                  style={{
                    ...styles.btnSuccessView,
                    backgroundColor: colors.GRAY3,
                  }}
                  onPress={() => props.setToggleFormKetThucGiamDinh(true)}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                    }}>
                    <Icon.FontAwesome name="check-circle" color={colors.WHITE} size={20} />
                    <Text style={styles.txtSuccess} children="Kết thúc giám định" />
                  </View>
                </TouchableOpacity> */}
                <TouchableOpacity
                  style={{
                    ...styles.btnSuccessView,
                    backgroundColor: colors.PRIMARY_08,
                  }}
                  onPress={() => {
                    props.setToggleModalSuccess(false);
                    setTimeout(() => NavigationUtil.pop(), 250);
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                    }}>
                    <Icon.AntDesign name="profile" color={colors.WHITE} size={20} />
                    <Text style={styles.txtSuccess} children="Xem chi tiết hồ sơ" />
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    ...styles.btnSuccessView,
                    backgroundColor: 'green',
                  }}
                  onPress={() => {
                    props.setToggleModalSuccess(false);
                    setTimeout(() => NavigationUtil.pop(4), 300);
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                    }}>
                    <Icon.Feather name="home" color={colors.WHITE} size={20} />
                    <Text style={styles.txtSuccess} children="Trở về trang chủ" />
                  </View>
                </TouchableOpacity>
              </>
            ) : (
              <View>
                <View>
                  <Text style={{marginBottom: 10}}>Nội dung yêu cầu kết thúc giám định</Text>
                  <CommonOutlinedTextFieldWithIcon
                    value={props.inputNoiDungKetThucGiamDinh}
                    onChangeText={(value) => props.setInputNoiDungKetThucGiamDinh(value)}
                    placeholder="Nhập nội dung"
                    isPickerModal
                    onPressOpenPickerModal={() => {
                      props.pickerModalNoiDungKetThucRef.current.openModal();
                    }}
                  />
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                    }}>
                    <TouchableOpacity
                      style={{
                        ...styles.btnFormKetThucGiamDinh,
                        backgroundColor: colors.GRAY3,
                      }}
                      onPress={() => props.setToggleFormKetThucGiamDinh(false)}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'center',
                        }}>
                        <Icon.FontAwesome name="arrow-left" color={colors.WHITE} size={20} />
                        <Text style={styles.txtSuccess}>Quay lại</Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        ...styles.btnFormKetThucGiamDinh,
                        backgroundColor: 'green',
                      }}
                      onPress={props.onPressEndAssessment}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'center',
                        }}>
                        <Icon.FontAwesome name="check-circle" color={colors.WHITE} size={20} />
                        <Text style={styles.txtSuccess}>Xác nhận</Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalSuccessView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  successTopView: {
    width: dimensions.width - 30,
    height: dimensions.height / 5,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // borderBottomWidth: 5,
    // borderBottomColor: colors.WHITE,
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 20,
  },
  btnSuccessView: {
    height: dimensions.height / 20,
    width: dimensions.width - 50,
    // borderWidth: 1,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
  },
  successCenterView: {
    width: dimensions.width - 30,
    height: dimensions.height / 4,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    // borderTopWidth: 5,
  },
  txtSuccess: {
    textAlign: 'center',
    paddingHorizontal: 10,
    color: colors.WHITE,
    fontSize: 16,
  },
  btnFormKetThucGiamDinh: {
    height: dimensions.height / 20,
    width: (dimensions.width - 100) / 2,
    // borderWidth: 1,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 5,
  },
});

const mapStateToProps = (state) => ({});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(ModalKetThucChupAnh);
