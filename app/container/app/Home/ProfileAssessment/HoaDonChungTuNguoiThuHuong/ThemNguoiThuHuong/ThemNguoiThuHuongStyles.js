import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width, height} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    // width: width,
    // marginTop: 10,
  },
  content: {
    flex: 1,
    marginHorizontal: 10,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  dropDownView: {
    marginVertical: 10,
    // flex : 1,
    // flexDirection: 'row',
    // justifyContent: 'flex-start',
  },
  headerView: {
    borderBottomWidth: 1,
    borderBottomColor: colors.WHITE6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
  },
  txtHeader: {
    fontSize: 17,
    fontWeight: '900',
  },
  iconClose: {
    color: colors.BLACK,
    opacity: 0.5,
  },
  txtHeaderInput: {
    marginBottom: 8,
    fontWeight: 'bold',
    // color: colors.BLACK
  },
  input: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 5,
    paddingLeft: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  inputView: {
    marginBottom: 10,
  },
  btn: {
    flex: 1,
    // borderWidth: 1,
    borderRadius: 30,
    // height:
  },

  btnView: {
    flex: 1,
    marginTop: 20,
    marginBottom: 30,
    flexDirection: 'row',
  },
  txtBtn: {
    paddingVertical: 15,
    paddingHorizontal: 15,
    textAlign: 'center',
    color: colors.WHITE,
    fontSize: 16,
  },
  btnClose: {
    backgroundColor: colors.GRAY9,
    marginRight: 30,
  },
  btnSucess: {
    backgroundColor: colors.GRAY9,
  },
  doubleInputRow: {
    width: (width - 30) / 2,
  },
  doubleInputRowView: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: {
    marginBottom: spacing.tiny,
    fontWeight: 'bold',
    // paddingRight: spacing.tiny,
  },
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: width,
    height: height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  footerView: {
    width: width,
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
    paddingHorizontal: scale(spacing.small),
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    color: colors.PRIMARY,
    marginVertical: scale(spacing.smaller),
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
    paddingVertical: vScale(spacing.tiny),
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
    paddingVertical: spacing.default,
  },
  txtTenHangMuc: {
    flex: 1,
    // fontSize: 13,
    // fontWeight: '500',
    alignSelf: 'center',
    color: colors.PRIMARY,
    marginHorizontal: spacing.tiny,
  },
  itemSoTienThanhToanTheoLHNVView: {
    // paddingVertical: spacing.default,
  },
  inputStyle: {
    fontSize: FontSize.size12,
    borderRadius: 0,
    width: dimensions.width / 5.5,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
});
