import {DATA_LOAI_THUE, FORMAT_DATE_TIME} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ModalSelectSimple, ScreenComponent, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, RefreshControl, SafeAreaView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {ModalChonLoaiThue} from '../Components';
import styles from './ThemHoaDonChungTuStyles';
import {onChangeAlias} from '@app/utils/string';
import {dimensions, spacing} from '@app/theme';

const ThemHoaDonChungTuScreenComponent = (props) => {
  console.log('ThemHoaDonChungTuOtoScreenComponent');
  const {route} = props;
  const {profileData, itemSelected} = route?.params;

  let maSoThueRef = useRef(null);
  let maSoThueDvNhanHoaDonRef = useRef(null);
  let diaChiRef = useRef(null);
  let mauHoaDonRef = useRef(null);
  let kyHieuHoaDonRef = useRef(null);
  let soHoaDonRef = useRef(null);
  let noiDungRef = useRef(null);
  let soTienRef = useRef(null);
  let tienThueRef = useRef(null);
  let tongCongRef = useRef(null);
  let tenDvNhanHoaDonRef = useRef(null);
  let dcDvNhanHoaDonRef = useRef(null);
  let webTraCuuHoaDonRef = useRef(null);
  let maTraCuuHoaDonRef = useRef(null);
  let refModalLoaiThue = useRef(null);
  let refModalWebsiteTraCuuHoaDon = useRef(null);

  const [toggleNgayPhatHanh, setToggleNgayPhatHanh] = useState(false);

  //dropdown đơn vị phát hành
  const [dataDvPhatHanh, setDataDvPhatHanh] = useState([]);
  const [openDvPhatHanh, setOpenDvPhatHanh] = useState(false);
  const [openLoaiHinhNghiepVu, setOpenLoaiHinhNghiepVu] = useState(false);
  const [openDoiTuongTonThat, setOpenDoiTuongTonThat] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dataWebsiteTraCuu, setDataWebsiteTraCuu] = useState([]);

  useEffect(() => {
    getAllDanhMuc();
    getMaWebsiteTraCuu();
  }, []);

  const onInputFocus = () => {};

  const onPressLuu = async (data) => {
    setDialogLoading(true);
    try {
      const params = {
        so_id: profileData?.ho_so?.so_id, //id hồ sơ
        bt: itemSelected?.bt || '', //ID hóa đơn chứng từ - nếu thêm mới thì truyền 0, còn sửa thì phải truyền tham số này
        dvi_ph: data.dvPhatHanh, //Đơn vị phát hành
        ngay_ct: moment(data.ngayPhatHanh).format(FORMAT_DATE_TIME.API_DATE_FORMAT), //				Ngày phát hành
        mau_hdon: data.mauHoaDon, //			Mẫu hóa đơn
        ky_hieu_hdon: data.kyHieuHoaDon, //		Ký hiệu hóa đơn
        so_hdon: data.soHoaDon, //	Số hóa đơn
        ten_dvi_phat_hanh: data.tenDvPhatHanh, //		Tên đơn vị phát hành
        dchi_dvi_phat_hanh: data.diaChi, //		Địa chỉ đơn vị phát hành
        mst_dvi_phat_hanh: data.maSoThue, //			Mã số thuế đơn vị phát hành
        dien_giai: data.noiDung, //		Diễn giải thông tin hóa đơn
        tien: data.soTien, //		Số tiền chưa VAT
        tl_thue: data.tyLeThue, //			Tỷ lệ thuế
        thue: data.tienThue, //			Số tiền thuế
        ten_dvi_nhan: data.tenDvNhanHoaDon, //				Tên đơn vị nhận hóa đơn
        mst_dvi_nhan: data.MSTDvNhanHoaDon, //			Mã số thuế đơn vị nhận hóa đơn
        dchi_dvi_nhan: data.diaChiDvNhanHoaDon, //				'Địa chỉ đơn vị nhận hóa đơn'
        ma_tra_cuu: data.maTraCuuHoaDon, //		'Mã tra cứu hóa đơn'
        website_tra_cuu: data.webTraCuuHoaDon, //			'Website tra cứu hóa đơn'
        loai_thue: data.loaiThue,
        lh_nv: data.loaiHinhNghiepVu,
        so_id_doi_tuong: data.doiTuongTonThat,
        tien_giam: data.tienGiam, //
        ghi_chu_giam: data.ghiChuGiam, //
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_THONG_TIN_HOA_DON_CT, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (itemSelected) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Sửa hoá đơn/chứng từ thành công', 'success');
      } else FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm hoá đơn/chứng từ thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const layThongTinHoaDonCT = async (loai) => {
    setDialogLoading(true);
    try {
      const params = {
        so_id: profileData?.ho_so?.so_id,
        loai: loai,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_THONG_TIN_HOA_DON_CT, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info;
      onSetDefaultValue(data);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getAllDanhMuc = async () => {
    setDialogLoading(true);
    const params = {};
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_TAT_CA_DANH_MUC, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const dataDVPH = response.data_info.phat_hanh;
      setDataDvPhatHanh(dataDVPH);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getMaWebsiteTraCuu = async () => {
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        nv: 'XE',
        pm: 'BT',
        nv_ct: 'WEBSITE_HOA_DON',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_NHAN_XET_MAU, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info.noi_dung;
      data.map((e) => {
        e.label = e.noi_dung;
        e.value = e.noi_dung;
        return e;
      });
      setDataWebsiteTraCuu(data);
    } catch (error) {
      Alert.alert('Thông báo', error.message + '- line - 143');
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate, type) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const getDefaultFormValue = () => {
    let doiTuongTonThat = '';
    if (itemSelected) doiTuongTonThat = itemSelected.so_id_doi_tuong;
    if (profileData?.ds_doi_tuong?.length === 1) doiTuongTonThat = profileData?.ds_doi_tuong[0].so_id_doi_tuong;
    return {
      dvPhatHanh: itemSelected?.dvi_ph || '',
      tenDvPhatHanh: itemSelected?.ten_dvi_phat_hanh || '',
      maSoThue: itemSelected?.mst_dvi_phat_hanh || '',
      diaChi: itemSelected?.dchi_dvi_phat_hanh || '',
      ngayPhatHanh: itemSelected?.ngay_ct ? moment(itemSelected?.ngay_ct, 'DD/MM/YYYY').toDate() : new Date(),
      mauHoaDon: itemSelected?.mau_hdon || '',
      kyHieuHoaDon: itemSelected?.ky_hieu_hdon || '',
      soHoaDon: itemSelected?.so_hdon || '',
      noiDung: itemSelected?.dien_giai || '',
      soTien: itemSelected?.tien || '',
      tyLeThue: itemSelected?.tl_thue || '',
      loaiThue: itemSelected?.loai_thue || '',
      tienThue: itemSelected?.thue || '',
      tongCong: itemSelected?.tong_cong || '',
      tenDvNhanHoaDon: itemSelected?.ten_dvi_nhan || '',
      MSTDvNhanHoaDon: itemSelected?.mst_dvi_nhan || '',
      diaChiDvNhanHoaDon: itemSelected?.dchi_dvi_nhan || '',
      webTraCuuHoaDon: itemSelected?.website_tra_cuu || '',
      maTraCuuHoaDon: itemSelected?.ma_tra_cuu || '',
      loaiHinhNghiepVu: itemSelected?.lh_nv || '',
      doiTuongTonThat: doiTuongTonThat,
      tienGiam: itemSelected?.tien_giam || '',
      ghiChuGiam: itemSelected?.ghi_chu_giam || '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    // setError,
    getValues,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const webTraCuuHoaDon = watch('webTraCuuHoaDon');

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onSetDefaultValue = (data) => {
    if (data === null) return;
    if (data.ten) {
      setValue('tenDvPhatHanh', data.ten);
    }
    if (data.thue) {
      setValue('tienThue', data.thue);
    }
    if (data.tien) {
      setValue('soTien', data.tien);
    }
    if (data.tl_thue) {
      setValue('tyLeThue', +data.tl_thue);
    }
    if (data.ten_dvi_nhan) {
      setValue('tenDvNhanHoaDon', data.ten_dvi_nhan);
    }
    if (data.dien_giai) {
      setValue('noiDung', data.dien_giai);
    }
    if (data.mst_dvi_nhan) {
      setValue('MSTDvNhanHoaDon', data.mst_dvi_nhan);
    }
    if (data.mau_hdon) {
      setValue('mauHoaDon', data.mau_hdon);
    }
    if (data.ky_hieu_hdon) {
      setValue('kyHieuHoaDon', data.ky_hieu_hdon);
    }
    if (data.dia_chi) {
      setValue('diaChi', data.dia_chi);
    }
    if (data.dchi_dvi_nhan) {
      setValue('diaChiDvNhanHoaDon', data.dchi_dvi_nhan);
    }
  };

  const watchDVPhatHanh = watch('dvPhatHanh');
  const watchTyLeThue = watch('tyLeThue');
  const watchLoaiThue = watch('loaiThue');
  const watchSoTien = watch('soTien');
  const watchTienThue = watch('tienThue');
  const watchNgayPhatHanh = watch('ngayPhatHanh');

  useEffect(() => {
    if (watchDVPhatHanh && !itemSelected) {
      layThongTinHoaDonCT(watchDVPhatHanh);
    }
  }, [watchDVPhatHanh]);

  const triggerTinhTienThue = () => {
    let soTien = +watchSoTien;
    if (watchTyLeThue > 0 && watchSoTien > 0) {
      setValue('tienThue', ((watchTyLeThue * soTien) / 100).toFixed());
    } else if (watchTyLeThue == 0) {
      setValue('tienThue', 0);
    }
  };

  useEffect(() => {
    let tienThue = +watchTienThue;
    let soTien = +watchSoTien;
    if (tienThue > 0) {
      setValue('tongCong', (soTien + tienThue).toFixed());
    } else setValue('tongCong', soTien.toFixed());
  }, [watchTienThue]);

  const onChangeValueLoaiThue = (val) => {
    let loaiThue = val?.ma;
    let tyLeThue = val?.tl_thue;
    setValue('loaiThue', loaiThue, {shouldValidate: true});
    setValue('tyLeThue', tyLeThue);
    let soTien = +watchSoTien;
    if (tyLeThue > 0 && watchSoTien > 0) {
      setValue('tienThue', ((tyLeThue * soTien) / 100).toFixed());
    } else {
      setValue('tienThue', 0);
    }
  };

  const getDisplayName = (val, data) => {
    let displayText = '';
    data.map((e) => {
      if (e.ma === val) {
        displayText = e.ten;
      }
    });
    return displayText;
  };

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle={!itemSelected ? 'Thêm hoá đơn/chứng từ' : 'Sửa hoá đơn/chứng từ'}
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false} refreshControl={<RefreshControl refreshing={dialogLoading} />}>
            <View style={styles.content}>
              <Controller
                control={control}
                name="loaiHinhNghiepVu"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title="Loại hình nghiệp vụ"
                    zIndex={8000}
                    items={profileData?.lh_nv}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    isOpen={openLoaiHinhNghiepVu}
                    setOpen={setOpenLoaiHinhNghiepVu}
                    placeholder="Chọn loại hình nghiệp vụ"
                    schema={{
                      label: 'ten',
                      value: 'ma',
                    }}
                    inputErr={errors.loaiHinhNghiepVu && getErrMessage('loaiHinhNghiepVu', errors.loaiHinhNghiepVu.type)}
                    isRequired={true}
                    searchable={false}
                    // onChangeValue={onChangeDvPhatHanh}
                  />
                )}
              />
              <Controller
                control={control}
                name="doiTuongTonThat"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title="Đối tượng tổn thất"
                    zIndex={7000}
                    items={profileData?.ds_doi_tuong.filter((item) => item.kieu_dt === 'TT')}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    isOpen={openDoiTuongTonThat}
                    setOpen={setOpenDoiTuongTonThat}
                    placeholder="Chọn đối tượng"
                    schema={{
                      label: 'ten_doi_tuong',
                      value: 'so_id_doi_tuong',
                    }}
                    inputErr={errors.doiTuongTonThat && getErrMessage('doiTuongTonThat', errors.doiTuongTonThat.type)}
                    isRequired={true}
                    searchable={false}
                    // onChangeValue={onChangeDvPhatHanh}
                  />
                )}
              />
              <Controller
                control={control}
                name="dvPhatHanh"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title={'Đơn vị phát hành'}
                    zIndex={6000}
                    items={dataDvPhatHanh}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    isOpen={openDvPhatHanh}
                    setOpen={setOpenDvPhatHanh}
                    placeholder="Chọn đơn vị phát hành"
                    schema={{
                      label: 'ten',
                      value: 'ma',
                    }}
                    inputErr={errors.dvPhatHanh && getErrMessage('dvPhatHanh', errors.dvPhatHanh.type)}
                    isRequired={true}
                    searchable={false}
                    // onChangeValue={onChangeDvPhatHanh}
                  />
                )}
              />
              <Controller
                control={control}
                name="tenDvPhatHanh"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    onFocus={onInputFocus}
                    onChangeText={onChange}
                    title="Tên đơn vị phát hành"
                    placeholder="Nhập tên đv phát hành"
                    onSubmitEditing={() => maSoThueRef?.focus()}
                    error={errors.tenDvPhatHanh && getErrMessage('tenDvPhatHanh', errors.tenDvPhatHanh.type)}
                  />
                )}
              />

              <Controller
                control={control}
                name="maSoThue"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    title="Mã số thuế"
                    blurOnSubmit={false}
                    returnKeyType="next"
                    onFocus={onInputFocus}
                    onChangeText={onChange}
                    placeholder="Nhập mã số thuế"
                    getRef={(ref) => (maSoThueRef = ref)}
                    onSubmitEditing={() => diaChiRef?.focus()}
                    error={errors.maSoThue && getErrMessage('maSoThue', errors.maSoThue.type)}
                  />
                )}
              />

              <Controller
                control={control}
                name="diaChi"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    title="Địa chỉ"
                    isRequired={true}
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    onFocus={onInputFocus}
                    onChangeText={onChange}
                    placeholder="Nhập địa chỉ"
                    getRef={(ref) => (diaChiRef = ref)}
                    onSubmitEditing={() => mauHoaDonRef?.focus()}
                    error={errors.diaChi && getErrMessage('diaChi', errors.diaChi.type)}
                  />
                )}
              />

              <View style={styles.doubleInputRowView}>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="ngayPhatHanh"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        value={moment(value).format('DD/MM/YYYY')}
                        isDateTimeField
                        editable={false}
                        isRequired={true}
                        isTouchableOpacity
                        blurOnSubmit={false}
                        returnKeyType={'next'}
                        onFocus={onInputFocus}
                        title="Ngày phát hành"
                        inputStyle={{color: colors.BLACK_03}}
                        onPress={() => setToggleNgayPhatHanh(true)}
                        error={errors.ngayPhatHanh && getErrMessage('ngayPhatHanh', errors.ngayPhatHanh.type)}
                      />
                    )}
                  />
                  {renderDateTimeComp(
                    toggleNgayPhatHanh,
                    setToggleNgayPhatHanh,
                    (value) => {
                      setValue('ngayPhatHanh', value);
                    },
                    watchNgayPhatHanh,
                    'date',
                    null,
                    new Date(),
                    0,
                  )}
                </View>

                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="mauHoaDon"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        isRequired
                        maxLength={1}
                        value={value > 0 && value < 7 ? onChangeAlias(value).toLocaleUpperCase() : ''}
                        keyboardType="numeric"
                        title="Mẫu hoá đơn"
                        blurOnSubmit={false}
                        onFocus={onInputFocus}
                        returnKeyType={'next'}
                        onChangeText={onChange}
                        placeholder="Nhập mẫu hoá đơn"
                        getRef={(ref) => (mauHoaDonRef = ref)}
                        onSubmitEditing={() => kyHieuHoaDonRef?.focus()}
                        error={errors.mauHoaDon && getErrMessage('mauHoaDon', errors.mauHoaDon.type)}
                      />
                    )}
                  />
                </View>
              </View>
              <View style={styles.doubleInputRowView}>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="kyHieuHoaDon"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        isRequired
                        value={onChangeAlias(value).toLocaleUpperCase()}
                        blurOnSubmit={false}
                        returnKeyType={'next'}
                        onFocus={onInputFocus}
                        title="Ký hiệu hoá đơn"
                        onChangeText={onChange}
                        placeholder="Nhập ký hiệu hoá đơn"
                        getRef={(ref) => (kyHieuHoaDonRef = ref)}
                        onSubmitEditing={() => soHoaDonRef?.focus()}
                        error={errors.kyHieuHoaDon && getErrMessage('kyHieuHoaDon', errors.kyHieuHoaDon.type)}
                        maxLength={6}
                      />
                    )}
                  />
                </View>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="soHoaDon"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        isRequired
                        value={onChangeAlias(value).toLocaleUpperCase()}
                        title="Số hoá đơn"
                        blurOnSubmit={false}
                        onFocus={onInputFocus}
                        returnKeyType={'next'}
                        onChangeText={onChange}
                        placeholder="Nhập số hoá đơn"
                        getRef={(ref) => (soHoaDonRef = ref)}
                        onSubmitEditing={() => noiDungRef?.focus()}
                        error={errors.soHoaDon && getErrMessage('soHoaDon', errors.soHoaDon.type)}
                      />
                    )}
                  />
                </View>
              </View>
              <Controller
                control={control}
                name="noiDung"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    title="Nội dung"
                    isRequired={true}
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    onFocus={onInputFocus}
                    onChangeText={onChange}
                    placeholder="Nhập nội dung"
                    getRef={(ref) => (noiDungRef = ref)}
                    onSubmitEditing={() => soTienRef?.focus()}
                    error={errors.noiDung && getErrMessage('noiDung', errors.noiDung.type)}
                  />
                )}
              />
              <View style={styles.doubleInputRowView}>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="soTien"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        isRequired
                        value={value}
                        placeholder="0"
                        title="Số tiền"
                        blurOnSubmit={false}
                        returnKeyType={'next'}
                        keyboardType="numeric"
                        onChangeText={onChange}
                        onBlur={triggerTinhTienThue}
                        inputStyle={{textAlign: 'right'}}
                        getRef={(ref) => (soTienRef = ref)}
                        onSubmitEditing={() => tienThueRef?.focus()}
                        error={errors.soTien && getErrMessage('soTien', errors.soTien.type)}
                      />
                    )}
                  />
                </View>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="loaiThue"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        isDropdown
                        editable={false}
                        isRequired={true}
                        title="Loại thuế"
                        isTouchableOpacity
                        blurOnSubmit={false}
                        returnKeyType={'next'}
                        onFocus={onInputFocus}
                        onChangeText={onChange}
                        placeholder="Chọn loại thuế"
                        value={getDisplayName(value, DATA_LOAI_THUE)}
                        getRef={(ref) => (noiDungRef = ref)}
                        inputStyle={{color: colors.BLACK_03}}
                        onSubmitEditing={() => soTienRef?.focus()}
                        onPress={() => refModalLoaiThue.current.show()}
                        error={errors.loaiThue && getErrMessage('loaiThue', errors.loaiThue.type)}
                      />
                    )}
                  />
                </View>
              </View>

              <View style={styles.doubleInputRowView}>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="tienThue"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        isRequired
                        title="Thuế"
                        value={value}
                        blurOnSubmit={false}
                        returnKeyType={'next'}
                        onFocus={onInputFocus}
                        keyboardType="numeric"
                        onChangeText={onChange}
                        placeholder="0"
                        inputStyle={{textAlign: 'right'}}
                        getRef={(ref) => (tienThueRef = ref)}
                        onSubmitEditing={() => tenDvNhanHoaDonRef?.focus()}
                        error={errors.tienThue && getErrMessage('tienThue', errors.tienThue.type)}
                      />
                    )}
                  />
                </View>
                <View style={styles.doubleInputRow}>
                  <Controller
                    control={control}
                    name="tongCong"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <TextInputOutlined
                        isRequired
                        value={value}
                        disabled={true}
                        placeholder="0"
                        editable={false}
                        title="Tổng cộng"
                        blurOnSubmit={false}
                        onFocus={onInputFocus}
                        returnKeyType={'next'}
                        keyboardType="numeric"
                        onChangeText={onChange}
                        inputStyle={{textAlign: 'right'}}
                        getRef={(ref) => (tongCongRef = ref)}
                        onSubmitEditing={() => tenDvNhanHoaDonRef?.focus()}
                        error={errors.tongCong && getErrMessage('tongCong', errors.tongCong.type)}
                      />
                    )}
                  />
                </View>
              </View>
              {/* <Controller
                control={control}
                name="tienGiam"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    placeholder="0"
                    title="Tiền giảm"
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    keyboardType="numeric"
                    onChangeText={onChange}
                    editable={watchLoaiThue == '99' || watchLoaiThue == 'YY'}
                    disabled={watchLoaiThue != '99' && watchLoaiThue != 'YY'}
                    inputStyle={{textAlign: 'right'}}
                  />
                )}
              />
              <Controller
                control={control}
                name="ghiChuGiam"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    multiline
                    value={value}
                    title="Ghi chú giảm"
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    placeholder="Nhập ghi chú"
                    editable={watchLoaiThue == '99' || watchLoaiThue == 'YY'}
                    disabled={watchLoaiThue != '99' && watchLoaiThue != 'YY'}
                  />
                )}
              /> */}
              <Controller
                control={control}
                name="tenDvNhanHoaDon"
                rules={{
                  required: false,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    blurOnSubmit={false}
                    onFocus={onInputFocus}
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    title="Tên đơn vị nhận hóa đơn"
                    getRef={(ref) => (tenDvNhanHoaDonRef = ref)}
                    placeholder="Tên đơn vị nhận hóa đơn"
                    onSubmitEditing={() => maSoThueDvNhanHoaDonRef?.focus()}
                  />
                )}
              />
              <Controller
                control={control}
                name="MSTDvNhanHoaDon"
                rules={{
                  required: false,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    blurOnSubmit={false}
                    onFocus={onInputFocus}
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    title="MST đơn vị nhận hóa đơn"
                    getRef={(ref) => (maSoThueDvNhanHoaDonRef = ref)}
                    placeholder="Nhập MST đơn vị nhận hóa đơn"
                    onSubmitEditing={() => dcDvNhanHoaDonRef?.focus()}
                  />
                )}
              />
              <Controller
                control={control}
                name="diaChiDvNhanHoaDon"
                rules={{
                  required: false,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    blurOnSubmit={false}
                    onFocus={onInputFocus}
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    title="Địa chỉ đơn vị nhận hóa đơn"
                    getRef={(ref) => (dcDvNhanHoaDonRef = ref)}
                    placeholder="Địa chỉ đơn vị nhận hóa đơn"
                    onSubmitEditing={() => webTraCuuHoaDonRef?.focus()}
                  />
                )}
              />

              <Controller
                control={control}
                name="webTraCuuHoaDon"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    blurOnSubmit={false}
                    onFocus={onInputFocus}
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    title="Website tra cứu hóa đơn"
                    placeholder="Website tra cứu hóa đơn"
                    error={errors.webTraCuuHoaDon && getErrMessage('webTraCuuHoaDon', errors.webTraCuuHoaDon.type)}
                    subTitle="Chọn mẫu có sẵn"
                    onPressSubTitle={() => refModalWebsiteTraCuuHoaDon.current.show()}
                    hideBtnAdd
                  />
                )}
              />
              <View marginBottom={spacing.default}>
                <Controller
                  control={control}
                  name="maTraCuuHoaDon"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isRequired
                      value={value}
                      blurOnSubmit={false}
                      onFocus={onInputFocus}
                      returnKeyType={'next'}
                      onChangeText={onChange}
                      title="Mã tra cứu hoá đơn"
                      getRef={(ref) => (maTraCuuHoaDonRef = ref)}
                      placeholder="Mã tra cứu hoá đơn"
                      error={errors.maTraCuuHoaDon && getErrMessage('maTraCuuHoaDon', errors.maTraCuuHoaDon.type)}
                    />
                  )}
                />
              </View>
            </View>
          </KeyboardAwareScrollView>
          <ModalChonLoaiThue
            data={DATA_LOAI_THUE}
            ref={refModalLoaiThue}
            value={watchLoaiThue}
            setValue={(value) => onChangeValueLoaiThue(value)}
            onBackPress={() => refModalLoaiThue.current.hide()}
          />
          <ModalSelectSimple
            value={webTraCuuHoaDon}
            title={'Chọn website tra cứu hoá đơn'}
            ref={refModalWebsiteTraCuuHoaDon}
            baseData={dataWebsiteTraCuu}
            setValue={(val) => setValue('webTraCuuHoaDon', val.label, {shouldValidate: true})}
            onBackPress={() => refModalWebsiteTraCuuHoaDon.current.hide()}
            modalHeight={dimensions.height * 0.5}
          />
        </SafeAreaView>
      }
      footer={<ButtonLinear title="Lưu" onPress={handleSubmit(onPressLuu)} />}
    />
  );
};

export const ThemHoaDonChungTuScreen = memo(ThemHoaDonChungTuScreenComponent, isEqual);
