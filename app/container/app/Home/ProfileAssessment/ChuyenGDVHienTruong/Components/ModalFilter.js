import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {Empty, Icon, ModalOptions, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {FlatList, SafeAreaView, ScrollView, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {MONTHS} from './Constants';

const ModalFilterComponent = (props) => {
  const {toggleModalFilter, closeModal, listChiNhanh, paramsFilter, setParamsFilter, listGDV, listNam, dialogLoading, listChinNhanhBaoHiem, setListChiNhanhBaoHiem} = props;
  const [chiNhanhSelected, setChiNhanhSelected] = useState(['']);
  const [gdvSelected, setGDVSelected] = useState(['']);
  const [namSelected, setNamSelected] = useState(moment().year());
  const [thangSelected, setThangSelected] = useState(moment().month() + 1);
  const [toggleTuNgay, setToggleTuNgay] = useState(false);
  const [toggleDenNgay, setToggleDenNgay] = useState(false);

  const [toggleModalChiNhanh, setToggleModalChiNhanh] = useState(false);
  const [searchInput, setSearchInput] = useState('');
  const [timeoutId, setTimeoutId] = useState(null);

  const refModalChiNhanh = useRef(null);
  const refModalGDV = useRef(null);
  const refModalNam = useRef(null);
  const refModalThang = useRef(null);

  const insect = useSafeAreaInsets();
  const {
    control,
    formState: {errors},
    reset,
  } = useForm({
    defaultValues: {
      searchInput: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (listChinNhanhBaoHiem.length > 0) {
      let arrMaChiNhanhSelected = [];
      arrMaChiNhanhSelected = deQuyLayMaChiNhanhConSelected(arrMaChiNhanhSelected, listChinNhanhBaoHiem);
      setChiNhanhSelected([...arrMaChiNhanhSelected]);
    }
  }, [listChinNhanhBaoHiem]);
  useEffect(() => {
    setParamsFilter((prevValue) => {
      prevValue.ma_chi_nhanh = chiNhanhSelected;
      prevValue.ma_gdv = gdvSelected;
      return {...prevValue};
    });
  }, [chiNhanhSelected, gdvSelected]);

  useEffect(() => {
    if (listGDV.length > 0) setGDVSelected([listGDV[0].ma]);
  }, [listGDV]);

  const resetFilter = () => {
    setChiNhanhSelected(['']);
    setGDVSelected(['']);
    setNamSelected(moment().year());
    setThangSelected(moment().month() + 1);
    setParamsFilter((prevValue) => {
      prevValue.tu_ngay = moment().startOf('month');
      prevValue.den_ngay = moment();
      return {...prevValue};
    });
  };

  /**
   *
   * @param {*} arrMaChiNhanhSelected : một mảng các chi nhánh được selected
   * @param {*} listChinNhanhBaoHiem : mảng cần duyệt
   * @returns {*} arrMaChiNhanhSelected
   */
  const deQuyLayMaChiNhanhConSelected = (arrMaChiNhanhSelected, listChinNhanhBaoHiem) => {
    //nếu là chi nhánh con cuối cùng
    listChinNhanhBaoHiem.map((itemChiNhanhCon) => {
      //nếu itemChiNhanhCon là thằng con cuối cùng && itemChiNhanhCon được check && itemChiNhanhCon chưa có trong arrMaChiNhanhSelected -> push vào arrMaChiNhanhSelected
      if (itemChiNhanhCon.listCon.length === 0 && itemChiNhanhCon.isCheck && !arrMaChiNhanhSelected.includes(itemChiNhanhCon.ma_chi_nhanh)) arrMaChiNhanhSelected.push(itemChiNhanhCon.ma_chi_nhanh);
      //nếu có thằng con thì gọi đệ quy tiếp
      else if (itemChiNhanhCon.listCon.length > 0) {
        let allListConChecked = true;
        itemChiNhanhCon.listCon.forEach((itemChiNhanhCon2) => {
          if (!itemChiNhanhCon2.isCheck) allListConChecked = false;
          // if (itemChiNhanhCon2.isCheck && !arrMaChiNhanhSelected.includes(itemChiNhanhCon.ma_chi_nhanh)) arrMaChiNhanhSelected.push(itemChiNhanhCon.ma_chi_nhanh);
        });
        if (allListConChecked && !arrMaChiNhanhSelected.includes(itemChiNhanhCon.ma_chi_nhanh)) arrMaChiNhanhSelected.push(itemChiNhanhCon.ma_chi_nhanh);
        arrMaChiNhanhSelected = deQuyLayMaChiNhanhConSelected(arrMaChiNhanhSelected, itemChiNhanhCon.listCon);
      }
    });
    return arrMaChiNhanhSelected;
  };

  //EXPAND ITEM CHI NHÁNH CHA
  const onPressToggleExpandItemChiNhanh = (item, index, listData) => {
    //xử lý theo kiểu thay string :D
    try {
      let strDataRoot = JSON.stringify(listChinNhanhBaoHiem); //string list Tỷ lệ thương tật
      let strListDataBanDau = JSON.stringify(listData); //string listData lúc chưa đổi Trạng thái
      //thay đổi data
      listData.map((e, i) => {
        if (i === index) {
          e.isExpand = !e.isExpand;
          return e;
        }
        return e;
      });
      let strListDataLucSau = JSON.stringify(listData); //string listData press sau khi thay đổi Trạng thái
      strDataRoot = strDataRoot.replace(strListDataBanDau, strListDataLucSau); //thay đổi string listData
      setListChiNhanhBaoHiem(JSON.parse(strDataRoot)); //set lại DATA
    } catch (error) {
      console.log('error', error);
    }
  };

  //set checked value mới của Item Chi Nhánh Cha cho các item Chi nhánh con còn lại
  const deQuyCheckAllListConChiNhanh = (data, oldValue) => {
    try {
      data = data.map((item) => {
        item.isCheck = !oldValue;
        //set checked value mới của Item Chi Nhánh Cha cho các item Chi nhánh con còn lại
        if (item.listCon.length > 0) item.listCon = deQuyCheckAllListConChiNhanh(item.listCon, oldValue);
        return item;
      });
      return data;
    } catch (error) {
      console.log('deQuyCheckAllListConChiNhanh error', error);
    }
  };

  //nếu tất cả các chi nhanh con đều check TRUE -> check true chi nhánh cha
  const deQuyCheckTrueCacChiNhanhCha = (data) => {
    /* data : mảng các chi nhánh */
    try {
      data = data.map((itemChiNhanhCha) => {
        let coMotThangConCheckTrue = false;
        //nếu chi nhánh cha check true và có list  chi nhánh con (nghĩa là bỏ qua các thằng con cuối cùng)
        if (itemChiNhanhCha.listCon.length > 0) {
          itemChiNhanhCha.listCon.map((itemChiNhanhCon) => {
            if (itemChiNhanhCon.isCheck) coMotThangConCheckTrue = true; //nếu có 1 thằng con check true -> check true thằng cha
            if (itemChiNhanhCon.listCon.length > 0) {
              itemChiNhanhCon.listCon = deQuyCheckTrueCacChiNhanhCha(itemChiNhanhCon.listCon); //return về 1 mảng các chi nhánh
              //duyệt xem mảng các chi nhanh con có chi nhánh nào được check không
              itemChiNhanhCon.listCon.map((item) => {
                //nếu có check thì lưu biến check để check true cho thằng cha
                if (item.isCheck) {
                  coMotThangConCheckTrue = true;
                  itemChiNhanhCon.isCheck = true;
                }
              });
            }
            return itemChiNhanhCon;
          });
          if (coMotThangConCheckTrue) itemChiNhanhCha.isCheck = true; //nếu có 1 thằng con check true -> check true thằng cha
        }
        return itemChiNhanhCha;
      });
      /* data :return mảng các chi nhânhs */
      return data;
    } catch (error) {
      console.log('deQuyCheckTrueCacChiNhanhCha error', data, error);
    }
  };

  //check xem nếu tất cà các chi nhanh con đều check false -> check false chi nhanh cha
  const deQuyCheckFalseCacChiNhanhCha = (data) => {
    /* data : mảng các chi nhánh */
    try {
      data = data.map((itemChiNhanhCha) => {
        //nếu chi nhánh cha check true và có list  chi nhánh con (nghĩa là bỏ qua các thằng con cuối cùng)
        if (itemChiNhanhCha.listCon.length > 0) {
          let tatCaChiNhanhConCheckFalse = true;
          itemChiNhanhCha.listCon.map((itemChiNhanhCon) => {
            if (itemChiNhanhCon.listCon.length > 0) {
              itemChiNhanhCon.listCon = deQuyCheckFalseCacChiNhanhCha(itemChiNhanhCon.listCon); //return về 1 mảng các chi nhánh
              //duyệt xem mảng các chi nhanh con có chi nhánh nào được check không
              let isAllCheckFalse = true;
              itemChiNhanhCon.listCon.map((item) => {
                //nếu có check thì lưu biến check để check true cho thằng cha
                if (item.isCheck) {
                  tatCaChiNhanhConCheckFalse = false;
                  isAllCheckFalse = false;
                  // itemChiNhanhCon.isCheck = true;
                }
              });
              if (isAllCheckFalse) itemChiNhanhCon.isCheck = false;
            }
            if (itemChiNhanhCon.isCheck) tatCaChiNhanhConCheckFalse = false; //nếu có 1 thằng con check true -> check true thằng cha
            return itemChiNhanhCon;
          });
          if (tatCaChiNhanhConCheckFalse) itemChiNhanhCha.isCheck = false; //nếu có 1 thằng con check true -> check true thằng cha
        }

        return itemChiNhanhCha;
      });
      /* data :return mảng các chi nhânhs */
      return data;
    } catch (error) {
      console.log('deQuyCheckFalseCacChiNhanhCha error', data, error);
    }
  };

  /* CHECK VÀO 1 ITEM CHI NHÁNH */
  const onChangeCheckboxValueItemChiNhanh = (oldValue, item, index, listData) => {
    //xử lý theo kiểu thay string :D
    try {
      let strDataRoot = JSON.stringify(listChinNhanhBaoHiem); //string list Tỷ lệ thương tật
      let strListDataBanDau = JSON.stringify(listData); //string listData lúc chưa đổi Trạng thái
      //cập nhật giá trị check
      listData.map((e, i) => {
        if (i === index) {
          e.isCheck = !oldValue;
          //set checked value mới của Item Chi Nhánh Cha cho các item Chi nhánh con còn lại
          if (e.listCon.length > 0) e.listCon = deQuyCheckAllListConChiNhanh(e.listCon, oldValue);
          return e;
        }
        return e;
      });
      let strListDataLucSau = JSON.stringify(listData); //string listData press sau khi thay đổi Trạng thái

      strDataRoot = strDataRoot.replace(strListDataBanDau, strListDataLucSau); //thay đổi string listData
      let objData = JSON.parse(strDataRoot);
      if (oldValue) objData = deQuyCheckFalseCacChiNhanhCha(objData); //nếu giá trị được check là false
      if (!oldValue) objData = deQuyCheckTrueCacChiNhanhCha(objData); //nếu giá trị được check là true
      setListChiNhanhBaoHiem(objData); //set lại DATA
    } catch (error) {
      console.log('error', error);
    }
  };

  const onChangeTextSearch = (textSearchValue, onChange) => {
    onChange(textSearchValue);
    if (!textSearchValue.trim()) {
      clearTimeout(timeoutId);
      // setDataFilter(dataRoot.length > 100 ? cloneDeep(dataRoot).splice(0, 100) : dataRoot);
      return;
    }
    clearTimeout(timeoutId);
    let timeoutIdTmp = setTimeout(() => {
      let result = [];
      /*search by text*/
      // for (let i = 0; i < dataRoot.length; i++) {
      //   if (dataRoot[i].label.toUpperCase().indexOf(textSearchValue.toUpperCase()) > -1) result.push(dataRoot[i]);
      // }
      // setDataFilter([...result]);
    }, 500);
    setTimeoutId(timeoutIdTmp);
  };

  /* RENDER */
  const renderDropdownActionSheet = (title, placeholder, value, getTextByValue, onPressShowActionsheet, containerStyle, error, isRequired) => {
    return (
      <View style={[styles.mucDoTonThatView, containerStyle]}>
        <Text style={styles.txtMucDoTitle}>
          {title} {isRequired && <Text children="(*)" style={{color: colors.RED1}} />}
        </Text>
        <View style={[styles.dropdownView, error && {borderColor: colors.RED1}]}>
          <TouchableOpacity style={[styles.mucDoTitleView]} onPress={onPressShowActionsheet}>
            <Text children={value !== undefined ? getTextByValue(value) : placeholder} style={{flex: 1}} />
            <Icon.Entypo name="chevron-small-down" size={20} />
          </TouchableOpacity>
        </View>
        {error !== '' && <Text children={error} style={{color: colors.RED1, marginTop: spacing.tiny}} />}
      </View>
    );
  };
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        setToggleDateTime(false);
        setDateTime(dateSelected);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderTitleSearch = (text) => {
    let arrTextSearch = searchInput.split(' ').map((item) => item.toUpperCase());
    return (
      <>
        <Text style={{flex: 1, textAlign: 'justify'}}>
          {text.split(' ').map((item) => {
            let itemUppercase = item.toUpperCase();
            if (arrTextSearch.includes(itemUppercase))
              return (
                <>
                  <Text children={item} style={{backgroundColor: colors.ORANGE1}} />
                  <Text children=" " />
                </>
              );
            return <Text children={item + ' '} />;
          })}
        </Text>
      </>
    );
  };
  /* RENDER ITEM CHI NHÁNH */
  const renderItemChiNhanh = ({item, index}, expandData) => {
    if (!item.isShow) return;
    return (
      <View>
        {item.listCon.length > 0 && (
          <View style={styles.itemChiNhanhTitleView}>
            <TouchableOpacity onPress={() => onPressToggleExpandItemChiNhanh(item, index, expandData)} style={{paddingRight: spacing.small}}>
              <Icon.Feather name={!item.isExpand ? 'chevron-right' : 'chevron-down'} size={25} color={colors.BLACK} style={styles.iconBtnTopRightView} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => onChangeCheckboxValueItemChiNhanh(item.isCheck, item, index, expandData)} style={{flexDirection: 'row', flex: 1, alignItems: 'center'}}>
              {item.hasChildCheck && <Icon.FontAwesome name="square" color={colors.PRIMARY} size={20} />}
              {item.isCheck && <Icon.Ionicons name="checkbox" color={colors.PRIMARY} size={20} />}
              {!item.isCheck && <Icon.Ionicons name="square-outline" color={colors.BLACK} size={20} />}
              <View style={styles.txtView}>
                <Text style={[styles.txtTitle]}>{item.ten_chi_nhanh}</Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
        {/* {item.listCon.length > 0 && (
          <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressTitle(item, index, expandData)}>
            <Icon.Feather name={!item.isExpand ? 'chevron-right' : 'chevron-down'} size={20} color={colors.BLACK} style={styles.iconBtnTopRightView} />
            <View style={styles.txtView}>
              <Text style={styles.txtTitle}>{item.ten_chi_nhanh}</Text>
            </View>
          </TouchableOpacity>
        )} */}
        {item.listCon.length === 0 && (
          <TouchableOpacity style={styles.itemHangMucView} onPress={() => onChangeCheckboxValueItemChiNhanh(item.isCheck, item, index, expandData)}>
            {item.isCheck && <Icon.Ionicons name="checkbox" color={colors.PRIMARY} size={20} />}
            {!item.isCheck && <Icon.Ionicons name="square-outline" color={colors.BLACK} size={20} />}
            {searchInput === '' ? (
              <View style={styles.txtView}>
                <Text style={{flex: 1, textAlign: 'justify', fontSize: 13}}>{`${item.ten_chi_nhanh}`}</Text>
              </View>
            ) : (
              renderTitleSearch(item.ten_chi_nhanh)
            )}
          </TouchableOpacity>
        )}
        {item.listCon.length > 0 && item.isExpand && <View style={{marginLeft: spacing.none}}>{renderListChiNhanh(item.listCon)}</View>}
      </View>
    );
  };

  const renderListChiNhanh = (listData) => {
    return (
      <FlatList
        data={listData}
        renderItem={(itemData) => renderItemChiNhanh(itemData, listData)}
        style={styles.flStyles}
        keyExtractor={(item) => item.ma_chi_nhanh + item.ma_goc}
        ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
        scrollEnabled={false}
      />
    );
  };

  /* RENDER SEARCH CỦA MODAL CHI NHÁNH */
  const renderSearch = () => {
    return (
      <View style={styles.searchView}>
        <Controller
          control={control}
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <View style={styles.searchInput}>
              <View style={{flexDirection: 'row', alignItems: 'center', flex: 1}}>
                <Icon.AntDesign name="search1" size={20} color={'#CCC'} />
                <TextInput
                  value={value}
                  onChangeText={(newValue) => onChangeTextSearch(newValue, onChange)}
                  placeholder="Nhập nội dung tìm kiếm"
                  style={styles.txtSearchInput}
                  placeholderTextColor="#CCC"
                />
              </View>
              {value !== '' && (
                <TouchableOpacity
                  onPress={() => {
                    onChange('');
                    // setDataFilter(dataRoot.length > 100 ? cloneDeep(dataRoot).splice(0, 100) : dataRoot);
                  }}>
                  <Icon.AntDesign name="closecircle" size={20} color={'#CCC'} />
                </TouchableOpacity>
              )}
            </View>
          )}
          name="searchInput"
        />
      </View>
    );
  };

  /* MODAL OPTIONS */
  const renderModalOptions = () => (
    <>
      {/* MODAL CHI NHÁNH */}
      <ModalOptions
        ref={refModalChiNhanh}
        title="Chi nhánh"
        showSearch
        onOptionSelected={(value) => {
          setChiNhanhSelected((prevValue) => {
            //nếu chọn tất cả
            if (value === '') prevValue = [''];
            else {
              //nếu không phải chọn tất cả và option cũ có tất cả -> bỏ cái tất cả đi
              if (prevValue.findIndex((maChiNhanh) => maChiNhanh === '') > -1) prevValue.splice(0, 1);
              //nếu là bỏ check
              if (prevValue.includes(value) && prevValue.length > 1)
                prevValue.splice(
                  prevValue.findIndex((item) => item === value),
                  1,
                );
              //nếu là check mới
              else prevValue.push(value);
            }
            refModalChiNhanh.current.show(prevValue); //update lại list checked
            return [...prevValue];
          });
        }}
        dataRoot={listChiNhanh}
        multiple
        containerStyle={{height: dimensions.height * 0.4}}
        dialogLoading={dialogLoading}
      />

      {/* MODAL CHI NHÁNH 2 */}
      <Modal
        onSwipeComplete={() => setToggleModalChiNhanh(false)}
        isVisible={toggleModalChiNhanh}
        onBackdropPress={() => setToggleModalChiNhanh(false)}
        onBackButtonPress={() => setToggleModalChiNhanh(false)}
        style={[styles.modalChiNhanhBaoHiem]}>
        <View style={[styles.modalChiNhanhBaoHiemContent, {paddingBottom: insect.bottom}]}>
          <View style={styles.headerChiNhanHView}>
            <Icon.AntDesign name="closecircleo" size={20} color="#FFF" />
            <Text children="Chi nhánh" style={styles.txtChiNhanhTitle} />
            <TouchableOpacity onPress={() => setToggleModalChiNhanh(false)}>
              <Icon.AntDesign name="closecircleo" size={20} />
            </TouchableOpacity>
          </View>
          {/* {renderSearch()} */}
          <ScrollView>{renderListChiNhanh(listChinNhanhBaoHiem)}</ScrollView>
        </View>
      </Modal>

      {/* MODAL GIÁM ĐỊNH VIÊN */}
      <ModalOptions
        ref={refModalGDV}
        title="Giám định viên"
        showSearch
        onOptionSelected={(value) => {
          setGDVSelected((prevValue) => {
            //nếu chọn tất cả
            if (value === '') prevValue = [''];
            else {
              let isTonTai = prevValue.findIndex((maGDV) => maGDV === '') > -1;
              //nếu không phải chọn tất cả và option cũ có tất cả -> bỏ cái tất cả đi
              if (isTonTai) prevValue.splice(0, 1);
              //nếu là bỏ check
              if (prevValue.includes(value) && prevValue.length > 1)
                prevValue.splice(
                  prevValue.findIndex((item) => item === value),
                  1,
                );
              //nếu là check mới
              else prevValue.push(value);
            }
            refModalGDV.current.show(prevValue); //update lại list checked
            return [...prevValue];
          });
        }}
        dataRoot={listGDV}
        multiple
        containerStyle={{height: dimensions.height * 0.4}}
        dialogLoading={dialogLoading}
        avoidKeyboar={true}
      />

      {/* MODAL NĂM */}
      <ModalOptions
        ref={refModalNam}
        title="Năm"
        onOptionSelected={(value) => {
          setNamSelected(value);
          setParamsFilter((prevValue) => {
            prevValue.tu_ngay = moment(prevValue.tu_ngay).set('year', value).startOf('year');
            prevValue.den_ngay = moment(prevValue.den_ngay).set('year', value).endOf('year');
            return {...prevValue};
          });
        }}
        dataRoot={listNam}
        containerStyle={{height: dimensions.height * 0.4}}
      />

      {/* MODAL THÁNG */}
      <ModalOptions
        ref={refModalThang}
        title="Tháng"
        onOptionSelected={(value) => {
          setThangSelected(value);
          setParamsFilter((prevValue) => {
            prevValue.tu_ngay = moment(prevValue.tu_ngay)
              .set('year', namSelected)
              .set('month', value - 1)
              .startOf('month');
            prevValue.den_ngay = moment(prevValue.den_ngay)
              .set('year', namSelected)
              .set('month', value - 1)
              .endOf('month');
            return {...prevValue};
          });
        }}
        dataRoot={MONTHS}
        containerStyle={{height: dimensions.height * 0.7}}
      />
    </>
  );

  /* HEADER MODAL FILTEL */
  const renderHeaderModalFilter = () => (
    <View style={styles.headerView}>
      <TouchableOpacity onPress={closeModal}>
        <Icon.AntDesign name="arrowleft" size={25} />
      </TouchableOpacity>
      <Text children="Tìm kiếm" style={styles.txtHeader} />
      <TouchableOpacity onPress={resetFilter}>
        <Icon.AntDesign name="reload1" size={25} />
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      onSwipeComplete={closeModal}
      propagateSwipe={true}
      swipeDirection={['right']}
      isVisible={toggleModalFilter}
      animationIn="fadeInRight"
      animationOut="fadeOutRight"
      onBackdropPress={closeModal}
      onBackButtonPress={closeModal}
      style={styles.modal}>
      <SafeAreaView style={{flex: 1}}>
        <View style={styles.modalContent}>
          {renderHeaderModalFilter()}
          <View>
            {/* CHI NHÁNH */}
            {renderDropdownActionSheet(
              'Chi nhánh',
              'Chọn chi nhánh',
              chiNhanhSelected,
              (valueSelected) => {
                {
                  /* let txtDisplay = [];
                listChiNhanh.forEach((itemChiNhanh) => {
                  if (valueSelected.includes(itemChiNhanh.ma)) txtDisplay.push(itemChiNhanh.ten_tat);
                });
                return txtDisplay.join(', '); */
                }
                return `Đã có ${chiNhanhSelected.length} đơn vị được chọn`;
              },
              () => {
                //refModalChiNhanh.current.show(chiNhanhSelected); //MODAL CHI NHÁNH CŨ
                setToggleModalChiNhanh(true); // MODAL CHI NHÁNH MỚI
              },
              {marginTop: spacing.small, marginHorizontal: spacing.small},
              '',
              false,
            )}
            {/* GIÁM ĐỊNH VIÊN */}
            {renderDropdownActionSheet(
              'Giám định viên',
              'Chọn giám định viên',
              gdvSelected,
              (valueSelected) => {
                let txtDisplay = [];
                listGDV.forEach((itemGdv) => {
                  if (valueSelected.includes(itemGdv.ma)) txtDisplay.push(itemGdv.ten);
                });
                return txtDisplay.join(', ');
              },
              () => refModalGDV.current.show(gdvSelected),
              {marginTop: spacing.small, marginHorizontal: spacing.small},
              '',
              false,
            )}
            <View style={{flexDirection: 'row'}}>
              {/* NĂM */}
              {renderDropdownActionSheet(
                'Năm',
                'Chọn năm',
                namSelected,
                (valueSelected) => listNam.find((item) => item.value === valueSelected)?.label || '',
                () => refModalNam.current.show(namSelected),
                {marginTop: spacing.small, marginHorizontal: spacing.small, flex: 1},
                '',
                false,
              )}
              {/* THÁNG */}
              {renderDropdownActionSheet(
                'Tháng',
                'Chọn tháng',
                thangSelected,
                (valueSelected) => MONTHS.find((item) => item.value === valueSelected)?.label || '',
                () => refModalThang.current.show(thangSelected),
                {marginTop: spacing.small, marginRight: spacing.small, flex: 1},
                '',
                false,
              )}
            </View>

            <View style={{flexDirection: 'row'}}>
              <TextInputOutlined
                title="Từ ngày"
                isTouchableOpacity={true}
                onPress={() => setToggleTuNgay(true)}
                value={moment(paramsFilter.tu_ngay).format('DD/MM/YYYY')}
                editable={false}
                isDateTimeField
                containerStyle={{marginHorizontal: spacing.small, flex: 1}}
              />
              {renderDateTimeComp(
                toggleTuNgay,
                setToggleTuNgay,
                (value) => {
                  if (!moment(value).isAfter(paramsFilter.den_ngay))
                    setParamsFilter((prevValue) => {
                      prevValue.tu_ngay = value;
                      return {...prevValue};
                    });
                },
                moment(paramsFilter.tu_ngay).toDate(),
                'date',
                null,
                moment().toDate(),
              )}
              <TextInputOutlined
                title="Đến ngày"
                isTouchableOpacity={true}
                onPress={() => setToggleDenNgay(true)}
                value={moment(paramsFilter.den_ngay).format('DD/MM/YYYY')}
                editable={false}
                isDateTimeField
                containerStyle={{marginRight: spacing.small, flex: 1}}
              />
              {renderDateTimeComp(
                toggleDenNgay,
                setToggleDenNgay,
                (value) => {
                  if (!moment(value).isBefore(paramsFilter.tu_ngay))
                    setParamsFilter((prevValue) => {
                      prevValue.den_ngay = value;
                      return {...prevValue};
                    });
                },
                moment(paramsFilter.den_ngay).toDate(),
                'date',
                null,
                moment().toDate(),
              )}
            </View>
          </View>
        </View>
      </SafeAreaView>
      {renderModalOptions()}
    </Modal>
  );
};
const styles = StyleSheet.create({
  modal: {
    alignItems: 'flex-end',
    margin: 0,
  },
  modalChiNhanhBaoHiem: {
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalChiNhanhBaoHiemContent: {backgroundColor: '#FFF', width: dimensions.width, height: dimensions.height * 0.8, borderTopLeftRadius: 20, borderTopRightRadius: 20},
  modalContent: {
    height: dimensions.height,
    width: dimensions.width * 0.8,
    flex: 1,
    backgroundColor: '#FFF',
    paddingTop: 10,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 0.5,
    paddingBottom: 10,
    paddingHorizontal: 10,
    borderColor: 'gray',
  },
  txtHeader: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  mucDoTonThatView: {},
  txtMucDoTitle: {
    fontWeight: '700',
    marginBottom: spacing.tiny,
  },
  dropdownView: {
    borderWidth: 1,
    paddingHorizontal: spacing.small,
    borderColor: colors.GRAY,
    borderRadius: 10,
  },
  mucDoTitleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },

  searchInput: {
    // height: 40,
    // borderWidth: 1,
    // borderRadius: 5,
    // paddingLeft: 20,
    // marginHorizontal: 16,
    // borderColor: colors.GRAY,
    borderWidth: 1,
    borderColor: colors.GRAY,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: spacing.small,
    borderRadius: 5,
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {},
  itemHangMucView: {
    paddingVertical: spacing.tiny,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 36,
    // borderWidth: 1,
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginTop: spacing.tiny,
    marginRight: isIOS ? spacing.smaller : spacing.medium,
    width: 20,
    height: 20,
  },
  txtView: {
    flex: 1,
    // marginTop: !isIOS ? 4 : 2,
    flexDirection: 'row',
  },
  flStyles: {
    paddingLeft: spacing.small,
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  txtTitle: {
    // flex: 1,
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'justify',
  },
  txtValue: {
    flex: 1,
    fontSize: 12,
    textAlign: 'justify',
    color: colors.BLACK_03,
  },
  searchView: {
    marginHorizontal: spacing.small,
    marginVertical: spacing.small,
  },
  txtSearchInput: {
    paddingVertical: spacing.medium,
    paddingHorizontal: spacing.smaller,
    flex: 1,
  },
  headerChiNhanHView: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    justifyContent: 'space-between',
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.small,
    borderColor: colors.GRAY,
    alignItems: 'center',
  },
  txtChiNhanhTitle: {fontWeight: 'bold', fontSize: 16},
  itemChiNhanhTitleView: {
    flexDirection: 'row',
    flex: 1,
    marginBottom: spacing.tiny,
  },
});

export const ModalFilter = memo(ModalFilterComponent, isEqual);
