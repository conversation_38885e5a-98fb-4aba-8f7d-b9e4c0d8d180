import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, RefreshControl, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {ModalTimKiemDoiTuong, RenderContentModalThayDoiDoiTuong} from './Components';
import styles from './DoiDoiTuongStyles';
import {useSelector} from 'react-redux';
import {selectUser} from '@app/redux/slices/UserSlice';
import {CustomModal} from '@app/components/CustomModal';

const MA_CHI_NHANH_KHONG_AUTO_FILL = ['TCT', 'KV01', 'KV02'];
function DoiDoiTuongScreenComponent(props) {
  console.log('DoiDoiTuongScreenComponent');
  const userInfo = useSelector(selectUser);
  const {route} = props;
  const {profileInfo, profileData} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [loading, setLoading] = useState(false);
  const [isGHD, setIsGHD] = useState(false);
  const [data, setData] = useState([]);
  const [dataLuongXuLyTheoGCN, setDataLuongXuLyTheoGCN] = useState([]);

  let refModalTimKiemHopDongXe = useRef(null);
  let refModalConfirm = useRef(null);

  useEffect(() => {
    if ((profileInfo?.gdh === 'C' || profileInfo?.so_hd === 'HD_KHONG_XAC_DINH') && !MA_CHI_NHANH_KHONG_AUTO_FILL.includes(userInfo?.nguoi_dung?.ma_chi_nhanh)) setIsGHD(true);
  }, [profileInfo]);

  const onPressSearch = () => {
    refModalTimKiemHopDongXe.current.show();
  };

  const onPressItem = async (e) => {
    setSelectedItem(e);
    let params = {
      so_id_hd: e.so_id_hdong,
      so_id_gcn: e.so_id_gcn,
    };
    setDialogLoading(true);
    await tichHopLaiGCN(e);
    await getDataLuongXuLyTheoGCN(params);
    setDialogLoading(false);
  };

  const tichHopLaiGCN = async (giayChungNhan) => {
    try {
      setLoading(true);
      let params = {
        ma_doi_tac: axiosConfig.CONFIG_SERVER.MA_DOI_TAC,
        ma_chi_nhanh: giayChungNhan.ma_chi_nhanh,
        nv: giayChungNhan.nv || giayChungNhan?.nghiep_vu || '',
        so_id_hd: giayChungNhan.so_id_hdong,
        so_id_dt: giayChungNhan.so_id_gcn,
        so_gcn: giayChungNhan?.so_gcn,
        // tich_hop_lai: 'C',
      };
      let response = await PartnerEndpoint.xemGCNXeDoiTac(axiosConfig.ACTION_CODE.XEM_GCN_XE_DOI_TAC, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDataLuongXuLyTheoGCN = async (params) => {
    try {
      setLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DATA_LUONG_XU_LY_THEO_HD, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let luongXuLyRespone = response.data_info;
      luongXuLyRespone.map((e, i) => {
        luongXuLyRespone[i].label = e.ten;
        luongXuLyRespone[i].value = e.ma;
      });
      // if (luongXuLyRespone.length === 1) setLuongXly(luongXuLyRespone[0].ma);
      setDataLuongXuLyTheoGCN([...luongXuLyRespone]);
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lấy data luồng xử lý thành công ' + luongXuLyRespone.length, 'success');
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  // const doiDoiTuong = async () => {
  //   try {
  //     setLoading(true);
  //     let params = {
  //       ma_doi_tac: selectedItem.ma_doi_tac || userInfo.nguoi_dung.ma_doi_tac,
  //       so_id: profileInfo.so_id,
  //       nv: selectedItem.nghiep_vu || selectedItem.nv,
  //       so_id_hd: selectedItem.so_id_hdong,
  //       so_id_dt: selectedItem.so_id_gcn,
  //       nguon_api: 'MOBILE_PARTNER',
  //       // so_gcn: selectedItem.so_gcn,
  //       so_id_gcn: selectedItem.so_id_gcn,
  //       ma_chi_nhanh: selectedItem.ma_chi_nhanh,
  //       so_hdong: selectedItem.so_hdong,
  //     };
  //     let response = await CarClaimEndpoint.doiDoiTuong(axiosConfig.ACTION_CODE.THAY_DOI_DOI_TUONG, params);
  //     setLoading(false);
  //     if (!response || !response.state_info || response.state_info.status !== 'OK') return;
  //     FlashMessageHelper.showFlashMessage('Thông báo', 'Thay đổi đối tượng thành công', 'success');
  //     NavigationUtil.pop();
  //   } catch (error) {
  //     setLoading(false);
  //     Alert.alert('Thông báo', error.message);
  //   }
  // };

  /**RENDER  */
  const renderProfileItem = ({item, index}) => {
    let linearColors = [];
    if (selectedItem === item) {
      linearColors = [colors.WHITE1, colors.WHITE1];
    } else linearColors = [colors.WHITE, colors.WHITE];
    return (
      <TouchableOpacity key={index} onPress={() => onPressItem(item)}>
        <LinearGradient colors={linearColors} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.profileItemView, {borderColor: selectedItem === item ? colors.PRIMARY : colors.GRAY}]}>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số hợp đồng: <Text style={styles.content}>{item.so_hdong}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Biển số xe: <Text style={styles.content}>{item.bien_so_xe}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số giấy chứng nhận: <Text style={styles.content}>{item.so_gcn}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Nghiệp vụ: <Text style={styles.content}>{item.ten_loai_gcn || item.loai_gcn}</Text>{' '}
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Hiệu lực bh: <Text style={styles.content} children={item.ngay_hl_bh} /> -
              <Text style={styles.content} children={item.ngay_kt_bh} />
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Tên chủ xe: <Text style={styles.content}>{item.ten_chu_xe}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Đơn vị cấp đơn:<Text style={styles.content}>{item.ten_chi_nhanh}</Text>
            </Text>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Thay đổi đối tượng"
      renderView={
        <View style={styles.container}>
          <View flex={1}>
            <View flexDirection="row">
              <ButtonLinear onPress={onPressSearch} title="Tìm kiếm đối tượng" linearStyle={styles.btnSearch} />
            </View>
            <FlatList
              data={data}
              removeClippedSubviews={true}
              refreshControl={<RefreshControl refreshing={loading} />}
              keyExtractor={(item, index) => index.toString()}
              renderItem={renderProfileItem}
              ListEmptyComponent={
                <View style={styles.noDataView}>
                  <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
                  <Text>Chưa có dữ liệu</Text>
                </View>
              }
            />
            <ModalTimKiemDoiTuong
              isGHD={isGHD}
              ref={refModalTimKiemHopDongXe}
              setData={setData}
              loading={loading}
              setLoading={setLoading}
              onBackPress={() => refModalTimKiemHopDongXe.current.hide()}
              profileInfo={profileInfo}
            />
          </View>
          <View style={styles.footerView}>
            <ButtonLinear
              disabled={!selectedItem}
              textStyle={{color: !selectedItem ? colors.BLACK_03 : colors.WHITE}}
              linearColors={!selectedItem ? [colors.GRAY, colors.GRAY] : [colors.PRIMARY, colors.PRIMARY]}
              // onPress={doiDoiTuong}
              onPress={() => refModalConfirm.current.show()}
              title="Áp dụng"
            />
          </View>
          <CustomModal
            ref={refModalConfirm}
            renderContent={() => (
              <RenderContentModalThayDoiDoiTuong
                selectedItem={selectedItem}
                userInfo={userInfo}
                data={dataLuongXuLyTheoGCN}
                profileData={profileData}
                soIdHoSo={profileInfo?.so_id}
                onBackPress={() => {
                  refModalConfirm.current.hide();
                  setTimeout(() => NavigationUtil.pop(), 500);
                }}
                onCancel={() => {
                  refModalConfirm.current.hide();
                }}
              />
            )}
          />
        </View>
      }
    />
  );
}

export const DoiDoiTuongScreen = memo(DoiDoiTuongScreenComponent, isEqual);
