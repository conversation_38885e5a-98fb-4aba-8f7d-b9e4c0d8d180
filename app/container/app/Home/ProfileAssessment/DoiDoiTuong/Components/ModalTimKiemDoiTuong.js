import { colors } from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import { selectChiNhanhBaoHiemDangCay } from '@app/redux/slices/CategoryCommonSlice';
import { selectUser } from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import { BenThuBaEndPoint, PartnerEndpoint } from '@app/services/endPoints';
import { dimensions, FontSize, scale, spacing, vScale } from '@app/theme';
import { logErrorTryCatch } from '@app/utils';
import { replaceAllISO8859 } from '@app/utils/string';
import { ButtonLinear, CommonOutlinedTextFieldWithIcon, DialogLoading, Icon, ModalChiNhanhTheoDangPhang, ModalQuetQR, Text } from '@component';
import moment from 'moment';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Alert, SafeAreaView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { useSelector } from 'react-redux';

const currentDate = new Date();
const ModalTimKiemDoiTuongComponent = forwardRef((props, ref) => {
  const userInfo = useSelector(selectUser);
  const chiNhanhBaoHiem = useSelector(selectChiNhanhBaoHiemDangCay);

  const { setValue, onBackPress, setData, setLoading, isGHD, loading, profileInfo } = props;
  const [isVisible, setIsVisible] = useState(false);
  let refModalChiNhanh = useRef();
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const initFormInput = {
    ngay_xr: moment(currentDate).format('DD/MM/YYYY'),
    so_hdong: '',
    so_gcn: __DEV__ ? '2200222174' : '',
    so_may: '',
    bien_so_xe: __DEV__ ? '039395' : '',
    so_khung: '',
    ten_kh: '',
    cmt_kh: '',
    mst_kh: '',
    nv: 'XE',
    ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
    ma_chi_nhanh: '',
  };

  const titleInput = ['Ngày xảy ra', 'Số hợp đồng', 'Số GCN bảo hiểm', 'Biển số xe', 'Số khung', 'Số máy'];
  const [formInput, setFormInput] = useState(initFormInput);
  const [toggleNgayDau, setToggleNgayDau] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [ngayXayRa, setNgayXayRa] = useState(moment(currentDate).toDate());
  let refModalQuetQR = useRef(null);

  const getData = async () => {
    try {
      setLoading(true);
      let params = formInput;
      params.nv = profileInfo?.nghiep_vu || 'XE';
      let response = await PartnerEndpoint.searchGCN(axiosConfig.ACTION_CODE.TIM_KIEM_GCN_XE_DOI_TAC, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.hd;
      if (data.length > 0) {
        onBackPress && onBackPress();
      } else Alert.alert('Thông báo', 'Không tìm thấy kết quả phù hợp!');
      setData(data);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeText = async (field, value) => {
    setFormInput((prev) => ({ ...prev, [field]: value }));
  };

  useEffect(() => {
    if (ngayXayRa) {
      onChangeText('ngay_xr', moment(ngayXayRa).format('DD/MM/YYYY'));
    }
  }, [ngayXayRa]);

  const onSearchPress = () => {
    setValue && setValue(formInput);
    // onBackPress && onBackPress();
    getData();
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetField = () => {
    setFormInput(initFormInput);
    setNgayXayRa(moment(currentDate).toDate());
  };

  const getTenHienThi = (value, data) => {
    let name = '';
    data.map((item) => {
      if (value === item.ma_chi_nhanh) name = item.ten_chi_nhanh;
    });
    return name;
  };

  const memoDisableBtnSearch = useMemo(() => {
    let value = true;
    let totalCount = 0;
    if (formInput.bien_so_xe !== '') totalCount += 1;
    if (formInput.so_gcn !== '') totalCount += 1;
    if (formInput.so_khung !== '') totalCount += 1;
    if (formInput.so_may !== '') totalCount += 1;
    if (totalCount >= 2 && formInput.ma_chi_nhanh !== '') value = false;
    if (!isGHD && totalCount >= 1) value = false;
    return value;
  }, [formInput]);

  const handleQRData = async (data) => {
    try {
      if (!data.includes('https://hotro.pjico.com.vn/')) {
        return setTimeout(() => {
          Alert.alert('Thông báo', 'Mã QR không phù hợp với Giấy chứng nhận');
        }, 350);
      }
      setDialogLoading(true);
      let response = await BenThuBaEndPoint.readUrlQrGcnPJICO(data);
      setDialogLoading(false);
      let arrInputResult = response.split('input-result">');
      let arrLabel = response.split('control-label">');
      arrInputResult = arrInputResult.map((item) => item.split('</label>')[0]);
      arrLabel = arrLabel.map((item) => item.split('</label>')[0]).filter((item) => item !== 'Tình trạng');
      if (arrInputResult.length <= arrLabel.length) {
        for (let i = 0; i < arrInputResult.length; i++) {
          if (arrLabel[i] === 'Số đơn BH') {
            let arrSoGCN = arrInputResult[i].split('/');
            let soGCN = '';
            /*xử lý trường hợp nối dài số GCN + số Sửa đổi bổ sung : P-23/HPH/XCG/5106/001316E-23/HPH/XCG/5106/001316-01
            số GCN dúng chỉ đến P-23/HPH/XCG/5106/001316
            */
            if (arrSoGCN.length > 5) {
              let indexSlice = 0;
              for (let j = 0; j < arrSoGCN[4].length; j++) {
                if (arrSoGCN[4][j] > '9') {
                  indexSlice = j;
                  break;
                }
              }
              soGCN = arrSoGCN.slice(0, 4).join('/') + '/' + arrSoGCN[4].slice(0, indexSlice);
            } else soGCN = arrInputResult[i];
            onChangeText('so_gcn', soGCN);
          } else if (arrLabel[i] === 'Tên khách hàng') onChangeText('ten_kh', replaceAllISO8859(arrInputResult[i]).toUpperCase());
          else if (arrLabel[i] === 'Biển kiểm soát') onChangeText('bien_so_xe', arrInputResult[i].length > 2 ? arrInputResult[i] : '');
          else if (arrLabel[i] === 'Số khung') onChangeText('so_khung', arrInputResult[i].length > 2 ? arrInputResult[i] : '');
          else if (arrLabel[i] === 'Số máy') onChangeText('so_may', arrInputResult[i].length > 2 ? arrInputResult[i] : '');
        }
      }
    } catch (error) {
      logErrorTryCatch({ code: 'QUET_QR_GCN_ERROR', message: data });
      setTimeout(() => {
        Alert.alert('Thông báo', error.message);
      }, 350);
    }
  };

  /**RENDER  */
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  /* RENDER */
  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.formInput}>
          {/* Ngày đầu/Ngày cuối */}
          <View>
            <CommonOutlinedTextFieldWithIcon
              isRequired
              isDateTimeField
              editable={true}
              disabled={false}
              placeholder={titleInput[0]}
              styleContainer={styles.containerInput}
              onPressInput={() => setToggleNgayDau(true)}
              value={moment(ngayXayRa).format('DD/MM/YYYY')}
            />
            {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, setNgayXayRa, ngayXayRa, 'date', null, new Date(), 0)}
          </View>
          <CommonOutlinedTextFieldWithIcon
            isRequired={isGHD}
            editable={true}
            disabled={false}
            value={getTenHienThi(formInput.ma_chi_nhanh, chiNhanhBaoHiem)}
            placeholder="Chi nhánh"
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_hdong', text)}
            isPickerModal
            onPressInput={() => refModalChiNhanh.current.show(true)}
          />
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.so_hdong}
            placeholder={titleInput[1]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_hdong', text)}
          // error={props.inputErrLienHe[0]}
          />

          <View flexDirection="row">
            <CommonOutlinedTextFieldWithIcon
              editable={true}
              disabled={false}
              value={formInput.so_gcn}
              placeholder={titleInput[2]}
              styleContainer={[styles.containerInput]}
              onChangeText={(text) => onChangeText('so_gcn', text)}
            />
          </View>
          <CommonOutlinedTextFieldWithIcon
            keyboardType="default"
            value={formInput.bien_so_xe}
            placeholder={titleInput[3]}
            onChangeText={(text) => onChangeText('bien_so_xe', text)}
            disabled={false}
            editable={true}
            styleContainer={styles.containerInput}
          />
          <View>
            <CommonOutlinedTextFieldWithIcon
              value={formInput.so_khung}
              keyboardType="default"
              placeholder={titleInput[4]}
              styleContainer={[styles.containerInput]}
              onChangeText={(text) => onChangeText('so_khung', text)}
            />
            <CommonOutlinedTextFieldWithIcon
              disabled={false}
              editable={true}
              placeholder={titleInput[5]}
              value={formInput.so_may}
              styleContainer={styles.containerInput}
              onChangeText={(text) => onChangeText('so_may', text)}
            />
          </View>

          {isGHD ? (
            <View style={styles.warningText}>
              <Icon.Ionicons name="warning" color={memoDisableBtnSearch ? colors.ORANGE : colors.GREEN} size={20} />
              <Text
                flex={1}
                children="Vui lòng chọn chi nhánh và nhập ít nhất 2 trường trong các trường thông tin (Biển số xe, Số GCN bảo hiểm, Số khung, Số máy)"
                color={memoDisableBtnSearch ? colors.ORANGE : colors.GREEN}
              />
            </View>
          ) : (
            <View style={styles.warningText}>
              <Icon.Ionicons name="warning" color={memoDisableBtnSearch ? colors.ORANGE : colors.GREEN} size={20} />
              <Text flex={1} children="Nhập ít nhất 1 trường trong các trường thông tin (Biển số xe, Số GCN bảo hiểm, Số khung, Số máy)" color={memoDisableBtnSearch ? colors.ORANGE : colors.GREEN} />
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <Modal
      // statusBarTranslucent={true}
      onBackButtonPress={onBackPress}
      animationIn="fadeInRight"
      animationOut="fadeOutRight"
      isVisible={isVisible}
      onModalWillShow={() => resetField()}
      style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal title="Tìm kiếm đối tượng"
          onBackPress={onBackPress}
          rightComponent={
            <TouchableOpacity onPress={() => refModalQuetQR.current?.show()} style={styles.qrGCNView}>
              <Text children="GCN" color={colors.PRIMARY} style={styles.txtGCN} />
              <Icon.Ionicons name="qr-code" size={20} color={colors.PRIMARY} />
            </TouchableOpacity>
          } />
        <KeyboardAwareScrollView>{renderFormInput()}</KeyboardAwareScrollView>
        <View style={styles.footer}>
          <ButtonLinear textStyle={{ color: colors.BLACK_03 }} linearColors={[colors.GRAY2, colors.GRAY2]} onPress={resetField} linearStyle={styles.btnLoginView} title="Nhập lại" />
          <ButtonLinear loading={loading} onPress={onSearchPress} linearStyle={styles.btnLoginView} disabled={memoDisableBtnSearch} title="Tìm kiếm" isSubBtn={memoDisableBtnSearch} />
        </View>
        <ModalChiNhanhTheoDangPhang
          baseData={chiNhanhBaoHiem}
          value={formInput.ma_chi_nhanh}
          ref={refModalChiNhanh}
          setValue={(val) => onChangeText('ma_chi_nhanh', val.ma_chi_nhanh)}
          onBackPress={() => refModalChiNhanh.current?.hide()}
        />
      </SafeAreaView>
      <ModalQuetQR ref={refModalQuetQR} handleData={handleQRData} />
      {dialogLoading && <DialogLoading />}
    </Modal>
  );
});

export const ModalTimKiemDoiTuong = memo(ModalTimKiemDoiTuongComponent, isEqual);

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
  },
  modalView: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  formInput: {
    marginHorizontal: scale(10),
    marginBottom: spacing.medium,
  },
  btnLoginView: {
    marginHorizontal: scale(8),
  },
  title: {
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: scale(10),
    fontSize: FontSize.size18,
  },
  containerInput: {
    height: 45,
    flex: 1,
  },
  footer: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    paddingHorizontal: spacing.small,
    paddingTop: vScale(10),
    borderTopColor: colors.GRAY2,
    backgroundColor: colors.WHITE,
  },
  warningText: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textDetail: {
    marginLeft: spacing.small,
    marginVertical: spacing.tiny,
  },
  qrGCNView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    paddingVertical: 2,
    paddingHorizontal: spacing.smaller,
    borderRadius: 8,
    borderColor: colors.PRIMARY,
  },
  txtGCN: {
    marginRight: spacing.tiny,
  },
});
