import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Text} from '@component';
import React, {forwardRef, memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

const BORDER_RADIUS = 8;
interface Props {
  profileData: {
    ho_so: {
      ma_doi_tac: string;
      so_id: string;
      ma_gdvht: string;
      ten_gdvht?: string;
    };
  };
  onBackPress?: () => void;
  onCancel?: () => void;
  data?: [];
  selectedItem?: {
    ma_doi_tac?: string;
    so_id_hdong?: string;
    so_id_gcn?: string;
    nghiep_vu?: string;
    ma_chi_nhanh?: string;
    so_hdong?: string;
  };
  userInfo: {
    nguoi_dung: {
      ma_doi_tac: string;
      ma_chi_nhanh: string;
    };
  };
  soIdHoSo: string;
}

const RenderContentModalThayDoiDoiTuongComponent = forwardRef((props: Props, ref) => {
  const {profileData, onBackPress, onCancel, data, selectedItem, userInfo, soIdHoSo} = props;
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      chiNhanh: profileData?.ho_so ? profileData.ho_so?.ma_chi_nhanh : '',
      ghiChu: '',
      giamDinhVien: profileData?.ho_so ? profileData.ho_so?.ten_gdvht : '',
      maChiNhanh: '',
      luongXuLy: '',
    },
    mode: 'onChange',
  });

  const doiDoiTuong = async (data) => {
    try {
      const profileInfo = profileData?.ho_so;
      setIsSubmiting(true);
      let params = {
        ma_doi_tac: selectedItem.ma_doi_tac || userInfo.nguoi_dung.ma_doi_tac,
        so_id: soIdHoSo,
        nv: selectedItem.nghiep_vu || selectedItem.nv,
        so_id_hd: selectedItem.so_id_hdong,
        so_id_dt: selectedItem.so_id_gcn,
        nguon_api: 'MOBILE_PARTNER',
        // so_gcn: selectedItem.so_gcn,
        so_id_gcn: selectedItem.so_id_gcn,
        ma_chi_nhanh: selectedItem.ma_chi_nhanh,
        so_hdong: selectedItem.so_hdong,
        nv_xly: data.luongXuLy,
      };
      let response = await CarClaimEndpoint.doiDoiTuong(axiosConfig.ACTION_CODE.THAY_DOI_DOI_TUONG, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Thay đổi đối tượng thành công', 'success');
      // NavigationUti.pop();
      onBackPress && onBackPress();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  /* RENDER */

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Đổi đối tượng
        </Text>
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={false}>
        <Controller

          control={control}
          name="luongXuLy"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              zIndex={9000}
              isRequired={true}
              searchable={false}
              items={data}
              itemSelected={value}
              isOpen={isOpenDropdown}
              setOpen={setIsOpenDropdown}
              title="Luồng xử lý"
              placeholder="Chọn luồng xử lý"
              setItemSelected={(dispatch) => onChange(dispatch())}
              inputErr={errors.luongXuLy && getErrMessage('luongXuLy', errors.luongXuLy.type)}
              containerStyle={{minHeight: 200}}
            />
          )}
        />
        {/* <Controller
          control={control}
          name="ghiChu"
          rules={{
            required: true,
          }}
          render={({ field: { onChange, value } }) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              isRequired
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              inputStyle={{ maxHeight: 80 }}
              containerStyle={{ zIndex: -1 }}
              error={errors.ghiChu && getErrMessage('ghiChu', errors.ghiChu.type)}
            />
          )}
        /> */}
      </KeyboardAwareScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={() => onCancel && onCancel()} title="Để sau" linearStyle={{marginRight: 10}} isSubBtn />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(doiDoiTuong)} title="Xác nhận" />
      </View>
    </View>
  );
});
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
    height: dimensions.height * 0.5,
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.smaller,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalThayDoiDoiTuong = memo(RenderContentModalThayDoiDoiTuongComponent, isEqual);
