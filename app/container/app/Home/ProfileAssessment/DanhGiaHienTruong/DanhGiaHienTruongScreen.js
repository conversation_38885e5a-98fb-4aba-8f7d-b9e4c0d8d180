import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, ScreenComponent, Text, TextInputOutlined} from '@component';
import {DATA_CONSTANT} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, ScrollView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import RadioGroup from 'react-native-radio-buttons-group';
import styles from './DanhGiaHienTruongStyles';
import ModalKetThucDanhGia from './Modal/ModalKetThucDanhGia';

const DanhGiaHienTruongScreenComponent = ({route}) => {
  console.log('DanhGiaHienTruongScreen');
  const [profileData, setProfileData] = useState(route.params.profileData);
  const [inputErr, setInputErr] = useState([]);
  const [dataFormDanhGiaHienTruong, setDataFormDanhGiaHienTruong] = useState([]);
  const [danhGiaHienTruong, setDanhGiaHienTruong] = useState(route.params.danhGiaHienTruong);

  //dữ liệu của modal thông báo thành công
  const [toggleModalSuccess, setToggleModalSuccess] = useState(false);

  useEffect(() => {
    setProfileData(route.params.profileData);
    getDataFormDanhGiaHienTruong();
  }, [route.params]);

  //sau khi có dữ liệu đánh giá hiện trường - thì điền data vào form đánh giá
  const dienDataVaoForm = (dataFormDanhGiaHienTruong) => {
    if (dataFormDanhGiaHienTruong.length > 0 && danhGiaHienTruong.length > 0) {
      let dataFormDanhGiaHienTruongTmp = dataFormDanhGiaHienTruong;
      dataFormDanhGiaHienTruongTmp.map((itemForm) => {
        for (let i = 0; i < danhGiaHienTruong.length; i++) {
          if (itemForm.loai === danhGiaHienTruong[i].loai) {
            if (itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
              itemForm.value = danhGiaHienTruong[i].noi_dung;
              break;
            } else if (itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
              itemForm.radioData.map((itemRadio) => {
                if (itemRadio.ma === danhGiaHienTruong[i].noi_dung) itemRadio.selected = true;
                else if (itemForm.bat_buoc_nhap === 1) itemRadio.selected = false;
                return itemRadio;
              });
              break;
            } else if (itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
              let giaTriCheckbox = danhGiaHienTruong[i].noi_dung.split(',');
              itemForm.checkBoxData.map((itemCheckbox) => {
                for (let j = 0; j < giaTriCheckbox.length; j++) if (itemCheckbox.ma === giaTriCheckbox[j]) itemCheckbox.checked = true;
                return itemCheckbox;
              });
              break;
            }
          }
        }
        return itemForm;
      });
      setInputErr(new Array(dataFormDanhGiaHienTruongTmp.length).fill(''));
      setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
    }
  };

  const onPressHoanThanhGiamDinhHienTruong = async () => {
    let params = {
      so_id: profileData.ho_so.so_id,
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      create_file: 'ESCS_BBGD_HIEN_TRUONG',
      remove_file: 'ESCS_BBGD_HIEN_TRUONG',
    };

    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.KET_THUC_CHUP_ANH_DANH_GIA_HIEN_TRUONG, params);
      setToggleModalSuccess(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setToggleModalSuccess(false);
      FlashMessageHelper.showFlashMessage('Thông báo', 'Nộp đánh giá hiện trường thành công', 'success');
      setTimeout(() => (route.params.prevScreen ? NavigationUtil.pop(2) : NavigationUtil.pop()), 250);
    } catch (error) {
      setToggleModalSuccess(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  //lấy dữ liệu để hiển thị form đánh giá hiện trường động
  const getDataFormDanhGiaHienTruong = async () => {
    let paramsFormDanhGia = {
      ma_doi_tac: route.params.profileData.ho_so.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.GET_FORM_DANH_GIA_HIEN_TRUONG, paramsFormDanhGia);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let formDanhGiaHienTruongInput = response.data_info.loai;
      let formDanhGiaHienTruongGiaTri = response.data_info.lke;
      formDanhGiaHienTruongInput.map((item) => {
        if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
          item.value = ''; //giá trị input thay đổi
          item.multiline = true;
          item.numberOfLines = 2;
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
          item.radioData = [];
          //filter lấy giá trị theo loai
          let giaTriRadio = formDanhGiaHienTruongGiaTri.filter((itemGiaTri) => itemGiaTri.loai === item.loai);
          //convert dữ liệu để hiển thị radio
          giaTriRadio.map((itemRadio) => {
            itemRadio.id = itemRadio.dg_stt;
            itemRadio.label = itemRadio.ten;
            itemRadio.value = itemRadio.ma;
            itemRadio.containerStyle = {
              marginRight: 0,
            };
            itemRadio.selected = false;
            itemRadio.size = 18;
            return itemRadio;
          });
          //nếu radio chưa được selectec giá trị và radio đấy bắt buộc nhập -> chọn 1 giá trị mặc định cho nó
          if (item.bat_buoc_nhap === 1) giaTriRadio[0].selected = true;
          item.radioData = giaTriRadio;
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
          item.checkBoxData = [];
          //filter lấy giá trị theo loai
          let checkBoxData = formDanhGiaHienTruongGiaTri.filter((itemGiaTri) => itemGiaTri.loai === item.loai);
          //convert dữ liệu để hiển thị checkbox
          checkBoxData.map((itemCheckbox) => {
            itemCheckbox.checked = false;
            return itemCheckbox;
          });
          item.checkBoxData = checkBoxData;
        }
      });
      setDataFormDanhGiaHienTruong(formDanhGiaHienTruongInput);
      dienDataVaoForm(formDanhGiaHienTruongInput);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //lấy dữ liệu đã đánh giá hiện trường
  const getDataDanhGiaHienTruong = async () => {
    let paramDanhGia = {
      ma_doi_tac: route.params.profileData.ho_so.ma_doi_tac,
      so_id: route.params.profileData.ho_so.so_id,
    };

    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DANH_GIA_HIEN_TRUONG, paramDanhGia);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDanhGiaHienTruong(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //đánh giá hiện trường
  const onPressSave = async () => {
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        data_loai: [],
        data_ten_loai: [],
        data_noi_dung: [],
      };
      let haveErr = false;
      let inputErrTmp = inputErr;
      dataFormDanhGiaHienTruong.map((item, index) => {
        if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
          if (item.bat_buoc_nhap === 1 && (!item.value || !item.value?.trim())) {
            inputErrTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          } else if (item.value) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            let value = '';
            if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA) value = item.value;
            else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
              if (item.value.includes(',')) value = +item.value.split(',').join('');
              else value = +item.value;
            }
            params.data_noi_dung.push(value);
          }
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
          item.radioData.map((itemRadio) => {
            if (itemRadio.selected) {
              params.data_noi_dung.push(itemRadio.ma);
              params.data_loai.push(item.loai);
              params.data_ten_loai.push(item.ten_loai);
            }
          });
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
          let coChecked = false,
            data_noi_dung = '';
          item.checkBoxData.map((itemCheckbox) => {
            if (itemCheckbox.checked) {
              coChecked = true;
              data_noi_dung = data_noi_dung + itemCheckbox.ma + ',';
            }
          });
          if (coChecked) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            params.data_noi_dung.push(data_noi_dung);
          }
          if (!coChecked && item.bat_buoc_nhap) {
            inputErrTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          }
        }
      });
      if (haveErr) {
        setInputErr([...inputErrTmp]);
        return;
      }
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.DANH_GIA_HIEN_TRUONG, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', danhGiaHienTruong ? 'Cập nhật thành công' : 'Đánh giá hiện trường thành công', 'success');
      setToggleModalSuccess(true);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeTxtInput = (index, value) => {
    let dataFormDanhGiaHienTruongTmp = dataFormDanhGiaHienTruong;
    let inputErrTmp = inputErr;
    if (!value.trim() && dataFormDanhGiaHienTruongTmp[index].bat_buoc_nhap === 1) inputErrTmp[index] = 'Thông tin bắt buộc';
    else if (value.trim()) inputErrTmp[index] = '';
    setInputErr([...inputErrTmp]);
    dataFormDanhGiaHienTruongTmp[index].value = value;
    setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
  };

  /**RENDER  */
  const renderRadioInput = (title, radioButtons, onPressRadioButton, isRequired) => {
    let radioButtonsTmp = cloneObject(radioButtons);
    if (radioButtons.length === 2) radioButtonsTmp.map((item) => (item.containerStyle.flex = 1));
    return (
      <View style={{marginVertical: spacing.tiny}}>
        <Text style={styles.inputTitle}>
          {title} {isRequired && <Text children="(*)" style={{color: colors.RED1}} />}
        </Text>
        <ScrollView horizontal>
          <RadioGroup radioButtons={radioButtonsTmp} onPress={onPressRadioButton} layout={'row'} />
        </ScrollView>
      </View>
    );
  };
  const renderItemCheckbox = (item, extraData) => {
    return (
      <View style={styles.checkboxView}>
        <Text children={item.ten} />
        <CheckboxComp value={item.checked} onValueChange={extraData.onChangeValue} checkboxStyle={{marginLeft: spacing.tiny}} />
      </View>
    );
  };
  const renderItemFormDanhGia = (data) => {
    let itemDanhGia = data.item;
    let dataFormDanhGiaHienTruongTmp = dataFormDanhGiaHienTruong;
    if (itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || itemDanhGia.kieu == DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER)
      return (
        <TextInputOutlined
          title={itemDanhGia.ten_loai === 'Xác định trọng tải' ? itemDanhGia.ten_loai + ' (đơn vị: KG)' : itemDanhGia.ten_loai}
          value={itemDanhGia.value}
          placeholder={itemDanhGia.ten_loai === 'Xác định trọng tải' ? itemDanhGia.ten_loai + ' (đơn vị: KG)' : itemDanhGia.ten_loai}
          keyboardType={itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA ? '' : 'numeric'}
          multiline={itemDanhGia.multiline}
          numberOfLines={itemDanhGia.numberOfLines}
          error={inputErr[data.index]}
          isRequired={itemDanhGia.bat_buoc_nhap === 1 ? true : false}
          onChangeText={(value) => onChangeTxtInput(data.index, value)}
        />
      );
    else if (itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
      return renderRadioInput(
        itemDanhGia.ten_loai,
        itemDanhGia.radioData,
        (value) => {
          itemDanhGia.radioData = value;
          dataFormDanhGiaHienTruongTmp[data.index] = itemDanhGia;
          setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
        },
        itemDanhGia.bat_buoc_nhap === 0 ? false : true,
      );
    } else if (itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
      return (
        <View style={{marginBottom: spacing.small}}>
          <Text style={[styles.inputTitle, {marginBottom: spacing.tiny}]}>
            {itemDanhGia.ten_loai} {itemDanhGia.bat_buoc_nhap && <Text children="(*)" style={{color: colors.RED1}} />}
          </Text>
          <FlatList
            data={itemDanhGia.checkBoxData}
            keyExtractor={(itemCheckbox) => itemCheckbox.ma}
            renderItem={({item, index}) =>
              renderItemCheckbox(item, {
                onChangeValue: (value) => {
                  itemDanhGia.checkBoxData[index].checked = value;
                  dataFormDanhGiaHienTruongTmp[data.index] = itemDanhGia;
                  setDataFormDanhGiaHienTruong([...dataFormDanhGiaHienTruongTmp]);
                  if (itemDanhGia.bat_buoc_nhap && value) {
                    let inputErrTmp = inputErr;
                    inputErrTmp[data.index] = '';
                    setInputErr([...inputErrTmp]);
                  }
                },
              })
            }
            horizontal
          />
          {inputErr[data.index] !== '' && <Text children={inputErr[data.index]} style={{color: colors.RED1}} />}
        </View>
      );
    }
  };
  const renderFormDanhGiaHienTruong = () => {
    return <FlatList data={dataFormDanhGiaHienTruong} key={(item) => item.loai_stt + ''} renderItem={renderItemFormDanhGia} />;
  };

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <ButtonLinear
          title="Nộp báo cáo"
          onPress={onPressHoanThanhGiamDinhHienTruong}
          linearStyle={{marginRight: spacing.small}}
          linearColors={[colors.GRAY, colors.GRAY]}
          textStyle={{color: colors.BLACK_03}}
        />
        <ButtonLinear title="Lưu" onPress={onPressSave} />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle={'Đánh giá hiện trường'}
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
            {dataFormDanhGiaHienTruong.length > 0 && <View style={styles.contentView}>{renderFormDanhGiaHienTruong()}</View>}
          </KeyboardAwareScrollView>
          <ModalKetThucDanhGia
            toggleModalSuccess={toggleModalSuccess}
            setToggleModalSuccess={setToggleModalSuccess}
            prevScreen={route.params.prevScreen}
            onPressHoanThanhGiamDinhHienTruong={onPressHoanThanhGiamDinhHienTruong}
          />
        </View>
      }
      footer={renderFooter()}
    />
  );
};

export const DanhGiaHienTruongScreen = memo(DanhGiaHienTruongScreenComponent, isEqual);
