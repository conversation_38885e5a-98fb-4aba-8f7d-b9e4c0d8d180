import {isIOS, NGHIEP_VU} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, DropdownPicker, Icon, ScreenComponent, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {ModalInput} from './Components';
import styles from './Styles';

const DanhSachNoiDungCongViecScreenComponent = (props) => {
  const route = useRoute();
  const {profileData} = route?.params;

  const [data, setData] = useState([]);
  const [lhnv, setLhnv] = useState([]);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [selectedIndexItem, setSelectedIndexItem] = useState(-1);
  const [valueGhiChu, setValueGhiChu] = useState('');
  const [openDropdown, setOpenDropdown] = useState(false);
  const [loaiHinhNhiepVuSelected, setLoaiHinhNhiepVuSelected] = useState(null);

  let refModalInput = useRef(null);

  useEffect(() => {
    getDsLoaiHinhNghiepVu();
  }, []);

  const getDsLoaiHinhNghiepVu = async () => {
    try {
      setDialogLoading(true);
      let params = {ma_doi_tac: profileData?.ho_so?.ma_doi_tac, nv: NGHIEP_VU.XE};
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_LHNV, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setLhnv(response.data_info);
      setDialogLoading(false);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 60');
    }
  };

  const getDsNoiDungCongViec = async (val) => {
    try {
      setDialogLoading(true);
      let params = {
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id: profileData?.ho_so?.so_id,
        nv: 'XE',
        lh_nv: val,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_NOI_DUNG_CV, params);
      setData(response.data_info);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 60');
    }
  };

  // Lưu YCBS
  const luuHSGiayToYeuCauBoSung = async (type) => {
    if (data.length <= 0) return;
    try {
      setDialogLoading(true);
      let arr = [];
      data.forEach((e) => {
        let json = {
          bt: e.bt,
          trang_thai: e.trang_thai,
          ghi_chu: e.ghi_chu,
        };
        arr.push(json);
      });
      const params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        nv: 'XE',
        lh_nv: loaiHinhNhiepVuSelected,
        arr: arr,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_NOI_DUNG_CV, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      getDsNoiDungCongViec(loaiHinhNhiepVuSelected);
      if (type === 'luu') return FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu nội dung công việc thành công!', 'success');
      return;
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 106');
    }
  };

  const onChangeCheckBoxTrangThai = (idx, val) => {
    let listCongViec = data;
    listCongViec.map(() => {
      if (val === 'C' || val === null) {
        listCongViec[idx].trang_thai = 'D';
      } else if (val === 'D') {
        listCongViec[idx].trang_thai = 'C';
      }
    });
    setData([...listCongViec]);
  };

  const onChangeModalInputValue = (val) => {
    let listCongViec = data;
    listCongViec.map(() => {
      listCongViec[selectedIndexItem].ghi_chu = val;
    });
    setData([...listCongViec]);
  };

  const onOpenModalInput = (idx, it) => {
    setValueGhiChu(it.ghi_chu);
    setSelectedIndexItem(idx);
    refModalInput.current.show();
  };

  const filterByMa = (val) => {
    if (val) {
      getDsNoiDungCongViec(val);
    }
  };

  const renderItemHSGiayTo = ({item, index}) => {
    const isCheck = item.trang_thai === 'D';
    return (
      <View style={styles.item}>
        <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
          <View width={dimensions.width * 0.5} flexDirection="row">
            <Text style={styles.txtTenHangMuc}>{item.ten}</Text>
          </View>
          <View style={styles.rowStyles}>
            <View style={styles.frame}>
              <TouchableOpacity onPress={() => onChangeCheckBoxTrangThai(index, item.trang_thai, item)}>
                <CheckboxComp disabled checkboxStyle={isIOS && styles.checkboxStyle} value={isCheck} />
              </TouchableOpacity>
            </View>
            <View style={styles.frame}>
              <TouchableOpacity style={styles.icBtn} onPress={() => onOpenModalInput(index, item)}>
                <Icon.Feather name="file-text" size={18} color={item.ghi_chu === '' || item.ghi_chu === null ? colors.GRAY3 : colors.PRIMARY} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Nội dung công việc"
      renderView={
        <SafeAreaView flex={1}>
          <View style={styles.content}>
            <DropdownPicker
              title="Loại hình nghiệp vụ"
              zIndex={8000}
              isOpen={openDropdown}
              setOpen={setOpenDropdown}
              items={lhnv}
              itemSelected={loaiHinhNhiepVuSelected}
              setItemSelected={setLoaiHinhNhiepVuSelected}
              placeholder="Chọn loại hình nghiệp vụ"
              isRequired={true}
              searchable={false}
              onChangeValue={() => filterByMa(loaiHinhNhiepVuSelected)}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
            />
            <View style={styles.tableTitleRow}>
              <View width={dimensions.width * 0.5} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Tên công việc" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Đã hoàn thành" style={styles.txtGroup} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Ghi chú" style={styles.txtGroup} />
                </View>
              </View>
            </View>
            <FlatList
              data={data}
              scrollEnabled={true}
              renderItem={renderItemHSGiayTo}
              keyExtractor={(item, index) => index.toString()}
              refreshControl={<RefreshControl refreshing={false} />}
              ListEmptyComponent={<Text style={{marginVertical: 10, textAlign: 'center'}}>Chưa có dữ liệu</Text>}
            />
          </View>
          <View style={styles.footerView}>
            <ButtonLinear loading={false} disabled={false} onPress={() => luuHSGiayToYeuCauBoSung('luu')} title={'Lưu'} />
          </View>
          <ModalInput ref={refModalInput} value={valueGhiChu} onBackPress={() => refModalInput.current.hide()} onPressLuu={(val) => onChangeModalInputValue(val)} />
        </SafeAreaView>
      }
    />
  );
};

export const DanhSachNoiDungCongViecScreen = memo(DanhSachNoiDungCongViecScreenComponent, isEqual);
