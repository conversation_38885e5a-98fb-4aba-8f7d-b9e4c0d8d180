import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';
const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  pdf: {
    flex: 1,
    width: width,
    height: height,
  },
  shareView: {
    position: 'absolute',
    right: 10,
    bottom: 10,
    borderRadius: 100,
    borderWidth: 3,
    borderColor: colors.PRIMARY,
    backgroundColor: colors.WHITE,
  },
  shareIcon: {
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    borderWidth: 1,
    borderRadius: 35,
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  modalSuccessView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  imgMailUser: {
    width: width / 4,
    height: width / 4,
  },
  successTopView: {
    width: width - 30,
    height: height / 6,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // borderBottomWidth: 5,
    // borderBottomColor: colors.WHITE,
  },
  btnSuccessView: {
    height: 40,
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 10,
    width: width / 2 - 40,
    justifyContent: 'center',
  },
  successCenterView: {
    width: width - 30,
    paddingBottom: 10,
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    backgroundColor: colors.WHITE,
    justifyContent: 'space-around',
  },
  txtSuccess: {
    fontSize: 16,
    textAlign: 'center',
    color: colors.WHITE,
    paddingHorizontal: 10,
  },
  buttonModalView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  addMailView: {
    width: width - 60,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  inputMail: {
    flex: 1,
    height: 50,
    paddingLeft: 20,
    borderWidth: 0.5,
    borderRadius: 30,
    borderBottomWidth: 0.5,
  },
  iconAddMail: {
    padding: 10,
    borderWidth: 1,
    borderRadius: 10,
  },
  title: {
    fontWeight: '700',
  },
  noteInput: {
    minHeight: 80,
    maxHeight: 100,
    textAlignVertical: 'top',
  },
  footerView: {
    paddingVertical: 10,
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
    paddingHorizontal: spacing.small,
  },
  row: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  item: {
    padding: 10,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.GRAY,
    marginVertical: spacing.tiny,
  },
  label: {
    color: colors.GRAY6,
  },
  content: {
    flex: 1,
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  subLabel: {
    fontWeight: '600',
    color: colors.ORANGE,
  },
  subContent: {
    fontWeight: '400',
    fontStyle: 'italic',
    color: colors.BLACK_03,
  },
});
