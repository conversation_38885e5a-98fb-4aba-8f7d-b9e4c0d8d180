import {isIOS, NGHIEP_VU} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, Icon, SearchBar, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useDebouncedCallback} from 'use-debounce';
import {ModalInput} from '../Components';

const LapPhuongAnChungTuGaraThuHoScreenComponent = (props) => {
  const route = useRoute();
  const {profileData} = route?.params;

  const [loading, setLoading] = useState(true);

  const [indexHm, setIndexHm] = useState(-1);
  const [titleModal, setTitleModal] = useState('');
  const [searchText, setSearchText] = useState('');
  const [valueGhiChu, setValueGhiChu] = useState('');

  const [arrDS, setArrDs] = useState([]);
  const [dsHoSoGiayToYCGaraBoSung, setDsHoSoGiayToYCGaraBoSung] = useState([]);
  let refModalInput = useRef(null);

  useEffect(() => {
    getDsHoSoGiayToYeuCauGaraBoSung();
  }, []);

  //Lấy ds cần bổ sung
  const getDsHoSoGiayToYeuCauGaraBoSung = async () => {
    setLoading(true);
    let params = {
      so_id: profileData?.ho_so?.so_id,
      nv: NGHIEP_VU.XE,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HS_GT_YC_GARA_BO_SUNG, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDsHoSoGiayToYCGaraBoSung(response.data_info);
      setArrDs(response.data_info);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 78');
    }
  };

  // Lưu danh sách yc bổ sung
  const onPressLuu = async () => {
    setLoading(true);
    let arr = [];
    dsHoSoGiayToYCGaraBoSung.forEach((e) => {
      let json = {ma_hs: e.ma_hs, gara_thu_ho: e.gara_thu_ho, ghi_chu: e.ghi_chu};
      arr.push(json);
    });
    const params = {
      so_id: profileData?.ho_so?.so_id,
      arr: arr,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_CHUNG_TU_GARA_THU_HO, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 56');
    }
  };

  //
  const debounced = useDebouncedCallback((value) => {
    setSearchText(value);
  }, 300);

  useEffect(() => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = arrDS.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDsHoSoGiayToYCGaraBoSung(filter);
    } else if (searchText === '') {
      setDsHoSoGiayToYCGaraBoSung(arrDS);
    }
  }, [arrDS, searchText]);

  //
  const onOpenModal = (idx, label, item) => {
    let listGiayTo = dsHoSoGiayToYCGaraBoSung;
    if (listGiayTo[idx].gara_thu_ho === 0) return Alert.alert('Thông báo', 'Vui lòng chọn chứng từ/Gara thu hộ trước khi nhập ghi chú!');
    setIndexHm(idx);
    setValueGhiChu(item.ghi_chu);
    if (label === 'ghi_chu') {
      setTitleModal('Ghi chú');
      refModalInput.current.show();
    }
  };

  const onChangeCheckBoxValue = (idx, val) => {
    let listGiayTo = arrDS;
    listGiayTo.map(() => {
      if (val === 1) {
        listGiayTo[idx].gara_thu_ho = 0;
        listGiayTo[idx].ghi_chu = '';
      } else if (val === 0) {
        listGiayTo[idx].gara_thu_ho = 1;
      }
    });
    setDsHoSoGiayToYCGaraBoSung([...listGiayTo]);
  };

  const onChangeModalInputValue = (val) => {
    let listGiayTo = arrDS;
    listGiayTo.map(() => {
      listGiayTo[indexHm].ghi_chu = val;
    });
    setDsHoSoGiayToYCGaraBoSung([...listGiayTo]);
  };

  /* RENDER */
  const renderItem = ({item, index}) => {
    const isCheck = item.gara_thu_ho === 1;
    return (
      <View style={styles.item}>
        <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
          <Text style={styles.txtTenHangMuc} children={item.ten} />
          <View style={styles.rowStyles}>
            <View style={styles.frame}>
              <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={isCheck} onValueChange={() => onChangeCheckBoxValue(index, item.gara_thu_ho)} />
            </View>
            <View style={styles.frame}>
              <TouchableOpacity style={styles.icBtn} onPress={() => onOpenModal(index, 'ghi_chu', item)}>
                <Icon.Feather name="file-text" size={18} color={item.ghi_chu === '' || item.ghi_chu === null ? colors.GRAY3 : colors.PRIMARY} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      isLoading={loading}
      title="Chứng từ Gara thu hộ-LPA"
      onPressRight={onPressLuu}
      rightComponent={renderRightHeaderComponent()}
      renderView={
        <SafeAreaView flex={1}>
          <SearchBar placeholder="Tìm kiếm giấy tờ" onTextChange={debounced} />
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getDsHoSoGiayToYeuCauGaraBoSung} />}>
            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Tên giấy tờ" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Gara thu hộ" style={styles.txtGroup} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Ghi chú" style={styles.txtGroup} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHoSoGiayToYCGaraBoSung}
              scrollEnabled={false}
              renderItem={renderItem}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
          <ModalInput
            value={valueGhiChu}
            title={titleModal}
            ref={refModalInput}
            onBackPress={() => refModalInput.current.hide()}
            setValue={(v) => onChangeModalInputValue(v.value)}
            onPressLuu={(nd, tit) => onChangeModalInputValue(nd, tit)}
          />
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    marginHorizontal: 10,
  },
  btnView: {
    paddingTop: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginBottom: !isIOS ? 10 : 0,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },
  btnLuu: {
    marginHorizontal: 10,
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
  containerInput: {
    margin: 0,
    paddingVertical: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
  },
  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  txtCheckAll: {
    marginRight: 10,
    marginVertical: 10,
    color: colors.BLACK_03,
  },
  errText: {
    marginBottom: 5,
    color: colors.RED1,
  },
  item: {
    // paddingRight: 2,
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: 2,
  },
  checkBox: {
    paddingHorizontal: 10,
  },
  checkAll: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: spacing.smaller,
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    alignItems: 'center',
    flex: 1,
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
    paddingVertical: spacing.tiny,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  icBtn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtGroup: {
    fontSize: 12,
  },

  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
});

export const LapPhuongAnChungTuGaraThuHoScreen = memo(LapPhuongAnChungTuGaraThuHoScreenComponent, isEqual);
