import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, DropdownPicker, Icon, TextInputOutlined, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalDKBS} from '../Components';
import {getValue} from './constant';

const LapPhuongAnTinhToanKhauTruScreenComponent = (props) => {
  console.log('LapPhuongAnTinhToanKhauTruScreenComponent');
  const route = useRoute();
  const {profileData, actionType, lhnv, soIdDoiTuong, garaBaoGia} = route?.params;

  const [loading, setLoading] = useState(false);
  const [errInput, setErrInput] = useState(['', '', '']);

  const [checked, setChecked] = useState(true);
  const [checkDKBS, setCheckDKBS] = useState(true);
  const [openTLT, setOpenTLT] = useState(false);
  const [indexHm, setIndexHm] = useState(-1);
  const [itemSelected, setItemSelected] = useState('');
  const [itemDKBS, setItemDKBS] = useState('');
  const [dataVuTonThat, setDataVuTonThat] = useState([]);
  const [dataTonThatTheoVu, setDataTonThatTheoVu] = useState([]);
  const [dsHangMuc, setDsHangMuc] = useState([]);
  // const [data, setData] = useState([]);
  const [tienKhauTruTienBH, settienKhauTruTienBH] = useState('');
  const loaiTS = actionType === 'tai_san_khac' ? 'KHAC' : actionType === 'tslx_khau_hao' ? 'XE' : actionType === 'tslx_khau_tru' ? 'XE' : '';

  let refModalDKBS = useRef(null);

  const onPressLuu = async () => {
    setLoading(true);
    let hm = [];
    dsHangMuc.forEach((e) => {
      let json = {
        hang_muc: e.hang_muc,
        pt_khau_hao: e.pt_khau_hao || 0,
        pt_bao_hiem: e.pt_bao_hiem || 0,
        pt_giam_tru: e.pt_giam_tru || 0,
        nguyen_nhan: e.nguyen_nhan || '',
        ghi_chu: e.ghi_chu || '',
        // --Nhap giam gia
        tl_giam_gia_vtu: e.tl_giam_gia_vtu || 0,
        tl_giam_gia_nhan_cong: e.tl_giam_gia_nhan_cong || 0,
        tl_giam_gia_khac: e.tl_giam_gia_khac || 0,
        lh_giam_gia: e.lh_giam_gia || '',
        lh_tt_giam_gia: e.lh_tt_giam_gia || '',
        // --Nhap khau tru
        tl_ktru_tien_bh: e.tl_ktru_tien_bh || 0,
        dkbs: e.dkbs || '',
        // --Nhap ti le thue
        tl_thue_vtu: e.tl_thue_vtu || 0,
        tl_thue_nhan_cong: e.tl_thue_nhan_cong || 0,
        tl_thue_khac: e.tl_thue_khac || 0,
        bt_bao_gia: e.bt_bao_gia || 0,
      };
      hm.push(json);
    });
    try {
      const params = {
        hm: hm,
        lh_nv: lhnv,
        loai_ts: loaiTS,
        loai: 'PT_KHAU_TRU',
        vu_tt: itemSelected || '',
        so_id: profileData?.ho_so?.so_id,
        so_id_pa: garaBaoGia.so_id_pa || 0,
        tien_ktru_tien_bh: tienKhauTruTienBH || '',
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id_doi_tuong: garaBaoGia.so_id_doi_tuong || 0,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN_PA, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 88');
    }
  };

  useEffect(() => {
    getData();
    initData();
  }, []);

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        lh_nv: lhnv,
        loai_ts: loaiTS,
        loai: 'PT_KHAU_TRU',
        so_id: profileData?.ho_so?.so_id,
        so_id_pa: garaBaoGia?.so_id_pa || 0,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        vu_tt: profileData?.dien_bien[0]?.vu_tt || '',
        so_id_doi_tuong: garaBaoGia?.so_id_doi_tuong || 0,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI_PA, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.data;
      const vuTonThat = response.data_info.vu_tt;
      setDsHangMuc(data);
      setDataTonThatTheoVu(vuTonThat);
      settienKhauTruTienBH(vuTonThat[0].tien_ktru_tien_bh);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 122');
    }
  };

  useEffect(() => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      listHangMucTmp[index].vu_tt = itemSelected;
    });
    setDsHangMuc([...listHangMucTmp]);
  }, [itemSelected]);

  const initData = () => {
    if (profileData?.dien_bien?.length > 0) {
      let dsVuTonThat = profileData.dien_bien;
      setDataVuTonThat([...dsVuTonThat]);
      setItemSelected(dsVuTonThat[0].vu_tt);
    }
  };

  useEffect(() => {
    if (dataTonThatTheoVu.length > 0) {
      dataTonThatTheoVu.forEach((e) => {
        if (itemSelected === e.vu_tt) {
          settienKhauTruTienBH(e.tien_ktru_tien_bh);
        }
      });
    }
  }, [itemSelected]);

  const onChangeInputValue = (it, idx, val) => {
    let replaceValue = val.replace(',', '.');
    const value = getValue(replaceValue);
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (checked) {
        listHangMucTmp[index].tl_ktru_tien_bh = value;
      } else {
        listHangMucTmp[idx].tl_ktru_tien_bh = value;
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };

  const onOpenModalDKBS = (it, idx) => {
    setItemDKBS(it);
    setIndexHm(idx);
    refModalDKBS.current.show();
  };

  const onChangeValueModal = (val) => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (checkDKBS) {
        listHangMucTmp[index].dkbs = val.join();
      } else {
        listHangMucTmp[indexHm].dkbs = val.join();
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };
  /* RENDER */

  const renderItemHangMuc = ({index, item}) => {
    return (
      <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
        <Text style={styles.txtTenHangMuc} children={item.ten} />
        <View style={styles.rowStyles}>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              error={errInput[0]}
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.tl_ktru_tien_bh?.toString()}
              onChangeText={(value) => onChangeInputValue(item, index, value)}
            />
          </View>
          <View style={styles.frame}>
            <TouchableOpacity style={styles.icBtn} onPress={() => onOpenModalDKBS(item, index)}>
              <Icon.Feather name="file-text" size={18} color={item.dkbs === '' || item.dkbs === null ? colors.GRAY3 : colors.PRIMARY} />
            </TouchableOpacity>
          </View>

          {/* <CheckboxComp checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={item.isCheck} onValueChange={() => onItemValueCheckBoxChange(!item.isCheck, index)} /> */}
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={loading}
      rightComponent={renderRightHeaderComponent()}
      title="Chi tiết khấu trừ-LPA"
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <DropdownPicker
              searchable={false}
              title="Vụ tổn thất"
              zIndex={9999}
              isOpen={openTLT}
              schema={{
                label: 'dia_diem',
                value: 'vu_tt',
              }}
              setOpen={setOpenTLT}
              items={dataVuTonThat}
              itemSelected={itemSelected}
              setItemSelected={setItemSelected}
              // onChangeValue={onChangeValueDoiTuong}
              placeholder="0"
              isRequired
              // inputErr={errDropdown[0]}
              // maxHeight={dimensions.height / 4}
            />
            <View flex={1} marginBottom={10}>
              <TextInputOutlined
                isRequired
                placeholder="0"
                keyboardType="numeric"
                onChangeText={settienKhauTruTienBH}
                inputStyle={{textAlign: 'right'}}
                title="Số tiền tối thiểu(chưa VAT)"
                value={tienKhauTruTienBH?.toString()}
              />
            </View>

            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Hạng mục" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="TL giảm (%)" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checked} onValueChange={setChecked} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="ĐKBS" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkDKBS} onValueChange={setCheckDKBS} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItemHangMuc}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
          <ModalDKBS itemDKBS={itemDKBS} data={profileData?.dkbs} ref={refModalDKBS} setValue={(list) => onChangeValueModal(list)} onBackPress={() => refModalDKBS.current.hide()} />
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    marginTop: vScale(10),
    marginHorizontal: scale(10),
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
    paddingVertical: vScale(spacing.smaller),
  },

  title: {
    fontWeight: 'bold',
    textAlign: 'right',
    fontSize: FontSize.size16,
    marginVertical: vScale(10),
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: FontSize.size13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: scale(2),
  },
  checkBox: {
    paddingHorizontal: scale(10),
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    alignItems: 'center',
    flex: 1,
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: vScale(6),
    backgroundColor: colors.GRAY2,
    paddingHorizontal: scale(spacing.small),
  },
  icBtn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtGroup: {
    fontSize: FontSize.size12,
  },
  txtEmpty: {
    textAlign: 'center',
    marginVertical: vScale(10),
  },
});

export const LapPhuongAnTinhToanKhauTruScreen = memo(LapPhuongAnTinhToanKhauTruScreenComponent, isEqual);
