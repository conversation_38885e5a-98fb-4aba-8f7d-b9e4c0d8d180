import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, dimensions, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, Text, TextInputOutlined} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {getValue} from './constant';

const LapPhuongAnTinhToanTrachNhiemBHTNDSVeTaiSanScreenComponent = (props) => {
  console.log('LapPhuongAnTinhToanTrachNhiemBHTNDSVeTaiSanScreenComponent');
  const route = useRoute();
  const {lhnv, garaBaoGia, screenTitle, hangMuc, soIdDoiTuong} = route?.params;
  const [dsHangMuc, setDsHangMuc] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isChecked, setIsChecked] = useState(true);

  const onPressLuu = async () => {
    setLoading(true);
    let hm = [];
    dsHangMuc.forEach((e) => {
      let json = {
        bt: e.bt,
        tien_vtu: e.tien_vtu,
        tien_nhan_cong: e.tien_nhan_cong,
        tien_khac: e.tien_khac,
        gia_duyet: +e.tien_khac + +e.tien_nhan_cong + +e.tien_vtu,
        pt_khau_hao: e.pt_khau_hao,
        pt_bao_hiem: e.pt_bao_hiem,
        pt_giam_tru: e.pt_giam_tru,
        pt_giam_tru_loi: e.pt_giam_tru_loi,
        nguyen_nhan: e.nguyen_nhan,
        ghi_chu: e.ghi_chu,
        tl_thue_vtu: e.tl_thue_vtu,
        tl_thue_nhan_cong: e.tl_thue_nhan_cong,
        tl_thue_khac: e.tl_thue_khac,
      };
      hm.push(json);
    });
    try {
      const params = {
        so_id: garaBaoGia?.so_id,
        so_id_doi_tuong: soIdDoiTuong || 0,
        lh_nv: lhnv,
        so_id_pa: garaBaoGia.so_id_pa || 0,
        data: hm,
        hang_muc: hangMuc,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN_PA_TNDS_TAI_SAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 71');
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        lh_nv: lhnv,
        so_id: garaBaoGia?.so_id,
        ma_doi_tac: garaBaoGia?.ma_doi_tac,
        hang_muc: hangMuc,
        so_id_doi_tuong: soIdDoiTuong,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI_PA_TNDS_TAI_SAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info;
      setDsHangMuc(data);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };

  const onChangeInputValue = (it, idx, val) => {
    let replaceValue = val.replace(',', '.');
    const value = getValue(replaceValue);
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (isChecked) {
        listHangMucTmp[index].pt_bao_hiem = value;
      } else {
        listHangMucTmp[idx].pt_bao_hiem = value;
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };
  /* RENDER */

  const renderItemHangMuc = ({index, item}) => {
    return (
      <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
        <Text style={styles.txtTenHangMuc} children={item.ten_chi_phi} />
        <View style={styles.rowStyles}>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              value={item?.pt_bao_hiem?.toString()}
              containerStyle={styles.inputContainer}
              onChangeText={(value) => onChangeInputValue(item, index, value)}
            />
            {/* <TextInput
              maxLength={3}
              placeholder="0"
              keyboardType="defau"
              inputStyle={styles.inputStyle}
              value={item?.pt_bao_hiem?.toString()}
              containerStyle={styles.inputContainer}
              onChangeText={(value) => onChangeInputValue(item, index, value)}
            /> */}
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={loading}
      rightComponent={renderRightHeaderComponent()}
      title={screenTitle}
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Hạng mục" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Tỷ lệ (%)" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={isChecked} onValueChange={setIsChecked} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItemHangMuc}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={
                <View borderWidth={0.5} borderColor={colors.GRAY}>
                  <Text style={styles.txtEmpty} children="Danh sách trống!" />
                </View>
              }
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
        </SafeAreaView>
      }
    />
  );
};

export const LapPhuongAnTinhToanTrachNhiemBHTNDSVeTaiSanScreen = memo(LapPhuongAnTinhToanTrachNhiemBHTNDSVeTaiSanScreenComponent, isEqual);
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },

  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  content: {
    flex: 1,
    marginTop: 10,
    marginHorizontal: 10,
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: 2,
  },
  rowStyles: {
    width: dimensions.width / 3,
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  txtEmpty: {
    textAlign: 'center',
    fontSize: FontSize.size14,
    marginVertical: vScale(spacing.smaller),
  },
});
