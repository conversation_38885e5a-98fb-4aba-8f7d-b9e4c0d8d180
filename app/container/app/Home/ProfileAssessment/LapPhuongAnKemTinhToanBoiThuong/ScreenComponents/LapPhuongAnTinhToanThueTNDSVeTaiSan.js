import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, Text, TextInputOutlined} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {getValue} from './constant';

const LapPhuongAnTinhToanThueTNDSVeTaiSanScreenComponent = (props) => {
  console.log('LapPhuongAnTinhToanThueTNDSVeTaiSanScreenComponent');
  const route = useRoute();
  const {lhnv, garaBaoGia, hangMuc, soIdDoiTuong} = route?.params;
  const [loading, setLoading] = useState(false);
  const [checkSon, setCheckSon] = useState(true);
  const [checkVatTu, setCheckVatTu] = useState(true);
  const [checkNhanCong, setCheckNhanCong] = useState(true);
  const [dsHangMuc, setDsHangMuc] = useState([]);

  const onPressLuu = async () => {
    setLoading(true);
    let hm = [];
    dsHangMuc.forEach((e) => {
      let json = {
        bt: e.bt,
        tien_vtu: e.tien_vtu,
        tien_nhan_cong: e.tien_nhan_cong,
        tien_khac: e.tien_khac,
        gia_duyet: +e.tien_khac + +e.tien_nhan_cong + +e.tien_vtu,
        pt_khau_hao: e.pt_khau_hao,
        pt_bao_hiem: e.pt_bao_hiem,
        pt_giam_tru: e.pt_giam_tru,
        nguyen_nhan: e.nguyen_nhan,
        ghi_chu: e.ghi_chu,
        tl_thue_vtu: e.tl_thue_vtu,
        tl_thue_nhan_cong: e.tl_thue_nhan_cong,
        tl_thue_khac: e.tl_thue_khac,
      };
      hm.push(json);
    });
    try {
      const params = {
        so_id: garaBaoGia?.so_id,
        so_id_doi_tuong: soIdDoiTuong || 0,
        lh_nv: lhnv,
        so_id_pa: garaBaoGia.so_id_pa || 0,
        data: hm,
        hang_muc: hangMuc,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN_PA_TNDS_TAI_SAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 71');
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        lh_nv: lhnv,
        so_id: garaBaoGia?.so_id,
        ma_doi_tac: garaBaoGia?.ma_doi_tac,
        hang_muc: hangMuc,
        so_id_doi_tuong: soIdDoiTuong,
      };
      console.log('🚀 ~ file: LapPhuongAnTinhToanThueTNDSVeTaiSan.js:85 ~ getData ~ params:', params);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI_PA_TNDS_TAI_SAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info;
      setDsHangMuc(data);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };

  const onChangeInputValue = (it, idx, val) => {
    let replaceValue = val.replace(',', '.');
    const value = getValue(replaceValue);
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (it === 'Vật tư') {
        if (checkVatTu) {
          listHangMucTmp[index].tl_thue_vtu = value;
        } else {
          listHangMucTmp[idx].tl_thue_vtu = value;
        }
      } else if (it === 'Nhân công') {
        if (checkNhanCong) {
          listHangMucTmp[index].tl_thue_nhan_cong = value;
        } else {
          listHangMucTmp[idx].tl_thue_nhan_cong = value;
        }
      } else if (it === 'Sơn') {
        if (checkSon) {
          listHangMucTmp[index].tl_thue_khac = value;
        } else {
          listHangMucTmp[idx].tl_thue_khac = value;
        }
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };
  /* RENDER */

  const renderItemHangMuc = ({item, index}) => {
    return (
      <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
        <Text style={styles.txtTenHangMuc} children={item.ten_chi_phi} />
        <View style={styles.rowStyles}>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.tl_thue_vtu?.toString()}
              onChangeText={(value) => onChangeInputValue('Vật tư', index, value)}
            />
          </View>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.tl_thue_nhan_cong?.toString() || ''}
              onChangeText={(value) => onChangeInputValue('Nhân công', index, value)}
            />
          </View>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.tl_thue_khac?.toString() || ''}
              onChangeText={(value) => onChangeInputValue('Sơn', index, value)}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={loading}
      rightComponent={renderRightHeaderComponent()}
      title="Chi tiết thuế (LPA)"
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Hạng mục" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="V.tư" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkVatTu} onValueChange={setCheckVatTu} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="N.công" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkNhanCong} onValueChange={setCheckNhanCong} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Sơn" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkSon} onValueChange={setCheckSon} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItemHangMuc}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
            {/* {dsHangMuc.length > 0 ? (
              dsHangMuc.map((item, index) => {
                return <View>{renderItemHangMuc(item, index)}</View>;
              })
            ) : (
              <Text style={styles.txtEmpty} children="Danh sách trống!" />
            )} */}
          </KeyboardAwareScrollView>
        </SafeAreaView>
      }
    />
  );
};

export const LapPhuongAnTinhToanThueTNDSVeTaiSanScreen = memo(LapPhuongAnTinhToanThueTNDSVeTaiSanScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    marginTop: 10,
    marginHorizontal: 10,
  },
  btnView: {
    paddingTop: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginBottom: !isIOS ? 10 : 0,
  },

  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },
  btnLuu: {
    marginHorizontal: 10,
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
  containerInput: {
    margin: 0,
    paddingVertical: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
  },
  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  txtCheckAll: {
    marginRight: 10,
    marginVertical: 10,
    color: colors.BLACK_03,
  },
  errText: {
    marginBottom: 5,
    color: colors.RED1,
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: 2,
  },
  checkBox: {
    paddingHorizontal: 10,
  },
  checkAll: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: spacing.smaller,
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  icBtn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtGroup: {
    fontSize: 12,
  },
  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
});
