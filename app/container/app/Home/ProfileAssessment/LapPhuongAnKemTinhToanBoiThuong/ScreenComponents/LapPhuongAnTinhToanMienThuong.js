import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {logErrorTryCatch} from '@app/utils';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {DropdownPicker, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

const DS_TLT = [
  {label: 'Theo TL thuế từng hạng mục', value: -1},
  {label: '0 %', value: 0},
  {label: '8 %', value: 8},
  {label: '10 %', value: 10},
];

const LapPhuongAnTinhToanMienThuongScreenComponent = ({route}) => {
  console.log('LapPhuongAnTinhToanMienThuongScreenComponent');
  const {profileData, actionType, lhnv, garaBaoGia} = route?.params;

  const [openTLT, setOpenTLT] = useState(false);
  const [TLTSelected, setTLTSelected] = useState('');
  const [mtTrenVu, setMtTrenVu] = useState(0);
  const [soVu, setSoVu] = useState(0);
  const [tienMienThuongSauThue, setTienMienThuongSauThue] = useState(0);
  const [ptApDungThueMT, setPtApDungThueMT] = useState('');
  const [dsHangMuc, setDsHangMuc] = useState([]);
  const loaiTS = actionType === 'tai_san_khac' ? 'KHAC' : actionType === 'tslx_khau_hao' ? 'XE' : actionType === 'tslx_khau_tru' ? 'XE' : '';

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    if (soVu > 0 && mtTrenVu > 0) {
      setTienMienThuongSauThue(soVu * mtTrenVu);
    } else setTienMienThuongSauThue(0);
  }, [soVu, mtTrenVu]);

  useEffect(() => {
    if (TLTSelected === -1) {
      setPtApDungThueMT('THM');
    } else {
      setPtApDungThueMT('TLT');
    }
  }, [TLTSelected]);

  const onPressLuu = async () => {
    try {
      setLoading(true);
      let hm = [];
      dsHangMuc.forEach((e) => {
        let json = {
          hang_muc: e.hang_muc,
          pt_khau_hao: e.pt_khau_hao || 0,
          pt_bao_hiem: e.pt_bao_hiem || 0,
          pt_giam_tru: e.pt_giam_tru || 0,
          nguyen_nhan: e.nguyen_nhan || '',
          ghi_chu: e.ghi_chu || '',
          // --Nhap giam gia
          tl_giam_gia_vtu: e.tl_giam_gia_vtu || 0,
          tl_giam_gia_nhan_cong: e.tl_giam_gia_nhan_cong || 0,
          tl_giam_gia_khac: e.tl_giam_gia_khac || 0,
          lh_giam_gia: e.lh_giam_gia || '',
          lh_tt_giam_gia: e.lh_tt_giam_gia || '',
          // --Nhap khau tru
          tl_ktru_tien_bh: e.tl_ktru_tien_bh || 0,
          dkbs: e.dkbs || '',
          // --Nhap ti le thue
          tl_thue_vtu: e.tl_thue_vtu || 0,
          tl_thue_nhan_cong: e.tl_thue_nhan_cong || 0,
          tl_thue_khac: e.tl_thue_khac || 0,
        };
        hm.push(json);
      });
      const params = {
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id: profileData?.ho_so?.so_id,
        so_id_doi_tuong: garaBaoGia.so_id_doi_tuong || 0,
        lh_nv: lhnv,
        loai: 'MIEN_THUONG',
        hm: hm,
        pt_ad_thue_mien_thuong: ptApDungThueMT || '',
        mien_thuong: tienMienThuongSauThue || '',
        khau_tru: 'K' || '',
        tl_thue: TLTSelected === -1 ? 0 : TLTSelected || '',
        thue: 0,
        loai_ts: loaiTS,
        so_id_pa: garaBaoGia.so_id_pa || 0,
        so_vu: +soVu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN_PA, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message + '- line - 106');
    }
  };

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        vu_tt: '',
        lh_nv: lhnv,
        loai_ts: loaiTS,
        loai: 'MIEN_THUONG',
        so_id: profileData?.ho_so?.so_id,
        so_id_pa: garaBaoGia?.so_id_pa || 0,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id_doi_tuong: garaBaoGia?.so_id_doi_tuong || 0,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI_PA, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.data;
      setDsHangMuc(data);
      let data0 = data[0];
      setMtTrenVu(data0.tien_mien_thuong_sau_thue / data0.so_vu_tt);
      setSoVu(data0.so_vu_tt);
      setTLTSelected(data0.pt_ad_thue_mien_thuong === 'THM' ? -1 : data0.tl_thue);
      setTienMienThuongSauThue(data0.tien_mien_thuong_sau_thue);
      return;
    } catch (error) {
      setLoading(false);
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message + '- line - 133');
    }
  };

  /* RENDER */
  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialodLoading={loading}
      rightComponent={renderRightHeaderComponent()}
      title="Khấu trừ / vụ - LPA"
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <View flex={1}>
              <TextInputOutlined value={mtTrenVu} placeholder="0" keyboardType="numeric" onChangeText={setMtTrenVu} inputStyle={{textAlign: 'right'}} title="Khấu trừ / vụ" />
            </View>
            {actionType !== 'nv_hhtx' && (
              <View>
                <View flex={1}>
                  <TextInputOutlined value={soVu} placeholder="0" keyboardType="numeric" onChangeText={setSoVu} inputStyle={{textAlign: 'right'}} title="Số vụ TT" />
                </View>
                <DropdownPicker
                  zIndex={9999}
                  items={DS_TLT}
                  placeholder="0"
                  isOpen={openTLT}
                  searchable={false}
                  setOpen={setOpenTLT}
                  itemSelected={TLTSelected}
                  title="Tỷ lệ thuế khấu trừ"
                  setItemSelected={setTLTSelected}
                  containerStyle={{height: dimensions.height / 2}}
                  disabled
                />
              </View>
            )}
          </KeyboardAwareScrollView>
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    marginTop: 10,
    marginHorizontal: 10,
  },
  btnView: {
    paddingTop: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginBottom: !isIOS ? 10 : 0,
  },

  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },
  btnLuu: {
    marginHorizontal: 10,
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
  containerInput: {
    margin: 0,
    paddingVertical: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
  },
  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  txtCheckAll: {
    marginRight: 10,
    marginVertical: 10,
    color: colors.BLACK_03,
  },
  errText: {
    marginBottom: 5,
    color: colors.RED1,
  },
  item: {
    // paddingRight: 2,
  },

  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: 13,
    marginLeft: 2,
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  checkBox: {
    paddingHorizontal: 10,
  },
  checkAll: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: spacing.smaller,
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  icBtn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtGroup: {
    fontSize: 12,
  },

  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
});
export const LapPhuongAnTinhToanMienThuongScreen = memo(LapPhuongAnTinhToanMienThuongScreenComponent, isEqual);
