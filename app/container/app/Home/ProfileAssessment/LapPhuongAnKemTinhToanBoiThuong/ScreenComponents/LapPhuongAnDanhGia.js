import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, DropdownPicker, TextInputOutlined, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalDeXuat, ModalInput, ModalNhanXet} from '../Components';

let timer;
const LapPhuongAnDanhGiaScreenComponent = (props) => {
  const route = useRoute();
  const {profileData} = route?.params;

  const [errInput, setErrInput] = useState(['', '', '', '']);
  const [selected1, setSelected1] = useState([false, false]); // hồ sơ đầy đủ
  const [selected2, setSelected2] = useState([false, false]); // nguyên nhân tai nạn
  const [selected3, setSelected3] = useState([false, false]); // tổn thất thuộc phạm vi bh
  const [selected4, setSelected4] = useState([false, false]); // Giá trị xe tham gia bh
  const [selected5, setSelected5] = useState([false, false]); // Tuân thủ của đv vấp bh
  const [selected6, setSelected6] = useState([false, false]); // thời hạn khai báo của kh

  const [itemSelected, setItemSelected] = useState('');
  const [dataDvNhanDon, setDataDvNhanDon] = useState([]);
  const [dataDanhGia, setDataDanhGia] = useState([]);
  const [openDropdown, setOpenDropdown] = useState(false);

  const [lhnv, setLhnv] = useState([]);
  const [itemLhnv, setItemLhnv] = useState('');
  const [openLhnv, setOpenLhnv] = useState(false);

  const [title, setTitle] = useState('');
  const [deXuat, setDeXuat] = useState('');
  const [nhanXet, setNhanXet] = useState('');

  const [nxMau, setNxMau] = useState([]);
  const [dxMau, setDxMau] = useState([]);
  const [loadingNX, setLoadingNX] = useState(false);
  const [loadingDX, setLoadingDX] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);

  let refModalNhanXet = useRef(null);
  let refModalDeXuat = useRef(null);
  let refModalInput = useRef(null);

  useEffect(() => {
    initData();
    getChiTietDanhGia();
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getChiTietDanhGia = async () => {
    setRefreshing(true);
    let params = {
      so_id: profileData?.ho_so?.so_id,
      ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
      lh_nv: profileData?.lh_nv[0].ma,
      pm: 'BT',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DANH_GIA, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info;
      setDataDanhGia(data);
      initField(data);
      return;
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
    return;
  };

  const initField = (data) => {
    if (data !== null) {
      setNhanXet(data.danh_gia);
      setDeXuat(data.de_xuat);
      if (data.ho_so_day_du === 'K') {
        setSelected1([false, true]);
      } else setSelected1([true, false]);
      if (data.nguyen_nhan === 'D') {
        setSelected2([true, false]);
      } else setSelected2([false, true]);
      if (data.pham_vi_tt === 'C') {
        setSelected3([true, false]);
      } else setSelected3([false, true]);
      if (data.gt_tham_gia_bh === 'D') {
        setSelected4([true, false]);
      } else setSelected4([false, true]);
      if (data.tuan_thu === 'C') {
        setSelected5([true, false]);
      } else setSelected5([false, true]);
      if (data.thoi_han_khai_bao === 'D') {
        setSelected6([true, false]);
      } else setSelected6([false, true]);
    }
  };

  const initData = () => {
    if (profileData?.dvi_nhan_hdon?.length > 0) {
      let arr = profileData?.dvi_nhan_hdon;
      arr.map((item, index) => {
        arr[index].value = item.dvi_nhan;
        arr[index].label = item.ten_dvi_nhan;
      });
      setDataDvNhanDon(arr);
      setItemSelected(arr[0].value);
    }
    if (profileData?.lh_nv?.length > 0) {
      let data = profileData?.lh_nv;
      data.map((item, index) => {
        data[index].value = item.ma;
        data[index].label = item.ten;
      });
      setLhnv(data);
      setItemLhnv(data[0].value);
    }
  };

  const onChangeValueModal = (val) => {
    onChangeTextNhanXet(val);
  };
  const onChangeValueDeXuat = (val) => {
    onChangeTextDeXuat(val);
  };

  const onChangeTextNhanXet = (val) => {
    setNhanXet(val);
    let errInputTemp = errInput;
    if (!val || val === '') {
      errInputTemp[1] = 'Vui lòng nhập nhận xét';
    } else {
      errInputTemp[1] = '';
    }
    setErrInput([...errInputTemp]);
  };
  const onChangeTextDeXuat = (val) => {
    setDeXuat(val);
    let errInputTemp = errInput;
    if (!val) {
      errInputTemp[2] = 'Vui lòng nhập đề xuất';
    } else {
      errInputTemp[2] = '';
    }
    setErrInput([...errInputTemp]);
  };

  const onOpenModalNhanXet = (t) => {
    setTitle(t);
    refModalInput.current.show();
  };
  const onOpenDeXuatMau = (t) => {
    setTitle(t);
    getDeXuatMau(t);
    refModalDeXuat.current.show();
  };
  const onOpenNhanXetMau = (t) => {
    setTitle(t);
    getNhanXetMau(t);
    refModalNhanXet.current.show();
  };

  const onPressLuu = async (d) => {
    let errInputTemp = errInput;
    let haveErr = false;
    if (!itemSelected) {
      errInputTemp[0] = 'Chưa chọn đơn vị nhận hồ sơ, tài liệu';
      haveErr = true;
    }
    if (!nhanXet) {
      errInputTemp[1] = 'Vui lòng nhập nhận xét';
      haveErr = true;
    }
    if (!deXuat) {
      errInputTemp[2] = 'Vui lòng nhập đề xuất';
      haveErr = true;
    }
    setErrInput([...errInputTemp]);
    if (haveErr) return;
    setLoading(true);
    let params = {
      ma_doi_tac: profileData?.ho_so.ma_doi_tac,
      so_id: profileData?.ho_so.so_id,
      ho_so_day_du: selected1[0] ? 'D' : 'K',
      nguyen_nhan: selected2[0] ? 'D' : 'K',
      pham_vi_tt: selected3[0] ? 'C' : 'K',
      gt_tham_gia_bh: selected4[0] ? 'D' : 'K',
      pt_gt_tham_gia_bh: dataDanhGia?.pt_gt_tham_gia_bh || 0,
      tuan_thu: selected5[0] ? 'C' : 'K',
      thoi_han_khai_bao: selected6[0] ? 'D' : 'K',
      pt_che_tai: dataDanhGia?.pt_che_tai || 0,
      trang_thai: 'D',
      lh_nv: itemLhnv,
      de_xuat: deXuat,
      danh_gia: nhanXet,
      dvi_nhan: itemSelected,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DANH_GIA, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu đánh giá thành công', 'success');
      getChiTietDanhGia();
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
    return;
  };
  const onPressLuuMau = async (nd, tit) => {
    let params = {
      ma_doi_tac: profileData?.ho_so.ma_doi_tac,
      so_id: 0,
      nv: 'XE',
      pm: 'BT',
      nv_ct: tit === 'Nhận xét' ? 'NHAN_XET' : 'DE_XUAT',
      noi_dung: nd,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_NHAN_XET_MAU, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', `Lưu ${tit} thành công`, 'success');
      return;
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };
  const getNhanXetMau = async () => {
    setLoadingNX(true);
    let params = {
      ma_doi_tac: profileData?.ho_so.ma_doi_tac,
      nv: 'XE',
      pm: 'BT',
      nv_ct: 'NHAN_XET',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_NHAN_XET_MAU, params);
      setLoadingNX(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.noi_dung;
      setNxMau(data);
      return;
    } catch (error) {
      setLoadingNX(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };
  const getDeXuatMau = async () => {
    setLoadingDX(true);
    let params = {
      ma_doi_tac: profileData?.ho_so.ma_doi_tac,
      nv: 'XE',
      pm: 'BT',
      nv_ct: 'DE_XUAT',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_NHAN_XET_MAU, params);
      setLoadingDX(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.noi_dung;
      setDxMau(data);
      return;
    } catch (error) {
      setLoadingDX(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };

  /* RENDER */
  const renderItem = () => {
    return (
      <View>
        <Text children="Đánh giá, nhận xét của BTV" style={[styles.label, {textTransform: 'uppercase', marginVertical: spacing.small}]} />
        <Text children="Hồ sơ (GPLX, GCNBH, ĐK…)" style={styles.label} />
        <View style={styles.itemCheckboxRow}>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected1[0]} onPress={() => setSelected1([true, false])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected1[0]} />
            <Text children="Đầy đủ, hợp lệ" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected1[1]} onPress={() => setSelected1([false, true])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected1[1]} />
            <Text children="Không đủ" />
          </TouchableOpacity>
        </View>
        <Text children="Nguyên nhân tai nạn" style={styles.label} />
        <View style={styles.itemCheckboxRow}>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected2[0]} onPress={() => setSelected2([true, false])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected2[0]} />
            <Text children="Đúng, rõ ràng" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected2[1]} onPress={() => setSelected2([false, true])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected2[1]} />
            <Text children="Không đúng" />
          </TouchableOpacity>
        </View>
        <Text children="Tổn thất thuộc phạm vi bảo hiểm" style={styles.label} />
        <View style={styles.itemCheckboxRow}>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected3[0]} onPress={() => setSelected3([true, false])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected3[0]} />
            <Text children="Có" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected3[1]} onPress={() => setSelected3([false, true])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected3[1]} />
            <Text children="Không" />
          </TouchableOpacity>
        </View>
        <Text children="Giá trị xe tham gia bảo hiểm" style={styles.label} />
        <View style={styles.itemCheckboxRow}>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected4[0]} onPress={() => setSelected4([true, false])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected4[0]} />
            <Text children="Đúng giá trị" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected4[1]} onPress={() => setSelected4([false, true])}>
            <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected4[1]} />
            <Text children={`Dưới giá trị (${dataDanhGia?.pt_gt_tham_gia_bh}%)`} />
          </TouchableOpacity>
        </View>
        <View>
          <Text children="Tuân thủ của đơn vị cấp BH" style={styles.label} />
          <View style={styles.itemCheckboxRow}>
            <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected5[0]} onPress={() => setSelected5([true, false])}>
              <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected5[0]} />
              <Text children="Có" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected5[1]} onPress={() => setSelected5([false, true])}>
              <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected5[1]} />
              <Text children="Không (ghi rõ)" />
            </TouchableOpacity>
          </View>
        </View>
        <View>
          <Text children="Thời hạn khai báo của khách hàng" style={styles.label} />
          <View style={styles.itemCheckboxRow}>
            <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected6[0]} onPress={() => setSelected6([true, false])}>
              <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected6[0]} />
              <Text children="Đúng" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.valueCheckboxStyles} disabled={selected6[1]} onPress={() => setSelected6([false, true])}>
              <CheckboxComp disabled checkboxStyle={[styles.checkBox, isIOS && styles.checkboxStyle]} value={selected6[1]} />
              <Text style={{flex: 1}} children={`Không (chế tài ${dataDanhGia?.pt_che_tai}%)`} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={loading}
      rightComponent={renderRightHeaderComponent()}
      title="Đánh giá của BTV-LPA"
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={refreshing} />}>
            <DropdownPicker
              isRequired
              items={lhnv}
              zIndex={9999}
              isOpen={openLhnv}
              searchable={false}
              setOpen={setOpenLhnv}
              inputErr={errInput[3]}
              itemSelected={itemLhnv}
              title="Loại hình nghiệp vụ"
              setItemSelected={setItemLhnv}
              containerStyle={{marginHorizontal: scale(10)}}
            />
            <DropdownPicker
              isRequired
              zIndex={9998}
              searchable={false}
              isOpen={openDropdown}
              items={dataDvNhanDon}
              inputErr={errInput[0]}
              setOpen={setOpenDropdown}
              title="Đơn vị nhận hồ sơ, tài liệu"
              itemSelected={itemSelected}
              setItemSelected={setItemSelected}
              containerStyle={{marginHorizontal: scale(10)}}
            />
            {renderItem()}
            <View marginHorizontal={10}>
              <TextInputOutlined
                isRequired
                value={nhanXet}
                multiline={true}
                error={errInput[1]}
                keyboardType="default"
                title="Nhận xét của BTV"
                placeholder="Nhập nhận xét"
                inputStyle={styles.inputStyle}
                subTitle="Chọn nhận xét đã tạo"
                onPressSubTitle={() => onOpenNhanXetMau()}
                onChangeText={(t) => onChangeTextNhanXet(t)}
                onPressAddIcon={() => onOpenModalNhanXet('Nhận xét')}
              />
              <TextInputOutlined
                isRequired
                value={deXuat}
                multiline={true}
                error={errInput[2]}
                keyboardType="default"
                title="Đề xuất của BTV"
                placeholder="Nhập đề xuất"
                subTitle="Chọn đề xuất đã tạo"
                inputStyle={styles.inputStyle}
                onPressAddIcon={() => onOpenModalNhanXet('Đề xuất')}
                onChangeText={(v) => onChangeTextDeXuat(v)}
                onPressSubTitle={() => onOpenDeXuatMau()}
              />
            </View>
          </KeyboardAwareScrollView>
          <ModalNhanXet refreshing={loadingNX} data={nxMau} ref={refModalNhanXet} setValue={(v) => onChangeValueModal(v.noi_dung)} value={nhanXet} onBackPress={() => refModalNhanXet.current.hide()} />
          <ModalDeXuat refreshing={loadingDX} data={dxMau} ref={refModalDeXuat} setValue={(v) => onChangeValueDeXuat(v.noi_dung)} value={deXuat} onBackPress={() => refModalDeXuat.current.hide()} />
          <ModalInput
            title={title}
            ref={refModalInput}
            onPressLuu={(nd, tit) => {
              setRefreshing(true);
              timer = setTimeout(() => {
                onPressLuuMau(nd, tit);
              }, 500);
            }}
            setValue={(v) => onChangeValueDeXuat(v.value)}
            onBackPress={() => refModalInput.current.hide()}
          />
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    marginTop: vScale(10),
  },
  label: {
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
    marginHorizontal: scale(10),
  },
  itemCheckboxRow: {
    borderBottomWidth: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    borderBottomColor: colors.GRAY2,
    paddingTop: vScale(spacing.tiny),
    paddingBottom: vScale(spacing.smaller),
    marginVertical: vScale(spacing.smaller),
  },
  valueCheckboxStyles: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: scale(spacing.medium),
  },
  checkBox: {
    marginRight: scale(spacing.smaller),
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  inputStyle: {
    height: 80,
    textAlignVertical: 'top',
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: vScale(6),
    backgroundColor: colors.GRAY2,
    paddingHorizontal: scale(spacing.small),
  },
});

export const LapPhuongAnDanhGiaScreen = memo(LapPhuongAnDanhGiaScreenComponent, isEqual);
