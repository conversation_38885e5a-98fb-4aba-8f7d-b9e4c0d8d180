import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  subLabel: {
    fontWeight: '500',
    color: colors.GRAY6,
    marginVertical: vScale(6),
    fontSize: FontSize.size12,
    marginHorizontal: scale(spacing.tiny),
  },
  footerView: {
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE1,
  },
  titleRowView: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderColor: colors.GRAY,
  },
  col1: {
    alignItems: 'center',
    width: dimensions.width / 12 - 6,
    borderRightWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  percentCol: {
    width: dimensions.width / 12,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  title: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: FontSize.size13,
    marginHorizontal: scale(spacing.tiny),
    marginVertical: vScale(spacing.smaller),
  },
  priceCol: {
    flex: 1,
    width: dimensions.width / 4,
    alignItems: 'flex-end',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  price: {
    fontWeight: '500',
    textAlign: 'right',
    color: colors.GRAY6,
    marginRight: scale(4),
    fontSize: FontSize.size13,
  },
  col1Txt: {
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size12,
  },
  col1SubTxt: {
    fontWeight: '500',
    color: colors.GRAY6,
    fontSize: FontSize.size12,
  },
  titleCol: {
    width: dimensions.width - 120,
  },
  subLabelCol: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontWeight: '600',
    color: colors.RED1,
    textAlign: 'center',
    fontSize: FontSize.size14,
    textTransform: 'uppercase',
    paddingVertical: vScale(spacing.smaller),
  },
  subHeaderTitle: {
    fontWeight: '600',
    textAlign: 'left',
    color: colors.RED1,
    fontSize: FontSize.size14,
    marginLeft: scale(spacing.smaller),
    paddingVertical: vScale(spacing.smaller),
  },
  btnDanhGia: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtBtn: {
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    textDecorationLine: 'underline',
  },
  toggleHeaderBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
});
