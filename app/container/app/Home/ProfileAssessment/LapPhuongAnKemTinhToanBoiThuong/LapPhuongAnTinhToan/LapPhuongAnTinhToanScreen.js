import { NGHIEP_VU, SCREEN_ROUTER_APP } from '@app/commons/Constant';
import { colors } from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import { selectUser } from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { dimensions, moderateScale, spacing } from '@app/theme';
import { CustomActionSheet, Icon, ScreenComponent, Text } from '@component';
import React, { memo, useEffect, useRef, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Alert, RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import { NumericFormat } from 'react-number-format';
import { useSelector } from 'react-redux';
import styles from './LapPhuongAnTinhToanStyles';

const title = [
  '% Khấu hao', //0
  'Giảm trừ theo tỷ lệ giá trị tham gia', //1
  'Giảm trừ bồi thường (chế tài...)', //2
  'Giảm giá sửa chữa', //3
  'Khấu trừ / vụ', //4
  'Khấu trừ (ĐKBS)', //5
  'Thuế', //6
  'Đánh giá/Nhận xét', //7
  'Giảm trừ lỗi', //8
];
const OPTION_ACT_KHAUHAO = [title[3], title[0], title[8], title[2], title[1]];
// const OPTION_ACT_KHAUHAO = [title[3], title[0], title[2], title[1]];
const OPTION_ACT_KHAUHAO_TS_KHAC = [title[0], title[1], title[2], title[8]];
// const OPTION_ACT_KHAUTRU = [title[4], title[5], title[6]];
const OPTION_ACT_KHAUTRU = [title[4], title[5]];
const OPTION_ACT_NV_HHTX = [title[0], title[1], title[2], title[4], title[6]];
const OPTION_ACT_NV_CON_NGUOI = [title[1], title[2]];

const LapPhuongAnTinhToanComponent = ({ route, navigation }) => {
  console.log('LapPhuongAnTinhToanComponent');
  const { profileData, garaBaoGia } = route?.params;
  const userInfo = useSelector(selectUser);
  let scrollViewRef = useRef();
  // let actionSheetKhauTruRef = createRef(null);
  let refCustomActionSheetTSKhac = useRef(null);
  let refCustomActionSheetKhauTru = useRef(null);
  let refCustomActionSheetKhauHao = useRef(null);
  let refCustomActionSheetNvHangHoa = useRef(null);
  let refCustomActionSheetNvConNguoi = useRef(null);

  // const [tabIndex, setTabIndex] = useState(0);
  const [dataDanhGia, setDataDanhGia] = useState([]);
  const [nhomNguyeNhan, setNhomNguyenNhan] = useState([]);

  const [dlVCX, setDLVCX] = useState({});
  const [dlTongHop, setDlTongHop] = useState({});
  const [dlChiPhi, setDlChiPhi] = useState({});
  const [dlTNDSTaiSanKhac, setDlTNDSTaiSanKhac] = useState([]);
  const [dlTNDSTaiSanLaXe, setDlTNDSTaiSanLaXe] = useState([]);
  const [dlNVHangHoa, setDlNVHangHoa] = useState({});
  const [dlNVNguoiNgoiTrenXe, setDlNVNguoiNgoiTrenXe] = useState({});
  const [dlNVNguoiLaiPhuXe, setDlNVNguoiLaiPhuXe] = useState({});
  const [dlNVNguoiHanhKhach, setDlNVNguoiHanhKhach] = useState({});
  const [dlNVTNDSVeNguoi, setDlNVTNDSVeNguoi] = useState({});

  const [toggleNvVcx, setToggleNvVcx] = useState(true);
  // const [toggleNvHangHoa, setToggleNvHangHoa] = useState(false);
  // const [toggleHanhKhach, setToggleHanhKhach] = useState(false);
  const [toggleTNDSTaiSan, setToggleTNDSTaiSan] = useState(true);
  // const [toggleTNDSVeNguoi, setToggleTNDSVeNguoi] = useState(false);
  // const [toggleChiPhiCauKeo, setToggleChiPhiCauKeo] = useState(false);
  // const [toggleNvNguoiLaiPhuXe, setToggleNvNguoiLaiPhuXe] = useState(false);
  // const [toggleNvNguoiNgoiTrenXe, setToggleNvNguoiNgoiTrenXe] = useState(false);
  // const [loading, setLoading] = useState(false);
  const [lhnv, setLhnv] = useState('');
  const [actionType, setActionType] = useState('');
  const [idDoiTuong, setIdDoiTuong] = useState('');
  const [hangMuc, setHangMuc] = useState('');
  // const [soTienBTTruocThue, setSoTienBTTruocThue] = useState('');

  const [showNVVCX, setShowNVVCX] = useState(false);
  // const [showTNDSVeNguoi, setShowTNDSVeNguoi] = useState(false);
  const [showTNDSVeTaiSan, setShowTNDSVeTaiSan] = useState(false);
  // const [showTNDSHanhKhach, setShowTNDSHanhKhach] = useState(false);
  const [showNghiepVuHangHoa, setShowNghiepVuHangHoa] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  // const [showNvNguoiNgoiTrenXe, setShowNvNguoiNgoiTrenXe] = useState(false);
  // const [showNvLaiPhuXe, setShowNvLaiPhuXe] = useState(false);
  const [actionSheetData, setActionSheetData] = useState(OPTION_ACT_KHAUHAO);

  useEffect(() => {
    navigation.addListener('focus', () => {
      initData();
    });
  }, []);

  const initData = async () => {
    await getDuLieuTinhToan();
    await getChiTietDanhGia();
    getDanhMucSPXe();
  };
  const getChiTietDanhGia = async () => {
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
        lh_nv: profileData?.lh_nv[0].ma,
        pm: 'BT',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DANH_GIA, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDataDanhGia(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDuLieuTinhToan = async () => {
    try {
      setDialogLoading(true)
      let params = {
        so_id: profileData?.ho_so?.so_id,
        nguon: 'MOBILE',
        so_id_doi_tuong: garaBaoGia.so_id_doi_tuong,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DL_TINH_TOAN_PHUONG_AN_GIA, params);
      setDialogLoading(false)
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const { tong_hop, hhoa, lh_nv, chi_phi, tnds_tsan_khac, tnds_tsan_xe, nntx, lphu_xe, tnds_nguoi_hk, tnds_nguoi, vcx } = response.data_info;
      setDLVCX(vcx);
      setDlTongHop(tong_hop);
      setDlChiPhi(chi_phi);
      setDlTNDSTaiSanKhac(tnds_tsan_khac);
      setDlTNDSTaiSanLaXe(tnds_tsan_xe.filter((e) => e.so_id_doi_tuong === garaBaoGia.so_id_doi_tuong));
      setDlNVHangHoa(hhoa);
      setDlNVNguoiNgoiTrenXe(nntx);
      setDlNVNguoiLaiPhuXe(lphu_xe);
      setDlNVNguoiHanhKhach(tnds_nguoi_hk);
      setDlNVTNDSVeNguoi(tnds_nguoi);
      lh_nv.map((e) => {
        if (e.vcx === 'VCX' && e.doi_tuong === 'XE') {
          setShowNVVCX(true);
          setActionSheetData([title[3], title[0], title[2], title[1]]);
        }
        if (e.vcx === 'TNDS' && e.doi_tuong === 'TAI_SAN') {
          setShowTNDSVeTaiSan(true);
          setActionSheetData(OPTION_ACT_KHAUHAO);
        }
        if (e.vcx === 'HH' && e.doi_tuong === 'HANG_HOA') {
          setShowNghiepVuHangHoa(true);
          setActionSheetData(OPTION_ACT_KHAUHAO);
        }
        // if (e.vcx === 'TNDS' && e.doi_tuong === 'NGUOI') setShowTNDSVeNguoi(true);
        // if (e.vcx === 'TNDS' && e.doi_tuong === 'NGUOI_HK') setShowTNDSHanhKhach(true);
        // if (e.vcx === 'NNTX' && e.doi_tuong === 'NGUOI') setShowNvNguoiNgoiTrenXe(true);
        // if (e.vcx === 'LPHU_XE' && e.doi_tuong === 'NGUOI') setShowNvLaiPhuXe(true);
      });
    } catch (error) {
      setDialogLoading(false)
      Alert.alert('Thông báo', error.message);
    }
  };

  const getDanhMucSPXe = async () => {
    try {
      let params = { ma_doi_tac: profileData?.ho_so?.ma_doi_tac, nv: profileData.ho_so.nghiep_vu };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.GET_DANH_MUC_SAN_PHAM_XE, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info;
      const filter = data.filter((item) => {
        return item.nhom === 'NGUYEN_NHAN_GIAM_TRU' && item.nv === NGHIEP_VU.XE;
      });
      setNhomNguyenNhan(filter);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onSelectAction = (item, index, listTitle) => {
    onOpenModal(title[index], index, listTitle);
  };

  const onSelectActionTSKhac = (item, index, listTitle) => {
    const params = { profileData, lhnv, actionType, soIdDoiTuong: idDoiTuong };
    if (index == 4) return;
    // if (index == 3) return NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_THUE_TS_KHAC, params);
    // onOpenModal(title[index], index);
    tinhToanTNDSVeTaiSan(title[index], index, listTitle);
  };

  const onSelectActionNvHhtx = (item, index) => {
    const params = { profileData, lhnv, actionType, soIdDoiTuong: idDoiTuong, garaBaoGia };
    if (index == 5) return;
    if (index === 3) return NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_MIEN_THUONG, params);
    if (index == 4) return NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_THUE_TS_KHAC, params);
    onOpenModal(title[index], index);
  };

  const onSelectActionNvConNguoi = (item, index) => {
    if (index == 2) return;
    const params = { profileData, lhnv, actionType, soIdDoiTuong: idDoiTuong };
    if (index === 0) {
      NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_TRACH_NHIEM_BH, params);
    } else if (index === 1) {
      NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_TY_LE_GIAM_TRU, { ...params, ...{ nhomNguyeNhan: nhomNguyeNhan } });
    }
  };

  const onSelectActionKhauTru = (item, index, listTitle) => {
    // if (index == 3) return;
    const title = listTitle[index];
    const params = { profileData, lhnv, actionType, soIdDoiTuong: idDoiTuong, garaBaoGia };
    if (title === 'Khấu trừ / vụ') NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_MIEN_THUONG, params); // Khấu trừ/vụ
    if (title === 'Khấu trừ (ĐKBS)') NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_KHAU_TRU, params); // Khấu trừ (ĐKBS)
    // if (index == 2) {
    //   NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_THUE, params); //Tính toán thuế
    // }
  };

  const onOpenModal = (t, index, listTitle) => {
    const title = listTitle[index];
    closeActionSheets();
    setTimeout(() => {
      const params = { profileData, lhnv, actionType, soIdDoiTuong: idDoiTuong, garaBaoGia };
      // Tính toán khấu hao
      if (title === '% Khấu hao') NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_KHAU_HAO, params);
      //Giảm trừ theo tỷ lệ giá trị tham gia
      else if (title === 'Giảm trừ theo tỷ lệ giá trị tham gia') NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_TRACH_NHIEM_BH, params);
      // Giảm trừ bồi thường (chế tài)
      else if (title === 'Giảm trừ bồi thường (chế tài...)') NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_TY_LE_GIAM_TRU, { ...params, ...{ nhomNguyeNhan: nhomNguyeNhan } });
      //Giảm giá
      else if (title === 'Giảm giá sửa chữa') NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_GIAM_GIA, params);
      //Giảm trừ lỗi
      else if (title === 'Giảm trừ lỗi') NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_GIAM_TRU_LOI, params);
    }, 200);
  };
  const tinhToanTNDSVeTaiSan = (t, index, listTitle) => {
    const title = listTitle[index];
    closeActionSheets();
    setTimeout(() => {
      const params = { profileData, lhnv, actionType, soIdDoiTuong: idDoiTuong, garaBaoGia, hangMuc, screenTitle: t };
      if (title === '% Khấu hao') {
        NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_KHAU_HAO_TNDS_TAI_SAN, params); // Tính toán khấu hao
      } else if (title === 'Giảm trừ theo tỷ lệ giá trị tham gia') {
        NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_TRACH_NHIEM_BH_TNDS_TAI_SAN, params); //Giảm trừ theo tỷ lệ giá trị tham gia
      } else if (title === 'Giảm trừ bồi thường (chế tài...)') {
        NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_GIAM_TRU_TNDS_TAI_SAN, { ...params, ...{ nhomNguyeNhan: nhomNguyeNhan } }); // Giảm trừ bồi thường (chế tài)
        // NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_TY_LE_GIAM_TRU, {...params, ...{nhomNguyeNhan: nhomNguyeNhan}});
      }
      //  else if (index === 3) {
      //   NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_THUE_TNDS_TAI_SAN, params); // Tính toán thuế
      // }
      else if (title === 'Giảm trừ lỗi') {
        NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_GIAM_TRU_LOI_TS, params); // Tính toán thuế
      }
    }, 200);
  };

  // const onPressDanhGia = () => {
  //   NavigationUtil.push(SCREEN_ROUTER_APP.LPA_DANH_GIA_CUA_BTV, {profileData: profileData, dataDanhGia: dataDanhGia});
  // };

  const onPressCalculator = (type, loaiHinhNv, id, hangMuc) => {
    setLhnv(loaiHinhNv);
    setActionType(type);
    setIdDoiTuong(id);
    setHangMuc(hangMuc);
    if (type === 'vcx_khau_hao' || type === 'tslx_khau_hao') {
      refCustomActionSheetKhauHao.current.show();
    }
    if (type === 'vcx_khau_tru' || type === 'tslx_khau_tru') {
      refCustomActionSheetKhauTru.current.show();
    }
    if (type === 'tai_san_khac') {
      refCustomActionSheetTSKhac.current.show();
    }
    if (type === 'nv_hhtx') {
      refCustomActionSheetNvHangHoa.current.show();
    }
    if (type === 'nv_con_nguoi') {
      refCustomActionSheetNvConNguoi.current.show();
    }
    if (type === 'cau_keo') {
      NavigationUtil.push(SCREEN_ROUTER_APP.LPA_CHI_PHI_CAU_KEO, { profileData, garaBaoGia });
    }
  };

  // const onPressActGaraThuHo = () => {
  //   NavigationUtil.push(SCREEN_ROUTER_APP.LPA_CHUNG_TU_GARA_THU_HO, {profileData: profileData});
  // };

  const closeActionSheets = () => {
    refCustomActionSheetTSKhac.current.hide();
    refCustomActionSheetKhauHao.current.hide();
    refCustomActionSheetKhauTru.current.hide();
  };

  // const getTotal = (type) => {
  //   if (type === 'tien_bt') {
  //     let total = 0;
  //     if (showNVVCX && garaBaoGia.so_id_doi_tuong === dlVCX.so_id_doi_tuong) {
  //       let tienBoiThuongTruocThue = dlVCX.gia_duyet_dx - (dlVCX.tong_giam_tru + dlVCX.tong_khau_tru);
  //       total = tienBoiThuongTruocThue;
  //     } else {
  //       let sumGiaDxDuyet = 0;
  //       let sumTongGiamTru = 0;
  //       let sumTongKhauTru = 0;
  //       dlTNDSTaiSanLaXe.map((e) => {
  //         sumGiaDxDuyet += e.gia_duyet_dx;
  //         sumTongGiamTru += e.tong_giam_tru;
  //         sumTongKhauTru += e.tong_khau_tru;
  //       });
  //       total = sumGiaDxDuyet - (sumTongGiamTru + sumTongKhauTru);
  //     }
  //     return total;
  //   }
  //   if (type === 'tien_thue') {
  //     let total = 0;
  //     if (showNVVCX && garaBaoGia.so_id_doi_tuong === dlVCX.so_id_doi_tuong) {
  //       let tienThue = dlVCX.tong_thue;
  //       total = tienThue;
  //     } else {
  //       let sumTienThue = 0;
  //       dlTNDSTaiSanLaXe.map((e) => {
  //         sumTienThue += e.tong_thue;
  //       });
  //       total = sumTienThue;
  //     }
  //     return total;
  //   }
  //   if (type === 'tong_bt_vat') {
  //     let total = 0;
  //     if (showNVVCX && garaBaoGia.so_id_doi_tuong === dlVCX.so_id_doi_tuong) {
  //       let tienBoiThuongTruocThue = dlVCX.gia_duyet_dx - (dlVCX.tong_giam_tru + dlVCX.tong_khau_tru);
  //       let tienThue = dlVCX.tong_thue;
  //       total = tienBoiThuongTruocThue + tienThue;
  //     } else {
  //       let sumGiaDxDuyet = 0;
  //       let sumTongGiamTru = 0;
  //       let sumTongKhauTru = 0;
  //       let sumTienThue = 0;
  //       dlTNDSTaiSanLaXe.map((e) => {
  //         sumGiaDxDuyet += e.gia_duyet_dx;
  //         sumTongGiamTru += e.tong_giam_tru;
  //         sumTongKhauTru += e.tong_khau_tru;
  //         sumTienThue += e.tong_thue;
  //       });
  //       total = sumGiaDxDuyet - (sumTongGiamTru + sumTongKhauTru) + sumTienThue;
  //     }
  //     return total;
  //   }
  // };

  //RENDER
  const renderTitleRow = (stt, title, price, calc, bg, type, lhnv, id, hangMuc) => {
    return (
      <View style={[styles.titleRowView, bg && { backgroundColor: colors.WHITE1 }]}>
        <View style={styles.col1}>
          <Text style={styles.col1Txt}>{stt}</Text>
        </View>
        <View style={[styles.titleCol]}>
          <Text style={styles.title}>{title}</Text>
        </View>
        {price !== null ? (
          <View style={styles.priceCol}>
            <NumericFormat value={price} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.price, { fontWeight: '700' }]} />} />
          </View>
        ) : null}
        {calc !== null && (
          <TouchableOpacity onPress={() => onPressCalculator(type, lhnv, id, hangMuc)} style={[styles.priceCol, { marginRight: 10, borderLeftWidth: 0 }]}>
            <Icon.MaterialCommunityIcons name="calculator-variant" size={moderateScale(30)} color={colors.PRIMARY} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderSubRow = (stt, title, subTxt, price) => {
    return (
      <View style={[styles.titleRowView, { borderTopWidth: 0 }]}>
        <View style={styles.col1}>
          <Text style={styles.col1SubTxt}>{stt}</Text>
        </View>
        <View style={[styles.titleCol, styles.subLabelCol]}>
          <Text style={styles.subLabel}>{title}</Text>
          <Text style={[styles.col1SubTxt, { marginRight: spacing.tiny }]}>{subTxt}</Text>
        </View>
        <View style={styles.priceCol}>
          <NumericFormat value={price} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.price} />} />
        </View>
      </View>
    );
  };

  const renderFinalRow = (title, price, style, bg) => {
    return (
      <View style={[styles.titleRowView, style || { borderTopWidth: 0 }, bg && { backgroundColor: colors.WHITE1 }]}>
        <View style={[styles.titleCol, { width: dimensions.width - 120 }]}>
          <Text style={styles.title}>{title}</Text>
        </View>
        <View style={styles.priceCol}>
          <NumericFormat value={price} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={[styles.price, { fontWeight: '700' }]} />} />
        </View>
      </View>
    );
  };
  // const renderBtnDanhGia = (bg) => {
  //   const bgColor = dataDanhGia?.trang_thai === 'D' ? '#C8E6C9' : '#FFCDD2';

  //   return (
  //     <View style={[styles.titleRowView, bg && {backgroundColor: colors.WHITE1}]}>
  //       <TouchableOpacity style={[styles.btnDanhGia, {backgroundColor: bgColor}]} onPress={onPressDanhGia}>
  //         {dataDanhGia?.trang_thai === 'D' ? (
  //           <Icon.Ionicons name="checkmark-circle-sharp" size={moderateScale(18)} color={colors.GREEN} />
  //         ) : (
  //           <Icon.Ionicons name="warning" size={moderateScale(18)} color={colors.RED1} />
  //         )}
  //         <Text style={[styles.title, styles.txtBtn]}>Đánh giá/nhận xét</Text>
  //       </TouchableOpacity>
  //       <TouchableOpacity style={[styles.btnDanhGia]} onPress={() => onPressActGaraThuHo()}>
  //         <Icon.Feather name="file-text" size={moderateScale(18)} color={colors.PRIMARY} />
  //         <Text style={[styles.title, styles.txtBtn]}>Chứng từ, GARA thu hộ</Text>
  //       </TouchableOpacity>
  //     </View>
  //   );
  // };

  const renderTable = () => {
    return (
      <View>
        {/* Nghiệp vụ VCX */}
        {showNVVCX && garaBaoGia.so_id_doi_tuong === dlVCX.so_id_doi_tuong && renderNVVCX()}
        {/* Nghiệp vụ TNDS về tài sản khác */}
        {showTNDSVeTaiSan && renderNVTNDSVeTaiSanKhac(dlTNDSTaiSanKhac)}

        {/* Nghiệp vụ TNDS về tài sản là xe */}
        {dlTNDSTaiSanLaXe.length > 0 && renderNVTNDSVeTaiSanLaXe(dlTNDSTaiSanLaXe)}

        {/* Nghiệp vụ Hàng hoá trên xe */}
        {showNghiepVuHangHoa && renderNVTNDSHangHoaTrenXe(dlNVHangHoa)}

        {/* Nghiệp vụ người ngồi trên xe */}
        {/* {showNvNguoiNgoiTrenXe && renderNVNguoiNgoiTrenXe()} */}

        {/* Nghiệp vụ người - lái phụ xe */}
        {/* {showNvLaiPhuXe && renderNVNguoiLaiPhuXe()} */}

        {/* Nghiệp vụ người - hành khách trên xe */}
        {/* {showTNDSHanhKhach && renderNVNguoiHanhKhach()} */}

        {/* Nghiệp vụ TNDS về người */}
        {/* {showTNDSVeNguoi && renderNVTNDSVeNguoi()} */}

        {/* Chi phí cẩu/kéo/khác */}
        {/* {renderChiPhiCauKeoKhac()} */}
      </View>
    );
  };

  // render theo nghiệp vụ

  // TNDS tài sản khác
  const renderNVTNDSVeTaiSanKhac = (data) => {
    return (
      <View>
        <View style={styles.toggleHeaderBtn}>
          <Text style={styles.headerTitle}>Nghiệp vụ TNDS về tài sản khác</Text>
        </View>
        {data.map((e) => {
          return (
            <View>
              <Text style={styles.subHeaderTitle}>{e.ten_doi_tuong}</Text>
              {/* <TouchableOpacity style={styles.toggleHeaderBtn} onPress={() => setToggleTNDSTaiSan(!toggleTNDSTaiSan)}>
            <View width={20} />
            <Text style={styles.headerTitle}>Nghiệp vụ TNDS về tài sản</Text>
            <Icon.Feather name={toggleTNDSTaiSan ? 'chevron-down' : 'chevron-right'} size={20} color={colors.RED1} />
          </TouchableOpacity> */}
              {showTNDSVeTaiSan && (
                <View>
                  {/* 1 lớn */}
                  {renderTitleRow('I', 'Tổng tiền tổn thất (chưa VAT)', e?.gia_duyet_dx, null, 'bg')}
                  {/* 2 lớn */}
                  {renderTitleRow('II', 'Khấu hao, giảm trừ ', null, 'calc', null, 'tai_san_khac', e?.lh_nv, e?.so_id_doi_tuong, e?.hang_muc)}
                  {renderSubRow('1', 'Số tiền khấu hao', null, e?.tien_khau_hao)}
                  {renderSubRow('2', 'Giảm trừ theo tỷ lệ giá trị tham gia', null, e?.tien_bao_hiem)}
                  {renderSubRow('3', 'Giảm trừ bồi thường', null, e?.tien_giam_tru)}
                  {renderSubRow('4', 'Giảm trừ lỗi', null, e?.tien_giam_tru_loi)}
                  {renderTitleRow('', 'Tổng khấu hao, giảm trừ', e?.tong_giam_tru, null, 'bg')}
                  {renderTitleRow('', 'Tổng Khấu trừ theo HĐ, quy tắc BH', 0, null, 'bg')}
                </View>
              )}
            </View>
          );
        })}
      </View>
    );
  };
  // TNDS hàng hoá trên xe
  const renderNVTNDSHangHoaTrenXe = (data) => {
    return (
      <View>
        <View style={styles.toggleHeaderBtn}>
          <Text style={styles.headerTitle}>TNDS HÀNG HÓA TRÊN XE</Text>
        </View>
        <View>
          <Text style={styles.subHeaderTitle}>{data.ten_doi_tuong}</Text>
          <View>
            {/* 1 lớn */}
            {renderTitleRow('I', 'Tổng tiền tổn thất (chưa VAT)', data?.gia_duyet_dx, null, 'bg')}
            {/* 2 lớn */}
            {renderTitleRow('II', 'Khấu hao, giảm trừ ', null, 'calc', null, 'tai_san_khac', data?.lh_nv, data?.so_id_doi_tuong, data?.hang_muc)}
            {renderSubRow('1', 'Số tiền khấu hao', null, data?.tien_khau_hao)}
            {renderSubRow('2', 'Giảm trừ theo tỷ lệ giá trị tham gia', null, data?.tien_bao_hiem)}
            {renderSubRow('3', 'Giảm trừ bồi thường', null, data?.tien_giam_tru)}
            {renderSubRow('4', 'Giảm trừ lỗi', null, data?.tien_giam_tru_loi)}
            {renderTitleRow('', 'Tổng khấu hao, giảm trừ', data?.tong_giam_tru, null, 'bg')}
            {renderTitleRow('', 'Tổng Khấu trừ theo HĐ, quy tắc BH', 0, null, 'bg')}
          </View>
        </View>
      </View>
    );
  };

  // TNDS VCX
  const renderNVVCX = () => {
    const arrGiamGiaTruoc = [
      { label: 'Giảm giá phụ tùng thay thế', value: dlVCX?.tien_giam_gia_vtu, decimal: '%' },
      { label: 'Giảm giá chi phí nhân công sửa chữa', value: dlVCX?.tien_giam_gia_nhan_cong, decimal: '%' },
      { label: 'Giảm giá chi phí sơn', value: dlVCX?.tien_giam_gia_khac, decimal: '%' },
      { label: 'Khấu hao phụ tùng thay mới', value: dlVCX?.tien_khau_hao, decimal: '%' },
      // {label: 'Giảm trừ lỗi', value: dlVCX?.tien_giam_tru_loi, decimal: '%'}, //!không xoá
      { label: 'Giảm trừ bồi thường', value: dlVCX?.tien_giam_tru, decimal: '%' },
      { label: 'Giảm trừ theo tỷ lệ giá trị tham gia', value: dlVCX?.tien_bao_hiem, decimal: '%' },
    ];
    const arrGiamGiaSau = [
      { label: 'Khấu hao phụ tùng thay mới', value: dlVCX?.tien_khau_hao, decimal: '%' },
      // {label: 'Giảm trừ lỗi', value: dlVCX?.tien_giam_tru_loi, decimal: '%'}, //!không xoá
      { label: 'Giảm trừ bồi thường', value: dlVCX?.tien_giam_tru, decimal: '%' },
      { label: 'Giảm trừ theo tỷ lệ giá trị tham gia', value: dlVCX?.tien_bao_hiem, decimal: '%' },
      { label: 'Giảm giá phụ tùng thay thế', value: dlVCX?.tien_giam_gia_vtu, decimal: '%' },
      { label: 'Giảm giá chi phí nhân công sửa chữa', value: dlVCX?.tien_giam_gia_nhan_cong, decimal: '%' },
      { label: 'Giảm giá chi phí sơn', value: dlVCX?.tien_giam_gia_khac, decimal: '%' },
    ];

    let giamGia = 'S';
    giamGia = dlVCX?.lh_tt_giam_gia;
    return (
      <View>
        <View style={styles.toggleHeaderBtn}>
          <Text style={styles.headerTitle}>Nghiệp vụ vật chất xe</Text>
        </View>
        <Text style={styles.subHeaderTitle}>{dlVCX?.ten_doi_tuong}</Text>
        {toggleNvVcx && (
          <View>
            {/* 1 lớn */}
            {renderTitleRow('I', 'Tổng tiền duyệt giá (chưa VAT)', dlVCX?.gia_duyet_dx, null, 'bg', null)}
            {/* 2 lớn */}
            {renderTitleRow('II', 'Giảm giá, khấu hao, giảm trừ', null, 'calc', null, 'vcx_khau_hao', dlVCX?.lh_nv, dlVCX?.so_id_doi_tuong)}
            {giamGia === 'T' && (
              <>
                {arrGiamGiaTruoc.map((e, i) => {
                  return renderSubRow(i + 1, e.label, e.decimal, e.value);
                })}
              </>
            )}
            {giamGia === 'S' && (
              <>
                {arrGiamGiaSau.map((e, i) => {
                  return renderSubRow(i + 1, e.label, e.decimal, e.value);
                })}
              </>
            )}
            {renderTitleRow('', 'Tổng giảm giá, khấu hao, giảm trừ', dlVCX?.tong_giam_tru, null, 'bg')}
            {/* 3 lớn */}
            {renderTitleRow('III', 'Khấu trừ theo HĐ, quy tắc BH', null, 'calc', null, 'vcx_khau_tru', dlVCX?.lh_nv, dlVCX?.so_id_doi_tuong)}
            {renderSubRow('1', 'Khấu trừ theo % số tiền bồi thường', '', dlVCX?.tien_ktru_tien_bh)}
            {renderSubRow('2', 'Khấu trừ theo số tiền/vụ (m.thường)', '', dlVCX?.tien_mien_thuong)}
            {renderTitleRow('', 'Tổng Khấu trừ theo HĐ, quy tắc BH', dlVCX?.tong_khau_tru, null, 'bg')}
          </View>
        )}
      </View>
    );
  };
  // TNDS tài sản là xe
  const renderNVTNDSVeTaiSanLaXe = (data) => {
    return data.map((item) => {
      return (
        <View>
          <View style={styles.toggleHeaderBtn}>
            <Text style={styles.headerTitle}>Nghiệp vụ TNDS về tài sản</Text>
          </View>
          <Text style={styles.subHeaderTitle}>{item.loai_gcn + ':   ' + item.ten_doi_tuong}</Text>
          {/* 1 lớn */}
          {renderTitleRow('I', 'Tổng tiền duyệt giá (chưa VAT)', item?.gia_duyet_dx, null, 'bg')}
          {/* 2 lớn */}
          {renderTitleRow('II', 'Khấu hao, giảm trừ bt, giảm giá', null, 'calc', null, 'tslx_khau_hao', item.lh_nv, item.so_id_doi_tuong)}
          {renderSubRow('1', 'Giảm giá phụ tùng thay thế', '%', item?.tien_giam_gia_vtu)}
          {renderSubRow('2', 'Giảm giá chi phí nhân công sửa chữa', '%', item?.tien_giam_gia_nhan_cong)}
          {renderSubRow('3', 'Giảm giá chi phí sơn', '%', item?.tien_giam_gia_khac)}
          {renderSubRow('4', 'Khấu hao phụ tùng thay mới', '%', item.tien_khau_hao)}
          {renderSubRow('5', 'Giảm trừ lỗi', '%', item?.tien_giam_tru_loi)}
          {renderSubRow('6', 'Giảm trừ bồi thường', '%', item?.tien_giam_tru)}
          {renderSubRow('7', 'Giảm trừ theo tỷ lệ giá trị tham gia', '%', item.tien_bao_hiem)}
          {renderTitleRow('', 'Tổng khấu hao, giảm trừ, giảm giá', item?.tong_giam_tru, null, 'bg')}
          {/* 3 lớn */}
          {renderTitleRow('III', 'Khấu trừ theo HĐ, quy tắc BH', null, 'calc', null, 'tslx_khau_tru', item.lh_nv, item.so_id_doi_tuong)}
          {renderSubRow('1', 'Khấu trừ theo % số tiền bồi thường', '', item?.tien_ktru_tien_bh)}
          {renderSubRow('2', 'Khấu trừ theo số tiền/vụ (m.thường)', '', item?.tien_mien_thuong)}
          {renderTitleRow('', 'Tổng Khấu trừ theo HĐ, quy tắc BH', item?.tong_khau_tru, null, 'bg')}
        </View>
      );
    });
  };


  // NV người ngồi trên xe
  // const renderNVNguoiNgoiTrenXe = () => {
  //   return (
  //     <View>
  //       <TouchableOpacity style={styles.toggleHeaderBtn} onPress={() => setToggleNvNguoiNgoiTrenXe(!toggleNvNguoiNgoiTrenXe)}>
  //         <View width={20} />
  //         <Text style={styles.headerTitle}>Nghiệp vụ người ngồi trên xe</Text>
  //         <Icon.Feather name={toggleNvNguoiNgoiTrenXe ? 'chevron-down' : 'chevron-right'} size={20} color={colors.RED1} />
  //       </TouchableOpacity>
  //       {toggleNvNguoiNgoiTrenXe && (
  //         <View>
  //           {renderTitleRow('I', 'Tổng tiền tổn thất (chưa VAT)', dlNVNguoiNgoiTrenXe?.gia_duyet_dx, null, 'bg')}
  //           {renderTitleRow('II', '% trách nhiệm, giảm trừ bồi thường', null, 'calc', null, 'nv_con_nguoi', dlNVNguoiNgoiTrenXe?.lh_nv)}
  //           {renderSubRow('1', 'Giảm trừ theo tỷ lệ giá trị tham gia', null, dlNVNguoiNgoiTrenXe?.tien_bao_hiem)}
  //           {renderSubRow('2', 'Giảm trừ bồi thường', null, dlNVNguoiNgoiTrenXe?.tien_giam_tru)}
  //           {renderTitleRow('', 'Tổng khấu hao, giảm trừ', dlNVNguoiNgoiTrenXe?.tong_giam, null, 'bg')}
  //         </View>
  //       )}
  //     </View>
  //   );
  // };
  // NV người - lái phụ xe
  // const renderNVNguoiLaiPhuXe = () => {
  //   return (
  //     <View>
  //       <TouchableOpacity style={styles.toggleHeaderBtn} onPress={() => setToggleNvNguoiLaiPhuXe(!toggleNvNguoiLaiPhuXe)}>
  //         <View width={20} />
  //         <Text style={styles.headerTitle}>Nghiệp vụ người - Lái phụ xe</Text>
  //         <Icon.Feather name={toggleNvNguoiLaiPhuXe ? 'chevron-down' : 'chevron-right'} size={20} color={colors.RED1} />
  //       </TouchableOpacity>
  //       {toggleNvNguoiLaiPhuXe && (
  //         <View>
  //           {renderTitleRow('I', 'Tổng tiền tổn thất (chưa VAT)', dlNVNguoiLaiPhuXe?.gia_duyet_dx, null, 'bg')}
  //           {renderTitleRow('II', '% trách nhiệm, giảm trừ bồi thường', null, 'calc', null, 'nv_con_nguoi', dlNVNguoiLaiPhuXe?.lh_nv)}
  //           {renderSubRow('1', 'Giảm trừ theo tỷ lệ giá trị tham gia', null, dlNVNguoiLaiPhuXe?.tien_bao_hiem)}
  //           {renderSubRow('2', 'Giảm trừ bồi thường', null, dlNVNguoiLaiPhuXe?.tien_giam_tru)}
  //           {renderTitleRow('', 'Tổng khấu hao, giảm trừ', dlNVNguoiLaiPhuXe?.tong_giam, null, 'bg')}
  //         </View>
  //       )}
  //     </View>
  //   );
  // };
  // NV người - hành khách trên xe
  // const renderNVNguoiHanhKhach = () => {
  //   return (
  //     <View>
  //       <TouchableOpacity style={styles.toggleHeaderBtn} onPress={() => setToggleHanhKhach(!toggleHanhKhach)}>
  //         <View width={20} />
  //         <Text style={styles.headerTitle}>Nghiệp vụ người - Hành khách trên xe</Text>
  //         <Icon.Feather name={toggleHanhKhach ? 'chevron-down' : 'chevron-right'} size={20} color={colors.RED1} />
  //       </TouchableOpacity>
  //       {/* 1 lớn */}
  //       {toggleHanhKhach && (
  //         <View>
  //           {renderTitleRow('I', 'Tổng tiền tổn thất (chưa VAT)', dlNVNguoiHanhKhach?.gia_duyet_dx, null, 'bg')}
  //           {renderTitleRow('II', '% trách nhiệm, giảm trừ bồi thường', null, 'calc', null, 'nv_con_nguoi', dlNVNguoiHanhKhach?.lh_nv)}
  //           {renderSubRow('1', 'Giảm trừ theo tỷ lệ giá trị tham gia', null, dlNVNguoiHanhKhach?.tien_bao_hiem)}
  //           {renderSubRow('2', 'Giảm trừ bồi thường', null, dlNVNguoiHanhKhach?.tien_giam_tru)}
  //           {renderTitleRow('', 'Tổng khấu hao, giảm trừ', dlNVNguoiHanhKhach?.tong_giam, null, 'bg')}
  //         </View>
  //       )}
  //     </View>
  //   );
  // };
  // NV TNDS về người
  // const renderNVTNDSVeNguoi = () => {
  //   return (
  //     <View>
  //       <TouchableOpacity style={styles.toggleHeaderBtn} onPress={() => setToggleTNDSVeNguoi(!toggleTNDSVeNguoi)}>
  //         <View width={20} />
  //         <Text style={styles.headerTitle}>Nghiệp vụ TNDS về người</Text>
  //         <Icon.Feather name={toggleTNDSVeNguoi ? 'chevron-down' : 'chevron-right'} size={20} color={colors.RED1} />
  //       </TouchableOpacity>
  //       {toggleTNDSVeNguoi && (
  //         <View>
  //           {renderTitleRow('I', 'Tổng tiền tổn thất (chưa VAT)', dlNVTNDSVeNguoi?.gia_duyet_dx, null, 'bg')}
  //           {renderTitleRow('II', '% trách nhiệm, giảm trừ bồi thường', null, 'calc', null, 'nv_con_nguoi', dlNVTNDSVeNguoi?.lh_nv)}
  //           {renderSubRow('1', 'Giảm trừ theo tỷ lệ giá trị tham gia', null, dlNVTNDSVeNguoi?.tien_bao_hiem)}
  //           {renderSubRow('2', 'Giảm trừ bồi thường', null, dlNVTNDSVeNguoi?.tien_giam_tru)}
  //           {renderTitleRow('', 'Tổng khấu hao, giảm trừ', dlNVTNDSVeNguoi?.tong_giam, null, 'bg')}
  //         </View>
  //       )}
  //     </View>
  //   );
  // };
  //Chi phí cẩu kéo khác
  // const renderChiPhiCauKeoKhac = () => {
  //   return (
  //     <View>
  //       <TouchableOpacity style={styles.toggleHeaderBtn} onPress={() => setToggleChiPhiCauKeo(!toggleChiPhiCauKeo)}>
  //         <View width={20} />
  //         <Text style={styles.headerTitle}>Chi phí cẩu/kéo/khác</Text>
  //         <Icon.Feather name={toggleChiPhiCauKeo ? 'chevron-down' : 'chevron-right'} size={20} color={colors.RED1} />
  //       </TouchableOpacity>

  //       {toggleChiPhiCauKeo && (
  //         <View>
  //           {renderTitleRow('IV', 'Chi phí cẩu kéo (gồm giảm trừ nếu có)', null, 'calc', null, 'cau_keo')}
  //           {renderSubRow('1', 'Chi phí cẩu xe', null, dlChiPhi?.chi_phi_cau)}
  //           {renderSubRow('2', 'Chi phí kéo xe', null, dlChiPhi?.chi_phi_keo)}
  //           {renderSubRow('3', 'Chi phí khác', null, dlChiPhi?.chi_phi_khac)}
  //           {renderTitleRow('', 'Tổng chi phí cẩu kéo', dlChiPhi?.tong_chi_phi, null, 'bg')}
  //         </View>
  //       )}
  //     </View>
  //   );
  // };

  const renderActionSheetKhauHao = () => (
    <CustomActionSheet title="Tính toán" ref={refCustomActionSheetKhauHao} onSelected={(item, index) => onSelectAction(item, index, actionSheetData)} options={actionSheetData} />
  );

  const renderActionSheetKhauHaoTSKhac = () => (
    <CustomActionSheet
      title="Tính toán"
      ref={refCustomActionSheetTSKhac}
      onSelected={(item, index) => onSelectActionTSKhac(item, index, OPTION_ACT_KHAUHAO_TS_KHAC)}
      options={OPTION_ACT_KHAUHAO_TS_KHAC}
    />
  );

  const renderActionSheetKhauTru = () => (
    <CustomActionSheet title="Tính toán" ref={refCustomActionSheetKhauTru} onSelected={(item, index) => onSelectActionKhauTru(item, index, OPTION_ACT_KHAUTRU)} options={OPTION_ACT_KHAUTRU} />
  );

  const renderActionSheetNvHangHoa = () => (
    <CustomActionSheet title="Tính toán" ref={refCustomActionSheetNvHangHoa} onSelected={(item, index) => onSelectActionNvHhtx(item, index)} options={OPTION_ACT_NV_HHTX} />
  );

  const renderActionSheetNvConNguoi = () => (
    <CustomActionSheet title="Tính toán" ref={refCustomActionSheetNvConNguoi} onSelected={(item, index) => onSelectActionNvConNguoi(item, index)} options={OPTION_ACT_NV_CON_NGUOI} />
  );

  const renderFooter = () => {
    return (
      <View>
        <View style={styles.footerView}>
          <Text style={styles.headerTitle}>Số tiền bồi thường</Text>
          {/* {renderBtnDanhGia('Đánh giá', 'Chứng từ, Gara thu hộ (2)')} */}
          {renderFinalRow('STBT TRƯỚC THUẾ (I-II-III+IV)', dlTongHop.tong_tien_bt, { borderTopWidth: 0.5 })}
          {/* {renderFinalRow('THUẾ VAT', dlTongHop.tong_thue)} */}
          {renderFinalRow('TỔNG TIỀN KH CHI TRẢ (CHƯA VAT)', dlTongHop.tien_kh_chi_tra)}
          {/* {renderFinalRow('TỔNG TIỀN KH CHI TRẢ(CHƯA VAT)', dlTongHop?.tien_kh_chi_tra)} */}
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Lập phương án"
      renderView={
        <SafeAreaView style={styles.container}>
          {/* {renderBtnDanhGia()} */}
          <ScrollView
            tabLabel="Thông tin tính toán"
            scrollEnabled={true}
            ref={scrollViewRef}
            showsVerticalScrollIndicator={false}
            refreshControl={<RefreshControl refreshing={false} onRefresh={() => getDuLieuTinhToan()} />}>
            {renderTable()}
          </ScrollView>
          {renderActionSheetKhauHao()}
          {renderActionSheetKhauTru()}
          {renderActionSheetNvHangHoa()}
          {renderActionSheetNvConNguoi()}
          {renderActionSheetKhauHaoTSKhac()}
          {renderFooter()}
        </SafeAreaView>
      }
    />
  );
};

export const LapPhuongAnTinhToan = memo(LapPhuongAnTinhToanComponent, isEqual);
