import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import {selectCities} from '@app/redux/slices/CitiesSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ModalChiNhanhTheoDangCay, ScreenComponent, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, SafeAreaView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
import {ModalNguoiXuLy} from './Components';
import styles from './Styles';
import {isRequiredFieldXaPhuong} from '@app/commons/Constant';

const TaoLanGiamDinhBoSungScreenComponent = ({route, navigation}) => {
  console.log('TaoLanGiamDinhBoSungScreenComponent');

  const {lanGiamDinh, profileData} = route.params || {};
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const citiesData = useSelector(selectCities);

  const [openTinhThanh, setOpenTinhThanh] = useState(false);
  const [openQuanHuyen, setOpenQuanHuyen] = useState(false);
  const [listQuanHuyen, setListQuanHuyen] = useState([]);
  const [openXaPhuong, setOpenXaPhuong] = useState(false);
  const [listXaPhuong, setListXaPhuong] = useState([]);
  const [openListDoiTuong, setOpenListDoiTuong] = useState(false);
  const [openDonViXuLy, setOpenDonViXuLy] = useState(false);
  const [openNguoiXuLy, setOpenNguoiXuLy] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [toggleGioGiamDinh, setToggleGioGiamDinh] = useState(false);
  const [toggleNgayGiamDinh, setToggleNgayGiamDinh] = useState(false);
  const [listMaDonViXuLySeleted, setListMaDonViXuLySelected] = useState([]); //list mã đơn vị xử lý được chọn

  let refModalChiNhanhTheoDangCay = useRef(null);
  let refModalNguoiXuLy = useRef(null);

  useEffect(() => {
    initChiNhanhBaoHiemDangCay();
  }, []);
  useEffect(() => {
    if (listMaDonViXuLySeleted.length > 0) onChangeDonViGiamDinh('', '', listMaDonViXuLySeleted);
    else setListNguoiXuLy([]);
  }, [listMaDonViXuLySeleted]);

  const initChiNhanhBaoHiemDangCay = () => {
    // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
    let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay; //list chi nhánh mà nó quản lý
    chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
      return {
        ...item,
        listCon: [],
        isExpand: true,
        isCheck: false, //bỏ check or check
        hasChildCheck: false, //list chi nhánh cha, có child checked
        isShow: true,
      };
    });
    let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
    for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
      let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
      chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
    }
    refModalChiNhanhTheoDangCay.current.setData(chiNhanhBaoHiemCha);
  };

  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
    let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
    if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
    if (listConLai.length === 0) return [];
    else {
      for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
      return listConFilter;
    }
  };

  const getDefaultFormValue = () => {
    let donViGiamDinh = [];
    return {
      gioGiamDinh: new Date(),
      ngayGiamDinh: new Date(),
      tinhThanh: lanGiamDinh ? lanGiamDinh.tinhThanh : null,
      quanHuyen: lanGiamDinh ? lanGiamDinh.quanHuyen : null,
      xaPhuong: lanGiamDinh ? lanGiamDinh.xaPhuong : null,
      diaDiemChiTiet: lanGiamDinh ? lanGiamDinh.diaDiemChiTiet : '',
      ghiChu: lanGiamDinh ? lanGiamDinh.ghiChu : '',
      // donViGiamDinh: lanGiamDinh ? lanGiamDinh.donViGiamDinh : null,
      donViGiamDinh: donViGiamDinh,
      canBoGiamDinh: lanGiamDinh ? lanGiamDinh.canBoGiamDinh : null,
    };
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onSubmit = async (data) => {
    setDialogLoading(true);
    let canBoGiamDinh = listNguoiXuLy.find((item) => item.ma === data.canBoGiamDinh);
    try {
      let params = {
        gio_gd: moment(data.gioGiamDinh).format('HH:mm'),
        ngay_gd: +moment(data.ngayGiamDinh).format('YYYYMMDD'),
        so_id: profileData.ho_so.so_id,
        tinh_thanh: data.tinhThanh,
        quan_huyen: data.quanHuyen,
        phuong_xa: data.xaPhuong,
        dia_diem: data.diaDiemChiTiet,
        dvi_gdinh: canBoGiamDinh ? canBoGiamDinh.ma_chi_nhanh : '',
        ma_gdv: data.canBoGiamDinh,
        ghi_chu: data.ghiChu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TAO_MOI_LAN_GIAM_DINH_HS_TN, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tạo mới lần giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const gioGiamDinh = watch('gioGiamDinh');
  const ngayGiamDinh = watch('ngayGiamDinh');
  const canBoGiamDinh = watch('canBoGiamDinh');

  const onChaneTinhThanh = (title, items, value) => {
    citiesData.forEach((city) => {
      if (city.ma === value) {
        setListQuanHuyen(city.district);
        return;
      }
    });
  };

  const onChangeQuanHuyen = (title, items, value) => {
    listQuanHuyen.forEach((quanHuyen) => {
      if (quanHuyen.ma === value) {
        setListXaPhuong(quanHuyen.ward);
        return;
      }
    });
  };

  const onOpenDropdown = (type) => {
    type !== 0 && openTinhThanh && setOpenTinhThanh(false);
    type !== 1 && openQuanHuyen && setOpenQuanHuyen(false);
    type !== 2 && openXaPhuong && setOpenXaPhuong(false);
    type !== 3 && openListDoiTuong && setOpenListDoiTuong(false);
    type !== 4 && openDonViXuLy && setOpenDonViXuLy(false);
    type !== 5 && openNguoiXuLy && setOpenNguoiXuLy(false);

    if (type === 1 && listQuanHuyen.length === 0) setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    if (type === 2 && listXaPhuong.length === 0) {
      setError('quanHuyen', {type: 'required', message: 'Thông tin bắt buộc'});
      listQuanHuyen.length === 0 && setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    }
    if (type === 5 && listNguoiXuLy.length === 0 && !getValues('donViGiamDinh')) setError('donViGiamDinh', {type: 'required', message: 'Thông tin bắt buộc'});
  };

  const onChangeDonViGiamDinh = async (title, items, item) => {
    if (!item) return;

    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      ma_chi_nhanh: item.join(','),
    };
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let giamDinhVien = response.data_info.map((item) => {
        item.value = item.ma;
        item.label = item.ten + (item.ten_chuc_danh ? ` (${item.ten_chuc_danh})` : '');
        return item;
      });
      setListNguoiXuLy([...giamDinhVien]);
      setValue('canBoGiamDinh', null);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const getTenHienThi = (value, data = []) => {
    let name = '';
    data.map((e) => {
      if (e.ma === value) name = e.ten;
    });
    return name;
  };

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  // RENDER
  const renderThongTinGiamDinh = () => (
    <View zIndex={10000}>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin giám định" style={styles.subLabel} />
      </View>
      <View style={{marginHorizontal: spacing.small}}>
        <View flexDirection="row">
          <View flex={1} marginRight={10}>
            <Controller
              control={control}
              name="gioGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  isDateTimeField
                  isTouchableOpacity
                  editable={false}
                  value={moment(value).format('HH:mm')}
                  title="Giờ giám định"
                  onPress={() => setToggleGioGiamDinh(true)}
                  error={errors.gioVaoVien && getErrMessage('gioVaoVien', errors.gioVaoVien.type)}
                  inputStyle={{color: colors.BLACK}}
                />
              )}
            />
            {renderDateTimeComp(toggleGioGiamDinh, setToggleGioGiamDinh, (value) => setValue('gioGiamDinh', value), gioGiamDinh, 'time', null, null, 0)}
          </View>
          <View flex={1}>
            <Controller
              control={control}
              name="ngayGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  isDateTimeField
                  isTouchableOpacity
                  editable={false}
                  value={moment(value).format('DD/MM/YYYY')}
                  title="Ngày giám định"
                  onPress={() => setToggleNgayGiamDinh(true)}
                  error={errors.ngayGiamDinh && getErrMessage('ngayGiamDinh', errors.ngayGiamDinh.type)}
                  inputStyle={{color: colors.BLACK}}
                />
              )}
            />
            {renderDateTimeComp(toggleNgayGiamDinh, setToggleNgayGiamDinh, (value) => setValue('ngayGiamDinh', value), ngayGiamDinh, 'date', null, new Date(), 0)}
          </View>
        </View>

        <Controller
          control={control}
          name="tinhThanh"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title="Tỉnh thành"
              placeholder="Chọn tỉnh thành"
              zIndex={9999}
              isOpen={openTinhThanh}
              setOpen={setOpenTinhThanh}
              items={citiesData}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              isRequired
              onChangeValue={onChaneTinhThanh}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(0)}
              inputErr={errors.tinhThanh && getErrMessage('tinhThanh', errors.tinhThanh.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="quanHuyen"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title="Quận huyện"
              placeholder="Chọn quận huyện"
              zIndex={8888}
              isOpen={openQuanHuyen}
              setOpen={setOpenQuanHuyen}
              items={listQuanHuyen}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              isRequired
              onChangeValue={onChangeQuanHuyen}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(1)}
              inputErr={errors.quanHuyen && getErrMessage('quanHuyen', errors.quanHuyen.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="xaPhuong"
          rules={{
            required: isRequiredFieldXaPhuong,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              isRequired={isRequiredFieldXaPhuong}
              title="Xã phường"
              placeholder="Chọn xã phường"
              zIndex={7777}
              isOpen={openXaPhuong}
              setOpen={setOpenXaPhuong}
              items={listXaPhuong}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(2)}
              inputErr={errors.xaPhuong && getErrMessage('xaPhuong', errors.xaPhuong.type)}
            />
          )}
        />

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="diaDiemChiTiet"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Địa điểm chi tiết"
              placeholder="Nhập địa điểm chi tiết"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.diaDiemChiTiet && getErrMessage('diaDiemChiTiet', errors.diaDiemChiTiet.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="ghiChu"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView, {zIndex: 4000}]}
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              value={value}
              onChangeText={onChange}
              returnKeyType={'next'}
              blurOnSubmit={false}
              multiline={true}
              numberOfLines={3}
            />
          )}
        />
      </View>
    </View>
  );
  const renderThongTinCanBoGiamDinh = () => (
    <View zIndex={6666}>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin cán bộ giám định" style={styles.subLabel} />
      </View>
      <View marginHorizontal={spacing.small} zIndex={6000}>
        <Controller
          control={control}
          name="donViGiamDinh"
          rules={{required: false}}
          render={({field: {onChange, value}}) => (
            <>
              <TextInputOutlined
                isTouchableOpacity
                editable={false}
                isDropdown
                title="Đơn vị giám định"
                value={value.length === 0 ? 'Chọn đơn vị giám định' : value.length === 1 ? value[0].ten_chi_nhanh : `Có ${value.length} đơn vị được chọn`}
                error={errors.donViGiamDinh && getErrMessage('donViGiamDinh', errors.donViGiamDinh.type)}
                placeholder="Chọn đơn vị giám định"
                onPress={() => refModalChiNhanhTheoDangCay.current.show()}
                inputStyle={{color: colors.BLACK}}
              />
            </>
          )}
        />

        <Controller
          control={control}
          name="canBoGiamDinh"
          rules={{
            required: false,
          }}
          render={({field: {value, onChange}}) => {
            let cleared = value !== null && value !== '';
            return (
              <TextInputOutlined
                cleared={cleared}
                isDropdown
                isTouchableOpacity
                onPressClear={() => setValue('canBoGiamDinh', null)}
                editable={false}
                title="Cán bộ giám định hiện trường"
                placeholder="Chọn cán bộ giám định"
                inputStyle={{color: colors.BLACK}}
                value={getTenHienThi(value, listNguoiXuLy)}
                onPress={() => refModalNguoiXuLy.current.show()}
              />
            );
          }}
        />
      </View>
    </View>
  );
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Tạo lần giám định bổ sung"
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
            {renderThongTinGiamDinh()}
            {renderThongTinCanBoGiamDinh()}
          </KeyboardAwareScrollView>

          <ModalChiNhanhTheoDangCay
            ref={refModalChiNhanhTheoDangCay}
            showCheckCha={true}
            multiple={false}
            setListMaDonViXuLySelected={setListMaDonViXuLySelected}
            setListItemDonViXulySelected={(value) => setValue('donViGiamDinh', value, {shouldValidate: true})}
          />
          <ModalNguoiXuLy
            value={canBoGiamDinh}
            data={listNguoiXuLy}
            ref={refModalNguoiXuLy}
            onBackPress={() => refModalNguoiXuLy.current.hide()}
            setValue={(val) => setValue('canBoGiamDinh', val.ma, {shouldValidate: true})}
          />
        </SafeAreaView>
      }
      footer={<ButtonLinear title="Lưu" onPress={handleSubmit(onSubmit)} />}
    />
  );
};

export const TaoLanGiamDinhBoSungScreen = memo(TaoLanGiamDinhBoSungScreenComponent, isEqual);
