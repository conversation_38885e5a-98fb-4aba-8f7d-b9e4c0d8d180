import {colors} from '@app/commons/Theme';
import {spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    // width: dimensions.width,
    marginTop: spacing.small,
    paddingBottom: 50,
    // flex: 1,
  },

  dropDownTitle: {
    marginBottom: spacing.small,
    fontWeight: 'bold',
  },
  dropDownView: {
    marginVertical: spacing.small,
    // flex : 1,
    // flexDirection: 'row',
    // justifyContent: 'flex-start',
  },
  headerView: {
    borderBottomWidth: 1,
    borderBottomColor: colors.WHITE6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.small,
  },
  txtHeader: {
    fontSize: 17,
    fontWeight: '900',
  },
  iconClose: {
    color: colors.BLACK,
    opacity: 0.5,
  },
  txtHeaderInput: {
    marginBottom: spacing.small,
    fontWeight: 'bold',
    // color: colors.BLACK
  },
  input: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 5,
    paddingLeft: spacing.small,
    color: colors.BLACK,
    paddingVertical: spacing.small,
    backgroundColor: colors.WHITE,
  },
  inputView: {
    marginBottom: spacing.small,
    flex: 1,
  },
  btn: {
    flex: 1,
    // borderWidth: 1,
    borderRadius: 30,
    // height:
  },

  btnView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    paddingHorizontal: spacing.small,
  },
  txtBtn: {
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.small,
    textAlign: 'center',
    color: colors.WHITE,
    fontSize: 16,
  },
  btnClose: {
    backgroundColor: colors.GRAY9,
    marginRight: spacing.small,
  },
  btnSucess: {
    backgroundColor: colors.GRAY9,
  },
  subLabel: {
    fontSize: 16,
    marginVertical: spacing.tiny,
    fontWeight: '700',
    color: colors.PRIMARY,
    paddingHorizontal: spacing.small,
  },
  headerTitleView: {
    marginBottom: 2,
    paddingVertical: 8,
    alignItems: 'center',
    flexDirection: 'row',
    // paddingHorizontal: 10,
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
  },
});
