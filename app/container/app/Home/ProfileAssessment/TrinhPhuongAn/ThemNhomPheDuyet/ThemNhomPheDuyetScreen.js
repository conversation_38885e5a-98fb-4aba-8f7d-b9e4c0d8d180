import {colors} from '@app/commons/Theme';
import {selectChiNhanhBaoHiem} from '@app/redux/slices/CategoryCommonSlice';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {ButtonLinear, CheckboxComp, DropdownPicker, Icon, ScreenComponent, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useFieldArray, useForm} from 'react-hook-form';
import {Alert, FlatList, TextInput, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';
import {ModalChonChiNhanh} from './Components';
import {LIST_LOAI_TRINH, LIST_NGHIEP_VU, LIST_NHOM_TRINH_PHAN_CAP, LIST_TO_LOAI_TRINH} from './Constant';
import styles from './ThemNhomPheDuyetStyles';
const ThemNhomPheDuyetScreenComponent = ({route}) => {
  console.log('ThemNhomPheDuyetScreenComponent', route);
  const chiNhanhBaoHiem = useSelector(selectChiNhanhBaoHiem);
  const userInfo = useSelector(selectUser);
  const {profileInfo} = route.params;
  let refModalChonChiNhanh = useRef(null);
  const insect = useSafeAreaInsets();

  const [isLoading, setIsLoading] = useState(false);
  const [openNghiepVu, setOpenNghiepVu] = useState(false);
  const [openNgayApDung, setOpenNgayApDung] = useState(false);
  const [openLoaiTrinh, setOpenLoaiTrinh] = useState(false);
  const [openLoaiToTrinh, setOpenLoaiToTrinh] = useState(false);
  const [openNhomTrinhPhanCap, setOpenNhomTrinhPhanCap] = useState(false);

  const [listCanBoDuyet, setListCanBoDuyet] = useState([]);
  useEffect(() => {}, []);

  const getDefaultFormValue = () => {
    return {
      chiNhanh: [],
      tenNhom: '',
      nghiepVu: LIST_NGHIEP_VU[0].value,
      ngayApDung: moment().toDate(),
      loaiTrinh: LIST_LOAI_TRINH[0].value,
      loaiToTrinh: '',
      tuDong: '',
      nhomTrinhPhanCap: LIST_NHOM_TRINH_PHAN_CAP[0].value,
      trangThai: '',
      chiNhanhDuyet: '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const watchChiNhanhSelected = watch('chiNhanh');
  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const closeDropdown = (title) => {};
  const onPressDateConfirm = (date, setToggleDateTime, setDate, mode) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const onPressItemChiNhanh = async (item) => {
    try {
      if (watchChiNhanhSelected.indexOf(item.ma) < 0) {
        watchChiNhanhSelected.push(item.ma);
        setIsLoading(true);
        // lấy GĐV theo chi nhánh
        let params = {
          ma_doi_tac: profileInfo.ma_doi_tac,
          ma_chi_nhanh: item.ma,
        };
        let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
        setIsLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        let listCanBoDuyetNew = [];
        response.data_info.forEach((item) => {
          listCanBoDuyetNew.push({
            ...item,
            checked: false,
            thuTu: '',
            thuTuDuyet: '',
            duyetChinhChecked: false,
          });
        });
        setListCanBoDuyet([...listCanBoDuyet.concat(listCanBoDuyetNew)]);
      } else {
        watchChiNhanhSelected.splice(watchChiNhanhSelected.indexOf(item.ma), 1);
        let listCanBoDuyetTmp = listCanBoDuyet;
        listCanBoDuyetTmp = listCanBoDuyetTmp.filter((canBo) => canBo.ma_chi_nhanh !== item.ma);
        setListCanBoDuyet([...listCanBoDuyetTmp]);
      }
      setValue('chiNhanh', watchChiNhanhSelected);
    } catch (error) {
      Alert.alert(error.message);
      setIsLoading(false);
    }
  };
  const getChiNhanhByMa = (ma) => chiNhanhBaoHiem.find((chiNhanh) => chiNhanh.ma === ma);

  const onPressLuu = async (data) => {
    console.log('onPressLuu', data);
    try {
      let params = {
        so_id: profileInfo.so_id,
        loai_trinh: data.loaiToTrinh,
        nghiep_vu: profileInfo.nghiep_vu,
        ten_nhom: data.tenNhom,
        loai: data.loaiTrinh,
        phan_cap: data.nhomTrinhPhanCap,
        nhom_ca_nhan: userInfo.nguoi_dung.nsd,
        arr_ma_chi_nhanh_duyet: data.chiNhanh,
        arr_nsd: listCanBoDuyet.filter((canBo) => canBo.checked === true).map((item) => item.nsd),
        arr_stt: listCanBoDuyet.filter((canBo) => canBo.checked === true).map((item) => +item.thuTu),
        arr_thu_tu_duyet: listCanBoDuyet.filter((canBo) => canBo.checked === true).map((item) => +item.thuTuDuyet),
        arr_phe_duyet: listCanBoDuyet
          .filter((canBo) => canBo.checked === true)
          .map((item) => {
            return item.duyetChinhChecked ? 1 : 0;
          }),
      };
      console.log('params', params);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TAO_NHOM_TRINH_PHE_DUYET_CA_NHAN, params);
      console.log('response', response);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
    } catch (error) {}
  };
  const onPressChonCanBoDuyet = (index) => {
    let listCanBoDuyetTmp = listCanBoDuyet;
    listCanBoDuyetTmp[index].checked = !listCanBoDuyetTmp[index].checked;
    setListCanBoDuyet([...listCanBoDuyetTmp]);
  };
  const onPressChonCanBoDuyetChinh = (viTriDuyetChinh) => {
    let listCanBoDuyetTmp = listCanBoDuyet;
    listCanBoDuyetTmp.map((canBo, index) => {
      canBo.duyetChinhChecked = index !== viTriDuyetChinh ? false : true;
      return canBo;
    });
    setListCanBoDuyet([...listCanBoDuyetTmp]);
  };
  const onChangeThuTuCanBoDuyet = (index, value) => {
    let listCanBoDuyetTmp = listCanBoDuyet;
    listCanBoDuyetTmp[index].thuTu = value;
    setListCanBoDuyet([...listCanBoDuyetTmp]);
  };
  const onChangeThuTuDuyet = (index, value) => {
    let listCanBoDuyetTmp = listCanBoDuyet;
    listCanBoDuyetTmp[index].thuTuDuyet = value;
    setListCanBoDuyet([...listCanBoDuyetTmp]);
  };

  // RENDER
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type)}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderItemChiNhanhSelected = ({item}) => {
    return (
      <View style={styles.itemChiNhanhSelectedView}>
        <Text children={getChiNhanhByMa(item).ten} style={{flex: 1, marginRight: spacing.small}} />
        <TouchableOpacity onPress={() => onPressItemChiNhanh({ma: item})}>
          <Icon.AntDesign name="closesquareo" size={20} color={colors.RED1} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItemCanBoDuyet = ({item, index}) => {
    return (
      <View style={styles.itemCanBoDuyetView}>
        <TouchableOpacity style={{width: dimensions.width / 10, alignItems: 'center'}} onPress={() => onPressChonCanBoDuyet(index)}>
          <CheckboxComp value={item.checked} disabled={true} style={styles.checkboxRowCanBo} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.tenCanBoView} onPress={() => onPressChonCanBoDuyet(index)}>
          <Text children={item.ten} font="regular12" style={styles.txtTenCanBo} numberOfLines={1} ellipsizeMode="middle" />
        </TouchableOpacity>
        <View style={{width: dimensions.width / 6}}>
          <TextInput
            value={item.thuTu}
            style={[styles.inputThuTu, {marginHorizontal: spacing.tiny}]}
            font="regular12"
            keyboardType="numeric"
            onChangeText={(value) => onChangeThuTuCanBoDuyet(index, value)}
            maxLength={3}
          />
        </View>
        <View style={{width: dimensions.width / 5}}>
          <TextInput
            value={item.thuTuDuyet}
            style={[styles.inputThuTu, {marginHorizontal: spacing.tiny}]}
            font="regular12"
            keyboardType="numeric"
            onChangeText={(value) => onChangeThuTuDuyet(index, value)}
            maxLength={3}
          />
        </View>
        <TouchableOpacity style={{width: dimensions.width / 5, alignItems: 'center'}} onPress={() => onPressChonCanBoDuyetChinh(index)}>
          <CheckboxComp value={item.duyetChinhChecked} style={styles.checkboxRowCanBo} disabled={true} />
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <ScreenComponent
      headerBack
      dialogLoading={isLoading}
      headerTitle="Thêm nhóm phê duyệt"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView style={{flex: 1, marginBottom: 70, marginTop: spacing.small}} showsVerticalScrollIndicator={false}>
            <View style={{marginHorizontal: spacing.small, zIndex: 9000}}>
              {/* TÊN NHÓM */}
              <Controller
                control={control}
                name="tenNhom"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    title="Tên nhóm"
                    value={value}
                    onFocus={closeDropdown}
                    onChangeText={onChange}
                    placeholder="Nhập tên nhóm"
                    isRequired={true}
                    error={errors.tenNhom && getErrMessage('tenNhom', errors.tenNhom.type)}
                    containerStyle={{marginBottom: 0}}
                  />
                )}
              />
              {/* NGHIỆP VỤ */}
              {/* <Controller
                control={control}
                name="nghiepVu"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title="Nghiệp vụ"
                    placeholder="Chọn nghiệp vụ"
                    zIndex={9000}
                    isOpen={openNghiepVu}
                    setOpen={setOpenNghiepVu}
                    items={LIST_NGHIEP_VU}
                    isRequired
                    searchable={false}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    containerStyle={{marginBottom: spacing.tiny}}
                    // onOpen={() => onOpenDropdown(0)}
                    inputErr={errors.nghiepVu && getErrMessage('nghiepVu', errors.nghiepVu.type)}
                  />
                )}
              /> */}
              {/* NGÀY ÁP DỤNG */}
              <Controller
                control={control}
                name="ngayApDung"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <>
                    <TextInputOutlined
                      value={moment(value).format('DD/MM/YYYY')}
                      editable={false}
                      isRequired={true}
                      isTouchableOpacity
                      title="Ngáy áp dụng"
                      inputStyle={{color: colors.BLACK_03}}
                      onPress={() => setOpenNgayApDung(true)}
                      error={errors.ngayApDung && getErrMessage('ngayApDung', errors.ngayApDung.type)}
                      containerStyle={{marginBottom: 0}}
                      isDateTimeField
                    />
                    {renderDateTimeComp(openNgayApDung, setOpenNgayApDung, onChange, value, 'date', new Date(), null, 0)}
                  </>
                )}
              />

              {/* LOẠI TRÌNH  */}
              <Controller
                control={control}
                name="loaiTrinh"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title="Loại trình"
                    placeholder="Chọn loại trình"
                    zIndex={8000}
                    isOpen={openLoaiTrinh}
                    setOpen={setOpenLoaiTrinh}
                    items={LIST_LOAI_TRINH}
                    isRequired
                    searchable={false}
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    containerStyle={{marginBottom: spacing.tiny}}
                    // onOpen={() => onOpenDropdown(0)}
                    inputErr={errors.loaiTrinh && getErrMessage('loaiTrinh', errors.loaiTrinh.type)}
                  />
                )}
              />
              {/* LOẠI TỜ TRÌNH  */}
              <Controller
                control={control}
                name="loaiToTrinh"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title="Loại tờ trình"
                    placeholder="Chọn loại tờ trình"
                    zIndex={7000}
                    isOpen={openLoaiToTrinh}
                    setOpen={setOpenLoaiToTrinh}
                    items={LIST_TO_LOAI_TRINH}
                    isRequired
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    containerStyle={{marginBottom: spacing.tiny}}
                    // onOpen={() => onOpenDropdown(0)}
                    inputErr={errors.loaiToTrinh && getErrMessage('loaiToTrinh', errors.loaiToTrinh.type)}
                  />
                )}
              />

              {/* NHÓM TRÌNH PHÂN CẤP */}
              <Controller
                control={control}
                name="nhomTrinhPhanCap"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title="Nhóm trình phân cấp"
                    placeholder="Chọn nhóm trình phân cấp"
                    zIndex={6000}
                    isOpen={openNhomTrinhPhanCap}
                    setOpen={setOpenNhomTrinhPhanCap}
                    items={LIST_NHOM_TRINH_PHAN_CAP}
                    isRequired
                    itemSelected={value}
                    searchable={false}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    containerStyle={{marginBottom: spacing.tiny}}
                    // onOpen={() => onOpenDropdown(0)}
                    inputErr={errors.nhomTrinhPhanCap && getErrMessage('nhomTrinhPhanCap', errors.nhomTrinhPhanCap.type)}
                  />
                )}
              />

              <View>
                <TouchableOpacity style={styles.chiNhanhTitleView} onPress={() => refModalChonChiNhanh.current.show()}>
                  <Text style={styles.txtChiNhanh}>
                    Chi nhánh <Text children="(*)" style={{color: colors.RED1}} />
                  </Text>
                  <View style={styles.btnChonView}>
                    <Text children="Chọn" style={styles.txtChon} />
                  </View>
                </TouchableOpacity>

                <FlatList
                  data={watchChiNhanhSelected}
                  scrollEnabled={false}
                  keyExtractor={(item) => item}
                  renderItem={renderItemChiNhanhSelected}
                  ListEmptyComponent={
                    <TouchableOpacity style={styles.listChiNhanhSelectedEmptyView} onPress={() => refModalChonChiNhanh.current.show()}>
                      <Text children="Chọn chi nhánh" />
                      <Icon.Entypo style={styles.iconDropdown} name="chevron-thin-down" size={14} color={colors.BLACK} />
                    </TouchableOpacity>
                  }
                />
              </View>
            </View>
            <View>
              <View style={[styles.chiNhanhTitleView, {marginHorizontal: spacing.small}]}>
                <Text style={styles.txtChiNhanh}>
                  Cán bộ duyệt <Text children="(*)" style={{color: colors.RED1}} />
                </Text>
              </View>
              <FlatList
                data={listCanBoDuyet}
                scrollEnabled={false}
                keyExtractor={(item) => item.ma}
                renderItem={renderItemCanBoDuyet}
                ListHeaderComponent={
                  <View style={styles.headerCanBoDuyet}>
                    <Text children="Chọn" style={[styles.txtHeaderCanBoDuyet, {width: dimensions.width / 10}]} font="bold12" numberOfLines={2} />
                    <Text children="Tên" style={[styles.txtHeaderCanBoDuyet, {flex: 1}]} font="bold12" numberOfLines={2} />
                    <Text children="Thứ tự" style={[styles.txtHeaderCanBoDuyet, {width: dimensions.width / 6}]} font="bold12" numberOfLines={2} />
                    <Text children="Thứ tự duyệt" style={[styles.txtHeaderCanBoDuyet, {width: dimensions.width / 5}]} font="bold12" numberOfLines={2} />
                    <Text children="Duyệt chính" style={[styles.txtHeaderCanBoDuyet, {width: dimensions.width / 5}]} font="bold12" numberOfLines={2} />
                  </View>
                }
                ListEmptyComponent={
                  <View style={styles.listCanBoDuyetEmptyView} onPress={() => refModalChonChiNhanh.current.show()}>
                    <Text children="Danh sách trống" />
                  </View>
                }
              />
            </View>

            <ModalChonChiNhanh
              ref={refModalChonChiNhanh}
              chiNhanhBaoHiem={chiNhanhBaoHiem}
              onPressItemChiNhanh={onPressItemChiNhanh}
              watchChiNhanhSelected={watchChiNhanhSelected}
              isLoading={isLoading}
            />
          </KeyboardAwareScrollView>
          <View style={[styles.footerView]}>
            <ButtonLinear title="Tạo mới" onPress={handleSubmit(onPressLuu)} linearStyle={{marginHorizontal: spacing.small}} />
          </View>
        </View>
      }
    />
  );
};

export const ThemNhomPheDuyetScreen = memo(ThemNhomPheDuyetScreenComponent, isEqual);
