import {NGHIEP_VU} from '@constant';
export const LIST_NGHIEP_VU = [
  {
    label: 'Xe ô tô',
    value: NGHIEP_VU.XE,
  },
];

export const LIST_LOAI_TRINH = [
  {
    label: 'Đồng thời',
    value: 'DT',
  },
  {
    label: 'Tuần tự',
    value: 'TT',
  },
];

export const LIST_TO_LOAI_TRINH = [
  {
    label: 'Trình duyệt báo cáo giám định',
    value: 'XE_TRINH_DUYET_BAO_CAO_GD',
  },
  {
    label: 'Trình duyệt biên bản giám định',
    value: 'XE_TRINH_DUYET_GIAM_DINH',
  },
  {
    label: 'Trình duyệt duyệt giá',
    value: 'XE_TRINH_DUYET_DUYET_GIA',
  },
  {
    label: 'Trình duyệt bảo lãnh',
    value: 'XE_TRINH_DUYET_BAO_LANH',
  },
  {
    label: 'Trình duyệt bồi thường',
    value: 'XE_TRINH_DUYET_BOI_THUONG',
  },
  {
    label: 'Trình duyệt từ chối bồi thường',
    value: 'XE_TRINH_DUYET_TU_CHOI',
  },
  {
    label: 'Trình duyệt tạm ứng bồi thường',
    value: 'XE_TRINH_DUYET_TAM_UNG_BT',
  },
  {
    label: 'Trình duyệt thu hồi vật tư',
    value: 'XE_TRINH_DUYET_THU_HOI_VAT_TU',
  },
  {
    label: 'Trình duyệt thu đòi người thứ 3',
    value: 'XE_TRINH_DUYET_THU_DOI_NTBA',
  },
  {
    label: 'Trình duyệt chi phí cứu hộ, cẩu kéo',
    value: 'XE_TRINH_DUYET_CHI_PHI_CUU_HO',
  },
  {
    label: 'Trình duyệt hồ sơ giám định',
    value: 'TRINH_DUYET_HO_SO_GIAM_DINH',
  },
  {
    label: 'Trình giám định, bồi thường hộ',
    value: 'TRINH_GDINH_BTH',
  },
];

export const LIST_NHOM_TRINH_PHAN_CAP = [
  {
    label: 'Trình nội bộ',
    value: 'K',
  },
  {
    label: 'Trình trên phân cấp',
    value: 'C',
  },
];
