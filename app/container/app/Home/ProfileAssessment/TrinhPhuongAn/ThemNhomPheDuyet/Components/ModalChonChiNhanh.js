import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing} from '@app/theme';
import {CheckboxComp, DialogLoading, Empty, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const ModalChonChiNhanhComponent = forwardRef(({chiNhanhBaoHiem, onPressItemChiNhanh, watchChiNhanhSelected, isLoading}, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: show,
      hide: hide,
    }),
    [],
  );
  const [isVisible, setIsVisible] = useState(false);
  const insect = useSafeAreaInsets();

  const show = () => setIsVisible(true);
  const hide = () => setIsVisible(false);
  const [searchInput, setSearchInput] = useState('');
  const getListChiNhanhBaoHiem = () => {
    if (!searchInput.trim()) return chiNhanhBaoHiem;
    return chiNhanhBaoHiem.filter((item) => item.ten.toUpperCase().indexOf(searchInput.toUpperCase()) > -1);
  };
  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Chọn chi nhánh" />
        <TouchableOpacity style={styles.closeView} onPress={hide}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = (item, index) => {
    let isCheck = watchChiNhanhSelected.includes(item.ma);
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItemChiNhanh(item)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} disabled={true} />
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK_03, flex: 1}}>{item.ten}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View>{getListChiNhanhBaoHiem().length > 0 ? <View>{getListChiNhanhBaoHiem().map((item, index) => renderItem(item, index))}</View> : <Empty imageStyle={styles.imageStyles} />}</View>
      </ScrollView>
    );
  };
  const renderSearchView = () => {
    return (
      <View>
        <SearchBar value={searchInput} onTextChange={setSearchInput} />
      </View>
    );
  };
  return (
    <Modal
      style={[styles.modal]}
      isVisible={isVisible}
      // propagateSwipe={true}
      //   swipeDirection={['']}
      //  onSwipeComplete={hide}
      onBackdropPress={hide}
      onBackButtonPress={hide}
      avoidKeyboard>
      <View style={[styles.modalView, {paddingBottom: insect.bottom}]}>
        {renderHeader()}
        {renderSearchView()}
        {renderContent()}
      </View>
      {isLoading && <DialogLoading />}
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: scale(spacing.small),
    borderColor: colors.GRAY,
    height: 40,
    margin: scale(spacing.medium),
    borderRadius: 25,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: scale(spacing.medium),
  },
  itemHangMucView: {
    padding: scale(spacing.tiny),
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.smaller,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: FontSize.size16,
    marginLeft: scale(30),
    fontWeight: 'bold',
    marginVertical: scale(spacing.medium),
    textAlign: 'center',
  },
  closeView: {
    marginRight: spacing.small,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: spacing.small,
  },
  content: {
    margin: spacing.small,
  },
  imageStyles: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
});

export const ModalChonChiNhanh = ModalChonChiNhanhComponent;
