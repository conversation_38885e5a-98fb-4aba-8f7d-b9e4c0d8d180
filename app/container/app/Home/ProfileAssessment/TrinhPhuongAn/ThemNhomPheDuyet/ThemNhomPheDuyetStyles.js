import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  txtChiNhanh: {
    fontWeight: 'bold',
    marginBottom: spacing.tiny,
  },
  txtChon: {
    color: '#FFF',
  },
  chiNhanhTitleView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.small,
  },
  btnChonView: {
    backgroundColor: colors.PRIMARY,
    paddingVertical: spacing.tiny,
    paddingHorizontal: spacing.small,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemChiNhanhSelectedView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing.tiny,
  },
  listChiNhanhSelectedEmptyView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 10,
    paddingLeft: scale(spacing.smaller),
    paddingVertical: vScale(spacing.small),
    paddingRight: scale(spacing.small),
    marginTop: spacing.tiny,
  },

  listCanBoDuyetEmptyView: {
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 10,
    paddingVertical: vScale(spacing.small),
    paddingRight: scale(spacing.small),
    marginTop: spacing.tiny,
    marginHorizontal: spacing.tiny,
  },
  checkbox: {
    marginRight: spacing.small,
  },
  txtHeaderCanBoDuyet: {
    // flex: 1,
    color: colors.PRIMARY,
    textAlign: 'center',
    // borderWidth: 1,
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.tiny,
    textAlignVertical: 'center',
  },
  headerCanBoDuyet: {
    flexDirection: 'row',
    // borderWidth: 1,
    alignItems: 'center',
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  itemCanBoDuyetView: {
    flexDirection: 'row',
    marginVertical: spacing.tiny,
    alignItems: 'center',
  },
  txtTenCanBo: {},
  tenCanBoView: {
    flex: 1,
    justifyContent: 'center',
  },
  inputThuTu: {
    borderBottomWidth: 0.5,
    textAlign: 'center',
    borderColor: '#999',
  },
  checkboxRowCanBo: {
    width: isIOS ? 15 : 20,
    height: isIOS ? 15 : 20,
  },
  footerView: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: spacing.small,
  },
});
