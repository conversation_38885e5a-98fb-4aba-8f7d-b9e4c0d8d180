import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {FontSize, dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  pdf: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
    backgroundColor: colors.WHITE,
  },
  title: {
    fontWeight: '700',
  },
  footerView: {
    width: dimensions.width,
    paddingVertical: 10,
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
    paddingHorizontal: spacing.small,
  },

  row: {
    flexDirection: 'row',
    marginBottom: spacing.small,
  },
  item: {
    padding: spacing.smaller,
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: spacing.tiny,
    borderColor: colors.GRAY,
  },
  label: {
    color: colors.GRAY6,
  },
  content: {
    flex: 1,
    fontWeight: '600',
    color: colors.BLACK_03,
  },
  btnThemNhomPheDuyetView: {flexDirection: 'row', backgroundColor: colors.BLUE3_07, paddingVertical: spacing.tiny, paddingHorizontal: spacing.smaller, borderRadius: 5},
  chonNhomPheDuyetView: {flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'},
  trinhPhuongAnView: {
    flex: 1,
    marginHorizontal: spacing.small,
  },
  txtThemNhom: {
    marginLeft: spacing.tiny,
    color: '#FFF',
  },
  modal: {
    margin: 0,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
    paddingTop: isIOS ? 50 : 10,
  },
  btnDoiMauInView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: spacing.medium,
    paddingVertical: spacing.small,
  },
  btnDoiMauIn: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtTenMauIn: {
    color: colors.PRIMARY,
    fontSize: FontSize.size16,
    fontWeight: '600',
  },
  txtBtnDoiMauIn: {
    marginRight: 10,
    color: colors.PRIMARY,
  },
});
