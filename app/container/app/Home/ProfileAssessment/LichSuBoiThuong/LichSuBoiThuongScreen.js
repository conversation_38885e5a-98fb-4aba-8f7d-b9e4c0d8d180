import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, spacing, vScale} from '@app/theme';
import {Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import styles from './LichSuBoiThuongStyles';

const LichSuBoiThuongScreenComponent = (props) => {
  console.log('LichSuBoiThuongScreenComponent');
  const {route} = props;
  const {profileData} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [lichSuBoiThuong, setLichSuBoiThuong] = useState([]);

  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    let params = {
      so_id: profileData.ho_so.so_id,
      so_id_hd: profileData.ho_so.so_id_hd,
      so_id_dt: profileData.ho_so.so_id_dt,
    };
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_LICH_SU_BOI_THUONG_OTO, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setLichSuBoiThuong(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  /**RENDER  */
  const renderLichSuBoiThuongItem = ({item, index}) => {
    index = lichSuBoiThuong.length - index;
    const renderLabel = (label, value) => {
      return (
        <View style={styles.contentRow}>
          <Text style={styles.subLabel}>{label}:</Text>
          {typeof value === 'number' ? (
            <NumericFormat value={item.tien} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={{flex: 1}}>{' ' + val}</Text>} />
          ) : (
            <Text style={styles.content}>{value}</Text>
          )}
        </View>
      );
    };
    return (
      <View style={styles.resolveItemView}>
        <View flexDirection="row" alignItems="center">
          <View style={styles.verticalLineStep}>
            <Text style={{color: colors.WHITE, fontSize: FontSize.size11}}>{index}</Text>
          </View>
          <View style={styles.titleView}>
            {item?.so_hs.trim() !== '' ? <Text style={styles.title}>{item?.so_hs}</Text> : <Text style={[styles.title, {color: colors.BLACK_03}]}>{'Hồ sơ chưa lấy số'}</Text>}
            <Text style={styles.date}>Thời gian: {item.ngay_ht}</Text>
          </View>
        </View>
        <View marginTop={vScale(10)} style={styles.contentColumn}>
          {renderLabel('Số tiền', item.tien)}
          {renderLabel('Trạng thái', item.trang_thai)}
        </View>
      </View>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Lịch sử bồi thường"
      renderView={
        <View style={styles.container}>
          <FlatList
            style={{paddingTop: spacing.small}}
            data={lichSuBoiThuong}
            renderItem={renderLichSuBoiThuongItem}
            keyExtractor={(item, index) => item.data + index.toString()}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={<Empty />}
          />
        </View>
      }
    />
  );
};

export const LichSuBoiThuongScreen = memo(LichSuBoiThuongScreenComponent, isEqual);
