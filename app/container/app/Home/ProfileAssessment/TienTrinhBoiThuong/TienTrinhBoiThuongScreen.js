import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Al<PERSON>, FlatList, SafeAreaView, View} from 'react-native';
import styles from './Styles';

const TienTrinhBoiThuongScreenComponent = (props) => {
  console.log('TienTrinhBoiThuongScreenComponent');
  const {route} = props;
  const {profileData} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [data, setData] = useState([]);

  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData.ho_so.so_id,
        ma_doi_tac: '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_THONG_TIN_TIEN_TRINH_BOI_THUONG, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setData(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  /**RENDER  */
  const renderLichSuBoiThuongItem = ({item, index}) => {
    const renderLabel = (label, value) => {
      return (
        <View style={styles.contentRow}>
          <Text style={styles.subLabel}>{label}:</Text>
          <Text style={styles.content}>{value}</Text>
        </View>
      );
    };
    return (
      <View style={styles.resolveItemView}>
        <View flexDirection="row" alignItems="center" marginBottom={10}>
          <View style={styles.verticalLineStep}>
            <Text font="regular10" style={{color: colors.WHITE}}>
              {index + 1}
            </Text>
          </View>
          <View style={styles.titleView}>
            <Text style={styles.title}>Bước: {item.ten}</Text>
            <Text style={styles.date}>Ngày thực hiện: {item.ngay_hthi}</Text>
          </View>
        </View>
        <View style={styles.contentColumn}>
          {renderLabel('Cán bộ thực hiện', item.nsd)}
          {renderLabel('Thời gian SLA (phút)', item.tgian)}
          {renderLabel('Thời gian thực hiện (phút)', item.kq)}
          <Text style={styles.subLabel}>
            Đánh giá: {item.dgia === 'D' ? <Text style={[styles.content, {color: colors.GREEN}]}>Đạt</Text> : <Text style={[styles.content, {color: colors.RED1}]}>Chưa đạt</Text>}
          </Text>
        </View>
      </View>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Thông tin tiến trình bồi thường"
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            style={styles.flStyles}
            data={data}
            renderItem={renderLichSuBoiThuongItem}
            keyExtractor={(item, index) => item.data + index.toString()}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={<Empty />}
          />
        </SafeAreaView>
      }
    />
  );
};

export const TienTrinhBoiThuongScreen = memo(TienTrinhBoiThuongScreenComponent, isEqual);
