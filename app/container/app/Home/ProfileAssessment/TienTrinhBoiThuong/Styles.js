import {colors} from '@app/commons/Theme';
import {FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  resolveItemView: {
    marginHorizontal: scale(spacing.smaller),
  },
  verticalLine: {
    flex: 1,
    borderWidth: 1,
    borderStyle: 'dotted',
    borderColor: '#00ACC1',
  },
  verticalLineStep: {
    width: 22,
    height: 22,
    backgroundColor: colors.PRIMARY,
    borderWidth: 1,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: 'white',
  },
  contentColumn: {
    borderLeftWidth: 1,
    borderColor: colors.GRAY,
    marginLeft: scale(spacing.small),
    marginBottom: vScale(spacing.medium),
  },
  title: {
    marginBottom: 4,
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  body: {
    flex: 1,
    fontSize: 15,
    color: colors.BLACK_03,
    lineHeight: 20,
    // marginRight: 8,
  },
  date: {
    fontSize: FontSize.size12,
    color: colors.GRAY6,
  },
  subLabel: {
    fontWeight: '400',
    fontSize: FontSize.size14,
    color: colors.GRAY6,
    marginLeft: scale(20),
  },
  content: {
    fontWeight: '400',
    fontSize: FontSize.size14,
    color: colors.PRIMARY,
    marginLeft: scale(spacing.tiny),
  },
  titleView: {
    marginLeft: 10,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: vScale(spacing.tiny),
  },
  flStyles: {
    paddingVertical: spacing.smaller,
  },
});
