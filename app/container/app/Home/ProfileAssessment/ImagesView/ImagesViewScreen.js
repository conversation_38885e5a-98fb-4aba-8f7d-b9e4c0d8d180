import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing, vScale} from '@app/theme';
import {ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import ImageZoom from 'react-native-image-pan-zoom';
import ImageProcess from 'react-native-image-progress';
import Progress from 'react-native-progress/Circle';
import Carousel from 'react-native-snap-carousel';
import styles from './ImagesViewStyles';

const indicatorProps = {
  size: 70,
  borderWidth: 1,
  borderRadius: 10,
  borderColor: '#EDF5EC',
  color: '#EDF5EC',
  unfilledColor: '#30C29A',
};
const extensionsFile = ['.pdf', '.doc', '.docx', '.xml', '.xls', '.xlsx']; //đuôi mở rộng của file
const extensionsImage = ['.jpg', '.jpeg', '.png', '.gif'];

const ImagesViewScreenComponent = (props) => {
  console.log('ImagesViewScreenComponent');
  const route = props.route;

  const [imageSlide, setImagesSlide] = useState([]);

  const [imageSelectedPosition, setImageSelectedPosition] = useState(-1);
  const [imageSelected, setImageSelected] = useState({});
  const [imagesData, setImagesData] = useState([]);
  let flatListRef = useRef();
  const [scroll, setScroll] = useState(true);
  let imageZoomRef = useRef();
  const scaleValue = useRef(1);
  let carouselRef = useRef();
  const [firstLoad, setFirstLoad] = useState(true);

  const [imgIndex, setImgIndex] = useState(imageSelectedPosition);
  const refContainer = useRef(null);

  useEffect(() => {
    setTimeout(() => {
      if (imgIndex > -1 && refContainer?.current && imagesData?.length && imgIndex < imagesData.length) {
        refContainer?.current?.scrollToIndex({animated: true, index: imgIndex >= 2 ? imgIndex - 2 : imgIndex});
      }
    }, 100);
  }, [imgIndex]);

  useEffect(() => {
    initDataImage();

    // route.params.imagesData.map((imageItem, index) => {
    //   console.log("extension",index)
    //   if (imageItem.bt == route.params.currentImageData.item.bt) {
    //     console.log("index",index,item.)
    //     setImageSelectedPosition(index);
    //   }
    // });
    // getDetailImage(route.params.currentImageData.item, route.params.currentImageData.index);
  }, []);

  const initDataImage = () => {
    let imageSlideTmp = [];
    let imageDataTmp = [];
    route.params.imagesData.map((item) => {
      if (extensionsFile.includes(item.extension)) return;
      imageSlideTmp.push(item.duong_dan);
      imageDataTmp.push(item);
    });
    imageDataTmp.map((item, index) => {
      if (item.bt === route.params.currentImageData.item.bt) {
        setImageSelectedPosition(index);
        setImgIndex(index);
      }
    });
    setImagesSlide([...imageSlideTmp]);
    setImagesData([...imageDataTmp]); //data ảnh thumbnail
  };
  //lấy chi tiết của 1 ảnh
  const getDetailImage = async (imageData, index) => {
    // return;
    //nếu ảnh chưa lấy nét
    // if (!imageData.isDetail) {
    let params = {
      so_id: imageData.so_id,
      bt: imageData.bt,
    };
    try {
      let response = await ESmartClaimEndpoint.getFile(axiosConfig.ACTION_CODE.DOCUMENT_DETAIL, params);
      console.log('response', response);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setImageSelected(response.data_info);
      if (imagesData.length > 0) {
        let imagesDataTmp = imagesData;
        let imageSlideTmp = imageSlide;
        imagesDataTmp[index].isDetail = true; //lưu là đã lấy nét ảnh
        imageSlideTmp[index] = response.data_info.duong_dan;
        setImagesData([...imagesDataTmp]);
        setImagesSlide([...imageSlideTmp]);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    // } else {
    // let imageSlideTmp = imageSlide;
    // imageSlideTmp[index] = imageData.duong_dan;
    // setImagesSlide([...imageSlideTmp]);
    // setImageSelected(imageData);
    // }
  };

  useEffect(() => {
    if (imageSelectedPosition < 0) return;
    getDetailImage(imagesData[imageSelectedPosition], imageSelectedPosition);
  }, [imageSelectedPosition]);

  /** RENDER */
  const renderCarouselItem = (data) => {
    // console.log('renderCarouselItem', data);
    // return;

    let imageHeight = dimensions.height - dimensions.width / 2 - 30;
    return (
      <View style={{justifyContent: 'center', alignItems: 'center'}}>
        <Text children={data.index + 1 + ' - ' + imagesData[data.index].nhom_anh} style={styles.imageName} />
        {/* <TouchableOpacity onPress={() => imageZoomRef.resetScale()} style={{position: 'absolute', top: 20, left: 50}}>
          <Text children="reset" />
        </TouchableOpacity> */}
        <ImageZoom
          cropWidth={dimensions.width}
          cropHeight={imageHeight}
          imageWidth={dimensions.width}
          imageHeight={imageHeight}
          // style={{marginTop: 50}}
          ref={(ref) => {
            // if (data.index == imageSelectedPosition) imageZoomRef = ref;
          }}>
          <FastImage
            source={
              {
                uri: `data:image/gif;base64,${data.item}`,
              } || R.images.img_no_data
            }
            style={{
              width: dimensions.width,
              height: imageHeight,
            }}
            resizeMode="contain"
          />

          {/* <Image
            source={{
              uri: `data:image/gif;base64,${data.item}`,
            }}
            style={{
              width: width,
              height: imageHeight,
            }}
            resizeMode={'contain'}
            loadingIndicatorSource
          /> */}
          {/* <ImageProcess
          source={{uri: `data:image/gif;base64,${data.item}`}}
          indicator={Progress.Circle}
          style={imageStyle}
          imageStyle={{resizeMode: 'contain', borderRadius: 10}}
          indicatorProps={indicatorProps}
          renderError={() => (
            <View>
              <Image
                source={R.images.img_no_image}
                style={{
                  width: width - 10,
                  height: width - 10,
                  borderRadius: 10,
                }}
                resizeMode={'contain'}
              />
            </View>
          )}
        /> */}
        </ImageZoom>
      </View>
    );
    return (
      <ImageProcess
        source={{uri: `data:image/gif;base64,${data.item}`}}
        indicator={Progress.Circle}
        style={imageStyle}
        imageStyle={{resizeMode: 'cover', borderRadius: 10}}
        indicatorProps={indicatorProps}
        renderError={() => (
          <View>
            <Image
              source={R.images.img_no_image}
              style={{
                width: width - 50,
                height: width - 50,
                borderRadius: 10,
              }}
              resizeMode={'contain'}
            />
          </View>
        )}
      />
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle={'Ảnh giám định'}
      renderView={
        <View style={styles.container}>
          {/* <View style={{flex: 1}}> */}
          {/* <ImageViewer imageUrls={imageSlide} /> */}
          {/* </View> */}

          {imageSelectedPosition != -1 && (
            <Carousel
              ref={(c) => (carouselRef = c)}
              data={imageSlide}
              renderItem={renderCarouselItem}
              sliderWidth={dimensions.width}
              itemWidth={dimensions.width}
              getItemLayout={(data, index) => ({
                length: dimensions.width,
                offset: dimensions.width * index,
                index,
              })}
              initialScrollIndex={imageSelectedPosition}
              firstItem={imageSelectedPosition} //vị trí hiển thị đầu tiên
              // loop={true}
              onSnapToItem={(slideIndex) => {
                // console.log('slideIndex', slideIndex);
                setImgIndex(slideIndex);
                setImageSelectedPosition(slideIndex);
              }}
              onLayout={(data) => {
                // console.log('onLayout', data);
              }}
            />
          )}

          {/* <ImageSlider
            style={{...styles.imageSliderView}}
            images={imageSlide}
            position={imageSelectedPosition}
            customSlide={({index, item, style, width}) => {
              // if (index == 0) return;
              return (
                <View>
                  <ImageZoom cropWidth={width} cropHeight={height - width / 2 - 30} imageWidth={width} imageHeight={height - width / 2 - 30}>
                    <Image
                      source={{
                        uri: `data:image/gif;base64,${item}`,
                      }}
                      style={{
                        width: width,
                        height: height - width / 2 - 30,
                      }}
                      resizeMode={'contain'}
                    />
                  </ImageZoom>
                </View>
              );
            }}
            onPositionChanged={(index) => {
              console.log('onPositionChanged', index);
              setImageSelectedPosition(index);
            }}
            customButtons={(position, move) => (
              <View style={styles.btnSlideView}>
                {imageSlide.map((image, index) => {
                  let dotStyle = [styles.dotSlide];
                  if (index == position) dotStyle.push(styles.btnSlideViewActive);
                  return <View style={dotStyle} />;
                })}
              </View>
            )}
          /> */}

          {/* <View>
            <ImageZoom cropWidth={width} cropHeight={height - width / 2 - 30} imageWidth={width} imageHeight={height - width / 2}>
              <Image
                source={{
                  uri: `data:image/gif;base64,${imageSelected.duong_dan}`,
                }}
                style={{
                  width: width,
                  height: height - width / 2,
                }}
                resizeMode={'contain'}
              />
            </ImageZoom>
          </View> */}

          <View>
            <FlatList
              style={{marginVertical: vScale(spacing.small)}}
              data={imagesData}
              horizontal
              ref={refContainer}
              keyExtractor={(item, index) => index.toString()}
              // getItemLayout={(data, index) => ({
              //   length: dimensions.width / 6,
              //   offset: (dimensions.width / 6) * index,
              //   index,
              // })}
              // initialScrollIndex={imagesData.length > 6 ? route.params.currentImageData.index : null}
              onScrollToIndexFailed={(error) => {
                refContainer?.current?.scrollToOffset({offset: error.averageItemLength * error.index, animated: true});
                setTimeout(() => {
                  if (imagesData.length !== 0 && refContainer !== null) {
                    refContainer?.current?.scrollToIndex({index: error.index, animated: true});
                  }
                }, 100);
              }}
              renderItem={({item, index}) => {
                // if (index == 0) return;
                return (
                  <TouchableOpacity
                    key={index}
                    underlayColor={colors.GRAY}
                    onPress={() => {
                      setImageSelected(item);
                      setImgIndex(index);
                      setImageSelectedPosition(index);
                      carouselRef.snapToItem(index, false);
                    }}
                    activeOpacity={0.7}>
                    <View
                      style={[
                        {
                          marginHorizontal: 3,
                          borderColor: colors.WHITE,
                          borderWidth: 2,
                        },
                        imageSelectedPosition == index && {
                          borderColor: colors.BLUE6,
                        },
                      ]}>
                      <Image
                        source={{
                          uri: `data:image/gif;base64,${item.duong_dan}`,
                        }}
                        style={{
                          width: dimensions.width / 6,
                          height: dimensions.width / 6,
                          //  borderRadius: 20,
                        }}
                        resizeMode={'contain'}
                      />
                    </View>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        </View>
      }
    />
  );
};

export const ImagesViewScreen = memo(ImagesViewScreenComponent, isEqual);
