import {colors} from '@app/commons/Theme';
import {StyleSheet, Dimensions, Platform} from 'react-native';
const {width, height} = Dimensions.get('screen');
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
export default StyleSheet.create({
  container: {
    flex: 1,
    // marginTop: Platform.OS == 'ios' ? getStatusBarHeight() : 0,
  },
  content: {},
  imageSliderView: {
    // height: height - 100,
    // justifyContent: 'center',
    backgroundColor: 'transparent',
    width: width,
    // borderWidth: 1,
    flex: 1,
  },
  dotSlide: {
    borderRadius: 10,
    width: 10,
    height: 10,
    backgroundColor: colors.PRIMARY,
    marginHorizontal: 4,
    opacity: 0.2,
  },
  btnSlideViewActive: {
    opacity: 1,
  },
  btnSlideView: {
    // position: 'absolute',
    // bottom: 40,
    // left: 20,
    flexDirection: 'row',
    // borderWidth: 1,
    justifyContent: 'center',
    marginBottom: 20,
  },
  imageName: {
    fontSize: 14,
    marginTop: 20,
    // position: 'absolute',
    // left: 0,
    // top: 0,
    // right: 0,
    textAlign: 'center',
  },
});
