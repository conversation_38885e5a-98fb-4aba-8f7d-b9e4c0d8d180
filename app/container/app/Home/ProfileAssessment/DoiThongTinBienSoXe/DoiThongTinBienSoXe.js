import {colors} from '@app/commons/Theme';
import {ButtonLinear, ModalSelectSimple, ScreenComponent, TextInputOutlined} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalHangXe, ModalHieuXe, ModalLoaiXe, ModalNamSanXuat} from './Components';
import {LIST_MUC_DICH_SU_DUNG} from './Constant';
import styles from './DoiThongTinBienSoXeStyles';
import {spacing} from '@app/theme';

function DoiThongTinBienSoXeScreenComponent({route}) {
  console.log('DoiThongTinBienSoXeScreenComponent');

  const {profileInfo, type} = route.params;
  const [loading, setLoading] = useState(false);
  const [dsHangXe, setDsHangXe] = useState([]);
  const [dsHieuXe, setDsHieuXe] = useState([]);
  const [dsHieuXeRoot, setdsHieuXeRoot] = useState([]);
  const [dsLoaiXeRoot, setdsLoaiXeRoot] = useState([]);
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      tenChuXe: profileInfo?.chu_xe ? profileInfo?.chu_xe : '',
      sdt: profileInfo?.dthoai_chu_xe ? profileInfo?.dthoai_chu_xe : '',
      soKhung: profileInfo?.so_khung ? profileInfo?.so_khung : '',
      soMay: profileInfo?.so_may ? profileInfo?.so_may : '',
      bsx: profileInfo?.doi_tuong ? profileInfo?.doi_tuong : '',
      hangXe: profileInfo?.hang_xe ? profileInfo?.hang_xe : '',
      hieuXe: profileInfo?.hieu_xe ? profileInfo?.hieu_xe : '',
      loaiXe: profileInfo?.loai_xe ? profileInfo?.loai_xe : '',
      namSx: profileInfo?.nam_sx ? profileInfo?.nam_sx : '',
      mucDichSuDung: profileInfo?.md_sd ? profileInfo?.md_sd : '',
    },
  });

  useEffect(() => {
    layDsHangXe();
    layDanhMucLoaiXe();
    layDsHieuXe();
  }, []);

  const hangXe = watch('hangXe');
  const hieuXe = watch('hieuXe');
  const namSx = watch('namSx');
  const loaiXe = watch('loaiXe');
  const mdsd = watch('mucDichSuDung');

  // useEffect(() => {}, [hangXe]);

  let refModal = useRef(null);
  let refModalHieuXe = useRef(null);
  let refModalNamSx = useRef(null);
  let refModalLoaiXe = useRef(null);
  let refModalMucDichSd = useRef(null);

  useEffect(() => {
    if (dsHangXe?.length > 0 && hangXe !== '') {
      let arr = dsHieuXeRoot.filter((e) => e.hang_xe === hangXe);
      setDsHieuXe(arr);
    }
  }, [hangXe, dsHangXe, dsHieuXeRoot]);

  const onSelectHangXe = (val) => {
    setValue('hangXe', val.ma, {shouldValidate: true});
    setValue('hieuXe', '');
  };

  const getErrMessage = (errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const getTextHienThi = (val, data) => {
    let text = '';
    data.map((e) => {
      if (e.ma === val) {
        text = e.ten;
      }
      if (e.value === val) {
        text = e.label;
      }
    });
    return text;
  };

  const layDsHangXe = async () => {
    try {
      setLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HANG_XE, {nv: profileInfo.nghiep_vu});
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDsHangXe(response.data_info);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDsHieuXe = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HIEU_XE, {nv: profileInfo.nghiep_vu});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setdsHieuXeRoot(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDanhMucLoaiXe = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DM_LOAI_XE, {nv: profileInfo.nghiep_vu});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setdsLoaiXeRoot(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressApdung = async (data) => {
    try {
      const actionCode = type && type === 'CTCT' ? axiosConfig.ACTION_CODE.SUA_THONG_TIN_BSX_CTCT : axiosConfig.ACTION_CODE.SUA_THONG_TIN_BSX;

      setLoading(true);
      let params = {
        ma_doi_tac: profileInfo.ma_doi_tac,
        so_id: profileInfo.so_id,
        so_id_dt: profileInfo.so_id_dt,
        so_id_hd: profileInfo.so_id_hd,
        ten_chu_xe: data.tenChuXe,
        dthoai_chu_xe: data.sdt,
        so_khung: data.soKhung,
        so_may: data.soMay,
        bien_xe: data.bsx,
        hang_xe: data.hangXe,
        hieu_xe: data.hieuXe,
        loai_xe: data.loaiXe,
        md_sd: data.mucDichSuDung,
        nam_sx: data.namSx,
        nv: profileInfo.nghiep_vu,
      };
      let response = await ESmartClaimEndpoint.execute(actionCode, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  /**RENDER  */

  return (
    <ScreenComponent
      dialogLoading={loading}
      headerBack
      headerTitle="Đổi thông tin biển số xe"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView style={styles.content}>
            <Controller
              control={control}
              name="tenChuXe"
              rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  title="Tên chủ xe"
                  blurOnSubmit={false}
                  onChangeText={onChange}
                  placeholder="Nhập tên chủ xe"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[styles.inputView]}
                  error={errors.tenChuXe && getErrMessage(errors.tenChuXe.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="sdt"
              rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  keyboardType="phone-pad"
                  value={value}
                  title="Số điện thoại"
                  blurOnSubmit={false}
                  onChangeText={onChange}
                  placeholder="Nhập số điện thoại"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[styles.inputView]}
                  error={errors.sdt && getErrMessage(errors.sdt.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="bsx"
              rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  title="Biển số xe"
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  placeholder="Nhập biển số xe"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[styles.inputView]}
                  error={errors.bsx && getErrMessage(errors.bsx.type)}
                />
              )}
            />
            <View style={{flexDirection: 'row'}}>
              <Controller
                control={control}
                name="soKhung"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    title="Số khung"
                    blurOnSubmit={false}
                    onChangeText={onChange}
                    placeholder="Nhập số khung"
                    inputStyle={{color: colors.BLACK}}
                    containerStyle={[styles.inputView, {flex: 1, marginRight: spacing.small}]}
                    error={errors.soKhung && getErrMessage(errors.soKhung.type)}
                  />
                )}
              />
              <Controller
                control={control}
                name="soMay"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    title="Số máy"
                    blurOnSubmit={false}
                    onChangeText={onChange}
                    placeholder="Nhập số máy"
                    inputStyle={{color: colors.BLACK}}
                    containerStyle={[styles.inputView, {flex: 1}]}
                    error={errors.soMay && getErrMessage(errors.soMay.type)}
                  />
                )}
              />
            </View>
            <View style={{flexDirection: 'row'}}>
              <Controller
                control={control}
                name="hangXe"
                rules={{required: true}}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDropdown
                    value={getTextHienThi(value, dsHangXe)}
                    title="Hãng xe"
                    editable={false}
                    isTouchableOpacity
                    placeholder="Chọn hãng xe"
                    inputStyle={{color: colors.BLACK}}
                    containerStyle={[styles.inputView, {marginRight: spacing.small, flex: 1}]}
                    onPress={() => refModal.current.show()}
                    error={errors.hangXe && getErrMessage(errors.hangXe.type)}
                  />
                )}
              />

              <Controller
                control={control}
                name="hieuXe"
                rules={{required: true}}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDropdown
                    value={getTextHienThi(value, dsHieuXeRoot)}
                    title="Hiệu xe"
                    editable={false}
                    isTouchableOpacity
                    placeholder="Chọn hiệu xe"
                    inputStyle={{color: colors.BLACK}}
                    containerStyle={[styles.inputView, {flex: 1}]}
                    onPress={() => refModalHieuXe.current.show()}
                    error={errors.hieuXe && getErrMessage(errors.hieuXe.type)}
                  />
                )}
              />
            </View>

            <Controller
              control={control}
              name="loaiXe"
              rules={{required: true}}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  isDropdown
                  title="Loại xe"
                  editable={false}
                  isTouchableOpacity
                  placeholder="Chọn loại xe"
                  inputStyle={{color: colors.BLACK, paddingRight: spacing.mediumPlush}}
                  containerStyle={[styles.inputView]}
                  value={getTextHienThi(value, dsLoaiXeRoot)}
                  onPress={() => refModalLoaiXe.current.show()}
                  error={errors.loaiXe && getErrMessage(errors.loaiXe.type)}
                />
              )}
            />
            <View style={{flexDirection: 'row'}}>
              <Controller
                control={control}
                name="namSx"
                rules={{required: true}}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDropdown
                    value={value}
                    editable={false}
                    isTouchableOpacity
                    title="Năm sản xuất"
                    placeholder="Chọn năm sản xuất"
                    inputStyle={{color: colors.BLACK}}
                    containerStyle={[styles.inputView, {flex: 1, marginRight: spacing.small}]}
                    onPress={() => refModalNamSx.current.show()}
                    error={errors.namSx && getErrMessage(errors.namSx.type)}
                  />
                )}
              />
              <Controller
                control={control}
                name="mucDichSuDung"
                rules={{required: true}}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    isDropdown
                    editable={false}
                    isTouchableOpacity
                    title="Mục đích sử dụng"
                    inputStyle={{color: colors.BLACK}}
                    containerStyle={[styles.inputView, {flex: 1}]}
                    placeholder="Chọn mục đích sử dụng"
                    value={getTextHienThi(value, LIST_MUC_DICH_SU_DUNG)}
                    onPress={() => refModalMucDichSd.current.show()}
                    error={errors.mucDichSuDung && getErrMessage(errors.mucDichSuDung.type)}
                  />
                )}
              />
            </View>
            <ModalHangXe data={dsHangXe} ref={refModal} setValue={onSelectHangXe} value={hangXe} setLoading={setLoading} onBackPress={() => refModal.current.hide()} />
            <ModalHieuXe
              value={hieuXe}
              data={dsHieuXe}
              ref={refModalHieuXe}
              setLoading={setLoading}
              onBackPress={() => refModalHieuXe.current.hide()}
              setValue={(val) => setValue('hieuXe', val.ma, {shouldValidate: true})}
            />
            <ModalNamSanXuat
              value={namSx}
              ref={refModalNamSx}
              setLoading={setLoading}
              onBackPress={() => refModalNamSx.current.hide()}
              setValue={(val) => setValue('namSx', val.label, {shouldValidate: true})}
            />
            <ModalLoaiXe
              value={loaiXe}
              data={dsLoaiXeRoot}
              ref={refModalLoaiXe}
              setLoading={setLoading}
              onBackPress={() => refModalLoaiXe.current.hide()}
              setValue={(val) => setValue('loaiXe', val.ma, {shouldValidate: true})}
            />
            <ModalSelectSimple
              title={'Mục đích sử dụng'}
              baseData={LIST_MUC_DICH_SU_DUNG}
              value={mdsd}
              ref={refModalMucDichSd}
              onBackPress={() => refModalMucDichSd.current.hide()}
              setValue={(val) => setValue('mucDichSuDung', val.value, {shouldValidate: true})}
            />
          </KeyboardAwareScrollView>
          <View style={styles.footerView}>
            <ButtonLinear onPress={handleSubmit(onPressApdung)} title="Áp dụng" />
          </View>
        </View>
      }
    />
  );
}

export const DoiThongTinBienSoXeScreen = memo(DoiThongTinBienSoXeScreenComponent, isEqual);
