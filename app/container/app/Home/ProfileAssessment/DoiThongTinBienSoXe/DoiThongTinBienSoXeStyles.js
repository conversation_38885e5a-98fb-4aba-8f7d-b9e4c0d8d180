import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  profileItemView: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.GRAY,
    marginVertical: vScale(5),
    marginHorizontal: scale(spacing.small),
    padding: scale(spacing.smaller),
  },
  subLabel: {
    color: colors.GRAY10,
    fontSize: FontSize.size14,
  },
  contentRow: {
    flexDirection: 'row',
    marginVertical: vScale(2),
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(spacing.small),
  },
  btnSearch: {
    margin: scale(spacing.small),
  },
  imageNoData: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  noDataView: {
    alignItems: 'center',
  },
  footerView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY2,
    paddingVertical: vScale(10),
    justifyContent: 'space-around',
    paddingHorizontal: scale(spacing.small),
  },
});
