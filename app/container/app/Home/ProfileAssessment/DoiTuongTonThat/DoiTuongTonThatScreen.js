import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, TextInputOutlined} from '@component';
import {DATA_CONSTANT} from '@constant';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, Keyboard, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './DoiTuongTonThatStyles';
import {ModalHangXe, ModalLoaiXe} from './Components';
const titleInput = ['<PERSON><PERSON><PERSON><PERSON> đối tượng', 'Tên đối tượng', '<PERSON>ê<PERSON> khách hàng / chủ đối tượng', '<PERSON><PERSON> chú', 'Loại tài sản', 'Loại đối tượng', 'Số CMND/CCCD', 'Địa chỉ'];
const listLoaiDoiTuong = [
  {
    label: 'TNDS về người',
    value: 'TNDS',
  },
  {
    label: 'Người ngồi trên xe',
    value: 'NNTX',
  },
  {
    label: 'Lái phụ xe',
    value: 'LPHU_XE',
  },
  {
    label: 'Hành khách trên xe',
    value: 'NGUOI_HK',
  },
];
const listLoaiTaiSan = [
  {
    label: 'Xe ô tô',
    value: 'XE',
  },
  {
    label: 'Xe máy',
    value: 'XE_MAY',
  },
  {
    label: 'Khác',
    value: 'KHAC',
  },
];
const listMucDoThuongTat = [
  {
    label: 'Thương tật',
    value: 'THUONG_TAT',
  },
  {
    label: 'Tử vong',
    value: 'TU_VONG',
  },
];
const listLoaiDoiTuongHangHoa = [
  {
    label: 'Tài sản khác',
    value: 'KHAC',
  },
];

const DoiTuongTonThatScreenComponent = ({route}) => {
  console.log('DoiTuongTonThatScreenComponent');
  const {profileData, doiTuongTonThat} = route.params;
  // DROPDOWN NHÓM ĐỐI TƯỢNG
  const [openNhomDoiTuong, setOpenNhomDoiTuong] = useState(false);
  // DROPDOWN LOẠI TÀI SẢN HÀNG HOÁ- dành riêng cho nhóm đối tượng là tài sản hàng hoá
  const [openLoaiTaiSanHangHoa, setOpenLoaiTaiSanHangHoa] = useState(false);
  // DROPDOWN LOẠI TÀI SẢN - dành riêng cho nhóm đối tượng là tài sản
  const [openLoaiTaiSan, setOpenLoaiTaiSan] = useState(false);
  // DROPDOWN LOẠI ĐỐI TƯỢNG - dành riêng cho nhóm đối tượng là con người
  const [openLoaiDoiTuong, setOpenLoaiDoiTuong] = useState(false);
  const [openMucDoThuongTat, setOpenMucDoThuongTat] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dsHangXe, setDsHangXe] = useState([]);
  const [dsLoaiXeRoot, setdsLoaiXeRoot] = useState([]);
  let refModalHangXe = useRef(null);
  let refModalLoaiXe = useRef(null);

  const getDefaultFormValue = () => {
    let mucDoThuongTat = '';
    let namSanXuat = '';
    if (doiTuongTonThat) {
      if (typeof doiTuongTonThat.nam_sx === 'number') namSanXuat = doiTuongTonThat.nam_sx.toString();
      else namSanXuat = doiTuongTonThat.nam_sx;
    }
    if (doiTuongTonThat && doiTuongTonThat.nhom === 'NGUOI') mucDoThuongTat = doiTuongTonThat.muc_do;
    else mucDoThuongTat = listMucDoThuongTat[1].value; // nếu có đối tượng tt thì lấy từ đối tượng tổn thất. còn không thì mặc định là thương tật

    let nhomDoiTuong = '';
    if (doiTuongTonThat) nhomDoiTuong = doiTuongTonThat.nhom;
    else if (profileData.nhom_doi_tuong.length === 1) nhomDoiTuong = profileData.nhom_doi_tuong[0].ma;

    let loaiTaiSan = '';
    if (doiTuongTonThat && doiTuongTonThat.nhom === 'TAI_SAN') loaiTaiSan = doiTuongTonThat.loai;
    else if (nhomDoiTuong === 'HANG_HOA') loaiTaiSan = listLoaiDoiTuongHangHoa[0].value;

    return {
      nhomDoiTuong: nhomDoiTuong,
      loaiTaiSan: loaiTaiSan,
      loaiDoiTuong: doiTuongTonThat && doiTuongTonThat.nhom === 'NGUOI' ? doiTuongTonThat.loai : '',
      tenDoiTuong: doiTuongTonThat ? doiTuongTonThat.ten_doi_tuong : '',
      tenKhachHang: doiTuongTonThat ? doiTuongTonThat.ten_kh : '',
      soCMND: doiTuongTonThat ? doiTuongTonThat.cmnd : '',
      diaChi: doiTuongTonThat ? doiTuongTonThat.dia_chi : '',
      ghiChu: doiTuongTonThat ? doiTuongTonThat.ghi_chu : '',
      namSanXuat: namSanXuat,
      tienThoaThuan: doiTuongTonThat ? doiTuongTonThat.tien_thoa_thuan : '',
      mucDoThuongTat: mucDoThuongTat,
      hangXe: doiTuongTonThat ? doiTuongTonThat.hang_xe : '',
      loaiXe: doiTuongTonThat ? doiTuongTonThat.loai_xe : '',
      soKhung: doiTuongTonThat ? doiTuongTonThat.so_khung : '',
      soMay: doiTuongTonThat ? doiTuongTonThat.so_may : '',
      soLuong: doiTuongTonThat ? doiTuongTonThat.so_luong : '',
      trongTaiXe: doiTuongTonThat ? doiTuongTonThat.trong_tai : '',
      loaiHangHoa: doiTuongTonThat ? doiTuongTonThat.loai_ts : '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });
  const watchNhomDoiTuong = watch('nhomDoiTuong');
  const loaiTaiSan = watch('loaiTaiSan');

  useEffect(() => {
    layDsHangXe();
    layDanhMucLoaiXe();
  }, []);

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const closeDropdown = title => {
    openNhomDoiTuong && setOpenNhomDoiTuong(false);
    openLoaiDoiTuong && setOpenLoaiDoiTuong(false);
  };

  const layDsHangXe = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HANG_XE, {nv: profileData.ho_so.nghiep_vu});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDsHangXe(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDanhMucLoaiXe = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DM_LOAI_XE, {nv: profileData.ho_so.nghiep_vu});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setdsLoaiXeRoot(response.data_info);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressSave = async data => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      so_id: profileData.ho_so.so_id,
      so_id_doi_tuong: doiTuongTonThat ? doiTuongTonThat.so_id_doi_tuong : '',
      nhom: data.nhomDoiTuong,
      ten_kh: data.tenKhachHang,
      ten_doi_tuong: data.tenDoiTuong,
      ghi_chu: data.ghiChu,
      loai: data.nhomDoiTuong === 'TAI_SAN' || data.nhomDoiTuong === 'HANG_HOA' ? data.loaiTaiSan : data.loaiDoiTuong,
      cmnd: data.soCMND,
      dia_chi: data.diaChi,
      muc_do: data.mucDoThuongTat,
      tien_thoa_thuan: data.tienThoaThuan,
      nam_sx: data.namSanXuat,
      hang_xe: data.hangXe,
      loai_xe: data.loaiXe,
      so_khung: data.soKhung,
      so_may: data.soMay,
      so_luong: data.soLuong,
      trong_tai: data.trongTaiXe,
      loai_ts: data.loaiHangHoa,
    };
    // console.log('params', params);
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DOI_TUONG_TON_THAT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', doiTuongTonThat ? 'Cập nhật thành công' : 'Tạo đối tượng tổn thất thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressXoa = () => {
    Alert.alert('Thông báo', 'Bạn có muốn xoá đối tượng này', [
      {
        text: 'Để sau',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          let params = {
            ma_doi_tac: profileData.ho_so.ma_doi_tac,
            so_id: profileData.ho_so.so_id,
            so_id_doi_tuong: doiTuongTonThat.so_id_doi_tuong,
          };
          try {
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_DOI_TUONG_TON_THAT, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá đối tượng tổn thất thành công', 'success');
            NavigationUtil.pop();
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const onSelectHangXe = val => {
    setValue('hangXe', val.ma, {shouldValidate: true});
    // setValue('hieuXe', '');
  };

  const getTextHienThi = (val, data) => {
    let text = '';
    data.map(e => {
      if (e.ma === val) {
        text = e.ten;
      }
      if (e.value === val) {
        text = e.label;
      }
    });
    return text;
  };

  /**RENDER  */

  const renderInputFilterTheoDoiTuongLaXe = () => {
    return (
      <View>
        <Controller
          control={control}
          name="hangXe"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined title="Nhãn hiệu xe" value={value} onChangeText={onChange} placeholder="Nhập nhãn hiệu xe" onFocus={closeDropdown} inputStyle={{color: colors.BLACK}} />
          )}
        />
        <Controller
          control={control}
          name="loaiXe"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined title="Loại xe" value={value} onChangeText={onChange} placeholder="Nhập loại xe" onFocus={closeDropdown} inputStyle={{color: colors.BLACK}} />
          )}
        />
        <Controller
          control={control}
          name="trongTaiXe"
          render={({field: {onChange, value}}) => <TextInputOutlined title="Tải trọng" value={value} onChangeText={onChange} placeholder="Nhập tải trọng" onFocus={closeDropdown} />}
        />
        <Controller
          control={control}
          name="soKhung"
          render={({field: {onChange, value}}) => <TextInputOutlined title="Số khung xe" value={value} onChangeText={onChange} placeholder="Nhập số khung xe" onFocus={closeDropdown} />}
        />
        <Controller
          control={control}
          name="soMay"
          render={({field: {onChange, value}}) => <TextInputOutlined title="Số máy" value={value} onChangeText={onChange} placeholder="Nhập số máy" onFocus={closeDropdown} />}
        />
      </View>
    );
  };
  const renderInputFilterTheoDoiTuongLaHangHoa = () => {
    return (
      <View>
        <Controller
          control={control}
          name="loaiHangHoa"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined title="Loại tài sản / hàng hoá" value={value} onChangeText={onChange} placeholder="Nhập loại tài sản / hàng hoá" onFocus={closeDropdown} />
          )}
        />
        <Controller
          control={control}
          name="soLuong"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined title="Số lượng / trọng lượng" value={value} onChangeText={onChange} placeholder="Nhập số lượng / trọng lượng" onFocus={closeDropdown} />
          )}
        />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle="Đối tượng tổn thất"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView showsVerticalScrollIndicator={false} scrollEnabled={true} keyboardShouldPersistTaps="handled">
            <View style={styles.contentView}>
              <Controller
                control={control}
                name="nhomDoiTuong"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <DropdownPicker
                    title={titleInput[0]}
                    zIndex={9000}
                    searchable={doiTuongTonThat ? true : false}
                    isOpen={openNhomDoiTuong}
                    setOpen={value => {
                      Keyboard.dismiss();
                      setOpenNhomDoiTuong(value);
                    }}
                    items={profileData.nhom_doi_tuong}
                    // items={profileData.nhom_doi_tuong.filter((item) => item.ma === profileData.ho_so.nv_xly)}
                    itemSelected={value}
                    setItemSelected={dispatch => onChange(dispatch())}
                    onOpen={() => openLoaiTaiSan && setOpenLoaiTaiSan(false)}
                    disabled={doiTuongTonThat ? true : false}
                    placeholder="Chọn nhóm đối tượng"
                    containerStyle={{marginBottom: spacing.small}}
                    isRequired={true}
                    inputErr={errors.nhomDoiTuong && getErrMessage('nhomDoiTuong', errors.nhomDoiTuong.type)}
                    schema={{
                      label: 'ten',
                      value: 'ma',
                    }}
                  />
                )}
              />

              {/* NẾU NHÓM ĐỐI TƯỢNG LÀ HÀNG HOÁ -> LOẠI TÀN SẢN MẶC ĐỊNH KHÁC */}
              {watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma && (
                <Controller
                  control={control}
                  name="loaiTaiSan"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title={titleInput[4]}
                      zIndex={8000}
                      searchable={false}
                      isOpen={openLoaiTaiSanHangHoa}
                      setOpen={setOpenLoaiTaiSanHangHoa}
                      items={listLoaiDoiTuongHangHoa}
                      itemSelected={value}
                      setItemSelected={dispatch => onChange(dispatch())}
                      placeholder="Chọn loại tài sản"
                      containerStyle={{marginBottom: spacing.small}}
                      isRequired={true}
                      inputErr={errors.loaiTaiSan && getErrMessage('loaiTaiSan', errors.loaiTaiSan.type)}
                    />
                  )}
                />
              )}

              {watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma && (
                <Controller
                  control={control}
                  name="loaiTaiSan"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <DropdownPicker
                      title={titleInput[4]}
                      zIndex={8000}
                      searchable={false}
                      isOpen={openLoaiTaiSan}
                      setOpen={setOpenLoaiTaiSan}
                      items={listLoaiTaiSan}
                      itemSelected={value}
                      setItemSelected={dispatch => onChange(dispatch())}
                      placeholder="Chọn loại tài sản"
                      containerStyle={{marginBottom: spacing.small}}
                      isRequired={true}
                      inputErr={errors.loaiTaiSan && getErrMessage('loaiTaiSan', errors.loaiTaiSan.type)}
                    />
                  )}
                />
              )}

              {watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma && (
                <>
                  <Controller
                    control={control}
                    name="loaiDoiTuong"
                    rules={{
                      required: true,
                    }}
                    render={({field: {onChange, value}}) => (
                      <DropdownPicker
                        title={titleInput[5]}
                        zIndex={7000}
                        searchable={false}
                        isOpen={openLoaiDoiTuong}
                        setOpen={setOpenLoaiDoiTuong}
                        items={listLoaiDoiTuong}
                        itemSelected={value}
                        setItemSelected={dispatch => onChange(dispatch())}
                        placeholder="Chọn loại đối tượng"
                        isRequired={true}
                        inputErr={errors.loaiDoiTuong && getErrMessage('loaiDoiTuong', errors.loaiDoiTuong.type)}
                      />
                    )}
                  />
                  <Controller
                    control={control}
                    name="mucDoThuongTat"
                    rules={{
                      required: false,
                    }}
                    render={({field: {onChange, value}}) => (
                      <DropdownPicker
                        title={'Mức độ'}
                        zIndex={6000}
                        searchable={false}
                        isOpen={openMucDoThuongTat}
                        setOpen={setOpenMucDoThuongTat}
                        items={listMucDoThuongTat}
                        itemSelected={value}
                        setItemSelected={dispatch => onChange(dispatch())}
                        placeholder="Chọn mức độ"
                      />
                    )}
                  />
                </>
              )}

              {/* tên đối tượng */}
              <View zIndex={5000}>
                <Controller
                  control={control}
                  name="tenDoiTuong"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      title={titleInput[1]}
                      value={value}
                      onChangeText={onChange}
                      placeholder={titleInput[1]}
                      onFocus={closeDropdown}
                      isRequired={true}
                      error={errors.tenDoiTuong && getErrMessage('tenDoiTuong', errors.tenDoiTuong.type)}
                    />
                  )}
                />
              </View>

              {watchNhomDoiTuong === 'TAI_SAN' && (loaiTaiSan === 'XE' || loaiTaiSan === 'XE_MAY') && renderInputFilterTheoDoiTuongLaXe()}
              {watchNhomDoiTuong === 'TAI_SAN' && loaiTaiSan === 'KHAC' && renderInputFilterTheoDoiTuongLaHangHoa()}

              {(watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma || watchNhomDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) && (
                <Controller
                  control={control}
                  name="namSanXuat"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => <TextInputOutlined value={value} placeholder={'Nhập năm sản xuất'} onChangeText={onChange} onFocus={closeDropdown} title={'Năm sản xuất'} />}
                />
              )}

              {/* Tên khách hàng */}
              <Controller
                control={control}
                name="tenKhachHang"
                render={({field: {onChange, value}}) => <TextInputOutlined title={titleInput[2]} value={value} onChangeText={onChange} placeholder={titleInput[2]} onFocus={closeDropdown} />}
              />
              <Controller
                control={control}
                name="soCMND"
                render={({field: {onChange, value}}) => <TextInputOutlined title={titleInput[6]} value={value} onChangeText={onChange} placeholder={titleInput[6]} onFocus={closeDropdown} />}
              />

              {watchNhomDoiTuong !== DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma && (
                <Controller
                  control={control}
                  name="tienThoaThuan"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      value={value}
                      placeholder={'0'}
                      keyboardType="numeric"
                      onChangeText={onChange}
                      onFocus={closeDropdown}
                      title={'Tiền thoả thuận'}
                      inputStyle={{textAlign: 'right'}}
                      // error={errors.tienThoaThuan && getErrMessage('tienThoaThuan', errors.tienThoaThuan.type)}
                    />
                  )}
                />
              )}
              <Controller
                control={control}
                name="diaChi"
                render={({field: {onChange, value}}) => <TextInputOutlined title={titleInput[7]} value={value} onChangeText={onChange} placeholder={titleInput[7]} onFocus={closeDropdown} />}
              />

              {/* Ghi chú */}
              <Controller
                control={control}
                name="ghiChu"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    title={titleInput[3]}
                    value={value}
                    onChangeText={onChange}
                    placeholder={titleInput[3]}
                    onFocus={closeDropdown}
                    numberOfLines={3}
                    multiline={true}
                    blurOnSubmit={false}
                    containerStyle={{maxHeight: 100}}
                    // getRef={(ref) => (ghiChuRef = ref)}
                  />
                )}
              />
            </View>
          </KeyboardAwareScrollView>
          <ModalHangXe data={dsHangXe} ref={refModalHangXe} setValue={onSelectHangXe} value={getValues('hangXe')} setLoading={setLoading} onBackPress={() => refModalHangXe.current.hide()} />
          <ModalLoaiXe
            value={getValues('loaiXe')}
            data={dsLoaiXeRoot}
            ref={refModalLoaiXe}
            // setLoading={setLoading}
            onBackPress={() => refModalLoaiXe.current.hide()}
            setValue={val => setValue('loaiXe', val.ma, {shouldValidate: true})}
          />
        </View>
      }
      footer={
        <View style={styles.footerView}>
          {doiTuongTonThat && <ButtonLinear title="Xoá" onPress={onPressXoa} linearColors={[colors.GRAY, colors.GRAY]} />}
          <ButtonLinear title="Lưu" onPress={handleSubmit(onPressSave)} linearStyle={{marginLeft: spacing.default}} />
        </View>
      }
    />
  );
};

export const DoiTuongTonThatScreen = memo(DoiTuongTonThatScreenComponent, isEqual);
