import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {Empty, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const ModalLoaiXeComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, data, value} = props;

  const [isVisible, setIsVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [dmLoaiXe, setDmLoaiXe] = useState([]);
  const insect = useSafeAreaInsets();
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    let newData = data;
    newData[index].isCheck = !item.isCheck;
    setDmLoaiXe([...newData].slice(0, 50));
  };

  const initModalData = () => {
    if (data?.length > 0) {
      let newData = data;
      newData.map((e) => {
        if (e.ma === value) e.isCheck = true;
        else e.isCheck = false;
      });
      setDmLoaiXe([...newData].slice(0, 50));
    }
    onSearch();
  };

  const onSearch = () => {
    if (searchText) {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = data.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDmLoaiXe(filter);
    } else if (searchText === '') setDmLoaiXe(data.slice(0, 50));
  };

  useEffect(() => {
    onSearch();
  }, [searchText]);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn loại xe" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    const isCheck = item.isCheck === true;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK}}>{item.ten}</Text>
        {isCheck && <Icon.Feather name="check" size={24} color={colors.PRIMARY} />}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['bottom']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onModalWillShow={initModalData}
      onBackButtonPress={onBackPress}>
      <View style={[styles.modalView, {paddingBottom: insect.bottom}]}>
        {renderHeader()}
        <SearchBar value={searchText} onTextChange={setSearchText} />
        <View flex={1}>
          <FlatList
            data={dmLoaiXe}
            renderItem={renderItem}
            ListEmptyComponent={<Empty />}
            keyExtractor={(e, i) => i.toString()}
            style={{padding: scale(spacing.small)}}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    flex: 1,
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    marginTop: isIOS ? getStatusBarHeight() : 0,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    margin: 16,
    borderWidth: 1,
    paddingLeft: 16,
    borderRadius: 25,
    borderColor: colors.GRAY,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: vScale(spacing.smaller),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  modalContentView: {
    marginTop: 10,
    marginHorizontal: 10,
  },
  container: {
    paddingBottom: 20,
    marginHorizontal: 16,
  },
});

export const ModalLoaiXe = memo(ModalLoaiXeComponent, isEqual);
