import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import styles from './DSCacLanGiamDinhStyles';

const DanhSachCacLanGiamDinhScreenComponent = ({route, navigation}) => {
  console.log('DanhSachCacLanGiamDinhScreenComponent');
  const {profileData, prevScreenIsHoSoDiaBan} = route.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [listLanGiamDinh, setListLanGiamDinh] = useState([]);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
    });
  }, []);

  const getData = async () => {
    setDialogLoading(true);
    let params = {
      so_id: profileData?.ho_so?.so_id,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_CAC_LAN_GIAM_DINH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info;
      setListLanGiamDinh(arrData);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onRefresh = () => {
    getData();
  };

  const xoaLanGiamDinh = async (item) => {
    Alert.alert('Xoá lần giám định', 'Bạn có chắc chắn muốn xoá lần giám định này hay không?', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          setDialogLoading(true);
          try {
            let params = {
              so_id: profileData?.ho_so?.so_id,
              lan_gd: item.lan_gd,
              ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
            };
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_LAN_GD_OTO, params);
            setDialogLoading(false);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            getData();
          } catch (error) {
            setDialogLoading(false);
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };

  const renderProfileItem = ({item}) => {
    const statusColorText = item.ngay_kt < 30000101 ? colors.GREEN : item.ngay_kt >= 30000101 ? colors.RED1 : null;
    const renderLabel = (title, value, style = null) => {
      return (
        <Text style={[styles.label]}>
          {title}: <Text style={[styles.detail, style]} children={value} />
        </Text>
      );
    };
    return (
      <View style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <View flexDirection="row" justifyContent="space-between" alingItems="center">
            {renderLabel('Lần GĐ', item.ten_lan_gd, styles.txtSoHS)}
            <TouchableOpacity onPress={() => xoaLanGiamDinh(item)}>
              <Icon.FontAwesome name="trash-o" size={24} color={colors.RED1} />
            </TouchableOpacity>
          </View>
          {renderLabel('Ngày GĐ', item.ngay_gd)}
          {renderLabel('Cán bộ GĐ', item.ten_gdv)}
          {renderLabel('Địa điểm GĐ', item.dia_diem_gd)}
          {renderLabel('Đối tượng', item.doi_tuong_gd)}
          <View flexDirection="row" justifyContent="space-between" alignItems="center">
            {renderLabel('Trạng thái', item.trang_thai, {color: statusColorText})}
            <TouchableOpacity style={styles.btnXemMauIn} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.XEM_MAU_IN_THEO_LAN_GD, {selectedItem: item, profileData})}>
              <Text style={styles.txtXemMauIn}>Xem tài liệu</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Các lần giám định"
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            data={listLanGiamDinh}
            renderItem={renderProfileItem}
            keyExtractor={(_, index) => index.toString()}
            onEndReachedThreshold={0.5}
            refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={onRefresh} />}
            ListEmptyComponent={<Empty />}
          />
        </SafeAreaView>
      }
      footer={!prevScreenIsHoSoDiaBan && <ButtonLinear title="Tạo yêu cầu giám định" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TAO_YEU_CAU_GIAM_DINH, {profileData: profileData})} />}
    />
  );
};

export const DanhSachCacLanGiamDinhScreen = memo(DanhSachCacLanGiamDinhScreenComponent, isEqual);
