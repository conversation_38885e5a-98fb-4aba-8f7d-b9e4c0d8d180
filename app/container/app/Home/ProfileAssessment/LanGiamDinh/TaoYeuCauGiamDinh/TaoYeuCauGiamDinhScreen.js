import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import {selectCities} from '@app/redux/slices/CitiesSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {logErrorTryCatch} from '@app/utils';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {geoCodeChuyenToaDoThanhDiaChi, geoFormatLaiDiaChi, requestCurrentLocation} from '@app/utils/LocationProvider';
import {ButtonLinear, DropdownPicker, Icon, ModalChiNhanhTheoDangCay, ScreenComponent, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {ActivityIndicator, Alert, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {useSelector} from 'react-redux';
import {ModalNguoiXuLy} from './Components';
import styles from './TaoYeuCauGiamDinhStyles';
import {isRequiredFieldXaPhuong, NGAY_CHUYEN_DOI} from '@app/commons/Constant';

const TaoYeuCauGiamDinhScreenComponent = ({route, navigation}) => {
  console.log('TaoYeuCauGiamDinhScreenComponent');

  const {params} = route;
  const {lanGiamDinh, profileData} = params || {};
  const hoSoSauNgayChuyenDoi = profileData?.ho_so?.ngay_mo_hs >= NGAY_CHUYEN_DOI && profileData?.ho_so?.ngay_mo_hs >= profileData?.ho_so?.ngay_upd_dvi_hanh_chinh;

  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);

  const citiesData = useSelector(selectCities);

  const [openTinhThanh, setOpenTinhThanh] = useState(false);
  const [openQuanHuyen, setOpenQuanHuyen] = useState(false);
  const [listQuanHuyen, setListQuanHuyen] = useState([]);
  const [openXaPhuong, setOpenXaPhuong] = useState(false);
  const [listXaPhuong, setListXaPhuong] = useState([]);

  const [openListDoiTuong, setOpenListDoiTuong] = useState(false);
  const [listDoiTuong, setListDoiTuong] = useState(lanGiamDinh ? [''] : profileData.ds_doi_tuong.length === 1 ? [profileData.ds_doi_tuong[0].so_id_doi_tuong] : ['']);

  const [openDonViXuLy, setOpenDonViXuLy] = useState(false);

  const [openNguoiXuLy, setOpenNguoiXuLy] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);

  const [dialogLoading, setDialogLoading] = useState(false);
  const [toggleGioGiamDinh, setToggleGioGiamDinh] = useState(false);
  const [toggleNgayGiamDinh, setToggleNgayGiamDinh] = useState(false);

  // DROPDOWN ĐƠN VỊ XỬ LÝ
  let refModalChiNhanhTheoDangCay = useRef(null);
  let refModalNguoiXuLy = useRef(null);
  const [listMaDonViXuLySeleted, setListMaDonViXuLySelected] = useState([]); //list mã đơn vị xử lý được chọn

  const [disableBtnLayDiaChi, setDisableBtnLayDiaChi] = useState(false);

  useEffect(() => {
    let listDoiTuongTmp = listDoiTuong;
    if (listDoiTuong[0] === '') listDoiTuongTmp.shift();
    setValue('listDoiTuongGiamDinh', listDoiTuongTmp);
  }, [listDoiTuong]);

  useEffect(() => {
    initChiNhanhBaoHiemDangCay();
  }, []);
  useEffect(() => {
    if (listMaDonViXuLySeleted.length > 0) onChangeDonViGiamDinh('', '', listMaDonViXuLySeleted);
    else setListNguoiXuLy([]);
  }, [listMaDonViXuLySeleted]);

  const initChiNhanhBaoHiemDangCay = () => {
    // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
    let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay;
    chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
      return {
        ...item,
        listCon: [],
        isExpand: true,
        isCheck: false, //bỏ check or check
        hasChildCheck: false, //list chi nhánh cha, có child checked
        isShow: true,
      };
    });
    let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
    for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
      let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
      chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
    }
    // setListChiNhanhBaoHiem([...chiNhanhBaoHiemCha]);
    refModalChiNhanhTheoDangCay.current.setData(chiNhanhBaoHiemCha);
  };

  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
    let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
    if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
    if (listConLai.length === 0) return [];
    else {
      for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
      return listConFilter;
    }
  };

  const getDefaultFormValue = () => {
    let donViGiamDinh = [];
    return {
      gioGiamDinh: null,
      ngayGiamDinh: null,
      tinhThanh: lanGiamDinh ? lanGiamDinh.tinhThanh : null,
      quanHuyen: lanGiamDinh ? lanGiamDinh.quanHuyen : null,
      xaPhuong: lanGiamDinh ? lanGiamDinh.xaPhuong : null,
      diaDiemChiTiet: lanGiamDinh ? lanGiamDinh.diaDiemChiTiet : '',
      listDoiTuongGiamDinh: lanGiamDinh ? [''] : profileData.ds_doi_tuong.length === 1 ? [profileData.ds_doi_tuong[0].so_id_doi_tuong] : [''],
      ghiChu: lanGiamDinh ? lanGiamDinh.ghiChu : '',
      // donViGiamDinh: lanGiamDinh ? lanGiamDinh.donViGiamDinh : null,
      donViGiamDinh: donViGiamDinh,
      canBoGiamDinh: lanGiamDinh ? lanGiamDinh.canBoGiamDinh : null,
    };
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onSubmit = async (data) => {
    try {
      let canBoGiamDinh = listNguoiXuLy.find((item) => item.ma === data.canBoGiamDinh);
      let params = {
        gio_gd: moment(data.gioGiamDinh).format('HH:mm'),
        ngay_gd: +moment(data.ngayGiamDinh).format('YYYYMMDD'),
        so_id: profileData.ho_so.so_id,
        tinh_thanh: data.tinhThanh,
        quan_huyen: data.quanHuyen,
        phuong_xa: data.xaPhuong,
        dia_diem: data.diaDiemChiTiet,
        dvi_gdinh: canBoGiamDinh ? canBoGiamDinh.ma_chi_nhanh : '',
        ma_gdv: data.canBoGiamDinh,
        doi_tuong_gd: data.listDoiTuongGiamDinh.join(','),
        ghi_chu: data.ghiChu,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TAO_MOI_LAN_GIAM_DINH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Tạo mới lần giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const canBoGiamDinh = watch('canBoGiamDinh');

  const onChaneTinhThanh = (title, items, value) => {
    citiesData.forEach((city) => {
      if (city.ma === value) {
        setListQuanHuyen(city.district);
        return;
      }
    });
  };
  const onChangeQuanHuyen = (title, items, value) => {
    listQuanHuyen.forEach((quanHuyen) => {
      if (quanHuyen.ma === value) {
        setListXaPhuong(quanHuyen.ward);
        return;
      }
    });
  };
  const onOpenDropdown = (type) => {
    type !== 0 && openTinhThanh && setOpenTinhThanh(false);
    type !== 1 && openQuanHuyen && setOpenQuanHuyen(false);
    type !== 2 && openXaPhuong && setOpenXaPhuong(false);
    type !== 3 && openListDoiTuong && setOpenListDoiTuong(false);
    type !== 4 && openDonViXuLy && setOpenDonViXuLy(false);
    type !== 5 && openNguoiXuLy && setOpenNguoiXuLy(false);

    if (type === 1 && listQuanHuyen.length === 0) setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    if (type === 2 && listXaPhuong.length === 0) {
      setError('quanHuyen', {type: 'required', message: 'Thông tin bắt buộc'});
      listQuanHuyen.length === 0 && setError('tinhThanh', {type: 'required', message: 'Thông tin bắt buộc'});
    }
    if (type === 5 && listNguoiXuLy.length === 0 && !getValues('donViGiamDinh')) setError('donViGiamDinh', {type: 'required', message: 'Thông tin bắt buộc'});
  };
  const getDisplayTextDoiTuong = (listDoiTuongSelected) => {
    if (!listDoiTuongSelected) return '';
    if (listDoiTuongSelected[0] === '') return 'Chọn đối tượng';
    let displayText = [];
    listDoiTuongSelected.forEach((idDoiTuong) => {
      profileData.ds_doi_tuong.forEach((doiTuong) => {
        if (idDoiTuong === doiTuong.so_id_doi_tuong) {
          displayText.push(doiTuong.ten_doi_tuong);
        }
      });
    });

    return displayText.join(' ; ');
  };
  const onChangeDonViGiamDinh = async (title, items, item) => {
    if (!item) return;
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      ma_chi_nhanh: item.join(','),
    };
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
      if (openNguoiXuLy) setOpenNguoiXuLy(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let giamDinhVien = response.data_info.map((item) => {
        item.value = item.ma;
        item.label = item.ten + (item.ten_chuc_danh ? ` (${item.ten_chuc_danh})` : '');
        return item;
      });
      setListNguoiXuLy([...giamDinhVien]);
      setValue('canBoGiamDinh', null);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const onPressLayDiaChiHienTai = () => {
    requestCurrentLocation(
      async (position) => {
        setDisableBtnLayDiaChi(true);
        let response = await geoCodeChuyenToaDoThanhDiaChi({
          lat: position.coords.latitude,
          lon: position.coords.longitude,
        });

        // console.log('response', response);
        setDisableBtnLayDiaChi(false);
        if (response) {
          if (response.data) {
            let diaChiFormat = {
              maTinhThanh: null,
              maQuanHuyen: null,
              maXaPhuong: null,
              diaChiDayDu: null,
            };
            diaChiFormat = geoFormatLaiDiaChi(response.data);
            // LOG RA LỖI NẾU KHÔNG FILL ĐỦ DATA VÀO
            if (response.data.error || diaChiFormat?.maTinhThanh === null || diaChiFormat?.maQuanHuyen === null || diaChiFormat?.maXaPhuong === null) {
              logErrorTryCatch({
                code: 'GEOCODE_KHAI_BAO_TON_THAT',
                message: JSON.stringify(response.data),
              });
            }
            if (diaChiFormat.maTinhThanh) {
              citiesData.forEach((itemTinhThanh) => {
                if (itemTinhThanh.ma === diaChiFormat.maTinhThanh) {
                  setValue('tinhThanh', itemTinhThanh.ma, {shouldValidate: true}); //set Tỉnh thành được chọn
                  setListQuanHuyen([...itemTinhThanh.district]); //set list quận huyện của tỉnh thành
                  //nếu có quận huyện được chọn
                  if (diaChiFormat.maQuanHuyen) {
                    let listQuanHuyen = itemTinhThanh.district;
                    listQuanHuyen.forEach((itemQuanHuyen) => {
                      if (itemQuanHuyen.ma === diaChiFormat.maQuanHuyen) {
                        setValue('quanHuyen', itemQuanHuyen.ma, {shouldValidate: true}); //set quận huyện được chọn
                        setListXaPhuong([...itemQuanHuyen.ward]); //set list xã phường của quận huyện
                        if (diaChiFormat.maXaPhuong) setValue('xaPhuong', diaChiFormat.maXaPhuong, {shouldValidate: true});
                      }
                    });
                  }
                }
              });
            }
          } else FlashMessageHelper.showFlashMessage('Thông báo', 'Chưa tồn tại địa chỉ tại địa điểm này. Vui lòng thử lại');
        }
      },
      (error) => logErrorTryCatch(error),
    );
  };
  // RENDER

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const getTenHienThi = (value, data = []) => {
    let name = '';
    data.map((e) => {
      if (e.ma === value) name = e.ten;
    });
    return name;
  };

  const renderThongTinGiamDinh = () => (
    <View zIndex={10000}>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin giám định" style={styles.subLabel} />
      </View>
      <View style={{marginHorizontal: spacing.small}}>
        <View flexDirection="row">
          <View flex={1} marginRight={10}>
            <Controller
              control={control}
              name="gioGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <>
                  <TextInputOutlined
                    isRequired
                    isDateTimeField
                    isTouchableOpacity
                    editable={false}
                    value={value ? moment(value).format('HH:mm') : ''}
                    placeholder="Chọn giờ"
                    title="Giờ giám định"
                    onPress={() => setToggleGioGiamDinh(true)}
                    inputStyle={{color: colors.BLACK}}
                    error={errors.gioGiamDinh && getErrMessage('gioGiamDinh', errors.gioGiamDinh.type)}
                  />
                  {renderDateTimeComp(toggleGioGiamDinh, setToggleGioGiamDinh, onChange, value || new Date(), 'time', null, null, 0)}
                </>
              )}
            />
          </View>
          <View flex={1}>
            <Controller
              control={control}
              name="ngayGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <>
                  <TextInputOutlined
                    isRequired
                    isDateTimeField
                    isTouchableOpacity
                    editable={false}
                    value={value ? moment(value).format('DD/MM/YYYY') : ''}
                    placeholder="Chọn ngày"
                    title="Ngày giám định"
                    onPress={() => setToggleNgayGiamDinh(true)}
                    inputStyle={{color: colors.BLACK}}
                    error={errors.ngayGiamDinh && getErrMessage('ngayGiamDinh', errors.ngayGiamDinh.type)}
                  />
                  {renderDateTimeComp(toggleNgayGiamDinh, setToggleNgayGiamDinh, onChange, value || new Date(), 'date', null, null, 0)}
                </>
              )}
            />
          </View>
        </View>
        <Controller
          control={control}
          name="tinhThanh"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <View style={{zIndex: 10000, flexDirection: 'row', alignItems: 'flex-start', flex: 1}}>
              <DropdownPicker
                title="Tỉnh thành"
                placeholder="Chọn tỉnh thành"
                zIndex={10000}
                isOpen={openTinhThanh}
                setOpen={setOpenTinhThanh}
                items={citiesData.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
                schema={{
                  label: 'ten',
                  value: 'ma',
                }}
                isRequired
                onChangeValue={onChaneTinhThanh}
                containerStyle={{marginBottom: openTinhThanh ? 300 : spacing.smaller, flex: 1, marginTop: 0}}
                itemSelected={value}
                setItemSelected={(dispatch) => onChange(dispatch())}
                onOpen={() => onOpenDropdown(0)}
                inputErr={errors.tinhThanh && getErrMessage('tinhThanh', errors.tinhThanh.type)}
              />
              <TouchableOpacity style={{marginTop: spacing.large, marginLeft: spacing.smaller}} onPress={onPressLayDiaChiHienTai} disabled={disableBtnLayDiaChi}>
                {!disableBtnLayDiaChi ? <Icon.Entypo name="location" color={colors.PRIMARY} size={30} /> : <ActivityIndicator size="large" color={colors.PRIMARY} />}
              </TouchableOpacity>
            </View>
          )}
        />

        <Controller
          control={control}
          name="quanHuyen"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title={hoSoSauNgayChuyenDoi ? 'Xã phường' : 'Quận huyện'}
              placeholder={`Chọn ${hoSoSauNgayChuyenDoi ? 'Xã phường' : 'Quận huyện'}`}
              zIndex={9000}
              isOpen={openQuanHuyen}
              setOpen={setOpenQuanHuyen}
              items={listQuanHuyen.filter((item) => (hoSoSauNgayChuyenDoi && item.ngay_ad >= NGAY_CHUYEN_DOI) || (!hoSoSauNgayChuyenDoi && item.ngay_ad < NGAY_CHUYEN_DOI))}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              isRequired
              onChangeValue={onChangeQuanHuyen}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(hoSoSauNgayChuyenDoi ? 2 : 1)}
              inputErr={errors.quanHuyen && getErrMessage('quanHuyen', errors.quanHuyen.type)}
            />
          )}
        />

        {/* <Controller
          control={control}
          name="xaPhuong"
          rules={{
            required: isRequiredFieldXaPhuong,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              isRequired={isRequiredFieldXaPhuong}
              title="Xã phường"
              placeholder="Chọn xã phường"
              zIndex={8000}
              isOpen={openXaPhuong}
              setOpen={setOpenXaPhuong}
              items={listXaPhuong}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(2)}
              inputErr={errors.xaPhuong && getErrMessage('xaPhuong', errors.xaPhuong.type)}
            />
          )}
        /> */}

        <Controller
          control={control}
          rules={{
            required: true,
          }}
          name="diaDiemChiTiet"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView]}
              title="Địa điểm chi tiết"
              placeholder="Nhập địa điểm chi tiết"
              value={value}
              onChangeText={onChange}
              isRequired={true}
              returnKeyType={'next'}
              blurOnSubmit={false}
              error={errors.diaDiemChiTiet && getErrMessage('diaDiemChiTiet', errors.diaDiemChiTiet.type)}
            />
          )}
        />

        <Controller
          control={control}
          name="listDoiTuongGiamDinh"
          rules={{
            required: true,
          }}
          render={({field: {value}}) => (
            <DropdownPicker
              title="Đối tượng"
              placeholder="Chọn đối tượng"
              zIndex={7000}
              isOpen={openListDoiTuong}
              setOpen={setOpenListDoiTuong}
              items={profileData.ds_doi_tuong}
              searchable={false}
              schema={{
                label: 'ten_doi_tuong',
                value: 'so_id_doi_tuong',
              }}
              isRequired
              itemSelected={value}
              setItemSelected={setListDoiTuong}
              onOpen={() => onOpenDropdown(3)}
              inputErr={errors.listDoiTuongGiamDinh && getErrMessage('listDoiTuongGiamDinh', errors.listDoiTuongGiamDinh.type)}
              multiple={true}
              multipleText={getDisplayTextDoiTuong(value)}
              containerStyle={{marginBottom: openListDoiTuong ? profileData.ds_doi_tuong.length * 40 : 0}}
            />
          )}
        />

        <Controller
          control={control}
          name="ghiChu"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              containerStyle={[styles.inputView, {zIndex: 4000}]}
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              value={value}
              onChangeText={onChange}
              returnKeyType={'next'}
              blurOnSubmit={false}
              multiline={true}
            />
          )}
        />
      </View>
    </View>
  );
  const renderThongTinCanBoGiamDinh = () => (
    <View>
      <View style={styles.headerTitleView}>
        <Text children="Thông tin cán bộ giám định" style={styles.subLabel} />
      </View>
      <View marginHorizontal={spacing.small}>
        <Controller
          control={control}
          rules={{
            required: false,
          }}
          name="donViGiamDinh"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              isTouchableOpacity
              editable={false}
              isDropdown
              title="Đơn vị giám định"
              value={value.length === 0 ? 'Chọn đơn vị giám định' : value.length === 1 ? value[0].ten_chi_nhanh : `Có ${value.length} đơn vị được chọn`}
              // error={errors.donViGiamDinh && getErrMessage('donViGiamDinh', errors.donViGiamDinh.type)}
              placeholder="Đơn vị giám định"
              onPress={() => refModalChiNhanhTheoDangCay.current.show()}
            />
          )}
        />

        {/* <Controller
          control={control}
          name="canBoGiamDinh"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              isRequired
              title="Cán bộ giám định"
              placeholder="Chọn cán bộ giám định"
              zIndex={5000}
              isOpen={openNguoiXuLy}
              setOpen={setOpenNguoiXuLy}
              items={listNguoiXuLy}
              itemSelected={value}
              setItemSelected={(dispatch) => onChange(dispatch())}
              onOpen={() => onOpenDropdown(5)}
              containerStyle={{marginBottom: openNguoiXuLy ? 300 : spacing.small}}
              cleared
              onPressClear={() => setValue('canBoGiamDinh', null)}
              inputErr={errors.canBoGiamDinh && getErrMessage('canBoGiamDinh', errors.canBoGiamDinh.type)}
            />
          )}
        /> */}
        <Controller
          control={control}
          name="canBoGiamDinh"
          rules={{
            required: false,
          }}
          render={({field: {value, onChange}}) => {
            let cleared = value && value !== null && value !== '';
            return (
              <TextInputOutlined
                cleared={cleared}
                isDropdown
                isTouchableOpacity
                onPressClear={() => setValue('canBoGiamDinh', null)}
                editable={false}
                title="Cán bộ giám định hiện trường"
                placeholder="Chọn cán bộ giám định"
                inputStyle={{color: colors.BLACK}}
                value={getTenHienThi(value, listNguoiXuLy)}
                onPress={() => refModalNguoiXuLy.current.show()}
              />
            );
          }}
        />
      </View>
    </View>
  );
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={lanGiamDinh ? 'Tạo yêu cầu giám định' : 'Yêu cầu giám định'} // luồng hồ sơ giám định
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.content}>
              {renderThongTinGiamDinh()}
              {renderThongTinCanBoGiamDinh()}
            </View>
            <ModalChiNhanhTheoDangCay
              ref={refModalChiNhanhTheoDangCay}
              showCheckCha={true}
              multiple={false}
              setListMaDonViXuLySelected={setListMaDonViXuLySelected}
              setListItemDonViXulySelected={(value) => setValue('donViGiamDinh', value, {shouldValidate: true})}
            />
          </KeyboardAwareScrollView>
          <ModalNguoiXuLy
            value={canBoGiamDinh}
            data={listNguoiXuLy}
            ref={refModalNguoiXuLy}
            onBackPress={() => refModalNguoiXuLy.current.hide()}
            setValue={(val) => setValue('canBoGiamDinh', val.ma, {shouldValidate: true})}
          />
        </SafeAreaView>
      }
      footer={<ButtonLinear title={lanGiamDinh ? 'Cập nhật' : 'Tạo mới'} onPress={handleSubmit(onSubmit)} />}
    />
  );
};

export const TaoYeuCauGiamDinhScreen = memo(TaoYeuCauGiamDinhScreenComponent, isEqual);
