import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
export default StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingTop: isIOS ? getStatusBarHeight() : 0,
  },
  pdf: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
  },
  shareView: {
    position: 'absolute',
    right: 10,
    bottom: 10,
    borderRadius: 100,
    borderWidth: 3,
    borderColor: colors.PRIMARY,
    backgroundColor: colors.WHITE,
  },
  shareIcon: {
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  contactCenterImage: {
    width: 50,
    height: 50,
    alignSelf: 'center',
    borderWidth: 1,
    borderRadius: 35,
  },
  actionButtonTextContainer: {
    borderWidth: 1,
    backgroundColor: colors.WHITE5,
  },
  modalSuccessView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  imgMailUser: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  successTopView: {
    width: dimensions.width - 30,
    height: dimensions.height / 6,
    backgroundColor: colors.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // borderWidth: 1,
  },
  btnSuccessView: {
    height: 40,
    width: dimensions.width / 2 - 40,
    justifyContent: 'center',
    borderRadius: 20,
    marginVertical: 5,
    marginHorizontal: 10,
  },
  successCenterView: {
    width: dimensions.width - 30,
    paddingBottom: 10,
    backgroundColor: colors.WHITE,
    justifyContent: 'space-around',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  txtSuccess: {
    textAlign: 'center',
    paddingHorizontal: 10,
    color: colors.WHITE,
    fontSize: 16,
  },
  buttonModalView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  addMailView: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: dimensions.width - 60,
  },
  inputMail: {
    borderBottomWidth: 0.5,
    paddingLeft: 20,
    borderWidth: 0.5,
    flex: 1,
    borderRadius: 30,
    height: 50,
  },
  iconAddMail: {
    borderRadius: 10,
    borderWidth: 1,
    padding: 10,
  },
  bottomView: {
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderTopColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
    paddingHorizontal: scale(spacing.small),
  },
});
