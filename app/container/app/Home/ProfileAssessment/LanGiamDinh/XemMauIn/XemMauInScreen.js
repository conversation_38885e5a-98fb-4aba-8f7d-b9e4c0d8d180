import {colors} from '@app/commons/Theme';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {Icon, ModalComp, ScreenComponent} from '@component';
import {REGUlAR_EXPRESSION, SCREEN_ROUTER_APP, isIOS} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, View} from 'react-native';
import ActionButton from 'react-native-action-button';
import Pdf from 'react-native-pdf';
import Share from 'react-native-share';
import ModalMailConfirm from './Modals/ModalMailConfirm';
import styles from './Styles';

const mauIn = 'ESCS_BBGD_XAC_DINH_THIET_HAI_XCG';

const XemMauInScreenComponent = (props) => {
  console.log('XemMauInScreenComponent');
  const {route} = props;
  const {prevScreen, selectedItem} = route.params;
  const [profileData, setProfileData] = useState(route.params.profileData);

  const [pdfData, setPDFData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [inputEmail, setInputEmail] = useState('');
  const [mailData, setMailData] = useState(null);
  const [inputErr, setInputErr] = useState(['']);

  const [toggleModalMailConfirm, setToggleModalMailConfirm] = useState(false);

  /**FUNCTION  */
  useEffect(() => {
    layMauInDuocChon();
  }, []);

  const layMauInDuocChon = () => {
    if (selectedItem) layMauInPDF();
  };

  const onPressSharePDF = () => {
    // if (!pdfData.filePath) {
    //   FlashMessageHelper.showFlashMessage('Thông báo', 'File chưa được tải về máy. Vui lòng thử lại', 'info');
    //   return;
    // }
    let options = {
      url: 'data:application/pdf;base64,' + pdfData.base64,
    };
    Share.open(options)
      .then((res) => {
        // console.log(res);
      })
      .catch((err) => {
        // err && console.log(err);
      });
  };

  const layMauInPDF = async () => {
    setIsLoading(true);
    let actionCode = '';
    let urlFile = '';
    let filter = profileData.mau_in.filter((e) => e.ma_mau_in === mauIn);
    if (filter.length > 0) {
      actionCode = filter[0].ma_action_api;
      urlFile = filter[0].url_file;
    }
    try {
      const params = {
        ma_mau_in: mauIn,
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        url_file: urlFile,
        lan_gd: selectedItem.lan_gd,
      };
      let response = await ESmartClaimEndpoint.exportPdfBase64(actionCode, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let pdfData = {
        base64: response.data_info.base64_string,
        filePath: '',
      };
      setPDFData(pdfData);
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getListMailData = async () => {
    try {
      const params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac, //ma_doi_tac
        so_id: profileData.ho_so.so_id ? profileData.ho_so.so_id : profileData.thong_tin_chung.so_id_tt, //so_id_tt
        nv: profileData.ho_so.nghiep_vu,
        loai: 'TEMPLATE_EMAIL_BBGD',
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.DANH_SACH_MAIL_NGUOI_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.out_value.email_nhan) {
        setMailData(response.out_value);
        setInputEmail(response.out_value.email_nhan || '');
      }
      setToggleModalMailConfirm(true);
      setInputErr(['']);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressGuiMail = async () => {
    try {
      if (!inputEmail.trim()) {
        setInputErr(['Vui lòng nhập email']);
        return;
      } else if (!REGUlAR_EXPRESSION.REG_EMAIL.test(inputEmail.trim())) {
        setInputErr(['Email không hợp lệ']);
        return;
      }
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac, //ma_doi_tac
        so_id: profileData.ho_so.so_id ? profileData.ho_so.so_id : profileData.thong_tin_chung.so_id_tt, //so_id_tt
        nv: 'XE',
        loai: 'TEMPLATE_EMAIL_BBGD',
        email_nhan: inputEmail,
        create_file: null,
        create_file_sign: mauIn,
        remove_file: mauIn,
      };
      try {
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GUI_EMAIL_XAC_NHAN, params);
        setIsLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        setToggleModalMailConfirm(false);
        FlashMessageHelper.showFlashMessage('Thông báo', 'Gửi email xác nhận thành công', 'success');
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    } catch (error) {
      Alert.alert('Thông báo', JSON.stringify(error));
    }
  };

  const onChangeEmail = (value) => {
    if (!value.trim()) setInputErr(['Vui lòng nhập email']);
    else if (!REGUlAR_EXPRESSION.REG_EMAIL.test(value.trim())) setInputErr(['Email không hợp lệ']);
    else setInputErr(['']);
    setInputEmail(value);
  };

  /* RENDER */
  const renderPDFView = () => {
    if (!pdfData) return;
    return (
      <Pdf
        source={{
          uri: 'data:application/pdf;base64,' + pdfData.base64,
        }}
        onLoadProgress={(percent) => {
          console.log('percent', percent);
        }}
        onLoadComplete={(numberOfPages, filePath) => {
          // let pdfDataTmp = pdfData;
          // if (!pdfDataTmp) return;
          // pdfDataTmp.filePath = filePath;
          // setPDFData(pdfDataTmp);
        }}
        onPageChanged={(page, numberOfPages) => {}}
        onError={(error) => {
          Alert.alert('Thông báo', error);
          // console.log(error);
        }}
        onPressLink={(uri) => {
          // console.log(`Link presse: ${uri}`);
        }}
        style={styles.pdf}
      />
    );
  };

  const renderModalConfirmSendMail = () => {
    return (
      <ModalComp
        isVisible={toggleModalMailConfirm}
        swipeDirection={[]}
        onBackdropPress={() => setToggleModalMailConfirm(false)}
        renderContentView={() => (
          <ModalMailConfirm inputEmail={inputEmail} onChangeEmail={onChangeEmail} setToggleModalMailConfirm={setToggleModalMailConfirm} inputErr={inputErr} onPressGuiMail={onPressGuiMail} />
        )}
      />
    );
  };

  const renderActionButton = () => {
    if (prevScreen == SCREEN_ROUTER_APP.TRINH_THANH_TOAN || prevScreen == SCREEN_ROUTER_APP.APPROVAL_PROFILE || route.params.prevPrevScreen == SCREEN_ROUTER_APP.HO_SO_DIA_BAN) return;
    if (profileData && profileData?.ho_so?.hien_thi_button == '1') return;
    return (
      <ActionButton
        buttonColor={colors.BLUE3_08}
        renderIcon={() => (
          <View borderRadius={20}>
            <Icon.SimpleLineIcons name="options-vertical" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
          </View>
        )}
        offsetY={vScale(60)}
        offsetX={10}
        size={55}
        degrees={0}>
        <ActionButton.Item buttonColor={colors.ORANGE} title="Gửi xác nhận khách hàng" textContainerStyle={styles.actionButtonTextContainer} onPress={() => getListMailData()}>
          <Icon.MaterialCommunityIcons name="email-send-outline" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
        </ActionButton.Item>

        <ActionButton.Item buttonColor={colors.GREEN} title="Chia sẻ" textContainerStyle={styles.actionButtonTextContainer} onPress={onPressSharePDF}>
          <Icon.FontAwesome name="share-alt" style={styles.actionButtonIcon} size={20} color={colors.WHITE} />
        </ActionButton.Item>
      </ActionButton>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={isLoading}
      headerBack
      headerTitle="Tài liệu"
      renderView={
        <View style={styles.container}>
          {renderPDFView()}
          {renderActionButton()}
          {renderModalConfirmSendMail()}
        </View>
      }
    />
  );
};

export const XemMauInScreen = memo(XemMauInScreenComponent, isEqual);
