import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, ScreenComponent, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './Styles';
import {spacing} from '@app/theme';
import NavigationUtil from '@app/navigation/NavigationUtil';

const NhapUocTonThatChoHoSoChuaLaySoScreenComponent = (props) => {
  const {route} = props;
  const {profileData} = route.params;
  const [dsHangMuc, setDsHangMuc] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getData();
  }, []);

  const onPressLuu = async () => {
    let dataPost = dsHangMuc.filter((e) => e.isChecked === true);
    if (dataPost.length <= 0) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn một loại hình nghiệp vụ!');
    let arr = [];
    dataPost.map((e) => {
      let json = {
        lh_nv: e.lh_nv,
        uoc_ton_that: +e.uoc_ton_that,
        tl_thue: 0,
      };
      arr.push(json);
    });

    try {
      const params = {
        so_id: profileData?.ho_so?.so_id,
        nv: profileData.ho_so.nghiep_vu,
        arr: arr,
      };
      setLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_UOC_TON_THAT, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật ước tổn thất thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + ' line 49');
    }
  };

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id: profileData?.ho_so?.so_id,
        so_id_hd: profileData?.ho_so?.so_id_hd,
        so_id_dt: profileData?.ho_so?.so_id_dt,
        pm: 'GD',
        nv: profileData.ho_so.nghiep_vu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DU_LIEU_NHAP_UOC_TT, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let mergeData = [];
      let dataUoc = response.data_info.uoc_ton_that_nv;
      let dataLhnv = response.data_info.dk;
      if (dataUoc.length > 0) {
        dataUoc.map((dataUocItem, index) => {
          dataLhnv.map((dataDK, i) => {
            if (dataDK.ma === dataUocItem.lh_nv) mergeData.push(dataUocItem);
            dataLhnv[i].uoc_ton_that = 0;
            if (dataDK.lh_nv !== dataUocItem.lh_nv) mergeData.push(dataDK);
          });
        });
      } else {
        dataLhnv.map((dataDK, i) => {
          dataLhnv[i].uoc_ton_that = 0;
          mergeData.push(dataDK);
        });
      }
      mergeData = mergeData.filter((e) => e.luong_xly === profileData.ho_so.nv_xly);
      if (mergeData.length === 1) {
        mergeData[0].isChecked = true;
      } else {
        mergeData.map((e, i) => {
          mergeData[i].isChecked = false;
        });
      }
      setDsHangMuc([...mergeData]);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 97');
    }
  };

  const onChangeInputValue = (idx, val) => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((e) => {
      listHangMucTmp[idx].uoc_ton_that = val;
    });
    setDsHangMuc([...listHangMucTmp]);
  };
  const onChangeCheckBoxValue = (idx, val) => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((e, i) => {
      listHangMucTmp[i].isChecked = false;
      listHangMucTmp[idx].isChecked = !val;
    });
    setDsHangMuc([...listHangMucTmp]);
  };

  /* RENDER */
  const renderItemHangMuc = ({index, item}) => {
    return (
      <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
        <TouchableOpacity style={styles.checkBoxView} onPress={() => onChangeCheckBoxValue(index, item.isChecked)}>
          <CheckboxComp value={item.isChecked} checkboxStyle={{marginHorizontal: spacing.tiny}} disabled />
          <Text style={styles.txtTenHangMuc} children={item.ten_lhnv} />
        </TouchableOpacity>
        <View style={styles.rowStyles}>
          <View style={styles.frame}>
            <TextInputOutlined
              editable={item.isChecked}
              placeholder="0"
              keyboardType="numeric"
              inputStyle={styles.inputStyle}
              value={item?.uoc_ton_that?.toString()}
              containerStyle={[styles.inputContainer]}
              onChangeText={(value) => onChangeInputValue(index, value)}
            />
          </View>
          {/* <View style={styles.frame}>
            <Text style={styles.subLabel}>% Thuế</Text>
            <TextInputOutlined
              maxLength={3}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              value={item?.tl_thue?.toString()}
              containerStyle={[styles.inputContainer]}
              onChangeText={(value) => onChangeInputValue(item, index, value, 'tl_thue')}
            />
          </View> */}
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerTitle="Ước tổn thất"
      headerBack
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Loại hình nghiệp vụ" />
              </View>
              <View style={styles.rowStyles}>
                <View justifyContent="center" alignItems="center" borderLeftWidth={1} borderColor={colors.GRAY}>
                  <Text style={styles.txtHangMuc} children="Ước tổn thất" />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItemHangMuc}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={
                <View borderWidth={0.5} borderColor={colors.GRAY}>
                  <Text style={styles.txtEmpty} children="Danh sách trống!" />
                </View>
              }
            />
          </KeyboardAwareScrollView>
          <View style={styles.footerView}>
            <ButtonLinear title="Lưu" onPress={onPressLuu} />
          </View>
        </SafeAreaView>
      }
    />
  );
};

export const NhapUocTonThatChoHoSoChuaLaySoScreen = memo(NhapUocTonThatChoHoSoChuaLaySoScreenComponent, isEqual);
