const {colors} = require('@app/commons/Theme');
const {dimensions, scale, vScale, spacing, FontSize} = require('@app/theme');
const {StyleSheet} = require('react-native');

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  inputStyle: {
    flex: 1,
    minWidth: 100,
    borderRadius: 0,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    textAlign: 'center',
    borderColor: colors.GRAY2,
    paddingVertical: vScale(spacing.tiny),
  },
  content: {
    flex: 1,
    marginTop: vScale(10),
    marginHorizontal: scale(10),
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    marginVertical: vScale(spacing.tiny),
    marginHorizontal: scale(spacing.tiny),
  },
  txtTenHangMuc: {
    flex: 1,
    fontWeight: '500',
    marginLeft: scale(2),
    color: colors.PRIMARY,
    fontSize: FontSize.size13,
  },
  rowStyles: {
    width: dimensions.width / 3,
  },
  frame: {
    paddingHorizontal: spacing.tiny,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    fontSize: FontSize.size14,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
    marginVertical: vScale(spacing.smaller),
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: vScale(6),
    backgroundColor: colors.GRAY2,
    paddingHorizontal: scale(spacing.small),
  },
  txtEmpty: {
    textAlign: 'center',
    fontSize: FontSize.size14,
    marginVertical: vScale(spacing.smaller),
  },
  inputContainer: {
    // flex: 1,
    textAlign: 'right',
  },
  subLabel: {
    textAlign: 'left',
    fontSize: FontSize.size13,
  },
  footerView: {
    borderTopWidth: 0.2,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-around',
    paddingHorizontal: spacing.small,
  },
  checkBoxView: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});
