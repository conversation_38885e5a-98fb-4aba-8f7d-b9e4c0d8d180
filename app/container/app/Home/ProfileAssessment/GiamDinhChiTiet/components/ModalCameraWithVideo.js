import {colors, dimension} from '@app/commons/Theme';
import {getAppSetting} from '@app/redux/slices/AppSettingSlice';
import {selectUser} from '@app/redux/slices/UserSlice';
import {dimensions, spacing} from '@app/theme';
import {getCauHinhHoSoByMa} from '@app/utils/DataProvider';
import {Icon, Text} from '@component';
import {APP_NAME, DATA_CONSTANT, isIOS} from '@constant';
import moment from 'moment';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Image, StyleSheet, TextInput, TouchableOpacity, Vibration, View} from 'react-native';
import {RNCamera} from 'react-native-camera';
import DeviceInfo from 'react-native-device-info';
import ImageCropPicker from 'react-native-image-crop-picker';
import ImageMarker, {Position} from 'react-native-image-marker';
import ImageResizer from 'react-native-image-resizer';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';
import ReactNativeBlobUtil from 'react-native-blob-util';
import {MAX_SO_LUONG_ANH_CHUP_HANG_MUC_KHAC, MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC} from '../constant';
import {useForm} from 'react-hook-form';
import {Controller} from 'react-hook-form';
import {logErrorTryCatch} from '@app/utils';

const anhToanCanhTitle = [
  'Phía trước xe', //0
  'Bên phụ xe trước', //1
  'Bên phụ xe sau', //2
  'Phía sau xe', //3
  'Bên lái xe sau', //4
  'Bên lái xe trước', //5
  'Ảnh toàn cảnh', //6
  'Số khung', //7
  'Số máy', //8
  'Tem đăng kiểm', //9
  'Giấy chứng nhận bảo hiểm', //10
  'Thông báo tai nạn và yêu cầu bồi thường', //11
  'Ảnh giám định viên', //12
];
const BLXTitle = ['Bằng lái xe mặt trước', 'Bằng lái xe mặt sau'];
const CCCDTitle = ['CCCD mặt trước', 'CCCD mặt sau'];
const DKXTitle = ['Đăng ký xe mặt trước', 'Đăng ký xe mặt sau'];
const DKTitle = ['Đăng kiểm xe mặt trước', 'Đăng kiểm xe mặt sau'];
const GIAY_TO_CHUP_NGANNG = ['ANH_GDV', 'TOAN_CANH', 'SO_KHUNG', 'SO_MAY', 'TEM_DANG_KIEM'];
const MEDIA_TYPE = {
  PICTURE: 0,
  VIDEO: 1,
};
const VIDEO_DURATION = 5;

const ModalCameraWithVideoComponent = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);
  const appSetting = useSelector(getAppSetting);
  const userInfo = useSelector(selectUser);
  useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
      setEnableCameraBtn(false);
    },
    hide: () => setVisible(false),
    getVisible: () => {
      return visible;
    },
  }));

  const [mediaType, setMediaType] = useState(MEDIA_TYPE.PICTURE);
  const [dangQuay, setDangQuay] = useState(false);
  const [videoQuantity, setVideoQuantity] = useState(RNCamera.Constants.VideoQuality['480p']);

  let refCamera;
  const [flashData, setFlashType] = useState({
    flashIcon: 'flash-off',
    flashMode: RNCamera.Constants.FlashMode.off,
  });
  const {
    giayToDuocChon,
    handleImage,
    setDialogLoading,
    currentPage,
    menuImageStep3Selected,
    menuImageStep2Selected,
    onPressPhanLoaiAnh,
    currentPosition,
    mucDoSelected,
    imageDataStep3,
    doiTuongDuocChupAnh,
    setMenuImageStep3Selected,
    getAnhDaUploadTheoMaHangMuc,
  } = props;

  const isDoiTuongTaiSanXeMay = doiTuongDuocChupAnh?.loai === 'XE_MAY' && doiTuongDuocChupAnh?.nhom === 'TAI_SAN';

  const [cameraType, setCameraType] = useState(RNCamera.Constants.Type.back);
  const [countPicture, setCountPicture] = useState(0);
  const [enableCameraBtn, setEnableCameraBtn] = useState(false);
  const [viTriChupAnhToanCanh, setViTriChupAnhToanCanh] = useState(0);
  const [loaiCat, setLoaiCat] = useState(0); //0 là điện thoại chụp dọc - 1 điện thoại chụp ngang

  const isHangMucTonThatKhac = menuImageStep3Selected && (menuImageStep3Selected.ma?.includes('ANH_KHAC') || menuImageStep3Selected.ma?.includes('ANH_TAI_SAN_KHAC'));
  const [editTenHangMuc, setEditTenHangMuc] = useState(false); //biến check để hiển thị View nhập tên hạng mục khác
  let refInputNhapTenHangMucKhac = useRef(null);
  const [isEditTenAnhTonThat, setIsEditTenHangMuc] = useState(false); //biến này để check là mỗi hạng mục chỉ được edit 1 lần

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
    getValues,
  } = useForm({
    defaultValues: {
      tenHangMucKhac: '',
    },
    mode: 'onChange',
  });

  const insect = useSafeAreaInsets();
  useEffect(() => {
    if (currentPage === 0) {
      if (!menuImageStep2Selected.hangMucChup) return;
      if (menuImageStep2Selected.hangMucChup.nhom_hang_muc === 'TOAN_CANH') setViTriChupAnhToanCanh(menuImageStep2Selected.indexOpened);
      // else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'SO_KHUNG') setViTriChupAnhToanCanh(4);
      // else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc == 'SO_MAY') setViTriChupAnhToanCanh(5);
    }
  }, [menuImageStep2Selected]);

  useEffect(() => {
    if (giayToDuocChon && giayToDuocChon.menuImageData && giayToDuocChon.menuImageData.nhom_hang_muc === 'ANH_GDV') setCameraType(RNCamera.Constants.Type.front);
  }, [giayToDuocChon]);

  //xử lý khi icon FLash được click
  const onPressChangeFlash = () => {
    //Nếu đang auto -> flash
    if (flashData.flashIcon === 'flash-auto') {
      setFlashType({
        flashIcon: 'flash',
        flashMode: RNCamera.Constants.FlashMode.on,
      });
    }
    //nếu đang flash -> tắt
    else if (flashData.flashIcon === 'flash') {
      setFlashType({
        flashIcon: 'flash-off',
        flashMode: RNCamera.Constants.FlashMode.off,
      });
    }
    //nếu đang tắt flash -> flash-auto
    else if (flashData.flashIcon === 'flash-off') {
      setFlashType({
        flashIcon: 'flash-auto',
        flashMode: RNCamera.Constants.FlashMode.auto,
      });
    }
  };

  //chuyển camera trước - sau
  const onPressSwapCamera = () => {
    if (cameraType === RNCamera.Constants.Type.back) setCameraType(RNCamera.Constants.Type.front);
    else if (cameraType === RNCamera.Constants.Type.front) setCameraType(RNCamera.Constants.Type.back);
  };
  //ẩn modal camera
  const onPressTatCameraModal = () => {
    setFlashType({
      flashIcon: 'flash-off',
      flashMode: RNCamera.Constants.FlashMode.off,
    });
    setCameraType(RNCamera.Constants.Type.back);
    setCountPicture(0);
    setLoaiCat(0);
    setVisible(false);
    // setDangQuay(false);
    // setMediaType(MEDIA_TYPE.PICTURE);
    // setVideoQuantity(RNCamera.Constants.VideoQuality['480p']);
    setEnableCameraBtn(false);
    setEditTenHangMuc(false);
    setValue('tenHangMucKhac', '');
    setIsEditTenHangMuc(false);
  };

  const getAnhTitle = () => {
    if (currentPage == 0) {
      if (!menuImageStep2Selected.hangMucChup) return;
      if (giayToDuocChon && giayToDuocChon.menuImageData) {
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'BANG_LAI') return BLXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'DANG_KY') return DKXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'DANG_KIEM') return DKTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'TEM_DANG_KIEM') return anhToanCanhTitle[9];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'ANH_GDV') return anhToanCanhTitle[12];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM) return anhToanCanhTitle[10];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.THONG_BAO_TAI_NAN) return anhToanCanhTitle[11];
      }
      if (menuImageStep2Selected.hangMucChup.nhom_hang_muc === 'TOAN_CANH') {
        if (!isDoiTuongTaiSanXeMay) return anhToanCanhTitle[viTriChupAnhToanCanh < 6 ? viTriChupAnhToanCanh : 6];
        else return anhToanCanhTitle[6];
      } else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc === 'SO_KHUNG') return anhToanCanhTitle[7];
      else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc === 'SO_MAY') return anhToanCanhTitle[8];
    } else if (currentPage === 1) return menuImageStep3Selected?.tenAlias || menuImageStep3Selected?.ten;
    else if (currentPage === 2) {
      if (giayToDuocChon && giayToDuocChon.menuImageData) {
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'BANG_LAI') return BLXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD) return CCCDTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'DANG_KY') return DKXTitle[giayToDuocChon.indexOpened];
        if (giayToDuocChon.menuImageData.nhom_hang_muc === 'DANG_KIEM') return DKTitle[giayToDuocChon.indexOpened];
        else return giayToDuocChon.menuImageData.ten;
      }
    }
  };

  const getSoAnhChupTheoHangMuc = () => {
    try {
      if (currentPage === 0) {
        return countPicture;
      } else if (currentPage === 1) {
        let hangMucChup = imageDataStep3.find((itemHangMuc) => itemHangMuc.ma === menuImageStep3Selected.ma);
        let soAnhDaChup = 0;
        let anhHangMucDaUpload = getAnhDaUploadTheoMaHangMuc(menuImageStep3Selected.ma);
        if (hangMucChup) soAnhDaChup = hangMucChup.images.length - 1;
        if (menuImageStep3Selected.ma?.includes('ANH_KHAC') || menuImageStep3Selected.ma?.includes('ANH_TAI_SAN_KHAC'))
          return soAnhDaChup + '/' + (MAX_SO_LUONG_ANH_CHUP_HANG_MUC_KHAC - anhHangMucDaUpload.length);
        else {
          if (MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC > 100) return soAnhDaChup;
          return soAnhDaChup + '/' + (MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC - anhHangMucDaUpload.length);
        }
      } else if (currentPage === 2) {
        return countPicture;
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      return countPicture;
    }
  };

  const onPressChupAnh = async () => {
    if (refCamera) {
      let cameraOptions = {fixOrientation: true};
      let cauHinhWidthAnh = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.ANH_TON_THAT_WIDTH, props.cauHinh);
      let cauHinhHeightAnh = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.ANH_TON_THAT_HEIGHT, props.cauHinh);
      if (currentPage !== 2) {
        cameraOptions.quality = isIOS ? 0.3 : 0.5;
        cameraOptions.width = 1800;
        if (cauHinhWidthAnh && cauHinhWidthAnh.gia_tri !== '0' && cauHinhHeightAnh && cauHinhHeightAnh.gia_tri !== '0') cameraOptions.quality = 0.8;
      } else cameraOptions.quality = 0.8;
      let dataImageCameraResponse = await refCamera.takePictureAsync(cameraOptions).catch((err) => {
        console.log('takePictureAsync err', err);
      });
      if (
        (GIAY_TO_CHUP_NGANNG.includes(giayToDuocChon?.menuImageData.nhom_hang_muc) || currentPage === 1) &&
        dataImageCameraResponse?.pictureOrientation !== 3 &&
        dataImageCameraResponse?.pictureOrientation !== 4 &&
        !__DEV__
      )
        return Alert.alert('Thông báo', 'Vui lòng chụp ảnh theo chiều ngang');

      let dataImage = dataImageCameraResponse;
      if (!dataImageCameraResponse) return; //trong trường hợp takePictureAsync bị vào Catch
      try {
        if (dataImage.pictureOrientation !== 1 && dataImage.deviceOrientation !== 1) dataImage = await xoayDocAnh(dataImage);
      } catch (error) {
        dataImage = dataImageCameraResponse;
      }
      dataImage.mediaType = 'photo';
      if (appSetting.rung) Vibration.vibrate(200);
      setCountPicture(countPicture + 1);
      //nếu đang chụp ảnh HỒ SƠ, GIẤY TỜ
      if (currentPage === 2) {
        if (
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD
        ) {
          Image.getSize(
            dataImage.uri,
            (imageWidth, imageHeight) => {
              let cropOptions = {
                path: dataImage.uri,
                freeStyleCropEnabled: true,
                cropperToolbarTitle: 'Xác nhận ảnh',
                disableCropperColorSetters: true,
                width: imageWidth,
                height: loaiCat === 0 ? imageWidth * 0.63 : imageHeight * 0.9,
              };
              if (giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM) cropOptions.height = loaiCat == 0 ? imageWidth * 0.67 : imageHeight * 0.9;
              setEnableCameraBtn(true);
              onPressTatCameraModal();
              if (!isIOS) {
                setDialogLoading(false);
                ImageCropPicker.openCropper(cropOptions)
                  .then((imageCropped) => {
                    setEnableCameraBtn(false);
                    handleImage(imageCropped, giayToDuocChon.menuImageData, giayToDuocChon.indexOpened, 0);
                  })
                  .catch((err) => {
                    console.log('ImageCropPicker.openCropper', err);
                    setEnableCameraBtn(false);
                  });
              } else {
                setDialogLoading(true);
                setTimeout(() => {
                  setDialogLoading(false);
                  ImageCropPicker.openCropper(cropOptions)
                    .then((imageCropped) => {
                      setEnableCameraBtn(false);
                      handleImage(imageCropped, giayToDuocChon.menuImageData, giayToDuocChon.indexOpened, 0);
                    })
                    .catch((err) => {
                      console.log('ImageCropPicker.openCropper', err);
                      setEnableCameraBtn(false);
                    });
                }, 1500);
              }
            },
            (err) => {
              setEnableCameraBtn(false);
              console.log('Image.getSize err', JSON.stringify(err));
              Alert.alert('Có lỗi xảy ra khi chụp ảnh, vui lòng chụp lại', err.message);
            },
          );
        } else {
          dataImage.path = dataImage.uri;
          handleImage(dataImage, giayToDuocChon.menuImageData, giayToDuocChon.indexOpened, 0);
          if (giayToDuocChon.menuImageData.nhom_hang_muc === 'TEM_DANG_KIEM') onPressTatCameraModal();
        }
      } else if (currentPage === 1) {
        dataImage.path = dataImage.uri;

        /* ADD TEXT TO IMAGE */
        let imageAddText = await chenThongTinLenAnh(dataImage);
        dataImage.path = (!isIOS ? 'file://' : '') + imageAddText;

        // let imageAddText = await ImageMarker.markImage({
        //   src: dataImage.path,
        //   markerSrc: R.images.img_logo_with_name, // icon uri
        //   position: 'bottomRight',
        //   // X: 100, // left
        //   // Y: 150, // top
        //   scale: 1, // scale of bg
        //   markerScale: 0.1, // scale of icon
        //   quality: 100, // quality of image
        //   saveFormat: 'png',
        // }).catch((err) => {
        //   Alert.alert('Chèn logo không thành công', err.message);
        // });

        /* END ADD TEXT TO IMAGE */

        if (cauHinhWidthAnh && cauHinhWidthAnh.gia_tri !== '0' && cauHinhHeightAnh && cauHinhHeightAnh.gia_tri !== '0') {
          try {
            let resizeResponse = await ImageResizer.createResizedImage(dataImage.path, +cauHinhWidthAnh.gia_tri, +cauHinhHeightAnh.gia_tri, 'PNG', 100, 0, undefined, undefined, {mode: 'stretch'});
            dataImage.path = resizeResponse.uri;
            handleImage(dataImage, menuImageStep3Selected, null, 0);
          } catch (error) {
            Alert.alert('Có lỗi khi chỉnh kích thước ảnh, vui lòng chụp lại', error.message);
          }
        } else handleImage(dataImage, menuImageStep3Selected, null, 0);
      } else if (currentPage === 0) {
        if (
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
          giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD
        ) {
          Image.getSize(
            dataImage.uri,
            (imageWidth, imageHeight) => {
              let cropOptions = {
                path: dataImage.uri,
                freeStyleCropEnabled: true,
                cropperToolbarTitle: 'Xác nhận ảnh',
                disableCropperColorSetters: true,
                width: imageWidth,
                height: loaiCat === 0 ? imageWidth * 0.63 : imageHeight * 0.9,
              };
              if (giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM) cropOptions.height = loaiCat == 0 ? imageWidth * 0.67 : imageHeight * 0.9;
              setEnableCameraBtn(true);
              onPressTatCameraModal();
              if (!isIOS) {
                setDialogLoading(false);
                ImageCropPicker.openCropper(cropOptions)
                  .then((imageCropped) => {
                    setEnableCameraBtn(false);
                    handleImage(imageCropped, menuImageStep2Selected, giayToDuocChon.indexOpened, 0);
                  })
                  .catch((error) => {
                    console.log('ImageCropPicker.openCropper error', JSON.stringify(error.message));
                    // if (error.code !== 'E_PICKER_CANCELLED') setEnableCameraBtn(false);
                    setEnableCameraBtn(false);
                  });
              } else {
                setDialogLoading(true);
                setTimeout(() => {
                  setDialogLoading(false);
                  ImageCropPicker.openCropper(cropOptions)
                    .then((imageCropped) => {
                      setEnableCameraBtn(false);
                      handleImage(imageCropped, menuImageStep2Selected, giayToDuocChon.indexOpened, 0);
                    })
                    .catch((error) => {
                      console.log('ImageCropPicker.openCropper error', JSON.stringify(error.message));
                      // if (error.code !== 'E_PICKER_CANCELLED') setEnableCameraBtn(false);
                      setEnableCameraBtn(false);
                    });
                }, 1500);
              }
            },
            (err) => {
              setEnableCameraBtn(false);
              Alert.alert('Có lỗi xảy ra khi chụp ảnh, vui lòng chụp lại', JSON.stringify(err.message));
            },
          );
        } else {
          // } else if (menuImageStep2Selected.hangMucChup.nhom_hang_muc !== DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM) {
          dataImage.path = dataImage.uri;
          let imageAddText = await chenThongTinLenAnh(dataImage);
          dataImage.path = (!isIOS ? 'file://' : '') + imageAddText;
          let menuImageStep2SelectedTmp = menuImageStep2Selected;
          handleImage(dataImage, menuImageStep2SelectedTmp, giayToDuocChon.indexOpened, 0);
          if (
            menuImageStep2SelectedTmp.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_KHUNG ||
            menuImageStep2SelectedTmp.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.SO_MAY ||
            menuImageStep2SelectedTmp.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TEM_DANG_KIEM ||
            giayToDuocChon.menuImageData.nhom_hang_muc === 'ANH_GDV'
          ) {
            onPressTatCameraModal();
            return;
          }
          // NẾU LÀ GIẤY CHỨNG NHẬN BẢO HIỂM -> chỉ chụp 2 cái
          // if (
          //   menuImageStep2SelectedTmp.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM &&
          //   (menuImageStep2SelectedTmp.indexOpened === 1 || countPicture === 2)
          // ) {
          //   onPressTatCameraModal();
          //   return;
          // }
          //nếu ảnh toàn cảnh đã chụp 6 ảnh - thì tắt modal camera
          if (menuImageStep2SelectedTmp.hangMucChup.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.TOAN_CANH && menuImageStep2SelectedTmp.indexOpened === 5 && !isDoiTuongTaiSanXeMay) {
            onPressTatCameraModal();
            return;
          }
          menuImageStep2SelectedTmp.indexOpened = menuImageStep2SelectedTmp.indexOpened + 1;
          setViTriChupAnhToanCanh(menuImageStep2SelectedTmp.indexOpened);
        }
        //  else {
        //   let menuImageStep2SelectedTmp = menuImageStep2Selected;
        //   dataImage.path = dataImage.uri;
        //   handleImage(dataImage, menuImageStep2SelectedTmp, giayToDuocChon.indexOpened, 0);
        //   onPressTatCameraModal();
        // }
      }
    }
  };

  const xoayDocAnh = async (dataImage) => {
    try {
      let gocQuay = 0;
      if (dataImage.pictureOrientation === 3 && dataImage.deviceOrientation === 3) gocQuay = 90;
      else if (dataImage.pictureOrientation === 2 && dataImage.deviceOrientation === 2) gocQuay = 180;
      else if (dataImage.pictureOrientation === 4 && dataImage.deviceOrientation === 4) gocQuay = 270;
      if (gocQuay > 0)
        Image.getSize(dataImage.uri, async (imageWidth, imageHeight) => {
          let response = await ImageResizer.createResizedImage(dataImage.uri, imageWidth, imageHeight, 'PNG', 100, gocQuay);
          dataImage.uri = response.uri;
          return dataImage;
        });

      return dataImage;
    } catch (error) {
      console.log('error', error);
      return dataImage;
    }
  };

  const chenThongTinLenAnh = async (dataImage) => {
    //ngày giờ / toạ độ / người / thông tin máy / biển số xe
    let txtChen = '';
    txtChen += userInfo.nguoi_dung.nsd;
    let tenMay = await DeviceInfo.getBrand();
    txtChen += '\n' + moment().format('HH:mm DD/MM/YYYY') + ' ';
    txtChen += tenMay;
    if (currentPosition && currentPosition.coords) txtChen += '\n' + currentPosition.coords.longitude.toFixed(3) + ' ; ' + currentPosition.coords.latitude.toFixed(3) + ' ';
    if (currentPage === 1) {
      if (props.profileData.ho_so.doi_tuong) txtChen += '\n' + props.profileData.ho_so.doi_tuong;
      if (menuImageStep3Selected) txtChen += '\n' + (menuImageStep3Selected.tenAlias || menuImageStep3Selected.ten);
      if (mucDoSelected?.ten) txtChen += (mucDoSelected.ten === 'Chưa xác định hư hỏng' || mucDoSelected.ten === 'Tổn thất toàn bộ' ? '\n' : '-') + mucDoSelected.ten;
    }

    let imageAddText = await ImageMarker.markText({
      backgroundImage: {
        src: dataImage.path,
        scale: 1,
      },
      watermarkTexts: [
        {
          text: txtChen,
          positionOptions: {
            position: Position.bottomRight,
          },
          style: {
            color: '#FFF',
            fontSize: isIOS ? 30 : 30,
          },
        },
      ],
      quality: 100,
    }).catch((err) => {
      Alert.alert('Chèn toạ độ không thành công', err.message);
      return '';
    });
    return imageAddText;
  };
  const onRecordingStart = (event) => {
    console.log('onRecordingStart', event);
  };
  const onRecordingEnd = (event) => {
    console.log('onRecordingEnd', event);
  };
  const onPressQuayVideo = async () => {
    try {
      if (!dangQuay) {
        let videoOptions = {
          quality: videoQuantity, //chất lượng video
          //   orientation: '', //Chỉ định hướng mà chúng tôi đã sử dụng để quay video,
          maxDuration: VIDEO_DURATION, //thời lượng video tính theo s
          //   maxFileSize: '', //(int lớn hơn 0) Tùy chọn này chỉ định tốc độ bit video mong muốn.
          //   mute: '',//tắt tiếng khi quay
          //   mirrorVideo: '',
          //   path: '',đường dẫn lưu
          //   videoBitrate: '', //Tùy chọn này chỉ định tốc độ bit video mong muốn.
          /** iOS only */
          //   codec: '', //Tùy chọn này chỉ định codec của video đầu ra. Đặt codec chỉ được hỗ trợ trên iOS> = 10.
          //   fps: '',
        };
        setDangQuay(true);
        let recordResponse = await refCamera.recordAsync(videoOptions);
        let fetchBlodResponse = await ReactNativeBlobUtil.fs.stat(recordResponse.uri);
        recordResponse.size = fetchBlodResponse.size * 0.000001; //đổi ra Mb
        recordResponse.mediaType = 'video';
        console.log('recordAsync response', recordResponse);
        // CameraRoll.saveToCameraRoll(response.uri, 'video');
        setDangQuay(false);

        if (currentPage === 2) {
        } else if (currentPage === 1) {
          recordResponse.path = recordResponse.uri;
          handleImage(recordResponse, menuImageStep3Selected, null, 0);
        } else if (currentPage === 0) {
        }
      } else {
        setDangQuay(false);
        refCamera.stopRecording(); //sau khi gọi stop recording thì mới có recordResponse
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onSubmitTenHangMucKhac = (newTenKhacMucKhac) => {
    try {
      if (!newTenKhacMucKhac?.trim()) {
        setValue('tenHangMucKhac', '');
        return setEditTenHangMuc(false);
      }
      //kiểm tra xem tên hạng mục có tồn tại hay chưa, nếu tồn tại rồi thì k cho sử dụng
      let isTonTaiTenHangMucKhac = false;
      let sttHangMucCuoiCung = 0;
      imageDataStep3.forEach((item, index) => {
        if (
          (item.ma.includes('ANH_KHAC') || item.ma.includes('ANH_TAI_SAN_KHAC')) &&
          (item.tenAlias?.toUpperCase() === newTenKhacMucKhac.toUpperCase() || item.ten?.toUpperCase() === newTenKhacMucKhac.toUpperCase())
        )
          isTonTaiTenHangMucKhac = true;
        if (item.ma.includes('ANH_KHAC') || item.ma.includes('ANH_TAI_SAN_KHAC')) {
          let stt = 0;
          let arrMaAnhKhac = item.ma.split('_');
          if (arrMaAnhKhac.length >= 3) stt = arrMaAnhKhac[arrMaAnhKhac.length - 1];
          if (stt > +sttHangMucCuoiCung) sttHangMucCuoiCung = +stt;
        }
      });
      if (isTonTaiTenHangMucKhac) {
        setIsEditTenHangMuc(false);
        return Alert.alert('Thông báo', `Tên hạng mục "${newTenKhacMucKhac}" đã tồn tại. Vui lòng nhập tên khác.`);
      }
      let ma = 'ANH_KHAC';
      // xử lý với ẢNH KHÁC XE MÁY
      if (menuImageStep3Selected.ma.includes('ANH_KHAC_XM')) ma = 'ANH_KHAC_XM';
      else if (menuImageStep3Selected.ma.includes('ANH_TAI_SAN_KHAC')) ma = 'ANH_TAI_SAN_KHAC'; // xử lý với ẢNH KHÁC XE MÁY
      setMenuImageStep3Selected({...menuImageStep3Selected, tenAlias: newTenKhacMucKhac?.trim(), ma: ma + '_' + (sttHangMucCuoiCung + 1)});
      setEditTenHangMuc(false);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  //nút next trên bàn phím được bấm
  const onPressChuyenSangHangMucKhac = () => {
    try {
      setIsEditTenHangMuc(false);
      setValue('tenHangMucKhac', '');
      let sttHangMucCuoiCung = 0;
      let arrMaAnhKhacHienTai = menuImageStep3Selected.ma.split('_');
      let sttAnhKhacHienTai = arrMaAnhKhacHienTai[arrMaAnhKhacHienTai.length - 1];
      imageDataStep3.forEach((item, index) => {
        if (item.ma.includes('ANH_KHAC') || item.ma.includes('ANH_TAI_SAN_KHAC')) {
          let stt = 0;
          let arrMaAnhKhac = item.ma.split('_');
          if (arrMaAnhKhac.length >= 3) stt = arrMaAnhKhac[arrMaAnhKhac.length - 1];
          if (stt > +sttHangMucCuoiCung) sttHangMucCuoiCung = +stt;
        }
      });
      if (sttHangMucCuoiCung > 0 && sttAnhKhacHienTai > sttHangMucCuoiCung) return Alert.alert('Thông báo', 'Chụp một ảnh trước khi chuyển sang hạng mục khác tiếp theo');
      let ma = 'ANH_KHAC';
      let tenAlias = 'Ảnh Tổn Thất Khác';
      // xử lý với ẢNH KHÁC XE MÁY
      if (menuImageStep3Selected.ma.includes('ANH_KHAC_XM')) ma = 'ANH_KHAC_XM';
      else if (menuImageStep3Selected.ma.includes('ANH_TAI_SAN_KHAC')) {
        // xử lý với ẢNH KHÁC XE MÁY
        ma = 'ANH_TAI_SAN_KHAC';
        tenAlias = 'Tài sản khác';
      }
      setMenuImageStep3Selected({...menuImageStep3Selected, tenAlias: tenAlias + ' ' + (sttHangMucCuoiCung + 1), ma: ma + '_' + (sttHangMucCuoiCung + 1)});
      setCountPicture(0);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      logErrorTryCatch(error);
    }
  };
  const getDieuKienHienThiIconEditTen = () => {
    let hangMucChup = imageDataStep3.find((itemHangMuc) => itemHangMuc.ma === menuImageStep3Selected.ma);
    let soAnhDaChup = 0;
    if (hangMucChup) soAnhDaChup = hangMucChup.images.length - 1;
    if (isHangMucTonThatKhac && menuImageStep3Selected?.daCoTrenHeThong !== true && soAnhDaChup === 0) return true;
    return false;
  };

  /* RENDER */
  return (
    <Modal isVisible={visible} swipeDirection={'down'} style={styles.modal} avoidKeyboard>
      <View style={styles.modalCameraView}>
        <View style={styles.modalCameraContent}>
          <View style={{flex: 1}}>
            <RNCamera
              ref={(ref) => (refCamera = ref)}
              playSoundOnCapture={appSetting.amThanhKhiChup}
              // onCameraReady={prepareRatio}
              ratio={
                (currentPage === 2 || currentPage === 0) &&
                (giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD)
                  ? '16:9'
                  : undefined
              } //nếu đang chụp ảnh HỒ SƠ, GIẤY TỜ
              style={styles.cameraPreview}
              type={cameraType}
              flashMode={flashData.flashMode}
              captureAudio={true}
              zoom={0}
              useNativeZoom={true}
              androidCameraPermissionOptions={{
                title: 'Cho phép ' + APP_NAME + ' truy cập camera của bạn',
                message: APP_NAME + ' muốn truy cập camera của bạn để chụp ảnh',
                buttonPositive: 'Đồng ý',
                buttonNegative: 'Để sau',
              }}
              //   onRecordingStart={onRecordingStart}
              //   onRecordingEnd={onRecordingEnd}
              defaultVideoQuality={videoQuantity}
              playSoundOnRecord={true}>
              {(currentPage === 2 || currentPage === 0) &&
                (giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                  giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD) && (
                  <View style={styles.cropView}>
                    <View style={[styles.cropTopView, {marginBottom: 10}]} />
                    <View
                      // style={[styles.khungCatAnhCamera, giayToDuocChon?.menuImageData.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ? styles.sizeBLXDoc : styles.sizeDKDoc]}
                      style={[
                        styles.khungCatAnhCamera,
                        giayToDuocChon?.menuImageData.nhom_hang_muc != DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM
                          ? loaiCat == 0
                            ? styles.sizeBLXDoc
                            : styles.sizeBLXNgang
                          : loaiCat == 0
                          ? styles.sizeDKDoc
                          : styles.sizeDKNgang,
                      ]}
                    />
                    <View style={[styles.cropTopView, {marginTop: 10}]} />
                  </View>
                )}
              <View style={[styles.topViewCamera, {top: insect.top}]}>
                <TouchableOpacity onPress={onPressTatCameraModal} style={styles.btnCloseCamera}>
                  <Icon.AntDesign name="arrowleft" size={40} color={colors.WHITE} />
                </TouchableOpacity>
                {currentPage === 1 && !dangQuay && (
                  <TouchableOpacity
                    onPress={() => {
                      if (countPicture === 0) return Alert.alert('Thông báo', 'Vui lòng chụp ít nhất 1 ảnh để phân loại');
                      onPressTatCameraModal();
                      setDialogLoading(true);
                      setTimeout(() => {
                        onPressPhanLoaiAnh(menuImageStep3Selected);
                        setDialogLoading(false);
                      }, 1500);
                    }}
                    style={styles.btnDanhGia}>
                    <Text children="Đánh giá" style={styles.txtDanhGia} />
                  </TouchableOpacity>
                )}
                {(currentPage === 2 || currentPage === 0) &&
                  (giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.BANG_LAI_XE ||
                    giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KY_XE ||
                    giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.DANG_KIEM ||
                    giayToDuocChon?.menuImageData.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.CCCD) && (
                    <TouchableOpacity onPress={() => setLoaiCat(loaiCat === 0 ? 1 : 0)} style={styles.btnDanhGia}>
                      <Icon.MaterialCommunityIcons name="crop-rotate" size={40} color="#FFF" />
                    </TouchableOpacity>
                  )}
              </View>

              <View style={styles.anhTitleView}>
                {giayToDuocChon && (GIAY_TO_CHUP_NGANNG.includes(giayToDuocChon?.menuImageData.nhom_hang_muc) || currentPage === 1) && (
                  <Text children="Chụp ảnh ngang" style={styles.txtTitleAnh} font="bold20" />
                )}
                <View style={{flexDirection: 'row', paddingHorizontal: 30, alignItems: 'center', justifyContent: 'center'}}>
                  <Text children={getAnhTitle()} style={styles.txtTitleAnh} font="bold20" />
                  {/* Chỉ hiển thị nút EDIT tên hạng mục trong trường hợp là hạng mục tổn thất khác và chưa upload lên hệ thống */}
                  {getDieuKienHienThiIconEditTen() && (
                    <TouchableOpacity
                      onPress={() => {
                        if (isEditTenAnhTonThat) return Alert.alert('Thông báo', 'Tên hạng mục chỉ được sửa một lần');
                        setEditTenHangMuc(!editTenHangMuc);
                      }}>
                      <Icon.AntDesign name="edit" size={20} color="#FFF" />
                    </TouchableOpacity>
                  )}
                </View>
                {currentPage === 1 && (
                  <TouchableOpacity>
                    <Text children={mucDoSelected?.ten || 'Chưa đánh giá'} style={styles.txtTitleAnh} font="bold20" />
                  </TouchableOpacity>
                )}
              </View>
              {mediaType === MEDIA_TYPE.PICTURE && (
                <View style={styles.countPicture}>
                  <Text children={getSoAnhChupTheoHangMuc()} style={styles.txtCountPicture} />
                </View>
              )}
            </RNCamera>
          </View>
          <View style={styles.bottomCameraView}>
            {/* {currentPage === 1 && (
              <View style={styles.mediaTypeView}>
                <View style={styles.mediaTypeCenterView}>
                  <TouchableOpacity style={[styles.btnMediaTypeAnh, mediaType === MEDIA_TYPE.PICTURE && styles.btnMediaTypeActive]} onPress={() => setMediaType(MEDIA_TYPE.PICTURE)}>
                    <Text children="Ảnh" style={[styles.txtMediaType, mediaType === MEDIA_TYPE.PICTURE && styles.txtMediaTypeActive]} />
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.btnMediaTypeVideo, mediaType === MEDIA_TYPE.VIDEO && styles.btnMediaTypeActive]} onPress={() => setMediaType(MEDIA_TYPE.VIDEO)}>
                    <Text children="Video" style={[styles.txtMediaType, mediaType === MEDIA_TYPE.VIDEO && styles.txtMediaTypeActive]} />
                  </TouchableOpacity>
                </View>
              </View>
            )} */}

            <View style={styles.btnsCameraView}>
              {!dangQuay && (
                <View style={{flexDirection: 'row', flex: 1}}>
                  {/* {isHangMucTonThatKhac && (
                    <TouchableOpacity style={styles.btnCameraView}>
                      <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color="transparent" />
                    </TouchableOpacity>
                  )} */}
                  {isHangMucTonThatKhac && (
                    <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
                      <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity onPress={onPressChangeFlash} style={styles.btnCameraView}>
                    <Icon.MaterialCommunityIcons name={flashData.flashIcon} size={30} color={colors.WHITE} />
                  </TouchableOpacity>
                </View>
              )}

              {mediaType === MEDIA_TYPE.PICTURE && (
                <TouchableOpacity style={[styles.btnCameraView]} onPress={onPressChupAnh} disabled={enableCameraBtn}>
                  <Icon.MaterialCommunityIcons name={'circle-slice-8'} size={100} color={colors.WHITE} />
                </TouchableOpacity>
              )}
              {mediaType === MEDIA_TYPE.VIDEO && (
                <TouchableOpacity style={[styles.btnVideoView]} onPress={onPressQuayVideo} disabled={enableCameraBtn}>
                  <View style={styles.btnVideoBorderView}>
                    {!dangQuay && <Icon.Entypo name={'controller-record'} size={60} color={colors.RED3} />}
                    {dangQuay && <Icon.Entypo name={'controller-stop'} size={60} color={colors.RED3} />}
                  </View>
                </TouchableOpacity>
              )}
              {!dangQuay && (
                <View style={{flexDirection: 'row', flex: 1}}>
                  {!isHangMucTonThatKhac && (
                    <TouchableOpacity style={styles.btnCameraView} onPress={onPressSwapCamera}>
                      <Icon.Ionicons name={'camera-reverse-outline'} size={35} color={colors.WHITE} />
                    </TouchableOpacity>
                  )}
                  {isHangMucTonThatKhac && (
                    <TouchableOpacity style={[styles.btnCameraView, {flexDirection: 'row'}]} onPress={onPressChuyenSangHangMucKhac}>
                      <Text children={'Chụp hạng \nmục khác'} color="#FFF" font="regular11" />
                      <Icon.Ionicons name="arrow-forward" size={35} color={colors.WHITE} />
                    </TouchableOpacity>
                  )}
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
      {editTenHangMuc && (
        <View style={[styles.inputTenHangMucContainer]}>
          <Controller
            control={control}
            name="tenHangMucKhac"
            render={({field: {onChange, value}}) => (
              <>
                <TouchableOpacity
                  style={styles.closeInputTenHangMucView}
                  onPress={() => {
                    setEditTenHangMuc(false);
                    setIsEditTenHangMuc(false);
                    onChange('');
                  }}>
                  <Icon.AntDesign name="closecircle" size={20} color={colors.PRIMARY} />
                </TouchableOpacity>
                <View style={styles.inputTenHangMucContent}>
                  <TextInput
                    value={value}
                    placeholder="Nhập tên hạng mục khác"
                    onChangeText={(value) => {
                      if (!value) setIsEditTenHangMuc(false); //nếu k cập nhật tên hạng mục -> coi như edit = false
                      if (value.length <= 100) {
                        !isEditTenAnhTonThat && value.trim() && setIsEditTenHangMuc(true);
                        onChange(value);
                      }
                    }}
                    ref={refInputNhapTenHangMucKhac}
                    style={styles.inputTenHangMucKhac}
                    onSubmitEditing={() => onSubmitTenHangMucKhac(value)}
                  />
                  <Text children={100 - value.length} style={styles.txtLengthInputTenHangMuc} />
                </View>
              </>
            )}
          />
        </View>
      )}
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  cameraPreview: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnsCameraView: {
    flexDirection: 'row',
    backgroundColor: colors.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  bottomCameraView: {
    backgroundColor: colors.BLACK,
    height: 140,
  },
  btnCameraView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnVideoView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnVideoBorderView: {
    borderWidth: 3,
    borderColor: '#FFF',
    borderRadius: 50,
  },
  topViewCamera: {
    position: 'absolute',
    left: 0,
    // top: !isIOS ? 20 : 50,
    right: 0,
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    flexDirection: 'row',
  },
  btnCloseCamera: {},
  btnDanhGia: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countPicture: {
    position: 'absolute',
    right: 15,
    bottom: 5,
  },
  txtCountPicture: {
    fontSize: 20,
    color: '#FFF',
    fontWeight: 'bold',
  },
  khungCatAnhCamera: {
    borderWidth: 3,
    borderStyle: 'dashed',
    borderRadius: 20,
  },
  //kích thước BANG_LAI_XE DỌC
  sizeBLXDoc: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.63,
  },
  //kích thước DANG_KIEM DỌC
  sizeDKDoc: {
    width: dimensions.width - 30,
    height: (dimensions.width - 30) * 0.67,
  },

  //kích thước BANG_LAI_XE DỌC
  sizeBLXNgang: {
    width: dimensions.width - 30,
    height: dimensions.height * 0.63,
  },
  //kích thước DANG_KIEM DỌC
  sizeDKNgang: {
    width: dimensions.width - 30,
    height: dimensions.height * 0.67,
  },

  txtDanhGia: {
    fontWeight: 'bold',
    fontSize: 16,
    color: colors.WHITE,
    marginRight: 5,
  },
  anhTitleView: {
    position: 'absolute',
    bottom: 10,
  },
  txtTitleAnh: {
    color: '#FFF',
    // paddingHorizontal: 30,
    textAlign: 'center',
    marginRight: spacing.default,
  },
  cropView: {
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cropTopView: {
    flex: 1,
    backgroundColor: '#000',
    width: dimensions.width,
    opacity: 0.8,
  },
  mediaTypeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing.smaller,
  },
  mediaTypeCenterView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#FFF',
    borderRadius: 10,
    width: dimension.width / 3,
  },
  txtMediaType: {
    color: '#FFF',
    fontWeight: 'bold',
  },
  btnMediaType: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnMediaTypeAnh: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnMediaTypeVideo: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnMediaTypeActive: {backgroundColor: '#FFF'},
  txtMediaTypeActive: {
    color: '#000',
  },
  inputTenHangMucContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: dimensions.height / 8,
    backgroundColor: '#FFF',
    justifyContent: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  inputTenHangMucKhac: {
    fontSize: 20,
    fontFamily: 'SFProDisplay-Bold',
    flex: 1,
    paddingRight: spacing.default,
  },
  txtLengthInputTenHangMuc: {
    fontSize: 20,
    fontFamily: 'SFProDisplay-Bold',
  },
  inputTenHangMucContent: {flexDirection: 'row', borderBottomWidth: 0.4, borderColor: colors.GRAY1, marginHorizontal: spacing.default, paddingBottom: spacing.tiny},
  closeInputTenHangMucView: {
    position: 'absolute',
    top: spacing.default,
    right: spacing.default,
  },
});

const ModalCameraWithVideoConnectMemo = memo(ModalCameraWithVideoComponent, isEqual);
export const ModalCameraWithVideo = ModalCameraWithVideoConnectMemo;
