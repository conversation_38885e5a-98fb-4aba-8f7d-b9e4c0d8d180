import {NGAY_CHUYEN_DOI} from '@app/commons/Constant';
import {colors, dimension} from '@app/commons/Theme';
import {selectCities} from '@app/redux/slices/CitiesSlice';
import {FontSize, dimensions, scale, spacing, vScale} from '@app/theme';
import {DropdownPicker, Empty, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';

const modalHeight = dimensions.height;
const titleDropdownInput = ['Tỉnh thành', 'Quận huyện', 'Xã phường'];

const ModalLuaChonGaraComponent = forwardRef(({listGara, listGaraRoot, setModalSelectedData, searchGara, profileData}, ref) => {
  useImperativeHandle(ref, () => ({
    show: () => setVisible(true),
    hide: () => setVisible(false),
  }));

  const cities = useSelector(selectCities);

  const [visible, setVisible] = useState(false);

  const [listGaraDisplay, setListGaraDisplay] = useState(listGara); //list Gara hiển thị
  const [searchInput, setSearchInput] = useState('');
  const insect = useSafeAreaInsets();

  const [inputErr, setInputErr] = useState(['', '']);
  const [openCity, setOpenCity] = useState(false);
  const [citySelected, setCitySelected] = useState(null); //thành phố được chọn

  const [districtsDataDropDown, setDistrictsDataDropDown] = useState([]);
  const [openDistrict, setOpenDistrict] = useState(false);
  const [districtSelected, setDistrictSelected] = useState(null); //quận huyện được chọn

  useEffect(() => {
    setListGaraDisplay([...listGara]);
  }, [listGara]);

  //nếu thay đổi thành phố -> lấy gara theo thành phố
  useEffect(() => {
    filterGara(citySelected, null);
    let itemSelected = cities.find((item) => item.ma === citySelected);
    if (itemSelected) setDistrictsDataDropDown(itemSelected.district);
  }, [citySelected]);

  //nếu thay đổi quận huyện -> lấy gara theo Thành phố + quận huyện
  useEffect(() => {
    filterGara(citySelected, districtSelected);
  }, [districtSelected]);

  const closeModal = () => {
    setVisible(false);
    resetInputModal();
  };

  const resetInputModal = () => {
    setOpenCity(false);
    setCitySelected(null);
    setOpenDistrict(false);
    setDistrictSelected(null);
    setDistrictsDataDropDown([]);
    setInputErr(['', '']);
  };

  const closeDropdown = (title) => {
    if (title === titleDropdownInput[0] && openDistrict) setOpenDistrict(false);
    else if (title === titleDropdownInput[1]) {
      openCity && setOpenCity(false);
      if (!citySelected) setInputErr(['Vui lòng chọn tỉnh thành', '']);
    }
  };

  const filterGara = (citySelected, districtSelected) => {
    if (citySelected) {
      let listGaraDisplayTmp = listGaraRoot;
      let garaFilter = listGaraDisplayTmp?.filter((item) => item.tinh_thanh === citySelected);
      if (districtSelected) garaFilter = listGaraDisplayTmp?.filter((item) => item.quan_huyen === districtSelected);
      if (searchInput) garaFilter = garaFilter?.filter((item) => item.ten.toUpperCase().indexOf(searchInput.trim().toUpperCase()) > -1);
      if (garaFilter.length > 0) {
        // trường hợp filter theo tỉnh thành có dữ liệu
        setListGaraDisplay([...garaFilter]);
        return garaFilter;
      } else {
        // trường hợp filter theo tỉnh thành mà tỉnh thành không có dữ liệu gara thì cho tìm hết
        let dataGaraHienThi = [];
        if (searchInput) dataGaraHienThi = listGaraDisplayTmp?.filter((item) => item.ten.toUpperCase().indexOf(searchInput.trim().toUpperCase()) > -1);
        setListGaraDisplay([...dataGaraHienThi]);
        return dataGaraHienThi;
      }
      // setListGaraDisplay([...garaFilter]);
      // return garaFilter;
    }
  };

  const onChangeValueDropdown = (title, items, itemValueSelected) => {
    setSearchInput('');
    let itemSelected = items.find((item) => item.ma === itemValueSelected);
    if (itemSelected) {
      if (title === titleDropdownInput[0]) {
        setDistrictsDataDropDown(itemSelected.district);
        setInputErr(['', '']);
        setDistrictSelected(null);
      }
    }
  };

  const onChangeValueGara = (value) => {
    if (!value.trim()) {
      if (citySelected) filterGara(citySelected, districtSelected);
      else setListGaraDisplay([...listGaraRoot.slice(0, 50)]);
    } else setListGaraDisplay([...listGaraRoot.slice(0, 50)]);
    setSearchInput(value);
    onPressSearch(value);
  };

  const onPressSearch = (value) => {
    if (!citySelected && !districtSelected) searchGara(searchInput);
    //nếu mà có Thành phố được chọn -> chỉ search theo listGara đã filter theo thành phố or theo cả quận huyện
    else if (citySelected) {
      let result = filterGara(citySelected, districtSelected).filter((item) => item.ten.toUpperCase().indexOf(value?.trim().toUpperCase() || searchInput.trim().toUpperCase()) > -1);
      setListGaraDisplay([...result]);
    }
    //không thì search theo
    // else {
    //   let result = [];
    //   for (let i = 0; i < props.listGaraRoot.length; i++) {
    //     if (
    //       props.listGaraRoot[i].ten.toUpperCase().indexOf(searchInput.toUpperCase()) > -1 ||
    //       props.listGaraRoot[i].dia_chi.toUpperCase().indexOf(searchInput.toUpperCase()) > -1 ||
    //       props.listGaraRoot[i].dien_thoai.split(' ').join('').toUpperCase().indexOf(searchInput.toUpperCase()) > -1
    //     )
    //       result.push(props.listGaraRoot[i]);
    //   }
    //   setListGaraDisplay([...result]);
    //   //gọi API để search theo tên
    // }
  };

  /* RENDER */
  const renderGaraItem = ({item}) => {
    return (
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <TouchableOpacity
          style={styles.profileItemCenterView}
          onPress={() => {
            setModalSelectedData(item);
            closeModal();
          }}>
          <Text style={styles.profileTxtHoSo}>{item.ten}</Text>
          <Text style={styles.profileTxtHoSo}>{item.dia_chi}</Text>
          <View style={styles.profileItemDetail}>
            <Text style={styles.profileTxtThoiGian}>{item.dien_thoai}</Text>
          </View>
        </TouchableOpacity>
      </LinearGradient>
    );
  };
  const renderSearchInput = () => {
    return (
      <>
        <Text children="Tên Gara" style={styles.dropDownTitle} />
        <SearchBar
          value={searchInput}
          onPressSearch={() => onPressSearch(searchInput)}
          onTextChange={(value) => onChangeValueGara(value)}
          containerStyle={{shadowOpacity: 0}}
          // onSubmitEditing={() => onPressSearch(searchInput)}
          searchViewStyle={{margin: 0}}
          placeholder="Nhập tên Gara"
          onFocus={() => {
            openCity && setOpenCity(false);
            openDistrict && setOpenDistrict(false);
          }}
        />
      </>
    );
  };
  return (
    <Modal
      isVisible={visible}
      onSwipeComplete={() => closeModal()}
      onBackdropPress={() => closeModal()}
      swipeDirection={['down']}
      // scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
      // scrollOffset={scrollOffSet}
      // scrollOffsetMax={modalHeight - contentModalHeight - 300} // content height - ScrollView height
      propagateSwipe={true}
      style={styles.modal}>
      <View style={[styles.modalView, {marginTop: insect.top}]}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Danh sách gara" />
          <TouchableOpacity style={styles.closeView} onPress={() => closeModal()}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>
        <View contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={true} style={styles.modalContentView}>
          <View style={{flexDirection: 'row', marginBottom: spacing.small}}>
            <View style={{flex: 1, marginRight: spacing.smaller}}>
              <DropdownPicker
                title={titleDropdownInput[0]}
                zIndex={6000}
                items={cities.filter((item) => item.ngay_ad >= 20250701)}
                itemSelected={citySelected}
                setItemSelected={setCitySelected}
                isOpen={openCity}
                setOpen={setOpenCity}
                placeholder="Chọn Tỉnh thành"
                onOpen={() => closeDropdown(titleDropdownInput[0])}
                // onChangeValue={onChangeValueDropdown}
                inputErr={inputErr[0]}
                isRequired={true}
                cleared={true}
                schema={{
                  label: 'ten',
                  value: 'ma',
                }}
                onPressClear={() => {
                  setCitySelected(null);
                  setDistrictSelected(null);
                  setDistrictsDataDropDown([]);
                  setListGaraDisplay([...listGara]);
                }}
                containerStyle={{marginVertical: 0}}
              />
            </View>

            <View style={{flex: 1}}>
              <DropdownPicker
                title={titleDropdownInput[2]}
                zIndex={6000}
                items={districtsDataDropDown.filter((item) => item.ngay_ad >= 20250701)}
                itemSelected={districtSelected}
                setItemSelected={setDistrictSelected}
                isOpen={openDistrict}
                setOpen={setOpenDistrict}
                placeholder={`Chọn Xã phường`}
                onOpen={() => closeDropdown(titleDropdownInput[2])}
                // onChangeValue={onChangeValueDropdown}
                inputErr={inputErr[1]}
                isRequired={true}
                cleared={true}
                onPressClear={() => {
                  setDistrictSelected(null);
                  filterGara(citySelected, null);
                }}
                schema={{
                  label: 'ten',
                  value: 'ma',
                }}
                containerStyle={{marginVertical: 0}}
              />
            </View>
          </View>
          <View style={{marginBottom: spacing.small, zIndex: -1}}>{renderSearchInput()}</View>
          <ScrollView style={{zIndex: -1}}>
            <View>
              <FlatList
                data={listGaraDisplay}
                renderItem={renderGaraItem}
                style={{marginBottom: 250}}
                keyExtractor={(item, index) => index.toString()}
                ListEmptyComponent={<Empty imageStyle={{width: dimension.width / 3, height: dimension.width / 3}} />}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  scrollView: {},
  modalView: {
    flex: 1,
    backgroundColor: colors.WHITE,
    height: modalHeight,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: FontSize.size16,
    fontWeight: 'bold',
    marginVertical: vScale(spacing.small),
    flex: 1,
    marginLeft: vScale(30),
  },
  closeView: {
    marginRight: scale(spacing.small),
  },
  modalContentView: {
    marginHorizontal: scale(spacing.small),
    marginTop: vScale(spacing.small),
  },
  profileItemView: {
    flexDirection: 'row',
    marginVertical: 5,
    paddingLeft: scale(spacing.small),
    paddingRight: scale(spacing.small),
    borderRadius: 10,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: vScale(spacing.tiny),
    paddingRight: scale(spacing.tiny),
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTxtHoSo: {
    marginBottom: vScale(spacing.tiny),
    color: colors.BLACK,
    fontSize: FontSize.size14,
  },
  profileTxtThoiGian: {
    fontSize: FontSize.size12,
    color: colors.GRAY5,
  },
  dropDownTitle: {
    marginBottom: vScale(spacing.tiny),
    fontWeight: 'bold',
  },
});

export const ModalLuaChonGara = memo(ModalLuaChonGaraComponent, isEqual);
