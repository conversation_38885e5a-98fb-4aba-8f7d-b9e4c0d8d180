import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Empty, HeaderModal, Icon, Text} from '@app/components';
import {selectMucDoTonThat} from '@app/redux/slices/CategoryCommonSlice';
import {FontSize, spacing} from '@app/theme';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Alert, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';

const ModalAIDataComponent = forwardRef(({dataAI, setDataAI}, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: showModal,
      hide: hideModal,
    }),
    [],
  );
  const [isVisible, setIsVisible] = useState(false);
  const mucDoTonThat = useSelector(selectMucDoTonThat);
  //RADIO PHƯƠNG ÁN KHẮC PHỤC
  const [recoveryPlantRadio, setRecoveryPlantRadio] = useState([
    {
      id: '1',
      label: DATA_CONSTANT.RECOVERY_PLANT.SUA_CHUA.label,
      value: DATA_CONSTANT.RECOVERY_PLANT.SUA_CHUA.value,
      containerStyle: {flex: 1},
      selected: true,
      size: 20,
    },
    {
      id: '2',
      label: DATA_CONSTANT.RECOVERY_PLANT.THAY_THE.label,
      value: DATA_CONSTANT.RECOVERY_PLANT.THAY_THE.value,
      containerStyle: {flex: 1},
      selected: false,
      size: 20,
    },
    {
      id: '3',
      label: DATA_CONSTANT.RECOVERY_PLANT.KHONG_XAC_DINH.label,
      value: DATA_CONSTANT.RECOVERY_PLANT.KHONG_XAC_DINH.value,
      containerStyle: {flex: 1},
      selected: false,
      size: 20,
    },
  ]);
  //RADIO THU HỒI VẬT TƯ
  const [recoveryRadio, setRecoveryRadio] = useState([
    {
      id: '1',
      label: DATA_CONSTANT.RECOVERY.CO.label,
      value: DATA_CONSTANT.RECOVERY.CO.value,
      containerStyle: {flex: 1},
      selected: false,
      size: 20,
      disabled: false,
    },
    {
      id: '2',
      label: DATA_CONSTANT.RECOVERY.KHONG.label,
      value: DATA_CONSTANT.RECOVERY.KHONG.value,
      containerStyle: {flex: 1},
      selected: true,
      size: 20,
      disabled: false,
    },
  ]);
  const showModal = () => {
    setIsVisible(true);
  };
  const hideModal = () => {
    setIsVisible(false);
  };
  const getMucDoTitle = (maMucDo) => {
    return mucDoTonThat.find((item) => item.ma === maMucDo)?.ten || '...';
  };
  const getThayTheTitle = (maThayThe) => {
    return recoveryPlantRadio.find((item) => item.value === maThayThe)?.label || '...';
  };
  const getThuHoiTitle = (maThuHoi) => {
    return recoveryRadio.find((item) => item.value === maThuHoi)?.label || '...';
  };
  const xoaHangMuc = (viTriXoa) => {
    Alert.alert('Thông báo', 'Bạn có muốn xoá hạng mục này', [
      {
        text: 'Để sau',
      },
      {
        text: 'Đồng ý',
        onPress: () => {
          let dataAITmp = dataAI;
          dataAITmp.splice(viTriXoa, 1);
          setDataAI([...dataAITmp]);
        },
      },
    ]);
  };
  /* RENDER */
  const renderItemHangMucAI = ({item, index}) => {
    return (
      <View style={[styles.itemHangMucView, index === dataAI.length - 1 && {borderBottomWidth: 0}]}>
        <View style={{flex: 1}}>
          <Text children={item.ten_hang_muc} style={styles.txtTenHangMuc} />
          <View style={styles.itemHangMucRow}>
            <Text children={'Mức độ '} style={styles.rowTitle} />
            <Text children={getMucDoTitle(item.muc_do)} style={styles.rowValue} />
          </View>
          <View style={styles.itemHangMucRow}>
            <Text children={'Thay thế '} style={styles.rowTitle} />
            <Text children={getThayTheTitle(item.thay_the_sc)} style={styles.rowValue} />
          </View>
          <View style={styles.itemHangMucRow}>
            <Text children={'Thu hồi '} style={styles.rowTitle} />
            <Text children={getThuHoiTitle(item.thu_hoi)} style={styles.rowValue} />
          </View>
          <View style={styles.itemHangMucRow}>
            <Text children={'Giá '} style={styles.rowTitle} />
            <Text children={item.tien_tu_dong} style={styles.rowValue} />
          </View>
        </View>
        <TouchableOpacity style={styles.btnRemoveHangMuc} onPress={() => xoaHangMuc(index)}>
          <Icon.Ionicons name="close" size={30} color={colors.RED1} />
        </TouchableOpacity>
      </View>
    );
  };
  return (
    <Modal isVisible={isVisible} style={styles.modal} onBackdropPress={hideModal}>
      <SafeAreaView style={styles.modal}>
        <HeaderModal
          title="Hạng mục nhận dạng AI"
          headerStyle={{backgroundColor: '#FFF', borderTopLeftRadius: 20, borderTopRightRadius: 20, paddingVertical: spacing.small}}
          leftComponent={
            <TouchableOpacity style={styles.backBtn} onPress={hideModal}>
              <Icon.AntDesign name="close" size={FontSize.size25} color={colors.BLACK_03} />
            </TouchableOpacity>
          }
        />
        <ScrollView style={styles.content} scrollEnabled={true}>
          {dataAI.length === 0 && <Empty />}
          {dataAI.map((item, index) => renderItemHangMucAI({item, index}))}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
});

export const ModalAIData = ModalAIDataComponent;

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    // flex: 1,
  },
  content: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.small,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  closeView: {
    marginRight: 15,
  },
  txtTenHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  itemHangMucView: {
    paddingHorizontal: spacing.small,
    borderBottomWidth: 1,
    borderColor: '#CCC',
    paddingVertical: spacing.small,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemHangMucRow: {
    flexDirection: 'row',
  },
  rowTitle: {flex: 1},
  rowValue: {flex: 4},
  btnRemoveHangMuc: {
    padding: spacing.small,
  },
});
