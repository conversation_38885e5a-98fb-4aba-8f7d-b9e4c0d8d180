import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, FlatList, Image, Keyboard, SafeAreaView, ScrollView, StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC} from '../constant';
import ImageZoom from 'react-native-image-pan-zoom';
import FastImage from 'react-native-fast-image';
import {TOA_DO_ANH_BEN_LAI_XE, TOA_DO_ANH_BEN_PHU_XE, TOA_DO_ANH_SAU_XE, TOA_DO_ANH_TRUOC_XE, anhXungQuanhXe} from './Constant';

const ModalHangMucComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  let scrollViewModalRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState(dimensions.height);
  const [searchInput, setSearchInput] = useState('');
  //DROPDOWN MỨC ĐỔ TỔN THẤT
  // const [levelLossSelected, setLevelLossSelected] = useState(null);
  const [levelLossData, setLevelLossData] = useState([]);
  const [hangMucSelected, setHangMucSelected] = useState(null);

  const collapsedHeight = 50;
  const [tabActive, setTabActive] = useState(0);
  const [kichThuocAnhThat, setKichThuocAnhThat] = useState({
    width: 0,
    height: 0,
  }); //kích thước ảnh thật
  const [isXoayAnh, setIsXoayAnh] = useState(false);
  const [kichThuocVungChuaANh, setKichThuocVungChuaAnh] = useState(null); //kích thước vùng chứa ảnh
  const [viTriAnhPress, setViTriAnhPress] = useState(null);
  const [anhDuocChon, setAnhDuocChon] = useState(0);
  const [kichThuocAnhHienThi, setKichThuocAnhHienThi] = useState({
    width: dimensions.width,
    height: dimensions.width,
  });

  const [isReloadTatCaHangMuc, setIsReloadTatCaHangMuc] = useState(false);

  useEffect(() => {
    initMucDoTonThat();
  }, []);
  const onModalHide = () => setHangMucSelected(null);

  const initMucDoTonThat = () => {
    // return;
    const {doiTuongDuocChupAnh, categoryCommon, profileData} = props;
    const maDoiTuong = doiTuongDuocChupAnh.nhom;
    let mucDoTonThatFilter = [];
    // FILTER THEO NGHIỆP VỤ
    let mucDoTonThatFilterNghiepVu = categoryCommon.levelLost.filter((item) => item.nv_hs === profileData.ho_so.nghiep_vu);
    //NẾU ĐỐI TƯỢNG LÀ XE
    if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.XE.ma) mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    // NẾU ĐỐI TƯỢNG LÀ HÀNG HOÁ
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.HANG_HOA.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.HANG_HOA) mucDoTonThatFilter.push(mucDo);
      }
    }
    // NẾU ĐỐI TƯỢNG LÀ NGƯỜI
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.NGUOI.ma) {
      for (let i = 0; i < mucDoTonThatFilterNghiepVu.length; i++) {
        let mucDo = JSON.parse(JSON.stringify(mucDoTonThatFilterNghiepVu[i]));
        if (mucDo.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.NGUOI) mucDoTonThatFilter.push(mucDo);
      }
    }
    // NẾU ĐÓI TƯỢNG LÀ TÀI SẢN
    else if (maDoiTuong === DATA_CONSTANT.NHOM_DOI_TUONG_TON_THAT.TAI_SAN.ma) {
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN KHÁC
      if (doiTuongDuocChupAnh?.loai === 'KHAC') mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === doiTuongDuocChupAnh.nhom);
      // NẾU ĐỐI TƯỢNG LÀ TÀI SẢN XE
      else if (doiTuongDuocChupAnh?.loai === 'XE' || doiTuongDuocChupAnh?.loai === 'XE_MAY')
        mucDoTonThatFilter = mucDoTonThatFilterNghiepVu.filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
    }
    setLevelLossData([...mucDoTonThatFilter]);
  };
  const onPressTaiLaiHangMuc = async () => {
    setIsReloadTatCaHangMuc(true);
    await props.onPressLayDataHangMuc();
    setIsReloadTatCaHangMuc(false);
    Alert.alert('Thành công', 'Tải dữ liệu hạng mục mới thành công');
  };

  const onPressXoayAnh = () => {
    if (isXoayAnh) {
      setKichThuocAnhHienThi({
        width: dimensions.width,
        height: dimensions.width,
      });
    } else {
      setKichThuocAnhHienThi({
        width: kichThuocVungChuaANh.width,
        height: kichThuocVungChuaANh.height - collapsedHeight - 50, //-50 vì k biết tại sao nó ra đủ màn hình :v
      });
    }
    setIsXoayAnh(!isXoayAnh);
    setViTriAnhPress(null);
  };

  const onPressAnh = (event) => {
    /*
    LƯU Ý : BÀI TOÁN TRÊN CHỈ ÁP DỤNG CHO ĐA GIÁC LỒI
    Dựa vào bài toán : vị trí tương đối của 2 điểm với 1 đường thẳng để giải quyết việc điểm được chọn có nằm trong hình hay không
    Bài toán có nêu là
    Cho đường thẳng d: ax + by + c = 0 và hai điểm M( xM; yM); N(xN; yN) không nằm trên d. Khi đó:
      + Hai điểm M ; N nằm cùng phía đối với đường thẳng d khi và chỉ khi:
        (axM + byM + c) * (axN + byN + c) > 0
      + Hai điểm M ; N nằm khác phía đối với đường thẳng d khi và chỉ khi:
        (axM + byM + c) * (axN + byN + c) < 0

    các bước thực hiện bài toán chọn điểm trên ảnh, xác dịnh xem điểm P (P là Press) 
    có thuộc hình ABCDE(ví dụ gồm 4 góc A, B, C, D, E) bất kì có sẵn không (Hình ABCDE là đa giác không phải hình sao)
    1./ Xác định toạ độ các đỉnh của hình trên ảnh 
    2./ Viết phương trình đường thẳng có dạng  y = ax + b (a: hệ số góc, b: số tự do)
     và lưu a, b 1 mảng đi qua các điểm A, B, C, D, E gồm 5 đường thẳng AB, BC, CD, DE, EA là các đỉnh của hình
    3./ Xác định 1 điểm cố định có sẵn nằm bên trong hình gọi là điểm I (xI, yI) I là Inside
    và 1 điểm tự do do người dùng bấm gọi là điểm P (xP, yP) P là press

    A----------------------B
    |                       \             P(xP, yP)
    |                        \
    |                         \ 
    |            I(xI, yI)     C
    |                          /
    |                         / 
    |                        / 
    E-----------------------D

    Sau khi có phương trình của tất cả các cạnh AB, BC, CD, DE, EA và toạ độ của điểm mà user Press 
    -> sử dụng công thức trên để xác định
    nếu I, P thay vào các cạnh với công thức trên đều > 0 (tức là nằm cùng phía) -> P sẽ nằm trong hình A, B, C, D, E
    nếu I, P thay vào các cạnh với công thức trên đều < 0 (tức là có 1 cạnh nằm khác phía) -> P sẽ nằm ngoài hình A, B, C, D, E

    */

    let toaDoAnhPress = {
      x: +event.locationX.toFixed(3),
      y: +event.locationY.toFixed(3),
    };
    if (toaDoAnhPress.x === 0 && toaDoAnhPress.y === 0) return;
    let toaDoAnhPressThuc = !isXoayAnh
      ? {
          x: (toaDoAnhPress.x * kichThuocAnhThat.width) / kichThuocAnhHienThi.width,
          y: (toaDoAnhPress.y * kichThuocAnhThat.width) / kichThuocAnhHienThi.width,
        }
      : {
          x: (toaDoAnhPress.x * kichThuocAnhThat.width) / kichThuocAnhHienThi.width,
          y: (toaDoAnhPress.y * kichThuocAnhThat.height) / kichThuocAnhHienThi.height,
        };
    // console.log('toaDoAnhPressThuc', toaDoAnhPressThuc);
    //nếu là ảnh trước xe
    let DATA_TOA_DO_XE = {};

    if (anhDuocChon === 1) DATA_TOA_DO_XE = TOA_DO_ANH_TRUOC_XE;
    else if (anhDuocChon === 2) DATA_TOA_DO_XE = TOA_DO_ANH_BEN_PHU_XE;
    else if (anhDuocChon === 3) DATA_TOA_DO_XE = TOA_DO_ANH_SAU_XE;
    else if (anhDuocChon === 4) DATA_TOA_DO_XE = TOA_DO_ANH_BEN_LAI_XE;

    //nếu ảnh DỌC
    for (let tenBoPhan in DATA_TOA_DO_XE) {
      let boPhanXe = DATA_TOA_DO_XE[tenBoPhan]; //data bộ phận xe
      //nếu là đang xoay ảnh -> bỏ qua những hạng mục DỌC
      if (isXoayAnh && boPhanXe.type === 'DOC') continue;
      else if (!isXoayAnh && boPhanXe.type === 'NGANG') continue; //nếu là đang xoay ảnh -> bỏ qua những hạng mục DỌC

      let toaDoDiemTrongHinh = DATA_TOA_DO_XE[tenBoPhan].TOA_DO_NAM_TRONG; // toạ độ điểm I (Inside) nằm trong hình
      let namTrong = true; //check xem có nằm trong hình nào hay không
      if (boPhanXe.hinhDang === 'DA_GIAC') {
        for (let i = 0; i < boPhanXe.TOA_DO.length; i++) {
          namTrong = true;
          let toaDo1 = boPhanXe.TOA_DO[i];
          let toaDo2 = {
            x: 0,
            y: 0,
          };
          if (i === boPhanXe.TOA_DO.length - 1) toaDo2 = boPhanXe.TOA_DO[0];
          //trường hợp điểm cuối cùng -> thì phải lấy điểm bắt đầu A
          else toaDo2 = boPhanXe.TOA_DO[i + 1]; //trường hợp bình thường -> lấy toạ độ của điểm kế tiếp
          //tính toán ra tham số hệ số góc, số tự do trong phương trình y = ax + b (a : hệ số góc, b là số tự do)
          let heSoGoc = (toaDo1.y - toaDo2.y) / (toaDo1.x - toaDo2.x);
          let soTuDo = toaDo1.y - heSoGoc * toaDo1.x;
          let viTriTuongDoi = (heSoGoc * toaDoDiemTrongHinh.x - toaDoDiemTrongHinh.y + soTuDo) * (heSoGoc * toaDoAnhPressThuc.x - toaDoAnhPressThuc.y + soTuDo);
          if (viTriTuongDoi < 0) {
            namTrong = false;
            hangMucSelected && setHangMucSelected(null); //nếu nằm ngoài -> setHangMuc được chọn bị null
            break; //break ra để duyệt bộ phận tiếp theo
          }
        }
      } else if (boPhanXe.hinhDang === 'HINH_TRON') {
        let banKinh = boPhanXe.TOA_DO[1].x - boPhanXe.TOA_DO[0].x;
        let doDaiPress = {x: Math.abs(toaDoAnhPressThuc.x - boPhanXe.TOA_DO[0].x), y: Math.abs(toaDoAnhPressThuc.y - boPhanXe.TOA_DO[0].y)};
        if (doDaiPress.x > banKinh || doDaiPress.y > banKinh) {
          namTrong = false;
          hangMucSelected && setHangMucSelected(null); //nếu nằm ngoài -> setHangMuc được chọn bị null
        }
      }

      //nếu nằm trong bộ phận nào -> setHangMuc được chọn
      if (namTrong) {
        handleHangMucDuocChon(boPhanXe.ma);
        return;
      }
      //nếu không nằm trong hạng mục nào -> setHangMuc được chọn bị null
      else if (!namTrong) setHangMucSelected(null);
    }
  };
  const handleHangMucDuocChon = (ma) => {
    const {categoryCommon} = props;
    categoryCommon.type1.forEach((hangMuc) => {
      if (hangMuc.ma === ma) return setHangMucSelected(hangMuc);
    });
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <TouchableOpacity style={styles.backBtn} onPress={() => setIsVisible(false)}>
          <Icon.AntDesign name="arrowleft" size={20} style={styles.iconBack} />
        </TouchableOpacity>
        <TextInput
          style={styles.searchInput}
          value={searchInput}
          placeholder="Tìm kiếm..."
          placeholderTextColor={colors.BLACK}
          onChangeText={(value) => {
            setSearchInput(value);
            setHangMucSelected(null);
            props.onChangeSearchTextHangMuc(value);
          }}
          onFocus={() => Keyboard.o}
        />

        <TouchableOpacity
          style={styles.backBtn}
          onPress={() => {
            props.clearSearch();
            setSearchInput('');
          }}>
          <Icon.MaterialCommunityIcons name="filter-remove-outline" size={20} style={styles.iconBack} />
        </TouchableOpacity>
        <View style={{width: 50, justifyContent: 'center', alignItems: 'center'}}>
          {isReloadTatCaHangMuc ? (
            <ActivityIndicator size="small" color={colors.PRIMARY} />
          ) : (
            <TouchableOpacity onPress={() => onPressTaiLaiHangMuc()}>
              <Icon.AntDesign name="download" size={20} style={styles.iconBack} />
            </TouchableOpacity>
          )}
        </View>

        {/* </View> */}
      </View>
    );
  };
  const renderItemHangMuc = (item, index) => {
    let anhHangMucDaUpload = props.getAnhDaUploadTheoMaHangMuc(item.ma);
    return (
      <TouchableOpacity
        key={index}
        style={styles.itemHangMucView}
        onPress={() => {
          if (anhHangMucDaUpload.length >= MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC) {
            setHangMucSelected(null);
            return Alert.alert('Thông báo', 'Hạng mục này đã chụp tối đa ' + MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC + ' ảnh. Vui lòng xoá bớt ảnh để chụp thêm');
          }
          setHangMucSelected(item);
          // setIsVisible(false);
          // props.onHangMucSelected(item);
        }}>
        {hangMucSelected?.ma === item.ma ? <Icon.AntDesign name="check" color={colors.PRIMARY} size={20} /> : <Icon.AntDesign size={20} name="check" color="transparent" />}
        <Text style={[{color: hangMucSelected?.ma == item.ma ? colors.PRIMARY : '#000'}, {marginLeft: 5}]}>
          {item.ten}
          {anhHangMucDaUpload.length > 0 && <Text color={colors.PRIMARY}>{' (' + anhHangMucDaUpload.length + '/' + MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC + ')'}</Text>}
        </Text>
        {/* <Text children={item.ten_alias} style={[{color: hangMucSelected?.ma == item.ma ? colors.PRIMARY : '#000'}, {marginLeft: 5}]} /> */}
      </TouchableOpacity>
    );
  };

  const renderItemMucDoTonThat = (item, index) => (
    <TouchableOpacity
      key={index}
      style={[styles.itemHangMucView, {paddingVertical: 20}]}
      onPress={() => {
        // setHangMucSelected(item);
        setIsVisible(false);
        props.setMucDoSelected(item);
        props.onHangMucSelected(hangMucSelected);
      }}>
      <Text children={item.ten} />
    </TouchableOpacity>
  );

  const renderMucDoTonThat = () => (
    <View style={styles.mucDoTonThatView}>
      <View style={styles.mucDoTonThatHeader}>
        <View style={{flex: 1, marginRight: spacing.tiny}}>
          <Text children="Chọn mức độ tổn thất" style={styles.txtChonMucDoTonThat} />
          {hangMucSelected && <Text children={hangMucSelected.ten} style={[styles.txtTenHangMucSelected]} color={colors.PRIMARY} font="bold14" />}
        </View>
        <TouchableOpacity onPress={() => setHangMucSelected(null)}>
          <Icon.AntDesign name="closecircleo" size={25} />
        </TouchableOpacity>
      </View>
      <ScrollView showsVerticalScrollIndicator={false}>{levelLossData.map((item, index) => renderItemMucDoTonThat(item, index))}</ScrollView>
      <View style={{borderTopWidth: 1, borderColor: '#000'}}>
        <TouchableOpacity
          onPress={() => {
            setIsVisible(false);
            props.setMucDoSelected(null);
            props.onHangMucSelected(hangMucSelected);
          }}>
          <Text children="Tiếp tục" style={styles.txtTiepTuc} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAnhXungQuanhXe = ({item, index}) => {
    if (tabActive === 1 && index === 0) return;
    return (
      <TouchableOpacity
        onPress={() => {
          setAnhDuocChon(index);
          setViTriAnhPress(null);
        }}
        activeOpacity={0.8}
        style={[styles.anhXungQuanhXeItemView, {borderBottomColor: index === anhDuocChon ? colors.PRIMARY : '#FFF', width: tabActive === 0 ? dimensions.width / 5 : dimensions.width / 4}]}>
        <Text children={item.name} style={index === anhDuocChon && {color: colors.PRIMARY, fontWeight: 'bold'}} />
      </TouchableOpacity>
    );
  };

  const renderTabDanhSachHangMuc = () => {
    const {data} = props;
    let keyHangMucFilter = anhXungQuanhXe[anhDuocChon].key;
    let hangMucFiter = [];
    if (searchInput != '' || anhDuocChon === 0) hangMucFiter = data;
    //trường hợp có searchInput thì phải lấy data truyền vào vì data này là hạng mục đã filter theo search
    else hangMucFiter = data.filter((item) => item.vi_tri === keyHangMucFilter); //chưa có search thì filter theo hạng mục mặc định truyền vào
    if (hangMucFiter.length === 0) hangMucFiter = data; //nếu k có hạng mục thì lấy mặc định
    return (
      <ScrollView
        style={{marginRight: spacing.small, marginLeft: spacing.small}}
        ref={scrollViewModalRef}
        onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}>
        <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>{hangMucFiter.map((item, index) => renderItemHangMuc(item, index))}</View>
      </ScrollView>
    );
  };

  const renderChonVungXeChupAnh = () => (
    <View style={styles.vungXeChupAnhView}>
      <View style={styles.flatListView}>
        <FlatList data={anhXungQuanhXe} renderItem={renderAnhXungQuanhXe} horizontal scrollEnabled={false} />
      </View>
    </View>
  );

  const renderTabAnhHangMuc = () => {
    return (
      <View style={styles.tabAnhHangMucView}>
        {renderChonVungXeChupAnh()}
        <View
          style={{
            flex: 1,
          }}>
          <View
            style={[{flex: 1}]}
            onLayout={(event) => {
              setKichThuocVungChuaAnh({
                width: event.nativeEvent.layout.width,
                height: event.nativeEvent.layout.height,
              });
            }}
            activeOpacity={0.9}>
            {!isXoayAnh && (
              <View>
                <View style={[styles.imageSelectedView]}>
                  <ImageZoom
                    cropWidth={kichThuocAnhHienThi.width}
                    cropHeight={kichThuocAnhHienThi.height}
                    imageWidth={kichThuocAnhHienThi.width}
                    imageHeight={kichThuocAnhHienThi.height}
                    onClick={(event) => onPressAnh(event)}>
                    <FastImage
                      source={anhXungQuanhXe[anhDuocChon].image}
                      resizeMode="stretch"
                      style={[styles.imgSelected, {width: kichThuocAnhHienThi.width, height: kichThuocAnhHienThi.height}]}
                      onLoad={(onLoadEvent) => {
                        let image = Image.resolveAssetSource(anhXungQuanhXe[anhDuocChon].image);
                        setKichThuocAnhThat({
                          width: image.width,
                          height: image.height,
                        });
                      }}
                    />
                  </ImageZoom>

                  {/* {viTriAnhPress && (
                    <View
                      style={{
                        position: 'absolute',
                        top: viTriAnhPress.y - 10, //-10 là kích thước của icon nên phải trừ đi 10 cho chính xác
                        left: viTriAnhPress.x - 10,
                      }}>
                      <Icon.MaterialCommunityIcons name="target" size={25} color={colors.RED3} />
                    </View>
                  )} */}
                </View>
              </View>
            )}

            {isXoayAnh && (
              <View>
                <View style={[styles.imageSelectedView]}>
                  <ImageZoom
                    cropWidth={kichThuocAnhHienThi.width}
                    cropHeight={kichThuocAnhHienThi.height}
                    imageWidth={kichThuocAnhHienThi.width}
                    imageHeight={kichThuocAnhHienThi.height}
                    onClick={(event) => onPressAnh(event)}>
                    <FastImage
                      source={anhXungQuanhXe[anhDuocChon].imageNgang}
                      resizeMode="stretch"
                      style={[
                        styles.imgSelected,
                        {
                          width: kichThuocAnhHienThi.width,
                          height: kichThuocAnhHienThi.height,
                        },
                      ]}
                      onLoad={(onLoadEvent) => {
                        let image = Image.resolveAssetSource(anhXungQuanhXe[anhDuocChon].imageNgang);
                        setKichThuocAnhThat({
                          width: image.width,
                          height: image.height,
                        });
                      }}
                    />
                  </ImageZoom>
                  {/* {viTriAnhPress && (
                    <View
                      style={{
                        position: 'absolute',
                        top: viTriAnhPress.y - 10, //-10 là kích thước của icon nên phải trừ đi 10 cho chính xác
                        left: viTriAnhPress.x - 10,
                      }}>
                      <Icon.MaterialCommunityIcons name="target" size={25} color={colors.RED3} />
                    </View>
                  )} */}
                </View>
              </View>
            )}
          </View>
        </View>

        <TouchableOpacity style={styles.iconXoayAnhView} onPress={onPressXoayAnh}>
          <Icon.Feather name={isXoayAnh ? 'rotate-ccw' : 'rotate-cw'} size={25} color={colors.PRIMARY} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderContent = () => {
    const {doiTuongDuocChupAnh} = props;
    const isDoiTuongXe = doiTuongDuocChupAnh.nhom === 'XE' || (doiTuongDuocChupAnh.nhom === 'TAI_SAN' && doiTuongDuocChupAnh.loai === 'XE');
    return (
      <View style={{flex: 1}}>
        {isDoiTuongXe && (
          <View style={styles.tabViewList}>
            <View style={styles.bgTabbar}>
              <View style={styles.tabView}>
                <TouchableOpacity
                  style={[styles.tabBar, tabActive === 0 && styles.borderBottom]}
                  onPress={() => {
                    setAnhDuocChon(0);
                    setTabActive(0);
                  }}>
                  <Text style={[styles.tabBarTxt, tabActive === 0 && styles.tabBarTxtActive]}>Danh sách</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tabBar, tabActive === 1 && styles.borderBottom]}
                  onPress={() => {
                    setAnhDuocChon(1);
                    setTabActive(1);
                  }}>
                  <Text style={[styles.tabBarTxt, tabActive === 1 && styles.tabBarTxtActive]}>Ảnh</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
        {/* <ScrollView
          style={{marginRight: 20, marginLeft: 10, marginTop: 20}}
          ref={scrollViewModalRef}
          onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}>
          <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>{data.map((item, index) => renderItemHangMuc(item, index))}</View>
        </ScrollView> */}

        {tabActive === 0 && isDoiTuongXe && renderChonVungXeChupAnh()}
        {tabActive === 0 && renderTabDanhSachHangMuc()}

        {tabActive === 1 && renderTabAnhHangMuc()}

        {hangMucSelected && renderMucDoTonThat()}
      </View>
    );
  };
  return (
    <Modal isVisible={isVisible} style={styles.modal} onModalHide={onModalHide} avoidKeyboard={true}>
      <SafeAreaView style={{flex: 1}}>
        <View style={styles.modalCameraView}>
          <View style={styles.modalCameraContent}>
            {renderHeader()}
            {renderContent()}
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
    flex: 1,
    backgroundColor: colors.WHITE,
    paddingVertical: spacing.small,
  },
  modalCameraView: {
    justifyContent: 'center',
    height: dimensions.height,
    width: dimensions.width,
    flex: 1,
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    paddingLeft: spacing.small,
    flex: 1,
    borderRadius: 5,
    height: 50,
  },
  headerView: {
    flexDirection: 'row',
  },
  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 50,
  },
  iconBack: {
    paddingHorizontal: spacing.small,
  },
  itemHangMucView: {
    paddingVertical: spacing.smaller,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mucDoTonThatView: {
    position: 'absolute',
    right: 0,
    top: 120,
    bottom: 0,
    backgroundColor: '#FFF',
    paddingHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,

    width: dimensions.width * 0.5,
  },
  tabViewList: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: colors.WHITE,
  },
  bgTabbar: {},
  tabView: {
    flexDirection: 'row',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: spacing.small,
    backgroundColor: colors.WHITE,
  },
  tabBar: {
    flex: 1,
    borderBottomWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: colors.BORDER_GRAY,
    paddingVertical: spacing.medium,
  },
  borderBottom: {
    borderBottomWidth: 3,
    borderColor: colors.PRIMARY,
  },
  tabBarTxtActive: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  mucDoTonThatHeader: {
    borderBottomWidth: 1,
    borderColor: '#000',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.smaller,
  },
  txtChonMucDoTonThat: {
    textAlign: 'center',
  },
  tabAnhHangMucView: {
    flex: 1,
  },
  matXeView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    marginHorizontal: spacing.small,
    borderBottomColor: '#000',
    paddingBottom: spacing.small,
  },
  txtTiepTuc: {
    paddingVertical: spacing.small,
    textAlign: 'center',
  },
  imgAnhChonHangMuc: {},
  imgAnhXungQuanhXeItem: {
    width: dimensions.width / 5,
    height: dimensions.width / 5,
    borderRadius: 20,
  },
  anhXungQuanhXeItemView: {
    borderBottomWidth: 2,
    // width: dimensions.width / 5,
    alignItems: 'center',
    backgroundColor: '#FFF',
    paddingBottom: spacing.small,
  },
  anhXungQuanhXeItemViewActive: {},
  imgSelected: {
    width: dimensions.width,
    height: dimensions.width,
  },
  imageSelectedView: {},
  txtHangMucKhac: {
    marginTop: spacing.small,
    textAlign: 'center',
    marginBottom: spacing.small,
    fontSize: 16,
  },
  boPhanKhacView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedView: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  flatListView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtChonKhuVucXe: {
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  chonKhuVucXeView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.small,
    backgroundColor: colors.PRIMARY,
    paddingTop: spacing.small,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  iconUpDownView: {
    alignSelf: 'center',
  },
  iconXoayAnhView: {
    position: 'absolute',
    right: 10,
    top: 50,
    alignItems: 'center',
  },
  txtHuongDanView: {
    backgroundColor: colors.PRIMARY,
    paddingVertical: spacing.tiny,
    paddingHorizontal: spacing.smaller,
    borderRadius: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtHuongDanPosition: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtHuongDan: {
    color: '#FFF',
    marginLeft: spacing.tiny,
  },
  vungXeChupAnhView: {
    // position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  txtTenHangMucSelected: {
    textAlign: 'center',
  },
});
export const ModalHangMuc = memo(ModalHangMucComponent, isEqual);
