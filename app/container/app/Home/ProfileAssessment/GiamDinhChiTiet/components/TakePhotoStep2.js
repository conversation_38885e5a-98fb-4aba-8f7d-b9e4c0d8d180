import {DATA_CONSTANT, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors, dimension} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {dimensions, spacing} from '@app/theme';
import {getCauHinhHoSoByMa} from '@app/utils/DataProvider';
import {Empty, Icon, ImageComp, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';

const TakePhotoStep2Component = ({
  profileData,
  removeImage,
  rotationImage,
  onPressOpenCamera,
  onPressXemLaiAnh,
  imagesData,
  doiTuongDuocChupAnh,
  // anhHoSo,
  dataAI,
  onPressShowDuLieuAI,
  onPressXemChiTietAnhDaUpload,
}) => {
  const [chonAnhTuThuVien, setChonAnhTuThuVien] = useState(false);
  useEffect(() => {
    if (profileData && profileData.cau_hinh) {
      let cauHinhChonAnhTuThuVien = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.CHON_ANH_THU_VIEN, profileData.cau_hinh);
      if (cauHinhChonAnhTuThuVien?.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO) setChonAnhTuThuVien(true);
    }
  }, []);
  // const checkDaChupAnh = (maHangMuc) => {
  //   let anhHangMuc = anhHoSo.find((item) => item.ma_file === maHangMuc && item.so_id_doi_tuong === doiTuongDuocChupAnh.so_id_doi_tuong);
  //   if (anhHangMuc) return true;
  //   return false;
  // };
  const renderImageItem = (imageData) => {
    //nếu là GIẤY CHỨNG NHẬN BẢO HIỂM đã chụp dc 2 cái ảnh, thì ẩn cái nút mở camera đi (nút có index = 2)
    // if (imageData.item.nhom.nhom_hang_muc === DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.GIAY_CHUNG_NHAN_BAO_HIEM && imageData.index === 2) return;
    return (
      <ImageComp
        imageData={imageData}
        removeImage={removeImage}
        rotationImage={rotationImage}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        onPressOpenCamera={onPressOpenCamera}
        onPressXemLaiAnh={onPressXemLaiAnh}
        uploadFromLib={chonAnhTuThuVien}
      />
    );
  };

  const renderItemAnhDaUpload = ({item, index}, extraData) => (
    <TouchableOpacity
      onPress={() => {
        NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
          currentImageData: {item, index},
          imagesData: extraData.listAnhHangMuc,
        });
      }}>
      <FastImage style={styles.imageProcess} source={{uri: `data:image/gif;base64,${item.duong_dan}`}} resizeMode={'contain'} />
    </TouchableOpacity>
  );

  //render ra 1 item của menu
  const renderItemMenuImage = ({item, index}) => {
    let hienThiBoSungThongTin = false;
    if (item.nhom_hang_muc === 'BANG_LAI' || item.nhom_hang_muc === 'DANG_KIEM') hienThiBoSungThongTin = true;
    let daCoTrenHeThong = false;
    let txtChuaCoTrenHeThong = 'Chưa có trên hệ thống';
    //nếu là mục SỐ KHUNG / SỐ MÁY
    if (item.nhom_hang_muc && typeof item.nhom_hang_muc === 'object') {
      let anhSoKhung = item.anhDaUpload.find((itemImage) => itemImage.nhom_hang_muc === 'SO_KHUNG');
      let anhSoMay = item.anhDaUpload.find((itemImage) => itemImage.nhom_hang_muc === 'SO_MAY');
      if (anhSoKhung && anhSoMay) daCoTrenHeThong = true;
      else if (!anhSoKhung || !anhSoMay) {
        txtChuaCoTrenHeThong = '';
        if (!anhSoKhung) txtChuaCoTrenHeThong = 'Số khung';
        if (!anhSoMay) txtChuaCoTrenHeThong += (txtChuaCoTrenHeThong === 'Số khung' ? ', ' : ' ') + 'Số máy';
        txtChuaCoTrenHeThong += ' chưa có trên hệ thống';
      }
    } else daCoTrenHeThong = item.anhDaUpload.length > 0;
    // const isAnhToanCanh = item.nhom.nhom === 'ANH_TOAN_CANH';
    return (
      <View style={[styles.itemMenuView, index % 2 === 0 && {backgroundColor: colors.WHITE8}]}>
        <View style={styles.imageCategoryTitleView}>
          <View style={styles.menuImageTitleView}>
            <View>
              <Text style={styles.imageCategoryTitle} children={item.ten} font="regular14" />
              <Text
                font="regular12"
                style={[styles.txtTrangThaiAnh, {color: daCoTrenHeThong ? colors.GREEN : colors.PRIMARY}]}
                children={daCoTrenHeThong ? 'Đã có trên hệ thống' : txtChuaCoTrenHeThong}
              />
            </View>
          </View>
          <View style={styles.menuImageActionView}>
            {hienThiBoSungThongTin && (
              <TouchableOpacity
                style={styles.thongTinChiTietView}
                onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.BO_SUNG_THONG_TIN_GIAY_TO, {thongTinGiayTo: item, profileData, doiTuongDuocChupAnh})}>
                <Text children="Thông tin chi tiết" style={[styles.imageCategoryTitle, {marginRight: spacing.tiny, color: colors.PRIMARY}]} />
                <Icon.FontAwesome name={'angle-up'} size={18} color={colors.PRIMARY} />
              </TouchableOpacity>
            )}
            <TouchableOpacity style={styles.thongTinChiTietView} onPress={() => onPressXemChiTietAnhDaUpload(index)}>
              <Text children={`Ảnh đã tải lên (${item.anhDaUpload.length})`} color={colors.PRIMARY} style={[styles.txtMenuImageActionView, {marginRight: spacing.tiny}]} />
              <Icon.FontAwesome name={item.isShowAnhDaUpload ? 'angle-down' : 'angle-up'} size={18} color={colors.PRIMARY} />
            </TouchableOpacity>
          </View>
          {/* {isAnhToanCanh && (
            <TouchableOpacity style={[styles.thongTinChiTietView]} onPress={onPressShowDuLieuAI} disabled={dataAI.length === 0}>
              <Text children={`${dataAI.length > 0 ? dataAI.length : ''} hạng mục [AI]`} style={[styles.imageCategoryTitle, {color: dataAI.length === 0 ? colors.GRAY6 : colors.PRIMARY}]} />
              <Icon.Entypo name="chevron-small-right" size={20} color={dataAI.length === 0 ? colors.GRAY6 : colors.PRIMARY} />
            </TouchableOpacity>
          )} */}
        </View>

        {item.isShowAnhDaUpload && (
          <View style={styles.anhDaUploadView}>
            <Text children="Ảnh đã tải lên hệ thống" style={[styles.imageCategoryTitle, {fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
            {item.anhDaUpload.length > 0 && (
              <FlatList
                keyExtractor={(item, index) => index.toString()}
                scrollEnabled={true}
                data={item.anhDaUpload}
                renderItem={(itemAnhDaUpload) => renderItemAnhDaUpload(itemAnhDaUpload, {listAnhHangMuc: item.anhDaUpload})}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
              />
            )}
            {item.anhDaUpload.length === 0 && (
              <View style={{marginBottom: spacing.default}}>
                <Empty imageStyle={{width: dimensions.width / 5, height: dimensions.width / 5}} />
              </View>
            )}
          </View>
        )}
        <View>
          {item.isShowAnhDaUpload && (
            <Text children="Ảnh chưa tải lên hệ thống" style={[styles.imageCategoryTitle, {marginLeft: spacing.default, fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
          )}

          <FlatList
            keyExtractor={(item, index) => index.toString()}
            scrollEnabled={true}
            data={item.images}
            renderItem={(itemAnh) => renderImageItem(itemAnh, {images: item.images})}
            numColumns={2}
            horizontal={false}
          />
        </View>
      </View>
    );
    // else return null;
  };

  return (
    <View style={{flex: 1, marginBottom: spacing.huge}}>
      <FlatList data={imagesData} renderItem={renderItemMenuImage} initialNumToRender={imagesData.length} keyExtractor={(item, index) => index.toString()} />
    </View>
  );
};
const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    width: dimension.width,
  },
  btnLoginView: {
    flexDirection: 'row',
    backgroundColor: colors.PRIMARY,
    marginHorizontal: 10,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerView: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    // borderRadius : 30,
    // marginBottom: 30,
    // marginTop: 20,
    backgroundColor: colors.WHITE,
    // justifyContent: 'center',
  },
  txtBtnLogin: {
    paddingRight: 5,
    paddingVertical: 15,
    fontSize: 20,
    textTransform: 'uppercase',
    color: colors.WHITE,
  },
  headerTitle: {
    marginVertical: 10,
    fontSize: 16,
  },
  contentDetail: {
    paddingHorizontal: 20,
    marginBottom: 7,
    backgroundColor: colors.WHITE,
    borderRadius: 10,
    marginHorizontal: 10,
  },
  modalSelectorView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    marginRight: 15,
  },

  stepIndicator: {
    marginVertical: 20,
  },
  page: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepLabel: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: '500',
    color: colors.GREEN,
  },
  imageCategoryTitleView: {
    flexDirection: 'row',
    // borderBottomWidth: 1,
    borderColor: colors.GRAY,
    marginHorizontal: spacing.default,
    // paddingBottom: 5,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  imageCategoryTitle: {
    // color: colors.BLACK,
    // fontWeight: 'bold',
    // marginLeft: 10,
    // textAlign: 'center',
  },
  txtTrangThaiAnh: {
    // marginLeft: spacing.default,
  },
  itemMenuView: {
    paddingVertical: spacing.default,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY11,
  },
  thongTinChiTietView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuImageTitleView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuImageActionView: {
    alignItems: 'flex-end',
  },
  txtMenuImageActionView: {
    // textDecorationLine: 'underline',
  },
  imageProcess: {
    marginRight: spacing.default,
    marginVertical: spacing.default,
    borderRadius: 8,
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    resizeMode: 'cover',
    backgroundColor: colors.GRAY2,
    // borderWidth: 1,
  },
  anhDaUploadView: {
    borderTopWidth: 0.3,
    borderBottomWidth: 0.3,
    marginHorizontal: spacing.default,
    marginTop: spacing.smaller,
    paddingTop: spacing.smaller,
    marginBottom: spacing.default,
    borderColor: colors.GRAY3,
  },
});

const TakePhotoStep2Memo = memo(TakePhotoStep2Component, isEqual);
export const TakePhotoStep2 = TakePhotoStep2Memo;
