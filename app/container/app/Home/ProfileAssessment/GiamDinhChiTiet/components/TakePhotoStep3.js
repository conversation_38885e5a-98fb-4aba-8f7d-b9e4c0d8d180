import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectMucDoTonThat} from '@app/redux/slices/CategoryCommonSlice';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {Empty, Icon, ImageComp, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Image, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';
import {MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC} from '../constant';

const TakePhotoStep3Component = ({
  imagesData,
  removeImage,
  onPressOpenCamera,
  onPressXemLaiAnh,
  onPressPhanLoaiAnh,
  profileData,
  openCameraModal,
  rotationImage,
  onPressXoaHangMuc,
  onPressXemChiTietAnhDaUpload,
  onPressXoaAnhDaUpload,
}) => {
  // let [noImage, setNoImage] = useState(true);
  // const [cauHinhPhanLoai, setCauHinhPhanLoai] = useState(null);

  const levelLost = useSelector(selectMucDoTonThat);

  // useEffect(() => {
  //   let cauHinhPhanLoai;
  //   if (profileData && profileData.cau_hinh) {
  //     cauHinhPhanLoai = getCauHinhHoSoByMa(DATA_CONSTANT.CAU_HINH_HO_SO_MA.PHAN_LOAI_DANH_GIA, profileData.cau_hinh);
  //     setCauHinhPhanLoai(cauHinhPhanLoai);
  //   }
  // }, [profileData]);

  // useEffect(() => {
  //   let noImage = true;
  //   for (let i = 0; i < imagesData.length; i++) {
  //     if (imagesData[i].images.length >= 2) noImage = false;
  //   }
  //   setNoImage(noImage);
  // }, [imagesData]);

  const getMucDoTonThayTheoMa = (maTonThat) => {
    if (!maTonThat)
      return {
        ten: '',
      };
    let mucDoTonThat;
    levelLost.map((item) => {
      if (item.ma === maTonThat) mucDoTonThat = item;
    });
    return mucDoTonThat;
  };

  const renderCameraButton = (imageData, listAnhDangChup, mucDoTonThat, anhDaUpload) => (
    <TouchableOpacity
      style={styles.cameraView}
      onPress={() => {
        if (listAnhDangChup.length - 1 >= MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC - anhDaUpload.length)
          return FlashMessageHelper.showFlashMessage('Thông báo', 'Hạng mục này chỉ chụp đã chụp tối đa ' + MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC + ' ảnh. Vui lòng xoá bớt ảnh để chụp thêm.');
        imageData ? openCameraModal(imageData.nhom, mucDoTonThat) : onPressOpenCamera();
      }}>
      <Icon.MaterialCommunityIcons name="camera-plus" size={40} color={colors.GRAY11} style={styles.iconCamera} />
      {MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC < 100 && <Text children={listAnhDangChup.length - 1 + '/' + (MAX_SO_LUONG_ANH_CHUP_MOT_HANG_MUC - anhDaUpload.length)} color={colors.GRAY11} />}
    </TouchableOpacity>
  );

  const renderImageCategoryButton = () => (
    <TouchableOpacity onPress={onPressOpenCamera}>
      <View style={styles.imageCategoryTitleView}>
        <Text style={styles.imageCategoryTitle} font="bold14" children="Thêm hạng mục tổn thất" color={colors.PRIMARY} />
      </View>
      <View style={[styles.cameraView, {borderWidth: 2, borderColor: colors.PRIMARY}]}>
        <Icon.MaterialCommunityIcons name="camera-plus" size={40} style={styles.iconCamera} color={colors.PRIMARY} />
      </View>
    </TouchableOpacity>
  );

  //render ra ảnh
  const renderImageItem = (imageData, extraData) => {
    let index = imageData.index;
    if (!imageData.item.path && index !== extraData.images.length - 1) return null; //vị trí cuối cùng
    if (index === extraData.images.length - 1) return renderCameraButton(extraData.images[0], extraData.images, imageData.item.muc_do, extraData.anhDaUpload);
    return <ImageComp imageData={imageData} removeImage={() => removeImage(imageData)} rotationImage={rotationImage} onPressXemLaiAnh={onPressXemLaiAnh} sttHangMuc={imageData.stt_hang_muc} />;
  };

  const renderItemAnhDaUpload = ({item, index}, extraData) => (
    <TouchableOpacity
      onPress={() => {
        NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
          currentImageData: {item, index},
          imagesData: extraData.listAnhHangMuc,
        });
      }}>
      <Image style={styles.imageProcess} source={{uri: `data:image/gif;base64,${item.duong_dan}`}} resizeMode={'contain'} />
      <View style={styles.iconRemoveAnhDaUploadView}>
        <TouchableOpacity style={{backgroundColor: '#FFF', borderRadius: 100}} onPress={() => onPressXoaAnhDaUpload({item, index})}>
          <Icon.AntDesign name="closecircle" size={25} color={colors.RED1} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderMenuImage = ({item, index}) => {
    let maMucDo = null;
    if (item.images.length > 0) maMucDo = item.images[0].muc_do;
    // else if (item.anhDaUpload.length > 0) maMucDo = item.anhDaUpload[0].muc_do;
    return (
      <View style={[styles.itemMenuView, index % 2 === 0 && {backgroundColor: colors.WHITE8}]}>
        <View style={[styles.imageCategoryTitleView, {justifyContent: 'space-between'}]}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <View>
              <View style={styles.tenHangMucView}>
                {/* {data.item.isAIData && <Text children="[AI]" style={styles.txtAI} font="bold14" />} */}
                <Text style={[styles.imageCategoryTitle, item.isAIData && {marginLeft: spacing.tiny}]} children={item.tenAlias || item.ten} />
              </View>
              {(item.ma?.includes('ANH_KHAC') || item.ma?.includes('ANH_TAI_SAN_KHAC')) && <Text children="Chưa phân loại theo bộ mã danh mục" color={colors.ORANGE} />}
              <TouchableOpacity style={{flexDirection: 'row'}} onPress={() => onPressPhanLoaiAnh(item)}>
                {maMucDo ? (
                  <Text style={styles.txtTonThat} children={(getMucDoTonThayTheoMa(maMucDo)?.ten || '') + ' >'} />
                ) : (
                  <Text style={[styles.txtTrangThaiDanhGia]} children="Chưa đánh giá >" color={colors.ORANGE} />
                )}
              </TouchableOpacity>
            </View>
          </View>
          {/* {data.item.isAIData && <Text children="[AI]" style={styles.txtAI} font="bold14" />} */}

          <TouchableOpacity style={styles.thongTinChiTietView} onPress={() => onPressXemChiTietAnhDaUpload(index)}>
            <Text children={`Ảnh đã tải lên (${item.anhDaUpload.length})`} color={colors.PRIMARY} style={[styles.txtMenuImageActionView, {marginRight: spacing.tiny}]} />
            <Icon.FontAwesome name={item.isShowAnhDaUpload ? 'angle-down' : 'angle-up'} size={18} color={colors.PRIMARY} />
          </TouchableOpacity>

          {/* <TouchableOpacity onPress={() => onPressXoaHangMuc({item, index})}>
            <Icon.FontAwesome name="trash-o" color={colors.RED1} size={25} />
          </TouchableOpacity> */}

          {/* {cauHinhPhanLoai && cauHinhPhanLoai.gia_tri === DATA_CONSTANT.CAU_HINH_HO_SO_GIA_TRI.CO && (
            <TouchableOpacity style={styles.trangThaiDanhGiaView} onPress={() => onPressPhanLoaiAnh(data.item)}>
              <Text
                style={[styles.txtTrangThaiDanhGia, {color: anhCuoiCung.nhomMoi || anhCuoiCung.muc_do ? colors.GREEN : colors.BLACK}]}
                children={anhCuoiCung.nhomMoi || anhCuoiCung.muc_do ? 'Đã đánh giá' : 'Đánh giá'}
              />
              <Icon.Entypo name="chevron-small-right" size={20} color={anhCuoiCung.nhomMoi || anhCuoiCung.muc_do ? colors.GREEN : colors.BLACK} />
            </TouchableOpacity>
          )} */}
        </View>
        {item.isShowAnhDaUpload && (
          <View style={styles.anhDaUploadView}>
            <Text children="Ảnh đã tải lên hệ thống" style={[styles.imageCategoryTitle, {fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
            {item.anhDaUpload.length > 0 && (
              <FlatList
                keyExtractor={(item, index) => index.toString()}
                scrollEnabled={true}
                data={item.anhDaUpload}
                renderItem={(itemAnhDaUpload) => renderItemAnhDaUpload(itemAnhDaUpload, {listAnhHangMuc: item.anhDaUpload})}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
              />
            )}
            {item.anhDaUpload.length === 0 && (
              <View style={{marginBottom: spacing.default}}>
                <Empty imageStyle={{width: dimensions.width / 5, height: dimensions.width / 5}} />
              </View>
            )}
          </View>
        )}
        <View>
          {item.isShowAnhDaUpload && (
            <Text children="Ảnh chưa tải lên hệ thống" style={[styles.imageCategoryTitle, {marginLeft: spacing.default, fontStyle: 'italic'}]} font="regular12" color={colors.GRAY7} />
          )}
          <FlatList
            scrollEnabled={true}
            data={item.images}
            renderItem={(itemAnh) => renderImageItem(itemAnh, {images: item.images, anhDaUpload: item.anhDaUpload})}
            numColumns={3}
            horizontal={false}
          />
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={{flex: 1, marginBottom: spacing.huge}}>
      {renderImageCategoryButton()}
      <FlatList data={imagesData} renderItem={renderMenuImage} keyExtractor={(item, index) => index.toString()} initialNumToRender={imagesData.length} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    width: dimensions.width,
  },
  cameraView: {
    borderWidth: 1,
    borderColor: colors.GRAY11,
    borderStyle: 'dashed',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: spacing.smaller,
    marginVertical: spacing.smaller,
  },
  iconCamera: {
    padding: spacing.smaller,
  },
  imageCategoryTitleView: {
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginHorizontal: spacing.small,
    // marginTop: spacing.smaller,
  },
  imageCategoryTitle: {
    // color: colors.BLACK,
    // fontWeight: 'bold',
    // marginLeft: spacing.smaller,
    marginRight: spacing.small,
    maxWidth: dimensions.width * 0.6,
    // borderWidth: 1,
  },
  txtTrangThaiDanhGia: {
    // color: colors.BLACK,
    // fontWeight: 'bold',
    // marginLeft: spacing.smaller,
    textAlign: 'right',
  },
  txtTonThat: {
    color: colors.GREEN,
  },
  itemMenuView: {
    paddingVertical: spacing.smaller,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY11,
  },
  trangThaiDanhGiaView: {
    flexDirection: 'row',
    // flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  txtAI: {
    color: colors.PRIMARY,
    marginLeft: spacing.smaller,
  },
  tenHangMucView: {
    flexDirection: 'row',
  },
  thongTinChiTietView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  anhDaUploadView: {
    borderTopWidth: 0.3,
    borderBottomWidth: 0.3,
    marginHorizontal: spacing.default,
    marginTop: spacing.smaller,
    paddingTop: spacing.smaller,
    marginBottom: spacing.default,
    borderColor: colors.GRAY3,
  },
  imageProcess: {
    marginRight: spacing.default,
    marginVertical: spacing.default,
    borderRadius: 8,
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    resizeMode: 'cover',
    backgroundColor: colors.GRAY2,
    // borderWidth: 1,
  },
  iconRemoveAnhDaUploadView: {
    position: 'absolute',
    top: spacing.medium,
    right: spacing.medium,
  },
});

const TakePhotoStep3Memo = memo(TakePhotoStep3Component, isEqual);
export const TakePhotoStep3 = TakePhotoStep3Memo;
