import {Dimensions, StyleSheet} from 'react-native';
import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {isIOS} from '@app/commons/Constant';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  headerTitleView: {
    marginBottom: spacing.tiny,
    paddingVertical: spacing.tiny,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: spacing.small,
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
  },
  subLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginVertical: 6,
    color: colors.GRAY6,
    marginHorizontal: spacing.tiny,
  },
  btnAddGara: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: spacing.smaller,
  },
  txtAddGara: {
    color: colors.PRIMARY,
    fontWeight: '700',
    paddingRight: spacing.smaller,
  },

  footerView: {
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE1,
    // paddingBottom: 70,
  },
  footerBtn: {
    marginHorizontal: 16,
    marginBottom: isIOS ? getStatusBarHeight() - 10 : 0,
  },
  itemPhuongAn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.smaller,
    justifyContent: 'space-between',
    paddingVertical: spacing.smaller,
    marginHorizontal: spacing.smaller,
    backgroundColor: '#FFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.55,
    shadowRadius: 3.84,
    elevation: 5,
  },
  txtGaraName: {
    fontSize: 16,
    color: colors.PRIMARY,
  },
  titleRowView: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderColor: colors.GRAY,
  },
  col1: {
    alignItems: 'center',
    width: width / 12 - 6,
    borderRightWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  percentCol: {
    width: width / 12,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  title: {
    fontSize: 13,
    lineHeight: 16,
    fontWeight: '700',
    color: colors.PRIMARY,
    marginHorizontal: spacing.tiny,
    marginVertical: spacing.smaller,
  },
  priceCol: {
    flex: 1,
    width: width / 4,
    alignItems: 'flex-end',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  price: {
    fontSize: 13,
    marginRight: 4,
    fontWeight: '500',
    textAlign: 'right',
    color: colors.GRAY6,
  },
  col1Txt: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  col1SubTxt: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.GRAY6,
  },
  titleCol: {
    width: width - 120,
  },
  subLabelCol: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.RED1,
    textAlign: 'center',
    textTransform: 'uppercase',
    paddingVertical: spacing.smaller,
  },
  subHeaderTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'left',
    color: colors.RED1,
    marginLeft: spacing.smaller,
    paddingVertical: spacing.smaller,
  },
  btnStyles: {
    marginHorizontal: 10,
  },
  bottomBtn: {
    width: width - 20,
    marginVertical: 10,
    marginHorizontal: 10,
  },
  bottomBtnView: {
    flex: 1,
    zIndex: 2,
    bottom: 0,
    position: 'absolute',
    backgroundColor: 'white',
  },
  btnDanhGia: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtBtn: {
    color: colors.BLACK_03,
    textDecorationLine: 'underline',
  },
  toggleHeaderBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  btnDieuChinhTLTT: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: spacing.mediumPlush,
  },
});
