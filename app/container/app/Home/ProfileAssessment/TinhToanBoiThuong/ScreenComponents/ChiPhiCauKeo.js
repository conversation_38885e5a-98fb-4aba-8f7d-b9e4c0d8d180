import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, TextInputOutlined, Text} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Dimensions, FlatList, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {NumericFormat} from 'react-number-format';

const {width} = Dimensions.get('window');
const ChiPhiCauKeoScreenComponent = (props) => {
  console.log('ChiPhiCauKeoScreenComponent');
  const route = useRoute();
  const {profileData} = route?.params;

  const [loading, setLoading] = useState(true);

  const [checkThue, setCheckThue] = useState(true);
  const [checkTNhiem, setCheckTNhiem] = useState(true);
  const [checkGiamTru, setCheckGiamTru] = useState(true);

  const [dsHangMuc, setDsHangMuc] = useState([]);

  useEffect(() => {
    getData();
  }, []);

  const onPressLuu = async () => {
    setLoading(true);
    let cp_khac = [];
    dsHangMuc.forEach((e) => {
      let json = {bt: e.bt, pt_bao_hiem: e.pt_bao_hiem, pt_giam_tru: e.pt_giam_tru, tl_thue: e.tl_thue, pt_giam_tru_loi: e.pt_giam_tru_loi};
      cp_khac.push(json);
    });
    const params = {
      ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
      so_id: profileData?.ho_so?.so_id,
      so_id_doi_tuong: dsHangMuc[0]?.so_id_doi_tuong,
      lh_nv: profileData?.lh_nv[0].ma,
      loai: 'CP_CAU_KEO',
      cp_khac: cp_khac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 71');
    }
  };

  const getData = async () => {
    setLoading(true);
    let params = {
      so_id: profileData?.ho_so?.so_id,
      ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
      lh_nv: profileData?.lh_nv[0].ma,
      loai: 'CP_CAU_KEO',
      vu_tt: '',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.data;
      setDsHangMuc(data);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 146');
    }
  };

  //
  const onChangeInputTrachNhiem = (it, idx, val) => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (checkTNhiem) {
        listHangMucTmp[index].pt_bao_hiem = +val;
      } else {
        listHangMucTmp[idx].pt_bao_hiem = +val;
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };
  const onChangeInputTLThue = (it, idx, val) => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (checkThue) {
        listHangMucTmp[index].tl_thue = +val;
      } else {
        listHangMucTmp[idx].tl_thue = +val;
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };
  const onChangeInputGiamTru = (it, idx, val) => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (checkGiamTru) {
        listHangMucTmp[index].pt_giam_tru = +val;
      } else {
        listHangMucTmp[idx].pt_giam_tru = +val;
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };

  const onChangeInputGiamTruLoi = (it, idx, val) => {
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (checkGiamTru) {
        listHangMucTmp[index].pt_giam_tru_loi = +val;
      } else {
        listHangMucTmp[idx].pt_giam_tru_loi = +val;
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };

  /* RENDER */
  const renderItem = ({index, item}) => {
    return (
      <View>
        <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
          <View flex={0.5} flexDirection="row">
            {/* <View style={styles.titleCol} borderRightWidth={0.5}>
              <Text style={styles.txtTenHangMuc} children={item.ten_doi_tuong} />
            </View> */}
            <View style={styles.titleCol} borderRightWidth={0.5}>
              <Text style={styles.txtTenHangMuc} children={item.ten_chi_phi} />
            </View>
            <View style={styles.titleCol}>
              <NumericFormat value={item.so_tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.txtTenHangMuc} children={value} />} />
            </View>
          </View>
          <View style={styles.rowStyles}>
            {/* % GIẢM TRỪ LỖI */}
            <View style={styles.frame}>
              <TextInputOutlined
                maxLength={3}
                placeholder="0"
                keyboardType="numeric"
                inputStyle={styles.inputStyle}
                value={item?.pt_giam_tru_loi?.toString()}
                containerStyle={[styles.inputContainer]}
                onChangeText={(value) => onChangeInputGiamTruLoi(item, index, value)}
              />
            </View>
            {/* % GIẢM TRỪ */}
            <View style={styles.frame}>
              <TextInputOutlined
                maxLength={3}
                placeholder="0"
                keyboardType="numeric"
                inputStyle={styles.inputStyle}
                value={item?.pt_giam_tru?.toString()}
                containerStyle={[styles.inputContainer]}
                onChangeText={(value) => onChangeInputGiamTru(item, index, value)}
              />
            </View>
            {/* % TRÁCH NHIỆM */}
            <View style={styles.frame}>
              <TextInputOutlined
                maxLength={3}
                placeholder="0"
                keyboardType="numeric"
                inputStyle={styles.inputStyle}
                value={item?.pt_bao_hiem?.toString()}
                containerStyle={[styles.inputContainer]}
                onChangeText={(value) => onChangeInputTrachNhiem(item, index, value)}
              />
            </View>
            {/* % THUẾ */}
            <View style={styles.frame}>
              <TextInputOutlined
                maxLength={3}
                placeholder="0"
                keyboardType="numeric"
                inputStyle={styles.inputStyle}
                value={item?.tl_thue?.toString()}
                containerStyle={[styles.inputContainer]}
                onChangeText={(value) => onChangeInputTLThue(item, index, value)}
              />
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      isLoading={loading}
      title="Chi phí cẩu/kéo/khác"
      onPressRight={onPressLuu}
      rightComponent={renderRightHeaderComponent()}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <View style={styles.tableTitleRow}>
              <View flexDirection="row" flex={0.5}>
                {/* <View style={styles.titleCol} borderRightWidth={0.5}>
                  <Text style={styles.txtHangMuc} children="Đ.tượng" />
                </View> */}
                <View style={styles.titleCol} borderRightWidth={0.5}>
                  <Text style={styles.txtHangMuc} children="Chi phí" />
                </View>
                <View style={styles.titleCol}>
                  <Text style={styles.txtHangMuc} children="Số tiền" />
                </View>
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="%G.trừ lỗi" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkTNhiem} onValueChange={setCheckTNhiem} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="%T.nhiệm" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkTNhiem} onValueChange={setCheckTNhiem} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="%G.trừ" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkGiamTru} onValueChange={setCheckGiamTru} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="%Thuế" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkThue} onValueChange={setCheckThue} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItem}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  btnLuu: {
    marginHorizontal: 10,
  },
  inputStyle: {
    borderRadius: 0,
    width: width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  item: {
    // paddingRight: 2,
  },
  content: {
    flex: 1,
    marginTop: 10,
    // marginHorizontal: 10,
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginVertical: spacing.small,
  },
  checkBox: {
    paddingHorizontal: 10,
  },

  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    alignItems: 'center',
    flex: 1,
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51, 85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: '600',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  txtGroup: {
    fontSize: 12,
  },
  titleCol: {
    flex: 1,
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'center',
  },
  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
});

export const ChiPhiCauKeoScreen = memo(ChiPhiCauKeoScreenComponent, isEqual);
