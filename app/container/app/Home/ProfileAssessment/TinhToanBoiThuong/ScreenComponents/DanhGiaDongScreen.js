import {DATA_CONSTANT} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {scale, spacing, vScale, FontSize} from '@app/theme';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, DropdownPicker, Icon, TextInputOutlined, Text} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, FlatList, RefreshControl, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import RadioGroup from 'react-native-radio-buttons-group';
import {ModalDeXuat, ModalInput, ModalNhanXet} from '../Components';

const DanhGiaDongScreenComponent = ({route}) => {
  console.log('DanhGiaDongScreenComponent');
  const {profileData, loaiDanhGia} = route.params;
  const [refreshing, setRefreshing] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(true);

  const [dataFormDanhGiaBoiThuongVien, setDataFormDanhGiaBoiThuongVien] = useState([]);

  const [dataDanhGia, setDataDanhGia] = useState(null);
  const [inputErrDong, setInputErrDong] = useState([]);

  const [openLhnv, setOpenLhnv] = useState(false); // LOẠI HÌNH NGHIỆP VỤ
  const [openDonViNhanHoaDon, setOpenDonViNhanHoaDon] = useState(false); //Đơn vị nhận hồ sơ, tài liệu
  const [openDonViXuatHoaDon, setOpenDonViXuatHoaDon] = useState(false); //Đơn vị nhận hồ sơ, tài liệu

  const [title, setTitle] = useState('');

  const [nxMau, setNxMau] = useState([]); //list các mẫu nhận xét
  const [dxMau, setDxMau] = useState([]); //list các mẫu đề xuất
  const [loadingNX, setLoadingNX] = useState(false); //loading nhận xét
  const [loadingDX, setLoadingDX] = useState(false); //loading đề xuất

  let refModalNhanXet = useRef(null);
  let refModalDeXuat = useRef(null);
  let refModalInput = useRef(null);

  const getDefaultFormValue = () => {
    return {
      loaiHinhNghiepVu: profileData.lh_nv.length > 0 ? profileData.lh_nv[0].ma : '',
      donViNhanHoaDon: profileData.dvi_nhan_hdon.length > 0 ? profileData.dvi_nhan_hdon[0].dvi_nhan : '',
      donViXuatHoaDon: profileData?.dvi_xuat_hdon?.length > 0 ? profileData.dvi_xuat_hdon[0].dvi_xuat : '',
      nhanXetCuaBTV: '',
      deXuatCuaBTV: '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    getValues,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });
  const watchLHNVSelected = watch('loaiHinhNghiepVu');
  const watchNhanXet = watch('nhanXetCuaBTV');
  const watchDeXuat = watch('deXuatCuaBTV');

  useEffect(() => {
    getCauTrucFormDanhGia();
    initDropdownData();
  }, []);

  const initDropdownData = () => {
    if (profileData && profileData.dvi_nhan_hdon) {
      profileData.dvi_nhan_hdon.map((e) => {
        e.ten_hien_thi_dv_nhan = e.dvi_nhan + ' - ' + e.ten_dvi_nhan;
        return e;
      });
    }
    if (profileData && profileData.dvi_xuat_hdon) {
      profileData.dvi_xuat_hdon.map((e) => {
        e.ten_hien_thi_dv_xuat = e.dvi_xuat + ' - ' + e.ten_dvi_xuat;
        return e;
      });
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    getCauTrucFormDanhGia();
  };
  const getCauTrucFormDanhGia = async () => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CAU_TRUC_FORM_DANH_GIA_DONG, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        refreshing && setRefreshing(false);
        dialogLoading && setDialogLoading(false);
        return;
      }
      let formDanhGiaBTVInput = response.data_info.loai;
      let formDanhGiaBTVGiaTri = response.data_info.lke;
      formDanhGiaBTVInput.map((item) => {
        if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
          item.value = ''; //giá trị input thay đổi
          item.multiline = true;
          item.numberOfLines = 2;
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
          item.radioData = [];
          //filter lấy giá trị theo loai
          let giaTriRadio = formDanhGiaBTVGiaTri.filter((itemGiaTri) => itemGiaTri.loai === item.loai);
          //convert dữ liệu để hiển thị radio
          giaTriRadio.map((itemRadio) => {
            itemRadio.id = itemRadio.dg_stt;
            itemRadio.label = itemRadio.ten;
            itemRadio.value = itemRadio.ma;
            itemRadio.containerStyle = {
              marginRight: 0,
            };
            itemRadio.selected = false;
            itemRadio.size = 18;
            return itemRadio;
          });
          //nếu radio chưa được selectec giá trị và radio đấy bắt buộc nhập -> chọn 1 giá trị mặc định cho nó
          if (item.bat_buoc_nhap === 1) giaTriRadio[0].selected = true;
          item.radioData = giaTriRadio;
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
          item.checkBoxData = [];
          //filter lấy giá trị theo loai
          let checkBoxData = formDanhGiaBTVGiaTri.filter((itemGiaTri) => itemGiaTri.loai === item.loai && itemGiaTri.lhnv === item.lhnv);
          //convert dữ liệu để hiển thị checkbox
          checkBoxData.map((itemCheckbox) => {
            itemCheckbox.checked = false;
            return itemCheckbox;
          });
          item.checkBoxData = checkBoxData;
        }
      });
      setDataFormDanhGiaBoiThuongVien(formDanhGiaBTVInput);
      dienDataDaDanhGiaVaoForm(formDanhGiaBTVInput);
    } catch (error) {
      refreshing && setRefreshing(false);
      dialogLoading && setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const dienDataDaDanhGiaVaoForm = async (formDanhGiaBTVInput) => {
    let params = {
      so_id: profileData.ho_so.so_id,
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      lh_nv: watchLHNVSelected,
      pm: loaiDanhGia,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DU_LIEU_FORM_DANH_GIA_DONG, params);
      setRefreshing(false);
      dialogLoading && setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDataDanhGia(response.data_info);
      const danhGiaChiTiet = response.data_info.danh_gia_ct;
      const thongTinKhac = response.data_info.thong_tin_khac;
      setValue('deXuatCuaBTV', thongTinKhac.de_xuat);
      setValue('nhanXetCuaBTV', thongTinKhac.danh_gia);
      setValue('donViNhanHoaDon', thongTinKhac.dvi_nhan_hdon ? thongTinKhac.dvi_nhan_hdon : profileData.dvi_nhan_hdon.length > 0 ? profileData.dvi_nhan_hdon[0].dvi_nhan : '');
      setValue('donViXuatHoaDon', thongTinKhac.dvi_xuat_hdon ? thongTinKhac.dvi_xuat_hdon : profileData.dvi_nhan_hdon.length > 0 ? profileData.dvi_nhan_hdon[0].dvi_nhan : '');
      if (danhGiaChiTiet.length > 0) {
        formDanhGiaBTVInput.map((itemForm) => {
          for (let i = 0; i < danhGiaChiTiet.length; i++) {
            if (itemForm.loai === danhGiaChiTiet[i].loai && itemForm.lhnv === danhGiaChiTiet[i].lhnv) {
              if (itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
                itemForm.value = danhGiaChiTiet[i].noi_dung;
                break;
              } else if (itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
                itemForm.radioData.map((itemRadio) => {
                  if (itemRadio.ma === danhGiaChiTiet[i].noi_dung) itemRadio.selected = true;
                  else if (itemForm.bat_buoc_nhap === 1) itemRadio.selected = false;
                  return itemRadio;
                });
                break;
              } else if (itemForm.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
                let giaTriCheckbox = danhGiaChiTiet[i].noi_dung;
                itemForm.checkBoxData.map((itemCheckbox) => {
                  if (itemCheckbox.ma === giaTriCheckbox) itemCheckbox.checked = true;
                  return itemCheckbox;
                });
                break;
              }
            }
          }
          return itemForm;
        });
        setDataFormDanhGiaBoiThuongVien([...formDanhGiaBTVInput]);
      }
    } catch (error) {
      refreshing && setRefreshing(false);
      dialogLoading && setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onOpenModalNhanXet = (t) => {
    setTitle(t);
    refModalInput.current.show();
  };
  const onOpenDeXuatMau = (t) => {
    setTitle(t);
    getDeXuatMau();
    refModalDeXuat.current.show();
  };
  const onOpenNhanXetMau = (t) => {
    setTitle(t);
    getNhanXetMau();
    refModalNhanXet.current.show();
  };

  const onPressLuu = async (data) => {
    try {
      let haveErr = false;
      let inputErrDongTmp = inputErrDong;
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        pt_che_tai: dataDanhGia.thong_tin_khac?.pt_che_tai || 0, //CÁI NÀY ĐANG SAI
        lh_nv: data.loaiHinhNghiepVu,
        de_xuat: data.deXuatCuaBTV,
        danh_gia: data.nhanXetCuaBTV,
        dvi_nhan: data.donViNhanHoaDon,
        dvi_xuat: data.donViXuatHoaDon,
        pm: loaiDanhGia,
        data_loai: [],
        data_ten_loai: [],
        data_lhnv: [],
        data_noi_dung: [],
      };

      dataFormDanhGiaBoiThuongVien.map((item, index) => {
        if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
          if (item.bat_buoc_nhap == 1 && (!item.value || !item.value?.trim())) {
            inputErrDongTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          } else if (item.value) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            let value = '';
            if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA) value = item.value;
            else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER) {
              if (item.value.includes(',')) value = +item.value.split(',').join('');
              else value = +item.value;
            }
            params.data_noi_dung.push(value);
            params.data_lhnv.push(item.lhnv);
          }
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
          item.radioData.map((itemRadio) => {
            if (itemRadio.selected) {
              params.data_noi_dung.push(itemRadio.ma);
              params.data_loai.push(item.loai);
              params.data_ten_loai.push(item.ten_loai);
            }
          });
        } else if (item.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
          let coChecked = false,
            data_noi_dung = '';
          item.checkBoxData.forEach((itemCheckbox) => {
            if (itemCheckbox.checked) {
              coChecked = true;
              data_noi_dung = itemCheckbox.ma;
            }
          });
          if (coChecked) {
            params.data_loai.push(item.loai);
            params.data_ten_loai.push(item.ten_loai);
            params.data_noi_dung.push(data_noi_dung);
            params.data_lhnv.push(item.lhnv);
          }
          if (!coChecked && item.bat_buoc_nhap) {
            inputErrDongTmp[index] = 'Thông tin bắt buộc';
            haveErr = true;
          }
        }
      });
      if (haveErr) {
        setInputErrDong([...inputErrDongTmp]);
        return;
      }
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_DANH_GIA_DONG, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu đánh giá thành công', 'success');
      dienDataDaDanhGiaVaoForm(dataFormDanhGiaBoiThuongVien);
    } catch (error) {
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };
  const onPressLuuMau = async (nd, tit) => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      so_id: 0,
      nv: 'XE',
      pm: 'BT',
      nv_ct: tit === 'Nhận xét' ? 'NHAN_XET' : 'DE_XUAT',
      noi_dung: nd,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_NHAN_XET_MAU, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', `Lưu ${tit} thành công`, 'success');
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };
  const getNhanXetMau = async () => {
    setLoadingNX(true);
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      nv: 'XE',
      pm: 'BT',
      nv_ct: 'NHAN_XET',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_NHAN_XET_MAU, params);
      setLoadingNX(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setNxMau(response.data_info.noi_dung);
    } catch (error) {
      setLoadingNX(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };
  const getDeXuatMau = async () => {
    setLoadingDX(true);
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      nv: 'XE',
      pm: 'BT',
      nv_ct: 'DE_XUAT',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_NHAN_XET_MAU, params);
      setLoadingDX(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDxMau(response.data_info.noi_dung);
    } catch (error) {
      setLoadingDX(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onOpenDropDown = (type) => {
    type !== 0 && openDonViNhanHoaDon && setOpenDonViNhanHoaDon(false);
    type !== 1 && openDonViXuatHoaDon && setOpenDonViXuatHoaDon(false);
  };

  /* RENDER */

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };
  const renderItemCheckbox = (item, index, extraData) => {
    return (
      <TouchableOpacity style={styles.checkboxView} onPress={() => extraData.onChangeValue(!item.checked, index)}>
        <CheckboxComp value={item.checked} checkboxStyle={[styles.checkBox]} disabled={true} />
        <Text children={item.ten} />
      </TouchableOpacity>
    );
  };
  const renderRadioInput = (title, radioButtons, onPressRadioButton, isRequired) => {
    let radioButtonsTmp = cloneObject(radioButtons);
    if (radioButtons.length === 2) radioButtonsTmp.map((item) => (item.containerStyle.flex = 1));
    return (
      <View style={{marginVertical: spacing.tiny}}>
        <Text style={styles.inputTitle}>
          {title} {isRequired && <Text children="(*)" style={{color: colors.RED1}} />}
        </Text>
        <ScrollView horizontal>
          <RadioGroup radioButtons={radioButtonsTmp} onPress={onPressRadioButton} layout={'row'} />
        </ScrollView>
      </View>
    );
  };
  const onChangeTxtInput = (index, value) => {
    let dataFormDanhGiaBoiThuongVienTmp = dataFormDanhGiaBoiThuongVien;
    let inputErrTmp = inputErrDong;
    if (!value.trim() && dataFormDanhGiaBoiThuongVienTmp[index].bat_buoc_nhap === 1) inputErrTmp[index] = 'Thông tin bắt buộc';
    else if (value.trim()) inputErrTmp[index] = '';
    setInputErrDong([...inputErrTmp]);
    dataFormDanhGiaBoiThuongVienTmp[index].value = value;
    setDataFormDanhGiaBoiThuongVien([...dataFormDanhGiaBoiThuongVienTmp]);
  };

  const renderItemFormDanhGia = (data) => {
    let itemDanhGia = data.item;
    if (itemDanhGia.lhnv !== watchLHNVSelected) return;
    let dataFormDanhGiaBoiThuongVienTmp = dataFormDanhGiaBoiThuongVien;
    if (itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA || itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.NUMBER)
      return (
        <View style={{marginHorizontal: spacing.small}}>
          <TextInputOutlined
            title={itemDanhGia.ten_loai}
            value={itemDanhGia.value}
            placeholder={itemDanhGia.ten_loai}
            keyboardType={itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.TEXTAREA ? '' : 'numeric'}
            multiline={itemDanhGia.multiline}
            numberOfLines={itemDanhGia.numberOfLines}
            error={inputErrDong[data.index]}
            isRequired={itemDanhGia.bat_buoc_nhap === 1 ? true : false}
            onChangeText={(value) => onChangeTxtInput(data.index, value)}
          />
        </View>
      );
    else if (itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.RADIO) {
      return renderRadioInput(
        itemDanhGia.ten_loai,
        itemDanhGia.radioData,
        (value) => {
          itemDanhGia.radioData = value;
          dataFormDanhGiaBoiThuongVienTmp[data.index] = itemDanhGia;
          setDataFormDanhGiaBoiThuongVien([...dataFormDanhGiaBoiThuongVienTmp]);
        },
        itemDanhGia.bat_buoc_nhap === 0 ? false : true,
      );
    } else if (itemDanhGia.kieu === DATA_CONSTANT.FORM_DANH_GIA_HIEN_TRUONG_KIEU_INPUT.CHECKBOX) {
      return (
        <View style={{marginTop: spacing.small}}>
          <Text style={styles.label}>
            {itemDanhGia.ten_loai}
            {itemDanhGia.bat_buoc_nhap !== 0 && <Text children="(*)" style={{color: colors.RED1}} />}
          </Text>
          <View style={styles.itemCheckboxRow}>
            {itemDanhGia.checkBoxData.map((item, index) =>
              renderItemCheckbox(item, index, {
                onChangeValue: (newValue, indexChange) => {
                  itemDanhGia.checkBoxData.map((itemCheckbox, indexCheckbox) => {
                    itemCheckbox.checked = indexCheckbox === indexChange ? true : false;
                  });
                  dataFormDanhGiaBoiThuongVienTmp[data.index] = itemDanhGia;
                  setDataFormDanhGiaBoiThuongVien([...dataFormDanhGiaBoiThuongVienTmp]);
                  if (itemDanhGia.bat_buoc_nhap && newValue) {
                    let inputErrTmp = inputErrDong;
                    inputErrTmp[data.index] = '';
                    setInputErrDong([...inputErrTmp]);
                  }
                },
              }),
            )}
          </View>
          {inputErrDong[data.index] && <Text children={inputErrDong[data.index]} style={{color: colors.RED1}} />}
        </View>
      );
    }
  };

  const renderFormDanhGiaCuaBoiThuongVien = () => {
    let showDaDanhGia = false;
    if (dataDanhGia?.danh_gia_ct?.filter((itemDaDanhGia) => itemDaDanhGia.lhnv === watchLHNVSelected).length > 0) showDaDanhGia = true;

    return (
      <View marginTop={spacing.medium}>
        <View style={styles.danhGiaNhanXetView}>
          <Text children="Đánh giá, nhận xét của BTV" style={styles.txtDanHGiaNhanXetCuaBTV} />
          {showDaDanhGia && (
            <View style={styles.daDanhGiaView}>
              <Text children=" - Đã đánh giá" style={styles.txtDaDanhGia} />
              <Icon.AntDesign name="checkcircle" color={colors.GREEN} size={15} />
            </View>
          )}
        </View>
        <FlatList data={dataFormDanhGiaBoiThuongVien} key={(_, index) => index.toString()} renderItem={renderItemFormDanhGia} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={dialogLoading}
      rightComponent={renderRightHeaderComponent()}
      title="Đánh giá bồi thường"
      onPressRight={handleSubmit(onPressLuu)}
      renderView={
        <View flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} showsVerticalScrollIndicator={false}>
            <Controller
              control={control}
              name="donViNhanHoaDon"
              rules={{required: true}}
              render={({field: {onChange, value}}) => {
                return (
                  <DropdownPicker
                    isRequired
                    zIndex={8000}
                    searchable={false}
                    isOpen={openDonViNhanHoaDon}
                    items={profileData.dvi_nhan_hdon}
                    setOpen={setOpenDonViNhanHoaDon}
                    title="Đơn vị nhận hồ sơ, tài liệu"
                    itemSelected={value}
                    setItemSelected={(dispatch) => onChange(dispatch())}
                    containerStyle={{marginHorizontal: scale(spacing.small)}}
                    schema={{
                      label: 'ten_hien_thi_dv_nhan',
                      value: 'dvi_nhan',
                    }}
                    inputErr={errors.donViNhanHoaDon && getErrMessage('donViNhanHoaDon', errors.donViNhanHoaDon.type)}
                    onOpen={() => onOpenDropDown(0)}
                  />
                );
              }}
            />
            <View marginBottom={openDonViXuatHoaDon ? 100 : 10}>
              <Controller
                control={control}
                name="donViXuatHoaDon"
                rules={{required: true}}
                render={({field: {onChange, value}}) => {
                  return (
                    <DropdownPicker
                      isRequired
                      zIndex={7000}
                      searchable={false}
                      isOpen={openDonViXuatHoaDon}
                      items={profileData.dvi_xuat_hdon}
                      setOpen={setOpenDonViXuatHoaDon}
                      title="Xuất hoá đơn cho đơn vị"
                      placeholder="Chọn đơn vị xuất hoá đơn"
                      itemSelected={value}
                      setItemSelected={(dispatch) => onChange(dispatch())}
                      containerStyle={{marginHorizontal: scale(spacing.small)}}
                      schema={{
                        label: 'ten_hien_thi_dv_xuat',
                        value: 'dvi_xuat',
                      }}
                      inputErr={errors.donViXuatHoaDon && getErrMessage('donViXuatHoaDon', errors.donViXuatHoaDon.type)}
                      onOpen={() => onOpenDropDown(1)}
                    />
                  );
                }}
              />
            </View>

            {loaiDanhGia === 'BT' && (
              <>
                <Controller
                  control={control}
                  name="loaiHinhNghiepVu"
                  render={({field: {onChange, value}}) => {
                    return (
                      <DropdownPicker
                        isRequired
                        items={profileData.lh_nv}
                        zIndex={6000}
                        isOpen={openLhnv}
                        searchable={false}
                        setOpen={setOpenLhnv}
                        itemSelected={value}
                        title="Loại hình nghiệp vụ"
                        setItemSelected={(dispatch) => onChange(dispatch())}
                        containerStyle={{marginHorizontal: scale(spacing.small)}}
                        schema={{
                          label: 'ten',
                          value: 'ma',
                        }}
                      />
                    );
                  }}
                />
                {renderFormDanhGiaCuaBoiThuongVien()}
                <View marginHorizontal={scale(spacing.small)}>
                  <Controller
                    control={control}
                    name="nhanXetCuaBTV"
                    rules={{
                      required: false,
                    }}
                    render={({field: {value, onChange}}) => (
                      <TextInputOutlined
                        // isRequired
                        value={value}
                        multiline={true}
                        // error={errors.nhanXetCuaBTV && getErrMessage('nhanXetCuaBTV', errors.nhanXetCuaBTV.type)}
                        keyboardType="default"
                        title="Nhận xét của BTV"
                        placeholder="Nhập nhận xét"
                        inputStyle={styles.inputStyle}
                        subTitle="Chọn nhận xét đã tạo"
                        onPressSubTitle={onOpenNhanXetMau}
                        onChangeText={onChange}
                        onPressAddIcon={() => onOpenModalNhanXet('Nhận xét')}
                        blurOnSubmit={false}
                      />
                    )}
                  />
                  <Controller
                    control={control}
                    name="deXuatCuaBTV"
                    rules={{
                      required: true,
                    }}
                    render={({field: {value, onChange}}) => (
                      <TextInputOutlined
                        isRequired
                        value={value}
                        multiline={true}
                        error={errors.deXuatCuaBTV && getErrMessage('deXuatCuaBTV', errors.deXuatCuaBTV.type)}
                        keyboardType="default"
                        title="Đề xuất của BTV"
                        placeholder="Nhập đề xuất"
                        subTitle="Chọn đề xuất đã tạo"
                        inputStyle={styles.inputStyle}
                        onPressAddIcon={() => onOpenModalNhanXet('Đề xuất')}
                        onChangeText={onChange}
                        onPressSubTitle={onOpenDeXuatMau}
                        blurOnSubmit={false}
                      />
                    )}
                  />
                </View>
              </>
            )}
          </KeyboardAwareScrollView>
          <ModalNhanXet
            refreshing={loadingNX}
            data={nxMau}
            ref={refModalNhanXet}
            setValue={(v) => setValue('nhanXetCuaBTV', v.noi_dung, {shouldValidate: true})}
            value={watchNhanXet}
            onBackPress={() => refModalNhanXet.current.hide()}
          />
          <ModalDeXuat
            refreshing={loadingDX}
            data={dxMau}
            ref={refModalDeXuat}
            setValue={(v) => setValue('deXuatCuaBTV', v.noi_dung, {shouldValidate: true})}
            value={watchDeXuat}
            onBackPress={() => refModalDeXuat.current.hide()}
          />
          <ModalInput
            title={title}
            ref={refModalInput}
            onPressLuu={(nd, tit) => {
              setRefreshing(true);
              setTimeout(() => onPressLuuMau(nd, tit), 500);
            }}
            setValue={(v) => setValue('deXuatCuaBTV', v.value, {shouldValidate: true})}
            onBackPress={() => refModalInput.current.hide()}
          />
        </View>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },
  btnView: {
    flexDirection: 'row',
    marginTop: 30,
  },
  title: {
    fontSize: FontSize.size16,
    marginVertical: vScale(spacing.small),
    fontWeight: 'bold',
    textAlign: 'right',
  },
  label: {
    color: colors.PRIMARY,
    marginLeft: scale(spacing.small),
  },
  txtDanHGiaNhanXetCuaBTV: {
    marginLeft: scale(spacing.small),
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  itemCheckboxRow: {
    borderBottomWidth: 2,
    marginHorizontal: scale(spacing.small),
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: vScale(spacing.smaller),
    paddingBottom: vScale(spacing.small),
    justifyContent: 'space-around',
    borderBottomColor: colors.GRAY2,
  },
  valueCheckboxStyles: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: scale(spacing.medium),
  },
  checkBox: {
    marginHorizontal: scale(spacing.smaller),
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    alignItems: 'center',
    flex: 1,
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  inputStyle: {
    height: 60,
    textAlignVertical: 'top',
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: vScale(spacing.tiny),
    paddingHorizontal: scale(spacing.small),
    backgroundColor: colors.GRAY2,
  },
  checkboxView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  inputTitle: {
    fontWeight: 'bold',
  },
  donViNhanHoaDonView: {
    borderBottomWidth: 5,
    borderColor: colors.GRAY,
    zIndex: 9000,
  },
  txtDaDanhGia: {
    color: colors.GREEN,
    marginRight: spacing.tiny,
    fontWeight: 'bold',
  },
  daDanhGiaView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  danhGiaNhanXetView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export const DanhGiaDongScreen = memo(DanhGiaDongScreenComponent, isEqual);
