import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Dimensions, FlatList, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {connect} from 'react-redux';
import {getValue} from './constant';

const TinhToanTrachNhiemBHScreenComponent = ({route}) => {
  console.log('TinhToanTrachNhiemBHScreenComponent');
  const {profileData, actionType, lhnv, soIdDoiTuong} = route?.params;
  const [dsHangMuc, setDsHangMuc] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isChecked, setIsChecked] = useState(true);
  const loaiTS = actionType === 'tai_san_khac' ? 'KHAC' : actionType === 'tslx_khau_hao' ? 'XE' : actionType === 'tslx_khau_tru' ? 'XE' : '';

  const onPressLuu = async () => {
    setLoading(true);
    let hm = [];
    dsHangMuc.forEach((e) => {
      let json = {
        hang_muc: e.hang_muc,
        pt_khau_hao: e.pt_khau_hao || 0,
        pt_bao_hiem: e.pt_bao_hiem || 0,
        pt_giam_tru: e.pt_giam_tru || 0,
        nguyen_nhan: e.nguyen_nhan || '',
        ghi_chu: e.ghi_chu || '',
        // --Nhap giam gia
        tl_giam_gia_vtu: e.tl_giam_gia_vtu || 0,
        tl_giam_gia_nhan_cong: e.tl_giam_gia_nhan_cong || 0,
        tl_giam_gia_khac: e.tl_giam_gia_khac || 0,
        lh_giam_gia: e.lh_giam_gia || '',
        lh_tt_giam_gia: e.lh_tt_giam_gia || '',
        // --Nhap khau tru
        tl_ktru_tien_bh: e.tl_ktru_tien_bh || 0,
        dkbs: e.dkbs || '',
        // --Nhap ti le thue
        tl_thue_vtu: e.tl_thue_vtu || 0,
        tl_thue_nhan_cong: e.tl_thue_nhan_cong || 0,
        tl_thue_khac: e.tl_thue_khac || 0,
        so_id_doi_tuong: e.so_id_doi_tuong || 0,
      };
      hm.push(json);
    });
    try {
      const params = {
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id: profileData?.ho_so?.so_id,
        so_id_doi_tuong: soIdDoiTuong || 0,
        lh_nv: lhnv,
        loai: 'PT_BAO_HIEM',
        hm: hm,
        loai_ts: loaiTS,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 71');
    }
  };

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        lh_nv: lhnv,
        loai: 'PT_BAO_HIEM',
        vu_tt: '',
        loai_ts: loaiTS,
        so_id_doi_tuong: soIdDoiTuong || 0,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.data;
      setDsHangMuc(data);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };

  const onChangeInputValue = (it, idx, val) => {
    let replaceValue = val.replace(',', '.');
    const value = getValue(replaceValue);
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (isChecked) {
        listHangMucTmp[index].pt_bao_hiem = value;
      } else {
        listHangMucTmp[idx].pt_bao_hiem = value;
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };
  /* RENDER */

  const renderItemHangMuc = ({index, item}) => {
    return (
      <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
        <Text style={styles.txtTenHangMuc} children={item.ten} />
        <View style={styles.rowStyles}>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              value={item?.pt_bao_hiem?.toString()}
              containerStyle={styles.inputContainer}
              onChangeText={(value) => onChangeInputValue(item, index, value)}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={loading}
      rightComponent={renderRightHeaderComponent()}
      title="Giảm trừ theo tỷ lệ giá trị tham gia"
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Tên đối tượng" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Tỷ lệ (%)" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={isChecked} onValueChange={setIsChecked} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItemHangMuc}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
        </SafeAreaView>
      }
    />
  );
};

const mapStateToProps = (state) => ({});

const mapDispatchToProps = {};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },

  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  content: {
    flex: 1,
    marginTop: 10,
    marginHorizontal: 10,
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: 2,
  },
  rowStyles: {
    width: dimensions.width / 3,
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
});

const TinhToanTrachNhiemBHScreenConnect = connect(mapStateToProps, mapDispatchToProps)(TinhToanTrachNhiemBHScreenComponent);
export const TinhToanTrachNhiemBHScreen = memo(TinhToanTrachNhiemBHScreenConnect, isEqual);
