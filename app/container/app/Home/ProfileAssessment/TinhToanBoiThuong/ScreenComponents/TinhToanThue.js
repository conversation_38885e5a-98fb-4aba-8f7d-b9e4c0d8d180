import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, Icon, Text, TextInputOutlined} from '@component';
import {useRoute} from '@react-navigation/native';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Dimensions, FlatList, RefreshControl, SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {getValue} from './constant';
import {NumericFormat} from 'react-number-format';
import {CustomModal} from '@app/components/CustomModal';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {Controller, useForm} from 'react-hook-form';

const TinhToanThueScreenComponent = (props) => {
  console.log('TinhToanThueScreenComponent');
  const route = useRoute();
  const {profileData, actionType, lhnv, soIdDoiTuong} = route?.params;
  const [loading, setLoading] = useState(false);
  const [checkSon, setCheckSon] = useState(true);
  const [checkVatTu, setCheckVatTu] = useState(true);
  const [checkNhanCong, setCheckNhanCong] = useState(true);
  const [dsHangMuc, setDsHangMuc] = useState([]);
  const [tongTienThue, setTongTienThue] = useState({});
  const [modalData, setModalData] = useState([]);
  const loaiTS = actionType === 'tai_san_khac' ? 'KHAC' : actionType === 'tslx_khau_hao' ? 'XE' : actionType === 'tslx_khau_tru' ? 'XE' : '';

  let refModalCapNhatTienThue = useRef(null);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    setError,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: {
      soTienGiamBT: '',
      ghiChuGiam: '',
    },
    mode: 'onChange',
  });
  const watchsoTienGiamBT = watch('soTienGiamBT');

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };
  const onPressLuu = async (data) => {
    setLoading(true);
    let hm = [];
    dsHangMuc.forEach((e) => {
      let json = {
        hang_muc: e.hang_muc,
        so_id_doi_tuong: e.so_id_doi_tuong || 0,
        pt_khau_hao: e.pt_khau_hao || 0,
        pt_bao_hiem: e.pt_bao_hiem || 0,
        pt_giam_tru: e.pt_giam_tru || 0,
        nguyen_nhan: e.nguyen_nhan || '',
        ghi_chu: e.ghi_chu || '',
        // --Nhap giam gia
        tl_giam_gia_vtu: e.tl_giam_gia_vtu || 0,
        tl_giam_gia_nhan_cong: e.tl_giam_gia_nhan_cong || 0,
        tl_giam_gia_khac: e.tl_giam_gia_khac || 0,
        lh_giam_gia: e.lh_giam_gia || '',
        lh_tt_giam_gia: e.lh_tt_giam_gia || '',
        // --Nhap khau tru
        tl_ktru_tien_bh: e.tl_ktru_tien_bh || 0,
        dkbs: e.dkbs || '',
        // --Nhap ti le thue
        tl_thue_vtu: e.tl_thue_vtu || 0,
        tl_thue_nhan_cong: e.tl_thue_nhan_cong || 0,
        tl_thue_khac: e.tl_thue_khac || 0,
      };
      hm.push(json);
    });
    try {
      const params = {
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id: profileData?.ho_so?.so_id,
        so_id_doi_tuong: soIdDoiTuong || 0,
        lh_nv: lhnv,
        loai: 'PT_THUE',
        hm: hm,
        loai_ts: loaiTS,
        tien_giam_bt: data.soTienGiamBT,
        ghi_chu_giam: data.ghiChuGiam,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 82');
    }
  };

  useEffect(() => {
    getData();
    getDataThue();
  }, []);

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        lh_nv: lhnv,
        loai: 'PT_THUE',
        vu_tt: profileData?.dien_bien[0]?.vu_tt || 0,
        loai_ts: loaiTS,
        so_id_doi_tuong: soIdDoiTuong || 0,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const data = response.data_info.data;
      setTongTienThue(response.out_value);
      setDsHangMuc(data);
      setValue('soTienGiamBT', response.out_value?.tien_giam_bt, {shouldValidate: true});
      setValue('ghiChuGiam', response.out_value?.ghi_chu_giam);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 112');
    }
  };

  const getDataThue = async () => {
    setLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_THUE, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const tienThue = convertTienThue(response.data_info);
      setModalData(tienThue);
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };

  const onCapNhatTienThue = async () => {
    setLoading(true);
    try {
      let tbldata = [];
      modalData.map((e) => {
        let json = {
          tl_thue: e.tl_thue,
          loai: e.loai,
          tien_thue: e.tien_thue,
          tien_thue_cp_khac: +e.tien_thue_cp_khac,
        };
        tbldata.push(json);
      });
      let params = {
        create_file_sign: 'ESCS_THONG_BAO_BOI_THUONG',
        gen_file: 'C',
        hanh_dong: 'K',
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        nv: profileData.ho_so.nghiep_vu,
        remove_file: 'ESCS_THONG_BAO_BOI_THUONG',
        so_id: profileData.ho_so.so_id,
        tbldata: tbldata,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CAP_NHAT_LAI_THUE, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      refModalCapNhatTienThue.current.hide();
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật thuế thành công!', 'success');
      getData();
      getDataThue();
      return;
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 35');
    }
  };

  const convertTienThue = ({data = [], cp_khac = []}) => {
    const formattedData = {};
    Array.from(data, (row) => {
      Object.keys(row)
        .filter((key) => key.startsWith('loai_'))
        .forEach((key) => {
          const loai_cp = key.replace('loai_', '');
          const loai = `${row[key]}`;

          if (loai === '') return;
          if (loai in formattedData) {
            formattedData[loai].so_tien += row[`so_tien_${loai_cp}`];
            formattedData[loai].tien_thue += row[`tien_thue_${loai_cp}`];
          } else {
            formattedData[loai] = {
              so_tien: row[`so_tien_${loai_cp}`],
              tien_thue: row[`tien_thue_${loai_cp}`],
            };
          }
        });
    });

    Array.from(cp_khac, (row) => {
      Object.keys(row)
        .filter((key) => key.startsWith('loai_'))
        .forEach((key) => {
          const loai_cp = key.replace('loai_', '');
          const loai = `${row[key]}`;

          if (loai === '') return;
          if (loai in formattedData) {
            formattedData[loai].so_tien_cp_khac = (formattedData[loai].so_tien_cp_khac ?? 0) + row[`so_tien_${loai_cp}`];
            formattedData[loai].tien_thue_cp_khac = (formattedData[loai].tien_thue_cp_khac ?? 0) + row[`tien_thue_${loai_cp}`];
          } else {
            formattedData[loai] = {
              so_tien_cp_khac: row[`so_tien_${loai_cp}`],
              tien_thue_cp_khac: row[`tien_thue_${loai_cp}`],
            };
          }
        });
    });

    return Object.keys(formattedData)
      .map((key) => {
        return {
          loai: `${key}%`,
          tl_thue: key,
          so_tien: formattedData[key].so_tien ?? 0,
          so_tien_cp_khac: formattedData[key].so_tien_cp_khac ?? 0,
          tien_thue: formattedData[key].tien_thue ?? 0,
          tien_thue_cp_khac: formattedData[key].tien_thue_cp_khac ?? 0,
        };
      })
      .filter(({so_tien, tien_thue}) => so_tien != 0 || tien_thue != 0)
      .map((item) => {
        return {
          ...item,
          tong_tien: item.so_tien + item.so_tien_cp_khac,
          tong_thue: item.tien_thue + item.tien_thue_cp_khac,
        };
      });
  };

  const onChangeInputValue = (it, idx, val) => {
    let replaceValue = val.replace(',', '.');
    const value = getValue(replaceValue);
    let listHangMucTmp = dsHangMuc;
    listHangMucTmp.map((item, index) => {
      if (it === 'Vật tư') {
        if (checkVatTu) {
          listHangMucTmp[index].tl_thue_vtu = value;
        } else {
          listHangMucTmp[idx].tl_thue_vtu = value;
        }
      } else if (it === 'Nhân công') {
        if (checkNhanCong) {
          listHangMucTmp[index].tl_thue_nhan_cong = value;
        } else {
          listHangMucTmp[idx].tl_thue_nhan_cong = value;
        }
      } else if (it === 'Sơn') {
        if (checkSon) {
          listHangMucTmp[index].tl_thue_khac = value;
        } else {
          listHangMucTmp[idx].tl_thue_khac = value;
        }
      }
    });
    setDsHangMuc([...listHangMucTmp]);
  };

  const onChangeTienThue = (field, value, index) => {
    let newArr = modalData;
    let tongTien = 0;
    if (field === 'tien_thue') newArr[index].tien_thue = value;
    if (field === 'tien_thue_cp_khac') newArr[index].tien_thue_cp_khac = value;
    newArr.map((e) => {
      tongTien = +e.tien_thue + +e.tien_thue_cp_khac;
      return (e.tong_thue = tongTien);
    });
    setModalData([...newArr]);
  };

  /* RENDER */

  const renderItemHangMuc = ({item, index}) => {
    return (
      <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
        <Text style={styles.txtTenHangMuc} children={item.ten} />
        <View style={styles.rowStyles}>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.tl_thue_vtu?.toString()}
              onChangeText={(value) => onChangeInputValue('Vật tư', index, value)}
            />
          </View>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.tl_thue_nhan_cong?.toString() || ''}
              onChangeText={(value) => onChangeInputValue('Nhân công', index, value)}
            />
          </View>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.tl_thue_khac?.toString() || ''}
              onChangeText={(value) => onChangeInputValue('Sơn', index, value)}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  const renderListFooter = () => {
    return (
      <View>
        <View style={[styles.tableTitleRow, {paddingHorizontal: spacing.smaller, paddingVertical: 2}]}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between', flex: 1}}>
            <View flex={1}>
              <Text style={styles.txtHangMuc} children="Tổng thuế" font="bold14" />
            </View>
            <View flex={1}>
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                <Text style={{color: colors.PRIMARY}} children="Sửa chữa: " font="regular14" />
                <NumericFormat value={tongTienThue.tong_thue_sc} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={{marginRight: 10}}>{value}</Text>} />
              </View>
              <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                <Text style={{color: colors.PRIMARY}} children="Chi phí khác: " font="regular14" />
                <NumericFormat value={tongTienThue.tong_thue_khac} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={{marginRight: 10}}>{value}</Text>} />
              </View>
            </View>
          </View>
        </View>
        {/* <ButtonLinear title="Cập nhật thuế" onPress={() => refModalCapNhatTienThue.current.show()} linearColors={[colors.PRIMARY, colors.PRIMARY]} /> */}
        <TouchableOpacity onPress={() => refModalCapNhatTienThue.current.show()} style={styles.bottomBtn}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Icon.MaterialCommunityIcons name="square-edit-outline" size={24} color={colors.WHITE} />
          </View>
          <Text font="regular14" style={{color: colors.WHITE, marginLeft: spacing.tiny}}>
            Cập nhật thuế
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <CustomScreen
      rightComponent={renderRightHeaderComponent()}
      title="Chi tiết thuế"
      onPressRight={handleSubmit(onPressLuu)}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <>
              <Controller
                control={control}
                rules={{
                  required: true,
                  min: 0,
                }}
                name="soTienGiamBT"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    placeholder="0"
                    title="Số tiền giảm BT"
                    blurOnSubmit={false}
                    keyboardType="numeric"
                    // returnKeyType={'next'}
                    onChangeText={onChange}
                    inputStyle={{textAlign: 'right'}}
                    containerStyle={{marginTop: 0}}
                    error={errors.soTienGiamBT && getErrMessage('soTienGiamBT', errors.soTienGiamBT.type)}
                  />
                )}
              />
              <Controller
                control={control}
                name="ghiChuGiam"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    title="Ghi chú giảm"
                    blurOnSubmit={false}
                    // returnKeyType={'next'}
                    onChangeText={onChange}
                    containerStyle={{marginTop: 0}}
                  />
                )}
              />
            </>
            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Hạng mục" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="V.tư" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkVatTu} onValueChange={setCheckVatTu} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="N.công" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkNhanCong} onValueChange={setCheckNhanCong} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Sơn" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkSon} onValueChange={setCheckSon} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItemHangMuc}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              ListFooterComponent={renderListFooter()}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
          <CustomModal
            ref={refModalCapNhatTienThue}
            renderContent={() => (
              <RenderModalCapNhatTienThue
                dialogLoading={loading}
                profileData={profileData}
                modalData={modalData}
                onChangeTienThue={onChangeTienThue}
                onPressLuu={onCapNhatTienThue}
                onCloseModal={() => refModalCapNhatTienThue.current.hide()}
              />
            )}
          />
        </SafeAreaView>
      }
    />
  );
};

const RenderModalCapNhatTienThue = (props) => {
  const {onCloseModal = () => {}, modalData = [], onChangeTienThue = () => {}, onPressLuu = () => {}, dialogLoading = false} = props;
  return (
    <View style={styles.containerModal}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Cập nhật tiền thuế
        </Text>
      </View>
      <ScrollView style={styles.contentModal}>
        <View style={styles.tableTitleRowModal}>
          <View>
            <Text style={[styles.txtHangMucModal, {textAlign: 'center'}]} width={dimensions.width / 6} children="Loại thuế" font="medium12" />
          </View>
          <View style={[styles.rowStyles]}>
            <View style={[styles.frame, styles.checkboxRow]}>
              <Text children="Sửa chữa" style={[styles.txtGroup]} />
            </View>
            <View style={[styles.frame, styles.checkboxRow]}>
              <Text children="Chi phí khác" style={styles.txtGroup} />
            </View>
            <View style={[styles.frame, styles.checkboxRow]}>
              <Text children="Tổng" style={styles.txtGroup} />
            </View>
          </View>
        </View>
        <View style={{borderWidth: 0.5, borderColor: colors.GRAY}}>
          {modalData.map((item, index) => {
            return (
              <View key={index} style={{flexDirection: 'row', alignItems: 'center', borderBottomWidth: 0.5, borderColor: colors.GRAY}}>
                <View width={dimensions.width / 6} style={{alignItems: 'center'}}>
                  <Text>{item.loai}</Text>
                </View>
                <View style={[styles.frame]}>
                  <TextInputOutlined
                    placeholder="0"
                    keyboardType="numeric"
                    inputStyle={styles.inputCapNhatThueStyle}
                    value={item?.tien_thue?.toString() || ''}
                    onChangeText={(value) => onChangeTienThue('tien_thue', value, index)}
                  />
                </View>
                <View style={[styles.frame]}>
                  <TextInputOutlined
                    placeholder="0"
                    keyboardType="numeric"
                    inputStyle={styles.inputCapNhatThueStyle}
                    value={item?.tien_thue_cp_khac?.toString() || ''}
                    onChangeText={(value) => onChangeTienThue('tien_thue_cp_khac', value, index)}
                  />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <TextInputOutlined editable={false} placeholder="0" keyboardType="numeric" inputStyle={styles.inputCapNhatThueStyle} value={item?.tong_thue?.toString() || ''} />
                </View>
              </View>
            );
          })}
        </View>
      </ScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={onCloseModal} title="Đóng" linearStyle={{marginRight: 10}} isSubBtn />
        <ButtonLinear title="Lưu" onPress={onPressLuu} loading={dialogLoading} disabled={modalData.length <= 0} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    // marginTop: 10,
    marginHorizontal: 10,
  },
  btnView: {
    paddingTop: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginBottom: !isIOS ? 10 : 0,
  },

  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },
  btnLuu: {
    marginHorizontal: 10,
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
  containerInput: {
    margin: 0,
    paddingVertical: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
  },
  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  txtCheckAll: {
    marginRight: 10,
    marginVertical: 10,
    color: colors.BLACK_03,
  },
  errText: {
    marginBottom: 5,
    color: colors.RED1,
  },
  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: 2,
  },
  checkBox: {
    paddingHorizontal: 10,
  },
  checkAll: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: spacing.smaller,
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    flex: 1,
    alignItems: 'center',
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  icBtn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtGroup: {
    fontSize: 12,
  },
  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
  txtHangMucModal: {
    color: colors.PRIMARY,
    marginVertical: spacing.tiny,
  },
  inputCapNhatThueStyle: {
    borderRadius: 0,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
    fontSize: 13,
    minWidth: dimensions.width / 6,
    marginTop: 2,
  },
  containerModal: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 12,
    borderRadius: 8,
    marginTop: getStatusBarHeight(),
    maxHeight: dimensions.height * 0.5,
  },
  tableTitleRowModal: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
  contentModal: {
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    backgroundColor: colors.WHITE,
    paddingVertical: spacing.smaller,
    paddingHorizontal: spacing.tiny,
  },
  headerModal: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  bottomBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    marginTop: 10,
    padding: 4,
    justifyContent: 'center',
    borderColor: colors.PRIMARY,
    borderRadius: 6,
    backgroundColor: colors.PRIMARY,
  },
});

export const TinhToanThueScreen = memo(TinhToanThueScreenComponent, isEqual);
