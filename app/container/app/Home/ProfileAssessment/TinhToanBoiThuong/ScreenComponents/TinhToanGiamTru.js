import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import CustomScreen from '@app/components/CustomScreen';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {logErrorTryCatch} from '@app/utils';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {CheckboxComp, Icon, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {connect} from 'react-redux';
import {ModalInput, ModalNguyenNhanGiamTru} from '../Components';
import {getValue} from './constant';

const TinhToanGiamTruScreenComponent = ({route}) => {
  console.log('TinhToanGiamTruScreenComponent');
  const {profileData, nhomNguyeNhan, actionType, lhnv, soIdDoiTuong} = route?.params;

  const [loading, setLoading] = useState(false);
  const [checkTLGiam, setCheckTLGiam] = useState(true);
  const [checkNguyenNhan, setCheckNguyenNhan] = useState(true);

  const [nhomNN, setNhomNN] = useState([]);
  const [itemHm, setItemHm] = useState([]);
  const [dsHangMuc, setDsHangMuc] = useState([]);
  const [titleModal, setTitleModal] = useState('');
  const [valueGhiChu, setValueGhiChu] = useState('');
  const [indexHm, setIndexHm] = useState(-1);
  const loaiTS = actionType === 'tai_san_khac' ? 'KHAC' : actionType === 'tslx_khau_hao' ? 'XE' : actionType === 'tslx_khau_tru' ? 'XE' : '';
  const [soTienGiamTru, setSoTienGiamTru] = useState('');
  const [ghiChuGiamTru, setGhiChuGiamTru] = useState('');

  let refModalInput = useRef(null);
  let refModalNguyenNhanGiamTru = useRef(null);

  const onPressLuu = async () => {
    setLoading(true);
    let hm = [];
    dsHangMuc.forEach((e) => {
      let json = {
        hang_muc: e.hang_muc,
        so_id_doi_tuong: e.so_id_doi_tuong || 0,
        pt_khau_hao: e.pt_khau_hao || 0,
        pt_bao_hiem: e.pt_bao_hiem || 0,
        pt_giam_tru: e.pt_giam_tru || 0,
        nguyen_nhan: e.nguyen_nhan || '',
        ghi_chu: e.ghi_chu || '',
        // --Nhap giam gia
        tl_giam_gia_vtu: e.tl_giam_gia_vtu || 0,
        tl_giam_gia_nhan_cong: e.tl_giam_gia_nhan_cong || 0,
        tl_giam_gia_khac: e.tl_giam_gia_khac || 0,
        lh_giam_gia: e.lh_giam_gia || '',
        lh_tt_giam_gia: e.lh_tt_giam_gia || '',
        // --Nhap khau tru
        tl_ktru_tien_bh: e.tl_ktru_tien_bh || 0,
        dkbs: e.dkbs || '',
        // --Nhap ti le thue
        tl_thue_vtu: e.tl_thue_vtu || 0,
        tl_thue_nhan_cong: e.tl_thue_nhan_cong || 0,
        tl_thue_khac: e.tl_thue_khac || 0,
      };
      hm.push(json);
    });
    try {
      const params = {
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        so_id: profileData?.ho_so?.so_id,
        so_id_doi_tuong: soIdDoiTuong || 0,
        lh_nv: lhnv,
        loai: 'PT_GIAM_TRU',
        hm,
        loai_ts: loaiTS,
        tien_giam_tru_bt: soTienGiamTru,
        ghi_chu_giam_tru: ghiChuGiamTru,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      setTimeout(() => {
        return NavigationUtil.pop();
      }, 500);
    } catch (error) {
      logErrorTryCatch(error);
      setLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 83');
    }
  };

  useEffect(() => {
    getData();
    setNhomNN(nhomNguyeNhan);
  }, []);

  const getData = async () => {
    setLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
        lh_nv: lhnv,
        loai: 'PT_GIAM_TRU',
        vu_tt: '',
        loai_ts: loaiTS,
        so_id_doi_tuong: soIdDoiTuong || 0,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DL_THEO_LOAI, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDsHangMuc(response.data_info.data);
      if (response.out_value.ghi_chu_giam_tru_bt) setGhiChuGiamTru(response.out_value.ghi_chu_giam_tru_bt);
      if (response.out_value.tien_giam_tru_bt) setSoTienGiamTru(response.out_value.tien_giam_tru_bt);
    } catch (error) {
      setLoading(false);
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message + '- line - 113');
    }
  };

  const onChangeInputValue = (it, idx, val) => {
    try {
      let replaceValue = val.replace(',', '.');
      const value = getValue(replaceValue);
      let listHangMucTmp = dsHangMuc;
      listHangMucTmp.map((item, index) => {
        if (checkTLGiam) {
          listHangMucTmp[index].pt_giam_tru = value;
        } else {
          listHangMucTmp[idx].pt_giam_tru = value;
        }
      });
      setDsHangMuc([...listHangMucTmp]);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };
  /* RENDER */

  const onChangeValueModal = (val, idx) => {
    try {
      let listHangMucTmp = dsHangMuc;
      listHangMucTmp.map((item, index) => {
        if (checkNguyenNhan) {
          listHangMucTmp[index].nguyen_nhan = val.join();
        } else {
          listHangMucTmp[indexHm].nguyen_nhan = val.join();
        }
      });
      setDsHangMuc([...listHangMucTmp]);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onOpenModal = (idx, label, item) => {
    try {
      setIndexHm(idx);
      setItemHm(item);
      setValueGhiChu(item.ghi_chu);
      if (label === 'nguyen_nhan') {
        refModalNguyenNhanGiamTru.current.show();
      }
      if (label === 'ghi_chu') {
        setTitleModal('Ghi chú');
        refModalInput.current.show();
      }
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeModalInputValue = (nd, title) => {
    try {
      let listHangMucTmp = dsHangMuc;
      listHangMucTmp.map(() => {
        listHangMucTmp[indexHm].ghi_chu = nd;
      });
      setDsHangMuc([...listHangMucTmp]);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };
  /* RENDER */

  const renderItemHangMuc = ({index, item}) => {
    return (
      <View style={[styles.inputRow, index > 0 && {borderTopWidth: 0}]}>
        <Text style={styles.txtTenHangMuc} children={item.ten} />
        <View style={styles.rowStyles}>
          <View style={styles.frame}>
            <TextInputOutlined
              maxLength={5}
              placeholder="0"
              keyboardType="decimal-pad"
              inputStyle={styles.inputStyle}
              containerStyle={[styles.inputContainer]}
              value={item?.pt_giam_tru?.toString()}
              onChangeText={(value) => onChangeInputValue(item, index, value)}
            />
          </View>
          <View style={styles.frame}>
            <TouchableOpacity style={styles.icBtn} onPress={() => onOpenModal(index, 'nguyen_nhan', item)}>
              <Icon.Feather name="file-text" size={18} color={item.nguyen_nhan === '' || item.nguyen_nhan === null ? colors.GRAY3 : colors.PRIMARY} />
            </TouchableOpacity>
          </View>
          <View style={styles.frame}>
            <TouchableOpacity style={styles.icBtn} onPress={() => onOpenModal(index, 'ghi_chu', item)}>
              <Icon.Feather name="file-text" size={18} color={item.ghi_chu === '' || item.ghi_chu === null ? colors.GRAY3 : colors.PRIMARY} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const renderRightHeaderComponent = () => {
    return (
      <View style={styles.headerBtnRight}>
        <Text children="Lưu" style={styles.txtBtnLuu} />
      </View>
    );
  };

  return (
    <CustomScreen
      dialogLoading={loading}
      rightComponent={renderRightHeaderComponent()}
      title="Giảm trừ bồi thường (chế tài...)"
      onPressRight={onPressLuu}
      renderView={
        <SafeAreaView flex={1}>
          <KeyboardAwareScrollView style={styles.content} refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}>
            <TextInputOutlined placeholder="0" keyboardType="numeric" onChangeText={setSoTienGiamTru} inputStyle={{textAlign: 'right'}} title="Số tiền giảm trừ" value={soTienGiamTru?.toString()} />
            <TextInputOutlined placeholder="Nhập ghi chú" onChangeText={setGhiChuGiamTru} title="Ghi chú giảm trừ" value={ghiChuGiamTru} multiline={true} />
            <View style={styles.tableTitleRow}>
              <View flex={1} justifyContent="center" alignItems="center">
                <Text style={styles.txtHangMuc} children="Hạng mục" />
              </View>
              <View style={[styles.rowStyles]}>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Tỷ lệ(%)" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkTLGiam} onValueChange={setCheckTLGiam} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="NN giảm" style={styles.txtGroup} />
                  <CheckboxComp checkboxStyle={isIOS && styles.checkboxStyle} value={checkNguyenNhan} onValueChange={setCheckNguyenNhan} />
                </View>
                <View style={[styles.frame, styles.checkboxRow]}>
                  <Text children="Ghi chú" style={styles.txtGroup} />
                </View>
              </View>
            </View>
            <FlatList
              data={dsHangMuc}
              scrollEnabled={false}
              renderItem={renderItemHangMuc}
              style={{paddingBottom: spacing.tiny}}
              keyExtractor={(item, index) => index.toString()}
              ListEmptyComponent={<Text style={styles.txtEmpty} children="Danh sách trống!" />}
              // refreshControl={<RefreshControl refreshing={loading} onRefresh={getData} />}
            />
          </KeyboardAwareScrollView>
          <ModalNguyenNhanGiamTru
            data={nhomNN}
            itemHm={itemHm}
            ref={refModalNguyenNhanGiamTru}
            setValue={(list) => onChangeValueModal(list)}
            onBackPress={() => refModalNguyenNhanGiamTru.current.hide()}
          />
          <ModalInput
            title={titleModal}
            value={valueGhiChu}
            ref={refModalInput}
            onBackPress={() => refModalInput.current.hide()}
            setValue={(v) => onChangeModalInputValue(v.value)}
            onPressLuu={(nd, tit) => onChangeModalInputValue(nd, tit)}
          />
        </SafeAreaView>
      }
    />
  );
};

const mapStateToProps = (state) => ({});

const mapDispatchToProps = {};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
    marginTop: 10,
    marginHorizontal: 10,
  },
  btnView: {
    paddingTop: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    marginBottom: !isIOS ? 10 : 0,
  },

  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },
  btnLuu: {
    marginHorizontal: 10,
  },
  inputStyle: {
    borderRadius: 0,
    width: dimensions.width / 8,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    paddingVertical: 8,
    borderRightWidth: 0,
    textAlign: 'center',
    paddingLeft: spacing.tiny,
    paddingRight: spacing.tiny,
    borderColor: colors.GRAY2,
  },
  containerInput: {
    margin: 0,
    paddingVertical: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
  },
  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  txtCheckAll: {
    marginRight: 10,
    marginVertical: 10,
    color: colors.BLACK_03,
  },
  errText: {
    marginBottom: 5,
    color: colors.RED1,
  },

  inputRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
  checkboxStyle: {
    width: 18,
    height: 18,
    margin: spacing.tiny,
  },
  txtTenHangMuc: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
    color: colors.PRIMARY,
    marginLeft: 2,
  },
  checkBox: {
    paddingHorizontal: 10,
  },
  checkAll: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: spacing.smaller,
  },
  checkboxRow: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowStyles: {
    flex: 1,
    flexDirection: 'row',
  },
  frame: {
    alignItems: 'center',
    flex: 1,
    borderLeftWidth: 0.5,
    borderColor: colors.GRAY,
  },
  tableTitleRow: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderBottomWidth: 0,
    borderColor: colors.GRAY,
    backgroundColor: 'rgba(51,	85,	180,0.2)',
  },
  txtHangMuc: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
    marginVertical: spacing.smaller,
  },
  txtBtnLuu: {
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
  headerBtnRight: {
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.GRAY2,
  },
  icBtn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtGroup: {
    fontSize: 12,
  },

  txtEmpty: {
    textAlign: 'center',
    marginVertical: 10,
  },
});

const TinhToanGiamTruScreenConnect = connect(mapStateToProps, mapDispatchToProps)(TinhToanGiamTruScreenComponent);
export const TinhToanGiamTruScreen = memo(TinhToanGiamTruScreenConnect, isEqual);
