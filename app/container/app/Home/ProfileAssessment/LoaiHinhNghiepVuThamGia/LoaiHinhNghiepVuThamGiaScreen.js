import {ScreenComponent, Text} from '@component';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, ScrollView, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import {connect} from 'react-redux';
import styles from './LoaiHinhNghiepVuThamGiaStyles';

const LoaiHinhNghiepVuThamGiaScreenComponent = (props) => {
  console.log('LoaiHinhNghiepVuThamGiaScreen');
  const {route} = props;
  const {loaiHinhNghiepVu} = route.params;

  /**RENDER  */
  const renderLoaiHinhNghiepVuItem = (data, extraData) => {
    let item = data.item;
    return (
      <View style={[styles.lhnvItemView]}>
        <View style={styles.lhvnRow}>
          <Text children="Sản phẩm" style={styles.lhnvTitle} />
          <Text children={item.ten} style={styles.lhnvValue} />
        </View>
        <View style={styles.lhvnRow}>
          <Text children="Tiền bảo hiểm" style={styles.lhnvTitle} />
          <NumericFormat value={item.tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvValue} />} />
        </View>
        <View style={styles.lhvnRow}>
          <Text children="Khấu trừ" style={styles.lhnvTitle} />
          <Text children={item.ktru == 'C' ? 'Có' : 'Không'} style={styles.lhnvValue} />
        </View>
        <View style={styles.lhvnRow}>
          <Text children="Mức khấu trừ" style={styles.lhnvTitle} />
          <NumericFormat value={item.mien_thuong || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvValue} />} />
        </View>
        <View style={styles.lhvnRow}>
          <Text children="Điều khoản bổ sung" style={styles.lhnvTitle} />
          <Text children={item.dkbs?.split(',').join(', ') || ''} style={styles.lhnvValue} />
        </View>
      </View>
    );
  };
  const renderLoaiHinhNghiepVu = () => <FlatList data={loaiHinhNghiepVu} renderItem={(item) => renderLoaiHinhNghiepVuItem(item, {loaiHinhNghiepVu: loaiHinhNghiepVu})} />;
  const renderTongTien = () => {
    let tongTienBaoHiem = 0,
      tongMienPhuong = 0;
    loaiHinhNghiepVu.map((item) => {
      tongTienBaoHiem += item.tien;
      tongMienPhuong += item.mien_thuong;
    });
    return (
      <View style={[styles.lhnvItemView, {borderBottomWidth: 0}]}>
        <View style={styles.lhvnRow}>
          <Text children="Tổng tiền bảo hiểm" style={styles.lhnvTongTienTitle} />
          <NumericFormat value={tongTienBaoHiem} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvTongTienValue} />} />
        </View>
        <View style={styles.lhvnRow}>
          <Text children="Tổng tiền khấu trừ/vụ" style={styles.lhnvTongTienTitle} />
          <NumericFormat value={tongMienPhuong} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvTongTienValue} />} />
        </View>
      </View>
    );
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle="Loại hình nghiệp vụ"
      renderView={
        <ScrollView style={styles.container}>
          {renderLoaiHinhNghiepVu()}
          {renderTongTien()}
        </ScrollView>
      }
    />
  );
};
const mapStateToProps = (state) => ({});

const mapDispatchToProps = {};

const LoaiHinhNghiepVuThamGiaScreenConnect = connect(mapStateToProps, mapDispatchToProps)(LoaiHinhNghiepVuThamGiaScreenComponent);
export const LoaiHinhNghiepVuThamGiaScreen = memo(LoaiHinhNghiepVuThamGiaScreenConnect, isEqual);
