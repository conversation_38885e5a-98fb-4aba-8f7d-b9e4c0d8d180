import {StyleSheet} from 'react-native';
import {colors} from '../../../../../commons/Theme';
export default StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 10,
  },
  lhvnRow: {
    flexDirection: 'row',
  },
  lhnvValue: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  lhnvTitle: {
    flex: 1,
    // textAlignVertical: 'center',
  },
  lhnvItemView: {
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    // borderBottomLeftRadius: 3,
    paddingHorizontal: 20,
  },
  lhnvTongTienValue: {
    // fontWeight: 'bold',
    fontSize: 16,
    flex: 1,
    textAlign: 'right',
    color: colors.PRIMARY,
  },
  lhnvTongTienTitle: {
    // fontWeight: 'bold',
    fontSize: 16,
    flex: 1,
    color: colors.PRIMARY,
  },
});
