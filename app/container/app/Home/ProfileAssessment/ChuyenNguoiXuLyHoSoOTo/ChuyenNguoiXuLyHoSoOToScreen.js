import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ModalChiNhanhTheoDangCay, ScreenComponent, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';
import styles from './ChuyenNguoiXuLyHoSoOToStyles';
import {ModalNguoiXuLy} from './Components';

const titleInput = ['Đơn vị xử lý', 'Người xử lý', 'Ghi chú'];

const ChuyenNguoiXuLyHoSoOToScreenComponent = ({route}) => {
  console.log('ChuyenNguoiXuLyHoSoOToScreenComponent');
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const {profileData} = route.params;

  const [dialogLoading, setDialogLoading] = useState(false);
  const [listMaDonViXuLySeleted, setListMaDonViXuLySelected] = useState([]); //list mã đơn vị xử lý được chọn
  const [openNguoiXuLy, setOpenNguoiXuLy] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);
  let refModalChiNhanhTheoDangCay = useRef(null);
  let refModalNguoiXuLy = useRef(null);

  useEffect(() => {
    initChiNhanhBaoHiemDangCay();
  }, []);

  useEffect(() => {
    if (listMaDonViXuLySeleted.length > 0) onChangeDonViXuLy('', '', listMaDonViXuLySeleted);
    else setListNguoiXuLy([]);
  }, [listMaDonViXuLySeleted]);

  const initChiNhanhBaoHiemDangCay = () => {
    // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
    let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay; //list chi nhánh mà nó quản lý
    chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
      return {
        ...item,
        listCon: [],
        isExpand: true,
        isCheck: false, //bỏ check or check
        hasChildCheck: false, //list chi nhánh cha, có child checked
        isShow: true,
      };
    });
    let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
    for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
      let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
      chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
    }
    refModalChiNhanhTheoDangCay.current.setData(chiNhanhBaoHiemCha);
  };

  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
    let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
    if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
    if (listConLai.length === 0) return [];
    else {
      for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
      return listConFilter;
    }
  };

  const getDefaultFormValue = () => {
    return {
      donViXuLy: [],
      nguoiXuLy: null,
      ghiChu: '',
    };
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const nguoiXuLy = watch('nguoiXuLy');

  const getTenHienThi = (value, data = []) => {
    let name = '';
    data.map((e) => {
      if (e.ma === value) name = e.ten;
    });
    return name;
  };

  const closeDropdown = () => {
    setOpenNguoiXuLy(false);
  };

  const onPressSave = async (data) => {
    setDialogLoading(true);
    try {
      let donViNguoiXuLy = listNguoiXuLy.find((item) => item.ma === data.nguoiXuLy);
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        nsd_moi: data.nguoiXuLy,
        ma_chi_nhanh_moi: donViNguoiXuLy ? donViNguoiXuLy.ma_chi_nhanh : '',
        ghi_chu: data.ghiChu,
        lan_gd: profileData.lan_gd.lan_gd,
        pm: 'BT',
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHUYEN_NGUOI_XU_LY_HO_SO_OTO, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển bồi thường viên thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  // Phải đủ cả biến title, items không là bị lỗi do hàm của Dropdown picker
  const onChangeDonViXuLy = async (title, items, itemValueSelected) => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      ma_chi_nhanh: itemValueSelected.join(','),
    };
    setDialogLoading(true);
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_GIAM_DINH_VIEN, params);
      if (openNguoiXuLy) setOpenNguoiXuLy(false);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let giamDinhVien = response.data_info.map((item) => {
        item.value = item.ma;
        item.label = item.ten + (item.ten_chuc_danh ? ` (${item.ten_chuc_danh})` : '');
        return item;
      });
      setListNguoiXuLy([...giamDinhVien]);
      setValue('nguoiXuLy', '', {shouldValidate: true});
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  /**RENDER  */
  const renderContent = () => {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView scrollEnabled={true}>
          <View style={styles.contentView}>
            <Controller
              control={control}
              name="donViXuLy"
              rules={{
                required: true,
              }}
              render={({field: {value, onChange}}) => (
                <TextInputOutlined
                  isTouchableOpacity
                  editable={false}
                  isDropdown
                  title="Đơn vị xử lý"
                  value={value.length === 0 ? 'Chọn đơn vị xử lý' : value.length === 1 ? value[0].ten_chi_nhanh : `Có ${value.length} đơn vị được chọn`}
                  error={errors.donViXuLy && getErrMessage('donViXuLy', errors.donViXuLy.type)}
                  placeholder={titleInput[0]}
                  onFocus={closeDropdown}
                  isRequired={true}
                  onPress={() => refModalChiNhanhTheoDangCay.current.show()}
                  inputStyle={{color: colors.BLACK}}
                />
              )}
            />

            <Controller
              control={control}
              name="nguoiXuLy"
              rules={{
                required: true,
              }}
              render={({field: {value, onChange}}) => (
                <TextInputOutlined
                  isRequired
                  isDropdown
                  cleared={value !== null && value !== ''}
                  isTouchableOpacity
                  editable={false}
                  title="Người xử lý"
                  placeholder="Chọn người xử lý"
                  inputStyle={{color: colors.BLACK}}
                  value={getTenHienThi(value, listNguoiXuLy)}
                  onPress={() => refModalNguoiXuLy.current.show()}
                  onPressClear={() => setValue('nguoiXuLy', '')}
                  error={errors.nguoiXuLy && getErrMessage('nguoiXuLy', errors.nguoiXuLy.type)}
                />
              )}
            />

            {/* lý do */}
            <Controller
              control={control}
              name="ghiChu"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined title={titleInput[2]} value={value} onChangeText={onChange} placeholder={titleInput[1]} onFocus={closeDropdown} multiline={true} numberOfLines={2} />
              )}
            />
          </View>
        </KeyboardAwareScrollView>
        <ModalChiNhanhTheoDangCay
          ref={refModalChiNhanhTheoDangCay}
          showCheckCha={true}
          multiple={false}
          setListMaDonViXuLySelected={setListMaDonViXuLySelected}
          setListItemDonViXulySelected={(value) => setValue('donViXuLy', value, {shouldValidate: true})}
        />
        <ModalNguoiXuLy
          value={nguoiXuLy}
          data={listNguoiXuLy}
          ref={refModalNguoiXuLy}
          onBackPress={() => refModalNguoiXuLy.current.hide()}
          setValue={(val) => setValue('nguoiXuLy', val.ma, {shouldValidate: true})}
        />
      </View>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Chuyển bồi thường viên"
      renderView={renderContent()}
      footer={<ButtonLinear title="Lưu" onPress={handleSubmit(onPressSave)} />}
    />
  );
};

export const ChuyenNguoiXuLyHoSoOToScreen = memo(ChuyenNguoiXuLyHoSoOToScreenComponent, isEqual);
