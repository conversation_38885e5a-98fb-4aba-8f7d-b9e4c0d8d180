import {colors} from '@app/commons/Theme';
import {ButtonLinear, ScreenComponent, TextInputOutlined} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import styles from './Styles';
import {REGUlAR_EXPRESSION} from '@app/commons/Constant';

function ThemSoDienThoaiChuXeScreenComponent(props) {
  console.log('ThemSoDienThoaiChuXeScreenComponent');
  const {route} = props;
  const {profileInfo} = route.params;
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      phone: profileInfo ? profileInfo.sdt_chu_xe : '',
    },
  });

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    if (inputName === 'phone') {
      if (errType === 'pattern') return 'Số điện thoại sai định dạng';
    }
    return '';
  };

  const luuSoDienThoai = async (data) => {
    try {
      setLoading(true);
      const params = {
        so_id: profileInfo.so_id,
        so_id_dt: profileInfo.so_id_dt,
        so_id_hd: profileInfo.so_id_hd,
        dien_thoai: data.phone,
        nv: profileInfo.nghiep_vu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.SUA_SO_DIEN_THOAI_LAI_XE, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật thành công!', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  /**RENDER  */

  return (
    <ScreenComponent
      dialogLoading={loading}
      headerBack
      headerTitle="Thêm số điện thoại chủ xe"
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView style={styles.content}>
            <Controller
              control={control}
              name="phone"
              rules={{
                pattern: REGUlAR_EXPRESSION.REG_PHONE,
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  blurOnSubmit={false}
                  onChangeText={onChange}
                  title="Số điện thoại"
                  keyboardType="phone-pad"
                  inputStyle={{color: colors.BLACK}}
                  containerStyle={[styles.inputView]}
                  value={value}
                  placeholder="Nhập số điện thoại"
                  error={errors.phone && getErrMessage('phone', errors.phone.type)}
                />
              )}
            />
          </KeyboardAwareScrollView>
          <View style={styles.footerView}>
            <ButtonLinear onPress={handleSubmit(luuSoDienThoai)} title="Lưu" />
          </View>
        </View>
      }
    />
  );
}

export const ThemSoDienThoaiChuXeScreen = memo(ThemSoDienThoaiChuXeScreenComponent, isEqual);
