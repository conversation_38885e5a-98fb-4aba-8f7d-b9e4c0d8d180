import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, TextInputOutlined} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';
import styles from './PhanCongGiamDinhStyles';

const PhanCongGiamDinhScreenComponent = ({route}) => {
  console.log('PhanCongGiamDinhScreen');
  const userInfo = useSelector(selectUser);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [profileData, setProfileData] = useState(route.params.profileData);
  const [donViXuLySelected, setDonViXuLySelected] = useState('');
  const [openNguoiXuLy, setOpenNguoiXuLy] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);

  const {
    control,
    handleSubmit,
    setValue,
    formState: {errors},
  } = useForm({
    defaultValues: {
      lyDo: '',
      nguoiXuLy: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    setProfileData(route.params.profileData);
    setDonViXuLySelected(userInfo.nguoi_dung.ma_chi_nhanh);
    layGDVPhanCong(userInfo.nguoi_dung.ma_chi_nhanh);
  }, [route.params]);

  const closeDropdown = (title) => {
    setOpenNguoiXuLy(false);
  };

  const onPressSave = async (data) => {
    let params = {
      ma_doi_tac: profileData.ho_so.ma_doi_tac,
      so_id: profileData.ho_so.so_id,
      nsd_moi: data.nguoiXuLy,
      ma_chi_nhanh_moi: donViXuLySelected,
      ly_do: data.lyDo,
      hanh_dong: 'PHAN_CONG_GDVHT',
      lan_gd: profileData.lan_gd.lan_gd,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHUYEN_GDV_HIEN_TRUONG, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Phân công giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layGDVPhanCong = async (maChiNhanh) => {
    try {
      setDialogLoading(true);
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        ma_chi_nhanh: maChiNhanh,
        tinh_thanh: profileData.dien_bien[0]?.tinh_thanh,
        quan_huyen: profileData.dien_bien[0]?.quan_huyen,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.DANH_SACH_CAN_BO_PHAN_CONG_GIAM_DINH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let giamDinhVien = response.data_info.map((item) => {
        item.value = item.nsd;
        item.label = item.ten_can_bo;
        return item;
      });
      setListNguoiXuLy([...giamDinhVien]);
      setValue('nguoiXuLy', '');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };
  /**RENDER  */
  const renderForm = () => {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView contentContainerStyle={styles.scrollView}>
          <View style={styles.contentView}>
            <Controller
              control={control}
              name="nguoiXuLy"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  title="Người xử lý"
                  zIndex={7000}
                  isOpen={openNguoiXuLy}
                  setOpen={setOpenNguoiXuLy}
                  items={listNguoiXuLy}
                  itemSelected={value}
                  placeholder="Chọn người xử lý"
                  isRequired={true}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  inputErr={errors.nguoiXuLy && getErrMessage('nguoiXuLy', errors.nguoiXuLy.type)}
                />
              )}
            />

            {/* lý do */}
            <Controller
              control={control}
              name="lyDo"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  title="Lý do"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Nhập lý do"
                  onFocus={closeDropdown}
                  multiline={true}
                  isRequired={true}
                  error={errors.lyDo && getErrMessage('lyDo', errors.lyDo.type)}
                />
              )}
            />
          </View>
        </KeyboardAwareScrollView>
      </View>
    );
  };
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Phân công giám định"
      renderView={renderForm()}
      footer={<ButtonLinear title="Lưu" onPress={handleSubmit(onPressSave)} />}
      //
    />
  );
};

export const PhanCongGiamDinhScreen = memo(PhanCongGiamDinhScreenComponent, isEqual);
