import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, dimensions, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {ItemHangMuc, ModalNhapGia, ModalShowNote} from '../Components';
import NavigationUtil from '@app/navigation/NavigationUtil';
// import {NumericFormat} from 'react-number-format';

const NhapTinhToanNvTaiSanScreenComponent = (props) => {
  console.log('NhapTinhToanNvTaiSanScreenComponent');
  const {route, navigation} = props;
  const {profileData, lhnvSelected} = route?.params;
  const [dialogLoading, setDialogLoading] = useState(false);
  const [listHangMuc, setListHangMuc] = useState([]);
  const [listDoiTuong, setListDoiTuong] = useState([]);
  const [note, setNote] = useState('');
  const [item, setItem] = useState({});
  const [index, setIndex] = useState(-1);
  const [isChangedData, setIsChangedData] = useState(false);

  let refModalShowNote = useRef(null);
  let refModalNhapGia = useRef(null);
  // const showBtnHuy = garaBaoGia?.ngay_dong_bg !== null && +moment(garaBaoGia?.ngay_dong_bg, 'DDMMYYYY').format('YYYYMMDD') < 30000101;

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDsDoiTuongTonThat();
    });
  }, []);

  const getDsDoiTuongTonThat = async () => {
    setDialogLoading(true);
    let params = {
      so_id: profileData?.ho_so?.so_id,
      lh_nv: lhnvSelected?.ma,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_DOI_TUONG_TON_THAT_NV_KHAC, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;

      let rsData = response.data_info;
      rsData.map((e, i) => {
        rsData[i].isChecked = false;
        rsData[0].isChecked = true;
      });
      setListDoiTuong([...rsData]);
      getChiTietDoiTuongTonThat(response.data_info[0]);
      return;
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 42');
    }
  };

  const handleLuu = async (type) => {
    try {
      let data = [];
      listHangMuc.map((e) => {
        let json = {
          vu_tt: e.vu_tt,
          ma_chi_phi: e.ma_chi_phi,
          ten_chi_phi: e.ten_chi_phi,
          so_luong: e.so_luong,
          dvi_tinh: e.dvi_tinh,
          muc_do: e.muc_do,
          thay_the_sc: e.thay_the_sc,
          chinh_hang: e.chinh_hang,
          thu_hoi: e.thu_hoi,
          tien_vtu: e.tien_vtu,
          tien_nhan_cong: e.tien_nhan_cong,
          tien_khac: e.tien_khac,
          tien_bgia_vtu: e.tien_bgia_vtu,
          tien_bgia_nhan_cong: e.tien_bgia_nhan_cong,
          tien_bgia_khac: e.tien_bgia_khac,
          tien_thoa_thuan: e.tien_thoa_thuan,
          ghi_chu: e.ghi_chu,
          nguyen_nhan: '',
        };
        data.push(json);
      });
      let params = {
        so_id: listHangMuc[0].so_id || '',
        lh_nv: listHangMuc[0].lh_nv || '',
        so_id_doi_tuong: listHangMuc[0].so_id_doi_tuong,
        hang_muc: listHangMuc[0].hang_muc,
        pm: 'BT',
        data: data,
      };
      setDialogLoading(true);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TINH_TOAN_TNDS_VE_TAI_SAN, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      else FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu chi tiết tính toán thành công!', 'success');
      setIsChangedData(false);
      getDsDoiTuongTonThat();
      if (type === 'act_back') {
        return setTimeout(() => {
          NavigationUtil.pop();
        }, 300);
      }
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getChiTietDoiTuongTonThat = async (selectedItem) => {
    setDialogLoading(true);
    try {
      const params = {
        lh_nv: selectedItem?.lh_nv,
        ma_doi_tac: selectedItem?.ma_doi_tac,
        so_id: selectedItem?.so_id,
        so_id_doi_tuong: selectedItem?.so_id_doi_tuong,
        hang_muc: selectedItem?.hang_muc,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DOI_TUONG_TON_THAT, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;

      setListHangMuc(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 99');
    }
  };

  const onModalUpdateData = (item, index) => {
    let listHangMucTmp = listHangMuc;
    listHangMucTmp[index] = item;
    setListHangMuc([...listHangMucTmp]);
    setIsChangedData(true);
  };

  const onOpenModalNhapGia = (item, index) => {
    // if (showBtnHuy) return Alert.alert('Thông báo', 'Không sửa/xoá hồ sơ đã kết thúc báo giá');
    refModalNhapGia.current.show();
    setItem(item);
    setIndex(index);
  };

  const onPressDoiTuong = (item, index) => {
    let newArr = listDoiTuong;
    newArr.map((e, i) => {
      newArr[i].isChecked = false;
      newArr[index].isChecked = true;
    });
    getChiTietDoiTuongTonThat(item);
    setListHangMuc([...newArr]);
  };

  const handleBackPress = () => {
    if (isChangedData) {
      Alert.alert('Thông báo', 'Bạn có muốn lưu thay đổi hay không?', [
        {
          text: 'Không',
          style: 'destructive',
          onPress: () =>
            setTimeout(() => {
              NavigationUtil.pop();
            }, 300),
        },
        {
          text: 'Có',
          onPress: () => {
            handleLuu('act_back');
          },
        },
      ]);
    } else NavigationUtil.pop();
  };

  /* RENDER */

  const renderHeaderItem = ({item, index}) => {
    return (
      <TouchableOpacity style={styles.listHeaderButton} onPress={() => onPressDoiTuong(item, index)}>
        <Text font="semi_bold16" style={styles.getLabelStyles(item.isChecked)}>
          {item.ten}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Nhập tính toán"
      onPressBack={() => handleBackPress()}
      renderView={
        <SafeAreaView style={styles.container}>
          <View>
            <FlatList horizontal data={listDoiTuong} renderItem={renderHeaderItem} keyExtractor={(_, i) => i.toString()} />
          </View>
          <FlatList
            data={listHangMuc}
            keyExtractor={(_, i) => i.toString()}
            ListEmptyComponent={<Empty description="Danh sách Hạng mục trống" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
            renderItem={(data) => (
              <ItemHangMuc
                data={data}
                setNote={setNote}
                // showBtnHuy={showBtnHuy}
                listHangMuc={listHangMuc}
                onPressEditPrice={onOpenModalNhapGia}
                setIsVisibleModal={() => refModalShowNote.current.show()}
                // onPressRemoveHangMuc={onPressRemoveHangMuc}
              />
            )}
            // ListFooterComponent={
            //   <View flexDirection="row" alignItems="center" marginLeft={8} marginTop={10} marginBottom={spacing.massive + 10}>
            //     <View style={[styles.totalCol, {width: dimensions.width / 4}]}>
            //       <Text style={styles.txtGaraName} children="Tổng cộng" />
            //     </View>
            //     <View style={styles.totalCol}>
            //       <NumericFormat value={tongTienGaraBaoGia} displayType={'text'} thousandSeparator={true} renderText={value => <Text children={value} style={[styles.txtValue]} />} />
            //     </View>
            //     <View style={[styles.totalCol, {width: dimensions.width / 3 + 10}]}>
            //       <NumericFormat value={tongTienDuyet} displayType={'text'} thousandSeparator={true} renderText={value => <Text children={value} style={[styles.txtValue, {marginLeft: 10}]} />} />
            //     </View>
            //   </View>
            // }
            showsVerticalScrollIndicator={false}
          />

          <ModalShowNote title="Ghi chú" ref={refModalShowNote} detail={note} onBackPress={() => refModalShowNote.current.hide(false)} />
          <ModalNhapGia updateData={onModalUpdateData} ref={refModalNhapGia} doiTuongSelected={item} index={index} onBackPress={() => refModalNhapGia.current.hide()} />
        </SafeAreaView>
      }
      footer={<ButtonLinear loading={false} title="Lưu" onPress={handleLuu} />}
    />
  );
};

export const NhapTinhToanNvTaiSanScreen = memo(NhapTinhToanNvTaiSanScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  btnView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },
  inputStyle: {
    textAlign: 'right',
    color: colors.BLACK_03,
  },
  title: {
    fontWeight: 'bold',
    textAlign: 'right',
    fontSize: FontSize.size16,
    marginVertical: vScale(10),
  },
  content: {
    flex: 1,
    paddingTop: vScale(10),
    marginHorizontal: scale(spacing.small),
  },
  inputView: {
    marginBottom: vScale(spacing.small),
    flex: 1,
  },
  rowView: {
    flexDirection: 'row',
  },
  label: {
    fontWeight: 'bold',
    fontSize: FontSize.size14,
  },
  value: {
    color: colors.PRIMARY,
  },
  txtPlaceholder: {
    textDecorationLine: 'underline',
  },
  listHeaderButton: {
    padding: spacing.smaller,
    marginVertical: spacing.tiny,
  },
  getLabelStyles: (value) => {
    return {
      color: colors.RED1,
      textDecorationLine: value ? 'underline' : 'none',
      fontSize: value ? FontSize.size15 : FontSize.size14,
    };
  },
});
