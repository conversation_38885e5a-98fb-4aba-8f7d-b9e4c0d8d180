import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, FontSize, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalBangTLThuongTat, ModalChonMucDoTonThat, ModalDonViTinh} from '../Components';
import {getPercentageValue} from '@app/utils/string';

const NhapTinhToanNvNguoiScreenComponent = (props) => {
  console.log('NhapTinhToanNvNguoiScreenComponent');
  const {route, navigation} = props;
  const {lhnvSelected, profileData, selectedItem, dataNV} = route?.params;
  const [mucDoTonThat, setMucDoTonThat] = useState({});
  const [tyLeThuongTat, setTyLeThuongTat] = useState({});
  const [dvTinhSelected, setDvTinhSelected] = useState('');
  const [mucDoSelected, setMucDoSelected] = useState('');
  const [dataTLTT, setDataTLTT] = useState([]);
  const [TLTTRoot, setTLTTRoot] = useState([]);
  const [isSubmiting, setSubmiting] = useState(false);

  let refModalChonMucDoTT = useRef(null);
  let refModalDVTinh = useRef(null);
  let refModalBangTLThuongTat = useRef(null);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: {
      diaChi: '',
      mucDoTonThat: '',
      tienTonThat: '',
      tienThoaThuan: '',
      tienDeXuat: '',
      ghiChu: '',
      cmnd: '',
      ptGiamTru: '',
      ptTrachNhiem: '',
    },
    mode: 'onChange',
  });

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const onOpenModalChonMucDo = () => {
    refModalChonMucDoTT.current.show();
  };

  const onSubmit = async (data) => {
    setSubmiting(true);
    const params = {
      lh_nv: lhnvSelected?.ma,
      so_id: profileData?.ho_so?.so_id,
      vu_tt: selectedItem?.vu_tt,
      hang_muc: selectedItem?.hang_muc,
      so_id_doi_tuong: selectedItem?.so_id_doi_tuong,
      thuong_tat: tyLeThuongTat?.id,
      muc_do: mucDoTonThat.ma || data.mucDoTonThat,
      cmnd: data.cmnd,
      dia_chi: data.diaChi,
      ghi_chu: data.ghiChu,
      tien_tt: data.tienTonThat,
      tien_dx_pa: data.tienDeXuat,
      tien_thoa_thuan: data.tienThoaThuan,
      pt_bao_hiem: data.ptTrachNhiem,
      pt_giam_tru: data.ptGiamTru,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.NHAP_TT_DXGIA_NV_KHAC, params);
      setSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      NavigationUtil.pop();
      FlashMessageHelper.showFlashMessage('Thông báo', 'Cập nhật tính toán thành công', 'success');
      return;
    } catch (error) {
      setSubmiting(false);
      Alert.alert('Thông báo', error.message + '- line - 99');
    }
  };

  const getChiTietDoiTuongTonThat = async (data) => {
    setSubmiting(true);
    const params = {
      lh_nv: selectedItem?.lh_nv,
      so_id: selectedItem?.so_id,
      so_id_doi_tuong: selectedItem?.so_id_doi_tuong,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_CHI_TIET_DOI_TUONG_TON_THAT, params);
      setSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDefaultValue(response.data_info);
    } catch (error) {
      setSubmiting(false);
      Alert.alert('Thông báo', error.message + '- line - 99');
    }
  };

  const getDsTyLeThuongTat = async (data) => {
    const params = {
      ma_doi_tac: profileData?.ho_so?.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_TL_THUONG_TAT, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let listTyLeThuongTatRoot = response.data_info;
      if (listTyLeThuongTatRoot.length === 0) return;
      let listSort = [];
      listTyLeThuongTatRoot = listTyLeThuongTatRoot.map((item) => {
        if (item.id === selectedItem.thuong_tat) {
          setTyLeThuongTat(item);
        }
        return {
          ...item,
          arrId: item.id.split('.'),
          arrParentId: item.parent.split('.'),
          level: item.id.split('.').length - 1,
          listCon: [],
          isExpand: false,
          isCheck: false,
          isShow: true,
        };
      });
      listTyLeThuongTatRoot = listTyLeThuongTatRoot.sort((a, b) => a.level - b.level); //sort theo level
      //tách thành mảng các level 1->n
      let maxLevel = listTyLeThuongTatRoot[listTyLeThuongTatRoot.length - 1].level; //level cuối
      let listByLevel = [];
      for (let i = 1; i < maxLevel + 1; i++) {
        let filterByLevel = listTyLeThuongTatRoot.filter((item) => item.level === i);
        listByLevel.push(filterByLevel);
      }
      //duyệt từ thằng cha (cuối cùng duyệt lên)
      for (let i = listByLevel.length - 1; i > 0; i--) {
        let listCha = listByLevel[i - 1];
        let listCon = listByLevel[i];
        for (let j = 0; j < listCha.length; j++) {
          let filterCon = listCon.filter((item) => item.parent === listCha[j].id); //filter tìm ra thằng con theo thằng cha
          filterCon = filterCon.sort((a, b) => +a.arrId[a.arrId.length - 1] - +b.arrId[b.arrId.length - 1]); //sort con theo thứ tự tăng dần
          listByLevel[i - 1][j].listCon = filterCon; //gán lại listCon
        }
      }
      listByLevel[0] = listByLevel[0].sort((a, b) => +a.arrId[a.arrId.length - 1] - +b.arrId[b.arrId.length - 1]); //sort lại các level root
      listSort = listByLevel[0]; //kết quả cuối cùng
      setDataTLTT(listSort);
      setTLTTRoot(listTyLeThuongTatRoot);
      return;
    } catch (error) {
      Alert.alert('Thông báo', error.message + '- line - 99');
    }
  };

  useEffect(() => {
    navigation.addListener('focus', () => {
      initData();
    });

    // getChiTietDanhGiaThuongTat();
    setDvTinhSelected(selectedItem?.dvi_tinh);
    setMucDoSelected(selectedItem?.muc_do);
  }, []);

  const initData = async () => {
    await getChiTietDoiTuongTonThat();
    getDsTyLeThuongTat();
  };
  // const getTyLeThuongTatConByCha = (listRoot, cha, con) => {
  //   // if(con.)
  // };

  const setDefaultValue = (resData) => {
    if (resData) {
      setValue('cmnd', resData.cmnd);
      setValue('diaChi', resData.dia_chi);
      setValue('ghiChu', resData.ghi_chu);
      setValue('tienDeXuat', resData.tien_dx_pa);
      setValue('tienTonThat', resData.tien_tt);
      setValue('tienThoaThuan', resData.tien_thoa_thuan);
      setValue('mucDoTonThat', resData.muc_do);
      setValue('ptTrachNhiem', resData.pt_bao_hiem);
      setValue('ptGiamTru', resData.pt_giam_tru);
    }
  };

  const onChangeValueInput = (value) => {
    setValue('donViTinh', value.ma);
    setValue('donViTinhTen', value.ten);
    setDvTinhSelected(value.ma);
  };

  const onChangeValueMucDo = (val) => {
    setMucDoSelected(val.ma);
    setMucDoTonThat(val);
  };

  const onChangeValueTLTT = (val) => {
    setTyLeThuongTat(val);
  };

  const onPressTLTT = () => {
    // refModalBangTLThuongTat.current.show();
    NavigationUtil.push(SCREEN_ROUTER_APP.THONG_TIN_MUC_DO_THUONG_TAT, {profileData: profileData, selectedItem: selectedItem, dataTLTT: dataTLTT, TLTTRoot: TLTTRoot});
  };

  /* RENDER */

  // Footer
  const renderFooter = () => {
    return (
      <View style={styles.btnView}>
        <ButtonLinear title="Lưu" linearStyle={styles.footerBtn} onPress={handleSubmit(onSubmit)} />
        {/* <ButtonLinear onPress={() => {}} isSubmiting={false} title="Lưu báo giá" linearStyle={styles.footerBtn} /> */}
      </View>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={isSubmiting}
      headerBack
      headerTitle="Nhập tính toán"
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView showsVerticalScrollIndicator={false} style={styles.content}>
            <Text style={styles.label}>
              Tên đối tượng: <Text style={styles.value}>{selectedItem?.ten}</Text>
            </Text>

            {/* <View marginTop={12} marginBottom={5}>
              <View style={styles.rowView}>
                <Text style={styles.label} children="Mức độ tổn thất: " />
                <TouchableOpacity onPress={() => onOpenModalChonMucDo()} style={{flex: 1}}>
                  {mucDoTonThat.ten ? (
                    <Text style={[styles.value, styles.txtPlaceholder]} children={mucDoTonThat.ten} />
                  ) : selectedItem?.muc_do_ten ? (
                    <Text style={[styles.value, styles.txtPlaceholder]} children={selectedItem?.muc_do_ten} />
                  ) : (
                    <Text style={styles.txtPlaceholder} children="Chọn mức độ" />
                  )}
                </TouchableOpacity>
              </View>
            </View> */}
            <View marginTop={12} marginBottom={5}>
              <View style={styles.rowView}>
                <Text style={styles.label} children="Tỷ lệ thương tật: " />
                <TouchableOpacity onPress={() => onPressTLTT()} style={{flex: 1}}>
                  {selectedItem?.dgia_tt > 0 ? (
                    <Text style={[styles.value, styles.txtPlaceholder]} children={`Đã đánh giá (${selectedItem?.dgia_tt})`} />
                  ) : (
                    <Text style={[styles.value, styles.txtPlaceholder, {color: colors.RED1}]} children="Chưa đánh giá" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
            <Controller
              control={control}
              rules={{
                required: false,
              }}
              name="cmnd"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  placeholder="Nhập số CCCD"
                  title="CMT/CCCD"
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  containerStyle={styles.inputView}
                  // error={errors.cmnd && getErrMessage('cmnd', errors.cmnd.type)}
                />
              )}
            />
            <View style={styles.rowView}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="tienTonThat"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    editable={false}
                    disabled
                    isRequired
                    value={value}
                    placeholder="0"
                    blurOnSubmit={false}
                    title="Tiền tổn thất"
                    keyboardType="numeric"
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    inputStyle={styles.inputStyle}
                    containerStyle={[styles.inputView, {marginRight: 10}]}
                    error={errors.tienTonThat && getErrMessage('tienTonThat', errors.tienTonThat.type)}
                  />
                )}
              />
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="ptTrachNhiem"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={getPercentageValue(value)}
                    placeholder="0"
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    keyboardType="numeric"
                    onChangeText={onChange}
                    title="% Trách nhiệm"
                    maxLength={3}
                    inputStyle={styles.inputStyle}
                    containerStyle={styles.inputView}
                    error={errors.ptTrachNhiem && getErrMessage('ptTrachNhiem', errors.ptTrachNhiem.type)}
                  />
                )}
              />
            </View>

            <View style={styles.rowView}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="ptGiamTru"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={getPercentageValue(value)}
                    maxLength={3}
                    placeholder="0"
                    title="% Giảm trừ"
                    blurOnSubmit={false}
                    keyboardType="numeric"
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    inputStyle={styles.inputStyle}
                    containerStyle={[styles.inputView, {marginRight: 10}]}
                    error={errors.ptGiamTru && getErrMessage('ptGiamTru', errors.ptGiamTru.type)}
                  />
                )}
              />
              <Controller
                control={control}
                rules={{
                  required: false,
                }}
                name="tienThoaThuan"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    editable={false}
                    disabled
                    value={value}
                    placeholder="0"
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    keyboardType="numeric"
                    onChangeText={onChange}
                    title="Tiền thoả thuận"
                    inputStyle={styles.inputStyle}
                    containerStyle={styles.inputView}
                    error={errors.tienThoaThuan && getErrMessage('tienThoaThuan', errors.tienThoaThuan.type)}
                  />
                )}
              />
            </View>
            <View style={styles.rowView}>
              {/* <Controller
                control={control}
                rules={{
                  required: false,
                }}
                name="tienThoaThuan"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    editable={false}
                    disabled
                    value={value}
                    placeholder="0"
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    keyboardType="numeric"
                    onChangeText={onChange}
                    title="Tiền thoả thuận"
                    inputStyle={styles.inputStyle}
                    containerStyle={styles.inputView}
                    error={errors.tienThoaThuan && getErrMessage('tienThoaThuan', errors.tienThoaThuan.type)}
                  />
                )}
              /> */}
              {/* <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="tienDeXuat"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    placeholder="0"
                    title="Tiền đề xuất"
                    blurOnSubmit={false}
                    keyboardType="numeric"
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    inputStyle={styles.inputStyle}
                    containerStyle={[styles.inputView, {marginLeft: 10}]}
                    error={errors.tienDeXuat && getErrMessage('tienDeXuat', errors.tienDeXuat.type)}
                  />
                )}
              /> */}
            </View>

            <Controller
              control={control}
              rules={{
                required: false,
              }}
              name="nguyenNhanGiamTru"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  multiline
                  value={value}
                  title="Nguyên nhân giảm trừ"
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  placeholder="Nhập nguyên nhân"
                  containerStyle={[styles.inputView]}
                />
              )}
            />
            <Controller
              control={control}
              rules={{
                required: false,
              }}
              name="ghiChu"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  multiline
                  value={value}
                  title="Ghi chú"
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  placeholder="Nhập ghi chú"
                  containerStyle={[styles.inputView]}
                />
              )}
            />
          </KeyboardAwareScrollView>
          {renderFooter()}
          {/* <ModalChonMucDoTonThat
            data={dataNV?.muc_do.filter((item) => item.nhom === 'NG')}
            ref={refModalChonMucDoTT}
            mucDoSelected={mucDoSelected}
            setValue={(value) => onChangeValueMucDo(value)}
            onBackPress={() => refModalChonMucDoTT.current.hide()}
          /> */}
          <ModalDonViTinh
            ref={refModalDVTinh}
            data={dataNV?.dvi_tinh}
            donViTinhSelected={dvTinhSelected}
            setValue={(val) => onChangeValueInput(val)}
            onBackPress={() => refModalDVTinh.current.hide()}
          />
          <ModalBangTLThuongTat setValue={(val) => onChangeValueTLTT(val)} arrData={dataTLTT} ref={refModalBangTLThuongTat} onBackPress={() => refModalBangTLThuongTat.current.hide()} />
        </SafeAreaView>
      }
    />
  );
};

export const NhapTinhToanNvNguoiScreen = memo(NhapTinhToanNvNguoiScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  btnView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    paddingHorizontal: scale(spacing.small),
  },

  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },

  inputStyle: {
    textAlign: 'right',
    color: colors.BLACK_03,
  },
  title: {
    fontWeight: 'bold',
    textAlign: 'right',
    fontSize: FontSize.size16,
    marginVertical: vScale(10),
  },
  content: {
    flex: 1,
    paddingTop: vScale(10),
    marginHorizontal: scale(spacing.small),
  },
  inputView: {
    marginBottom: vScale(spacing.small),
    flex: 1,
  },
  rowView: {
    flexDirection: 'row',
  },
  label: {
    fontWeight: 'bold',
  },
  value: {
    color: colors.PRIMARY,
  },
  txtPlaceholder: {
    textDecorationLine: 'underline',
  },
  noteInput: {
    height: 80,
    textAlignVertical: 'top',
  },
});
