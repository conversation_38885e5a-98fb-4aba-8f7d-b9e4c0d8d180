import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {FlatList, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalDonViTinhComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, setValue, data, donViTinhSelected} = props;
  const [isSelectedItemIndex, setIsSelectedItemIndex] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const [arrDVTinh, setArrDVTinh] = useState([]);

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setIsSelectedItemIndex(index);
  };

  const initModalData = () => {
    setArrDVTinh(data);
    setIsSelectedItemIndex(data.findIndex((item) => item.ma === donViTinhSelected));
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Đơn vị tính" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    const isCheck = isSelectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
        <Text style={{color: isCheck ? colors.PRIMARY : colors.BLACK_03, marginTop: Platform.OS == 'android' ? 4 : 2}}>{item.ten}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return <FlatList data={arrDVTinh} renderItem={renderItem} keyExtractor={(item, index) => index.toString()} />;
  };
  return (
    <Modal
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onModalShow={initModalData}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        <View margin={10}>{renderContent()}</View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height / 3,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: 16,
    borderColor: colors.GRAY,
    height: 40,
    margin: 16,
    borderRadius: 25,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    padding: 5,
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
});

export const ModalDonViTinh = ModalDonViTinhComponent;
