import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, Text, TextInputOutlined} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalInputComponent = forwardRef(({field, onBackPress, setValue, value}, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  const [text, setText] = useState('');

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const onPressLuu = () => {
    setValue && setValue(text, field);
    onBackPress && onBackPress();
  };

  /* RENDER */

  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Nhập nội dung" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down']}
      onModalShow={() => setText(value)}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}
      style={styles.modal}
      avoidKeyboard={true}>
      <View style={styles.modalContent}>
        {renderHeader()}
        <View flex={1} marginHorizontal={10} marginTop={10}>
          {field === 'Ghi chú' || field === 'Địa chỉ' ? (
            <TextInputOutlined
              multiline
              value={text}
              title={field}
              onChangeText={setText}
              placeholder="Nhập nội dung"
              containerStyle={[styles.inputContainer]}
              inputStyle={{height: 80, textAlignVertical: 'top'}}
            />
          ) : (
            <TextInputOutlined
              value={text}
              title={field}
              keyboardType="numeric"
              onChangeText={setText}
              placeholder="Nhập tiền"
              inputStyle={{textAlign: 'right'}}
              containerStyle={[styles.inputContainer]}
            />
          )}
        </View>
        <View style={styles.btnView}>
          <ButtonLinear
            title="Đóng"
            linearStyle={styles.btnLuu}
            onPress={() => {
              setIsVisible(false);
            }}
            linearColors={[colors.GRAY, colors.GRAY]}
            textStyle={{color: colors.BLACK_03}}
          />
          <ButtonLinear title={'Lưu'} linearStyle={[styles.btnLuu, {marginLeft: spacing.smaller}]} onPress={onPressLuu} />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    margin: 0,
  },
  btnView: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 30,
    left: 10,
    right: 10,
  },
  modalContent: {
    height: dimensions.height * 0.4,
    backgroundColor: '#FFF',
    // borderWidth: 1,
    width: dimensions.width,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    // paddingHorizontal: spacing.small,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    width: dimensions.width / 5,
    height: 7,
    // borderWidth: 1,
    backgroundColor: colors.GRAY,
    borderRadius: 20,
  },
  btnLuu: {
    // marginTop: spacing.smaller,
  },
  inputContainer: {
    flex: 1,
    marginBottom: 0,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
    // paddingBottom :
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.RED1,
    marginVertical: 10,
  },
  errText: {
    color: colors.RED1,
    marginBottom: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
});
export const ModalInput = memo(ModalInputComponent, isEqual);
