import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {Icon, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {scale, spacing, vScale} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';

const ItemBaoGiaComponent = forwardRef((props, ref) => {
  const {data, onPressEditItem, profileData} = props;
  const {item, index} = data;

  useImperativeHandle(ref, () => ({
    // show: () => setIsVisible(true),
  }));

  /* RENDER */

  const renderLabel = (title, value) => {
    return (
      <View style={styles.rowData}>
        <Text children={title} style={styles.txtLabel} />
        <NumericFormat value={value || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
      </View>
    );
  };
  return (
    <View style={styles.container}>
      <View style={styles.garaDetailView}>
        <View flex={1}>
          <View style={styles.rowData}>
            <Text font="semi_bold16" children={item.ten_pa} style={{color: colors.RED1}} />
          </View>
          {/* {renderLabel('Tiền vật tư:', item.tien_vtu)}
          {renderLabel('Tiền nhân công:', item.tien_nhan_cong)}
          {renderLabel('Tiền khác:', item.tien_khac_dx_pa)} */}
        </View>
        {/* <View>
          <TouchableOpacity style={{marginBottom: spacing.smaller}} onPress={() => onPressEditGara(item, index)}>
            <Icon.FontAwesome name="edit" color={colors.PRIMARY} size={20} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onPressRemoveGara(item, index)}>
            <Icon.AntDesign name="closesquareo" color={colors.RED1} size={20} />
          </TouchableOpacity>
        </View> */}
      </View>
      <View style={styles.alignSelf} flexDirection="row" justifyContent="space-between">
        <TouchableOpacity style={styles.themHangMucView} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.LPA_TINH_TOAN_BT, {garaBaoGia: item, indexGara: index, profileData: profileData})}>
          <Text font="semi_bold16" children={'Tính toán bồi thường'} style={styles.txtThemHangMuc} />
          <Icon.AntDesign name="doubleright" size={15} color={colors.PRIMARY} />
        </TouchableOpacity>
        <TouchableOpacity style={[styles.themHangMucView, {justifyContent: 'flex-end'}]} onPress={() => onPressEditItem(item, index)}>
          <Text font="semi_bold16" children="Nhập tính toán" style={styles.txtThemHangMuc} />
          <Icon.AntDesign name="doubleright" size={15} color={colors.PRIMARY} />
        </TouchableOpacity>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: scale(spacing.smaller),
    paddingVertical: vScale(spacing.smaller),
    marginBottom: vScale(spacing.smaller),
    backgroundColor: '#FFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  themHangMucView: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: vScale(spacing.smaller),
  },
  txtThemHangMuc: {
    color: colors.PRIMARY,
    paddingRight: scale(spacing.smaller),
  },
  garaDetailView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // flex: 1,
  },
  rowData: {
    flex: 2,
    flexDirection: 'row',
    paddingVertical: vScale(spacing.tiny),
  },
  txtLabel: {
    flex: 1,
    color: colors.LABEL_GRAY1,
  },
  txtValue: {
    flex: 1,
    textAlign: 'right',
  },
  txtGaraName: {
    flex: 1,
    fontSize: 16,
    color: colors.PRIMARY,
  },
  titleRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bottomBtnRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  alignSelf: {
    alignSelf: 'flex-end',
  },
});
export const ItemBaoGia = memo(ItemBaoGiaComponent, isEqual);
