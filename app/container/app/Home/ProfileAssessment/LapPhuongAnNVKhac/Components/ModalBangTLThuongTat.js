import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import Empty from '@app/components/Empty';
import HeaderModal from '@app/components/HeaderModal';
import {dimensions, spacing} from '@app/theme';
import {CheckboxComp, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Platform, SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalBangTLThuongTatComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, arrData, setValue} = props;
  const [searchInput, setSearchInput] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [data, setData] = useState([]);
  const [dataRoot, setDataRoot] = useState([]);
  const [selectedItem, setSelectedItem] = useState({});

  useEffect(() => {
    // getData(searchInput);
    // return () => {
    //   setDataMaBenh([]);
    // };
  }, []);

  const initModalData = () => {
    setData(arrData);
    setDataRoot(arrData);
  };
  useEffect(() => {
    // getData(searchInput, 1, 40);
  }, [searchInput]);

  const onPressSearch = () => {
    setRefreshing(true);
    let arrTextSearch = searchInput.trim().split(' ');
    arrTextSearch = arrTextSearch.filter((item) => item !== '');
    let result = filterByTitle(dataRoot, arrTextSearch);
    // console.log(result);
    setData([...result]);
  };

  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 200);

  const filterByTitle = (data, arrTextSearch) => {
    try {
      data = data.map((item) => {
        let arrTenItem = item.text.split(' ');
        let tonTai = 0; //nếu tonTai == (arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
        for (let j = 0; j < arrTextSearch.length; j++) {
          for (let k = 0; k < arrTenItem.length; k++) {
            /*
              j + 1 != tonTai : để loại trường hợp chuỗi tên hạng mục có 2 từ giống nhau
              ví dụ :
              tên hạng mục : tôi là tôi
              từ cần tìm : tôi là
              -> khi duyệt từ 'tôi' ở từ cần tìm -> 'tôi' sẽ được tính 2 lần ->  dẫn đến sai kết quả của biến tonTai
              //có cách khác là remove duplicate ở cả 2 mảng arrTenHangMuc và arrTextSearch r tìm -> sẽ không bị dính trường hợp trên =))
              */
            if (arrTenItem[k].toUpperCase() === arrTextSearch[j].toUpperCase() && j + 1 !== tonTai) {
              tonTai = tonTai + 1;
              break;
            }
          }
        }
        if (tonTai !== arrTextSearch.length) item.isShow = false;
        else item.isShow = true;
        if (item.listCon.length > 0) {
          item.listCon = filterByTitle(item.listCon, arrTextSearch);
          item.listCon = item.listCon.filter((itemListCon) => itemListCon.isShow === true); //chỉ lấy những thằng true, thằng false thì ẩn hết
          if (item.listCon.length > 0) {
            item.isShow = true; //nếu còn list con -> cho show
            item.isExpand = true; //nếu còn list con -> cho expand
          }
        }
        return item;
      });

      return data;
    } catch (error) {
      console.log('filterByTitle error', error);
    }
  };

  const onRefresh = () => {
    console.log('xx');
    setRefreshing(false);
    // getData(searchInput, 1);
  };

  const onSave = () => {
    setValue && setValue(selectedItem);
    onBackPress && onBackPress();
  };

  const onPressTitle = (item, index, listData) => {
    // console.log('onPressTitle', item);
    //xử lý theo kiểu thay string :D
    try {
      let strDataRoot = JSON.stringify(data); //string list Tỷ lệ thương tật
      let strListDataBanDau = JSON.stringify(listData); //string listData lúc chưa đổi Trạng thái
      //thay đổi data
      listData.map((e, i) => {
        if (i === index) {
          e.isExpand = !e.isExpand;
          return e;
        }
        return e;
      });
      let strListDataLucSau = JSON.stringify(listData); //string listData press sau khi thay đổi Trạng thái
      strDataRoot = strDataRoot.replace(strListDataBanDau, strListDataLucSau); //thay đổi string listData
      setData(JSON.parse(strDataRoot)); //set lại DATA
    } catch (error) {
      console.log('error', error);
    }
  };
  const checkAllFalse = (data, itemPressed) => {
    try {
      data = data.map((item) => {
        if (item.id !== itemPressed.id) item.isCheck = false;
        if (item.listCon.length > 0) item.listCon = checkAllFalse(item.listCon, itemPressed);
        return item;
      });
      return data;
    } catch (error) {
      console.log('checkAllFalse error', error);
    }
  };
  const onChangeCheckboxValue = (value, item, index, listData) => {
    //xử lý theo kiểu thay string :D
    try {
      let strDataRoot = JSON.stringify(data); //string list Tỷ lệ thương tật
      let strListDataBanDau = JSON.stringify(listData); //string listData lúc chưa đổi Trạng thái
      //cập nhật giá trị check
      listData.map((e, i) => {
        if (i === index) {
          e.isCheck = !value;
          return e;
        }
        return e;
      });
      let strListDataLucSau = JSON.stringify(listData); //string listData press sau khi thay đổi Trạng thái

      strDataRoot = strDataRoot.replace(strListDataBanDau, strListDataLucSau); //thay đổi string listData
      let objData = JSON.parse(strDataRoot);
      objData = checkAllFalse(objData, item); //check all item còn lại
      setData(objData); //set lại DATA
      setSelectedItem(item);
    } catch (error) {
      console.log('error', error);
    }
  };
  /*RENDER */
  const renderTitleSearch = (text) => {
    let arrTextSearch = searchInput.split(' ').map((item) => item.toUpperCase());
    return (
      <>
        <Text style={{flex: 1, textAlign: 'justify'}}>
          {text.split(' ').map((item) => {
            let itemUppercase = item.toUpperCase();
            if (arrTextSearch.includes(itemUppercase))
              return (
                <>
                  <Text children={item} style={{backgroundColor: colors.ORANGE1}} />
                  <Text children=" " />
                </>
              );
            return <Text children={item + ' '} />;
          })}
        </Text>
      </>
    );
  };
  const renderItem = ({item, index}, expandData) => {
    if (!item.isShow) return;
    let tyLe = '';
    tyLe = item.pt_tu === item.pt_toi ? item.pt_toi : `${item.pt_tu} - ${item.pt_toi}`;
    return (
      <View>
        {item.listCon.length > 0 && (
          <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressTitle(item, index, expandData)}>
            <Icon.Feather name={!item.isExpand ? 'chevron-right' : 'chevron-down'} size={20} color={colors.BLACK} style={styles.iconBtnTopRightView} />
            <View style={styles.txtView}>
              <Text style={styles.txtTitle}>{item.text}</Text>
            </View>
          </TouchableOpacity>
        )}
        {item.listCon.length === 0 && (
          <TouchableOpacity style={styles.itemHangMucView} onPress={() => onChangeCheckboxValue(item.isCheck, item, index, expandData)}>
            <CheckboxComp
              disabled
              value={item.isCheck}
              checkboxStyle={styles.checkbox}
              // onValueChange={(value) => onChangeCheckboxValue(value, item, index, expandData)}
            />
            {searchInput === '' ? (
              <View style={styles.txtView}>
                <Text style={{flex: 1, textAlign: 'justify', fontSize: 13}}>{`${item.text} (${tyLe} %)`}</Text>
              </View>
            ) : (
              renderTitleSearch(item.text)
            )}
          </TouchableOpacity>
        )}
        {item.listCon.length > 0 && item.isExpand && <View style={{marginLeft: spacing.small}}>{renderListTyLeThuongTat(item.listCon)}</View>}
      </View>
    );
  };

  const renderListTyLeThuongTat = (listData) => {
    return (
      <FlatList
        data={listData}
        // extraData={data}
        renderItem={(itemData) => renderItem(itemData, listData)}
        style={styles.flStyles}
        onEndReachedThreshold={0.3}
        keyExtractor={(item) => item.id}
        // refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
        scrollEnabled={false}
      />
    );
  };
  const renderContent = () => {
    return <>{<ScrollView style={{paddingRight: spacing.small}}>{renderListTyLeThuongTat(data)}</ScrollView>}</>;
  };
  return (
    <Modal onModalWillShow={initModalData} style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal
          onPressRight={onSave}
          onBackPress={onBackPress}
          centerComponent={<SearchBar placeholder="Tìm tỉ lệ thương tật" onSubmitEditing={onPressSearch} onTextChange={debounced} onPressSearch={onPressSearch} />}
          rightComponent={<Text style={styles.txtBtnSave}>Chọn</Text>}
        />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});

export const ModalBangTLThuongTat = memo(ModalBangTLThuongTatComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 5,
    paddingLeft: 20,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  itemHangMucView: {
    paddingVertical: spacing.tiny,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginTop: spacing.tiny,
    marginRight: isIOS ? spacing.smaller : spacing.medium,
    width: 15,
    height: 15,
  },
  txtView: {
    flex: 1,
    marginTop: Platform.OS === 'android' ? 4 : 2,
    flexDirection: 'row',
  },
  flStyles: {
    paddingLeft: spacing.small,
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  txtTitle: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'justify',
  },
  txtValue: {
    flex: 1,
    fontSize: 12,
    textAlign: 'justify',
    color: colors.BLACK_03,
  },
});
