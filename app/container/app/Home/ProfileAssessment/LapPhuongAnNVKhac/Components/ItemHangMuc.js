import {colors} from '@app/commons/Theme';
import {Icon, Text} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';

const ItemHangMucComponent = forwardRef((props, ref) => {
  const {data, setNote, setIsVisibleModal, onPressEditPrice} = props;
  const {item, index} = data;
  useImperativeHandle(ref, () => ({
    // show: () => setIsVisible(true),
  }));

  const onPressShowNote = () => {
    setNote && setNote(item.ghi_chu);
    setIsVisibleModal(true);
  };

  // const onPressEdit = () => {
  //   if (showBtnHuy) return Alert.alert('Thông báo', 'Không sửa/xo<PERSON> hồ sơ đã kết thúc báo giá');
  //   NavigationUtil.push(SCREEN_ROUTER_APP.THEM_HANG_MUC_BAO_GIA, {hangMucBaoGia: item, index: index, type: 'EDIT'});
  // };

  /* RENDER */

  const renderLabel = (title) => {
    return (
      <View style={[styles.frame]}>
        <Text children={title} style={styles.txtLabel} />
      </View>
    );
  };
  const renderPrice = (value = 0) => {
    return (
      <View style={[styles.frame]}>
        <NumericFormat value={value || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtValue} />} />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.txtGaraName}>
        {index + 1 + '. '}
        {item?.ten_chi_phi}
        <Text style={styles.subText} children={' - '} />
        <Text style={styles.subText} children={item.muc_do_ten + '/'} />
        <Text style={styles.subText} children={item.so_luong} />
      </Text>
      <View style={styles.garaDetailView}>
        <View flex={1}>
          <View style={[styles.rowStyles, {backgroundColor: 'rgba(51,	85,	180,0.2)'}]}>
            <View style={[styles.frame]} />
            <View style={[styles.frame]}>
              <Text children="Tiền báo giá" style={styles.txtGroup} />
            </View>
            <View style={[styles.frame]}>
              <Text children="Đề xuất duyệt" style={styles.txtGroup} />
            </View>
          </View>

          <TouchableOpacity onPress={() => onPressEditPrice(item, index)} style={[styles.rowStyles, {borderTopWidth: 0}]}>
            {renderLabel('Tiền vật tư')}
            {renderPrice(item.tien_bgia_vtu)}
            {renderPrice(item.tien_vtu)}
          </TouchableOpacity>

          <TouchableOpacity onPress={() => onPressEditPrice(item, index)} style={[styles.rowStyles, {borderTopWidth: 0}]}>
            {renderLabel('Nhân công')}
            {renderPrice(item.tien_bgia_nhan_cong)}
            {renderPrice(item.tien_nhan_cong)}
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onPressEditPrice(item, index)} style={[styles.rowStyles, {borderTopWidth: 0}]}>
            {renderLabel('Tiền sơn')}
            {renderPrice(item.tien_bgia_khac)}
            {renderPrice(item.tien_khac)}
          </TouchableOpacity>

          <View style={styles.bottomRow}>
            <TouchableOpacity style={{flexDirection: 'row'}} onPress={() => onPressShowNote(item)}>
              <Text children={'Ghi chú: '} />
              <Icon.Feather name="file-text" size={18} color={item.ghi_chu === '' || item.ghi_chu === null ? colors.GRAY3 : colors.PRIMARY} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      {/* <View style={{alignSelf: 'flex-end'}}>
        <TouchableOpacity style={styles.themHangMucView} onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.BAO_GIA_HANG_MUC)}>
          <Text children="Chi tiết báo giá" style={styles.txtThemHangMuc} />
          <Icon.AntDesign name="doubleright" size={15} color={colors.PRIMARY} />
        </TouchableOpacity>
      </View> */}
    </View>
  );
});

export const ItemHangMuc = memo(ItemHangMucComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 2,
    backgroundColor: '#FFF',
  },

  themHangMucView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: spacing.smaller,
    // borderWidth: 1,
  },
  txtThemHangMuc: {
    color: colors.PRIMARY,
    fontWeight: '700',
    paddingRight: spacing.smaller,
    fontSize: 16,
  },
  garaDetailView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    // flex: 1,
  },
  rowData: {
    flexDirection: 'row',
  },
  txtLabel: {
    flex: 1,
    paddingVertical: spacing.tiny,
    textAlign: 'left',
    marginLeft: spacing.tiny,
  },
  txtValue: {
    flex: 1,
    textAlign: 'right',
    paddingVertical: spacing.tiny,
    marginRight: spacing.smaller,
  },
  txtGaraName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingBottom: spacing.smaller,
    marginHorizontal: 10,
    marginTop: 16,
  },
  txtGroup: {
    color: colors.PRIMARY,
    paddingVertical: spacing.tiny,
    textAlign: 'center',
  },
  subText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.BLACK_03,
    fontStyle: 'italic',
  },
  rowStyles: {
    borderWidth: 0.5,
    flexDirection: 'row',
    borderColor: colors.GRAY,
  },
  frame: {
    flex: 1,
    borderRightWidth: 0.5,
    borderColor: colors.GRAY,
  },
  bottomRow: {
    paddingLeft: 4,
    borderWidth: 0.5,
    paddingRight: 10,
    borderTopWidth: 0,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
  },
});
