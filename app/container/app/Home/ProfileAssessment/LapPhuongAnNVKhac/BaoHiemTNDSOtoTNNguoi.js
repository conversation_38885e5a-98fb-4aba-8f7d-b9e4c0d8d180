import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {Empty, Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';

const BaoHiemTNDSOtoTNNguoiScreenComponent = (props) => {
  console.log('BaoHiemTNDSOtoTNNguoiScreenComponent');
  const {route, navigation} = props;
  const {lhnvSelected, profileData, dataNV} = route?.params;

  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getDsDoiTuongTonThat();
    });
  }, []);

  const getDsDoiTuongTonThat = async () => {
    setIsLoading(true);
    let params = {
      so_id: profileData?.ho_so?.so_id,
      lh_nv: lhnvSelected?.ma,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_DOI_TUONG_TON_THAT_NV_KHAC, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setData(response.data_info);
      return;
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 42');
    }
  };

  const onPressEditItem = (it) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.BH_TNDS_NHAP_TINH_TOAN_NV_NGUOI, {selectedItem: it, profileData: profileData, dataNV: dataNV, lhnvSelected: lhnvSelected});
  };

  /** RENDER */

  const renderPriceView = (label, value) => {
    return (
      <View flex={1}>
        <Text style={styles.label}>{label}</Text>
        <NumericFormat value={value} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.value}>{value}</Text>} />
      </View>
    );
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity style={styles.itemView} onPress={() => onPressEditItem(item)}>
        <View flex={1}>
          <View style={[styles.rowView]} marginTop={8}>
            <Text style={styles.label} children="Tên đối tượng: " />
            <Text style={[styles.value, {color: colors.PRIMARY}]} children={item.ten} />
          </View>
          <Text style={styles.label}>
            Tỷ lệ thương tật: <Text style={styles.value}>{item?.ds_thuong_tat}</Text>
          </Text>

          <View style={styles.rowView}>
            {renderPriceView('Tiền tổn thất', item.tien_tt)}
            {renderPriceView('Tiền thoả thuận', item.tien_thoa_thuan)}
          </View>
        </View>
        <View marginLeft={10}>
          <Icon.Feather name="chevron-right" size={20} color={colors.PRIMARY} />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={isLoading}
      headerBack
      headerTitle={lhnvSelected?.ten || ''}
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            data={data}
            renderItem={renderItem}
            style={styles.flStyles}
            removeClippedSubviews={true}
            keyExtractor={(item, index) => index.toString()}
            ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
            refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => getDsDoiTuongTonThat()} />}
          />
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  itemView: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    marginVertical: spacing.tiny,
    paddingLeft: spacing.smaller,
    backgroundColor: colors.WHITE1,
    marginHorizontal: spacing.small,
  },
  rowView: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: spacing.tiny,
    justifyContent: 'space-between',
  },
  label: {
    lineHeight: 20,
    color: colors.LABEL_GRAY1,
  },
  value: {
    flex: 1,
    lineHeight: 20,
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  footerView: {
    flex: 1,
    width: dimensions.width,
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: spacing.small,
    // marginBottom: Platform.OS === 'ios' ? getStatusBarHeight() - 10 : 0,
  },
  btnRemove: {
    top: 4,
    right: 4,
    position: 'absolute',
  },

  flStyles: {
    marginTop: 10,
    marginBottom: 40,
  },
});
export const BaoHiemTNDSOtoTNNguoiScreen = memo(BaoHiemTNDSOtoTNNguoiScreenComponent, isEqual);
