import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {NumericFormat} from 'react-number-format';
import {ModalShowNote} from './Components';
import styles from './ThongTinMucDoThuongTatStyles';

const ThongTinMucDoTonThatScreenComponent = (props) => {
  console.log('ThongTinMucDoTonThatScreenComponent');
  const {navigation, route} = props;
  const {profileData, selectedItem, dataTLTT, TLTTRoot} = route?.params;

  const [refreshing, setRefreshing] = useState(false);
  const [data, setData] = useState([]);
  const [soTienBaoHiem, setSoTienBaoHiem] = useState(0);
  const [note, setNote] = useState('');

  let refModalShowNote = useRef();

  useEffect(() => {
    navigation.addListener('focus', () => {
      getData();
    });
  }, []);

  const getData = async () => {
    setRefreshing(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id || '',
        pm: 'BT',
        vu_tt: selectedItem?.vu_tt || '',
        lh_nv: selectedItem?.lh_nv || '',
        hang_muc: selectedItem?.hang_muc || '',
        so_id_doi_tuong: selectedItem?.so_id_doi_tuong || '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.DS_TY_LE_THUONG_TAT_CUA_DOI_TUONG, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let rsData = response.data_info;
      rsData.map((item) => {
        item.muc_toi_thieu = (item.pttt_tu / 100) * item.so_tien_bh;
        item.muc_toi_da = (item.pttt_toi / 100) * item.so_tien_bh;
        return item;
      });
      setData(response.data_info);

      setSoTienBaoHiem(response.out_value.so_tien_bh);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message + '- line - 99');
    }
  };

  const handleRemoveItem = (item) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn xoá mức độ thương tật này không?', [
      {text: 'Để sau', style: 'destructive'},
      {
        text: 'Đồng ý',
        onPress: async () => {
          setRefreshing(true);
          let params = {
            so_id: item?.so_id || '',
            pm: 'BT',
            bt: item?.bt || '',
            vu_tt: item?.vu_tt || '',
            lh_nv: item?.lh_nv || '',
            hang_muc: item?.hang_muc || '',
            so_id_doi_tuong: item?.so_id_doi_tuong || '',
            ma_thuong_tat: item?.ma_thuong_tat || '',
          };
          try {
            setRefreshing(false);
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_MUC_DO_THUONG_TAT, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá mức độ thương tật thành công', 'success');
            getData();
            return;
          } catch (error) {
            setRefreshing(false);
            Alert.alert('Thông báo', error.message + '- line - 99');
          }
        },
      },
    ]);
  };

  const onRefresh = () => {
    getData();
  };

  const onPressItem = (it) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.THEM_MUC_DO_THUONG_TAT, {profileData: profileData, itemEditSelected: it, selectedItem: selectedItem, dataTLTT: dataTLTT, TLTTRoot: TLTTRoot, soTienBaoHiem});
  };

  const onPressShowNote = (item) => {
    setNote(item.item.ghi_chu);
    refModalShowNote.current.show();
  };

  // footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <ButtonLinear
          title="Thêm mức độ thương tật"
          onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_MUC_DO_THUONG_TAT, {profileData: profileData, dataTLTT: dataTLTT, selectedItem: selectedItem, soTienBaoHiem})}
        />
      </View>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={refreshing}
      headerBack
      headerTitle="Thông tin mức độ thương tật"
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            data={data}
            ListFooterComponent={<View marginBottom={60} />}
            keyExtractor={(item, index) => index.toString()}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
            renderItem={(item) => (
              <RenderItem data={data} items={item} onPressItem={(it) => onPressItem(it)} onPressTrash={(it) => handleRemoveItem(it)} onPressShowNote={() => onPressShowNote(item)} />
            )}
          />
          <ModalShowNote ref={refModalShowNote} detail={note} onBackPress={() => refModalShowNote.current.hide()} />
        </SafeAreaView>
      }
      footer={renderFooter()}
    />
  );
};

const RenderItem = (props) => {
  const {items, onPressTrash, onPressItem, onPressShowNote} = props;
  const item = items.item;

  const content = (label, value, style) => {
    return (
      <View flex={1}>
        <Text numberOfLines={2} style={[styles.txtLabel]}>
          {label}
          {label === 'Ghi chú: ' ? (
            <TouchableOpacity onPress={onPressShowNote}>
              <Icon.Feather name="file-text" size={18} color={value === '' || value === null ? colors.GRAY3 : colors.PRIMARY} />
            </TouchableOpacity>
          ) : (
            <Text style={[styles.detail, style]}>{value}</Text>
          )}
        </Text>
      </View>
    );
  };

  const renderViewPrice = (label, value, style) => {
    return (
      <View flexDirection="row">
        <Text style={styles.txtLabel}>{label}</Text>
        <NumericFormat value={value} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={[styles.detail, style]}>{val}</Text>} />
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={() => onPressItem(item)}>
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <View flexDirection="row">
            {content('Mức thương tật: ', item.ten_thuong_tat, styles.txtStyles)}
            <TouchableOpacity onPress={() => onPressTrash(item)}>
              <Icon.AntDesign name="closesquareo" color={colors.RED1} size={24} />
            </TouchableOpacity>
          </View>
          <View style={styles.rowStyles}>
            {renderViewPrice('Mức tối thiểu: ', item.muc_toi_thieu.toFixed(), styles.txtPrice)}
            {renderViewPrice('Mức tối đa: ', item.muc_toi_da.toFixed(), styles.txtPrice)}
          </View>
          {content('Tỷ lệ thương tật: ', item.pttt + ' %')}
          {renderViewPrice('Mức trách nhiệm: ', item.tien_ht, styles.txtPrice)}
          {content('Ghi chú: ', item.ghi_chu)}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export const ThongTinMucDoTonThatScreen = memo(ThongTinMucDoTonThatScreenComponent, isEqual);
