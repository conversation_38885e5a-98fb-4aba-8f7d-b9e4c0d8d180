import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalBangTLThuongTat} from '../../Components';
import {getPercentageValue} from '@app/utils/string';

const ThemMucDoThuongTatScreenComponent = ({route}) => {
  console.log('ThemMucDoThuongTatScreenComponent');
  const {profileData, selectedItem, dataTLTT, itemEditSelected, TLTTRoot, soTienBaoHiem} = route?.params;

  const [mucDoThuongTat, setMucDoThuongTat] = useState(null);
  const [errField, setErrField] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  let refModalBangTLThuongTat = useRef(null);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: {
      tienTonThat: itemEditSelected?.tien_ht || '',
      ghiChu: itemEditSelected?.ghi_chu || '',
      tyLeThuongTat: itemEditSelected?.pttt || '',
      mucToiThieu: itemEditSelected?.muc_toi_thieu || '',
      mucToiDa: itemEditSelected?.muc_toi_da || '',
    },
    mode: 'onChange',
  });

  const getErrMessage = (inputName, errType) => {
    if (inputName === 'tienTonThat') {
      if (errType === 'required') return 'Thông tin bắt buộc';
    } else if (inputName === 'tyLeThuongTat') {
      if (errType === 'required') return 'Thông tin bắt buộc';
    }
    return '';
  };

  useEffect(() => {
    if (TLTTRoot?.length > 0) {
      TLTTRoot.map((e) => {
        if (e.id === itemEditSelected?.ma_thuong_tat) {
          setMucDoThuongTat(e);
        }
      });
    }
  }, []);

  useEffect(() => {
    if (mucDoThuongTat) {
      let mucToiThieu = +(mucDoThuongTat.pt_tu / 100) * +soTienBaoHiem;
      let mucToiDa = +(mucDoThuongTat.pt_toi / 100) * +soTienBaoHiem;
      setValue('mucToiThieu', mucToiThieu.toFixed());
      setValue('mucToiDa', mucToiDa.toFixed());
    }
  }, [mucDoThuongTat]);

  const onSubmit = async (data) => {
    if (!mucDoThuongTat) return setErrField(true);
    setIsLoading(true);
    try {
      const params = {
        pm: 'BT',
        bt: selectedItem?.bt,
        lh_nv: selectedItem?.lh_nv,
        vu_tt: selectedItem?.vu_tt,
        so_id: profileData?.ho_so?.so_id,
        hang_muc: selectedItem?.hang_muc,
        ma_thuong_tat: mucDoThuongTat?.id || itemEditSelected?.ma_thuong_tat,
        so_id_doi_tuong: selectedItem?.so_id_doi_tuong,
        ghi_chu: data.ghiChu,
        tien: data?.tienTonThat,
        pttt: data?.tyLeThuongTat,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_TT_THUONG_TAT_CUA_DPO_TUONG, params);
      setIsLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      NavigationUtil.pop();
      if (!itemEditSelected) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm mức độ thương tật thành công', 'success');
      } else FlashMessageHelper.showFlashMessage('Thông báo', 'Sửa mức độ thương tật thành công', 'success');
      return;
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Thông báo', error.message + '- line - 99');
    }
  };

  const onChangeValueTLTT = (val) => {
    setMucDoThuongTat(val);
    setErrField(false);
  };

  const onPressShowBangTyLeThuongTat = () => {
    refModalBangTLThuongTat.current.show();
  };

  /* RENDER */

  return (
    <ScreenComponent
      headerBack
      headerTitle="Thêm mức độ thương tật"
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView style={styles.content}>
            <View marginTop={12} marginBottom={5}>
              <View style={styles.rowView}>
                <Text style={styles.label} children="Mức độ thương tật: " />
                <TouchableOpacity onPress={() => onPressShowBangTyLeThuongTat()} style={{flex: 1}}>
                  {mucDoThuongTat?.text ? (
                    <Text numberOfLines={2} style={[styles.value, styles.txtPlaceholder]}>
                      {mucDoThuongTat.text}
                      <Text>
                        {' '}
                        ({mucDoThuongTat.pt_tu} - {mucDoThuongTat.pt_toi}%)
                      </Text>
                    </Text>
                  ) : (
                    <Text style={[styles.txtPlaceholder, errField && {color: colors.RED1}]} children="Chọn tỷ lệ" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.rowView}>
              <Controller
                control={control}
                name="mucToiThieu"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    disabled
                    editable={false}
                    value={value}
                    placeholder="0"
                    title="Mức tối thiểu"
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    keyboardType="numeric"
                    onChangeText={onChange}
                    inputStyle={styles.inputStyle}
                    containerStyle={styles.inputView}
                  />
                )}
              />
              <Controller
                control={control}
                name="mucToiDa"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    disabled
                    editable={false}
                    value={value}
                    placeholder="0"
                    blurOnSubmit={false}
                    title="Mức tối đa"
                    keyboardType="numeric"
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    inputStyle={styles.inputStyle}
                    containerStyle={[styles.inputView, {marginLeft: 10}]}
                  />
                )}
              />
            </View>

            <View style={styles.rowView}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="tyLeThuongTat"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={getPercentageValue(value)}
                    isRequired
                    maxLength={3}
                    placeholder="0"
                    title="% Tỷ lệ"
                    blurOnSubmit={false}
                    returnKeyType={'next'}
                    keyboardType="numeric"
                    onChangeText={onChange}
                    inputStyle={styles.inputStyle}
                    containerStyle={styles.inputView}
                    error={errors.tyLeThuongTat && getErrMessage('tyLeThuongTat', errors.tyLeThuongTat.type)}
                  />
                )}
              />
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="tienTonThat"
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    placeholder="0"
                    blurOnSubmit={false}
                    title="Mức trách nhiệm"
                    keyboardType="numeric"
                    returnKeyType={'next'}
                    onChangeText={onChange}
                    inputStyle={styles.inputStyle}
                    containerStyle={[styles.inputView, {marginLeft: 10}]}
                    error={errors.tienTonThat && getErrMessage('tienTonThat', errors.tienTonThat.type)}
                  />
                )}
              />
            </View>

            <Controller
              control={control}
              rules={{
                required: false,
              }}
              name="ghiChu"
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  multiline
                  value={value}
                  title="Ghi chú"
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  placeholder="Nhập ghi chú"
                  containerStyle={styles.inputView}
                />
              )}
            />
          </KeyboardAwareScrollView>

          <ModalBangTLThuongTat setValue={(val) => onChangeValueTLTT(val)} arrData={dataTLTT} ref={refModalBangTLThuongTat} onBackPress={() => refModalBangTLThuongTat.current.hide()} />
        </SafeAreaView>
      }
      footer={<ButtonLinear loading={isLoading} title="Lưu" onPress={handleSubmit(onSubmit)} />}
    />
  );
};

export const ThemMucDoThuongTatScreen = memo(ThemMucDoThuongTatScreenComponent, isEqual);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  btnView: {
    paddingVertical: vScale(10),
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    paddingHorizontal: spacing.small,
  },

  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },

  inputStyle: {
    textAlign: 'right',
    color: colors.BLACK_03,
  },

  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    textAlign: 'right',
  },

  content: {
    flex: 1,
    marginTop: 10,
    marginHorizontal: spacing.small,
  },

  inputView: {
    marginBottom: spacing.small,
    flex: 1,
  },
  rowView: {
    flexDirection: 'row',
  },
  label: {
    fontWeight: 'bold',
  },
  value: {
    color: colors.PRIMARY,
  },
  txtPlaceholder: {
    textDecorationLine: 'underline',
  },
  noteInput: {
    height: 80,
    textAlignVertical: 'top',
  },
});
