import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {Empty, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import {ItemBaoGia} from './Components';

const BaoHiemTNDSTaiSanScreenComponent = (props) => {
  console.log('BaoHiemTNDSTaiSanScreenComponent');
  const {route, navigation} = props;
  const {lhnvSelected, profileData, dataNV} = route?.params;

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    navigation.addListener('focus', () => {
      // getDsDoiTuongTonThat();
      layDanhSachPhuongAn();
    });
  }, []);

  const layDanhSachPhuongAn = async () => {
    setLoading(true);
    try {
      let params = {
        so_id: profileData.ho_so.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_PHUONG_AN_TNDS_VE_TAI_SAN, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setData(response.data_info);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressEditItem = (it, idx) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.BH_TNDS_NHAP_TINH_TOAN, {selectedItem: it, profileData: profileData, dataNV: dataNV, lhnvSelected: lhnvSelected});
  };

  /** RENDER */

  return (
    <ScreenComponent
      dialogLoading={loading}
      headerBack
      headerTitle={lhnvSelected?.ten || ''}
      renderView={
        <SafeAreaView style={styles.container}>
          <FlatList
            data={data}
            keyExtractor={(_, index) => index.toString()}
            ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
            renderItem={(data) => (
              <ItemBaoGia
                data={data}
                profileData={profileData}
                // indexSelected={indexSelected}
                // setIndexSelected={setIndexSelected}
                // onPressRemoveGara={(item) => onPressRemoveGara(item)}
                // onPressEditGara={(item, index) => onPressEditGara(item, index)}
                onPressEditItem={onPressEditItem}
              />
            )}
            refreshControl={<RefreshControl refreshing={false} onRefresh={() => layDanhSachPhuongAn()} />}
          />
          {/* <FlatList
            data={data}
            style={styles.flStyles}
            removeClippedSubviews={true}
            renderItem={renderItemTaiSan}
            keyExtractor={(item, index) => index.toString()}
            ListEmptyComponent={<Empty imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
            refreshControl={<RefreshControl refreshing={loading} onRefresh={() => getDsDoiTuongTonThat()} />}
          /> */}
        </SafeAreaView>
      }
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  itemView: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY,
    marginVertical: spacing.tiny,
    paddingLeft: spacing.smaller,
    backgroundColor: colors.WHITE1,
    marginHorizontal: spacing.small,
  },
  rowView: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: spacing.tiny,
    justifyContent: 'space-between',
  },
  label: {
    lineHeight: 20,
    fontWeight: '400',
    color: colors.BLACK_03,
  },
  value: {
    flex: 1,
    lineHeight: 20,
    fontWeight: '500',
    color: colors.PRIMARY,
  },
  footerView: {
    flex: 1,
    width: dimensions.width,
    paddingVertical: 10,
    borderTopWidth: 0.2,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: spacing.small,
  },
  btnRemove: {
    top: 4,
    right: 4,
    position: 'absolute',
  },

  flStyles: {
    marginTop: 10,
    marginBottom: 40,
  },
});
export const BaoHiemTNDSTaiSanScreen = memo(BaoHiemTNDSTaiSanScreenComponent, isEqual);
