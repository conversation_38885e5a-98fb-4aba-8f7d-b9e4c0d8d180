import { colors } from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import { ESmartClaimEndpoint } from '@app/services/endPoints';
import { dimensions, spacing } from '@app/theme';
import { FlashMessageHelper } from '@app/utils/FlashMessageHelper';
import { ButtonLinear, Text, TextInputOutlined } from '@component';
import React, { forwardRef, memo, useState } from 'react';
import isEqual from 'react-fast-compare';
import { Controller, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

const BORDER_RADIUS = 8;
const RenderContentModalTraLaiGiamDinhComponent = forwardRef((props, ref) => {
  const { profileData, onBackPress, onCancel } = props;
  const [isSubmiting, setIsSubmiting] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      chiNhanh: profileData?.ho_so ? profileData.ho_so?.ma_chi_nhanh : '',
      ghiChu: '',
      giamDinhVien: profileData?.ho_so ? profileData.ho_so?.ten_gdvht : '',
      maChiNhanh: '',
    },
    mode: 'onChange',
  });

  const onPressChuyen = async (data) => {
    try {
      setIsSubmiting(true);
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        nsd_moi: profileData.ho_so.ma_gdvht,
        ghi_chu: data.ghiChu,
        pm: 'BT',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.HS_TRA_LAI_GIAM_DINH, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Hồ sơ trả lại giám định thành công!', 'success');
      onBackPress && onBackPress();
    } catch (error) {
      setIsSubmiting(false);
      console.log('error', error);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  /* RENDER */

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Trả lại hồ sơ về bộ phận giám định
        </Text>
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={false}>
        <Controller
          control={control}
          name="giamDinhVien"
          rules={{
            required: true,
          }}
          render={({ field: { onChange, value } }) => (
            <TextInputOutlined
              editable={false}
              value={value}
              onChangeText={onChange}
              isRequired
              title="Giám định viên"
              error={errors.giamDinhVien && getErrMessage('giamDinhVien', errors.giamDinhVien.type)}
            />
          )}
        />
        <Controller
          control={control}
          name="ghiChu"
          rules={{
            required: true,
          }}
          render={({ field: { onChange, value } }) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              isRequired
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              inputStyle={{ maxHeight: 80 }}
              containerStyle={{ zIndex: -1 }}
              error={errors.ghiChu && getErrMessage('ghiChu', errors.ghiChu.type)}
            />
          )}
        />
      </KeyboardAwareScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={() => onCancel && onCancel()} title="Để sau" linearStyle={{ marginRight: 10 }} isSubBtn />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(onPressChuyen)} title="Chuyển" />
      </View>
    </View>
  );
});
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.smaller,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalTraLaiGiamDinh = memo(RenderContentModalTraLaiGiamDinhComponent, isEqual);
