import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint} from '@app/services/endPoints';
import {FontSize, dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Text} from '@component';
import React, {forwardRef, memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import RenderHTML, {defaultSystemFonts} from 'react-native-render-html';

const systemFonts = [...defaultSystemFonts];
const BORDER_RADIUS = 8;
const RenderContentModalChuyenThanhToanComponent = forwardRef((props, ref) => {
  const {profileData, onBackPress, onCancel} = props;
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [listDonViThanhToan, setListDonViThanhToan] = useState([]);
  const [sourceNote, setSourceNote] = useState('');

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      ghiChu: '',
      donViThanhToan: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    getListDonViThanhToan();
  }, []);

  const getListDonViThanhToan = async () => {
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
        nv: profileData?.ho_so?.nghiep_vu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_DV_THANH_TOAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let dataDonViThanhToan = [];
      if (response?.out_value?.dvi_thanh_toan !== null) {
        dataDonViThanhToan = response.data_info.filter((e) => e.ma_chi_nhanh === response.out_value.dvi_thanh_toan);
        setIsDisabled(true);
        setValue('donViThanhToan', response.out_value.dvi_thanh_toan);
      } else dataDonViThanhToan = response.data_info;
      setSourceNote(response.out_value.ghi_chu);
      setListDonViThanhToan(dataDonViThanhToan);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressChuyen = async (data) => {
    return Alert.alert('Chuyển thanh toán', 'Bạn có chắc chắn muốn chuyển thanh toán không?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: () => {
          setTimeout(async () => {
            try {
              setIsSubmiting(true);
              let params = {
                so_id: profileData?.ho_so?.so_id,
                ghi_chu: data.ghiChu,
                ma_chi_nhanh: data.donViThanhToan,
              };
              let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHUYEN_THANH_TOAN_BOI_THUONG, params);
              // let response = await CarClaimEndpoint.chuyenThanhToan(axiosConfig.ACTION_CODE.CHUYEN_THANH_TOAN_BOI_THUONG, params);
              setIsSubmiting(false);
              if (!response || !response.state_info || response.state_info.status !== 'OK') return;
              FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển thanh toán thành công!', 'success');
              onBackPress && onBackPress();
              onCancel && onCancel();
            } catch (error) {
              setIsSubmiting(false);
              Alert.alert('Thông báo', error.message);
            }
          }, 300);
        },
      },
    ]);
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  /* RENDER */

  //
  const WebDisplay = React.memo(function WebDisplay({}) {
    //custom style cho thẻ <pre>
    const customStyle = {fontSize: FontSize.size14, fontFamily: 'SFProDisplay, Helvetica, sans-serif', lineHeight: 22};
    return (
      <RenderHTML
        contentWidth={dimensions.width}
        source={{
          html: `${sourceNote}`,
        }}
        tagsStyles={{
          pre: customStyle,
        }}
        systemFonts={systemFonts}
      />
    );
  });

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Chuyển thanh toán
        </Text>
      </View>
      <View marginHorizontal={spacing.default} zIndex={9001}>
        <Controller
          control={control}
          name="donViThanhToan"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              zIndex={9001}
              isRequired={true}
              searchable={false}
              items={listDonViThanhToan}
              itemSelected={value}
              isOpen={isOpenDropdown}
              setOpen={setIsOpenDropdown}
              title="Đơn vị thanh toán"
              maxHeight={100}
              placeholder="Chọn đơn vị thanh toán"
              setItemSelected={(dispatch) => onChange(dispatch())}
              inputErr={errors.donViThanhToan && getErrMessage('donViThanhToan', errors.donViThanhToan.type)}
              schema={{
                label: 'ten_chi_nhanh',
                value: 'ma_chi_nhanh',
              }}
              disabled={isDisabled}
              containerStyle={{
                marginBottom: isOpenDropdown && listDonViThanhToan.length > 0 ? 100 : spacing.small,
              }}
            />
          )}
        />
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={true}>
        {/* <Controller
          control={control}
          name="ghiChu"
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              title="Ghi chú"
              placeholder="Nhập ghi chú chuyển"
              inputStyle={{maxHeight: 80}}
              containerStyle={{zIndex: -1}}
              error={errors.ghiChu && getErrMessage('ghiChu', errors.ghiChu.type)}
            />
          )}
        /> */}
        <View zIndex={-1}>
          <Text font="bold14">Ghi chú:</Text>
          <WebDisplay />
        </View>
      </KeyboardAwareScrollView>

      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={onCancel} title="Để sau" linearColors={[colors.GRAY2, colors.GRAY2]} linearStyle={{marginRight: 10}} textStyle={{color: colors.BLACK_03}} />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(onPressChuyen)} title="Chuyển" />
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
    maxHeight: dimensions.height * 0.8,
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.default,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalChuyenThanhToan = memo(RenderContentModalChuyenThanhToanComponent, isEqual);
