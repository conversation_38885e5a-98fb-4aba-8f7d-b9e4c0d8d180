import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Text, TextInputOutlined} from '@component';
import React, {forwardRef, memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useSelector} from 'react-redux';

const BORDER_RADIUS = 8;
const RenderContentModalChuyenBoPhanBoiThuongComponent = forwardRef((props, ref) => {
  const {profileData, onBackPress, onCancel} = props;
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [listNguoiXuLy, setListNguoiXuLy] = useState([]);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      chiNhanh: profileData?.ho_so ? profileData.ho_so?.ma_chi_nhanh : '',
      ghiChu: '',
      boiThuongVien: profileData?.ho_so ? profileData.ho_so?.btv : '',
      maChiNhanh: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    getListBoiThuongVien();
  }, []);

  const getListBoiThuongVien = async () => {
    try {
      let params = {
        ma_chi_nhanh: profileData.ho_so.ma_chi_nhanh,
        quyen: 'BTXE',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_BTV, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListNguoiXuLy(response.data_info);
      setValue('maChiNhanh', response.data_info[0].ma_chi_nhanh);
      // if (response.data_info.length === 1) setValue('boiThuongVien', response.data_info[0].nsd, {shouldValidate: true});
    } catch (error) {
      console.log('error', error);
    }
  };
  const onPressChuyen = async (data) => {
    try {
      setIsSubmiting(true);
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        nsd_moi: data.boiThuongVien,
        ghi_chu: data.ghiChu,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHUYEN_HS_SANG_BO_PHAN_BOI_THUONG, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển hồ sơ sang bộ phận bồi thường thành công!', 'success');
      onBackPress && onBackPress();
      // setTimeout(() => {
      //   NavigationUtil.popToRootStack();
      // }, 500);
    } catch (error) {
      setIsSubmiting(false);
      console.log('error', error);
    }
  };

  const getTenHienThi = (value, data = []) => {
    let name = '';
    data.map((e) => {
      if (e.ma_chi_nhanh === value) name = e.ten_chi_nhanh;
    });
    return name;
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  /* RENDER */

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Chuyển hồ sơ sang bộ phận bồi thường
        </Text>
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={false}>
        <Controller
          control={control}
          name="chiNhanh"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              title="Khu vực giám định"
              value={getTenHienThi(value, chiNhanhBaoHiemDangCay)}
              editable={false}
              disabled
              isRequired
              error={errors.chiNhanh && getErrMessage('chiNhanh', errors.chiNhanh.type)}
            />
          )}
        />
        <Controller
          control={control}
          name="boiThuongVien"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              zIndex={9001}
              isRequired={true}
              searchable={false}
              items={listNguoiXuLy}
              itemSelected={value}
              isOpen={isOpenDropdown}
              setOpen={setIsOpenDropdown}
              title="Người xử lý"
              maxHeight={100}
              placeholder="Chọn người xử lý"
              setItemSelected={(dispatch) => onChange(dispatch())}
              inputErr={errors.boiThuongVien && getErrMessage('boiThuongVien', errors.boiThuongVien.type)}
              schema={{
                label: 'ten',
                value: 'ma',
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="ghiChu"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              isRequired
              title="Ghi chú"
              placeholder="Nhập ghi chú"
              inputStyle={{maxHeight: 80}}
              containerStyle={{zIndex: -1}}
              error={errors.ghiChu && getErrMessage('ghiChu', errors.ghiChu.type)}
            />
          )}
        />
      </KeyboardAwareScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={() => onCancel && onCancel()} title="Để sau" linearStyle={{marginRight: 10}} isSubBtn />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(onPressChuyen)} title="Chuyển" />
      </View>
    </View>
  );
});
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.smaller,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalChuyenBoPhanBoiThuong = memo(RenderContentModalChuyenBoPhanBoiThuongComponent, isEqual);
