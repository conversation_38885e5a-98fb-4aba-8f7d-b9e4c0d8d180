import {colors} from '@app/commons/Theme';
import axiosConfig from '@app/services/axiosConfig';
import {CarClaimEndpoint, ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Text, TextInputOutlined} from '@component';
import React, {forwardRef, memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

const BORDER_RADIUS = 8;
const RenderContentModalTuChoiBoiThuongComponent = forwardRef((props, ref) => {
  const {profileData, onBackPress, onCancel} = props;
  const [isSubmiting, setIsSubmiting] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: {errors},
  } = useForm({
    defaultValues: {
      noiDung: '',
    },
    mode: 'onChange',
  });

  //XÁC NHẬN TỪ CHỐI BỒI THƯỜNG
  const xacNhanTuChoiBoiThuong = async (data) => {
    Alert.alert('Từ chối bồi thường', 'Bạn có chắc chắn muốn từ chối bồi thường?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          onCancel && onCancel();
          setIsSubmiting(true);
          setTimeout(async () => {
            try {
              let params = {
                so_id: profileData?.ho_so?.so_id,
                pm: 'GD',
                nd_tchoi: data.noiDung,
                create_file: 'ESCS_TB_TU_CHOI_TB',
                create_file_sign: 'ESCS_TB_TU_CHOI_TB',
                remove_file: 'ESCS_TB_TU_CHOI_TB',
              };
              let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TU_CHOI_BOI_THUONG, params);
              setIsSubmiting(false);
              if (!response || !response.state_info || response.state_info.status !== 'OK') return;
              FlashMessageHelper.showFlashMessage('Thông báo', 'Từ chối bồi thường thành công!', 'success');
              onBackPress && onBackPress();
            } catch (error) {
              setIsSubmiting(false);
              Alert.alert('Thông báo', error.message);
            }
          }, 300);
          return;
        },
      },
    ]);
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
  };

  /* RENDER */

  return (
    <View style={styles.container}>
      <View style={styles.headerModal}>
        <Text style={styles.txtHeader} font="bold14">
          Từ chối bồi thường
        </Text>
      </View>
      <KeyboardAwareScrollView style={styles.contentModal} scrollEnabled={false}>
        <Controller
          control={control}
          name="noiDung"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <TextInputOutlined
              value={value}
              onChangeText={onChange}
              multiline
              isRequired
              title="Nội dung từ chối"
              placeholder="Nhập rõ nội dung từ chối"
              inputStyle={{minHeight: 100, maxHeight: dimensions.height * 0.25}}
              containerStyle={{zIndex: -1}}
              error={errors.noiDung && getErrMessage('noiDung', errors.noiDung.type)}
              blurOnSubmit={false}
            />
          )}
        />
      </KeyboardAwareScrollView>
      <View flexDirection="row" bottom={10} marginHorizontal={10} marginTop={spacing.medium}>
        <ButtonLinear onPress={() => onCancel && onCancel()} title="Đóng" linearStyle={{marginRight: 10}} isSubBtn />
        <ButtonLinear loading={isSubmiting} disabled={isSubmiting} onPress={handleSubmit(xacNhanTuChoiBoiThuong)} title="Từ chối BT" />
      </View>
    </View>
  );
});
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.WHITE,
    width: dimensions.width - 24,
    borderRadius: BORDER_RADIUS,
    marginTop: getStatusBarHeight(),
  },
  headerModal: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
    backgroundColor: colors.BUTTON.LIGHT.PRIMARY,
  },
  contentModal: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
    backgroundColor: colors.WHITE,
    padding: spacing.smaller,
  },
  txtHeader: {
    color: colors.WHITE,
    textAlign: 'center',
    marginVertical: spacing.small,
  },
});

export const RenderContentModalTuChoiBoiThuong = memo(RenderContentModalTuChoiBoiThuongComponent, isEqual);
