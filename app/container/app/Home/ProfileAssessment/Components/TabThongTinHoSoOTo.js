import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectType2} from '@app/redux/slices/CategoryCommonSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, FontSize, moderateScale, scale, spacing, vScale} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, Empty, Icon, Text} from '@component';
import {REGUlAR_EXPRESSION, SCREEN_ROUTER_APP} from '@constant';
import Clipboard from '@react-native-clipboard/clipboard';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Linking, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import ActionSheet from 'react-native-actionsheet';
import Modal from 'react-native-modal';
import Share from 'react-native-share';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {HEADER_TITLE, ICON_HEADER} from './Constant';
import prompt from 'react-native-prompt-android';
import moment from 'moment';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const TabThongTinHoSoOToComponent = (props) => {
  const {
    profileData,
    danhGiaHienTruong,
    actionSheetMauInData,
    setActionSheetXacNhanBienBan,
    actionSheetMauInXacNhanBienBanData,
    prevScreen,
    laySoHoSo,
    listCanhBaoHoSo,
    xoaBenThamGiaGiamDinh,
    openActionSheetChuyenHienTruongXe,
  } = props;
  const prevScreenIsHoSoDiaBan = prevScreen === SCREEN_ROUTER_APP.HO_SO_DIA_BAN;
  const type2 = useSelector(selectType2);
  let scrollViewModalRef = useRef(null);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const [flatListHeight, setFlatListHeight] = useState((dimensions.height / 3) * 4);
  const [toggleModal, setToggleModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalData, setModalData] = useState([]);
  let actionSheetRef = useRef(null);
  // let actionSheetXacNhanBienBanRef = useRef(null);
  let actionSheetBoSungThongTinGiayToRef = useRef(null);
  let refModalGuiLinkChupAnhTuanTinNhan = useRef(null);
  const insect = useSafeAreaInsets();

  // useEffect(() => {
  //   setActionSheetXacNhanBienBan(actionSheetXacNhanBienBanRef);
  // }, []);
  /** FUNCTION  */
  useEffect(() => {
    //xử lý trường hợp xoá 1 đối tượng tổn thất
    if (modalTitle === HEADER_TITLE[6] && toggleModal && modalData.length !== props.profileData.ds_doi_tuong.length && !props.toggleModalChonDoiTuong) setModalData(profileData.ds_doi_tuong);
  }, [props.profileData]);

  //xử lý trường hợp mở modal chọn đối tượng khi chụp ảnh
  useEffect(() => {
    if (props.toggleModalChonDoiTuong) {
      setToggleModal(true);
      setModalTitle('Chọn đối tượng chụp ảnh');
      setModalData(profileData.ds_doi_tuong);
    } else setToggleModal(false);
  }, [props.toggleModalChonDoiTuong]);

  const onPressHeader = (headerTitle, dataModal, routeName) => {
    const {ho_so} = profileData;
    let routeParams = {profileData, prevScreenIsHoSoDiaBan};
    // các menu chuyển màn luôn không cần check điều kiện
    const noNeedToCheckConditionAction =
      headerTitle === HEADER_TITLE[4] ||
      headerTitle === HEADER_TITLE[13] ||
      headerTitle === HEADER_TITLE[14] ||
      headerTitle === HEADER_TITLE[16] ||
      headerTitle === HEADER_TITLE[22] ||
      headerTitle === HEADER_TITLE[26];
    //các menu cần check điều kiện
    const checkConditonAction =
      headerTitle === HEADER_TITLE[3] ||
      headerTitle === HEADER_TITLE[1] ||
      headerTitle === HEADER_TITLE[7] ||
      headerTitle === HEADER_TITLE[8] ||
      headerTitle === HEADER_TITLE[9] ||
      headerTitle === HEADER_TITLE[10] ||
      headerTitle === HEADER_TITLE[11] ||
      headerTitle === HEADER_TITLE[17] ||
      headerTitle === HEADER_TITLE[18] ||
      headerTitle === HEADER_TITLE[19] ||
      headerTitle === HEADER_TITLE[20] ||
      headerTitle === HEADER_TITLE[21] ||
      headerTitle === HEADER_TITLE[24] ||
      headerTitle === HEADER_TITLE[25] ||
      headerTitle === HEADER_TITLE[27] ||
      headerTitle === HEADER_TITLE[28] ||
      headerTitle === HEADER_TITLE[29] ||
      headerTitle === HEADER_TITLE[32] ||
      headerTitle === HEADER_TITLE[23] ||
      headerTitle === HEADER_TITLE[30] ||
      headerTitle === HEADER_TITLE[31];
    const modalMenu = headerTitle === HEADER_TITLE[5] || headerTitle === HEADER_TITLE[6] || headerTitle === HEADER_TITLE[12] || headerTitle === HEADER_TITLE[15];

    if (headerTitle === HEADER_TITLE[2]) actionSheetRef.show();

    if (noNeedToCheckConditionAction) {
      if (headerTitle === HEADER_TITLE[14])
        routeParams = {
          loaiHinhNghiepVu: profileData.lh_nv,
        };
      if (headerTitle === HEADER_TITLE[13]) {
        routeParams = {
          giayChungNhan: {
            ma_doi_tac: ho_so.ma_doi_tac,
            ma_chi_nhanh: ho_so.ma_chi_nhanh_ql,
            nv: 'XE',
            so_id_hdong: ho_so.so_id_hd,
            so_id_gcn: ho_so.so_id_dt,
            so_gcn: ho_so.gcn,
          },
          profileData,
          loaiHinhNghiepVu: profileData.lh_nv,
          prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
          prevScreenIsHoSoDiaBan,
        };
      }
      NavigationUtil.push(routeName, routeParams);
    } else if (checkConditonAction) {
      if (ho_so.hien_thi_button === '1' && headerTitle !== HEADER_TITLE[11] && headerTitle !== HEADER_TITLE[10]) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Bạn chưa nhận hồ sơ', 'info');
        return;
      }
      if (headerTitle === HEADER_TITLE[17]) {
        actionSheetBoSungThongTinGiayToRef.show();
        return;
      }
      if (headerTitle === HEADER_TITLE[7]) {
        if (ho_so.hien_truong === 'K') {
          FlashMessageHelper.showFlashMessage('Thông báo', 'Không thể đánh giá do xe không ở hiện trường', 'info');
          return;
        }
      }
      if (headerTitle === HEADER_TITLE[27]) return props.onPressHuyHoSo && props.onPressHuyHoSo();
      if (headerTitle === HEADER_TITLE[28]) return props.onPressGoHuyHoSo && props.onPressGoHuyHoSo();
      if (headerTitle === HEADER_TITLE[30]) props.onPressTuChoiBoiThuong && props.onPressTuChoiBoiThuong('TCBT');
      if (headerTitle === HEADER_TITLE[31]) return onPressGoTuChoiBoiThuong();
      NavigationUtil.push(routeName, {
        profileData,
        danhGiaHienTruong,
        prevScreenIsHoSoDiaBan,
      });
    } else if (modalMenu) {
      setModalTitle(headerTitle);
      //nếu là Tình trạng thanh toán - thì gọi API để lấy data
      setModalData(dataModal);
      setToggleModal(!toggleModal);
    }
  };

  const onPressXemChiTietMauIn = (index, type) => {
    if (type === 0 && index === actionSheetMauInData.length - 1) return;
    else if (type === 1 && index === actionSheetMauInXacNhanBienBanData.length - 1) return;
    // if (index === actionSheetMauInData.length - 1 || index === actionSheetMauInXacNhanBienBanData.length - 1) return;
    let params = {
      profileData,
      viTriMauIn: index,
      prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
      actionSheetMauInData: type === 0 ? actionSheetMauInData : actionSheetMauInXacNhanBienBanData,
    };
    if (prevScreen) params.prevPrevScreen = SCREEN_ROUTER_APP.HO_SO_DIA_BAN;
    NavigationUtil.push(SCREEN_ROUTER_APP.PDF_VIEW, params);
  };

  const onPressXoaDoiTuongTonThat = (doiTuongTonThat) => {
    Alert.alert('Thông báo', 'Bạn có muốn xoá đối tượng này', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: () => props.xoaDoiTuongTonThat(doiTuongTonThat),
      },
    ]);
  };
  const onPressTatModal = () => {
    setToggleModal(false);
    props.setToggleModalChonDoiTuong(false);
  };

  const onPressShareLink = (linkType, linkShared) => {
    let linkTitle = 'hiện trường';
    if (linkType === 1) linkTitle = 'chi tiết';
    else if (linkType === 2) linkTitle = 'nghiệm thu';
    else if (linkType === 3) linkTitle = 'bổ sung hồ sơ';
    if (!linkShared || !linkShared.trim()) {
      Alert.alert('Thông báo', 'Bạn không có quyền chia sẻ link ' + linkTitle);
      return;
    }
    let options = {
      title: 'Chia sẻ link chụp ảnh ' + linkTitle,
      url: linkShared,
    };
    Share.open(options);
  };

  const onPressGuiTinNhan = (linkType, linkShared) => {
    let linkTitle = 'hiện trường';
    if (linkType === 1) linkTitle = 'chi tiết';
    else if (linkType === 2) linkTitle = 'nghiệm thu';
    else if (linkType === 3) linkTitle = 'bổ sung hồ sơ';
    if (!linkShared || !linkShared.trim()) {
      Alert.alert('Thông báo', 'Bạn không có quyền gửi tin nhắn link ' + linkTitle + ' đến khách hàng');
      return;
    }

    prompt(
      'Thông báo',
      'Bạn có muốn gửi link chụp ảnh ' + linkTitle + ' đến khách hàng ' + profileData.ho_so.nguoi_lhe + ' - ' + profileData.ho_so.dthoai_lhe + '\n Số điện thoại cách nhau bởi dấu chấm phẩy ";"',
      [
        {text: 'Để sau', onPress: () => {}, style: 'destructive'},
        {
          text: 'Đồng ý',
          onPress: async (stringSDT) => {
            try {
              let isStringSdtValidate = true;
              let arrSdt = stringSDT
                .split(';')
                .filter((item) => item.trim())
                .map((itemSdt) => {
                  if (isStringSdtValidate && itemSdt.trim() && !REGUlAR_EXPRESSION.REG_PHONE.test(itemSdt.trim())) {
                    Alert.alert('Thông báo', 'Số điện thoại sai định dạng hoặc không cách nhau bởi dấu chấm phẩy. Vui lòng nhập lại');
                    isStringSdtValidate = false;
                  }
                  return itemSdt.trim();
                });
              if (!isStringSdtValidate) return;
              setToggleModal(false);
              let params = {
                ma_doi_tac: profileData.ho_so.ma_doi_tac,
                so_id: profileData.ho_so.so_id,
                nv: 'XE',
                loai: linkType === 0 ? 'HIEN_TRUONG' : 'CHUP_ANH_CHI_TIET',
                dthoai_lhe: arrSdt.join(';'),
              };
              let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.GUI_SMS_LINK_CHUP_ANH, params);
              if (!response || !response.state_info || response.state_info.status !== 'OK') return;
              FlashMessageHelper.showFlashMessage('Thông báo', 'Gửi tin nhắn thành công', 'success');
            } catch (error) {
              Alert.alert('Thông báo', error.message);
            }
          },
        },
      ],
      {
        defaultValue: profileData.ho_so.dthoai_lhe + ' ; ',
        placeholder: 'Số điện thoại người nhận',
      },
    );
    return;
    Alert.alert('Thông báo', 'Bạn có muốn gửi link chụp ảnh ' + linkTitle + ' đến khách hàng ' + profileData.ho_so.nguoi_lhe + ' - ' + profileData.ho_so.dthoai_lhe, [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: async () => {
          setToggleModal(false);
          try {
            let params = {
              ma_doi_tac: profileData.ho_so.ma_doi_tac,
              so_id: profileData.ho_so.so_id,
              nv: 'XE',
              loai: linkType === 0 ? 'HIEN_TRUONG' : 'CHUP_ANH_CHI_TIET',
              dthoai_lhe: '',
            };
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.GUI_SMS_LINK_CHUP_ANH, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Gửi tin nhắn thành công', 'success');
          } catch (error) {
            Alert.alert('Thông báo', error.message);
          }
        },
      },
    ]);
  };
  const onPressSaoChepLink = (linkType, linkShared) => {
    let linkTitle = 'hiện trường';
    if (linkType === 1) linkTitle = 'chi tiết';
    else if (linkType === 2) linkTitle = 'nghiệm thu';
    else if (linkType === 3) linkTitle = 'bổ sung hồ sơ';
    if (!linkShared || !linkShared.trim()) {
      Alert.alert('Thông báo', 'Bạn không có quyền sao chép link ' + linkTitle);
      return;
    }
    Clipboard.setString(linkShared);
    FlashMessageHelper.showFlashMessage('Thông báo', 'Sao chép thành công', 'success');
  };

  const onPressBoSungThongTinGiayToXe = (index) => {
    if (index === 2) return;
    let params = {
      profileData,
      prevScreen: SCREEN_ROUTER_APP.PROFILE_ASSESSMENT,
      prevScreenIsHoSoDiaBan,
      thongTinGiayTo: {},
    };
    if (index === 0) params.thongTinGiayTo.nhom_hang_muc = 'BANG_LAI';
    else if (index === 1) params.thongTinGiayTo.nhom_hang_muc = 'DANG_KIEM';
    NavigationUtil.push(SCREEN_ROUTER_APP.BO_SUNG_THONG_TIN_GIAY_TO, params);
  };

  const onPressThemDoiTuong = () => {
    setToggleModal(false);
    NavigationUtil.push(SCREEN_ROUTER_APP.DOI_TUONG_TON_THAT, {
      profileData,
    });
  };

  const doiDoiTuong = () => NavigationUtil.push(SCREEN_ROUTER_APP.DOI_DOI_TUONG, {profileInfo: profileData?.ho_so, profileData});

  const boSungThongTinChuXe = () => NavigationUtil.push(SCREEN_ROUTER_APP.BO_SUNG_THONG_TIN_CHU_XE_OTO, {profileInfo: profileData?.ho_so, profileData});

  const doiThongTinBienSoXe = () => {
    if (prevScreenIsHoSoDiaBan) return;
    NavigationUtil.push(SCREEN_ROUTER_APP.DOI_THONG_TIN_BSX, {profileInfo: profileData?.ho_so, type: ''});
  };

  const getCanhBaoHoSo = (loai) => listCanhBaoHoSo.find((item) => item.loai === loai);

  const onPressPhoneBtn = (soDienThoai) => {
    if (prevScreenIsHoSoDiaBan) return;
    if (soDienThoai && soDienThoai !== '') {
      Linking.openURL(`tel:${soDienThoai}`);
    } else NavigationUtil.push(SCREEN_ROUTER_APP.THEM_SDT_CHU_XE, {profileInfo: profileData?.ho_so});
  };

  const onPressGoTuChoiBoiThuong = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn gỡ từ chối bồi thường?', [
      {
        text: 'Để sau',
        style: 'destructive',
      },
      {
        text: 'Đồng ý',
        onPress: () => props.xacNhanGoTuChoiBoiThuong && props.xacNhanGoTuChoiBoiThuong(),
      },
    ]);
  };

  /* RENDER */
  // RENDER header các đầu mục
  const renderProfileInformationHeader = (title, data, routeName) => {
    const showWarning = title === HEADER_TITLE[5] || title === HEADER_TITLE[6];
    const actionHuy = title === HEADER_TITLE[27] || title === HEADER_TITLE[28];
    const actionTuChoi = title === HEADER_TITLE[30] || title === HEADER_TITLE[31];

    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => onPressHeader(title, data, routeName)}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View flexDirection="row" alignItems="center">
              <Icon.FontAwesome name={ICON_HEADER[title]} size={moderateScale(15)} style={styles.iconBtnTopLeftView} />
              <Text style={[styles.headerTitle, (actionHuy || actionTuChoi) && {color: colors.RED1}]}>{title + ' ' + dataLength}</Text>
            </View>
          </View>
          <View flexDirection="row">
            {showWarning && <View>{data?.length > 0 ? <Icon.AntDesign name="checkcircle" color={colors.GREEN} size={20} /> : <Icon.Ionicons name="warning" color={colors.RED1} size={20} />}</View>}
            {title === HEADER_TITLE[3] && getCanhBaoHoSo('HO_SO_MUON') && (
              <TouchableOpacity onPress={() => Alert.alert('Thông báo', getCanhBaoHoSo('HO_SO_MUON').noi_dung)}>
                <Icon.AntDesign name="warning" color={colors.ORANGE} size={moderateScale(20)} />
              </TouchableOpacity>
            )}

            {title !== HEADER_TITLE[0] && <Icon.SimpleLineIcons name={'arrow-right'} size={moderateScale(15)} style={styles.iconBtnTopRightView} />}
          </View>
        </TouchableOpacity>
        {title !== HEADER_TITLE[0] && (
          <View style={{backgroundColor: colors.WHITE5}}>
            <View style={styles.spacing} />
          </View>
        )}
      </View>
    );
  };
  const renderThongTinChungChidren = (title, data, containerStyle, subValue) => {
    let showBtnDoiBienSoXe = title === 'Biển xe' && +profileData.ho_so.hien_thi_button !== 1;
    return (
      <View style={[styles.inforView, containerStyle]}>
        <View flexDirection="row">
          <Text style={styles.txtTitle} children={title} />
          {title === 'Biển xe' && profileData.ho_so.doi_tuong !== profileData.ho_so.doi_tuong_goc && (
            <Text children={`(Gốc: ${profileData.ho_so.doi_tuong_goc})`} style={styles.txtDoiTuongGoc} font="regular12" />
          )}
        </View>
        {typeof data === 'number' ? (
          <NumericFormat
            value={data}
            displayType={'text'}
            thousandSeparator={true}
            renderText={(value) => <Text style={styles.txtDetail} selectable children={value + (subValue ? ' (' + subValue + '%)' : '')} />}
          />
        ) : (
          <>
            {data?.trim() !== '' && (
              <View style={styles.detailView}>
                <Text style={[styles.txtDetail, title === 'Trạng thái' && {color: colors.GREEN}, title === 'Biển xe' && {paddingRight: 0}]} selectable children={data} />
              </View>
            )}
            {data?.trim() === '' && title === 'Số hồ sơ' && profileData.ho_so.hien_thi_button !== '1' && !prevScreenIsHoSoDiaBan && (
              <TouchableOpacity style={styles.laySoHoSoView} onPress={laySoHoSo}>
                <Icon.FontAwesome name="ticket" color={colors.PRIMARY} size={moderateScale(20)} style={{marginRight: spacing.smaller}} />
                <Text style={[styles.txtDetail, {color: colors.PRIMARY}]} children="Lấy số hồ sơ" />
              </TouchableOpacity>
            )}
            {/* {data?.trim() === 'HD_KHONG_XAC_DINH' && title === 'Số hợp đồng' && ( && */}
            {title === 'Số hợp đồng' && (
              <TouchableOpacity style={styles.laySoHoSoView} onPress={doiDoiTuong}>
                <Icon.FontAwesome name="exchange" color={colors.PRIMARY} size={moderateScale(20)} style={{marginRight: spacing.smaller}} />
                <Text style={[styles.txtDetail, {color: colors.PRIMARY}]} children="Đổi đối tượng" />
              </TouchableOpacity>
            )}
            {title === 'Tên chủ xe' && (
              <TouchableOpacity style={styles.laySoHoSoView} onPress={boSungThongTinChuXe}>
                <Icon.FontAwesome name="address-card" color={colors.PRIMARY} size={moderateScale(20)} style={{marginRight: spacing.smaller}} />
                <Text style={[styles.txtDetail, {color: colors.PRIMARY}]} children="Bổ sung thông tin" />
              </TouchableOpacity>
            )}
            {showBtnDoiBienSoXe && (
              <TouchableOpacity style={styles.laySoHoSoView} onPress={doiThongTinBienSoXe}>
                <Icon.FontAwesome name="exchange" color={colors.PRIMARY} size={moderateScale(20)} style={{marginRight: spacing.smaller}} />
                <Text style={[styles.txtDetail, {color: colors.PRIMARY}]} children="Đổi thông tin" />
              </TouchableOpacity>
            )}
          </>
        )}
      </View>
    );
  };

  //render ra thông tin hồ sơ
  const renderProfileInformation = () => {
    if (!profileData || !profileData.ho_so) return;
    const {ho_so, dien_bien} = profileData;
    let indexDienBien = 0;
    if (dien_bien?.length > 0) indexDienBien = dien_bien.length - 1;
    let showBtnHuy = false;
    let showBtnGoHuy = false;
    let showBtnGoTuChoiBt = false;
    const validateBtnHuy = [
      +moment(profileData?.ho_so?.ngay_huy_hs, 'YYYYMMDD').format('YYYYMMDD') === null,
      +moment(profileData?.ho_so?.ngay_huy_hs, 'YYYYMMDD').format('YYYYMMDD') === 30000101,
      +moment(profileData?.ho_so?.ngay_huy_hs, 'YYYYMMDD').format('YYYYMMDD')?.toString().trim() === '',
    ];
    if (validateBtnHuy.indexOf(true) === -1) {
      showBtnGoHuy = true;
    } else showBtnHuy = true;
    if (profileData.ho_so.ma_trang_thai === 'HSBT_XE_BT_TU_CHOI') {
      showBtnGoTuChoiBt = true;
    }
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View flex={1}>
          {renderProfileInformationHeader(HEADER_TITLE[0])}
          <View flexDirection="row">
            {renderThongTinChungChidren('Số hồ sơ', ho_so.so_hs, {borderTopLeftRadius: 20, backgroundColor: colors.WHITE})}
            {renderThongTinChungChidren('Biển xe', ho_so.doi_tuong || '')}
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Số hợp đồng', ho_so.so_hd)}
            {renderThongTinChungChidren('Giấy chứng nhận', ho_so.gcn || '')}
          </View>

          <View flexDirection="row">
            {renderThongTinChungChidren('Tên chủ xe', ho_so.chu_xe || '')}
            {ho_so.dthoai_chu_xe && (
              <View style={styles.inforView}>
                <View>
                  <Text style={styles.txtTitle}>Điện thoại chủ xe</Text>
                  <TouchableOpacity style={styles.phoneRow} onPress={() => onPressPhoneBtn(ho_so.dthoai_chu_xe)}>
                    <Icon.Entypo name="old-phone" size={moderateScale(20)} color={colors.PRIMARY} style={{marginRight: 10}} />
                    <Text style={[styles.txtDetail, {color: colors.PRIMARY}]}>{ho_so.dthoai_chu_xe || 'Thêm số điện thoại'}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Người liên hệ', ho_so.nguoi_lhe)}
            {ho_so.dthoai_lhe && (
              <View style={styles.inforView}>
                <TouchableOpacity>
                  <Text style={styles.txtTitle}>Điện thoại người liên hệ</Text>
                  <TouchableOpacity style={styles.phoneRow} onPress={() => Linking.openURL(`tel:${ho_so.dthoai_lhe}`)}>
                    <Icon.Entypo name="old-phone" size={moderateScale(20)} color={colors.PRIMARY} style={{marginRight: 10}} />
                    <Text style={[styles.txtDetail, {color: colors.PRIMARY}]}>{ho_so.dthoai_lhe || ''}</Text>
                  </TouchableOpacity>
                </TouchableOpacity>
              </View>
            )}
          </View>

          {renderThongTinChungChidren('Thời gian, địa điểm dự kiến giám định', ho_so.dia_diem_gd || ho_so.noi_dung)}
          <View flexDirection="row">
            <View style={styles.inforView}>
              <TouchableOpacity
                onPress={() => {
                  !prevScreenIsHoSoDiaBan && openActionSheetChuyenHienTruongXe();
                }}>
                <Text style={styles.txtTitle}>Hiện trường</Text>
                <View alignItems="center" flexDirection="row">
                  {ho_so.hien_truong === 'D' ? (
                    <Icon.FontAwesome5 name="car-crash" size={moderateScale(20)} color={colors.PRIMARY} style={{marginRight: spacing.tiny}} />
                  ) : (
                    <Icon.MaterialCommunityIcons name="garage" size={moderateScale(20)} color={colors.PRIMARY} style={{marginRight: spacing.tiny}} />
                  )}

                  <Text style={[styles.txtDetail, {color: colors.PRIMARY}]}>{ho_so.hien_truong === 'D' ? 'Xe đang ở hiện trường' : 'Xe không ở hiện trường'}</Text>
                </View>
              </TouchableOpacity>
            </View>
            {/* {renderThongTinChungChidren('Hiện trường', ho_so.hien_truong === 'D' ? 'Xe đang ở hiện trường' : 'Xe không ở hiện trường')} */}
            <View style={styles.inforView}>
              <TouchableOpacity onPress={() => onPressHeader(HEADER_TITLE[15])}>
                <Text style={styles.txtTitle}>Link chụp ảnh</Text>
                <View alignItems="center" flexDirection="row">
                  <Icon.FontAwesome name="share-alt" size={moderateScale(20)} color={colors.PRIMARY} style={{marginRight: 10}} />
                  <Text style={[styles.txtDetail, {color: colors.PRIMARY}]}>Chia sẻ</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          <View flexDirection="row">
            {renderThongTinChungChidren('Tên lái xe', dien_bien[indexDienBien]?.ten_lxe || '')}
            <View style={styles.inforView}>
              <View>
                <Text style={styles.txtTitle}>Điện thoại lái xe</Text>
                <TouchableOpacity onPress={() => Linking.openURL(`tel:${dien_bien[indexDienBien]?.dthoai_lxe}`)} style={styles.phoneRow}>
                  <Icon.Entypo name="old-phone" size={20} color={colors.PRIMARY} />
                  <Text style={[styles.txtDetail, {marginLeft: 8}]}>{dien_bien[indexDienBien]?.dthoai_lxe || ''}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View flexDirection="row">
            {renderThongTinChungChidren('Trạng thái', ho_so.trang_thai || '')}
            {renderThongTinChungChidren('Ngày thông báo', ho_so.ngay_tb || '')}
          </View>
        </View>
        <View flexDirection="row">
          {renderThongTinChungChidren('Bồi thường viên', ho_so.btv || '')}
          {renderThongTinChungChidren('Giám định viên', ho_so.ma_gdvht || '')}
        </View>
        <View flexDirection="row">
          {renderThongTinChungChidren('Đơn vị cấp đơn', ho_so.dvi_cap_don || '')}
          {renderThongTinChungChidren('Giá trị xe', ho_so.gia_tri_xe || '')}
        </View>
        <View flexDirection="row">
          {renderThongTinChungChidren('Mức khấu trừ', ho_so.mien_thuong_hs || '')}
          {renderThongTinChungChidren('Số tiền bảo hiểm vật chất', ho_so.so_tien_bh_vcx || '')}
        </View>
        <View flexDirection="row">
          {renderThongTinChungChidren('Mã chương trình BH', ho_so.ctrinh_bh || '', {backgroundColor: colors.WHITE, borderBottomLeftRadius: 20, borderBottomWidth: 0})}
          {renderThongTinChungChidren('Tên chương trình BH', ho_so.ctrinh_bh_ten || '', {borderBottomWidth: 0}, ho_so.tl_tham_gia_bh || '')}
        </View>
        {/* GIẤY CHỨNG NHẬN */}
        {renderProfileInformationHeader(HEADER_TITLE[13], null, SCREEN_ROUTER_APP.GIAY_CHUNG_NHAN)}
        {/* Lịch sử bồi thường */}
        {renderProfileInformationHeader(HEADER_TITLE[16], null, SCREEN_ROUTER_APP.LICH_SU_BOI_THUONG)}
        {/* LỊCH GIÁM ĐỊNH */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[1], null, SCREEN_ROUTER_APP.ASSESSMENT_SCHEDULE_PROFILE)}
        {/* DANH SÁCH CÁC VỤ TỔN THẤT */}
        {renderProfileInformationHeader(HEADER_TITLE[3], profileData.dien_bien, SCREEN_ROUTER_APP.DS_CAC_VU_TON_THAT)}
        {/* DANH SÁCH ĐỐI TƯỢNG TỔN THẤT */}
        {renderProfileInformationHeader(HEADER_TITLE[6], profileData.ds_doi_tuong)}
        {/* BÊN THAM GIA GÍAM ĐỊNH */}
        {renderProfileInformationHeader(HEADER_TITLE[5], profileData.nguoi_dd)}
        {/* XEM MẤU IN */}
        {renderProfileInformationHeader(HEADER_TITLE[2], profileData.mau_in)}
        {/* Thông tin giấy tờ xe */}
        {renderProfileInformationHeader(HEADER_TITLE[17])}
        {/* QUÁ TRÌNH GIẢI QUYẾT */}
        {renderProfileInformationHeader(HEADER_TITLE[4], profileData.qtxl, SCREEN_ROUTER_APP.QUA_TRINH_XL_HS_XE_OTO)}
        {/* DANH SÁCH CÁC LẦN GĐ */}
        {renderProfileInformationHeader(HEADER_TITLE[18], null, SCREEN_ROUTER_APP.DS_CAC_LAN_GIAM_DINH)}
        {/* ĐÁNH GIÁ HIỆN TRƯỜNG */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[7], null, SCREEN_ROUTER_APP.DANH_GIA_HIEN_TRUONG)}
        {/* CHỈ ĐỊNH GARA - ẨN */}
        {/* {renderProfileInformationHeader(HEADER_TITLE[8], null, SCREEN_ROUTER_APP.CHI_DINH_GARA)} */}
        {/* Báo cáo giám định */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[9], null, SCREEN_ROUTER_APP.BAO_CAO_GIAM_DINH)}
        {/* Chuyển luồng xử lý */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[32], null, SCREEN_ROUTER_APP.CHUYEN_LUONG_XL_HS)}
        {/* Chuyển giám định viên hiện trường */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[10], null, SCREEN_ROUTER_APP.CHUYEN_GDV_HIEN_TRUONG)}
        {/* CHUYỂN NGƯỜI XỬ LÝ HỒ SƠ */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[24], null, SCREEN_ROUTER_APP.CHUYEN_NGUOI_XU_LY_HO_SO_O_TO)}
        {/* Giám định hộ */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[25], null, SCREEN_ROUTER_APP.DS_YEU_CAU_GIAM_DINH_BOI_THUONG_HO)}
        {/* Phân công giám định */}
        {/* {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[11], null, SCREEN_ROUTER_APP.PHAN_CONG_GIAM_DINH)} */}
        {/* Tình trạng thanh toán */}
        {/* {renderProfileInformationHeader(HEADER_TITLE[12])} */}
        {/* Loại hình nghiệp vụ tham gia */}
        {/* {renderProfileInformationHeader(HEADER_TITLE[14], null, SCREEN_ROUTER_APP.LOAI_HINH_NGHIEP_VU_THAM_GIA)} */}

        {/* Hoá đơn chứng từ người thụ hưởng */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[19], null, SCREEN_ROUTER_APP.DS_NGUOI_THU_HUONG)}
        {/* YÊU CẦU BỔ SUNG HỒ SƠ, GIẤY TỜ  */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[20], null, SCREEN_ROUTER_APP.YC_BO_SUNG_HS_GIAY_TO)}
        {/* CHI PHÍ CẨU/KÉO/KHÁC */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[23], null, SCREEN_ROUTER_APP.CHI_PHI_KHAC)}
        {/* Ý KIẾN TRAO ĐỔI */}
        {!prevScreenIsHoSoDiaBan && renderProfileInformationHeader(HEADER_TITLE[21], null, SCREEN_ROUTER_APP.Y_KIEN_TRAO_DOI)}
        {renderProfileInformationHeader(HEADER_TITLE[26], null, SCREEN_ROUTER_APP.TIEN_TRINH_BOI_THUONG)}
        {/* Huỷ hồ sơ */}
        {!prevScreenIsHoSoDiaBan && showBtnHuy && renderProfileInformationHeader(HEADER_TITLE[27])}
        {/*Gỡ Huỷ hồ sơ */}
        {!prevScreenIsHoSoDiaBan && showBtnGoHuy && renderProfileInformationHeader(HEADER_TITLE[28])}
        {/*Từ chối bồi thường */}
        {!prevScreenIsHoSoDiaBan && !showBtnGoTuChoiBt && renderProfileInformationHeader(HEADER_TITLE[30])}
        {/*Gỡ Từ chối bồi thường */}
        {!prevScreenIsHoSoDiaBan && showBtnGoTuChoiBt && renderProfileInformationHeader(HEADER_TITLE[31])}
        {/* {!prevScreenIsHoSoDiaBan && __DEV__ && renderProfileInformationHeader(HEADER_TITLE[29], null, SCREEN_ROUTER_APP.TAO_LAN_GD_BO_SUNG)} */}
        {/* {renderProfileInformationHeader(HEADER_TITLE[22], null, SCREEN_ROUTER_APP.VIDEO)} */}
      </View>
    );
  };

  //RENDER ITEM CÁC BÊN THAM GIA
  const renderJoinResolveItem = (item) => {
    let daiDien = '';
    for (let i = 0; i < type2.length; i++) {
      if (item.dai_dien === type2[i].value) {
        daiDien = type2[i].label;
        break;
      }
    }
    return (
      <View style={styles.joinResolveView}>
        <View style={{flex: 1}}>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Đại diện</Text>
            <Text style={styles.txtDetail}>{daiDien || '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Họ tên</Text>
            <Text style={styles.txtDetail}>{item.ten?.trim() || '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Điện thoại</Text>
            <Text style={styles.txtDetail}>{item.dien_thoai?.trim() || '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Email</Text>
            <Text style={styles.txtDetail}>{item.email?.trim() || '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Lần giám định</Text>
            <Text style={styles.txtDetail}>{item.lan_gd_hthi?.trim() || '...'}</Text>
          </View>
        </View>
        {prevScreen !== SCREEN_ROUTER_APP.HO_SO_DIA_BAN && profileData.ho_so.hien_thi_button != 1 && (
          <View style={styles.actionBenThemGiaGiamDinhView}>
            <TouchableOpacity
              onPress={() => {
                setToggleModal(false);
                NavigationUtil.push(SCREEN_ROUTER_APP.JOIN_RESOLVE, {
                  profileData,
                  joinResolveData: item,
                });
              }}
              style={styles.iconActionBenThamGiamDinhView}>
              <Icon.FontAwesome5 name="user-edit" size={moderateScale(20)} color={colors.BLACK} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => xoaBenThamGiaGiamDinh(item, () => setToggleModal(false))} style={styles.iconActionBenThamGiamDinhView}>
              <Icon.FontAwesome5 name="user-times" size={moderateScale(20)} color={colors.RED1} />
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };
  // ITEM ĐỐI TƯỢNG TỔN THẤT
  const renderDoiTuongTonThatItem = (item) => {
    let nhomDoiTuong = '';
    for (let i = 0; i < profileData.nhom_doi_tuong.length; i++) {
      if (item.nhom === profileData.nhom_doi_tuong[i].ma) {
        nhomDoiTuong = profileData.nhom_doi_tuong[i].ten;
        break;
      }
    }
    return (
      <TouchableOpacity
        style={styles.joinResolveView}
        onPress={() => profileData.ho_so.hien_thi_button != 1 && props.toggleModalChonDoiTuong && !prevScreenIsHoSoDiaBan && props.setDoiTuongDuocChon({...item})}>
        <View style={{flex: 1}}>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Nhóm đối tượng</Text>
            <Text style={styles.txtDetail}>{nhomDoiTuong || '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Tên đối tượng</Text>
            <Text style={styles.txtDetail}>{item.ten_doi_tuong?.trim() || '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Tên khách hàng</Text>
            <Text style={styles.txtDetail}>{item.ten_kh?.trim() || '...'}</Text>
          </View>
          <View style={styles.joinResolveDetailView}>
            <Text style={styles.txtTitle}>Ghi chú</Text>
            <Text style={styles.txtDetail}>{item.ghi_chu?.trim() || '...'}</Text>
          </View>
        </View>

        {/* Nút sửa - xoá */}
        <View style={{alignItems: 'center'}}>
          {profileData.ho_so.hien_thi_button != 1 && !props.toggleModalChonDoiTuong && !prevScreenIsHoSoDiaBan && (
            <View style={styles.actionBenThemGiaGiamDinhView}>
              <TouchableOpacity
                style={styles.iconActionBenThamGiamDinhView}
                onPress={() => {
                  if (item.kieu_dt !== undefined && item.kieu_dt !== null && item.kieu_dt === 'BH') {
                    return Alert.alert('Thông báo', 'Không sửa xoá đối tượng bảo hiểm!');
                  }
                  setToggleModal(false);
                  NavigationUtil.push(SCREEN_ROUTER_APP.DOI_TUONG_TON_THAT, {
                    profileData,
                    doiTuongTonThat: item,
                  });
                }}>
                <Icon.FontAwesome5 name="edit" size={moderateScale(20)} color={colors.BLACK} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconActionBenThamGiamDinhView}
                onPress={() => {
                  if (item.kieu_dt !== undefined && item.kieu_dt !== null && item.kieu_dt === 'BH') {
                    return Alert.alert('Thông báo', 'Không sửa xoá đối tượng bảo hiểm!');
                  }
                  onPressXoaDoiTuongTonThat(item);
                }}>
                <Icon.FontAwesome5 name="times" size={moderateScale(20)} color={colors.RED3} />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* nút chụp ảnh theo đối tượng */}
        {profileData.ho_so.hien_thi_button != 1 && props.toggleModalChonDoiTuong && !prevScreenIsHoSoDiaBan && (
          <View style={{alignItems: 'center', justifyContent: 'center'}}>
            <TouchableOpacity style={styles.iconActionBenThamGiamDinhView} onPress={() => props.setDoiTuongDuocChon({...item})}>
              <Icon.FontAwesome name="camera" size={moderateScale(20)} color={colors.BLACK} />
            </TouchableOpacity>
          </View>
        )}
      </TouchableOpacity>
    );
  };
  //ITEM LINK CHỤP ẢNH
  const renderLinkChupAnhItem = (title, link, type) => (
    <View style={styles.inforView}>
      <Text style={styles.linkChupAnhTitle} children={title} />
      <View style={styles.linkChupAnhActionView}>
        <TouchableOpacity onPress={() => onPressShareLink(type, link)} style={styles.linkChupAnhAction}>
          <Icon.FontAwesome name="share-alt" size={moderateScale(20)} color={colors.PRIMARY} />
          <Text style={[styles.txtDetail, {color: '#3355b4', paddingRight: 0}]}>Chia sẻ</Text>
        </TouchableOpacity>
        {type !== 2 && type !== 3 && (
          <TouchableOpacity onPress={() => onPressGuiTinNhan(type, link)} style={styles.linkChupAnhAction}>
            <Icon.MaterialCommunityIcons name="android-messages" size={moderateScale(20)} color={colors.PRIMARY} />
            <Text style={[styles.txtDetail, {color: '#3355b4', paddingRight: 0}]}>Gửi SMS</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity onPress={() => onPressSaoChepLink(type, link)} style={styles.linkChupAnhAction}>
          <Icon.FontAwesome name="copy" size={moderateScale(20)} color={colors.PRIMARY} />
          <Text style={[styles.txtDetail, {color: '#3355b4', paddingRight: 0}]}>Sao chép</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
  // MODAL CONTENT LINK CHỤP ẢNH
  const renderLinkChupAnh = () => {
    if (!profileData) return;
    const {ho_so} = profileData;
    return (
      <>
        {ho_so.link_chup_anh_ht && !prevScreenIsHoSoDiaBan && renderLinkChupAnhItem('Link chụp ảnh hiện trường', ho_so.link_chup_anh_ht, 0)}
        {ho_so.link_chup_anh_ct && !prevScreenIsHoSoDiaBan && renderLinkChupAnhItem('Link chụp ảnh chi tiết', ho_so.link_chup_anh_ct, 1)}
        {ho_so.link_chup_anh_nghiem_thu && !prevScreenIsHoSoDiaBan && renderLinkChupAnhItem('Link chụp ảnh nghiệm thu', ho_so.link_chup_anh_nghiem_thu, 2)}
        {ho_so.link_bo_sung_hs && !prevScreenIsHoSoDiaBan && renderLinkChupAnhItem('Link chụp ảnh bổ sung hồ sơ', ho_so.link_bo_sung_hs, 3)}
      </>
    );
  };

  const renderItemModal = ({item}) => {
    // Bên tham gia giám định
    if (modalTitle === HEADER_TITLE[5]) return renderJoinResolveItem(item);
    //danh đối tượng tổn thất
    else if (modalTitle === HEADER_TITLE[6] || props.toggleModalChonDoiTuong) return renderDoiTuongTonThatItem(item);
  };

  const renderModal = () => {
    const modalHeight = modalTitle === HEADER_TITLE[15] ? dimensions.height / 2 : (dimensions.height * 3) / 4;
    return (
      <Modal
        isVisible={toggleModal}
        onSwipeComplete={onPressTatModal}
        onBackdropPress={onPressTatModal}
        // swipeDirection={['down']}
        scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
        scrollOffset={scrollOffSet}
        scrollOffsetMax={modalHeight - flatListHeight} // content height - ScrollView height
        propagateSwipe={true}
        onBackButtonPress={onPressTatModal}
        style={styles.modal}>
        <View style={[styles.modalView, {height: modalHeight}]}>
          <View style={styles.modalTitleView}>
            <Text style={styles.modalTitle}>{modalTitle}</Text>
            <TouchableOpacity style={styles.closeView} onPress={onPressTatModal}>
              <Icon.AntDesign name="closecircleo" size={moderateScale(20)} />
            </TouchableOpacity>
          </View>

          <ScrollView ref={scrollViewModalRef} onScroll={(event) => setScrollOffSet(event.nativeEvent.contentOffset.y)} scrollEventThrottle={16} showsVerticalScrollIndicator={false}>
            <View onLayout={(event) => setFlatListHeight(event.nativeEvent.layout.height)}>
              {modalTitle === HEADER_TITLE[15] && renderLinkChupAnh()}
              {modalTitle === 'Chọn đối tượng chụp ảnh' && modalData.length === 1 && (
                <View style={styles.warningDoiTuongTonThatView}>
                  <Icon.Ionicons name="warning" color={colors.ORANGE} size={20} />
                  <Text children="Chưa tồn tại đối tượng TỔN THẤT. Vui lòng tạo đối tượng TỔN THẤT" style={styles.txtWarningDoiTuongTonThat} font="regular12" />
                </View>
              )}
              {modalTitle !== HEADER_TITLE[15] && (
                <FlatList
                  data={modalData}
                  style={{marginBottom: 70}}
                  renderItem={renderItemModal}
                  keyExtractor={(item, index) => index.toString()}
                  ListEmptyComponent={
                    modalTitle === 'Chọn đối tượng chụp ảnh' && (
                      <Empty
                        addMore
                        textAddMore="Thêm đối tượng"
                        description="Chưa có đối tượng tổn thất!"
                        imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}}
                        onPress={() => onPressThemDoiTuong()}
                      />
                    )
                  }
                />
              )}
            </View>
          </ScrollView>
        </View>

        {((modalTitle === 'Chọn đối tượng chụp ảnh' && modalData.length === 1) ||
          ((modalTitle === HEADER_TITLE[5] || modalTitle === HEADER_TITLE[6]) && profileData.ho_so.hien_thi_button != 1 && !props.toggleModalChonDoiTuong && !prevScreenIsHoSoDiaBan)) && (
          <View style={[styles.btnThemDoiTuongTonThat, {paddingBottom: insect.bottom}]}>
            <ButtonLinear
              title={modalTitle === HEADER_TITLE[5] ? 'Thêm bên tham gia giám định' : 'Thêm đối tượng'}
              onPress={() => {
                setToggleModal(false);
                NavigationUtil.push(modalTitle === HEADER_TITLE[5] ? SCREEN_ROUTER_APP.JOIN_RESOLVE : SCREEN_ROUTER_APP.DOI_TUONG_TON_THAT, {
                  profileData,
                });
              }}
            />
          </View>
        )}
      </Modal>
    );
  };
  const renderActionSheetMauIn = () => (
    <ActionSheet
      ref={(o) => (actionSheetRef = o)}
      title={'Chọn mẫu in'}
      options={actionSheetMauInData}
      cancelButtonIndex={actionSheetMauInData.length - 1}
      destructiveButtonIndex={actionSheetMauInData.length - 1}
      onPress={(index) => onPressXemChiTietMauIn(index, 0)}
    />
  );

  // const renderActionSheetXacNhanBienBan = () => (
  //   <ActionSheet
  //     ref={(o) => (actionSheetXacNhanBienBanRef = o)}
  //     title={'Chọn mẫu in'}
  //     options={actionSheetMauInXacNhanBienBanData}
  //     cancelButtonIndex={actionSheetMauInXacNhanBienBanData.length - 1}
  //     destructiveButtonIndex={actionSheetMauInXacNhanBienBanData.length - 1}
  //     onPress={(index) => onPressXemChiTietMauIn(index, 1)}
  //   />
  // );

  const renderActionSheetBoSungThongTinGiayTo = () => (
    <ActionSheet
      ref={(o) => (actionSheetBoSungThongTinGiayToRef = o)}
      title="Bổ sung thông tin"
      options={['Bằng lái xe', 'Đăng kiểm', 'Để sau']}
      cancelButtonIndex={2}
      destructiveButtonIndex={2}
      onPress={(index) => onPressBoSungThongTinGiayToXe(index)}
    />
  );

  return (
    <>
      {renderProfileInformation()}
      {renderModal()}
      {renderActionSheetMauIn()}
      {/* {renderActionSheetXacNhanBienBan()} */}
      {renderActionSheetBoSungThongTinGiayTo()}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    marginTop: vScale(5),
    marginBottom: vScale(60),
    flex: 1,
  },
  iconBtnTopLeftView: {
    alignSelf: 'center',
    marginRight: scale(spacing.small),
  },
  iconBtnTopRightView: {
    alignSelf: 'center',
    marginHorizontal: scale(spacing.small),
  },
  headerCollap: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE5,
    paddingHorizontal: scale(spacing.small),
  },
  txtTitle: {
    fontSize: FontSize.size12,
    marginBottom: vScale(spacing.tiny),
  },
  txtDetail: {
    flexShrink: 1,
    color: colors.GRAY6,
    paddingRight: scale(10),
  },

  txtAccidentDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
  },
  inforView: {
    flex: 1,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY4,
    backgroundColor: colors.WHITE,
    paddingLeft: scale(spacing.small),
    paddingVertical: vScale(spacing.smaller),
  },
  inforHeaderView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: dimensions.width - 20,
    backgroundColor: colors.WHITE5,
    justifyContent: 'space-between',
  },
  accidentHeader: {
    flexDirection: 'row',
    paddingVertical: vScale(spacing.small),
    justifyContent: 'space-between',
    paddingHorizontal: scale(spacing.small),
  },
  txtTime: {
    fontSize: FontSize.size12,
  },
  accidentView: {
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
  },
  txtLocation: {
    fontSize: FontSize.size12,
    fontWeight: 'bold',
    color: colors.GREEN2,
    marginBottom: vScale(spacing.smaller),
  },
  accidentInforView: {
    paddingHorizontal: scale(spacing.small),
    paddingVertical: vScale(spacing.small),
  },
  resolveAccidentInforView: {
    paddingHorizontal: scale(spacing.small),
  },
  resolveAccidentView: {
    borderBottomWidth: 0.5,
    paddingBottom: vScale(10),
    borderColor: colors.GRAY4,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: (dimensions.height * 3) / 4,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: FontSize.size18,
    marginVertical: vScale(spacing.smaller),
  },
  closeView: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    justifyContent: 'center',
    marginRight: scale(spacing.small),
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalTitleView: {
    height: vScale(50),
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    justifyContent: 'center',
  },
  joinResolveDetailView: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: vScale(spacing.tiny),
  },
  joinResolveView: {
    marginTop: scale(spacing.small),
    borderBottomWidth: 2,
    flexDirection: 'row',
    borderColor: colors.GRAY4,
    paddingHorizontal: scale(spacing.small),
  },
  btnAddJoinResolve: {
    borderWidth: 3,
    right: scale(10),
    borderRadius: 100,
    bottom: vScale(10),
    position: 'absolute',
    borderColor: colors.PRIMARY,
    backgroundColor: colors.WHITE,
  },
  iconAddJoinResolve: {
    paddingVertical: vScale(14),
    paddingHorizontal: scale(12),
  },
  linkChupAnhActionView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  linkChupAnhAction: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#3355b4',
    width: dimensions.width / 4,
    borderRadius: 10,
    paddingVertical: vScale(5),
    marginTop: vScale(10),
  },
  linkChupAnhTitle: {
    fontWeight: 'bold',
    textDecorationLine: 'underline',
    marginBottom: vScale(spacing.tiny),
  },
  laySoHoSoView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionBenThemGiaGiamDinhView: {
    justifyContent: 'space-around',
  },
  iconActionBenThamGiamDinhView: {
    paddingHorizontal: scale(spacing.small),
    paddingVertical: vScale(spacing.small),
  },
  spacing: {
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
  },
  detailView: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  txtDoiTuongGoc: {
    color: colors.RED1,
    fontStyle: 'italic',
  },
  warningDoiTuongTonThatView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    marginTop: spacing.small,
  },
  txtWarningDoiTuongTonThat: {
    color: colors.ORANGE,
    paddingRight: spacing.small,
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    maxWidth: dimensions.width - dimensions.width * 0.3,
  },
  btnThemDoiTuongTonThat: {
    left: 0,
    right: 0,
    bottom: 10,
    position: 'absolute',
    borderColor: colors.PRIMARY,
    backgroundColor: colors.WHITE,
    marginHorizontal: spacing.small,
  },
});

export const TabThongTinHoSoOTo = memo(TabThongTinHoSoOToComponent, isEqual);
