import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TextInput, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalNhapNoiDungHuyHSComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  const {onChangeText, value, onBackPress, onPressSave, type, inputError} = props;
  const [isVisible, setIsVisible] = useState(false);
  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Nhập nội dung" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  const _onPressSave = () => {
    onPressSave && onPressSave(type);
  };

  return (
    <Modal
      avoidKeyboard
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down', 'right']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        <TextInput
          autoFocus
          multiline
          placeholder="Nhập nội dung"
          style={[styles.searchInput, inputError && {borderColor: colors.RED1}]}
          value={value}
          onChangeText={onChangeText}
          placeholderTextColor="#CCC"
        />
        {inputError && <Text style={styles.errText}>Trường bắt buộc</Text>}
        <View style={styles.actButtonGr}>
          <TouchableOpacity style={[styles.actButton, {backgroundColor: colors.GRAY2}]} onPress={onBackPress}>
            <Text style={[styles.actButtonTxt, {color: colors.BLACK_03}]}>Huỷ</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actButton} onPress={() => _onPressSave()}>
            <Text style={styles.actButtonTxt}>Lưu</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height * 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
    // borderWidth: 1,
  },
  searchInput: {
    marginHorizontal: spacing.medium,
    marginBottom: spacing.tiny,
    marginTop: spacing.medium,
    height: 120,
    padding: 10,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.GRAY,
    textAlignVertical: 'top',
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    padding: 5,
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY2,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
  actButtonGr: {
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  actButton: {
    borderRadius: 5,
    paddingVertical: 8,
    marginHorizontal: 8,
    paddingHorizontal: 30,
    backgroundColor: colors.PRIMARY,
  },
  actButtonTxt: {
    fontWeight: '600',
    color: colors.WHITE,
  },
  errText: {
    color: colors.RED1,
    marginHorizontal: spacing.medium,
  },
});

export const ModalNhapNoiDungHuyHS = memo(ModalNhapNoiDungHuyHSComponent, isEqual);
