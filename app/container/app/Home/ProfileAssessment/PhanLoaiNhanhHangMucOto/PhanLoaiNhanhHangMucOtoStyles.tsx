import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  headerView: {
    paddingHorizontal: spacing.small,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
  },
  imageDocument: {
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    // marginVertical: vScale(spacing.small),
    // marginHorizontal: scale(spacing.small),
    marginRight: spacing.default,
  },
  headerItemHangMucView: {
    paddingHorizontal: spacing.small,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitleView: {
    flex: 1,
    justifyContent: 'space-between',
    // flexDirection: 'row',
  },
  headerSubTitle: {
    marginRight: spacing.tiny,
  },
  itemPhanLoaiNhanhView: {
    paddingVertical: spacing.small,
    borderBottomWidth: 0.5,
    borderColor: '#CCC',
  },
  txtDoiTuong: {
    fontStyle: 'italic',
    marginTop: spacing.smaller,
  },
});
