import R from '@R';
import {DATA_CONSTANT, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectCategoryCommon, selectMucDoTonThat, selectType1} from '@app/redux/slices/CategoryCommonSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {logErrorTryCatch} from '@app/utils';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, Empty, Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FlatList, Image, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Progress from 'react-native-progress/Circle';
import {useSelector} from 'react-redux';
import styles from './PhanLoaiNhanhHangMucOtoStyles';

const PhanLoaiNhanhHangMucOtoScreenComponent = ({route, navigation}) => {
  console.log('PhanLoaiNhanhHangMucOtoScreen');
  const {profileData} = route.params;
  const hangMucOto = useSelector(selectType1);
  const categoryCommon = useSelector(selectCategoryCommon);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [listHangMucPhanLoai, setListHangMucPhanLoai] = useState([]);
  const [expandAllHangMuc, setExpandAllHangMuc] = useState(false);
  const [viTriHangMucDangTai, setViTriHangMucDangTai] = useState(null);

  //dropdown hạng mục
  const [indexDropdownHangMucOpen, setIndexDropdownHangMucOpen] = useState(null);
  const [hangMucDropdownSelected, setHangMucDropdownSelected] = useState(null);
  const [timeoutId, setTimeoutId] = useState(null);
  const [listHangMuc, setListHangMuc] = useState(hangMucOto.slice(0, 50));

  //dropdown mức độ
  const [indexDropdownMucDoOpen, setIndexDropdownMucDoOpen] = useState(null);
  const [mucDoDropdownSelected, setMucDoDropdownSelected] = useState(null);
  const listMucDo = useSelector(selectMucDoTonThat).filter((item) => item.nhom === DATA_CONSTANT.NHOM_MUC_DO_TON_THAT.XE);
  const [daLuu, setDaLuu] = useState(null);

  useEffect(() => {
    initDataListHangMucPhanLoai();
    //xử lý nút Back
    let backHandler;
    navigation.addListener('focus', () => (backHandler = BackHandler.addEventListener('hardwareBackPress', backAction)));
    navigation.addListener('blur', () => backHandler?.remove());
  }, []);

  //hạng mục dropdown
  useEffect(() => {
    try {
      if (hangMucDropdownSelected && indexDropdownHangMucOpen !== null) {
        let listHangMucPhanLoaiTmp = listHangMucPhanLoai;
        listHangMucPhanLoaiTmp[indexDropdownHangMucOpen].hang_muc.hang_muc = hangMucDropdownSelected;
        // listHangMucPhanLoaiTmp[indexDropdownHangMucOpen].hang_muc.ten_hang_muc_moi = hangMucOto.find((item) => item.ma === hangMucDropdownSelected).ten;
        listHangMucPhanLoaiTmp[indexDropdownHangMucOpen].thayDoi = true;
        setListHangMucPhanLoai([...listHangMucPhanLoaiTmp]);
        if (listHangMuc.length < 10) setListHangMuc(hangMucOto.slice(0, 50));
        setHangMucDropdownSelected(null);
        if (daLuu !== false) setDaLuu(false);
      }
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  }, [hangMucDropdownSelected, indexDropdownHangMucOpen]);

  useEffect(() => {
    if (hangMucDropdownSelected) setIndexDropdownHangMucOpen(null);
  }, [hangMucDropdownSelected]);
  //end hạng mục dropdown

  //mức độ dropdown
  useEffect(() => {
    try {
      if (mucDoDropdownSelected && indexDropdownMucDoOpen !== null) {
        let listHangMucPhanLoaiTmp = listHangMucPhanLoai;
        listHangMucPhanLoaiTmp[indexDropdownMucDoOpen].hang_muc.muc_do = mucDoDropdownSelected;
        listHangMucPhanLoaiTmp[indexDropdownMucDoOpen].thayDoi = true;
        setListHangMucPhanLoai([...listHangMucPhanLoaiTmp]);
        setMucDoDropdownSelected(null);
        setDaLuu(false);
        if (daLuu !== false) setDaLuu(false);
      }
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  }, [mucDoDropdownSelected, indexDropdownMucDoOpen]);

  useEffect(() => {
    if (mucDoDropdownSelected) setIndexDropdownMucDoOpen(null);
  }, [mucDoDropdownSelected]);
  //end mức độ dropdown

  const backAction = () => {
    if (daLuu === false) {
      return Alert.alert('Thông báo', 'Dữ liệu phân loại chưa được lưu vào hệ thống. Bạn có muốn thoát khỏi màn này', [
        {text: 'Ở lại'},
        {
          text: 'Thoát',
          style: 'destructive',
          onPress: () => NavigationUtil.pop(),
        },
      ]);
    } else NavigationUtil.pop();
  };

  // LIST HẠNG MỤC PHÂN LOẠI
  const initDataListHangMucPhanLoai = () => {
    let listHangMucPhanLoaiTmp = cloneObject(route.params.listHangMucPhanLoai);
    listHangMucPhanLoaiTmp.map((item) => (item.validate = null));
    setListHangMucPhanLoai(listHangMucPhanLoaiTmp);
  };

  const getListHangMucKhiDaThayDoi = (itemHangMuc) => {
    let listHangMucTmp = cloneObject(listHangMuc);
    //nếu hạng mục đấy không phải là hạng mục ANH_KHAC và chưa có hạng mục trong list thì mới unshift vào
    if (!listHangMucTmp.find((item) => item.ma === itemHangMuc.hang_muc.hang_muc) && !itemHangMuc.hang_muc.hang_muc.includes('ANH_KHAC')) {
      let hangMucObj = hangMucOto.find((item) => item.ma === itemHangMuc.hang_muc.hang_muc);
      listHangMucTmp.unshift(hangMucObj);
    }
    return listHangMucTmp;
  };

  const onPressExpandAllHangMuc = async (expandAllHangMuc) => {
    try {
      let imageDataStep3Tmp = [...listHangMucPhanLoai];
      for (let i = 0; i < imageDataStep3Tmp.length; i++) {
        let hangMucAnh = imageDataStep3Tmp[i];
        if (expandAllHangMuc === true) {
          if (hangMucAnh.images.length === 0) {
            setViTriHangMucDangTai(i);
            let response = await getTaiLieuBoiThuong(hangMucAnh.ma);
            response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +hangMucAnh.hang_muc.so_id_doi_tuong);
            let imagesTmp = response.map((item) => {
              item.checked = false;
              item.path = item.duong_dan;
              item.name = item.ten_file;
              let nhom = {
                checked: false,
                ma: item.ma_file,
                ten: item.nhom_anh,
                nhom_hang_muc: item.nhom_hang_muc,
              };
              item.nhom = nhom;
              return item;
            });
            imageDataStep3Tmp[i].images = imagesTmp;
          }
          imageDataStep3Tmp[i].expanded = true;
        } else if (expandAllHangMuc === false) imageDataStep3Tmp[i].expanded = false;
      }
      setViTriHangMucDangTai(null);
      setListHangMucPhanLoai([...imageDataStep3Tmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //ĐÓNG / MỞ 1 HẠNG MỤC
  const onPressToggleExpandHangMuc = async (index) => {
    try {
      //nếu có ảnh thì expand ra luôn nhưng vẫn gọi lại API để lấy ảnh
      let daExpand = false; //đã expand rồi thì bên dưới k cần gọi expand nữa
      let newValueExpand = null;
      if (listHangMucPhanLoai[index].images.length > 0) {
        daExpand = true;
        setListHangMucPhanLoai((prevValue) => {
          prevValue[index].expanded = !prevValue[index].expanded; //cập nhật giá trị expand mới
          newValueExpand = prevValue[index].expanded;
          return [...prevValue];
        });
      }
      //nếu là thu lại thì k gọi API
      if (newValueExpand === false) return;
      setViTriHangMucDangTai(index);
      let response = await getTaiLieuBoiThuong(listHangMucPhanLoai[index].ma);
      response = response.filter((itemImage) => itemImage.so_id_doi_tuong === +listHangMucPhanLoai[index].hang_muc.so_id_doi_tuong);
      setViTriHangMucDangTai(null);
      let imagesTmp = response.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
          nhom_hang_muc: item.nhom_hang_muc,
        };
        item.nhom = nhom;
        return item;
      });
      setListHangMucPhanLoai((prevValue) => {
        prevValue[index].images = imagesTmp;
        if (!daExpand) prevValue[index].expanded = !prevValue[index].expanded; //bên trên chưa expand thì mới cần expand ra
        return [...prevValue];
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  //click vào 1 ảnh trong Tài liệu bồi thường
  const onPressOpenImageView = (currentImageData, listAnhXem) => {
    try {
      NavigationUtil.push(SCREEN_ROUTER_APP.IMAGES_VIEW, {
        currentImageData,
        imagesData: listAnhXem,
      });
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  // GET ẢNH TÀI LIỆU THEO MÃ
  const getTaiLieuBoiThuong = async (ma_file) => {
    return new Promise(async (resolve, reject) => {
      try {
        //lấy thông tin của ảnh giám định
        let params = {so_id: profileData.ho_so.so_id, ma_file};
        let responseFileThumbnail = await ESmartClaimEndpoint.getFileThumbnail(axiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, params);
        setDialogLoading(false);
        if (!responseFileThumbnail || !responseFileThumbnail.state_info || responseFileThumbnail.state_info.status !== 'OK') return resolve([]);
        resolve(responseFileThumbnail.data_info);
      } catch (error) {
        setDialogLoading(false);
        Alert.alert('Thông báo', error.message);
        resolve([]);
      }
    });
  };

  const onChangeSearchTextHangMuc = (textSearch) => {
    try {
      if (!textSearch.trim()) {
        setListHangMuc([...hangMucOto.slice(0, 50)]);
        clearTimeout(timeoutId);
        return;
      }
      clearTimeout(timeoutId);
      let timeoutIdTmp = setTimeout(() => {
        let result = [];
        /*search by text*/
        // for (let i = 0; i < categoryCommon.type1.length; i++) {
        //   if (categoryCommon.type1[i].label.toUpperCase().indexOf(textSearch.toUpperCase()) > -1) result.push(categoryCommon.type1[i]);
        // }

        // let arrTextSearch = textSearch.trim().split(' ');
        // arrTextSearch = arrTextSearch.filter((item) => item !== '');
        if (categoryCommon.type1.length === 0) Alert.alert('Thông báo', 'Danh sách hạng mục rỗng');
        let nhomHangMucFilter = [];
        nhomHangMucFilter = categoryCommon.type1.filter((item) => item.loai === 'CHINH' || item.loai === 'BT_TOAN_BO');
        // if (doiTuongDuocChupAnh.nhom === 'TAI_SAN' && doiTuongDuocChupAnh.loai === 'XE_MAY') nhomHangMucFilter = categoryCommon?.listHangMucXeMay.filter((item) => item.loai === 'CHINH');
        // else if (doiTuongDuocChupAnh.hang_muc === 'XE') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.loai === 'CHINH' || item.loai === 'BT_TOAN_BO');
        // else if (doiTuongDuocChupAnh.hang_muc === 'TAI_SAN' || doiTuongDuocChupAnh.hang_muc === 'HANG_HOA') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.nhom === 'TAI_SAN');
        // else nhomHangMucFilter = categoryCommon.type1.filter((item) => item.ma === doiTuongDuocChupAnh.hang_muc);

        const lowerCaseSearchText = textSearch?.toLowerCase();
        result = nhomHangMucFilter.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));

        setListHangMuc([...result]);
      }, 500);
      setTimeoutId(timeoutIdTmp);
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo tìm kiếm hạng mục', JSON.stringify(error));
    }
  };

  const onPressDropdownHangMuc = (index) => {
    // if (doiTuongDuocChupAnh.nhom === 'TAI_SAN' && doiTuongDuocChupAnh.loai === 'XE_MAY') nhomHangMucFilter = categoryCommon?.listHangMucXeMay.filter((item) => item.loai === 'CHINH');
    // else if (doiTuongDuocChupAnh.hang_muc === 'XE') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.loai === 'CHINH' || item.loai === 'BT_TOAN_BO');
    // else if (doiTuongDuocChupAnh.hang_muc === 'TAI_SAN' || doiTuongDuocChupAnh.hang_muc === 'HANG_HOA') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.nhom === 'TAI_SAN');
    // else nhomHangMucFilter = categoryCommon.type1.filter((item) => item.ma === doiTuongDuocChupAnh.hang_muc);
    setIndexDropdownHangMucOpen(index === indexDropdownHangMucOpen ? (indexDropdownHangMucOpen === null ? index : null) : index);
  };

  const onPressDropdownMucDo = (index) => {
    // if (doiTuongDuocChupAnh.nhom === 'TAI_SAN' && doiTuongDuocChupAnh.loai === 'XE_MAY') nhomHangMucFilter = categoryCommon?.listHangMucXeMay.filter((item) => item.loai === 'CHINH');
    // else if (doiTuongDuocChupAnh.hang_muc === 'XE') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.loai === 'CHINH' || item.loai === 'BT_TOAN_BO');
    // else if (doiTuongDuocChupAnh.hang_muc === 'TAI_SAN' || doiTuongDuocChupAnh.hang_muc === 'HANG_HOA') nhomHangMucFilter = categoryCommon.type1.filter((item) => item.nhom === 'TAI_SAN');
    // else nhomHangMucFilter = categoryCommon.type1.filter((item) => item.ma === doiTuongDuocChupAnh.hang_muc);
    setIndexDropdownMucDoOpen(index === indexDropdownMucDoOpen ? (indexDropdownMucDoOpen === null ? index : null) : index);
  };

  const onPressLuuPhanLoai = async () => {
    try {
      let arrHangMuc = [];
      let arrHangMucCu = [];
      let arrHangMucMoi = [];
      let arrMucDo = [];
      let arrIdDoiTuong = [];
      let listHangMucPhanLoaiTmp = listHangMucPhanLoai;
      let haveErr = false;
      let arrIndexErr = [];
      listHangMucPhanLoaiTmp.map((item, index) => {
        if (item.thayDoi === true) {
          //chỉ lấy những hạng mục đã chọn hạng mục
          if (!item.hang_muc.hang_muc.includes('ANH_KHAC')) {
            arrHangMuc.push({
              hang_muc_cu: item.ma,
              hang_muc_moi: item.hang_muc.hang_muc,
              muc_do: item.hang_muc.muc_do,
              so_id_doi_tuong: item.hang_muc.so_id_doi_tuong,
            });
            arrHangMucCu.push(item.ma);
            arrHangMucMoi.push(item.hang_muc.hang_muc); //trường hợp item.ma khi user chỉ chọn mức độ thôi, không chọn hạng mục
            arrMucDo.push(item.hang_muc.muc_do);
            arrIdDoiTuong.push(item.hang_muc.so_id_doi_tuong);
          }
          //nếu đã đánh giá hạng mục mới, nhưng chưa đánh giá mức độ -> hiển thị lỗi
          if (!item.hang_muc.hang_muc.includes('ANH_KHAC') && !item.hang_muc.muc_do) {
            arrIndexErr.push(index + 1);
            item.validate = false;
            haveErr = true;
          }
        }
        return item;
      });
      if (haveErr) {
        setListHangMucPhanLoai([...listHangMucPhanLoaiTmp]);
        return Alert.alert('Thông báo', 'Vui lòng chọn đầy đủ thông tin hạng mục thứ ' + arrIndexErr.join(', '));
      }

      setDialogLoading(true);
      let params = {
        so_id: profileData.ho_so.so_id,
        arr: arrHangMuc,
        // hang_muc_cu: arrHangMucCu, hang_muc_moi: arrHangMucMoi, so_id_doi_tuong: arrIdDoiTuong, muc_do: arrMucDo
      };
      console.log('params', params);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.PHAN_LOAI_NHANH_HANG_MUC_O_TO, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thành công', 'Phân loại hạng mục thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      logErrorTryCatch(error);
      Alert.alert('Thông báo', error.message);
    }
  };

  /**RENDER  */
  const renderItemAnh = ({item, index}, {listImage}) => {
    let source = {uri: `data:image/gif;base64,${item.duong_dan}`};
    return (
      <TouchableOpacity onPress={() => onPressOpenImageView({item, index}, listImage)}>
        <ImageProcess
          source={source}
          indicator={Progress.Circle}
          style={styles.imageDocument}
          imageStyle={{borderRadius: 20}}
          indicatorProps={{
            size: 70,
            borderWidth: 0,
            color: colors.PRIMARY,
            unfilledColor: colors.PRIMARY_LIGHT,
          }}
          renderError={() => (
            <View>
              <Image source={R.images.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />
            </View>
          )}
        />
      </TouchableOpacity>
    );
  };
  const renderItemPhanLoaiNhanh = ({item, index}) => {
    return (
      <View style={[{backgroundColor: index % 2 == 0 ? colors.WHITE8 : '#FFF'}, styles.itemPhanLoaiNhanhView]}>
        <View style={styles.headerItemHangMucView}>
          <View style={styles.headerTitleView}>
            <TouchableOpacity onPress={() => onPressToggleExpandHangMuc(index)} style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text style={styles.headerSubTitle} font="medium14" color={'#000'} children={index + 1 + '. ' + item.ten} />
            </TouchableOpacity>
            {profileData.ds_doi_tuong.length > 1 && (
              <TouchableOpacity onPress={() => onPressToggleExpandHangMuc(index)}>
                <Text style={styles.txtDoiTuong} font="regular12" children={item.tenDoiTuong} />
              </TouchableOpacity>
            )}
          </View>

          {viTriHangMucDangTai === index && <ActivityIndicator size="small" color={colors.PRIMARY} />}

          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            {/* {valiateItemPhanLoai && <Icon.AntDesign name="checkcircleo" color={colors.GREEN} size={16} />} */}
            {item.validate === false && (
              <TouchableOpacity onPress={() => FlashMessageHelper.showFlashMessage('Thông báo', 'Hạng mục chưa chọn đủ thông tin', 'warning')}>
                <Icon.AntDesign name="warning" color={colors.ORANGE} size={16} />
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={() => onPressToggleExpandHangMuc(index)} style={{paddingHorizontal: spacing.small}}>
              <Icon.MaterialIcons name={'photo'} size={20} color={colors.PRIMARY} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={{flexDirection: 'row', marginHorizontal: spacing.default}}>
          <DropdownPicker
            zIndex={6000}
            items={item.thayDoi || !item.hang_muc.hang_muc.includes('ANH_KHAC') ? getListHangMucKhiDaThayDoi(item) : listHangMuc}
            itemSelected={item.hang_muc.hang_muc.includes('ANH_KHAC') ? '' : item.hang_muc.hang_muc}
            setItemSelected={setHangMucDropdownSelected}
            isOpen={index === indexDropdownHangMucOpen ? true : false}
            placeholder="Hạng mục"
            disableLocalSearch={true}
            onChangeSearchText={onChangeSearchTextHangMuc}
            maxHeight={200}
            schema={{
              label: 'ten',
              value: 'ma',
            }}
            onPress={() => onPressDropdownHangMuc(index)}
            containerStyle={{marginRight: spacing.default, marginBottom: index === indexDropdownHangMucOpen ? 200 : spacing.default, flex: 1.7}}
          />
          <DropdownPicker
            zIndex={6000}
            items={listMucDo}
            itemSelected={item.hang_muc?.muc_do}
            setItemSelected={setMucDoDropdownSelected}
            isOpen={index === indexDropdownMucDoOpen ? true : false}
            placeholder="Mức độ"
            maxHeight={200}
            schema={{
              label: 'ten',
              value: 'ma',
            }}
            searchable={false}
            onPress={() => onPressDropdownMucDo(index)}
            containerStyle={{marginBottom: index === indexDropdownMucDoOpen ? 200 : spacing.default, flex: 1}}
          />
        </View>
        {/* CHỈ HIỂN THỊ LIST ẢNH VỚI TRƯỜNG HỢP item.expanded(ẢNH HỒ SƠ, ẢNH TỔN THẤT CÓ PROS NÀY) VÀ CÁC LOẠI ẢNH KHÁC ANH_TON_THAT VÀ ANH_HO_SO */}
        {item.expanded && (
          <FlatList
            data={item.images}
            renderItem={(itemImage) => renderItemAnh(itemImage, {listImage: item.images})}
            keyExtractor={(itemImage) => itemImage.bt.toString()}
            horizontal={true}
            style={{marginLeft: spacing.default}}
          />
        )}
      </View>
    );
  };

  const renderPhanLoaiNhanhView = () => {
    return (
      <FlatList
        data={listHangMucPhanLoai}
        renderItem={(item) => renderItemPhanLoaiNhanh(item)}
        initialNumToRender={50}
        ListEmptyComponent={<Empty />}
        contentContainerStyle={{paddingBottom: 200}}
        style={{marginBottom: spacing.default}}
        ListHeaderComponent={
          <TouchableOpacity
            style={styles.headerView}
            onPress={() => {
              setExpandAllHangMuc(!expandAllHangMuc);
              onPressExpandAllHangMuc(!expandAllHangMuc);
            }}>
            <Text style={styles.headerTitle} font="medium16" children={expandAllHangMuc ? 'Đóng tất cả' : 'Xem tất cả'} />
            <View style={{padding: spacing.small}}>
              <Icon.MaterialIcons name={'photo-library'} size={20} color={colors.PRIMARY} />
            </View>
          </TouchableOpacity>
        }
      />
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      onPressBack={backAction}
      headerBack
      headerTitle="Phân loại nhanh"
      renderView={
        <View style={[styles.container]}>
          <KeyboardAwareScrollView contentContainerStyle={{flex: 1}}>{renderPhanLoaiNhanhView()}</KeyboardAwareScrollView>
        </View>
      }
      footer={<ButtonLinear title="Lưu phân loại" onPress={onPressLuuPhanLoai} />}
    />
  );
};

export const PhanLoaiNhanhHangMucOtoScreen = memo(PhanLoaiNhanhHangMucOtoScreenComponent, isEqual);
