import {colors} from '@app/commons/Theme';
import {scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  contentView: {
    flex: 1,
    marginHorizontal: scale(15),
    zIndex: 8000,
  },
  dropDownView: {
    marginVertical: vScale(10),
  },
  dropDownTitle: {
    marginBottom: vScale(5),
    fontWeight: 'bold',
  },
  inputTitle: {
    marginBottom: vScale(5),
    fontWeight: 'bold',
  },
  linearBtnView: {
    borderRadius: 30,
    backgroundColor: colors.WHITE,
    marginBottom: vScale(20),
  },
  btnReceivingRecords: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: scale(10),
    minHeight: 50,
    borderRadius: 10,
  },
  txtBtnReceivingRecords: {
    color: colors.WHITE,
    fontWeight: 'bold',
  },
  textInputType: {
    borderWidth: 1,
    borderRadius: 5,
    borderColor: colors.GRAY,
    paddingLeft: 15,
    color: colors.BLACK,
    backgroundColor: colors.WHITE,
  },
  textInputView: {
    marginBottom: vScale(10),
  },
  optionLoaiGD: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: vScale(spacing.smaller),
  },
  footerView: {
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
    paddingHorizontal: scale(spacing.small),
  },
});
