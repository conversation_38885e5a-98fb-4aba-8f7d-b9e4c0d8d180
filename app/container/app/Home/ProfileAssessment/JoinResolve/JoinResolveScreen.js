import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {scale, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CheckboxComp, DropdownPicker, ScreenComponent, Text, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {connect} from 'react-redux';
import styles from './JoinResolveStyles';
import {REGUlAR_EXPRESSION} from '@app/commons/Constant';

const OPTION_THAM_GIA_GD = [
  {label: 'Tham gia giám định hiện trường và chi tiết', value: 'CA_HAI'},
  {label: 'Chỉ tham gia giám định hiện trường', value: 'GDHT'},
  {label: 'Chỉ tham gia giám định chi tiết', value: 'GDCT'},
];
const titleInput = ['Vai trò người đại diện', 'Họ tên người đại diện', 'Địa chỉ', 'Điện thoại', 'Email'];

const JoinResolveScreenComponent = (props) => {
  console.log('JoinResolveScreenComponent-BenThamGiaGiamDinhOto');
  const {route, categoryCommon} = props;

  let hoTenRef = useRef();
  let diaChiRef = useRef();
  let dienThoaiRef = useRef();
  let emailRef = useRef();

  const [profileData, setProfileData] = useState(route.params.profileData);
  const [joinResolveData, setJoinResolveData] = useState(null);

  const [openUserRole, setOpenUserRole] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [userRoleSelected, setUserRoleSelected] = useState('');
  const [userRoleData, setUserRoleData] = useState([]);

  const [nameInput, setNameInput] = useState(''); //INPUT CHI PHÍ DO GDV XÁC ĐỊNH
  const [addressInput, setAddressInput] = useState(''); //INPUT CHI PHÍ DO GDV XÁC ĐỊNH
  const [phoneInput, setPhoneInput] = useState(''); //INPUT CHI PHÍ DO GDV XÁC ĐỊNH
  const [emailInput, setEmailInput] = useState(''); //INPUT CHI PHÍ DO GDV XÁC ĐỊNH
  const [thamGiaGd, setThamGiaGd] = useState(OPTION_THAM_GIA_GD[0].value); //INPUT CHI PHÍ DO GDV XÁC ĐỊNH
  const [option, setOption] = useState(OPTION_THAM_GIA_GD); //INPUT CHI PHÍ DO GDV XÁC ĐỊNH
  const [errPhone, setErrPhone] = useState('');

  const [openLanGiamDinh, setOpenLanGiamDinh] = useState(false);
  const [lanGiamDinhSelected, setLanGiamDinnhSelected] = useState(joinResolveData ? joinResolveData.lan_gd : '');
  const [listLanGiamDinh, setListLanGiamDinh] = useState([]);

  useEffect(() => {
    setProfileData(route.params.profileData);
    if (route.params.joinResolveData) {
      setJoinResolveData(route.params.joinResolveData);
      setNameInput(route.params.joinResolveData.ten);
      setAddressInput(route.params.joinResolveData.dia_chi);
      setPhoneInput(route.params.joinResolveData.dien_thoai);
      setEmailInput(route.params.joinResolveData.email);
    }
    initDataCategoryData();
    initThamGiaGd();
    initLanGiamDinh();
  }, [route.params]);

  const initThamGiaGd = () => {
    let selectedItem = route.params.joinResolveData;
    let newArr = OPTION_THAM_GIA_GD;
    if (profileData?.ho_so?.hien_truong === 'K') {
      newArr = OPTION_THAM_GIA_GD.filter((item) => item.value === 'GDCT');
    } else newArr = OPTION_THAM_GIA_GD;
    newArr.map((e, i) => {
      newArr[i].isChecked = false;
      if (selectedItem && selectedItem.tham_gia_gd === e.value) {
        newArr[i].isChecked = true;
        setThamGiaGd(newArr[i].value);
      }
      if (selectedItem === undefined || selectedItem.tham_gia_gd === null) {
        newArr[0].isChecked = true;
        setThamGiaGd(newArr[0].value);
      }
    });
    setOption([...newArr]);
  };

  const initLanGiamDinh = async () => {
    setDialogLoading(true);
    try {
      let params = {
        so_id: profileData?.ho_so?.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_CAC_LAN_GIAM_DINH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrData = response.data_info;
      //nếu là tạo mới thì mới điền mặc định
      if (arrData.length === 1) !joinResolveData && setLanGiamDinnhSelected(arrData[0].lan_gd);
      arrData.map((e) => {
        e.ten_lan_gd = e.ten_lan_gd + ` (${e.ngay_gd})`;
      });
      setListLanGiamDinh(arrData);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
      setDialogLoading(false);
    }
  };

  const resetValue = () => {
    setNameInput('');
    setAddressInput('');
    setPhoneInput('');
    setEmailInput('');
  };

  const initDataCategoryData = async () => {
    if (route.params.joinResolveData) setUserRoleSelected(route.params.joinResolveData.dai_dien);
    setUserRoleData(categoryCommon.type2); //danh mục gốc
  };

  const closeDropdown = (title) => {
    setOpenUserRole(false);
  };

  const onPressSave = async () => {
    if (!phoneInput || phoneInput.trim() === '') return setErrPhone('Vui lòng nhập số điện thoại');
    if (!userRoleSelected || !nameInput.trim() || !lanGiamDinhSelected) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng nhập đầy đủ thông tin', 'info');

    setDialogLoading(true);
    try {
      let params = {
        ma_doi_tac: profileData.ho_so.ma_doi_tac,
        so_id: profileData.ho_so.so_id,
        bt: joinResolveData ? joinResolveData.bt : '',
        dai_dien: userRoleSelected,
        ten: nameInput,
        dia_chi: addressInput,
        dien_thoai: phoneInput,
        email: emailInput,
        tham_gia_gd: thamGiaGd,
        lan_gd: lanGiamDinhSelected,
      };

      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.UPDATE_JOIN_RESOLVE, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      NavigationUtil.pop();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeValueDropdownPicker = (title, items, value) => {
    // console.log('onChangeValueDropdownPicker', value);
    //người thông báo
    if (value == profileData.ho_so.moi_qh_tb) {
      setNameInput(profileData.ho_so.nguoi_tb);
      setPhoneInput(profileData.ho_so.dthoai_tb);
      setEmailInput(profileData.ho_so.email_tb);
    }
    //người liên hệ
    else if (value == profileData.ho_so.moi_qh_lhe) {
      setNameInput(profileData.ho_so.nguoi_lhe);
      setPhoneInput(profileData.ho_so.dthoai_lhe);
      setEmailInput(profileData.ho_so.email_lhe);
    } else {
      // resetValue();
    }
  };

  const onSelectOption = (val, idx) => {
    let newArr = OPTION_THAM_GIA_GD;
    newArr.map((e, index) => {
      if (idx === index) {
        newArr[idx].isChecked = true;
        setThamGiaGd(e.value);
      } else {
        newArr[index].isChecked = false;
      }
      setOption([...newArr]);
    });
  };

  const onChangeText = (value) => {
    if (!value || value === '') setErrPhone('Vui lòng nhập số điện thoại');
    else if (!REGUlAR_EXPRESSION.REG_PHONE.test(value)) setErrPhone('Số điện thoại sai định dạng');
    else if (REGUlAR_EXPRESSION.REG_PHONE.test(value)) setErrPhone('');
    setPhoneInput(value);
  };
  /**RENDER  */
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle={'Bên tham gia giám định'}
      renderView={
        <View style={styles.container}>
          <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} scrollEnabled={true}>
            <View style={styles.contentView}>
              <DropdownPicker
                title={'Lần giám định'}
                zIndex={9000}
                searchable={false}
                isOpen={openLanGiamDinh}
                setOpen={(value) => setOpenLanGiamDinh(value)}
                items={listLanGiamDinh}
                itemSelected={lanGiamDinhSelected}
                placeholder="Chọn lần giám định"
                // onSelectItem={onChangeValueDropdownPicker}
                containerStyle={{marginBottom: 10}}
                isRequired
                setItemSelected={setLanGiamDinnhSelected}
                inputErr={!lanGiamDinhSelected ? 'Thông tin bắt buộc' : ''}
                schema={{
                  label: 'ten_lan_gd',
                  value: 'lan_gd',
                }}
              />

              <DropdownPicker
                title={titleInput[0]}
                zIndex={8000}
                searchable={false}
                isOpen={openUserRole}
                setOpen={setOpenUserRole}
                items={userRoleData}
                itemSelected={userRoleSelected}
                setItemSelected={setUserRoleSelected}
                // onOpen={() => setOpenLoaiTaiSan(false)}
                placeholder="Chọn vai trò người đại diện"
                onChangeValue={onChangeValueDropdownPicker}
                // disabled={joinResolveData ? true : false}
                containerStyle={{marginBottom: 10}}
                isRequired={true}
              />
              {/* Họ tên */}
              <TextInputOutlined
                title={titleInput[1]}
                value={nameInput}
                onFocus={closeDropdown}
                onChangeText={setNameInput}
                placeholder={'Họ tên người đại diện'}
                isRequired={true}
                getRef={(ref) => (hoTenRef = ref)}
                onSubmitEditing={() => diaChiRef?.focus()}
                blurOnSubmit={false}
                returnKeyType={'next'}
              />
              {/* Địa chỉ */}
              <TextInputOutlined
                title={titleInput[2]}
                value={addressInput}
                onFocus={closeDropdown}
                onChangeText={setAddressInput}
                placeholder={'Địa chỉ'}
                getRef={(ref) => (diaChiRef = ref)}
                onSubmitEditing={() => dienThoaiRef?.focus()}
                blurOnSubmit={false}
                returnKeyType={'next'}
              />
              {/* Số điện thoại */}
              <TextInputOutlined
                title={titleInput[3]}
                value={phoneInput}
                onFocus={closeDropdown}
                onChangeText={(e) => onChangeText(e)}
                placeholder={'Điện thoại'}
                keyboardType="phone-pad"
                getRef={(ref) => (dienThoaiRef = ref)}
                onSubmitEditing={() => emailRef?.focus()}
                blurOnSubmit={false}
                returnKeyType={'next'}
                isRequired={true}
                error={errPhone}
              />
              {/* Email */}
              <TextInputOutlined
                title={titleInput[4]}
                value={emailInput}
                onFocus={closeDropdown}
                onChangeText={setEmailInput}
                placeholder={'Email'}
                keyboardType="email-address"
                getRef={(ref) => (emailRef = ref)}
              />
              {option.map((e, i) => {
                return (
                  <TouchableOpacity style={styles.optionLoaiGD} onPress={() => onSelectOption(e, i)}>
                    <CheckboxComp disabled value={e.isChecked} />
                    <Text style={{marginLeft: scale(spacing.smaller)}}>{e.label}</Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </KeyboardAwareScrollView>
          <View style={styles.footerView}>
            <ButtonLinear title="Lưu" onPress={onPressSave} linearStyle={{marginHorizontal: 0}} />
          </View>
        </View>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  categoryCommon: state.categoryCommon.data,
});

const mapDispatchToProps = {};

const JoinResolveScreenConnect = connect(mapStateToProps, mapDispatchToProps)(JoinResolveScreenComponent);
export const JoinResolveScreen = memo(JoinResolveScreenConnect, isEqual);
