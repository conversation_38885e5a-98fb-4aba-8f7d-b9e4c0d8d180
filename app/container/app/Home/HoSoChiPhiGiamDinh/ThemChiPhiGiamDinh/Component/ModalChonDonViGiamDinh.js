import {isAndroid} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {CheckboxComp, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Platform, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalChonDonViGiamDinhComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, data, setValue, value} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [dsChiNhanh, setDsChiNhanh] = useState([]);
  const [rootData, setRootData] = useState([]);
  const [searchText, setSearchText] = useState('');

  const initModalData = () => {
    let cloneData = data;
    cloneData.map((e, i) => {
      cloneData[i].isChecked = false;
      if (e.value === value) {
        cloneData[i].isChecked = true;
      }
    });
    setDsChiNhanh([...cloneData]);
    setRootData([...cloneData]);
  };

  const onChangeCheckBoxValue = (item, index) => {
    setValue && setValue(item);
    onBackPress && onBackPress();
  };

  const debounced = useDebouncedCallback((value) => {
    setSearchText(value);
  }, 300);

  useEffect(() => {
    if (searchText !== '') {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = rootData.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDsChiNhanh(filter);
    } else {
      setDsChiNhanh(rootData);
    }
  }, [searchText]);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn đơn vị giám định" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
        <SearchBar showSearchBtn={false} onTextChange={debounced} />
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    let isChecked = item.isChecked === true;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index)}>
        <CheckboxComp disabled value={isChecked} checkboxStyle={styles.checkbox} />
        <Text style={{color: isChecked ? colors.PRIMARY : colors.BLACK_03, marginTop: Platform.OS == 'android' ? 4 : 2}}>{item.label}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        style={[styles.content, {marginTop: 0}]}
        data={dsChiNhanh}
        renderItem={renderItem}
        keyExtractor={(e, i) => i.toString()}
        ListEmptyComponent={<Text style={styles.emptyTxt}>Chưa có dữ liệu</Text>}
      />
    );
  };
  return (
    <Modal
      ///onModalShow
      style={styles.modal}
      deviceHeight={dimensions.height}
      isVisible={isVisible}
      propagateSwipe={true}
      onModalWillShow={initModalData}
      swipeDirection={['down', 'right']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.modalView}>
        {renderHeader()}
        <KeyboardAwareScrollView>{renderContent()}</KeyboardAwareScrollView>
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: isAndroid ? dimensions.height * 0.5 : dimensions.height * 0.8,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    height: 45,
    borderWidth: 1,
    borderRadius: 8,
    margin: spacing.small,
    padding: spacing.smaller,
    borderColor: colors.GRAY,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 8,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
  emptyTxt: {
    textAlign: 'center',
    marginVertical: spacing.smaller,
  },
});

export const ModalChonDonViGiamDinh = memo(ModalChonDonViGiamDinhComponent, isEqual);
