import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {CheckboxComp, Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonNhomGiamDinhComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, value, setValue, data} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const onChangeCheckBoxValue = (item, index) => {
    setSelectedIndex(index);
    setValue && setValue(item);
    onBackPress && onBackPress();
  };

  const initModalData = () => {
    data.map((e, i) => {
      if (e.value === value) setSelectedIndex(i);
    });
  };

  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Chọn nhóm giám định" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    let isChecked = selectedIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index)}>
        <CheckboxComp disabled value={isChecked} checkboxStyle={styles.checkbox} />
        <Text style={{color: isChecked ? colors.PRIMARY : colors.BLACK_03, marginTop: Platform.OS == 'android' ? 4 : 2}}>{item.label}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return <FlatList style={styles.content} data={data} renderItem={renderItem} keyExtractor={(e, i) => i.toString()} ListEmptyComponent={<Text style={styles.emptyTxt}>Chưa có dữ liệu</Text>} />;
  };
  return (
    <Modal
      ///onModalShow
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      onModalWillShow={initModalData}
      swipeDirection={['down', 'right']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height * 0.3,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: 16,
    borderColor: colors.GRAY,
    // flex: 1,
    height: 40,
    margin: 16,
    borderRadius: 25,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 8,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
  emptyTxt: {
    textAlign: 'center',
    marginVertical: spacing.smaller,
  },
});

export const ModalChonNhomGiamDinh = memo(ModalChonNhomGiamDinhComponent, isEqual);
