import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, TextInputOutlined, Text} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalBoSungCPThucTeComponent = forwardRef(({onBackPress, onPressLuu, value}, ref) => {
  const [isVisible, setIsVisible] = useState(false);

  const [cpThucTe, setCpThucTe] = useState('');
  const [tienThue, setTienThue] = useState('');
  let refInputCPThucTe = useRef();
  let refInputTienThue = useRef();

  useImperativeHandle(ref, () => ({
    show: (data) => {
      setIsVisible(true);
      // setDataHangMuc(data);
    },
    hide: () => {
      setIsVisible(false);
      // setDataHangMuc(null);
    },
  }));

  const initModalData = () => {
    refInputCPThucTe?.focus();
    setCpThucTe('');
    setTienThue('');
  };

  const _onPressLuu = (val) => {
    onBackPress && onBackPress();
    onPressLuu && onPressLuu(cpThucTe, tienThue);
  };

  /* RENDER */

  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Bổ sung chi phí thực tế" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down', 'right']}
      onModalWillShow={initModalData}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      style={styles.modal}
      avoidKeyboard={true}>
      <View style={styles.modalContent}>
        {renderHeader()}
        <View marginHorizontal={spacing.small} marginTop={10} flexDirection="row">
          <TextInputOutlined
            isRequired
            value={cpThucTe}
            keyboardType="numeric"
            title="Số tiền thực tế"
            onChangeText={setCpThucTe}
            inputStyle={styles.inputStyle}
            placeholder={'Số tiền thực tế'}
            getRef={(ref) => (refInputCPThucTe = ref)}
            containerStyle={[styles.containerInput, {marginRight: spacing.small}]}
          />
          <TextInputOutlined
            value={tienThue}
            title="Tiền thuế"
            keyboardType="numeric"
            onChangeText={setTienThue}
            placeholder="Nhập tiền thuế"
            inputStyle={styles.inputStyle}
            getRef={(ref) => (refInputTienThue = ref)}
            containerStyle={styles.containerInput}
          />
        </View>
        <View style={styles.btnView}>
          {/* <ButtonLinear title="Đóng" linearStyle={styles.btnLuu} onPress={() => setIsVisible(false)} linearColors={[colors.GRAY, colors.GRAY]} textStyle={{color: colors.BLACK_03}} /> */}
          <ButtonLinear title={'Lưu'} linearStyle={[styles.btnLuu]} onPress={() => _onPressLuu()} />
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  btnView: {
    left: spacing.small,
    right: spacing.small,
    bottom: spacing.small,
    flexDirection: 'row',
    position: 'absolute',
  },
  modalContent: {
    backgroundColor: '#FFF',
    width: dimensions.width,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: dimensions.height * 0.3,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerRow: {
    height: 7,
    borderRadius: 20,
    width: dimensions.width / 5,
    backgroundColor: colors.GRAY,
  },

  inputContainer: {
    // flex: 1,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
    // paddingBottom :
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.RED1,
    marginVertical: 10,
  },
  errText: {
    color: colors.RED1,
    marginBottom: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  inputStyle: {
    // flex: 1,
  },
  containerInput: {
    flex: 1,
  },
});
export const ModalBoSungCPThucTe = memo(ModalBoSungCPThucTeComponent, isEqual);
