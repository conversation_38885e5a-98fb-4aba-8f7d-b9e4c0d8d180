import {isAndroid} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {CheckboxComp, Icon, SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const DATA = [
  {label: 'Nội bộ', value: 'NB'},
  {label: 'Ngoài', value: 'NGOAI'},
];
const ModalChonCanBoGiamDinhComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, data, setValue, value} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [dsCanBoGiamDinh, setDsCanBoGiamDinh] = useState([]);
  const [dsCanBoGiamDinhRoot, setDsCanBoGiamDinhRoot] = useState([]);
  const [searchText, setSearchText] = useState('');

  const initModalData = () => {
    let cloneData = data;
    cloneData.map((e, i) => {
      cloneData[i].isChecked = false;
      if (e.value === value) {
        cloneData[i].isChecked = true;
      }
    });
    setDsCanBoGiamDinh([...cloneData]);
    setDsCanBoGiamDinhRoot([...cloneData]);
  };
  const onChangeCheckBoxValue = (item, index) => {
    setValue && setValue(item);
    onBackPress && onBackPress();
  };

  const debounced = useDebouncedCallback((value) => {
    setSearchText(value);
  }, 300);

  useEffect(() => {
    if (searchText !== '') {
      const lowerCaseSearchText = searchText?.toLowerCase();
      const filter = dsCanBoGiamDinhRoot.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDsCanBoGiamDinh(filter);
    } else {
      setDsCanBoGiamDinh(dsCanBoGiamDinhRoot);
    }
  }, [searchText]);

  /* RENDER */
  const renderHeader = () => {
    return (
      <View>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children="Chọn cán bộ giám định" />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
        <SearchBar showSearchBtn={false} onTextChange={debounced} />
      </View>
    );
  };
  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onChangeCheckBoxValue(item, index)}>
        <CheckboxComp disabled value={item.isChecked} checkboxStyle={styles.checkbox} />
        <Text style={{color: item.isChecked ? colors.PRIMARY : colors.BLACK_03, marginTop: isAndroid ? 4 : 2}}>{item.label}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList style={styles.content} data={dsCanBoGiamDinh} renderItem={renderItem} keyExtractor={(e, i) => i.toString()} ListEmptyComponent={<Text style={styles.emptyTxt}>Chưa có dữ liệu</Text>} />
    );
  };
  return (
    <Modal
      ///onModalShow
      style={styles.modal}
      isVisible={isVisible}
      propagateSwipe={true}
      onModalWillShow={initModalData}
      swipeDirection={['down', 'right']}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        <KeyboardAwareScrollView>{renderContent()}</KeyboardAwareScrollView>
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
    height: isAndroid ? dimensions.height * 0.5 : dimensions.height * 0.8,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 8,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
  emptyTxt: {
    textAlign: 'center',
    marginVertical: spacing.smaller,
  },
});

export const ModalChonCanBoGiamDinh = memo(ModalChonCanBoGiamDinhComponent, isEqual);
