import {FORMAT_DATE_TIME} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {default as axiosConfig, default as AxiosConfig} from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, ScreenComponent, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {ModalBoSungCPThucTe, ModalChonCanBoGiamDinh, ModalChonDonViGiamDinh, ModalChonNhomGiamDinh} from './Component';
import styles from './ThemChiPhiGiamDinhStyles';

const DATA_NHOM_GD = [
  {label: 'Nội bộ', value: 'NB'},
  {label: 'Ngoài', value: 'NGOAI'},
];

const ThemChiPhiGiamDinhScreenComponent = (props) => {
  console.log('ThemChiPhiGiamDinhScreenComponent');
  const {route} = props;
  const {type, chiTietHSGiamDinh, chiTietChiPhiGiamDinh} = route?.params;

  let refInputTenChiPhi = useRef();
  let refInputChiPhiDuKien = useRef();
  let refInputSoTienDuyet = useRef();
  let refInputSoChungTu = useRef();
  let refInputGhiChu = useRef();

  let refModal = useRef();
  let refModalDVGiamDinh = useRef();
  let refModalCanBoGiamDinh = useRef();
  let refModalBoSungCPThucTe = useRef();

  const [toggleNgayPhatSinh, setToggleNgayPhatSinh] = useState(false);
  const [submiting, setSubmiting] = useState(false);

  const [dsChiNhanh, setDsChiNhanh] = useState([]);
  const [dsCtyGiamDinh, setDsCtyGiamDinh] = useState([]);
  const [dviGDTheoNhom, setDviGDTheoNhom] = useState([]);
  const [dsNguoiSuDungRoot, setDsNguoiSuDungRoot] = useState([]);
  const [dsNguoiSuDungTheoChiNhanh, setDsNguoiSuSungTheoChiNhanh] = useState([]);

  const getDefaultFormValue = () => {
    return {
      ngayPhatSinh: chiTietChiPhiGiamDinh?.ngay_ht ? moment(chiTietChiPhiGiamDinh?.ngay_ht, FORMAT_DATE_TIME.API_DATE_FORMAT).toDate() : new Date(),
      tienDuyet: chiTietChiPhiGiamDinh?.tien_duyet || '',
      tenChiPhi: chiTietChiPhiGiamDinh?.ten_chi_phi || '',
      nhomGD: chiTietChiPhiGiamDinh?.nhom_gd || '',
      dviGD: chiTietChiPhiGiamDinh?.dvi_gd || '',
      cbGiamDinh: chiTietChiPhiGiamDinh?.nsd_gd || '',
      tienThue: chiTietChiPhiGiamDinh?.tien_thue || 0,
      ghiChu: chiTietChiPhiGiamDinh?.ghi_chu || '',
      soChungTu: chiTietChiPhiGiamDinh?.so_ct || '',
      cpDuKien: chiTietChiPhiGiamDinh?.tien_dx || '',
      tongCong: chiTietChiPhiGiamDinh?.tong_cong || '',
      cpThucTe: chiTietChiPhiGiamDinh?.tien_thoa_thuan || '',
    };
  };

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const ngayPhatSinh = watch('ngayPhatSinh');
  const nhomGiamDinh = watch('nhomGD');
  const dviGD = watch('dviGD');
  const chiPhiDuKien = watch('cpDuKien');
  const chiPhiThucTe = watch('cpThucTe');
  const tienDuyet = watch('tienDuyet');
  const tienThue = watch('tienThue');
  const cbGiamDinh = watch('cbGiamDinh');

  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    await layDsCongTyGiamDinh();
    await layDsChiNhanh();
    layDsNguoiSuDung();
  };
  useEffect(() => {
    if (nhomGiamDinh === 'NGOAI') setDviGDTheoNhom(dsCtyGiamDinh);
    else setDviGDTheoNhom(dsChiNhanh);
  }, [nhomGiamDinh, dsChiNhanh, dsCtyGiamDinh]);

  useEffect(() => {
    let filter = dsNguoiSuDungRoot.filter((e) => e.ma_chi_nhanh === dviGD);
    setDsNguoiSuSungTheoChiNhanh(filter);
  }, [dsNguoiSuDungRoot, dviGD]);

  useEffect(() => {
    if (type === 'EDIT') return;
    setValue('tienDuyet', chiPhiDuKien);
  }, [chiPhiDuKien, setValue, type]);

  useEffect(() => {
    setValue('cpThucTe', tienDuyet);
  }, [setValue, tienDuyet]);

  useEffect(() => {
    setValue('tongCong', +chiPhiThucTe + +tienThue);
  }, [chiPhiThucTe, setValue, tienThue]);

  const luuChiPhiGiamDinh = async (data) => {
    setSubmiting(true);
    try {
      const params = {
        so_id: chiTietHSGiamDinh?.so_id,
        bt: chiTietChiPhiGiamDinh ? chiTietChiPhiGiamDinh?.bt : '',
        ngay_ht: +moment(data.ngayPhatSinh).format(FORMAT_DATE_TIME.API_DATE_FORMAT), //
        ten_chi_phi: data.tenChiPhi,
        nhom_gd: data.nhomGD,
        dvi_gd: data.dviGD,
        nsd_gd: data.cbGiamDinh,
        tien_dx: data.cpDuKien, //dự kiến
        tien_duyet: data.tienDuyet, // tiền duyệt
        tien_thoa_thuan: data.cpThucTe, //thực tế
        tien_thue: data.tienThue,
        ghi_chu: data.ghiChu,
        so_ct: data.soChungTu, //chứng từ
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LUU_CHI_PHI_GIAM_DINH, params);
      setSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu chi phí giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDsChiNhanh = async () => {
    try {
      let params = {
        ma_doi_tac: chiTietHSGiamDinh?.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DANH_SACH_CHI_NHANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info;
      data.map((item, index) => {
        data[index].label = item.ten;
        data[index].value = item.ma;
      });
      setDsChiNhanh(data);
    } catch (error) {
      // setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const layDsCongTyGiamDinh = async () => {
    try {
      const params = {ma_doi_tac: chiTietHSGiamDinh?.ma_doi_tac, nv: chiTietHSGiamDinh?.nv};
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_CT_GIAM_DINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info;
      data.map((item, index) => {
        data[index].label = item.ten;
        data[index].value = item.ma;
      });
      setDsCtyGiamDinh(data);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const layDsNguoiSuDung = async () => {
    try {
      const params = {ma_doi_tac: chiTietHSGiamDinh?.ma_doi_tac};
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DS_NSD, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let data = response.data_info;
      data.map((item, index) => {
        data[index].label = item.ten;
        data[index].value = item.ma;
      });
      setDsNguoiSuDungRoot(data);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate, type) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const getErrMessage = (inputName, errType) => {
    // console.log('getErrMessage', inputName, errType);
    if (errType === 'required') return 'Thông tin bắt buộc';
    else return '';
  };

  const onChangeValueNhomGD = (val) => {
    setValue('nhomGD', val.value, {shouldValidate: true});
    setValue('dviGD', '');
    setValue('cbGiamDinh', '');
  };

  const onChangeValueDvGiamDinh = (val) => {
    setValue('dviGD', val.value, {shouldValidate: true});
    setValue('cbGiamDinh', '');
  };
  const onChangeValueCBGiamDinh = (val) => {
    setValue('cbGiamDinh', val.value, {shouldValidate: true});
  };

  const onPressLuuCPThucTe = (cptt, tt) => {
    setValue('cpThucTe', cptt);
    setValue('tienThue', tt);
    handleSubmit(luuChiPhiGiamDinh);
  };

  const getTextHienThi = (val, field) => {
    let text = '';
    if (field === 'nhomGD') {
      DATA_NHOM_GD.map((e) => {
        if (e.value == val) {
          text = e.label;
        }
      });
    }
    if (field === 'dviGD') {
      dviGDTheoNhom.map((e) => {
        if (e.value == val) {
          text = e.label;
        }
      });
    }
    if (field === 'cbGiamDinh') {
      dsNguoiSuDungTheoChiNhanh.map((e) => {
        if (e.value == val) {
          text = e.label;
        }
      });
    }
    return text;
  };

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderContent = () => {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            <View style={styles.doubleInputRowView}>
              <View flex={1}>
                <Controller
                  control={control}
                  name="ngayPhatSinh"
                  rules={{
                    required: true,
                  }}
                  render={({field: {value}}) => (
                    <TextInputOutlined
                      isRequired
                      isDateTimeField
                      editable={false}
                      title="Ngày phát sinh"
                      isTouchableOpacity={true}
                      value={moment(value).format('DD/MM/YYYY')}
                      onPress={() => setToggleNgayPhatSinh(true)}
                      containerStyle={{flex: 1, marginRight: spacing.smaller}}
                      error={errors.ngayPhatSinh && getErrMessage('ngayPhatSinh', errors.ngayPhatSinh.type)}
                    />
                  )}
                />
                {renderDateTimeComp(toggleNgayPhatSinh, setToggleNgayPhatSinh, (value) => setValue('ngayPhatSinh', value), ngayPhatSinh, 'date', null, new Date())}
              </View>
              <Controller
                control={control}
                name="tenChiPhi"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    title="Tên chi phí"
                    onChangeText={onChange}
                    containerStyle={{flex: 1}}
                    placeholder="Nhập tên chi phí"
                    getRef={(ref) => (refInputTenChiPhi = ref)}
                    onSubmitEditing={() => refInputChiPhiDuKien?.focus()}
                    error={errors.tenChiPhi && getErrMessage('tenChiPhi', errors.tenChiPhi.type)}
                  />
                )}
              />
            </View>

            <View>
              <Controller
                control={control}
                name="nhomGD"
                rules={{
                  required: true,
                }}
                render={({field: {value}}) => (
                  <TextInputOutlined
                    title="Nhóm giám định"
                    isTouchableOpacity={true}
                    onPress={() => refModal.current.show(true)}
                    value={getTextHienThi(value, 'nhomGD')}
                    placeholder="Chọn nhóm giám định"
                    editable={false}
                    containerStyle={{flex: 1}}
                    isRequired
                    isDropdown
                    inputStyle={{color: colors.BLACK_03}}
                    error={errors.nhomGD && getErrMessage('nhomGD', errors.nhomGD.type)}
                  />
                )}
              />
            </View>

            <Controller
              control={control}
              name="dviGD"
              rules={{
                required: true,
              }}
              render={({field: {value}}) => (
                <TextInputOutlined
                  isRequired
                  isDropdown
                  editable={false}
                  title="Đơn vị giám định"
                  containerStyle={{flex: 1}}
                  isTouchableOpacity={true}
                  placeholder="Chọn đơn vị giám định"
                  inputStyle={{color: colors.BLACK_03}}
                  value={getTextHienThi(value, 'dviGD')}
                  onPress={() => refModalDVGiamDinh.current.show()}
                  error={errors.dviGD && getErrMessage('dviGD', errors.dviGD.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="cbGiamDinh"
              rules={{
                required: true,
              }}
              render={({field: {value}}) => (
                <TextInputOutlined
                  isDropdown
                  isRequired
                  editable={false}
                  isTouchableOpacity
                  title="Cán bộ giám định"
                  containerStyle={{flex: 1}}
                  placeholder="Chọn cán bộ giám định"
                  inputStyle={{color: colors.BLACK_03}}
                  value={getTextHienThi(value, 'cbGiamDinh')}
                  onPress={() => refModalCanBoGiamDinh.current.show()}
                  error={errors.cbGiamDinh && getErrMessage('cbGiamDinh', errors.cbGiamDinh.type)}
                />
              )}
            />
            <View flexDirection="row">
              <Controller
                control={control}
                name="cpDuKien"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    isRequired
                    value={value}
                    placeholder="0"
                    keyboardType="numeric"
                    onChangeText={onChange}
                    title="Chi phí dự kiến"
                    containerStyle={{flex: 1}}
                    inputStyle={{textAlign: 'right'}}
                    getRef={(ref) => (refInputChiPhiDuKien = ref)}
                    onSubmitEditing={() => refInputSoTienDuyet?.focus()}
                    error={errors.cpDuKien && getErrMessage('cpDuKien', errors.cpDuKien.type)}
                  />
                )}
              />
              <Controller
                control={control}
                name="tienDuyet"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    placeholder="0"
                    isRequired={true}
                    title="Số tiền duyệt"
                    keyboardType="numeric"
                    onChangeText={onChange}
                    inputStyle={{textAlign: 'right'}}
                    getRef={(ref) => (refInputSoTienDuyet = ref)}
                    onSubmitEditing={() => refInputSoChungTu?.focus()}
                    containerStyle={{flex: 1, marginLeft: spacing.smaller}}
                    error={errors.tienDuyet && getErrMessage('tienDuyet', errors.tienDuyet.type)}
                  />
                )}
              />
            </View>
            <View flexDirection="row">
              <Controller
                control={control}
                name="cpThucTe"
                rules={{
                  required: true,
                }}
                render={({field: {value}}) => (
                  <TextInputOutlined
                    disabled
                    placeholder="0"
                    inputStyle={{textAlign: 'right'}}
                    value={value}
                    editable={false}
                    isRequired={true}
                    title="Chi phí thực tế"
                    keyboardType="numeric"
                    containerStyle={{flex: 1}}
                    error={errors.cpThucTe && getErrMessage('cpThucTe', errors.cpThucTe.type)}
                  />
                )}
              />
              <Controller
                control={control}
                name="tienThue"
                rules={{
                  required: true,
                }}
                render={({field: {value}}) => (
                  <TextInputOutlined
                    disabled
                    value={value}
                    placeholder="0"
                    editable={false}
                    title="Tiền thuế"
                    isRequired={true}
                    keyboardType="numeric"
                    inputStyle={{textAlign: 'right'}}
                    containerStyle={{flex: 1, marginLeft: spacing.smaller}}
                    error={errors.tienThue && getErrMessage('tienThue', errors.tienThue.type)}
                  />
                )}
              />
            </View>
            <Controller
              control={control}
              name="tongCong"
              rules={{
                required: true,
              }}
              render={({field: {value}}) => (
                <TextInputOutlined
                  disabled
                  value={value}
                  placeholder="0"
                  editable={false}
                  title="Tổng cộng"
                  isRequired={true}
                  keyboardType="numeric"
                  inputStyle={{textAlign: 'right'}}
                  error={errors.tongCong && getErrMessage('tongCong', errors.tongCong.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="soChungTu"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  getRef={(ref) => (refInputSoChungTu = ref)}
                  onSubmitEditing={() => refInputGhiChu?.focus()}
                  onChangeText={onChange}
                  placeholder="Nhập số chứng từ"
                  title="Thông tin chứng từ"
                  value={value}
                />
              )}
            />
            <Controller
              control={control}
              name="ghiChu"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined multiline getRef={(ref) => (refInputGhiChu = ref)} onChangeText={onChange} placeholder="Nhập ghi chú" title="Ghi chú" value={value} />
              )}
            />
          </View>
        </KeyboardAwareScrollView>
        <ModalChonNhomGiamDinh data={DATA_NHOM_GD} value={nhomGiamDinh} setValue={(val) => onChangeValueNhomGD(val)} ref={refModal} onBackPress={() => refModal.current.hide()} />
        <ModalChonDonViGiamDinh data={dviGDTheoNhom} value={dviGD} setValue={(val) => onChangeValueDvGiamDinh(val)} ref={refModalDVGiamDinh} onBackPress={() => refModalDVGiamDinh.current.hide()} />
        <ModalChonCanBoGiamDinh
          value={cbGiamDinh}
          ref={refModalCanBoGiamDinh}
          data={dsNguoiSuDungTheoChiNhanh}
          setValue={(val) => onChangeValueCBGiamDinh(val)}
          onBackPress={() => refModalCanBoGiamDinh.current.hide()}
        />
        <ModalBoSungCPThucTe onPressLuu={(cptt, tt) => onPressLuuCPThucTe(cptt, tt)} ref={refModalBoSungCPThucTe} onBackPress={() => refModalBoSungCPThucTe.current.hide()} />
      </View>
    );
  };

  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {type === 'EDIT' && (
          <ButtonLinear
            title="Bổ sung CP thực tế"
            onPress={() => refModalBoSungCPThucTe.current.show()}
            linearStyle={{marginRight: spacing.small}}
            linearColors={[colors.GRAY2, colors.GRAY2]}
            textStyle={{color: colors.BLACK_03}}
          />
        )}
        <ButtonLinear loading={submiting} disabled={submiting} title="Lưu" onPress={handleSubmit(luuChiPhiGiamDinh)} />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle="Thêm chi phí giám định"
      renderView={renderContent()}
      footer={renderFooter()}
      //
    />
  );
};

export const ThemChiPhiGiamDinhScreen = memo(ThemChiPhiGiamDinhScreenComponent, isEqual);
