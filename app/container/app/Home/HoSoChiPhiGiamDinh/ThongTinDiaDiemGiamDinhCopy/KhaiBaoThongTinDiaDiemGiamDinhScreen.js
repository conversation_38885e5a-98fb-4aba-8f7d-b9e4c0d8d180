import {FORMAT_DATE_TIME} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon, DropdownPicker, ScreenComponent, Text, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {connect} from 'react-redux';
import {ModalChonHienTruongXe, ModalChonMoiQuanHeVoiChuXe} from '../MoHoSoChiPhiGiamDinh/MoHoSoGiamDinhBuoc2/Components';
import styles from './KhaiBaoThongTinDiaDiemGiamDinhStyles';

const dataHienTruong = [
  {ten: 'Xe đang ở hiện trường', ma: 'D'},
  {ten: 'Xe không ở hiện trường', ma: 'K'},
];

const relationship = [
  {Name: 'Chủ xe', ma: 'QH.0001', Value: 'QH.0001', Id: 'QH.0001'},
  {Name: 'Lái xe', ma: 'QH.0002', Value: 'QH.0002', Id: 'QH.0002'},
  {Name: 'Người khác', ma: 'QH.0005', Value: 'QH.0005', Id: 'QH.0005'},
];

const KhaiBaoThongTinDiaDiemGiamDinhScreenComponent = (props) => {
  console.log('KhaiBaoThongTinDiaDiemGiamDinhScreenComponent');
  const {route, citiesData} = props;
  const {profileData} = route?.params;
  const titleDropdownInput = ['Tỉnh thành', 'Quận huyện', 'Xã phường', 'Nhóm nguyên nhân'];

  const [inputErr, setInputErr] = useState(new Array(9).fill(''));

  const [addressInput, setAddressInput] = useState('');

  const [citiesDataDropDown, setCitiesDataDropDown] = useState([]);
  const [openCity, setOpenCity] = useState(false);
  const [citySelected, setCitySelected] = useState(null); //thành phố được chọn

  const [districtsDataDropDown, setDistrictsDataDropDown] = useState([]);
  const [openDistrict, setOpenDistrict] = useState(false);
  const [districtSelected, setDistrictSelected] = useState(null); //quận huyện được chọn

  const [wardsDataDropDown, setWardsDataDropDown] = useState([]);
  const [openWard, setOpenWard] = useState(false);
  const [wardSelected, setWardSelected] = useState(null); //xã phường được chọn

  const [toggleGioXR, setToggleGioXR] = useState(false);
  const [toggleNgayXR, setToggleNgayXR] = useState(false);

  const [ngayXR, setNgayXR] = useState(moment().format('DD/MM/YYYY'));
  const [gioXR, setGioXR] = useState(moment().add(15, 'm').format('HH:mm'));

  const [tenLienHe, setTenLienHe] = useState(profileData?.ho_so?.nguoi_lhe || '');
  const [dthoaiLienHe, setDthoaiLienHe] = useState(profileData?.ho_so?.dthoai_lhe || '');
  const [emailLienHe, setEmailLienHe] = useState(profileData?.ho_so?.email_lhe || '');

  const [hienTruong, setHienTruong] = useState({ten: 'Xe không ở hiện trường', ma: 'K'});
  const [moiQuanHeNgThongBao, setMoiQuanHeNgThongBao] = useState(null);
  const [isDisable, setIsDisable] = useState(false);

  let tieuDeRef = useRef(null);
  let diaDiemRef = useRef(null);
  let noiDungRef = useRef(null);
  let refModalMoiQuanHe = useRef(null);
  let refModalHienTruong = useRef(null);

  useEffect(() => {
    setCitySelected(profileData?.ho_so?.tinh_thanh);
    setDistrictSelected(profileData.ho_so?.quan_huyen);
    setWardSelected(profileData.ho_so?.phuong_xa);
    setAddressInput(profileData.ho_so?.dia_diem);
    const filter = relationship.filter((item) => item.ma === profileData?.ho_so?.moi_qh_lhe);
    setMoiQuanHeNgThongBao(filter[0]);
  }, []);

  useEffect(() => {
    if (profileData?.dien_bien?.length > 0) {
      if (hienTruong.ma === 'D') {
        setCitySelected(profileData.dien_bien[0].tinh_thanh);
        setDistrictSelected(profileData.dien_bien[0].quan_huyen);
        setWardSelected(profileData.dien_bien[0].phuong_xa);
        setAddressInput(profileData.dien_bien[0].dia_diem);
        setIsDisable(true);
      } else {
        setCitySelected(profileData?.ho_so?.tinh_thanh);
        setDistrictSelected(profileData.ho_so?.quan_huyen);
        setWardSelected(profileData.ho_so?.phuong_xa);
        setAddressInput(profileData.ho_so?.dia_diem);
        setIsDisable(false);
      }
    }
  }, [hienTruong]);

  //xử lý dữ liệu về thành phố, quận huyện, xã phường
  useEffect(() => {
    let cityDropdownTmp = JSON.parse(JSON.stringify(citiesData));
    //duyệt mảng city
    for (let i = 0; i < cityDropdownTmp.length; i++) {
      let city = cityDropdownTmp[i];
      city.label = city.ten;
      city.value = city.ma;
      //duyệt mảng district trong city
      for (let j = 0; j < city.district.length; j++) {
        let district = city.district[j];
        district.label = district.ten;
        district.value = district.ma;
        //duyệt mảng ward trong district
        for (let k = 0; k < district.ward.length; k++) {
          let ward = district.ward[k];
          ward.label = ward.ten;
          ward.value = ward.ma;
          district.ward[k] = ward;
        }
        city.district[j] = district;
      }
      cityDropdownTmp[i] = city;
    }
    setCitiesDataDropDown(cityDropdownTmp);
  }, []);

  useEffect(() => {
    if (moiQuanHeNgThongBao?.ma === 'QH.0001') {
      setTenLienHe(profileData?.ho_so?.chu_xe);
      setEmailLienHe(profileData?.ho_so?.email);
      setDthoaiLienHe(profileData?.ho_so?.dien_thoai);
    }
    if (moiQuanHeNgThongBao?.ma === 'QH.0002') {
      setTenLienHe(profileData?.dien_bien[0]?.ten_lxe);
      setEmailLienHe(profileData?.dien_bien[0]?.email_lxe);
      setDthoaiLienHe(profileData?.dien_bien[0]?.dthoai_lxe);
    }
  }, [moiQuanHeNgThongBao]);

  const onInputFocus = () => {
    setOpenCity(false);
    setOpenDistrict(false);
    setOpenWard(false);
  };

  const onPressRegisterSchedule = async (type) => {
    let haveErr = false;
    let errInputTmp = inputErr;
    if (addressInput === '' || !addressInput) {
      errInputTmp[3] = 'Vui lòng ghi rõ địa điểm';
      haveErr = true;
    }
    if (!citySelected || citySelected === '') {
      errInputTmp[0] = 'Vui lòng chọn tỉnh/thành phố';
      haveErr = true;
    }
    if (!districtSelected || districtSelected === '') {
      errInputTmp[1] = 'Vui lòng chọn quận/huyện';
      haveErr = true;
    }
    if (!wardSelected || wardSelected === '') {
      errInputTmp[2] = 'Vui lòng chọn phường/xã';
      haveErr = true;
    }
    // if (moiQuanHeNgThongBao == null || !moiQuanHeNgThongBao) {
    //   errInputTmp[4] = 'Vui lòng chọn mối quan hệ';
    //   haveErr = true;
    // }
    // if (tenLienHe === '' || !tenLienHe) {
    //   errInputTmp[5] = 'Vui lòng nhập họ tên';
    //   haveErr = true;
    // }
    // if (dthoaiLienHe === '' || !dthoaiLienHe) {
    //   errInputTmp[6] = 'Vui lòng số điện thoại';
    //   haveErr = true;
    // }
    setInputErr([...errInputTmp]);

    if (haveErr) return;
    const params = {
      pm: 'TNBT',
      dvi_gdinh: '',
      ma_gdv: '',
      so_id: profileData?.ho_so?.so_id, //id hồ sơ
      hien_truong: hienTruong?.ma,
      gio_gd: gioXR,
      ngay_gd: +moment(ngayXR).format(FORMAT_DATE_TIME.API_DATE_FORMAT) || '',
      tinh_thanh: citySelected,
      quan_huyen: districtSelected,
      phuong_xa: wardSelected,
      dia_diem: addressInput,
      nguoi_lhe: tenLienHe,
      moi_qh_lhe: moiQuanHeNgThongBao?.ma,
      dthoai_lhe: dthoaiLienHe,
      email_lhe: emailLienHe,
    };

    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.KHAI_BAO_DIA_DIEM_GIAM_DINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (type === 'NEXT') chuyenGiamDinh();
      if (type === 'SAVE') FlashMessageHelper.showFlashMessage('Thông báo', 'Lưu thông tin giám định thành công', 'success');
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const chuyenGiamDinh = async () => {
    const params = {
      so_id: profileData?.ho_so?.so_id, //id hồ sơ
    };

    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHUYEN_GIAM_DINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Chuyển giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const closeDropdown = (title) => {
    let inputErrTmp = inputErr;
    if (title == titleDropdownInput[0]) {
      setOpenDistrict(false);
      setOpenWard(false);
    } else if (title == titleDropdownInput[1]) {
      if (!citySelected) {
        inputErrTmp[0] = 'Vui lòng chọn Tỉnh thành';
      }
      setOpenCity(false);
      setOpenWard(false);
    } else if (title == titleDropdownInput[2]) {
      if (!citySelected) {
        inputErrTmp[1] = 'Vui lòng chọn quận huyện';
      }
      if (!districtSelected) {
        inputErrTmp[2] = 'Vui lòng chọn xã phường';
      }
      setOpenCity(false);
      setOpenDistrict(false);
    }
    setInputErr([...inputErrTmp]);
  };

  const onChangeValueDropdown = (title, items, itemValueSelected) => {
    let inputErrTmp = inputErr;
    if (title == titleDropdownInput[0] && itemValueSelected) {
      inputErrTmp[0] = '';
    } else if (title == titleDropdownInput[1] && itemValueSelected) {
      inputErrTmp[1] = '';
    } else if (title == titleDropdownInput[2] && itemValueSelected) {
      inputErrTmp[2] = '';
    }
    setInputErr([...inputErrTmp]);
    //itemValueSelected : value của item được chọn
    let itemSelected = items.filter((item) => item.ma == itemValueSelected);
    if (itemSelected.length > 0) {
      if (title == titleDropdownInput[0]) {
        setDistrictsDataDropDown(itemSelected[0].district);
        setWardsDataDropDown([]);
      } else if (title == titleDropdownInput[1]) {
        if (itemSelected.length > 0) setWardsDataDropDown(itemSelected[0].ward);
      }
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate, mode) => {
    setToggleDateTime(false);
    if (mode === 'date') {
      setDate(moment(date).format('DD/MM/YYYY'));
    } else {
      setDate(moment(date).format('HH:mm'));
    }
  };

  const onChangeTextDiaDiem = (value) => {
    let errInputTmp = inputErr;
    errInputTmp[3] = '';
    if (value != null && !value.trim()) {
      errInputTmp[3] = 'Vui lòng ghi rõ địa điểm';
    }
    setAddressInput(value);
    setInputErr([...errInputTmp]);
  };
  const onChangeMoiQuanHe = (value) => {
    let errInputTmp = inputErr;
    errInputTmp[4] = '';
    if (value != null && !value) {
      errInputTmp[4] = 'Vui lòng chọn mối quan hệ';
    }
    setMoiQuanHeNgThongBao(value);
    setInputErr([...errInputTmp]);
  };

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => {
    return (
      <DateTimePickerModal
        mode={mode}
        Date={date}
        locale={'vi_VN'}
        display="spinner"
        confirmTextIOS="Chọn"
        maximumDate={maxDate}
        minimumDate={minDate}
        cancelTextIOS="Để sau"
        isVisible={toggleDateTime}
        onCancel={() => setToggleDateTime(false)}
        onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, mode)}
        themeVariant="light"
        isDarkModeEnabled={false}
        // is24Hour={false}
      />
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle={'Thông tin địa điểm GĐ'}
      renderView={
        <SafeAreaView style={styles.container}>
          <KeyboardAwareScrollView resetScrollToCoords={{x: 0, y: 0}} contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={false}>
            <View style={styles.content}>
              <Text style={styles.label}>
                Hiện trường
                <Text style={{color: colors.RED1}}>(*)</Text>
              </Text>
              <CommonOutlinedTextFieldWithIcon isPickerModal value={hienTruong?.ten} styleContainer={{marginVertical: 0}} onPressInput={() => refModalHienTruong.current.show()} />
              <View style={styles.doubleInputRowView}>
                <View style={styles.doubleInputRow}>
                  <CommonOutlinedTextFieldWithIcon
                    // error={props.inputErrLienHe[0]}
                    isDateTimeField
                    placeholder="Giờ hẹn giám định *"
                    styleContainer={{marginVertical: 5}}
                    onPressInput={() => setToggleGioXR(true)}
                    value={gioXR}
                  />
                  {renderDateTimeComp(toggleGioXR, setToggleGioXR, setGioXR, gioXR, 'time', null, null, 0)}
                </View>
                <View style={styles.doubleInputRow}>
                  <CommonOutlinedTextFieldWithIcon
                    // error={props.inputErrLienHe[0]}
                    isDateTimeField
                    placeholder="Ngày hẹn giám định *"
                    styleContainer={{marginVertical: 5}}
                    onPressInput={() => setToggleNgayXR(true)}
                    value={ngayXR}
                  />
                  {renderDateTimeComp(toggleNgayXR, setToggleNgayXR, setNgayXR, ngayXR, 'date', null, new Date(), 0)}
                </View>
              </View>

              <DropdownPicker
                zIndex={7000}
                isOpen={openCity}
                isRequired={true}
                disabled={isDisable}
                setOpen={setOpenCity}
                inputErr={inputErr[0]}
                items={citiesDataDropDown}
                itemSelected={citySelected}
                title={titleDropdownInput[0]}
                placeholder="Chọn Tỉnh thành"
                setItemSelected={setCitySelected}
                containerStyle={{marginBottom: 10}}
                onChangeValue={onChangeValueDropdown}
                onOpen={() => closeDropdown(titleDropdownInput[0])}
              />

              <DropdownPicker
                zIndex={6000}
                isRequired={true}
                disabled={isDisable}
                isOpen={openDistrict}
                inputErr={inputErr[1]}
                setOpen={setOpenDistrict}
                placeholder="Chọn Quận huyện"
                title={titleDropdownInput[1]}
                items={districtsDataDropDown}
                itemSelected={districtSelected}
                setItemSelected={setDistrictSelected}
                onChangeValue={onChangeValueDropdown}
                onOpen={() => closeDropdown(titleDropdownInput[1])}
              />

              <DropdownPicker
                zIndex={5000}
                isOpen={openWard}
                isRequired={true}
                disabled={isDisable}
                setOpen={setOpenWard}
                inputErr={inputErr[2]}
                items={wardsDataDropDown}
                itemSelected={wardSelected}
                placeholder="Chọn Xã phường"
                title={titleDropdownInput[2]}
                setItemSelected={setWardSelected}
                containerStyle={{marginBottom: 10}}
                onChangeValue={onChangeValueDropdown}
                onOpen={() => closeDropdown(titleDropdownInput[2])}
              />

              <TextInputOutlined
                isRequired={true}
                error={inputErr[3]}
                blurOnSubmit={false}
                value={addressInput}
                returnKeyType={'next'}
                onFocus={onInputFocus}
                title="Địa điểm thực hiện giám định"
                getRef={(ref) => (tieuDeRef = ref)}
                onSubmitEditing={() => diaDiemRef?.focus()}
                onChangeText={(t) => onChangeTextDiaDiem(t)}
              />
              {/* <Text style={styles.title}>Thông tin liên hệ với khách hàng tại địa điểm giám định</Text>
              <View marginTop={10}>
                <Text style={styles.label}>
                  Mối quan hệ với chủ sở hữu xe
                  <Text style={{color: colors.RED1}}>(*)</Text>
                </Text>
                <CommonOutlinedTextFieldWithIcon
                  isPickerModal
                  disabled={false}
                  editable={false}
                  value={moiQuanHeNgThongBao?.Name || 'Chọn mối quan hệ'}
                  styleContainer={{marginVertical: 5}}
                  onPressInput={() => refModalMoiQuanHe.current.show()}
                  error={inputErr[4]}
                />
              </View>

              <TextInputOutlined
                title="Họ tên"
                isRequired
                getRef={(ref) => (diaDiemRef = ref)}
                onSubmitEditing={() => noiDungRef?.focus()}
                value={tenLienHe}
                onChangeText={onChangeTextName}
                onFocus={onInputFocus}
                blurOnSubmit={false}
                error={inputErr[5]}
              />
              <TextInputOutlined
                title="Điện thoại"
                getRef={(ref) => (diaDiemRef = ref)}
                onSubmitEditing={() => noiDungRef?.focus()}
                value={dthoaiLienHe}
                onChangeText={onChangeTextPhone}
                onFocus={onInputFocus}
                isRequired={true}
                blurOnSubmit={false}
                error={inputErr[6]}
              />
              <TextInputOutlined
                title="Email"
                value={emailLienHe}
                blurOnSubmit={false}
                onFocus={onInputFocus}
                onChangeText={setEmailLienHe}
                getRef={(ref) => (diaDiemRef = ref)}
                onSubmitEditing={() => noiDungRef?.focus()}
              /> */}
            </View>
          </KeyboardAwareScrollView>
          <View style={styles.footerView}>
            <ButtonLinear title="Lưu" onPress={() => onPressRegisterSchedule('SAVE')} linearStyle={styles.footerBtn} linearColors={[colors.GRAY2, colors.GRAY2]} textStyle={{color: colors.BLACK_03}} />
            <ButtonLinear title="Lưu và chuyển" onPress={() => onPressRegisterSchedule('NEXT')} linearStyle={styles.footerBtn} />
          </View>
          <ModalChonHienTruongXe data={dataHienTruong} setValue={setHienTruong} ref={refModalHienTruong} onBackPress={() => refModalHienTruong.current.hide()} />
          <ModalChonMoiQuanHeVoiChuXe data={relationship} setValue={onChangeMoiQuanHe} ref={refModalMoiQuanHe} onBackPress={() => refModalMoiQuanHe.current.hide()} />
        </SafeAreaView>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  citiesData: state.cities.data,
  userInfo: state.user.data,
});
const mapDispatchToProps = {};
const KhaiBaoThongTinDiaDiemGiamDinhScreenConnect = connect(mapStateToProps, mapDispatchToProps)(KhaiBaoThongTinDiaDiemGiamDinhScreenComponent);
export const KhaiBaoThongTinDiaDiemGiamDinhScreen = memo(KhaiBaoThongTinDiaDiemGiamDinhScreenConnect, isEqual);
