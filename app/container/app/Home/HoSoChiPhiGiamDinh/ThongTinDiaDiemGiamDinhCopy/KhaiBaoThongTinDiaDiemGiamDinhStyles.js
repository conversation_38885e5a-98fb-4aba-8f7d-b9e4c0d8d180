import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {Dimensions, StyleSheet} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';

const {width, height} = Dimensions.get('window');
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    width: width,
    marginTop: 10,
  },
  content: {
    flex: 1,
    marginBottom: 60,
    marginHorizontal: 10,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  dropDownView: {
    marginVertical: 10,
    // flex : 1,
    // flexDirection: 'row',
    // justifyContent: 'flex-start',
  },
  headerView: {
    borderBottomWidth: 1,
    borderBottomColor: colors.WHITE6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
  },
  txtHeader: {
    fontSize: 17,
    fontWeight: '900',
  },
  iconClose: {
    color: colors.BLACK,
    opacity: 0.5,
  },
  txtHeaderInput: {
    marginBottom: 8,
    fontWeight: 'bold',
    // color: colors.BLACK
  },
  input: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 5,
    paddingLeft: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  inputView: {
    marginBottom: 10,
  },
  btn: {
    flex: 1,
    // borderWidth: 1,
    borderRadius: 30,
    // height:
  },

  btnView: {
    flex: 1,
    marginTop: 20,
    marginBottom: 30,
    flexDirection: 'row',
  },
  txtBtn: {
    paddingVertical: 15,
    paddingHorizontal: 15,
    textAlign: 'center',
    color: colors.WHITE,
    fontSize: 16,
  },
  btnClose: {
    backgroundColor: colors.GRAY9,
    marginRight: 30,
  },
  btnSucess: {
    backgroundColor: colors.GRAY9,
  },
  doubleInputRow: {
    width: (width - 30) / 2,
  },
  doubleInputRowView: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: {
    marginBottom: spacing.tiny,
    fontWeight: 'bold',
    // paddingRight: spacing.tiny,
  },
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: width,
    height: height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  footerView: {
    flex: 1,
    left: 0,
    right: 0,
    bottom: 0,
    width: width,
    paddingVertical: 10,
    position: 'absolute',
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: 16,
    marginBottom: isIOS ? getStatusBarHeight() - 10 : 0,
  },
  title: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: 'bold',
    color: colors.PRIMARY,
  },
});
