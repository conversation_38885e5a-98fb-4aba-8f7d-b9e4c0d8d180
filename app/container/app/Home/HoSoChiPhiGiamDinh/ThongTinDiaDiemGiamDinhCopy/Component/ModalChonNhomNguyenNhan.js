import {colors} from '@app/commons/Theme';
import {CheckboxComp, HeaderModal, Text} from '@component';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {FlatList, Platform, RefreshControl, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';

const ModalChonNhomNguyenNhanComponent = forwardRef((props, ref) => {
  const {onBackPress, setValue, danhMucSanPhamXe} = props;
  const [data, setData] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedItem, setSelectedItem] = useState(-1);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const fitlerNhomNguyenNhan = () => {
    if (danhMucSanPhamXe?.length > 0) {
      const filter = danhMucSanPhamXe.filter((item) => item.nhom === 'NHOM_NGUYEN_NHAN');
      setData(filter);
    }
    setRefreshing(false);
  };

  const onPressItem = (item, index) => {
    setSelectedItem(index);
    setValue && setValue(item);
    onBackPress && onBackPress();
  };

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  const onRefresh = () => {
    setRefreshing(true);
    fitlerNhomNguyenNhan();
  };

  // useEffect(() => {
  //   const lowerCaseSearchText = searchInput?.toLowerCase();
  //   const filter = data.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
  //   setData(filter);
  // }, [searchInput]);

  // const debounced = useDebouncedCallback((value) => {
  //   setSearchInput(value);
  // }, 500);

  /* RENDER */
  const renderItem = ({item, index}) => {
    const isChecked = selectedItem === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isChecked} checkboxStyle={styles.checkbox} onValueChange={() => onPressItem(item, index)} />
        <Text style={styles.txtView}>{item.label}</Text>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        keyExtractor={(item, index) => item + index.toString()}
        style={styles.flStyles}
      />
    );
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} onBackButtonPress={onBackPress} onModalShow={fitlerNhomNguyenNhan}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={onPressBack} title="Chọn nhóm nguyên nhân" />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 8,
    marginHorizontal: 16,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingVertical: 10,
    flexDirection: 'row',
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtBtnSave: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: 16,
  },
  checkbox: {
    marginRight: 5,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Platform.OS === 'android' ? 4 : 2,
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  flStyles: {
    paddingHorizontal: 10,
  },
});

export const ModalChonNhomNguyenNhan = ModalChonNhomNguyenNhanComponent;
