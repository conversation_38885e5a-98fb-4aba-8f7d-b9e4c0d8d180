import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {getImageNameFromUriCamera} from '@app/utils/CameraProvider';
import {cloneObject} from '@app/utils/DataProvider';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {Icon, ImageComp, ScreenComponent, Text} from '@component';
import {DATA_CONSTANT} from '@constant';
import R from '@R';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, FlatList, Platform, ScrollView, TouchableOpacity, View} from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';
import ModalCamera from './Modal/ModalCamera';
import ModalHangMuc from './Modal/ModalHangMuc';
import ModalXemChiTietAnh from './Modal/ModalXemChiTietAnh';
import ModalXemLaiAnh from './Modal/ModalXemLaiAnh';
import styles from './UploadHinhAnhHSGDStyles';

const UploadHinhAnhHSGDScreenComponent = (props) => {
  const {route, navigation} = props;
  const profileData = route.params.profileData;
  const categoryImage = profileData.nhom_hang_muc ? profileData.nhom_hang_muc : categoryImage;

  const [anhHoSo, setAnhHoSo] = useState(route.params.imagesData || []);
  const [currentPosition, setCurrentPosition] = useState({});
  const [toggleLoading, setToggleLoading] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ
  const [toggleModalHangMuc, setToggleModalHangMuc] = useState(false); //disable nút Next để tránh việc ấn liên tục đẫn đến up ảnh nhiều lầnÏ
  const [imgsData, setImgsData] = useState([]);
  //data của modal hướng dẫn chụp ảnh
  const [toggleModalImage, setToggleModalImage] = useState(false);
  const [modalImageSelectedData, setModalImageSelectedData] = useState(null);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [menuImageSelected, setMenuImageSelected] = useState({});

  //data modal phần xem lại ảnh
  const [anhXemLai, setAnhXemLai] = useState([]);
  const [toggleModalXemLaiAnh, setToggleModalXemLaiAnh] = useState(false);

  //data modal camera
  const [toggleModalCamera, setToggleModalCamera] = useState(false);

  const [dataHangMuc, setDataHangMuc] = useState([]);

  let [type, setType] = useState(-1);
  let [indexOpened, setIndexOpened] = useState(-1);

  /* FUNCTION  */
  useEffect(() => {
    getData();
    initimgsData();
    requestCurrentLocation(
      (position) => setCurrentPosition(position),
      (err) => console.log(err),
    );
  }, []);

  const getData = async () => {
    let params = {
      ma_doi_tac: profileData?.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DANH_SACH_HANG_MUC_ANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const filter = response.data_info.filter((item) => item.loai === DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TAI_LIEU);
      setDataHangMuc(cloneObject(filter));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // xử lý nút Back
  // useEffect(() => {
  //   let backHandler;
  //   navigation.addListener('focus', () => {
  //     backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
  //   });
  //   navigation.addListener('blur', () => {
  //     backHandler.remove();
  //   });
  // }, []);

  //xử lý nút quay lại ở màn Chụp ảnh
  const backAction = () => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn thoát chụp ảnh?', [
      {
        text: 'Huỷ',
        onPress: () => null,
        style: 'cancel',
      },
      {
        text: 'Đồng ý',
        onPress: () => NavigationUtil.pop(),
      },
    ]);
    return true;
  };
  //lấy ảnh thumbnail của hồ sơ
  const getThumbnailDocument = async () => {
    try {
      let response = await ESmartClaimEndpoint.getFileThumbnail(AxiosConfig.ACTION_CODE.LIST_THUMBNAIL_DOCUMENT, {so_id: profileData.ho_so?.so_id});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let imagesTmp = response.data_info.map((item) => {
        item.checked = false;
        item.path = item.duong_dan;
        item.name = item.ten_file;
        let nhom = {
          checked: false,
          ma: item.ma_file,
          ten: item.nhom_anh,
        };
        item.nhom = nhom;
        return item;
      });
      setAnhHoSo([...imagesTmp]);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  //khởi tạo ảnh step 1
  const initimgsData = () => {
    let imgsDataTmp = [{path: '', preView: R.icons.ic_gallery}];
    setImgsData(imgsDataTmp);
  };

  //xử lý ảnh khi chụp

  const handleImage = (imageData) => {
    let imagesTmp = imgsData;
    let nhom = {};
    {
      let imageName = getImageNameFromUriCamera(imageData.path); //lấy ra tên ảnh từ uri
      imagesTmp.unshift({
        path: imageData.path,
        nhom: nhom,
        name: imageName,
      });
    }
    setImgsData([...imagesTmp]);
  };

  // Xl ảnh thành công
  const xuLyAnhUploadThanhCong = (anhThanhCong) => {
    let imageDataTmp = imgsData;
    imageDataTmp.map((itemHangMuc) => {
      itemHangMuc.images.map((itemAnh) => {
        if (itemAnh.path === anhThanhCong.path) itemAnh.uploadThanhCong = true;
        return itemAnh;
      });
      return itemHangMuc;
    });
    setImgsData([...imageDataTmp]);
  };

  //xoá ảnh
  const removeImage = (imageData) => {
    Alert.alert('Thông báo', 'Bạn có chắc muốn xoá ảnh này', [
      {text: 'Để sau'},
      {
        text: 'Đồng ý',
        onPress: () => {
          let tmp = imgsData;
          tmp.splice(imageData.index, 1);
          setImgsData([...tmp]);
        },
      },
    ]);
  };

  // xl ảnh thất bại
  const xuLyAnhUploadThatBai = (anhThatBai) => {
    let imageDataTmp = imgsData;
    imageDataTmp.map((itemHangMuc) => {
      itemHangMuc.images.map((itemAnh) => {
        if (itemAnh.path == anhThatBai.path) {
          itemAnh.uploadThatBai = true;
          itemAnh.lyDoLoi = anhThatBai.lyDoLoi;
        }
        return itemAnh;
      });
      return itemHangMuc;
    });
    setImgsData([...imageDataTmp]);
  };

  // ấn nút NEXT
  const onPressNext = async () => {
    if (imgsData.length <= 0) return FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng chọn ít nhất 1 ảnh', 'warning');
    if (toggleLoading) return;
    let imagesUploadToServer = [];
    // console.log('sttMax', sttMax);
    imagesUploadToServer = imgsData.filter((imageItem) => imageItem.path !== '');
    setImgsData([...imgsData]);
    setToggleLoading(true);
    let response = await Promise.all(
      imagesUploadToServer.map(async (item) => {
        return uploadImageToServer(
          [item],
          (anhThanhCong) => {
            // // let imageUploadedTmp = imageUploaded;
            // imageUploadedTmp[currentPage] = imageUploadedTmp[currentPage] + 1;
            // // console.log('imageUploadedTmp', imageUploadedTmp);
            // setImageUploaded([...imageUploadedTmp]);
            xuLyAnhUploadThanhCong(anhThanhCong);
          },
          (anhThatBai) => xuLyAnhUploadThatBai(anhThatBai),
        );
      }),
    );
    let haveErr = false;
    response.map((item, index) => {
      if (item != true) {
        haveErr = true;
        if (item.message == 'Network Error') {
          FlashMessageHelper.showFlashMessage('Thông báo', 'Vui lòng kiểm tra lại đường truyền');
          return;
        } else FlashMessageHelper.showFlashMessage('Thông báo', 'Có lỗi khi upload ảnh thứ ' + index + 1, 'info');
      }
    });
    //thực hiện xong hết thì mới reset lại data
    setToggleLoading(false);
    getThumbnailDocument();
    if (haveErr) return;
    // if (route.params.loaiAnh) {
    //   NavigationUtil.pop();
    //   FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
    //   return;
    // }
    FlashMessageHelper.showFlashMessage('Thông báo', 'Tải ảnh lên thành công', 'success');
    NavigationUtil.pop();
  };

  //mở ảnh đã chụp
  const onPressAnhDaChup = () => {
    setToggleModalXemLaiAnh(true);
    let imgsTmp = [];
    //ảnh hiện trường
    imgsTmp = anhHoSo.filter((item) => item.loai == DATA_CONSTANT.IMAGE_CATEGORY_TYPE.TOAN_CANH && item.nhom_hang_muc == DATA_CONSTANT.IMAGE_CATEGORY_NHOM_HANG_MUC.HIEN_TRUONG);
    setAnhXemLai([...imgsTmp]);
  };

  //type : 0 - camera ; type : 1 - lib
  // const onPressOpenCamera = (indexOpened, menuImageData, type) => {
  //   setMenuImageSelected(menuImageData);
  //   if (menuImageData === undefined) {
  //     setToggleModalHangMuc(true);
  //   } else if (type === 0) {
  //     openCameraModal();
  //   } else if (type === 1) {
  //     openCamera(indexOpened, menuImageData, type);
  //   }
  //   setType(type);
  //   setIndexOpened(indexOpened);
  //   // actionSheetChupAnhRef.show();
  // };

  const onPressOpenCamera = (indexOpened, menuImageData, type) => {
    if (type === 0) openCameraModal();
    else if (type === 1) openCamera(indexOpened, menuImageData, type);
  };

  //mở IMAGE CROP CAMERA
  //type : 0 - camera ; type : 1 - library
  const openCamera = (indexOpened, menuImageData, type) => {
    console.log('jvhsoh');
    let imgCropOpts = {
      mediaType: 'photo', //mặc định là chụp ảnh từ camera
      cropping: false,
      enableRotationGesture: true,
      compressImageMaxWidth: 1800, //nén ảnh với chiều dài tối đa
      showCropGuidelines: true, //tắt khung 3x3 đi
      compressImageQuality: 0.5,
      useFrontCamera: false,
    };
    if (type == 1) {
      ImageCropPicker.openPicker(imgCropOpts)
        .then((data) => handleImage(data, menuImageData, indexOpened, type))
        .catch((err) => console.log('err', err));
      return;
    }
    //Open Camera
    ImageCropPicker.openCamera(imgCropOpts)
      .then((data) => handleImage(data, menuImageData, indexOpened, type))
      .catch((err) => console.log('err', err));
  };

  //tắt modal camera
  const onPressTatCameraModal = () => {
    setToggleModalCamera(false);
  };
  //mở modal camera
  const openCameraModal = () => {
    setToggleModalCamera(true);
  };

  //upload ảnh lên server
  const uploadImageToServer = (imagesData, cbSuccess) => {
    setDialogLoading(true);
    return new Promise(
      async (resolve) => {
        let files = [];
        imagesData.forEach((e, i) => {
          let file = {
            key_file: 'file' + i,
            // nhom: e.nhom.ma,
            x: currentPosition.coords?.latitude,
            y: currentPosition.coords?.longitude,
            stt_hang_muc: 0,
          };
          files.push(file);
        });

        try {
          let params = {
            pm: 'HSGD', //null
            nv: 'HSGD', // HSGD
            files: files,
            images: imagesData,
            ung_dung: 'MOBILE', //MOBILE
            so_id: profileData.so_id,
            ma_doi_tac: profileData.ma_doi_tac,
          };
          console.log('🚀 ~ file: UploadHinhAnhHSGDScreen.js:348 ~ params', params);
          let response = await ESmartClaimEndpoint.uploadFile(AxiosConfig.ACTION_CODE.UPLOAD_FILE, params);
          setDialogLoading(false);
          if (!response || !response.state_info || response.state_info.status !== 'OK') {
            resolve(response);
            return;
          }
          resolve(true);
        } catch (error) {
          setDialogLoading(false);
          Alert.alert('Thông báo', error.message);
          resolve(error);
        }
        return;
      },
      (reject) => reject(),
    );
  };

  //lấy chi tiết ảnh đã chụp
  const onPressLayChiTietAnh = async (imageData) => {
    try {
      let params = {
        so_id: imageData.so_id,
        bt: imageData.bt,
      };
      let response = await ESmartClaimEndpoint.getFile(AxiosConfig.ACTION_CODE.DOCUMENT_DETAIL, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setModalImageSelectedData({
        title: 'Ảnh chi tiết',
        imageData: response.data_info.duong_dan,
        base64: true,
      });
      setToggleModalImage(true);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  //xem chi tiết ảnh đã chụp
  const onPressXemLaiAnh = (imageData) => {
    setModalImageSelectedData({
      title: 'Ảnh chi tiết',
      imageData: imageData,
    });
    setToggleModalImage(true);
  };

  // các nút ở footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        <View style={styles.btnBack} />
        <TouchableOpacity activeOpacity={0.5} onPress={onPressNext} style={styles.btnNext} disabled={toggleLoading}>
          {!toggleLoading ? <Text style={styles.txtBtnBottom}>Tải lên</Text> : <ActivityIndicator size="large" color={colors.GRAY} style={{flex: 1}} />}
          <View style={styles.iconRightBtnView}>
            <Icon.Ionicons name={'checkmark-sharp'} size={25} style={styles.iconRightBtn} color={colors.WHITE} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderImageItem = (imageData) => {
    return (
      <ImageComp
        uploadFromLib={true}
        imageData={imageData}
        width={dimensions.width / 2 - 30}
        height={dimensions.width / 2 - 30}
        removeImage={removeImage}
        onPressXemLaiAnh={onPressXemLaiAnh}
        onPressOpenCamera={onPressOpenCamera}
      />
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Tải ảnh lên"
      onPressBack={backAction}
      renderView={
        <View flex={1}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <FlatList data={imgsData} renderItem={renderImageItem} numColumns={2} initialNumToRender={imgsData.length} keyExtractor={(e, i) => i.toString()} />
            {/* {renderImageCategoryButton()} */}

            {/* modal camera  */}
            <ModalCamera
              key="ModalCamera"
              handleImage={handleImage}
              giayToDuocChon={menuImageSelected}
              setDialogLoading={setDialogLoading}
              toggleModalCamera={toggleModalCamera}
              tatCameraModal={onPressTatCameraModal}
              menuImageStep3Selected={menuImageSelected}
            />
            <ModalXemChiTietAnh key="ModalXemChiTietAnh" toggleModalImage={toggleModalImage} setToggleModalImage={setToggleModalImage} modalImageSelectedData={modalImageSelectedData} />
            <ModalXemLaiAnh setToggleModalXemLaiAnh={setToggleModalXemLaiAnh} toggleModalXemLaiAnh={toggleModalXemLaiAnh} anhXemLai={anhXemLai} onPressLayChiTietAnh={onPressLayChiTietAnh} />
            <ModalHangMuc
              data={dataHangMuc}
              closeModal={setToggleModalHangMuc}
              toggleModalHangMuc={toggleModalHangMuc}
              onHangMucSelected={(data) => {
                setMenuImageSelected(data);
                setDialogLoading(true);
                setTimeout(
                  () => {
                    setDialogLoading(false);
                    // openCameraModal();
                    if (type === 0) {
                      openCameraModal();
                    } else openCamera(indexOpened, data, 1);
                  },
                  Platform.OS == 'android' ? 100 : 1000,
                );
              }}
            />
          </ScrollView>

          {renderFooter()}
        </View>
      }
    />
  );
};

export const UploadHinhAnhHSGDScreen = memo(UploadHinhAnhHSGDScreenComponent, isEqual);
