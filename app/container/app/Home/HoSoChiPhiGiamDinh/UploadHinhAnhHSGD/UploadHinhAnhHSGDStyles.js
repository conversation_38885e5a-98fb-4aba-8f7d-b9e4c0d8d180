import {colors} from '@app/commons/Theme';
import {dimensions, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  imageCompView: {
    width: dimensions.width / 2,
    position: 'relative',
  },
  scrollView: {
    width: dimensions.width,
  },
  imageProcess: {
    borderRadius: 20,
    marginVertical: 15,
    resizeMode: 'cover',
    marginHorizontal: 15,
    backgroundColor: colors.GRAY2,
    width: dimensions.width / 2 - 30,
    height: dimensions.width / 2 - 30,
  },
  stepIndicator: {
    marginVertical: 10,
  },
  stepLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    color: colors.GRAY10,
  },
  stepLabelSelected: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    color: colors.GREEN,
  },
  footerView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: vScale(10),
    justifyContent: 'space-around',
  },
  buttonCameraView: {
    bottom: 0,
    alignSelf: 'center',
    position: 'absolute',
    flexDirection: 'row',
  },
  btnHint: {
    flex: 0,
    borderRadius: 40,
  },
  btnBack: {
    flex: 1,
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    backgroundColor: colors.PRIMARY_08,
  },
  iconRightBtnView: {
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
    backgroundColor: colors.PRIMARY_DARK_08,
  },
  iconRightBtn: {
    flex: 0,
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
  },
  iconLeftBtnView: {
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    backgroundColor: colors.PRIMARY_DARK_08,
  },
  iconLeftBtn: {
    flex: 0,
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
  },
  btnNext: {
    flex: 1,
    borderRadius: 30,
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 10,
    borderTopLeftRadius: 30,
    borderBottomLeftRadius: 30,
    backgroundColor: colors.PRIMARY_08,
  },
  txtBtnBottom: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'center',
    color: colors.WHITE,
  },
  btnHintView: {
    right: 10,
    bottom: 90,
    position: 'absolute',
  },
  btnImageView: {
    right: 10,
    bottom: 90,
    position: 'absolute',
  },
  imageCategoryTitleView: {
    flex: 1,
    marginTop: 10,
    flexDirection: 'row',
    marginHorizontal: 15,
    alignItems: 'center',
    borderColor: colors.GRAY,
  },
  imageCategoryTitle: {
    flex: 1,
    marginLeft: 10,
    fontWeight: 'bold',
    color: colors.BLACK,
    justifyContent: 'flex-start',
  },
  btnCamera: {
    opacity: 0.5,
    borderRadius: 40,
    paddingVertical: 10,
    marginHorizontal: 10,
    paddingHorizontal: 10,
    backgroundColor: colors.PRIMARY,
  },
});
