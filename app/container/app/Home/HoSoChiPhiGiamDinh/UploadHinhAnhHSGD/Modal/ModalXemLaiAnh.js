import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import R from '@R';
import React, {memo, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, Image, Platform, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import ImageProcess from 'react-native-image-progress';
import Modal from 'react-native-modal';
import Progress from 'react-native-progress/Circle';

function ModalXemLaiAnh(props) {
  // console.log('ModalXemLaiAnh');
  let scrollViewModalRef = useRef(null);
  const [scrollOffSet, setScrollOffSet] = useState(null);
  const contentModalHeight = useState((dimensions.height / 3) * 4);
  /* RENDER */
  let title = '';
  // if (props.currentPage == 0) title = 'Ảnh hiện trường đã chụp';
  if (props.currentPage == 0) title = 'Ảnh toàn cảnh đã chụp';
  else if (props.currentPage == 1) title = 'Ảnh tổn thất đã chụp';
  else if (props.currentPage == 2) title = 'Ảnh hồ sơ, giấy tờ đã chụp';

  //render ITEM ảnh xem lại
  const renderAnhXemLaiItem = (data) => {
    let imageData = data.item;
    return (
      <TouchableOpacity
        onPress={() => {
          if (Platform.OS == 'ios') return;
          props.onPressLayChiTietAnh(imageData);
        }}>
        <ImageProcess
          source={{
            uri: `data:image/gif;base64,${imageData.duong_dan}`,
          }}
          indicator={Progress.Circle}
          style={styles.imageDocument}
          imageStyle={{borderRadius: 20}}
          indicatorProps={{
            size: 70,
            borderWidth: 0,
            color: colors.PRIMARY,
            unfilledColor: colors.PRIMARY_LIGHT,
          }}
          renderError={() => (
            <View>
              <Image source={R.icons.img_no_image} style={styles.imageDocument} resizeMode={'contain'} />
            </View>
          )}
        />
        {/* {imageData.stt_hang_muc != undefined && <Text children={imageData.stt_hang_muc} style={{position: 'absolute', top: 20, left: 25, color: '#FFF', fontSize: 18, fontWeight: 'bold'}} />} */}
      </TouchableOpacity>
    );
  };
  const renderAnhXemLai = (listAnhXemLai) => {
    return <FlatList data={listAnhXemLai} renderItem={renderAnhXemLaiItem} keyExtractor={(item) => item.bt.toString()} numColumns={3} horizontal={false} style={{marginBottom: 10}} />;
  };

  return (
    <Modal
      isVisible={props.toggleModalXemLaiAnh}
      onSwipeComplete={() => props.setToggleModalXemLaiAnh(false)}
      onBackdropPress={() => props.setToggleModalXemLaiAnh(false)}
      swipeDirection={['down']}
      scrollTo={(point) => scrollViewModalRef.current.scrollTo(point)}
      scrollOffset={scrollOffSet}
      scrollOffsetMax={dimensions.height / 2 - contentModalHeight} // content height - ScrollView height
      propagateSwipe={true}
      style={styles.modal}>
      <View style={styles.modalView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle}>{title}</Text>
          <TouchableOpacity style={styles.closeView} onPress={() => props.setToggleModalXemLaiAnh(false)}>
            <Icon.AntDesign name="closecircleo" size={20} />
          </TouchableOpacity>
        </View>
        <ScrollView
          ref={scrollViewModalRef}
          onScroll={(event) => {
            setScrollOffSet(event.nativeEvent.contentOffset.y);
          }}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}>
          {/* {props.currentPage == 0 && renderAnhXemLai(props.anhXemLai)}
          {(props.currentPage == 1 || props.currentPage == 2) && ( */}
          <View>
            <FlatList
              data={props.anhXemLai}
              renderItem={(data) => (
                <>
                  {/* <Text style={styles.tieuDeDanhMucAnhXemLai}>{(props.currentPage == 1 ? data.item.sttHangMuc + './ ' : '') + data.item.ten}</Text> */}
                  <Text style={styles.tieuDeDanhMucAnhXemLai}>{data.item.ten}</Text>
                  {renderAnhXemLai(data.item.images)}
                </>
              )}
              keyExtractor={(item) => item.ma}
            />
          </View>
          {/* )} */}
        </ScrollView>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalView: {
    backgroundColor: colors.WHITE,
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  modalTitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 10,
    flex: 1,
    marginLeft: 30,
    // borderWidth: 1,
  },
  closeView: {
    marginRight: 15,
    // position: 'absolute',
    // top: ,
    // right: 15,
  },
  tieuDeDanhMucAnhXemLai: {
    marginLeft: 10,
    fontWeight: 'bold',
  },
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 1,
  },
});

// const mapStateToProps = (state) => ({});

// const mapDispatchToProps = {};

// export default connect(mapStateToProps, mapDispatchToProps)(ModalXemLaiAnh);
export default memo(ModalXemLaiAnh, isEqual);
