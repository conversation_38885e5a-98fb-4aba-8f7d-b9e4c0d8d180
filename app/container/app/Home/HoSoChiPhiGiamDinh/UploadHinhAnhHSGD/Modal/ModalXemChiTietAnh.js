import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

function ModalXemChiTietAnh(props) {
  const {toggleModalImage, setToggleModalImage, modalImageSelectedData} = props;

  return (
    <Modal
      propagateSwipe={true}
      swipeDirection={['down']}
      isVisible={toggleModalImage}
      onSwipeComplete={() => setToggleModalImage(false)}
      onBackdropPress={() => setToggleModalImage(false)}
      style={styles.modal}>
      <View style={styles.modalImageView}>
        <View style={styles.modalImageContentView}>
          <View style={styles.modalTitleView}>
            <Text style={styles.modalTitle}>{modalImageSelectedData?.title}</Text>
            <TouchableOpacity style={styles.closeView} onPress={() => setToggleModalImage(false)}>
              <Icon.AntDesign name="closecircle" color="red" size={25} style={styles.closeIcon} />
            </TouchableOpacity>
          </View>
          <Image
            source={
              modalImageSelectedData?.base64
                ? {
                    uri: `data:image/gif;base64,${modalImageSelectedData.imageData}`,
                  }
                : modalImageSelectedData?.imageData
            }
            style={styles.imageModal}
            resizeMode={'contain'}
          />
        </View>
      </View>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalImageView: {
    width: dimensions.width,
    height: dimensions.height,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalImageContentView: {
    backgroundColor: colors.WHITE,
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  closeView: {
    marginRight: 15,
  },
  imageModal: {
    margin: 10,
    borderRadius: 20,
    width: dimensions.width - 30,
    height: dimensions.width - 30,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 10,
    textAlign: 'center',
    // borderWidth: 1,
  },
  closeIcon: {
    borderRadius: 20,
    backgroundColor: colors.WHITE,
  },
});

// const mapStateToProps = (state) => ({});

// const mapDispatchToProps = {};

// export default connect(mapStateToProps, mapDispatchToProps)(ModalXemChiTietAnh);
export default memo(ModalXemChiTietAnh, isEqual);
