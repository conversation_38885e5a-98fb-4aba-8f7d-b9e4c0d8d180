import {colors} from '@app/commons/Theme';
import {Dimensions, StyleSheet} from 'react-native';

const {width} = Dimensions.get('window');
export default StyleSheet.create({
  contentView: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.WHITE,
  },
  scrollView: {
    width: width,
  },
  btnLoginView: {
    flexDirection: 'row',
    backgroundColor: colors.PRIMARY,
    marginHorizontal: 10,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerView: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    backgroundColor: colors.WHITE,
  },
  stepIndicator: {
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingRight: 5,
    color: colors.WHITE,
    paddingVertical: 10,
    textTransform: 'uppercase',
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.BLUE3,
  },
  contentDetail: {
    marginBottom: 7,
    backgroundColor: colors.WHITE,
    borderRadius: 10,
  },
  modalSelectorView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
    marginRight: 15,
  },
  dropDownSelectedTxt: {
    flex: 1,
    fontSize: 14,
    paddingRight: 5,
    paddingLeft: 10,
    paddingVertical: 10,
  },
  headerTitleView: {
    marginBottom: 10,
    paddingVertical: 9,
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
  },
  errTxt: {
    fontSize: 12,
    marginLeft: 12,
    color: colors.RED1,
  },
  descRow: {
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewRowGCN: {
    marginVertical: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: {
    color: colors.GRAY6,
  },
  detail: {
    flex: 1,
    textAlign: 'right',
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  doubleInputRow: {
    width: (width - 30) / 2,
  },
  doubleInputRowView: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
