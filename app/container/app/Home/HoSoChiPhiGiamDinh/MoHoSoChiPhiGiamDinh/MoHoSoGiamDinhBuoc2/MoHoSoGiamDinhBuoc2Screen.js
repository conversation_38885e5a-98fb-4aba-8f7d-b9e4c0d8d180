import {colors} from '@app/commons/Theme';
import {CommonOutlinedTextFieldWithIcon, Text} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {View} from 'react-native';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {ModalChonLuongXuLy, ModalChonMoiQuanHeVoiChuXe} from './Components';
import styles from './MoHoSoGiamDinhBuoc2Styles';

const relationship = [
  {Name: 'Chủ xe', ma: 'QH.0001', Value: 'QH.0001', Id: 'QH.0001'},
  {Name: 'Lái xe', ma: 'QH.0002', Value: 'QH.0002', Id: 'QH.0002'},
  {Name: '<PERSON>ườ<PERSON> kh<PERSON>c', ma: 'QH.0005', Value: 'QH.0005', Id: 'QH.0005'},
];
// const dataHienTruong = [
//   {ten: 'Xe đang ở hiện trường', ma: 'D'},
//   {ten: 'Xe không ở hiện trường', ma: 'K'},
// ];

const MoHoSoGiamDinhBuoc2ScreenComponent = (props) => {
  const {selectedItemHoSo, setGioTB, setNgayTB, luongXly, moiQuanHeNgThongBao, chiTietHS, dataLuongXly, showForm} = props;

  let refModalMoiQuanHe = useRef(null);
  let refModalLuongXuly = useRef(null);
  // let refModalHienTruong = useRef(null);

  const [toggleGioThongBao, setToggleGioThongBao] = useState(false);
  const [toggleNgayThongBao, setToggleNgayThongBao] = useState(false);

  const [gioThongBao, setGioThongBao] = useState(moment().format('HH:mm'));
  const [ngayThongBao, setNgayThongBao] = useState(moment().format('DD/MM/YYYY'));
  // const [dataLuongXly, setDataLuongXly] = useState(dataInfo?.dk);

  useEffect(() => {
    setGioTB(gioThongBao);
  }, [gioThongBao]);
  useEffect(() => {
    setNgayTB(ngayThongBao);
  }, [ngayThongBao]);

  const onPressDateConfirm = (date, setToggleDateTime, setDate, mode) => {
    setToggleDateTime(false);
    if (mode === 'date') {
      setDate(moment(date).format('DD/MM/YYYY'));
    } else {
      setDate(moment(date).format('HH:mm'));
    }
  };

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate) => {
    return (
      <DateTimePickerModal
        mode={mode}
        Date={date}
        locale={'vi_VN'}
        display="spinner"
        confirmTextIOS="Chọn"
        maximumDate={maxDate}
        minimumDate={minDate}
        cancelTextIOS="Để sau"
        isVisible={toggleDateTime}
        onCancel={() => setToggleDateTime(false)}
        onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, mode)}
        themeVariant="light"
        isDarkModeEnabled={false}
        // is24Hour={false}
      />
    );
  };

  const renderContactInfo = () => {
    return (
      <View style={[styles.contentDetail]}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin người thông báo</Text>
        </View>
        <View style={{marginHorizontal: 10}}>
          <CommonOutlinedTextFieldWithIcon
            isPickerModal
            editable={false}
            value={moiQuanHeNgThongBao?.Name}
            styleContainer={{marginVertical: 5}}
            placeholder={'Mối quan hệ với chủ xe'}
            onPressInput={() => refModalMoiQuanHe.current.show()}
            error={props.inputErrLienHe[8]}
          />
          <CommonOutlinedTextFieldWithIcon
            value={props.userNoticeName}
            onChangeText={(value) => {
              props.setUserNoticeName(value);
              if (props.checkboxIsUserNotice) props.setUserContactName(value);
            }}
            placeholder="Họ tên *"
            error={props.inputErrLienHe[0]}
            editable={true}
          />
          <CommonOutlinedTextFieldWithIcon
            value={props.userNoticePhone}
            placeholder="Điện thoại *"
            keyboardType="numeric"
            onChangeText={(value) => {
              props.setUserNoticePhone(value);
              if (props.checkboxIsUserNotice) props.setUserContactPhone(value);
            }}
            error={props.inputErrLienHe[1]}
            editable={true}
          />
          <CommonOutlinedTextFieldWithIcon
            value={props.userNoticeEmail}
            placeholder="Email"
            keyboardType="email-address"
            onChangeText={(value) => {
              props.setUserNoticeEmail(value);
              if (props.checkboxIsUserNotice) props.setUserContactEmail(value);
            }}
            error={props.inputErrLienHe[2]}
            editable={true}
          />
        </View>
      </View>
    );
  };
  /**THÔNG TIN NGƯỜI LIÊN HỆ */
  const renderContactUser = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin người liên hệ</Text>
        </View>
        <View marginHorizontal={10}>
          <View style={styles.descRow}>
            <Text style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>Tôi là người thông báo và là người liên hệ</Text>
            <BouncyCheckbox
              iconStyle={{borderColor: colors.PRIMARY}}
              fillColor={colors.PRIMARY}
              isChecked={props.checkboxIsUserNotice}
              onPress={() => {
                props.onPressCheckbox();
              }}
            />
          </View>
          <CommonOutlinedTextFieldWithIcon
            value={props.userContactName}
            onChangeText={(value) => props.setUserContactName(value)}
            placeholder="Họ tên *"
            disabled={props.checkboxIsUserNotice}
            editable={!props.checkboxIsUserNotice}
            error={props.inputErrLienHe[3]}
          />
          <CommonOutlinedTextFieldWithIcon
            value={props.userContactPhone}
            placeholder="Điện thoại *"
            keyboardType="numeric"
            onChangeText={(value) => props.setUserContactPhone(value)}
            disabled={props.checkboxIsUserNotice}
            editable={!props.checkboxIsUserNotice}
            error={props.inputErrLienHe[4]}
          />
          <CommonOutlinedTextFieldWithIcon
            value={props.userContactEmail}
            placeholder="Email"
            keyboardType="email-address"
            onChangeText={(value) => props.setUserContactEmail(value)}
            disabled={props.checkboxIsUserNotice}
            editable={!props.checkboxIsUserNotice}
            error={props.inputErrLienHe[5]}
          />
        </View>
      </View>
    );
  };

  //thông tin xe
  const renderCarInfo = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông tin xe</Text>
        </View>
        <View marginHorizontal={10}>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Biển số:</Text>
            <Text style={styles.detail}>{chiTietHS?.bien_xe || ''}</Text>
          </View>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Chủ xe:</Text>
            <Text style={styles.detail}>{chiTietHS?.ten_chu_xe || ''}</Text>
          </View>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Điện thoại:</Text>
            <Text style={styles.detail}>{chiTietHS?.dthoai || ''}</Text>
          </View>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Email:</Text>
            <Text style={styles.detail}>{chiTietHS?.email || ''}</Text>
          </View>
        </View>
      </View>
    );
  };
  // luồng xử lý
  const renderLuongXuly = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Chọn luồng xử lý</Text>
        </View>
        <View marginHorizontal={10}>
          <CommonOutlinedTextFieldWithIcon
            error={props.inputErrLienHe[7]}
            value={luongXly?.ten}
            editable={false}
            placeholder="Chọn luồng xử lý*"
            // disabled={true}
            isPickerModal
            onPressInput={() => refModalLuongXuly.current.show()}
          />
          {/* <CommonOutlinedTextFieldWithIcon value={'MOBILE'} editable={false} placeholder="Nguồn thông báo*" disabled={true} /> */}
          <View style={styles.doubleInputRowView}>
            <View style={styles.doubleInputRow}>
              <CommonOutlinedTextFieldWithIcon
                // error={props.inputErrLienHe[0]}
                isDateTimeField
                disabled={false}
                editable={false}
                placeholder="Giờ thông báo *"
                styleContainer={{marginVertical: 5}}
                onPressInput={() => setToggleGioThongBao(true)}
                value={gioThongBao}
              />
              {renderDateTimeComp(toggleGioThongBao, setToggleGioThongBao, setGioThongBao, gioThongBao, 'time', null, null, 0)}
            </View>
            <View style={styles.doubleInputRow}>
              <CommonOutlinedTextFieldWithIcon
                // error={props.inputErrLienHe[0]}
                isDateTimeField
                editable={true}
                disabled={false}
                placeholder="Ngày thông báo *"
                styleContainer={{marginVertical: 5}}
                onPressInput={() => setToggleNgayThongBao(true)}
                value={ngayThongBao}
              />
              {renderDateTimeComp(toggleNgayThongBao, setToggleNgayThongBao, setNgayThongBao, ngayThongBao, 'date', null, new Date(), 0)}
            </View>
          </View>
          {/* <CommonOutlinedTextFieldWithIcon value={'hienTruong.ten'} editable={false} placeholder="Hiện trường*" isPickerModal onPressInput={() => refModalHienTruong.current.show()} /> */}
        </View>
      </View>
    );
  };

  const renderThongTinGCN = () => {
    return (
      <View style={styles.contentDetail}>
        <View style={styles.headerTitleView}>
          <Text style={styles.headerTitle}>Thông giấy chứng nhận</Text>
        </View>
        <View marginHorizontal={10}>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Tên khách hàng:</Text>
            <Text style={styles.detail}>{selectedItemHoSo?.ten_chu_xe || ''}</Text>
          </View>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Số hợp đồng:</Text>
            <Text style={styles.detail}>{selectedItemHoSo?.so_hdong || ''}</Text>
          </View>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Số giấy chứng nhận:</Text>
            <Text style={styles.detail}>{selectedItemHoSo?.so_gcn || ''}</Text>
          </View>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Loại hình:</Text>
            <Text style={styles.detail}>{selectedItemHoSo?.ten_loai_gcn || ''}</Text>
          </View>
          <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Hiệu lực: </Text>
            <Text style={styles.detail}>
              {selectedItemHoSo?.ngay_hl_bh || ''} - {selectedItemHoSo?.ngay_kt_bh || ''}
            </Text>
          </View>
          {/* <View style={styles.viewRowGCN}>
            <Text style={styles.label}>Tổng phí: </Text>
            <NumericFormat value={5000000} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.detail}>{value} đ</Text>} />
          </View> */}
        </View>
      </View>
    );
  };
  return (
    <View style={styles.contentView}>
      {/* Thông tin xe */}
      {!showForm && (
        <View>
          {renderThongTinGCN()}
          {renderCarInfo()}
        </View>
      )}

      {renderLuongXuly()}
      {/* THÔNG TIN NGƯỜI THÔNG BÁO */}
      {renderContactInfo()}
      {/* Thông tin người liên hệ */}
      {renderContactUser()}
      <ModalChonMoiQuanHeVoiChuXe data={relationship} setValue={props.setMoiQuanHeNgThongBao} ref={refModalMoiQuanHe} onBackPress={() => refModalMoiQuanHe.current.hide()} />
      <ModalChonLuongXuLy luongXly={luongXly} data={dataLuongXly} setValue={props.setLuongXly} ref={refModalLuongXuly} onBackPress={() => refModalLuongXuly.current.hide()} />
      {/* <ModalChonHienTruongXe data={dataHienTruong} setValue={props.setHienTruong} ref={refModalHienTruong} onBackPress={() => refModalHienTruong.current.hide()} /> */}
    </View>
  );
};

export const MoHoSoGiamDinhBuoc2Screen = memo(MoHoSoGiamDinhBuoc2ScreenComponent, isEqual);
