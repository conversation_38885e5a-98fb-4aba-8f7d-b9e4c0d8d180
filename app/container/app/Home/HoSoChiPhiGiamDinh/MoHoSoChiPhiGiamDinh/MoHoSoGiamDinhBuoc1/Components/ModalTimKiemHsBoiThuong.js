import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import axiosConfig from '@app/services/axiosConfig';
import {PartnerEndpoint} from '@app/services/endPoints';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon} from '@component';
import moment from 'moment';
import React, {forwardRef, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {memo} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Dimensions, Platform, SafeAreaView, StyleSheet, View} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

const {width, height} = Dimensions.get('screen');
const currentDate = moment(new Date()).format('DD/MM/YYYY');

const ModalTimKiemHsBoiThuongComponent = forwardRef((props, ref) => {
  const {setValue, onBackPress, setData, setLoading} = props;
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const initFormInput = {
    ngay_xr: currentDate,
    so_hdong: '',
    so_gcn: '',
    so_may: '',
    bien_so_xe: (__DEV__ && '30h99999') || '',
    so_khung: '',
    ten_kh: '',
    cmt_kh: '',
    mst_kh: '',
  };

  const titleInput = ['Ngày xảy ra *', 'Số hợp đồng', 'Số GCN bảo hiểm', 'Biển số xe', 'Số khung', 'Số máy'];
  const [formInput, setFormInput] = useState(initFormInput);

  // modal value
  const [ngayHieuLuc, setNgayHieuLuc] = useState(new Date());
  // toggle modal
  const [toggleNgayDau, setToggleNgayDau] = useState(false);
  const [index, setIndex] = useState(0);

  const getData = async () => {
    setLoading(true);
    let params = formInput;
    try {
      let response = await PartnerEndpoint.searchGCN(axiosConfig.ACTION_CODE.TIM_KIEM_GCN_XE_DOI_TAC, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) setData([]);
      else {
        const data = response.data_info.hd;
        setData(data);
      }
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onChangeText = async (field, value) => {
    setFormInput((prev) => ({...prev, [field]: value}));
  };

  useEffect(() => {
    if (ngayHieuLuc) {
      onChangeText('ngay_xr', Number(moment(ngayHieuLuc).format('YYYYMMDD')));
    }
  }, [ngayHieuLuc]);

  const onSearchPress = () => {
    setValue && setValue(formInput);
    onBackPress && onBackPress();
    getData();
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetField = () => {
    setFormInput(initFormInput);
    setIndex(0);
  };

  const memoDisableBtnSearch = useMemo(() => {
    return formInput.bien_so_xe === '' && formInput.so_gcn === '' && formInput.so_hdong === '' && formInput.so_khung === '' && formInput.so_may === '';
  }, [formInput]);

  /**RENDER  */
  const renderDateTimeComp = (toggle, setToggle, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      date={date}
      mode={mode}
      locale={'vi_VN'}
      display="spinner"
      isVisible={toggle}
      confirmTextIOS="Chọn"
      maximumDate={maxDate}
      minimumDate={minDate}
      cancelTextIOS="Để sau"
      onCancel={() => setToggle(false)}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggle, setDateTime)}
      themeVariant="light"
      isDarkModeEnabled={false}
      //   is24Hour={true}
    />
  );

  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.formInput}>
          {/* Ngày đầu/Ngày cuối */}
          <View>
            <CommonOutlinedTextFieldWithIcon
              isDateTimeField
              editable={true}
              disabled={false}
              // error={props.inputErrLienHe[0]}
              placeholder={titleInput[0]}
              styleContainer={styles.containerInput}
              onPressInput={() => setToggleNgayDau(true)}
              value={moment(ngayHieuLuc).format('DD/MM/YYYY')}
            />
            {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, setNgayHieuLuc, ngayHieuLuc, 'date', null, new Date(), 0)}
          </View>
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.so_hdong}
            placeholder={titleInput[1]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_hdong', text)}
            // error={props.inputErrLienHe[0]}
          />
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.so_gcn}
            placeholder={titleInput[2]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_gcn', text)}
            // error={props.inputErrLienHe[0]}
          />
          <CommonOutlinedTextFieldWithIcon
            keyboardType="default"
            value={formInput.bien_so_xe}
            placeholder={titleInput[3]}
            onChangeText={(text) => onChangeText('bien_so_xe', text)}
            // error={props.inputErrLienHe[1]}
            disabled={false}
            editable={true}
            styleContainer={styles.containerInput}
          />
          <CommonOutlinedTextFieldWithIcon
            value={formInput.so_khung}
            keyboardType="default"
            placeholder={titleInput[4]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_khung', text)}
            // error={props.inputErrLienHe[2]}
          />
          <CommonOutlinedTextFieldWithIcon
            // error={props.inputErrLienHe[0]}
            disabled={false}
            editable={true}
            placeholder={titleInput[5]}
            value={formInput.so_may}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_may', text)}
          />
        </View>
      </View>
    );
  };

  /* RENDER */
  const renderContent = () => {
    return <View style={{flex: 1, marginTop: 10}}>{renderFormInput()}</View>;
  };
  return (
    <Modal onBackButtonPress={onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} onModalShow={() => resetField()} style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal title="Tìm kiếm đối tượng" onBackPress={onBackPress} />
        <KeyboardAwareScrollView>{renderContent()}</KeyboardAwareScrollView>
        <View style={styles.footer}>
          <ButtonLinear textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} onPress={resetField} linearStyle={styles.btnLoginView} title="Nhập lại" />
          <ButtonLinear onPress={onSearchPress} linearStyle={styles.btnLoginView} disabled={memoDisableBtnSearch} title="Tìm kiếm" />
        </View>
      </SafeAreaView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  modalView: {
    flex: 1,
    width: width,
    height: height,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  searchInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  backBtn: {},
  iconBack: {},
  itemHangMucView: {
    paddingVertical: 10,
  },
  stepIndicator: {
    backgroundColor: colors.WHITE,
    paddingVertical: 10,
  },
  headerView: {
    marginVertical: 10,
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  icon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  headerTitleView: {
    marginBottom: 10,
    paddingVertical: 9,
    paddingHorizontal: 12,
    backgroundColor: colors.WHITE1,
  },
  headerTitle: {
    marginBottom: 16,
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  formInput: {
    marginHorizontal: 10,
  },
  btnLoginView: {
    marginHorizontal: 8,
  },
  txtBtnLogin: {
    fontSize: 16,
    paddingVertical: 10,
    color: colors.WHITE,
  },

  subLabel: {
    color: colors.GRAY10,
  },
  contentRow: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  title: {
    fontSize: 18,
    marginLeft: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  containerInput: {
    height: 45,
  },
  doubleRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footer: {
    bottom: 0,
    paddingTop: 10,
    borderTopWidth: 0.2,
    position: 'absolute',
    flexDirection: 'row',
    paddingHorizontal: 15,
    borderTopColor: colors.GRAY2,
    marginBottom: Platform.OS === 'ios' ? getStatusBarHeight() : 0,
  },
});

export const ModalTimKiemHsBoiThuong = memo(ModalTimKiemHsBoiThuongComponent, isEqual);
