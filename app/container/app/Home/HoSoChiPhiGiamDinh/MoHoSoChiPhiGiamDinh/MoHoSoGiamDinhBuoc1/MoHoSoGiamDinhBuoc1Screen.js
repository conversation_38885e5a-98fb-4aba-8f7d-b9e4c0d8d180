import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import {DropdownPicker, SearchBar, Text} from '@app/components';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import React, {useEffect, useRef, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {FlatList, Image, RefreshControl, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {connect, useSelector} from 'react-redux';
import {useDebouncedCallback} from 'use-debounce';
import {ModalTimKiemHsBoiThuong} from './Components';
import styles from './MoHoSoGiamDinhBuoc1Styles';

let timer;
const NGHIEP_VU = [
  {label: 'Người', value: 'NGUOI'},
  {label: 'Xe', value: 'XE'},
];
function BoiThuongXeBuoc1(props) {
  const {
    setData,
    data,
    dataLoaiHinh,
    loaiHinhSelected,
    setLoaiHinhSelected,
    dataLHNV,
    loaiHinhNVSelected,
    setLoaiHinhNVSelected,
    bienSoXe,
    setBienSoXe,
    tenChuXe,
    setTenChuXe,
    showForm,
    setShowForm,
  } = props;
  const [selectedItem, setSelectedItem] = useState(-1);
  const [isVisibleModal, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const userInfo = useSelector(selectUser);
  // const [showForm, setShowForm] = useState(false);
  const [openLoaiHinh, setOpenLoaiHinh] = useState(false);
  const [openLoaiHinhNV, setOpenLoaiHinhNV] = useState(false);
  const [openNghiepVu, setOpenNghiepVu] = useState(false);
  const [inputErr, setInputErr] = useState(new Array(4).fill(''));

  const [searchText, setSearchText] = useState('');
  const [nvSelected, setNvSelected] = useState('');

  let refModalTimKiemHopDongXe = useRef(null);
  let tenChuXeRef = useRef(null);
  let bsXeRef = useRef(null);

  const onPressSearch = () => {
    setShowForm(false);
    refModalTimKiemHopDongXe.current.show();
  };
  const onPressKhongXDDT = () => {
    setShowForm(true);
  };

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: {
      nghiepVu: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (data.length <= 0) {
      timer = setTimeout(() => {
        setIsVisible(true);
      }, 500);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [data]);

  const onPressItem = (i) => {
    setSelectedItem(i);
    props.setSelectedItemHoSo(i);
  };

  useEffect(() => {
    // navigation.addListener('focus', () => {
    //   getData();
    // });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const debounced = useDebouncedCallback((value) => {
    setSearchText(value);
  }, 500);

  useEffect(() => {
    handleSubmit(getData);
  }, [searchText]);

  const getData = async (data) => {
    console.log('🚀 ~ file: MoHoSoGiamDinhBuoc1Screen.js:109 ~ getData ~ data', data);
    setLoading(true);
    try {
      let params = {
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
        nv: data.nghiepVu,
        nd_tim: searchText,
      };
      console.log('vao day', params);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TIM_KIEM_HS_BOI_THUONG, params);
      console.log('🚀 ~ file: MoHoSoGiamDinhBuoc1Screen.js:99 ~ getData ~ response', response);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) setData([]);
      else {
        const data = response.data_info;
        setData(data);
      }
    } catch (error) {
      setLoading(false);
      console.log('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  /**RENDER  */

  const renderProfileItem = ({item, index}) => {
    let linearColors = [];
    if (selectedItem === item) {
      linearColors = [colors.WHITE1, colors.WHITE1];
    } else linearColors = [colors.WHITE, colors.WHITE];
    return (
      <TouchableOpacity key={index} onPress={() => onPressItem(item)}>
        <LinearGradient colors={linearColors} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.profileItemView, {borderColor: selectedItem === item ? colors.PRIMARY : colors.GRAY}]}>
          {/* <Text style={styles.profileTxtHoSo(colors.BLACK)}>{item.so_hs}</Text> */}
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số HSBT: <Text style={styles.content}>{item.so_hs}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Biển số xe: <Text style={styles.content}>{item.bien_so_xe}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số giấy chứng nhận: <Text style={styles.content}>{item.so_gcn}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Nghiệp vụ: <Text style={styles.content}>{item.ten_loai_gcn}</Text>{' '}
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Hiệu lực bh: <Text style={styles.content} children={item.ngay_hl_bh} /> -
              <Text style={styles.content} children={item.ngay_kt_bh} />
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Tên chủ xe: <Text style={styles.content}>{item.ten_chu_xe}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Đơn vị cấp đơn:<Text style={styles.content}>{item.ten_chi_nhanh}</Text>
            </Text>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <View flex={1}>
      <Controller
        control={control}
        name="nghiepVu"
        rules={{
          required: true,
        }}
        render={({field: {onChange, value}}) => (
          <DropdownPicker
            title="Nghiệp vụ"
            zIndex={9000}
            items={NGHIEP_VU}
            itemSelected={value}
            placeholder="Chọn nghiệp vụ"
            isRequired={true}
            searchable={false}
            isOpen={openNghiepVu}
            setOpen={setOpenNghiepVu}
            setItemSelected={(dispatch) => onChange(dispatch())}
            maxHeight={150}
            containerStyle={{marginHorizontal: 14}}
            inputErr={errors.nghiepVu && getErrMessage('nghiepVu', errors.nghiepVu.type)}
          />
        )}
      />
      <SearchBar placeholder="Nhập số hồ sơ, biển số xe" onTextChange={debounced} onPressSearch={handleSubmit(getData)} />
      {/* <View flexDirection="row" marginHorizontal={5}>
        <ButtonLinear
          onPress={onPressSearch}
          title="Tìm kiếm hồ sơ bồi thường"
          linearStyle={styles.btnSearch}
          textStyle={{color: showForm ? colors.BLACK_03 : colors.WHITE}}
          linearColors={showForm ? [colors.GRAY2, colors.GRAY2] : [colors.PRIMARY, colors.PRIMARY]}
        />
        <ButtonLinear
          title="Không x/đ đ.tượng"
          onPress={onPressKhongXDDT}
          linearStyle={styles.btnSearch}
          textStyle={{color: !showForm ? colors.BLACK_03 : colors.WHITE}}
          linearColors={!showForm ? [colors.GRAY2, colors.GRAY2] : [colors.PRIMARY, colors.PRIMARY]}
        />
      </View> */}
      <FlatList
        data={data || []}
        removeClippedSubviews={true}
        refreshControl={<RefreshControl refreshing={loading} />}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderProfileItem}
        ListEmptyComponent={
          <View style={styles.noDataView}>
            <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
            <Text>Chưa có dữ liệu</Text>
          </View>
        }
      />
      <ModalTimKiemHsBoiThuong ref={refModalTimKiemHopDongXe} setData={setData} setLoading={setLoading} onBackPress={() => refModalTimKiemHopDongXe.current.hide()} />
    </View>
  );
}

const mapStateToProps = () => ({});

const mapDispatchToProps = {
  // updateSwitchNavigator,
};

export default connect(mapStateToProps, mapDispatchToProps)(BoiThuongXeBuoc1);
