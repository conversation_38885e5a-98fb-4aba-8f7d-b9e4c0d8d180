import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  footerView: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: colors.GRAY2,
    paddingVertical: vScale(10),
    justifyContent: 'space-around',
    paddingHorizontal: scale(spacing.small),
  },
  profileItemView: {
    padding: 8,
    borderWidth: 1,
    borderRadius: 10,
    borderColor: colors.GRAY,
    marginVertical: vScale(5),
    marginHorizontal: scale(10),
  },
  subLabel: {
    color: colors.GRAY10,
  },
  contentRow: {
    flexDirection: 'row',
    marginVertical: vScale(2),
  },
  content: {
    flex: 1,
    color: colors.PRIMARY,
  },
  imageNoData: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  noDataView: {
    alignItems: 'center',
  },
});
