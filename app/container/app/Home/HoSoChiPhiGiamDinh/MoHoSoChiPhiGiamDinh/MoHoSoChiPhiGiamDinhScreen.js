import R from '@app/assets/R';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, SearchBar, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {FlatList, Image, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {useDebouncedCallback} from 'use-debounce';
import styles from './Styles';
import {spacing} from '@app/theme';

const NGHIEP_VU = [
  {label: 'Người', value: 'NGUOI'},
  {label: 'Xe ô tô', value: 'XE'},
  {label: 'Xe máy', value: 'XE_MAY'},
];

const MoHoSoGiamDinhScreenComponent = (props) => {
  console.log('MoHoSoGiamDinhScreenComponent');
  const userInfo = useSelector(selectUser);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [openNghiepVu, setOpenNghiepVu] = useState(false);
  const [onSubmiting, setOnSubmiting] = useState(false);
  const [data, setData] = useState();

  const [searchText, setSearchText] = useState('');

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: {
      nghiepVu: '',
    },
    mode: 'onChange',
  });

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const getData = async (data) => {
    setLoading(true);
    try {
      let params = {
        ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
        nv: data.nghiepVu,
        nd_tim: searchText,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.TIM_KIEM_HS_BOI_THUONG, params);
      console.log('🚀 ~ file: MoHoSoGiamDinhScreen.js:65 ~ getData ~ response', response);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) setData([]);
      else {
        const data = response.data_info;
        setData(data);
      }
    } catch (error) {
      setLoading(false);
      console.log('Thông báo', error.message);
    }
  };
  const moHoSoGiamDinh = async () => {
    setOnSubmiting(true);
    try {
      let params = {
        so_id: null, // id hs giám định
        so_id_bt: selectedItem.so_id,
        nv: selectedItem.nv,
        so_hs: '', //số hs giám địhh
        tien_dx: 0, //-- nếu lưu mới thì = 0
        tien_duyet: 0, //-- nếu lưu mới thì = 0
        tien_thoa_thuan: 0, // -- nếu lưu mới thì = 0
        tien_thue: 0, // -- nếu lưu mới thì = 0
        ghi_chu: '', // -- null
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.MO_HS_GIAM_DINH, params);
      setOnSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Mở hồ sơ giám định thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setOnSubmiting(false);
      console.log('Err', error.message);
    }
  };

  const debounced = useDebouncedCallback((value) => {
    setSearchText(value);
  }, 500);

  useEffect(() => {
    handleSubmit(getData);
  }, [searchText]);

  const onPressItem = (item) => {
    setSelectedItem(item);
  };

  const renderProfileItem = ({item, index}) => {
    let linearColors = [];
    if (selectedItem === item) {
      linearColors = [colors.WHITE1, colors.WHITE1];
    } else linearColors = [colors.WHITE, colors.WHITE];
    return (
      <TouchableOpacity key={index} onPress={() => onPressItem(item)}>
        <LinearGradient colors={linearColors} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.profileItemView, {borderColor: selectedItem === item ? colors.PRIMARY : colors.GRAY}]}>
          {/* <Text style={styles.profileTxtHoSo(colors.BLACK)}>{item.so_hs}</Text> */}
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số HSBT: <Text style={styles.content}>{item.so_hs}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Ngày mở HS: <Text style={styles.content}>{item.ngay_mo_hs}</Text>
            </Text>
          </View>
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Số GCN: <Text style={styles.content}>{item.gcn}</Text>
            </Text>
          </View>
          {/* <View style={styles.contentRow}>
            <Text style={styles.subLabel}>
              Nghiệp vụ: <Text style={styles.content}>{item.ten_loai_gcn}</Text>{' '}
            </Text>
          </View> */}
          <View style={styles.contentRow}>
            <Text style={styles.subLabel}>Tiền duyệt: </Text>
            <NumericFormat value={item.tien_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.txtDetail} selectable children={value} />} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    return (
      <SafeAreaView style={styles.container}>
        <Controller
          control={control}
          name="nghiepVu"
          rules={{
            required: true,
          }}
          render={({field: {onChange, value}}) => (
            <DropdownPicker
              title="Nghiệp vụ"
              zIndex={9000}
              items={NGHIEP_VU}
              itemSelected={value}
              placeholder="Chọn nghiệp vụ"
              isRequired={true}
              searchable={false}
              isOpen={openNghiepVu}
              setOpen={setOpenNghiepVu}
              setItemSelected={(dispatch) => onChange(dispatch())}
              maxHeight={150}
              inputErr={errors.nghiepVu && getErrMessage('nghiepVu', errors.nghiepVu.type)}
              containerStyle={{marginHorizontal: spacing.small}}
            />
          )}
        />
        <View zIndex={10}>
          <SearchBar placeholder="Nhập số hồ sơ, biển số xe" onTextChange={debounced} onPressSearch={handleSubmit(getData)} />
        </View>
        <FlatList
          data={data}
          removeClippedSubviews={true}
          refreshControl={<RefreshControl refreshing={loading} />}
          keyExtractor={(item, index) => index.toString()}
          renderItem={renderProfileItem}
          ListEmptyComponent={
            <View style={styles.noDataView}>
              <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
              <Text>Chưa có dữ liệu</Text>
            </View>
          }
        />
      </SafeAreaView>
    );
  };

  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Mở hồ sơ giám định"
      renderView={renderContent()}
      footer={
        <ButtonLinear
          loading={onSubmiting}
          disabled={onSubmiting || !selectedItem}
          title="Mở hồ sơ giám định"
          onPress={moHoSoGiamDinh}
          linearColors={!selectedItem ? [colors.GRAY, colors.GRAY] : [colors.PRIMARY, colors.PRIMARY]}
          textStyle={{color: selectedItem ? colors.WHITE : colors.BLACK_03}}
        />
      }
    />
  );
};

export const MoHoSoGiamDinhScreen = memo(MoHoSoGiamDinhScreenComponent, isEqual);
