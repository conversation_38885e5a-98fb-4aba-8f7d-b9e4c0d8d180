import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, CustomTabBar, Empty, Icon, ScreenComponent, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {ChiTietHoSo} from './ChiTietHoSo/ChiTietHoSo';
import styles from './Styles';

const ChiTietHoSoChiPhiGiamDinhScreenComponent = (props) => {
  console.log('ChiTietHoSoChiPhiGiamDinhScreenComponent');
  const {route, navigation} = props;
  const userInfo = useSelector(selectUser);
  const {prevScreen, profileDetail} = route?.params;

  const scrollViewRef = useRef(null);
  const tabViewRef = useRef(null);

  const [refreshing, setRefreshing] = useState(false);
  const [chiTietHSGiamDinh, setChiTietHSGiamDinh] = useState(null);

  const [dataChiPhiGiamDinh, setDataChiPhiGiamDinh] = useState([]);

  const [btnTabActive, setBtnTabActive] = useState(0);

  let actionSheetRef = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      getChiTietHSGiamDinh(profileDetail);
    });
  }, []);

  useEffect(() => {
    if (dataChiPhiGiamDinh?.length > 0 && prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_TON_THAT) {
      tabViewRef.current.goToPage(1);
    }
  }, [dataChiPhiGiamDinh]);

  // useEffect(() => {
  //   let profileDetail = props.notificationFirebase;
  //   profileDetail?.ho_so && getProfileData(profileDetail, 'props.notificationFirebase');
  // }, [props.notificationFirebase]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getChiTietHSGiamDinh(chiTietHSGiamDinh);
  }, [chiTietHSGiamDinh]);

  const getChiTietHSGiamDinh = async (profileDetail, choGoiAPI) => {
    // if (APP_NAME == 'ESCS' && choGoiAPI) Alert.alert('Thông báo', choGoiAPI);
    // setDebugGetchiTietHSGiamDinh(choGoiAPI);
    //lấy chi tiết hồ sơ
    try {
      let paramsProfileDetail = {
        ma_doi_tac: profileDetail?.ma_doi_tac,
        so_id: profileDetail?.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.CHI_TIET_HS_GIAM_DINH, paramsProfileDetail);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setChiTietHSGiamDinh(response.data_info.ho_so[0]);
      // setdataCacVuTonThat(response.data_info.dien_bien);
      setDataChiPhiGiamDinh(response.data_info.chi_phi_ct);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // const onPressHuyHoSo = () => {
  //   Alert.alert('Thông báo', 'Bạn có chắc chắn muốn HUỶ hồ sơ tiếp nhận', [
  //     {
  //       text: 'Huỷ',
  //       style: 'cancel',
  //     },
  //     {text: 'Đồng ý', onPress: () => handleHuyHoSo()},
  //   ]);
  // };
  // const onPressGoHuyHoSo = () => {
  //   Alert.alert('Thông báo', 'Bạn có chắc chắn muốn GỠ HUỶ hồ sơ tiếp nhận', [
  //     {
  //       text: 'Huỷ',
  //       style: 'cancel',
  //     },
  //     {text: 'Đồng ý', onPress: () => handleGoHuyHoSo()},
  //   ]);
  // };

  const handleHuyHoSo = async () => {
    const params = {
      so_id: chiTietHSGiamDinh?.ho_so?.so_id, //id hồ sơ
      ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.HUY_HO_SO_TIEP_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Huỷ hồ sơ tiếp nhận thành công', 'success');
      if (prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1) NavigationUtil.pop(2);
      else NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const handleGoHuyHoSo = async () => {
    const params = {
      so_id: chiTietHSGiamDinh?.ho_so?.so_id, //id hồ sơ
      ma_doi_tac: userInfo.nguoi_dung.ma_doi_tac,
    };
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.GO_HUY_HO_SO_TIEP_NHAN, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Gỡ huỷ hồ sơ tiếp nhận thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const handleXoaChiPhi = async (soId, bt) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn muốn XOÁ chi phí này không', [{text: 'Để sau'}, {text: 'Đồng ý', onPress: () => confirmXoaChiPhi(soId, bt)}]);
  };

  const confirmXoaChiPhi = async (soId, bt) => {
    try {
      const params = {
        so_id: soId, //id hồ sơ
        bt: bt,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XOA_CP_GIAM_DINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá chi phí thành công', 'success');
      getChiTietHSGiamDinh(chiTietHSGiamDinh);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressLaySoHoSo = async () => {
    try {
      let params = {
        ma_doi_tac: chiTietHSGiamDinh.ma_doi_tac,
        so_id: chiTietHSGiamDinh.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_SO_HO_SO_GIAM_DINH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Lấy số hồ sơ thành công', 'success');
      getChiTietHSGiamDinh(chiTietHSGiamDinh);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  /** RENDER */

  const renderFooter = () => {
    return (
      <>
        {btnTabActive == 1 ? (
          <View style={styles.footerView}>
            <ButtonLinear title="Thêm chi phí GĐ" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_CHI_PHI_GIAM_DINH, {chiTietHSGiamDinh: chiTietHSGiamDinh})} />
            {dataChiPhiGiamDinh?.length > 0 && (
              <ButtonLinear title="Trình/Huỷ trình" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.TRINH_CP_GD, {profileInfo: chiTietHSGiamDinh})} linearStyle={{marginLeft: spacing.small}} />
            )}
          </View>
        ) : null}
      </>
    );
  };

  const renderContent = () => {
    return (
      <SafeAreaView style={styles.container}>
        <ScrollableTabView
          locked
          ref={tabViewRef}
          style={styles.centerView}
          initialPage={0}
          onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))}
          renderTabBar={() => <CustomTabBar />}>
          <ScrollView
            tabLabel="Thông tin hồ sơ"
            scrollEnabled={true}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
            ref={scrollViewRef}
            showsVerticalScrollIndicator={false}>
            <ChiTietHoSo onPressLaySoHS={onPressLaySoHoSo} chiTietHSGiamDinh={chiTietHSGiamDinh} prevScreen={route.params.prevScreen} />
          </ScrollView>
          <View tabLabel="Chi phí giám định" style={styles.centerView}>
            {dataChiPhiGiamDinh?.length > 0 ? (
              <RenderChiPhiGiamDinh chiTietHSGiamDinh={chiTietHSGiamDinh} dataChiPhiGiamDinh={dataChiPhiGiamDinh} hanldeXoaChiPhi={(soId, bt) => handleXoaChiPhi(soId, bt)} />
            ) : (
              <View style={styles.emptyView}>
                <Empty description="Chưa có dữ liệu" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />
              </View>
            )}
          </View>
        </ScrollableTabView>
      </SafeAreaView>
    );
  };

  return (
    <ScreenComponent
      headerBack
      onPressBack={prevScreen === SCREEN_ROUTER_APP.KHAI_BAO_BOI_THUONG_XE_BUOC1 ? () => NavigationUtil.pop(2) : () => NavigationUtil.pop()}
      headerTitle="Chi tiết hồ sơ chi phí giám định"
      renderView={renderContent()}
      footer={renderFooter()}
    />
  );
};

export const ChiTietHoSoChiPhiGiamDinhScreen = memo(ChiTietHoSoChiPhiGiamDinhScreenComponent, isEqual);

const RenderChiPhiGiamDinh = ({dataChiPhiGiamDinh, chiTietHSGiamDinh, hanldeXoaChiPhi}) => {
  return (
    <ScrollView>
      {dataChiPhiGiamDinh.map((item, index) => {
        return (
          <TouchableOpacity
            key={index}
            onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_CHI_PHI_GIAM_DINH, {type: 'EDIT', chiTietChiPhiGiamDinh: item, chiTietHSGiamDinh: chiTietHSGiamDinh})}>
            <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={[styles.profileItemView]}>
              <View flex={1}>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Ngày phát sinh: </Text>
                    <Text style={styles.detailContent}>{item.ngay_ht_text}</Text>
                  </View>
                  <TouchableOpacity onPress={() => hanldeXoaChiPhi(item.so_id, item.bt)}>
                    <Icon.AntDesign name="closesquareo" color={colors.RED1} size={22} />
                  </TouchableOpacity>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Tên chi phí: </Text>
                    <Text style={[styles.detailContent, {color: colors.PRIMARY}]}>{item.ten_chi_phi}</Text>
                  </View>
                </View>
                <View style={styles.contentRow}>
                  <View style={styles.contentCol}>
                    <Text style={styles.labelItem}>Nhóm giám định: </Text>
                    <Text style={styles.detailContent}>{item.nhom_gd_ten}</Text>
                  </View>
                </View>

                <View style={styles.contentRow}>
                  <View flexDirection="row" flex={1}>
                    <Text style={styles.labelItem}>CP dự kiến: </Text>
                    <NumericFormat value={item.tien_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                  </View>
                  <View flexDirection="row" flex={1}>
                    <Text style={styles.labelItem}>Tiền duyệt: </Text>
                    <NumericFormat value={item.tien_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                  </View>
                </View>

                <View style={styles.contentRow}>
                  <View flexDirection="row" flex={1}>
                    <Text style={styles.labelItem}>CP thực tế: </Text>
                    <NumericFormat value={item.tien_thoa_thuan} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                  </View>
                  <View flexDirection="row" flex={1}>
                    <Text style={styles.labelItem}>Tiền thuế: </Text>
                    <NumericFormat value={item.tien_thue} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                  </View>
                </View>
                <View flexDirection="row" flex={1}>
                  <Text style={styles.labelItem}>Tổng cộng: </Text>
                  <NumericFormat value={item.tong_cong} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.detailContent} />} />
                </View>
              </View>
              {/* <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} /> */}
            </LinearGradient>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
};
