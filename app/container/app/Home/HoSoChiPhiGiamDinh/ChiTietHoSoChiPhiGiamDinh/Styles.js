import {colors} from '@app/commons/Theme';
import {dimensions, FontSize, moderateScale, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  centerView: {
    flex: 1,
  },
  txtTitle: {
    fontSize: FontSize.size12,
    marginBottom: vScale(spacing.tiny),
  },
  txtDetail: {
    color: colors.GRAY6,
    textAlign: 'justify',
    paddingRight: scale(10),
    fontSize: FontSize.size14,
  },
  detailContent: {
    fontWeight: '500',
    color: colors.BLACK_03,
    fontSize: FontSize.size14,
    lineHeight: moderateScale(20),
  },
  contentRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: vScale(spacing.tiny),
  },
  contentCol: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtBtnThemChiPhi: {
    fontWeight: '600',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  subLabel: {
    fontWeight: '700',
    color: colors.PRIMARY,
    fontSize: FontSize.size16,
    marginVertical: vScale(spacing.tiny),
  },
  profileItemView: {
    borderWidth: 1,
    borderRadius: 10,
    flexDirection: 'row',
    borderColor: colors.GRAY,
    justifyContent: 'space-between',
    marginVertical: vScale(spacing.tiny),
    paddingVertical: vScale(spacing.tiny),
    marginHorizontal: scale(spacing.small),
    paddingHorizontal: scale(spacing.small),
  },
  labelItem: {
    fontWeight: '600',
    color: colors.GRAY6,
    fontSize: FontSize.size14,
    lineHeight: moderateScale(20),
  },
  footerView: {
    flex: 1,
    flexDirection: 'row',
  },
  footerBtn: {
    marginHorizontal: scale(spacing.small),
  },
  doubleBtn: {
    width: dimensions.width,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
