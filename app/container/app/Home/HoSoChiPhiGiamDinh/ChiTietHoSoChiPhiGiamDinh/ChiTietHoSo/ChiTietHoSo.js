import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {spacing} from '@app/theme';
import {Icon, Text} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {TouchableOpacity, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import styles from './ChiTietHoSoStyles';
import {HEADER_TITLE, ICON_HEADER} from './Constant';

function ChiTietHoSoComponent(props) {
  const {chiTietHSGiamDinh, onPressLaySoHS} = props;

  const onPressHeader = (headerTitle) => {
    if (headerTitle === HEADER_TITLE[1]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.XEM_CHI_TIET_HINH_ANH_HSGD, {profileInfo: chiTietHSGiamDinh});
    }
    if (headerTitle === HEADER_TITLE[2]) {
      NavigationUtil.push(SCREEN_ROUTER_APP.DS_NGUOI_THU_HUONG_HOA_DON_CT, {profileInfo: chiTietHSGiamDinh});
    }
  };

  /* RENDER */
  // RENDER header các đầu mục
  const renderProfileInformationHeader = (title, data) => {
    let indexIcon = HEADER_TITLE.findIndex((item) => item == title);
    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <TouchableOpacity style={[styles.inforHeaderView]} onPress={() => onPressHeader(title, data)}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name={ICON_HEADER[indexIcon]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>{title + ' ' + dataLength}</Text>
            </View>
          </View>
          {title != HEADER_TITLE[0] && <Icon.SimpleLineIcons name={'arrow-right'} size={15} style={styles.iconBtnTopRightView} />}
        </TouchableOpacity>
        {title != HEADER_TITLE[0] && (
          <View style={{backgroundColor: colors.WHITE5}}>
            <View style={styles.blockHeader} />
          </View>
        )}
      </View>
    );
  };
  const renderThongTinChungChidren = (title, data, containerStyle, subValue) => (
    <View style={[styles.inforView, containerStyle]}>
      <Text style={styles.txtTitle} children={title} />
      {typeof data == 'number' ? (
        <NumericFormat
          value={data}
          displayType={'text'}
          thousandSeparator={true}
          renderText={(value) => <Text style={styles.txtDetail} selectable children={value + (subValue ? ' (' + subValue + '%)' : '')} />}
        />
      ) : (
        // <Text style={styles.txtDetail} selectable children={data} />
        <>
          {data?.trim() !== '' && <Text style={styles.txtDetail} selectable children={data} />}
          {data?.trim() === '' && title === 'Số HS' && (
            <TouchableOpacity style={styles.laySoHoSoView} onPress={onPressLaySoHS}>
              <Icon.FontAwesome name="ticket" color={colors.PRIMARY} size={20} style={{marginRight: spacing.smaller}} />
              <Text style={[styles.txtDetail, {color: colors.PRIMARY}]} children="Lấy số hồ sơ" />
            </TouchableOpacity>
          )}
        </>
      )}
    </View>
  );
  //render ra thông tin hồ sơ
  const renderProfileInformation = () => {
    if (!chiTietHSGiamDinh) return;
    const ho_so = chiTietHSGiamDinh;
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View style={{flex: 1}}>
          {renderProfileInformationHeader(HEADER_TITLE[0])}
          <View style={styles.contentRow}>
            {renderThongTinChungChidren('Số HS', ho_so.so_hs || '', {borderTopLeftRadius: 20, backgroundColor: colors.WHITE})}
            {renderThongTinChungChidren('Số HSBT', ho_so.so_hs_bt || '', {borderTopLeftRadius: 20, backgroundColor: colors.WHITE})}
          </View>
          <View style={styles.contentRow}>
            {renderThongTinChungChidren('Ngày mở HS', ho_so.ngay_ht)}
            {renderThongTinChungChidren('Nghiệp vụ', ho_so.nv_text)}
          </View>
          <View style={styles.contentRow}>
            {renderThongTinChungChidren('Chi phí dự kiến', ho_so.tien_dx || '')}
            {renderThongTinChungChidren('Chi phí thực tế', ho_so.tien_thoa_thuan || '')}
          </View>
          {renderThongTinChungChidren('Tiền thuế', ho_so.tien_thue)}
          <View style={styles.contentRow}>
            {renderThongTinChungChidren('Ngày trình', ho_so.ngay_trinh || 'Chưa có')}
            {renderThongTinChungChidren('Ngày duyệt', ho_so.ngay_duyet || 'Chưa có')}
          </View>
          {renderThongTinChungChidren('Trạng thái', ho_so.trang_thai_ten || '')}
        </View>

        {/* GIẤY CHỨNG NHẬN */}
        {renderProfileInformationHeader(HEADER_TITLE[1])}
        {renderProfileInformationHeader(HEADER_TITLE[2])}
      </View>
    );
  };

  return <>{renderProfileInformation()}</>;
}

export const ChiTietHoSo = memo(ChiTietHoSoComponent, isEqual);
