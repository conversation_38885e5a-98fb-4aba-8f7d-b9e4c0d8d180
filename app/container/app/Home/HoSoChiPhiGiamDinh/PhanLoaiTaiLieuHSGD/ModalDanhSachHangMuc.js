import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {CheckboxComp, HeaderModal, Text} from '@component';
import React, {useState} from 'react';
import {FlatList, Platform, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';
import {connect} from 'react-redux';

function ModalDanhSachHangMuc(props) {
  const {isVisible, onBackPress, setValue, data} = props;

  const [selectedItemIndex, setSelectedItemIndex] = useState(-1);

  const onPressBack = () => {
    onBackPress && onBackPress();
  };

  const onPressItem = (item, index) => {
    onBackPress && onBackPress();
    setValue && setValue(item);
    setSelectedItemIndex(index);
  };

  /* RENDER */

  const renderItem = ({item, index}) => {
    const isCheck = selectedItemIndex === index;
    return (
      <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onPressItem(item, index)}>
        <CheckboxComp value={isCheck} checkboxStyle={styles.checkbox} onValueChange={(value) => onPressItem(item, index, value)} />
        <View style={styles.txtView}>
          <Text style={{flex: 1}}>{item.ten}</Text>
        </View>
      </TouchableOpacity>
    );
  };
  const renderContent = () => {
    return (
      <FlatList
        data={data}
        extraData={data}
        renderItem={renderItem}
        onEndReachedThreshold={0.1}
        style={styles.flStyles}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={Platform.OS === 'ios' ? false : true}
        // refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      />
    );
  };
  return (
    <Modal animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal}>
      <>
        <HeaderModal title="Hạng mục tài liệu" onBackPress={onPressBack} />
        {renderContent()}
      </>
    </Modal>
  );
}
const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
    paddingTop: Platform.OS == 'ios' ? 50 : 10,
    // paddingHorizontal: 10,
  },
  modalCameraView: {
    flex: 1,
    width: dimensions.width,
    height: dimensions.height,
    justifyContent: 'center',
  },
  modalCameraContent: {
    backgroundColor: colors.WHITE,
    flex: 1,
    // borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    paddingRight: 10,
    paddingBottom: 10,
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 8,
    flexDirection: 'row',
  },
  checkbox: {
    marginRight: 8,
  },
  txtView: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Platform.OS === 'android' ? 4 : 0,
  },
  titleModal: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  flStyles: {
    marginBottom: 20,
    paddingHorizontal: 10,
  },
});

const mapStateToProps = (state) => ({});
const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(ModalDanhSachHangMuc);
