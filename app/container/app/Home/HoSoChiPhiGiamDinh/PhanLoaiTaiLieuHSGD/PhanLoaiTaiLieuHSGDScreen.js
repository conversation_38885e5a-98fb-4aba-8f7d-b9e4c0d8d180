import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import ModalDanhSachHangMuc from './ModalDanhSachHangMuc';

const PhanLoaiTaiLieuHSGDScreenComponent = (props) => {
  const {route} = props;
  const {imagesClassify, profileData} = route?.params;
  const [dataHangMuc, setDataHangMuc] = useState([]);
  const [isvisible, setIsvisible] = useState(false);
  const [tenHangMuc, setTenHangMuc] = useState();
  const [itemHangMuc, setItemHangMuc] = useState({});

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    try {
      let params = {
        ma_doi_tac: profileData?.ma_doi_tac,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DANH_SACH_HANG_MUC_ANH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      else {
        const filter = response.data_info.filter((n) => n.ma_doi_tac === profileData?.ma_doi_tac && n.loai === 'TLHSGD');
        setDataHangMuc(filter);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const onSave = async () => {
    if (imagesClassify.length > 0) {
      try {
        let arrBt = imagesClassify.map((item) => item.bt);
        let params = {
          pm: 'HSGD',
          loai: itemHangMuc?.loai,
          so_id: profileData?.so_id,
          hang_muc: itemHangMuc?.ma,
          ma_doi_tac: profileData?.ma_doi_tac,
          bt: arrBt,
        };
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.PHAN_LOAI_TAI_LIEU_THEO_HM_HSGD, params);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        FlashMessageHelper.showFlashMessage('Thông báo', 'Phân loại hạng mục thành công', 'success');
        NavigationUtil.pop();
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
      return;
    }
  };

  const iconSize = 20;

  const renderContent = () => {
    return (
      <View marginTop={16} marginHorizontal={10}>
        <View marginBottom={10}>
          <Text style={styles.titleLabel}>
            Nhóm tài liệu
            <Text children="(*)" style={{color: colors.RED1}} />
          </Text>
          <TouchableOpacity style={[styles.buttonChonBenhVien, {backgroundColor: colors.GRAY2}]}>
            <Text style={{flex: 1}}>Giấy tờ tài liệu</Text>
          </TouchableOpacity>
        </View>
        <View marginBottom={10}>
          <Text style={styles.titleLabel}>
            Hạng mục tài liệu
            <Text children="(*)" style={{color: colors.RED1}} />
          </Text>
          <TouchableOpacity style={styles.buttonChonBenhVien} onPress={() => setIsvisible(true)}>
            <Text style={{flex: 1}}>{itemHangMuc.ten || 'Chọn hạng mục'}</Text>
            {!itemHangMuc.ten ? (
              <Icon.MaterialIcons name="keyboard-arrow-down" size={iconSize} style={styles.icon} color={colors.BLUE1} />
            ) : (
              <TouchableOpacity onPress={() => setItemHangMuc({})}>
                <Icon.MaterialIcons name="close" size={iconSize} style={styles.icon} color={colors.BLUE1} />
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.actButtonGr}>
          <TouchableOpacity style={[styles.actButton, {backgroundColor: colors.GRAY2}]} onPress={() => NavigationUtil.pop()}>
            <Text style={[styles.actButtonTxt, {color: colors.BLACK_03}]}>Huỷ</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actButton} onPress={onSave}>
            <Text style={styles.actButtonTxt}>Lưu</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle={'Phân loại tài liệu'}
      renderView={
        <SafeAreaView style={styles.container}>
          {renderContent()}
          <ModalDanhSachHangMuc setTenHangMuc={setTenHangMuc} setValue={setItemHangMuc} data={dataHangMuc} isVisible={isvisible} onBackPress={() => setIsvisible(false)} />
        </SafeAreaView>
      }
    />
  );
};

export const PhanLoaiTaiLieuHSGDScreen = memo(PhanLoaiTaiLieuHSGDScreenComponent, isEqual);

const styles = StyleSheet.create({
  imageDocument: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
    marginHorizontal: 15,
    marginVertical: 15,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // borderWidth: 1,
  },
  switch: {
    // flex: 1,
    marginVertical: 10,
    marginHorizontal: 10,
    alignItems: 'center',
    flexDirection: 'row',
  },
  checkboxImgView: {
    right: 15,
    bottom: 15,
    position: 'absolute',
    borderTopLeftRadius: 5,
    backgroundColor: colors.WHITE,
  },
  container: {
    flex: 1,
  },
  footerView: {
    flex: 1,
    left: 0,
    right: 0,
    bottom: 20,
    width: dimensions.width,
    paddingVertical: 10,
    position: 'absolute',
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    padding: 5,
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY2,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
  actButtonGr: {
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  actButton: {
    borderRadius: 5,
    paddingVertical: 8,
    marginHorizontal: 8,
    paddingHorizontal: 30,
    backgroundColor: colors.PRIMARY,
  },
  actButtonTxt: {
    fontWeight: '600',
    color: colors.WHITE,
  },
  buttonChonBenhVien: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 10,
    paddingLeft: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
    minHeight: 40,
    paddingRight: 15,
    textAlignVertical: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  titleLabel: {
    fontWeight: '600',
    marginBottom: 5,
  },
});
