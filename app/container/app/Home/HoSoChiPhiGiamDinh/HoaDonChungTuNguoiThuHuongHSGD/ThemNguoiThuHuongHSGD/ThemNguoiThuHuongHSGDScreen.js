import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, TextInputOutlined} from '@component';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, SafeAreaView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ModalNganHang} from '../Components';
import {DV_PHAT_HANH, PHUONG_THUC_TT} from '../Contants';
import styles from './ThemNguoiThuHuongHSGDStyles';

const ThemNguoiThuHuongHSGDScreenComponent = (props) => {
  console.log('ThemNguoiThuHuongHSGDScreenComponent');
  const {route} = props;
  const {profileInfo, itemSelected} = route?.params;

  let soTaiKhoanRef = useRef();
  let tenDoiTuongThuHuongRef = useRef();
  let noiDungRef = useRef();
  let refInputSoTien = useRef();

  let refModalNganHang = useRef(null);
  // let refModalChonSoHoaDon = useRef(null);

  //dropdown đơn vị phát hành
  const [openDvThuHuong, setOpenDvThuHuong] = useState(false);
  // dropdown phương thức thanh toán
  const [openPhuongThucThanhToan, setOpenPhuongThucThanhToan] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);
  //Ngân hàng thụ hưởng
  const [dataNganHang, setDataNganHang] = useState([]);

  const getDefaultFormValue = () => {
    return {
      dvThuHuong: itemSelected?.dvi_th || '',
      tenDoiTuongThuHuong: itemSelected?.ten || '',
      phuongThucThanhToan: itemSelected?.pttt || '',
      nganHangThuHuong: itemSelected?.ma_ngan_hang || '',
      soTKThuHuong: itemSelected?.tk_cmt || '',
      soTien: itemSelected?.tien || '',
      noiDung: itemSelected?.dien_giai || '',
    };
  };

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    // setError,
    // getValues,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  useEffect(() => {
    getAllDanhMuc();
  }, []);

  const phuongThucThanhToan = watch('phuongThucThanhToan');

  const onInputFocus = () => {};

  const onPressLuu = async (data) => {
    setIsSubmiting(true);
    try {
      const params = {
        so_id: profileInfo?.so_id, //id hồ sơ
        bt: itemSelected ? itemSelected?.bt : '', // ID người thụ hưởng - nếu thêm mới thì truyền 0, còn sửa thì phải truyền tham số này
        dvi_th: data.dvThuHuong, // Đơn vị thụ hưởng
        pttt: data.phuongThucThanhToan, // 	Phương thức thanh toán
        tk_cmt: data.soTKThuHuong, // Tài khoản thụ hưởng
        ten: data.tenDoiTuongThuHuong, // Tên đối tượng thụ hưởng
        ma_ngan_hang: data.nganHangThuHuong, //	Mã ngân hàng
        dien_giai: data.noiDung, // Nội dung diễn giải
        ma_chi_nhanh: '',
        tien: data.soTien,
        dia_chi_nhan: '',
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_THONG_TIN_THU_HUONG_HSGD, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (itemSelected) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Sửa thông tin người thụ hưởng thành công', 'success');
      } else FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm người thụ hưởng thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getAllDanhMuc = async () => {
    const params = {};
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_TAT_CA_DANH_MUC, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      // const dataThuHuong = response.data_info.dvi_thu_huong;
      // setDataDvThuHuong(dataThuHuong);
      setDataNganHang(response.data_info.ngan_hang);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const getDisplayTextTenNganHang = (value) => {
    if (!value) return '';
    let displayText = '';
    dataNganHang.forEach((item) => {
      if (value === item.ma) {
        displayText = item.ten;
      }
    });
    return displayText;
  };

  const setValueNganHang = (val) => {
    setValue('nganHangThuHuong', val.ma, {shouldValidate: true});
  };

  const renderContent = () => {
    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={true}>
          <View style={styles.content}>
            <Controller
              control={control}
              name="dvThuHuong"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  title={'Đơn vị thụ hưởng'}
                  zIndex={6000}
                  items={DV_PHAT_HANH}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  isOpen={openDvThuHuong}
                  setOpen={setOpenDvThuHuong}
                  placeholder="Chọn đơn vị thụ hưởng"
                  inputErr={errors.dvThuHuong && getErrMessage('dvThuHuong', errors.dvThuHuong.type)}
                  isRequired={true}
                  searchable={false}
                  // onChangeValue={onChangeDvThuHuong}
                />
              )}
            />

            <Controller
              control={control}
              name="phuongThucThanhToan"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  isRequired
                  zIndex={5000}
                  searchable={false}
                  itemSelected={value}
                  items={PHUONG_THUC_TT}
                  title="Phương thức thanh toán"
                  isOpen={openPhuongThucThanhToan}
                  setOpen={setOpenPhuongThucThanhToan}
                  placeholder="Chọn phương thức thanh toán"
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  inputErr={errors.phuongThucThanhToan && getErrMessage('phuongThucThanhToan', errors.phuongThucThanhToan.type)}
                />
              )}
            />

            <Controller
              control={control}
              name="nganHangThuHuong"
              rules={{
                required: phuongThucThanhToan === 'TM' ? false : true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  isDropdown
                  editable={false}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  title="Ngân hàng thụ hưởng"
                  placeholder="Chọn ngân hàng"
                  inputStyle={{color: colors.BLACK_03}}
                  value={getDisplayTextTenNganHang(value)}
                  onPress={() => refModalNganHang.current.show()}
                  disabled={phuongThucThanhToan === 'TM' ? true : false}
                  isTouchableOpacity={phuongThucThanhToan === 'TM' ? false : true}
                  error={errors.nganHangThuHuong && getErrMessage('nganHangThuHuong', errors.nganHangThuHuong.type)}
                />
              )}
            />

            <Controller
              control={control}
              name="soTKThuHuong"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  isRequired={true}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  getRef={(ref) => (soTaiKhoanRef = ref)}
                  onSubmitEditing={() => tenDoiTuongThuHuongRef?.focus()}
                  title={phuongThucThanhToan === 'TM' ? 'CMT/CCCD' : 'Số tài khoản thụ hưởng'}
                  placeholder={phuongThucThanhToan === 'TM' ? 'Nhập số CMT/CCCD' : 'Nhập số t/k thụ hưởng'}
                  error={errors.soTKThuHuong && getErrMessage('soTKThuHuong', errors.soTKThuHuong.type)}
                />
              )}
            />

            <Controller
              control={control}
              name="tenDoiTuongThuHuong"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  title="Tên đối tượng thụ hưởng"
                  getRef={(ref) => (tenDoiTuongThuHuongRef = ref)}
                  placeholder="Nhập tên đối tượng thụ hưởng"
                  onSubmitEditing={() => refInputSoTien.focus()}
                  error={errors.tenDoiTuongThuHuong && getErrMessage('tenDoiTuongThuHuong', errors.tenDoiTuongThuHuong.type)}
                />
              )}
            />
            <Controller
              control={control}
              name="soTien"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  placeholder="0"
                  title="Số tiền"
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  keyboardType="numeric"
                  onChangeText={onChange}
                  getRef={(ref) => (refInputSoTien = ref)}
                  error={errors.soTien && getErrMessage('soTien', errors.soTien.type)}
                />
              )}
            />

            <View>
              <Controller
                control={control}
                name="noiDung"
                rules={{
                  required: true,
                }}
                render={({field: {onChange, value}}) => (
                  <TextInputOutlined
                    value={value}
                    title="Nội dung"
                    isRequired={true}
                    blurOnSubmit={false}
                    onFocus={onInputFocus}
                    onChangeText={onChange}
                    placeholder="Nhập nội dung"
                    getRef={(ref) => (noiDungRef = ref)}
                    error={errors.noiDung && getErrMessage('noiDung', errors.noiDung.type)}
                  />
                )}
              />
            </View>
          </View>
        </KeyboardAwareScrollView>
        <ModalNganHang data={dataNganHang} ref={refModalNganHang} setValue={(val) => setValueNganHang(val)} onBackPress={() => refModalNganHang.current.hide()} />
      </SafeAreaView>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={isSubmiting}
      headerTitle={'Thêm người thụ hưởng'}
      renderView={renderContent()}
      footer={<ButtonLinear loading={isSubmiting} disabled={isSubmiting} title="Lưu" onPress={handleSubmit(onPressLuu)} />}
    />
  );
};

export const ThemNguoiThuHuongHSGDScreen = memo(ThemNguoiThuHuongHSGDScreenComponent, isEqual);
