import {colors} from '@app/commons/Theme';
import {dimensions, spacing} from '@app/theme';
import {StyleSheet} from 'react-native';
export default StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    width: dimensions.width,
  },
  content: {
    flex: 1,
    marginHorizontal: 10,
  },
  dropDownTitle: {
    marginBottom: 5,
    fontWeight: 'bold',
  },
  dropDownView: {
    marginVertical: 10,
  },
  headerView: {
    borderBottomWidth: 1,
    borderBottomColor: colors.WHITE6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
  },
  txtHeader: {
    fontSize: 17,
    fontWeight: '900',
  },
  iconClose: {
    color: colors.BLACK,
    opacity: 0.5,
  },
  txtHeaderInput: {
    marginBottom: 8,
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: colors.GRAY,
    borderRadius: 5,
    paddingLeft: 15,
    color: colors.BLACK,
    paddingVertical: 10,
    backgroundColor: colors.WHITE,
  },
  inputView: {
    marginBottom: 10,
  },
  btn: {
    flex: 1,
    borderRadius: 30,
  },
  btnView: {
    flex: 1,
    marginTop: 20,
    marginBottom: 30,
    flexDirection: 'row',
  },
  txtBtn: {
    fontSize: 16,
    paddingVertical: 15,
    textAlign: 'center',
    color: colors.WHITE,
    paddingHorizontal: 15,
  },
  btnClose: {
    backgroundColor: colors.GRAY9,
    marginRight: 30,
  },
  btnSucess: {
    backgroundColor: colors.GRAY9,
  },
  doubleInputRow: {
    width: (dimensions.width - 30) / 2,
  },
  doubleInputRowView: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: {
    fontWeight: 'bold',
    marginBottom: spacing.tiny,
  },
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
    height: dimensions.height / 2,
  },
  footerView: {
    paddingVertical: 10,
    flexDirection: 'row',
    borderTopWidth: 0.2,
    width: dimensions.width,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: spacing.small,
  },
});
