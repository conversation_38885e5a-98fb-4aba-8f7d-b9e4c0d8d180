import {colors} from '@app/commons/Theme';
import Empty from '@app/components/Empty';
import HeaderModal from '@app/components/HeaderModal';
import {dimensions} from '@app/theme';
import {SearchBar, Text} from '@component';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, SafeAreaView, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import {useDebouncedCallback} from 'use-debounce';

const ModalNganHangComponent = forwardRef((props, ref) => {
  const [isVisible, setIsVisible] = useState(false);
  const {onBackPress, data, setValue} = props;

  const [searchInput, setSearchInput] = useState('');
  const [dataNganHang, setDataNganHang] = useState([]);

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const initModalData = () => {
    setDataNganHang(data);
  };

  const onSelectItem = (item) => {
    setValue && setValue(item);
    onBackPress && onBackPress();
  };

  const debounced = useDebouncedCallback((value) => {
    setSearchInput(value);
  }, 500);

  useEffect(() => {
    if (searchInput) {
      const lowerCaseSearchText = searchInput?.toLowerCase();
      const filter = data.filter((item) => item?.ten.toLowerCase()?.includes(lowerCaseSearchText));
      setDataNganHang(filter);
    } else if (searchInput === '') {
      setDataNganHang(data);
    }
  }, [searchInput]);

  /* RENDER */
  const renderItemHangMuc = ({item, index}) => (
    <TouchableOpacity key={index} style={styles.itemHangMucView} onPress={() => onSelectItem(item)}>
      <Text children={item.ten} />
    </TouchableOpacity>
  );

  const renderContent = () => {
    return (
      <FlatList
        data={dataNganHang}
        style={styles.flStyles}
        extraData={dataNganHang}
        onEndReachedThreshold={0.3}
        renderItem={renderItemHangMuc}
        keyExtractor={(item, index) => item + index.toString()}
        ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
      />
    );
  };
  return (
    <Modal animationIn={'fadeIn'} animationOut={'fadeOut'} onModalShow={initModalData} isVisible={isVisible} style={styles.modal}>
      <SafeAreaView style={{flex: 1}}>
        <HeaderModal title="Chọn ngân hàng thụ hưởng" onBackPress={onBackPress} />
        <SearchBar placeholder="Tìm kiếm ngân hàng" onTextChange={debounced} />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },

  searchInput: {
    flex: 1,
    borderWidth: 1,
    paddingLeft: 20,
    borderRadius: 5,
    borderColor: colors.GRAY,
  },
  headerView: {
    flexDirection: 'row',
  },
  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    paddingVertical: 10,
    marginHorizontal: 10,
  },
  title: {
    fontSize: 18,
    marginLeft: 10,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  searchBar: {
    height: 45,
    borderRadius: 8,
  },
});

export const ModalNganHang = memo(ModalNganHangComponent, isEqual);
