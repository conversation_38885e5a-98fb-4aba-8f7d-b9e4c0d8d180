import {FORMAT_DATE_TIME} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {ButtonLinear, DropdownPicker, ScreenComponent, TextInputOutlined} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, SafeAreaView, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {ModalChonTyLeThue} from '../Components';
import {DV_PHAT_HANH, TY_LE_THUE} from '../Contants';
import styles from './ThemHoaDonChungTuHSGDStyles';

const ThemHoaDonChungTuHSGDScreenComponent = (props) => {
  console.log('ThemHoaDonChungTuHSGDScreenComponent');
  const {route} = props;
  const {profileInfo, itemSelected} = route?.params;

  let tenDvPhatHanhRef = useRef(null);
  let maSoThueRef = useRef(null);
  let maSoThueDvNhanHoaDonRef = useRef(null);
  let diaChiRef = useRef(null);
  let mauHoaDonRef = useRef(null);
  let kyHieuHoaDonRef = useRef(null);
  let soHoaDonRef = useRef(null);
  let noiDungRef = useRef(null);
  let soTienRef = useRef(null);
  let tienThueRef = useRef(null);
  let tongCongRef = useRef(null);
  let tenDvNhanHoaDonRef = useRef(null);
  let dcDvNhanHoaDonRef = useRef(null);
  let webTraCuuHoaDonRef = useRef(null);
  let maTraCuuHoaDonRef = useRef(null);
  let refModalTyLeThue = useRef(null);

  const [toggleNgayPhatHanh, setToggleNgayPhatHanh] = useState(false);

  //dropdown đơn vị phát hành
  const [dataDvPhatHanh, setDataDvPhatHanh] = useState([]);
  const [openDvPhatHanh, setOpenDvPhatHanh] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);

  const getDefaultFormValue = () => {
    return {
      dvPhatHanh: itemSelected?.dvi_ph || '',
      tenDvPhatHanh: itemSelected?.ten_dvi_phat_hanh || '',
      maSoThue: itemSelected?.mst_dvi_phat_hanh || '',
      diaChi: itemSelected?.dchi_dvi_phat_hanh || '',
      ngayPhatHanh: itemSelected ? moment(itemSelected?.ngay_ct, FORMAT_DATE_TIME.DATE_FORMAT).toDate() : new Date(),
      mauHoaDon: itemSelected?.mau_hdon || '',
      kyHieuHoaDon: itemSelected?.ky_hieu_hdon || '',
      soHoaDon: itemSelected?.so_hdon || '',
      noiDung: itemSelected?.dien_giai || '',
      soTien: itemSelected?.tien || '',
      tyLeThue: itemSelected?.tl_thue || '',
      tienThue: itemSelected?.thue || '',
      tongCong: itemSelected?.tong_cong || '',
      tenDvNhanHoaDon: itemSelected?.ten_dvi_nhan || '',
      MSTDvNhanHoaDon: itemSelected?.mst_dvi_nhan || '',
      diaChiDvNhanHoaDon: itemSelected?.dchi_dvi_nhan || '',
      webTraCuuHoaDon: itemSelected?.website_tra_cuu || '',
      maTraCuuHoaDon: itemSelected?.ma_tra_cuu || '',
    };
  };

  useEffect(() => {
    getAllDanhMuc();
  }, []);

  const onInputFocus = () => {};

  const onPressLuu = async (data) => {
    setIsSubmiting(true);
    try {
      const params = {
        so_id: profileInfo?.so_id, //id hồ sơ
        bt: itemSelected ? itemSelected?.bt : '', //ID hóa đơn chứng từ - nếu thêm mới thì truyền 0, còn sửa thì phải truyền tham số này
        dvi_ph: data.dvPhatHanh, //Đơn vị phát hành
        ngay_ct: +moment(data.ngayPhatHanh).format(FORMAT_DATE_TIME.API_DATE_FORMAT), //				Ngày phát hành
        mau_hdon: data.mauHoaDon, //			Mẫu hóa đơn
        ky_hieu_hdon: data.kyHieuHoaDon, //		Ký hiệu hóa đơn
        so_hdon: data.soHoaDon, //	Số hóa đơn
        ten_dvi_phat_hanh: data.tenDvPhatHanh, //		Tên đơn vị phát hành
        dchi_dvi_phat_hanh: data.diaChi, //		Địa chỉ đơn vị phát hành
        mst_dvi_phat_hanh: data.maSoThue, //			Mã số thuế đơn vị phát hành
        dien_giai: data.noiDung, //		Diễn giải thông tin hóa đơn
        tien: data.soTien, //		Số tiền chưa VAT
        tl_thue: data.tyLeThue, //			Tỷ lệ thuế
        thue: data.tienThue, //			Số tiền thuế
        ten_dvi_nhan: data.tenDvNhanHoaDon, //				Tên đơn vị nhận hóa đơn
        mst_dvi_nhan: data.MSTDvNhanHoaDon, //			Mã số thuế đơn vị nhận hóa đơn
        dchi_dvi_nhan: data.diaChiDvNhanHoaDon, //				'Địa chỉ đơn vị nhận hóa đơn'
        // ma_tra_cuu: data.maTraCuuHoaDon, //		'Mã tra cứu hóa đơn'
        // website_tra_cuu: data.webTraCuuHoaDon, //			'Website tra cứu hóa đơn'
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_THONG_TIN_HOA_DON_CT_HSGD, params);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (itemSelected) {
        FlashMessageHelper.showFlashMessage('Thông báo', 'Sửa hoá đơn/chứng từ thành công', 'success');
      } else FlashMessageHelper.showFlashMessage('Thông báo', 'Thêm hoá đơn/chứng từ thành công', 'success');
      NavigationUtil.pop();
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getAllDanhMuc = async () => {
    const params = {};
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_TAT_CA_DANH_MUC, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const dataDVPH = response.data_info.phat_hanh;
      setDataDvPhatHanh(dataDVPH);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate, mode) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    // setError,
    // getValues,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  // const watchDVPhatHanh = watch('dvPhatHanh');
  const watchTyLeThue = watch('tyLeThue');
  const watchSoTien = watch('soTien');
  const watchTienThue = watch('tienThue');
  const ngayPhatHanh = watch('ngayPhatHanh');

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    return '';
  };

  const triggerTinhTienThue = () => {
    let soTien = +watchSoTien;
    if (watchTyLeThue > 0 && watchSoTien > 0) {
      setValue('tienThue', ((watchTyLeThue * soTien) / 100).toFixed());
    } else if (watchTyLeThue == 0) {
      setValue('tienThue', 0);
    }
  };

  useEffect(() => {
    let tienThue = +watchTienThue;
    let soTien = +watchSoTien;
    if (tienThue > 0) {
      setValue('tongCong', (soTien + tienThue).toFixed());
    } else setValue('tongCong', soTien.toFixed());
  }, [watchTienThue]);

  const onChangeValueTLT = (val) => {
    setValue('tyLeThue', val.value, {shouldValidate: true});
    let tlt = val?.value;
    let soTien = +watchSoTien;
    if (tlt > 0 && watchSoTien > 0) {
      setValue('tienThue', ((tlt * soTien) / 100).toFixed());
    } else {
      setValue('tienThue', 0);
    }
  };

  const getDisplayNameTyLeThue = (val) => {
    let displayText = '';
    TY_LE_THUE.map((e) => {
      if (e.value == val) {
        displayText = e.label;
      }
    });
    return displayText;
  };
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
    />
  );

  const renderContent = () => {
    return (
      <View style={styles.container}>
        <KeyboardAwareScrollView contentContainerStyle={styles.scrollView} showsVerticalScrollIndicator={true}>
          <View style={styles.content}>
            <Controller
              control={control}
              name="dvPhatHanh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <DropdownPicker
                  title="Đơn vị phát hành"
                  zIndex={6000}
                  items={DV_PHAT_HANH}
                  itemSelected={value}
                  setItemSelected={(dispatch) => onChange(dispatch())}
                  isOpen={openDvPhatHanh}
                  setOpen={setOpenDvPhatHanh}
                  placeholder="Chọn đơn vị phát hành"
                  inputErr={errors.dvPhatHanh && getErrMessage('dvPhatHanh', errors.dvPhatHanh.type)}
                  isRequired={true}
                  searchable={false}
                  // onChangeValue={onChangeDvPhatHanh}
                />
              )}
            />
            <Controller
              control={control}
              name="tenDvPhatHanh"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  title="Tên đơn vị phát hành"
                  placeholder="Nhập tên đv phát hành"
                  getRef={(ref) => (tenDvPhatHanhRef = ref)}
                  onSubmitEditing={() => maSoThueRef?.focus()}
                  error={errors.tenDvPhatHanh && getErrMessage('tenDvPhatHanh', errors.tenDvPhatHanh.type)}
                />
              )}
            />

            <Controller
              control={control}
              name="maSoThue"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  isRequired
                  value={value}
                  title="Mã số thuế"
                  blurOnSubmit={false}
                  returnKeyType="next"
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  placeholder="Nhập mã số thuế"
                  getRef={(ref) => (maSoThueRef = ref)}
                  onSubmitEditing={() => diaChiRef?.focus()}
                  error={errors.maSoThue && getErrMessage('maSoThue', errors.maSoThue.type)}
                />
              )}
            />

            <Controller
              control={control}
              name="diaChi"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  title="Địa chỉ"
                  isRequired={true}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  placeholder="Nhập địa chỉ"
                  getRef={(ref) => (diaChiRef = ref)}
                  onSubmitEditing={() => mauHoaDonRef?.focus()}
                  error={errors.diaChi && getErrMessage('diaChi', errors.diaChi.type)}
                />
              )}
            />

            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="ngayPhatHanh"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isDateTimeField
                      value={moment(value).format('DD/MM/YYYY')}
                      editable={false}
                      isRequired={true}
                      isTouchableOpacity
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      onFocus={onInputFocus}
                      title="Ngày phát hành"
                      inputStyle={{color: colors.BLACK_03}}
                      onPress={() => setToggleNgayPhatHanh(true)}
                      error={errors.ngayPhatHanh && getErrMessage('ngayPhatHanh', errors.ngayPhatHanh.type)}
                    />
                  )}
                />
                {renderDateTimeComp(
                  toggleNgayPhatHanh,
                  setToggleNgayPhatHanh,
                  (value) => {
                    setValue('ngayPhatHanh', value);
                  },
                  ngayPhatHanh,
                  'date',
                  null,
                  new Date(),
                  0,
                )}
              </View>

              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="mauHoaDon"
                  rules={{
                    required: false,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      value={value}
                      title="Mẫu hoá đơn"
                      blurOnSubmit={false}
                      onFocus={onInputFocus}
                      returnKeyType={'next'}
                      onChangeText={onChange}
                      placeholder="Nhập mẫu hoá đơn"
                      getRef={(ref) => (mauHoaDonRef = ref)}
                      onSubmitEditing={() => kyHieuHoaDonRef?.focus()}
                    />
                  )}
                />
              </View>
            </View>
            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="kyHieuHoaDon"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isRequired
                      value={value}
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      onFocus={onInputFocus}
                      title="Ký hiệu hoá đơn"
                      onChangeText={onChange}
                      placeholder="Nhập ký hiệu hoá đơn"
                      getRef={(ref) => (kyHieuHoaDonRef = ref)}
                      onSubmitEditing={() => soHoaDonRef?.focus()}
                      error={errors.kyHieuHoaDon && getErrMessage('kyHieuHoaDon', errors.kyHieuHoaDon.type)}
                    />
                  )}
                />
              </View>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="soHoaDon"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isRequired
                      value={value}
                      title="Số hoá đơn"
                      blurOnSubmit={false}
                      onFocus={onInputFocus}
                      returnKeyType={'next'}
                      onChangeText={onChange}
                      placeholder="Nhập số hoá đơn"
                      getRef={(ref) => (soHoaDonRef = ref)}
                      onSubmitEditing={() => noiDungRef?.focus()}
                      error={errors.soHoaDon && getErrMessage('soHoaDon', errors.soHoaDon.type)}
                    />
                  )}
                />
              </View>
            </View>
            <Controller
              control={control}
              name="noiDung"
              rules={{
                required: true,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  title="Nội dung"
                  isRequired={true}
                  blurOnSubmit={false}
                  returnKeyType={'next'}
                  onFocus={onInputFocus}
                  onChangeText={onChange}
                  placeholder="Nhập nội dung"
                  getRef={(ref) => (noiDungRef = ref)}
                  onSubmitEditing={() => soTienRef?.focus()}
                  error={errors.noiDung && getErrMessage('noiDung', errors.noiDung.type)}
                />
              )}
            />
            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="soTien"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isRequired
                      value={value}
                      placeholder="0"
                      title="Số tiền"
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      keyboardType="numeric"
                      onChangeText={onChange}
                      onBlur={triggerTinhTienThue}
                      inputStyle={{textAlign: 'right'}}
                      getRef={(ref) => (soTienRef = ref)}
                      onSubmitEditing={() => tienThueRef?.focus()}
                      error={errors.soTien && getErrMessage('soTien', errors.soTien.type)}
                    />
                  )}
                />
              </View>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="tyLeThue"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      editable={false}
                      isRequired={true}
                      title="Tỷ lệ thuế"
                      isTouchableOpacity
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      onFocus={onInputFocus}
                      onChangeText={onChange}
                      placeholder="Chọn tỷ lệ"
                      value={getDisplayNameTyLeThue(value)}
                      getRef={(ref) => (noiDungRef = ref)}
                      inputStyle={{color: colors.BLACK_03}}
                      onSubmitEditing={() => soTienRef?.focus()}
                      onPress={() => refModalTyLeThue.current.show()}
                      error={errors.tyLeThue && getErrMessage('tyLeThue', errors.tyLeThue.type)}
                    />
                  )}
                />
              </View>
            </View>

            <View style={styles.doubleInputRowView}>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="tienThue"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isRequired
                      title="Thuế"
                      value={value}
                      blurOnSubmit={false}
                      returnKeyType={'next'}
                      onFocus={onInputFocus}
                      keyboardType="numeric"
                      onChangeText={onChange}
                      placeholder="0"
                      inputStyle={{textAlign: 'right'}}
                      getRef={(ref) => (tienThueRef = ref)}
                      onSubmitEditing={() => tenDvNhanHoaDonRef?.focus()}
                      error={errors.tienThue && getErrMessage('tienThue', errors.tienThue.type)}
                    />
                  )}
                />
              </View>
              <View style={styles.doubleInputRow}>
                <Controller
                  control={control}
                  name="tongCong"
                  rules={{
                    required: true,
                  }}
                  render={({field: {onChange, value}}) => (
                    <TextInputOutlined
                      isRequired
                      value={value}
                      disabled={true}
                      placeholder="0"
                      editable={false}
                      title="Tổng cộng"
                      blurOnSubmit={false}
                      onFocus={onInputFocus}
                      returnKeyType={'next'}
                      keyboardType="numeric"
                      onChangeText={onChange}
                      inputStyle={{textAlign: 'right'}}
                      getRef={(ref) => (tongCongRef = ref)}
                      onSubmitEditing={() => tenDvNhanHoaDonRef?.focus()}
                      error={errors.tongCong && getErrMessage('tongCong', errors.tongCong.type)}
                    />
                  )}
                />
              </View>
            </View>
            <Controller
              control={control}
              name="tenDvNhanHoaDon"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  blurOnSubmit={false}
                  onFocus={onInputFocus}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  title="Tên đơn vị nhận hóa đơn"
                  getRef={(ref) => (tenDvNhanHoaDonRef = ref)}
                  placeholder="Tên đơn vị nhận hóa đơn"
                  onSubmitEditing={() => maSoThueDvNhanHoaDonRef?.focus()}
                />
              )}
            />
            <Controller
              control={control}
              name="MSTDvNhanHoaDon"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  blurOnSubmit={false}
                  onFocus={onInputFocus}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  title="MST đơn vị nhận hóa đơn"
                  getRef={(ref) => (maSoThueDvNhanHoaDonRef = ref)}
                  placeholder="Nhập MST đơn vị nhận hóa đơn"
                  onSubmitEditing={() => dcDvNhanHoaDonRef?.focus()}
                />
              )}
            />
            <Controller
              control={control}
              name="diaChiDvNhanHoaDon"
              rules={{
                required: false,
              }}
              render={({field: {onChange, value}}) => (
                <TextInputOutlined
                  value={value}
                  blurOnSubmit={false}
                  onFocus={onInputFocus}
                  returnKeyType={'next'}
                  onChangeText={onChange}
                  title="Địa chỉ đơn vị nhận hóa đơn"
                  getRef={(ref) => (dcDvNhanHoaDonRef = ref)}
                  placeholder="Địa chỉ đơn vị nhận hóa đơn"
                  onSubmitEditing={() => webTraCuuHoaDonRef?.focus()}
                />
              )}
            />
          </View>
        </KeyboardAwareScrollView>

        <ModalChonTyLeThue
          data={TY_LE_THUE}
          ref={refModalTyLeThue}
          tyLeThueSelected={watchTyLeThue}
          setValue={(value) => onChangeValueTLT(value)}
          onBackPress={() => refModalTyLeThue.current.hide()}
        />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle={!itemSelected ? 'Thêm hoá đơn/chứng từ' : 'Sửa hoá đơn/chứng từ'}
      renderView={renderContent()}
      footer={<ButtonLinear loading={isSubmiting} disabled={isSubmiting} title="Lưu" onPress={handleSubmit(onPressLuu)} />}
    />
  );
};

export const ThemHoaDonChungTuHSGDScreen = memo(ThemHoaDonChungTuHSGDScreenComponent, isEqual);
