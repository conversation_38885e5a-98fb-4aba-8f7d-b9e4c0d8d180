import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Empty, Icon, ScreenComponent, Text, CustomTabBar} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {NumericFormat} from 'react-number-format';
import styles from './DSNguoiThuHuongHoaDonChungTuStyles';

let timer;

const DanhSachNguoiThuHuongHSGDScreenComponent = (props) => {
  console.log('DanhSachNguoiThuHuongHSGDScreenComponent');
  const {navigation, route} = props;
  const {profileInfo} = route?.params;

  const [refreshing, setRefreshing] = useState(false);
  const [dataChungTu, setDataChungTu] = useState([]);
  const [dataThuHuong, setDataThuHuong] = useState([]);
  const [dataSoHoaDonChungTu, setDataSoHoaDonChungTu] = useState([]);

  const [tongTienCT, setTongTienCT] = useState(0);
  const [btnTabActive, setBtnTabActive] = useState(0);
  const [tongTienThuHuong, setTongTienThuHuong] = useState(0);

  let tabViewRef = useRef(null);

  useEffect(() => {
    navigation.addListener('focus', () => {
      // getData();
      getChiTietHSGiamDinh();
    });
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const getData = async () => {
    setRefreshing(true);
    let params = {
      so_id: profileInfo?.so_id || '',
    };
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_NGUOI_THU_HUONG_HOA_DON_CT, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrSoHoaDon = [];
      let tongTienCt = 0;
      let tongTienThuHuong = 0;
      let arrChungTu = response.data_info.chung_tu;
      let arrThuHuong = response.data_info?.thu_huong;

      arrChungTu.map((item) => {
        let sum = item.tong_cong;
        tongTienCt += sum;
      });
      arrThuHuong.map((item) => {
        let sum = item.tien;
        tongTienThuHuong += sum;
      });
      setTongTienThuHuong(tongTienThuHuong);
      setTongTienCT(tongTienCt);
      setDataChungTu(arrChungTu);
      setDataThuHuong(arrThuHuong);
      arrChungTu.map((item) => {
        arrSoHoaDon.push({label: item.mau_hdon + ' - ' + item.ten_dvi_phat_hanh, value: item.bt});
      });
      setDataSoHoaDonChungTu(arrSoHoaDon);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const getChiTietHSGiamDinh = async () => {
    try {
      let paramsProfileDetail = {
        ma_doi_tac: profileInfo?.ma_doi_tac,
        so_id: profileInfo?.so_id,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHI_TIET_HS_GIAM_DINH, paramsProfileDetail);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let arrSoHoaDon = [];
      let tongTienCt = 0;
      let tongTienThuHuong = 0;
      let arrChungTu = response.data_info.chung_tu;
      let arrThuHuong = response.data_info?.thu_huong;

      arrChungTu.map((item) => {
        let sum = item.tong_cong;
        tongTienCt += sum;
      });
      arrThuHuong.map((item) => {
        let sum = item.tien;
        tongTienThuHuong += sum;
      });
      setTongTienThuHuong(tongTienThuHuong);
      setTongTienCT(tongTienCt);
      setDataChungTu(arrChungTu);
      setDataThuHuong(arrThuHuong);
      arrChungTu.map((item) => {
        arrSoHoaDon.push({label: item.mau_hdon + ' - ' + item.ten_dvi_phat_hanh, value: item.bt});
      });
      setDataSoHoaDonChungTu(arrSoHoaDon);
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const handleXoaChungTu = (item) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn XOÁ hoá đơn/chứng từ này không?', [{text: 'Huỷ'}, {text: 'Đồng ý', onPress: () => xoaChungTu(item)}]);
  };
  const handleXoaThuHuong = (item) => {
    Alert.alert('Thông báo', 'Bạn có chắc chắn XOÁ hoá người thụ hưởng này không?', [{text: 'Huỷ'}, {text: 'Đồng ý', onPress: () => xoaNguoiThuHuong(item)}]);
  };

  const xoaChungTu = async (item) => {
    setRefreshing(true);
    try {
      let params = {
        so_id: profileInfo?.so_id || '',
        bt: item.bt,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_HOA_DON_CT_HSGD, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá hoá đơn/chứng từ thành công', 'success');
      getChiTietHSGiamDinh();
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const xoaNguoiThuHuong = async (item) => {
    setRefreshing(true);
    try {
      let params = {
        so_id: profileInfo?.so_id || '',
        bt: item.bt,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_THONG_TIN_NG_THU_HUONG_HSGD, params);
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá người thụ hưởng thành công', 'success');
      getChiTietHSGiamDinh();
    } catch (error) {
      setRefreshing(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onRefresh = () => {
    getChiTietHSGiamDinh();
  };

  const onEditChungTu = (it) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.THEM_HOA_DON_CHUNG_TU_HSGD, {profileInfo: profileInfo, itemSelected: it});
  };
  const onEditThuHuong = (it) => {
    NavigationUtil.push(SCREEN_ROUTER_APP.THEM_NGUOI_THU_HUONG_HSGD, {profileInfo: profileInfo, dataSoHoaDonCT: dataSoHoaDonChungTu, itemSelected: it});
  };

  // footer
  const renderFooter = () => {
    return (
      <View style={styles.footerView}>
        {btnTabActive == 0 && <ButtonLinear title="Thêm hoá đơn chứng từ" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_HOA_DON_CHUNG_TU_HSGD, {profileInfo: profileInfo})} />}
        {btnTabActive == 1 && (
          <ButtonLinear
            title="Thêm người thụ hưởng"
            onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.THEM_NGUOI_THU_HUONG_HSGD, {profileInfo: profileInfo, dataSoHoaDonCT: dataSoHoaDonChungTu})}
          />
        )}
      </View>
    );
  };

  const ListFooter = (price) => {
    return (
      <View flexDirection="row" justifyContent="space-between" flex={1} paddingHorizontal={20} marginBottom={60}>
        <Text style={styles.totalPrice}>Tổng cộng</Text>
        <NumericFormat value={price} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.totalPrice}>{value}</Text>} />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle="Hoá đơn/chứng từ/người thụ hưởng"
      renderView={
        <SafeAreaView style={styles.container}>
          <ScrollableTabView ref={tabViewRef} initialPage={0} onChangeTab={(tabActive) => setBtnTabActive(tabActive.ref.key.charAt(1))} renderTabBar={() => <CustomTabBar />}>
            <View tabLabel="Hoá đơn chứng từ" style={styles.component}>
              <FlatList
                data={dataChungTu}
                renderItem={(item) => <RenderItemHoaDonCT data={dataChungTu} items={item} onPressTrash={(it) => handleXoaChungTu(it)} onPressItem={(it) => onEditChungTu(it)} />}
                keyExtractor={(item, index) => index.toString()}
                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
                ListEmptyComponent={<Empty description="Chưa có dữ liệu!" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
                ListFooterComponent={dataChungTu.length > 0 && ListFooter(tongTienCT.toFixed())}
              />
            </View>
            <View tabLabel="DS người thụ hưởng" style={styles.component}>
              <FlatList
                data={dataThuHuong}
                renderItem={(item) => <RenderItemNguoiThuHuong data={dataThuHuong} items={item} onPressTrash={(it) => handleXoaThuHuong(it)} onPressItem={(it) => onEditThuHuong(it)} />}
                keyExtractor={(item, index) => index.toString()}
                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
                ListEmptyComponent={<Empty description="Chưa có dữ liệu!" imageStyle={{width: dimensions.width / 4, height: dimensions.width / 4}} />}
                ListFooterComponent={dataThuHuong.length > 0 && ListFooter(tongTienThuHuong.toFixed())}
              />
            </View>
          </ScrollableTabView>
        </SafeAreaView>
      }
      footer={renderFooter()}
    />
  );
};

const RenderItemNguoiThuHuong = (props) => {
  const {items, onPressTrash, onPressItem} = props;
  const item = items.item;

  const content = (label, value, style) => {
    return (
      <View flex={1} marginVertical={4}>
        {label === 'Số tài khoản/CCCD' ? (
          <View marginVertical={2} flex={1}>
            <Text style={[styles.txtLabel]}>{label}</Text>
            <Text style={[styles.detail, style]}>{value}</Text>
          </View>
        ) : (
          <Text style={[styles.txtLabel]}>
            {label}
            {typeof value == 'number' ? (
              <NumericFormat value={value?.toFixed()} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={[styles.detail, style]}>{val}</Text>} />
            ) : (
              <Text style={[styles.detail, style]}>{value}</Text>
            )}
          </Text>
        )}
      </View>
    );
  };

  const renderViewPrice = (label, value, style) => {
    return (
      <View marginVertical={4}>
        <Text style={styles.txtLabel}>{label}</Text>
        <NumericFormat value={value} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={[styles.detail, style]}>{val}</Text>} />
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={() => onPressItem(item)}>
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <View flexDirection="row">
            {content('Đối tượng thụ hưởng: ', item.ten, styles.txtStyles)}
            <TouchableOpacity style={{marginTop: spacing.tiny}} onPress={() => onPressTrash(item)}>
              <Icon.AntDesign name="closesquareo" color={colors.RED1} size={24} />
            </TouchableOpacity>
          </View>
          {content('Ngân hàng: ', item.ten_ngan_hang || 'Chưa có thông tin')}
          <View style={styles.rowStyles}>
            {content('Số tài khoản/CCCD', item.tk_cmt)}
            {renderViewPrice('Số tiền: ', item.tien, styles.txtPrice)}
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const RenderItemHoaDonCT = (props) => {
  const {items, onPressTrash, onPressItem} = props;
  const item = items.item;

  const content = (label, value, style) => {
    return (
      <View marginVertical={2} flex={1}>
        <Text style={[styles.txtLabel, {marginBottom: 4}]}>
          {label}
          <Text style={[styles.detail, style]}>{value}</Text>
        </Text>
      </View>
    );
  };

  const renderViewPrice = (label, value, style) => {
    return (
      <View marginVertical={2}>
        <Text style={styles.txtLabel}>{label}</Text>
        {/* <NumericFormat value={value?.toFixed()} displayType={'text'} thousandSeparator={true} renderText={(val) => <Text style={[styles.detail, style]}>{val}</Text>} /> */}
      </View>
    );
  };

  return (
    <TouchableOpacity onPress={() => onPressItem(item)}>
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <View flexDirection="row">
            {content('ĐV phát hành: ', item.ten_dvi_phat_hanh, styles.txtStyles)}
            <TouchableOpacity style={{marginTop: spacing.tiny}} onPress={() => onPressTrash(item)}>
              <Icon.AntDesign name="closesquareo" color={colors.RED1} size={24} />
            </TouchableOpacity>
          </View>
          <View flexDirection="row" justifyContent="space-between">
            {content('Ngày phát hành: ', item.ngay_ct)}
            {content('Số: ', item.so_hdon)}
          </View>
          <View style={styles.rowStyles}>
            {renderViewPrice('Số tiền', item.tien, styles.txtPrice)}
            {renderViewPrice('Tiền thuế', item.thue, styles.txtPrice)}
            {renderViewPrice('Tổng cộng', item.tong_cong, styles.txtPrice)}
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export const DanhSachNguoiThuHuongHSGDScreen = memo(DanhSachNguoiThuHuongHSGDScreenComponent, isEqual);
