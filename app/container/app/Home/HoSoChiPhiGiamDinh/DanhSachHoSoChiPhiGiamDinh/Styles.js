import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  content: {
    flex: 1,
  },
  footerView: {
    flexDirection: 'row',
    borderTopWidth: 0.2,
    borderColor: colors.GRAY,
    paddingVertical: vScale(10),
    backgroundColor: colors.WHITE,
  },
  footerBtn: {
    marginHorizontal: scale(spacing.small),
  },
  txtButton: {
    fontWeight: '700',
    color: colors.WHITE,
  },
  imageNoData: {
    width: dimensions.width / 3,
    height: dimensions.width / 3,
  },
  profileItemView: {
    borderRadius: 10,
    borderWidth: 0.4,
    flexDirection: 'row',
    paddingLeft: scale(10),
    borderColor: colors.GRAY,
    marginVertical: vScale(5),
    marginHorizontal: scale(10),
  },
  profileItemCenterView: {
    flex: 1,
    paddingRight: scale(5),
    paddingVertical: vScale(5),
    borderBottomColor: colors.GRAY4,
  },
  profileItemRightView: {
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: colors.GRAY4,
  },
  profileItemDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileTimeView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  profileTxtHoSo: {
    fontSize: 14,
    marginBottom: 4,
    color: colors.BLACK_03,
  },
  profileImgClock: {
    width: 14,
    height: 14,
    opacity: 0.8,
    marginRight: 5,
  },
  label: {
    fontWeight: '500',
    color: colors.GRAY6,
    marginBottom: vScale(5),
  },
  noDataView: {
    alignItems: 'center',
  },
  btnSearch: {
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: vScale(4),
    paddingHorizontal: scale(20),
    backgroundColor: colors.GRAY2,
  },
  txtTimKiem: {
    fontSize: 16,
    color: colors.WHITE,
    textTransform: 'uppercase',
    paddingVertical: vScale(10),
  },
  txtHeaderList: {
    fontWeight: '600',
    color: colors.BLACK_03,
  },
  date: {
    flex: 1,
    color: colors.PRIMARY,
  },
  txtSoHS: {
    fontWeight: '600',
    color: colors.PRIMARY,
  },
  renderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 0.2,
    borderColor: colors.GRAY,
    paddingHorizontal: scale(10),
    backgroundColor: colors.WHITE1,
    justifyContent: 'space-between',
    paddingVertical: vScale(spacing.small),
  },
  detail: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.BLACK_03,
  },
});
