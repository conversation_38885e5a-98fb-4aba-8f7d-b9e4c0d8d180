import {FORMAT_DATE_TIME} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {ButtonLinear, CommonOutlinedTextFieldWithIcon, ModalSelectSimple} from '@component';
import moment from 'moment';
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {SafeAreaView, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Mo<PERSON> from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

const startOfMonth = moment().startOf('month').format(FORMAT_DATE_TIME.API_DATE_FORMAT);
const currentDate = moment().format(FORMAT_DATE_TIME.API_DATE_FORMAT);

const ModalTimKiemHoSoGiamDinhComponent = forwardRef((props, ref) => {
  const {setValue, onBackPress, loading, userInfo} = props;
  let refModalTrangThaiHoSo = useRef(null);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const dataTrangThaiHS = [
    {label: 'Chưa chuyển', value: 'C'},
    {label: 'Đã chuyển', value: 'D'},
    {label: 'Đã huỷ', value: 'H'},
  ];

  const initFormInput = {
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    ma_doi_tac: userInfo?.nguoi_dung?.ma_doi_tac,
    ma_chi_nhanh: '',
    trang_thai: '', // D, C, H đã chuyển / chưa chuyển / huỷ
    nv: 'HSGD',
    so_hs: '',
    so_hs_bt: '',
    // dvi_gd: '',
    nd_tim: '',
  };
  const titleInput = ['Ngày đầu *', 'Ngày cuối *', 'Biển số xe', 'Giấy chứng nhận', 'Tên khách hàng', 'Điện thoại', 'Trạng thái hồ sơ', 'Số hồ sơ', 'Số hồ sơ bồi thường'];
  const [formInput, setFormInput] = useState(initFormInput);

  // modal value
  const [ngayDau, setNgayDau] = useState(moment(startOfMonth).toDate());
  const [ngayCuoi, setNgayCuoi] = useState(moment(currentDate).toDate());
  // toggle modal
  const [toggleNgayDau, setToggleNgayDau] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [toggleNgayCuoi, setToggleNgayCuoi] = useState(false);

  const onChangeText = async (field, value) => {
    setFormInput((prev) => ({...prev, [field]: value}));
  };

  useEffect(() => {
    if (ngayDau) {
      onChangeText('ngay_d', Number(moment(ngayDau).format(FORMAT_DATE_TIME.API_DATE_FORMAT)));
    }
    if (ngayCuoi) {
      onChangeText('ngay_c', Number(moment(ngayCuoi).format(FORMAT_DATE_TIME.API_DATE_FORMAT)));
    }
  }, [ngayDau, ngayCuoi]);

  const onSearchPress = () => {
    setValue && setValue(formInput);
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  const resetField = () => {
    setFormInput(initFormInput);
    setNgayDau(moment(startOfMonth).toDate());
    setNgayCuoi(moment(currentDate).toDate());
  };

  const getTenHienThi = (val, data) => {
    let name = '';
    data.map((e) => {
      if (val === e.value) name = e.label;
    });
    return name;
  };

  /**RENDER  */
  const renderDateTimeComp = (toggle, setToggle, setDateTime, date, mode, minDate, maxDate) => (
    <DateTimePickerModal
      date={date}
      mode={mode}
      locale={'vi_VN'}
      display="spinner"
      isVisible={toggle}
      confirmTextIOS="Chọn"
      maximumDate={maxDate}
      minimumDate={minDate}
      cancelTextIOS="Để sau"
      onCancel={() => setToggle(false)}
      onConfirm={(dateSelected) => onPressDateConfirm(dateSelected, setToggle, setDateTime)}
      themeVariant="light"
      isDarkModeEnabled={false}
      //   is24Hour={true}
    />
  );

  const renderFormInput = () => {
    return (
      <View>
        <View style={styles.formInput}>
          {/* Ngày đầu/Ngày cuối */}
          <View style={styles.doubleRowView}>
            <View style={{width: (dimensions.width - 30) / 2}}>
              <CommonOutlinedTextFieldWithIcon
                isDateTimeField
                editable={true}
                disabled={false}
                placeholder={titleInput[0]}
                styleContainer={styles.containerInput}
                onPressInput={() => setToggleNgayDau(true)}
                value={moment(ngayDau).format(FORMAT_DATE_TIME.DATE_FORMAT)}
              />
              {renderDateTimeComp(toggleNgayDau, setToggleNgayDau, setNgayDau, ngayDau, 'date', null, new Date(), 0)}
            </View>
            <View style={{width: (dimensions.width - 30) / 2}}>
              <CommonOutlinedTextFieldWithIcon
                editable={true}
                disabled={false}
                placeholder={titleInput[1]}
                styleContainer={styles.containerInput}
                value={moment(ngayCuoi).format(FORMAT_DATE_TIME.DATE_FORMAT)}
                isDateTimeField
                onPressInput={() => setToggleNgayCuoi(true)}
              />
              {renderDateTimeComp(toggleNgayCuoi, setToggleNgayCuoi, setNgayCuoi, ngayCuoi, 'date', null, new Date(), 0)}
            </View>
          </View>

          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.so_hs}
            placeholder={titleInput[7]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_hs', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            value={formInput.so_hs_bt}
            placeholder={titleInput[8]}
            styleContainer={styles.containerInput}
            onChangeText={(text) => onChangeText('so_hs_bt', text)}
          />
          <CommonOutlinedTextFieldWithIcon
            editable={true}
            disabled={false}
            isPickerModal={true}
            keyboardType="default"
            placeholder={titleInput[6]}
            styleContainer={styles.containerInput}
            value={getTenHienThi(formInput.trang_thai, dataTrangThaiHS)}
            onPressInput={() => refModalTrangThaiHoSo.current.show()}
          />
        </View>
      </View>
    );
  };

  /* RENDER */

  return (
    <Modal onBackButtonPress={onBackPress} animationIn="fadeInRight" animationOut="fadeOutRight" isVisible={isVisible} style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal title="Tìm kiếm hồ sơ" onBackPress={onBackPress} />
        <KeyboardAwareScrollView style={styles.content}>{renderFormInput()}</KeyboardAwareScrollView>
        <View style={styles.footer}>
          <ButtonLinear textStyle={{color: colors.BLACK_03}} linearColors={[colors.GRAY2, colors.GRAY2]} onPress={resetField} title="Nhập lại" />
          <ButtonLinear loading={loading} onPress={onSearchPress} linearStyle={{marginLeft: spacing.small}} title="Tìm kiếm" />
        </View>
      </SafeAreaView>
      {/* modal trạng thái hs */}
      <ModalSelectSimple
        title={'Chọn trạng thái hồ sơ'}
        ref={refModalTrangThaiHoSo}
        value={formInput.trang_thai}
        baseData={dataTrangThaiHS}
        setValue={(valueSelected) => onChangeText('trang_thai', valueSelected.value)}
        onBackPress={() => refModalTrangThaiHoSo.current.hide()}
      />
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  formInput: {
    marginHorizontal: scale(spacing.small),
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: scale(10),
  },
  containerInput: {
    height: 45,
  },
  doubleRowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footer: {
    borderTopWidth: 0.2,
    flexDirection: 'row',
    paddingVertical: vScale(10),
    borderTopColor: colors.GRAY2,
    paddingHorizontal: scale(spacing.small),
  },
  content: {
    flex: 1,
    marginTop: vScale(spacing.small),
  },
});

export const ModalTimKiemHoSoGiamDinh = React.memo(ModalTimKiemHoSoGiamDinhComponent, isEqual);
