import R from '@app/assets/R';
import {FORMAT_DATE_TIME, SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@app/components';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, RefreshControl, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {NumericFormat} from 'react-number-format';
import {useSelector} from 'react-redux';
import {ModalTimKiemHoSoGiamDinh} from './Compontents';
import styles from './Styles';
import ListProfilesHeader from '@app/components/ListProfilesHeader';

let timer;
const startOfMonth = moment().startOf('month').format(FORMAT_DATE_TIME.API_DATE_FORMAT);
const currentDate = moment().format(FORMAT_DATE_TIME.API_DATE_FORMAT);

const DanhSachHoSoChiPhiGiamDinhScreenComponent = (props) => {
  console.log('DanhSachHoSoChiPhiGiamDinhScreenComponent');
  const userInfo = useSelector(selectUser);
  const [dialogLoading, setDialogLoading] = useState(false);
  const user = userInfo?.nguoi_dung;

  let refModalTimKiemHoSo = useRef(null);

  useEffect(() => {
    timer = setTimeout(() => {
      getData(objParams);
    }, 300);
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const [objParams, setObjParams] = useState({
    ngay_d: +startOfMonth,
    ngay_c: +currentDate,
    ma_doi_tac: user?.ma_doi_tac,
    ma_chi_nhanh: '',
    trang_thai: '', // D, C, H đã chuyển / chưa chuyển / huỷ
    nv: 'HSGD',
    so_hs: '',
    so_hs_bt: '',
    // dvi_gd: '',
    nd_tim: '',
  });

  const [data, setData] = useState([]);
  const [total, setTotal] = useState(20);
  const [current, setCurrent] = useState(1);

  const getData = async (defaultObj, oldData = [], trang = 1, so_dong = 20) => {
    setDialogLoading(true);
    let subObj = {
      trang: trang,
      so_dong: so_dong,
    };
    try {
      let params = {...subObj, ...defaultObj};
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_HS_GIAM_DINH, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      if (response.data_info.length === 0) return;
      setTotal(response.data_info.tong_so_dong);
      let arrData = response.data_info.data;
      let mergeData = [...oldData, ...arrData];
      setData(mergeData);
      refModalTimKiemHoSo.current.hide();
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onRefresh = () => {
    setDialogLoading(true);
    setCurrent(1);
    getData(objParams);
  };

  const handleLoadMore = async () => {
    if (current * 20 <= total && !dialogLoading) {
      setCurrent(current + 1);
      getData(objParams, data, current + 1);
    }
  };

  const onPressSearch = (params) => {
    getData(params);
    setObjParams(params);
  };

  const renderProfileItem = ({item, index}) => {
    const statusColorText =
      item.trang_thai_ten === 'Đã duyệt giám định'
        ? colors.GREEN
        : item.trang_thai_ten === 'Hồ sơ đang xử lý'
        ? colors.ORANGE
        : item.trang_thai_ten === 'Đang trình giám định'
        ? colors.PRIMARY_08
        : item.trang_thai_ten === 'Đã đóng, chờ thanh toán'
        ? colors.RED1
        : null;
    return (
      <TouchableOpacity onPress={() => NavigationUtil.navigate(SCREEN_ROUTER_APP.CHI_TIET_HO_SO_GIAM_DINH, {profileDetail: item, prevScreen: SCREEN_ROUTER_APP.DS_HS_GIAM_DINH})}>
        <LinearGradient colors={[colors.WHITE1, colors.WHITE1]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
          <View style={styles.profileItemCenterView}>
            <Text style={[styles.label]}>
              Số HS: <Text style={styles.txtSoHS} children={item.so_hs} />
            </Text>
            <Text style={[styles.label]}>
              Số HSBT: <Text style={styles.txtSoHS} children={item.so_hs_bt} />
            </Text>
            <Text style={styles.label}>
              Nghiệp vụ: <Text style={styles.detail}>{item.nv_hthi}</Text>
            </Text>
            <Text style={styles.label}>
              Trạng thái: <Text style={[styles.detail, {color: statusColorText}]} children={item.trang_thai_ten} />
            </Text>
            <Text style={styles.label}>
              Số tiền: <NumericFormat value={item.tien_duyet || item.tien_dx} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text>{value}</Text>} />
            </Text>
          </View>
          <View style={styles.profileItemRightView}>
            <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    return (
      <View style={styles.container}>
        <ListProfilesHeader ngayDau={objParams.ngay_d} ngayCuoi={objParams.ngay_c} onPressSearch={() => refModalTimKiemHoSo.current.show()} />
        <FlatList
          data={data}
          renderItem={renderProfileItem}
          keyExtractor={(item, index) => index.toString()}
          onEndReachedThreshold={0.5}
          onEndReached={handleLoadMore}
          refreshControl={<RefreshControl refreshing={dialogLoading} onRefresh={onRefresh} />}
          ListEmptyComponent={
            <View style={styles.noDataView}>
              <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
              <Text>Chưa có dữ liệu</Text>
            </View>
          }
        />
        <ModalTimKiemHoSoGiamDinh
          userInfo={userInfo}
          loading={dialogLoading}
          ref={refModalTimKiemHoSo}
          initFormInput={objParams}
          setValue={(params) => onPressSearch(params)}
          onBackPress={() => refModalTimKiemHoSo.current.hide()}
        />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle="Hồ sơ chi phí giám định"
      renderView={renderContent()}
      footer={<ButtonLinear title="Mở hồ sơ chi phí giám định" onPress={() => NavigationUtil.push(SCREEN_ROUTER_APP.MO_HS_GIAM_DINH, {page: 0, info: {}})} />}
    />
  );
};

export const DanhSachHoSoChiPhiGiamDinhScreen = memo(DanhSachHoSoChiPhiGiamDinhScreenComponent, isEqual);
