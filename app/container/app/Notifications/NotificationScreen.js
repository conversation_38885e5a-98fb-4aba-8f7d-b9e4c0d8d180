import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {getNotification} from '@app/utils/DataProvider';
import {BottomTabs, Icon, ScreenComponent, Text} from '@component';
import {DATA_CONSTANT, SCREEN_ROUTER_APP} from '@constant';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, ScrollView, TouchableOpacity, View} from 'react-native';
import {connect} from 'react-redux';
import styles from './NotificationStyle';

const NotificationScreenComponent = (props) => {
  console.log('NotificationScreen');
  const [notifications, setNotifications] = useState(props.notification.notifications);
  const notificationUnread = props.notification.so_tb_chua_doc;
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    getAllNotification();
  }, []);

  useEffect(() => {
    setNotifications(props.notification.notifications);
  }, [props.notification.notifications]);

  const getAllNotification = () => {
    let params = {
      loai_thong_bao: DATA_CONSTANT.NOTIFICATION.LOAI_THONG_BAO,
      so_dong: 200,
    };
    getNotification(params, () => {
      setRefreshing(false);
    });
  };
  const onRefresh = () => {
    setRefreshing(true);
    getAllNotification();
  };

  const onPressNotificationItem = async (notificationData) => {
    const {NOTIFICATION} = DATA_CONSTANT;
    console.log('onPressNotificationItem', notificationData);
    let arrHoSoGiamDinhXe = [NOTIFICATION.CHI_TIET_HANH_DONG.XEM_CTIET_HO_SO_BT, NOTIFICATION.CHI_TIET_HANH_DONG.XEM_CTIET_HO_SO_GD, NOTIFICATION.CHI_TIET_HANH_DONG.XEM_CTIET_Y_KIEN_HO_SO];
    let arrHoSoGiamDinhXeMay = [NOTIFICATION.CHI_TIET_HANH_DONG.XEM_CTIET_HO_SO_GD_XE_MAY, NOTIFICATION.CHI_TIET_HANH_DONG.XEM_CTIET_HO_SO_BT_XE_MAY];
    let arrHoSoBaoLanh = [NOTIFICATION.CHI_TIET_HANH_DONG.NG_XEM_CTIET_HO_SO_BT];
    let arrHoSoPheDuyet = [NOTIFICATION.CHI_TIET_HANH_DONG.XEM_CTIET_HO_SO_PHE_DUYET];
    let arrHoSoPheDuyetXe = [
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_TRINH_DUYET_BOI_THUONG,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_TRINH_DUYET_TAM_UNG_BT,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_TRINH_DUYET_DUYET_GIA,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_TRINH_DUYET_GIAM_DINH,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_TRINH_DUYET_BAO_CAO_GD,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_TRINH_DUYET_TU_CHOI,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_TRINH_DUYET_BAO_LANH,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_TRINH_DUYET_HO_SO_GIAM_DINH,
    ];
    let arrHoSoPheDuyetXeMay = [
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_MAY_TRINH_DUYET_BOI_THUONG,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_MAY_TRINH_DUYET_TAM_UNG_BT,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_MAY_TRINH_DUYET_DUYET_GIA,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_MAY_TRINH_DUYET_GIAM_DINH,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_MAY_TRINH_DUYET_BAO_CAO_GD,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_MAY_TRINH_DUYET_TU_CHOI,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_XE_MAY_TRINH_DUYET_BAO_LANH,
    ];
    let arrHoSoPheDuyetConNguoi = [
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_NG_TRINH_DUYET_DUYET_GIA,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_NG_TRINH_DUYET_BAO_LANH,
      NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_NG_TRINH_DUYET_TU_CHOI,
    ];
    let arrThanhToan = [NOTIFICATION.ACTION_CODE.BT_TRINH_DUYET_TRINH_THANH_TOAN];

    if (arrHoSoGiamDinhXe.includes(notificationData.ctiet_hanh_dong)) console.log('VÀO HỒ SƠ GIÁM ĐỊNH XE');
    else if (arrHoSoBaoLanh.includes(notificationData.ctiet_hanh_dong)) console.log('VÀO HỒ SƠ BẢO LÃNH');
    else if (arrHoSoPheDuyet.includes(notificationData.ctiet_hanh_dong)) {
      if (arrHoSoPheDuyetXe.includes(notificationData.ctiet_action_code)) console.log('VÀO HỒ SƠ PHÊ PHÊ DUYỆT XE');
      else if (arrHoSoPheDuyetConNguoi.includes(notificationData.ctiet_action_code)) console.log('VÀO HỒ SƠ PHÊ PHÊ DUYỆT CON NGƯỜI');
      else if (arrThanhToan.includes(notificationData.ctiet_action_code)) console.log('VÀO HỒ SƠ TRÌNH THANH TOÁN');
    }

    if (!notificationData.doc_noi_dung) {
      let params = {
        gid: notificationData.gid,
      };
      try {
        let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.NOTIFICATION_UPDATE_READ, params);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        getAllNotification();
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    }

    // let actionCode = notificationData.ctiet_action_code.split(':');
    // actionCode = actionCode[0] + ':' + actionCode[1];
    //nếu là hồ sơ giám định -> vào chi tiết hồ sơ giám định
    if (arrHoSoGiamDinhXe.includes(notificationData.ctiet_hanh_dong)) {
      console.log('VÀO HỒ SƠ GIÁM ĐỊNH XE');

      let profileDetail = {
        ho_so: {
          ma_doi_tac: notificationData.ctiet_ma_doi_tac,
          so_id: notificationData.ctiet_so_id,
        },
      };
      let params = {profileDetail: profileDetail};
      if (notificationData.ctiet_hanh_dong === NOTIFICATION.CHI_TIET_HANH_DONG.XEM_CTIET_Y_KIEN_HO_SO) params.subScreen = SCREEN_ROUTER_APP.Y_KIEN_TRAO_DOI;
      NavigationUtil.push(SCREEN_ROUTER_APP.PROFILE_ASSESSMENT, params);
    } else if (arrHoSoBaoLanh.includes(notificationData.ctiet_hanh_dong)) {
      console.log('VÀO HỒ SƠ BẢO LÃNH');
      let profileDetail = {
        ho_so: {
          ma_doi_tac: notificationData.ctiet_ma_doi_tac,
          so_id: notificationData.ctiet_so_id,
        },
      };
      NavigationUtil.push(SCREEN_ROUTER_APP.BLVP_THEM_QUYEN_LOI_BAO_LANH, {
        profileDetail: profileDetail,
      });
    }
    //nếu là hồ sơ phê duyệt -> vào chi tiết hồ sơ phê
    else if (arrHoSoPheDuyet.includes(notificationData.ctiet_hanh_dong)) {
      let profileDetail = {
        thong_tin_chung: {
          ma_doi_tac: notificationData.ctiet_ma_doi_tac,
          bt: notificationData.ctiet_so_id,
        },
      };
      //vào hồ sơ PHÊ DUYỆT XE
      if (arrHoSoPheDuyetXe.includes(notificationData.ctiet_action_code) || arrHoSoPheDuyetXeMay.includes(notificationData.ctiet_action_code)) {
        console.log('vào hồ sơ PHÊ DUYỆT XE');
        NavigationUtil.push(SCREEN_ROUTER_APP.APPROVAL_PROFILE, {
          profileDetail: profileDetail,
        });
      }
      //vào hồ sơ PHÊ DUYỆT CON NGƯỜI
      else if (arrHoSoPheDuyetConNguoi.includes(notificationData.ctiet_action_code)) {
        console.log('vào hồ sơ PHÊ DUYỆT CON NGƯỜI');
        NavigationUtil.push(SCREEN_ROUTER_APP.APPROVAL_NG_PROFILE, {
          profileDetail: profileDetail,
        });
      }
      //vào MÀN HÌNH TRÌNH THANH TOÁN
      else if (arrThanhToan.includes(notificationData.ctiet_action_code)) {
        console.log(' //vào hồ sơ THANH TOÁN');
        NavigationUtil.push(SCREEN_ROUTER_APP.TRINH_THANH_TOAN, {
          profileDetail: profileDetail,
        });
      }
    } else if (arrHoSoGiamDinhXeMay.includes(notificationData.ctiet_hanh_dong)) {
      console.log('VÀO HỒ SƠ GIÁM ĐỊNH XE MÁY');
      let profileDetail = {
        ho_so: {
          ma_doi_tac: notificationData.ctiet_ma_doi_tac,
          so_id: notificationData.ctiet_so_id,
        },
      };
      NavigationUtil.push(SCREEN_ROUTER_APP.GIAM_DINH_XE_MAY, {
        profileDetail: profileDetail,
      });
    }
  };

  /* RENDER */
  const renderNotificationItem = ({item}) => {
    return (
      <TouchableOpacity style={styles.itemView} onPress={() => onPressNotificationItem(item)}>
        <View style={styles.itemLeftView}>
          <View style={styles.itemLeftIconView}>
            <Icon.FontAwesome name="bell" size={20} color={colors.WHITE} />
          </View>
        </View>
        <View style={styles.itemCenterView}>
          <Text style={[styles.txtName, {fontWeight: item.doc_noi_dung === 0 ? 'bold' : '400'}]}>{item.tieu_de}</Text>
          <View style={styles.itemDetail}>
            <Text style={[styles.txtDate]}>{item.nd_tom_tat}</Text>
          </View>
          <Text style={styles.txtLicensePlates}>{item.tg_thong_bao_hthi}</Text>
        </View>
        <View style={styles.itemRightView}>{item.doc_noi_dung === 0 && <Icon.Entypo name="dot-single" color={'royalblue'} size={50} />}</View>
      </TouchableOpacity>
    );
  };
  return (
    <ScreenComponent
      header
      screenTitle={'Thông báo (' + notificationUnread + ')'}
      renderView={
        <View style={styles.container}>
          <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} showsVerticalScrollIndicator={false}>
            <View style={styles.contentView}>
              <FlatList data={notifications} renderItem={renderNotificationItem} keyExtractor={(item) => item.id} />
            </View>
          </ScrollView>
          <BottomTabs />
        </View>
      }
    />
  );
};
const mapStateToProps = (state) => ({
  notification: state.notification.data,
});

const mapDispatchToProps = {};

const NotificationConnect = connect(mapStateToProps, mapDispatchToProps)(NotificationScreenComponent);
export const NotificationScreen = memo(NotificationConnect, isEqual);
