import {Dimensions, StyleSheet} from 'react-native';
import {colors} from '../../../commons/Theme';

const {width, height} = Dimensions.get('screen');
export default StyleSheet.create({
  container: {flex: 1},
  contentView: {
    marginBottom: 50,
  },
  itemView: {
    flexDirection: 'row',
    paddingVertical: 13,
    paddingHorizontal: 20,
  },
  itemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY,
    borderBottomWidth: 1,
    paddingBottom: 10,
    paddingRight: 5,
    justifyContent: 'center',
    // flexDirection: 'column',
  },
  itemLeftView: {
    justifyContent: 'center',
    borderBottomColor: colors.GRAY,
    borderBottomWidth: 1,
    paddingVertical: 10,
  },
  itemRightView: {
    borderBottomColor: colors.GRAY,
    borderBottomWidth: 1,
    paddingBottom: 10,
    justifyContent: 'center',
  },
  itemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtDate: {
    fontSize: 13,
    color: colors.GRAY5,
  },
  txtLicensePlates: {
    fontSize: 14,
    color: colors.GRAY5,
  },
  txtName: {
    marginBottom: 4,
  },
  itemLeftIconView: {
    width: 40,
    height: 40,
    borderRadius: 4,
    marginRight: 16,
    backgroundColor: colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imgThumbnailItem: {
    width: 40,
    height: 40,
    borderRadius: 5,
  },
  imgThumbnailItemBordered: {
    borderWidth: 2,
    borderColor: colors.NEON,
  },
});
