import {ScreenComponent, Text} from '@component';
import messaging from '@react-native-firebase/messaging';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {FlatList, PermissionsAndroid, Platform, ScrollView, View} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {check, checkNotifications, PERMISSIONS} from 'react-native-permissions';
import {connect} from 'react-redux';
import styles from './DiagnosticStyles';

const DiagnosticScreenComponent = (props) => {
  // console.log('DiagnosticScreen render', props);
  const {debug} = props;
  const [fcmToken, setFcmToken] = useState('');
  const [notificationStatus, setNotificationStatus] = useState('');
  const [recordAudioStatus, setRecordAudioStatus] = useState('');
  const [cameraStatus, setCameraStatus] = useState('');
  const [androidId, setAndroidId] = useState('');
  const [brand, setBrand] = useState('');
  const [carrier, setCarrier] = useState('');
  const [device, setDevice] = useState('');
  const [deviceId, setDeviceId] = useState('');
  const [systemVersion, setSystemVersion] = useState('');
  const [uniqueId, setUniqueId] = useState('');
  const [syncUniqueId, setSyncUniqueId] = useState('');

  useEffect(() => {
    getConfigData();
  }, []);
  const getConfigData = async () => {
    let fcmToken = await messaging().getToken();
    setFcmToken(fcmToken);

    let response = await checkNotifications();
    setNotificationStatus(JSON.stringify(response));

    response = await DeviceInfo.getBrand();
    setBrand(JSON.stringify(response));

    response = await DeviceInfo.getCarrier();
    setCarrier(JSON.stringify(response));

    response = await DeviceInfo.getDevice();
    setDevice(JSON.stringify(response));

    response = await DeviceInfo.getDeviceId();
    setDeviceId(JSON.stringify(response));

    response = await DeviceInfo.getSystemVersion();
    setSystemVersion(JSON.stringify(response));

    response = await DeviceInfo.getUniqueId();
    setUniqueId(JSON.stringify(response));

    response = await DeviceInfo.syncUniqueId();
    setSyncUniqueId(JSON.stringify(response));

    if (Platform.OS == 'android') {
      response = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO);
      setRecordAudioStatus(JSON.stringify(response));

      response = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA);
      setCameraStatus(JSON.stringify(response));

      response = await DeviceInfo.getAndroidId();
      setAndroidId(JSON.stringify(response));
    } else {
      response = await check(PERMISSIONS.IOS.MICROPHONE);
      setRecordAudioStatus(JSON.stringify(response));

      response = await check(PERMISSIONS.IOS.CAMERA);
      setCameraStatus(JSON.stringify(response));
    }
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle={'Thông tin ứng dụng'}
      renderView={
        <ScrollView style={styles.container}>
          <View style={{flexDirection: 'row'}}>
            <Text children="FCM Token : " />
            <Text selectable children={fcmToken} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="Notification Status : " />
            <Text selectable children={notificationStatus} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="Record audio Status : " />
            <Text selectable children={recordAudioStatus} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="Camera Status : " />
            <Text selectable children={cameraStatus} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="Android Id : " />
            <Text selectable children={androidId} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="Brand : " />
            <Text selectable children={brand} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="Carrier : " />
            <Text selectable children={carrier} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="Device : " />
            <Text selectable children={device} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="DeviceId : " />
            <Text selectable children={deviceId} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="SystemVersion : " />
            <Text selectable children={systemVersion} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="UniqueId : " />
            <Text selectable children={uniqueId} style={{flexShrink: 1}} />
          </View>
          <View style={{flexDirection: 'row'}}>
            <Text children="SyncUniqueId : " />
            <Text selectable children={syncUniqueId} style={{flexShrink: 1}} />
          </View>
          <FlatList
            data={debug}
            renderItem={(data) => {
              // console.log('data', data);
              return <Text children={data.item} />;
            }}
          />
        </ScrollView>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  debug: state.debug.data,
});

const mapDispatchToProps = {};

const DiagnosticScreenConnect = connect(mapStateToProps, mapDispatchToProps)(DiagnosticScreenComponent);
export const DiagnosticScreen = memo(DiagnosticScreenConnect, isEqual);
