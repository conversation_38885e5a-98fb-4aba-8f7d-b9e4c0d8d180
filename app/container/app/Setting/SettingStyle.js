import {spacing} from '@app/theme';
import {StyleSheet} from 'react-native';
import {colors} from '../../../commons/Theme';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  settingItemView: {
    flexDirection: 'row',
    paddingVertical: spacing.small,
    marginHorizontal: spacing.small,
  },
  avatar: {
    width: 120,
    height: 120,
    // marginTop: -50,
    borderRadius: 20,
  },
  contentView: {
    paddingHorizontal: spacing.small,
  },
  profileView: {
    flexDirection: 'row',
    // justifyContent: 'center',
    alignItems: 'center',
    // justifyContent : 'space-between'
  },
  profileDetail: {
    paddingLeft: spacing.small,
    flexShrink: 1,
  },
  name: {
    fontSize: 18,
    flexWrap: 'wrap',
  },
  address: {
    color: colors.GRAY6,
    opacity: 0.6,
  },
  email: {
    color: colors.GRAY6,
    opacity: 0.6,
  },
  phone: {
    color: colors.GRAY6,
    opacity: 0.6,
  },
  settingView: {
    marginTop: 20,
    flex: 1,
  },
  headerSetting: {
    paddingVertical: spacing.small,
    paddingLeft: spacing.small,
    marginBottom: spacing.smaller,
    backgroundColor: colors.WHITE7,
    justifyContent: 'center',
  },
  leftView: {
    width: 36,
    height: 36,
    backgroundColor: colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 3,
    marginRight: spacing.small,
  },
  centerView: {
    flex: 1,
    borderBottomWidth: 1,
    // flexDirection: 'row',
    borderBottomColor: colors.GRAY,
    justifyContent: 'center',
    // alignItems: 'center',
  },
  rightView: {
    borderBottomWidth: 1,
    borderBottomColor: colors.GRAY,
  },
  iconArrow: {
    opacity: 0.2,
  },
  switchView: {
    marginVertical: spacing.tiny,
    // flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: spacing.mediumPlush,
  },
  switchTitle: {
    fontSize: 16,
  },
  switch: {alignItems: 'center', justifyContent: 'center'},
  txtVersion: {
    textAlign: 'center',
    marginTop: spacing.mediumPlush,
  },
  khuVucCanhBao: {
    paddingVertical: spacing.small,
    paddingLeft: spacing.small,
    marginBottom: spacing.smaller,
    backgroundColor: colors.RED1,
    justifyContent: 'center',
    color: '#FFF',
  },
  txtChuaCapNhat: {
    color: colors.RED_NOTIFICATION,
    marginLeft: spacing.tiny,
  },
});
