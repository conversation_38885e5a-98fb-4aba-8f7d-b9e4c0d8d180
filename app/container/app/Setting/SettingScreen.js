import R from '@R';
import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {updateAmThanhKhiChup, updateDangNhapKhuonMat, updateDangNhapVanTay, updateGuiViTri, updateLuuAnh<PERSON><PERSON>DinhKhiChup, updateRung} from '@app/redux/slices/AppSettingSlice';
import {updateSwitchNavigator} from '@app/redux/slices/SwitchNavigatorSlice';
import {updateUserInfo} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import keychain from '@app/utils/KeychainProvider';
import {requestFaceIdPermission} from '@app/utils/PermisstionProvider';
import {Icon, ScreenComponent, Text} from '@component';
import {APP_VERSION_STORE_ANDROID, APP_VERSION_STORE_IOS, ASYNC_STORAGE_KEY, SCREEN_ROUTER, SCREEN_ROUTER_APP, URL_APP_ON_STORE, isIOS} from '@constant';
import notifee from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Image, Linking, ScrollView, TouchableOpacity, View} from 'react-native';
import ActionSheet from 'react-native-actionsheet';
import CodePush from 'react-native-code-push';
import DeviceInfo from 'react-native-device-info';
import {BIOMETRY_TYPE} from 'react-native-keychain';
import {openSettings} from 'react-native-permissions';
import prompt from 'react-native-prompt-android';
import {Switch} from 'react-native-switch';
import {connect} from 'react-redux';
import {settingTitle} from './Constants';
import styles from './SettingStyle';

const SettingScreenComponent = (props) => {
  console.log('SettingScreen');
  const {userInfo, appSetting, updateRung, updateGuiViTri, updateAmThanhKhiChup, updateDangNhapVanTay, updateDangNhapKhuonMat, updateLuuAnhGiamDinhKhiChup} = props;

  let actionSheetSetting = useRef();
  const [supportBiometrics, setSupportBiometrics] = useState(false);
  const [codepushInfo, setCodepushInfo] = useState(null);
  const [showResetDataCodepush, setShowResetDataCodepush] = useState(0);

  useEffect(() => {
    checkXacThucSinhHoc(); //check xem thiết bị có cho phép sử dụng xác thực sinh học không
    // getCodepushInfo();
    checkQuyenLuuAnhGiamDinhKhiChup();
  }, []);

  useEffect(() => {
    if (showResetDataCodepush === 5) {
      Alert.alert('Thông báo', 'Bạn có muốn xoá dữ liệu cập nhật ứng dụng', [
        {
          text: 'Đồng ý',
          onPress: () => {
            CodePush.clearUpdates();
            CodePush.restartApp();
          },
        },
        {
          text: 'TỪ CHỐI',
          style: 'destructive',
        },
      ]);
      setShowResetDataCodepush(0);
    }
  }, [showResetDataCodepush]);

  const onPressSettingItem = useCallback(async (title) => {
    if (title === settingTitle[0]) NavigationUtil.push(SCREEN_ROUTER_APP.ASSESSMENT_LOCATION);
    else if (title === settingTitle[1]) openSettings();
    else if (title === settingTitle[5]) onPressXoaTaiKhoan();
    else if (title === settingTitle[8]) openAppOnStore();
    else if (title === settingTitle[9]) NavigationUtil.push(SCREEN_ROUTER_APP.DOI_MAT_KHAU);
  }, []);

  const checkQuyenLuuAnhGiamDinhKhiChup = () => {
    if (appSetting.luuAnhGiamDinhKhiChup === undefined) updateLuuAnhGiamDinhKhiChup(true);
  };

  const openAppOnStore = () => {
    Linking.openURL(isIOS ? URL_APP_ON_STORE.IOS : URL_APP_ON_STORE.ANDROID);
  };
  const getCodepushInfo = async () => {
    try {
      let response = await CodePush.getUpdateMetadata(CodePush.UpdateState.RUNNING);
      setCodepushInfo(response);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  const checkXacThucSinhHoc = async () => {
    try {
      let loaiSinhHocSupport = await keychain.getSupportedBiometryType();
      if (isIOS && loaiSinhHocSupport == null) loaiSinhHocSupport = BIOMETRY_TYPE.FACE_ID;
      setSupportBiometrics(loaiSinhHocSupport);
    } catch (error) {
      setSupportBiometrics(false);
    }
  };
  const onPressXoaTaiKhoan = () => {
    prompt(
      'Cảnh báo',
      'Sau khi xoá tài khoản, tất cả dữ liệu của tài khoản trong hệ thống sẽ bị xoá cùng tài khoản. Vui lòng nhập email đăng nhập để xác nhận',
      [
        {text: 'Huỷ', onPress: () => {}, style: 'cancel'},
        {
          text: 'Xác nhận',
          onPress: async (email) => {
            let params = {
              email: email,
              mat_khau: userInfo.nguoi_dung.pas,
            };
            let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XOA_TAI_KHOAN, params);
            if (!response || !response.state_info || response.state_info.status !== 'OK') return;
            FlashMessageHelper.showFlashMessage('Thông báo', 'Xoá tài khoản thành công', 'success');
            resetValue();
          },
        },
      ],
      {
        defaultValue: '',
        placeholder: 'Nội dung duyệt',
      },
    );
  };
  const resetValue = () => {
    AsyncStorage.setItem(ASYNC_STORAGE_KEY.USER_INFO, '');
    AsyncStorage.setItem(ASYNC_STORAGE_KEY.PUSH_TOKEN_REGISTERED, '');
    props.updateUserInfo('');
    props.updateSwitchNavigator(SCREEN_ROUTER.AUTH);
  };

  const onPressActionSheet = useCallback((index) => {
    if (index === 2) return;
    if (index === 0)
      notifee
        .openNotificationSettings()
        .then((response) => {})
        .catch((err) => {});
    else if (index === 1) NavigationUtil.push(SCREEN_ROUTER_APP.DIAGNOSTIC);
  }, []);

  const onChangeAppSettings = useCallback(
    async (value, type) => {
      let newAppSetting = {...appSetting};
      if (type === 0) {
        updateRung(value);
        newAppSetting.rung = value;
      } else if (type === 1) {
        updateGuiViTri(value);
        newAppSetting.guiViTri = value;
      } else if (type === 2) {
        updateAmThanhKhiChup(value);
        newAppSetting.amThanhKhiChup = value;
      } else if (type === 3) {
        updateDangNhapVanTay(value);
        newAppSetting.dangNhapVanTay = value;
      } else if (type === 4) {
        updateDangNhapKhuonMat(value);
        newAppSetting.dangNhapKhuonMat = value;
        await AsyncStorage.setItem(ASYNC_STORAGE_KEY.APP_SETTINGS, JSON.stringify(newAppSetting));
        await requestFaceIdPermission();
      } else if (type === 5) {
        if (!value)
          Alert.alert('Thông báo', 'Bạn đang tắt tính năng "Lưu ảnh giám định", ảnh sẽ không được lưu vào điện thoại trong quá trình giám định', [
            {
              text: 'Đồng ý',
              onPress: () => {
                updateLuuAnhGiamDinhKhiChup(value);
                newAppSetting.luuAnhGiamDinhKhiChup = value;
              },
            },
            {text: 'Để sau', style: 'destructive'},
          ]);
        else {
          updateLuuAnhGiamDinhKhiChup(value);
          newAppSetting.luuAnhGiamDinhKhiChup = value;
        }
      }
      await AsyncStorage.setItem(ASYNC_STORAGE_KEY.APP_SETTINGS, JSON.stringify(newAppSetting));
    },
    [appSetting],
  );

  const checkUpdateStore = () => {
    let version = isIOS ? APP_VERSION_STORE_IOS.VERSION : APP_VERSION_STORE_ANDROID.VERSION;
    if (DeviceInfo.getVersion() !== version) return true;
    return false;
  };

  /* RENDER */
  const renderSettingItem = useCallback((title) => {
    return (
      <TouchableOpacity style={styles.settingItemView} activeOpacity={0.5} onPress={() => onPressSettingItem(title)}>
        <View style={[styles.leftView, title === settingTitle[5] && {backgroundColor: colors.RED1}]}>
          {title === settingTitle[0] && <Icon.Entypo name="location" size={25} color={colors.WHITE} />}
          {title === settingTitle[1] && <Icon.MaterialIcons name="phonelink-setup" size={25} color={colors.WHITE} />}
          {title === settingTitle[5] && <Icon.MaterialCommunityIcons name="delete-alert" size={25} color={colors.WHITE} />}
          {title === settingTitle[8] && <Icon.Ionicons name={!isIOS ? 'logo-google-playstore' : 'logo-apple-appstore'} size={25} color={colors.WHITE} />}
          {title === settingTitle[9] && <Icon.FontAwesome name={'lock'} size={25} color={colors.WHITE} />}
        </View>
        <View style={styles.centerView}>
          <Text style={[title === settingTitle[5] && {color: colors.RED1}]}>{title}</Text>
          {title === settingTitle[8] && checkUpdateStore() && (
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Icon.FontAwesome name="warning" size={15} color={colors.RED_NOTIFICATION} />
              <Text children="Chưa cập nhật" style={styles.txtChuaCapNhat} />
            </View>
          )}
        </View>
        <View style={styles.rightView}>
          <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={styles.iconArrow} />
        </View>
      </TouchableOpacity>
    );
  }, []);

  const renderSwitchImageViewButton = useCallback(
    (title, value, onValueChange, activeText, inActiveText, switchLeftPx, switchRightPx, switchWidthMultiplier) => (
      <View style={styles.settingItemView}>
        <View style={styles.leftView}>
          {title === settingTitle[2] && <Icon.MaterialCommunityIcons name={value ? 'vibrate' : 'vibrate-off'} size={25} color={colors.WHITE} />}
          {title === settingTitle[3] && <Icon.MaterialIcons name="my-location" size={25} color={colors.WHITE} />}
          {title === settingTitle[4] && <Icon.AntDesign name="sound" size={25} color={colors.WHITE} />}
          {title === settingTitle[6] && <Icon.MaterialCommunityIcons name={value ? 'fingerprint' : 'fingerprint-off'} size={25} color={colors.WHITE} />}
          {title === settingTitle[7] && <Icon.MaterialCommunityIcons name="face-recognition" size={25} color={colors.WHITE} />}
          {title === settingTitle[10] && <Icon.MaterialCommunityIcons name={value ? 'image-plus' : 'image-off'} size={25} color={colors.WHITE} />}
        </View>
        <View style={styles.centerView}>
          <Text>{title}</Text>
        </View>
        <View style={styles.rightView}>
          <Switch
            value={value}
            onValueChange={onValueChange}
            disabled={false}
            activeText={activeText}
            inActiveText={inActiveText}
            circleSize={20}
            barHeight={25}
            circleBorderWidth={0}
            // màu khi active
            circleActiveColor={colors.WHITE}
            backgroundActive={colors.GREEN}
            //màu khi InActive
            circleInActiveColor={colors.WHITE}
            backgroundInactive={colors.GRAY}
            // renderInsideCircle={() => <CustomComponent />} // custom component to render inside the Switch circle (Text, Image, etc.)
            changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
            innerCircleStyle={styles.switch} // style for inner animated circle for what you (may) be rendering inside the circle
            outerCircleStyle={{}} // style for outer animated circle
            renderActiveText={true}
            renderInActiveText={true}
            switchLeftPx={switchLeftPx} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
            switchRightPx={switchRightPx} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
            switchWidthMultiplier={switchWidthMultiplier} // multipled by the `circleSize` prop to calculate total width of the Switch
            switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
          />
        </View>
      </View>
    ),
    [],
  );
  const renderCaiDatXacThucSinhHoc = () => {
    if (supportBiometrics === BIOMETRY_TYPE.FINGERPRINT || supportBiometrics === BIOMETRY_TYPE.TOUCH_ID)
      return renderSwitchImageViewButton(settingTitle[6], appSetting.dangNhapVanTay, (value) => onChangeAppSettings(value, 3), 'BẬT', 'TẮT', 2, 2, 4);
    else if (supportBiometrics === BIOMETRY_TYPE.FACE_ID)
      return renderSwitchImageViewButton(settingTitle[7], appSetting.dangNhapKhuonMat, (value) => onChangeAppSettings(value, 4), 'BẬT', 'TẮT', 2, 2, 4);
  };
  return (
    <ScreenComponent
      header
      renderView={
        <View style={styles.container}>
          <ScrollView>
            <View style={styles.contentView}>
              <View style={styles.profileView}>
                <Image source={R.images.img_man} style={styles.avatar} resizeMode={'contain'} />
                <View style={styles.profileDetail}>
                  <Text style={styles.address}>{props.userInfo?.nguoi_dung?.ten || 'Địa chỉ'}</Text>
                  <Text style={styles.email}>{props.userInfo?.nguoi_dung?.email || 'Email'}</Text>
                  <TouchableOpacity onPress={() => setShowResetDataCodepush((prev) => prev + 1)}>
                    <Text style={styles.phone}>{props.userInfo?.nguoi_dung?.dthoai || 'Số điện thoại'}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View style={styles.settingView}>
              <Text style={styles.headerSetting}>CẤU HÌNH</Text>
              {renderSettingItem(settingTitle[9])}
              {renderSettingItem(settingTitle[0])}
              {renderSettingItem(settingTitle[1])}
              {renderSwitchImageViewButton(settingTitle[2], appSetting.rung, (value) => onChangeAppSettings(value, 0), 'BẬT', 'TẮT', 2, 2, 4)}
              {renderSwitchImageViewButton(settingTitle[10], appSetting.luuAnhGiamDinhKhiChup, (value) => onChangeAppSettings(value, 5), 'BẬT', 'TẮT', 2, 2, 4)}
              {!isIOS && renderSwitchImageViewButton(settingTitle[4], appSetting.amThanhKhiChup, (value) => onChangeAppSettings(value, 2), 'BẬT', 'TẮT', 2, 2, 4)}
              {supportBiometrics && renderCaiDatXacThucSinhHoc()}
              {renderSettingItem(settingTitle[8])}

              <Text style={[styles.khuVucCanhBao]}>CÀI ĐẶT TÀI KHOẢN</Text>
              {renderSettingItem(settingTitle[5])}

              <TouchableOpacity onLongPress={() => actionSheetSetting.show()}>
                <Text style={styles.txtVersion}>{'Version ' + DeviceInfo.getVersion() + ' - ' + DeviceInfo.getBuildNumber() + ' - ' + (codepushInfo?.label || '')}</Text>
              </TouchableOpacity>
            </View>
            <ActionSheet
              ref={(o) => (actionSheetSetting = o)}
              title="Tuỳ chọn"
              options={['Cài đặt thông báo', 'Thông số ứng dụng', 'Đóng']}
              cancelButtonIndex={2}
              destructiveButtonIndex={2}
              onPress={(index) => onPressActionSheet(index)}
            />
          </ScrollView>
        </View>
      }
    />
  );
};

const mapStateToProps = (state) => ({
  appSetting: state.appSetting,
  userInfo: state.user.data,
});

const mapDispatchToProps = {
  updateRung: updateRung,
  updateGuiViTri: updateGuiViTri,
  updateAmThanhKhiChup: updateAmThanhKhiChup,
  updateUserInfo: updateUserInfo,
  updateSwitchNavigator: updateSwitchNavigator,
  updateDangNhapVanTay: updateDangNhapVanTay,
  updateDangNhapKhuonMat: updateDangNhapKhuonMat,
  updateLuuAnhGiamDinhKhiChup: updateLuuAnhGiamDinhKhiChup,
};

const SettingScreenConnect = connect(mapStateToProps, mapDispatchToProps)(SettingScreenComponent);
export const SettingScreen = memo(SettingScreenConnect, isEqual);
