import {ASYNC_STORAGE_KEY, REGUlAR_EXPRESSION, SCREEN_ROUTER} from '@app/commons/Constant';
import {selectUser, updateUserInfo} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {ButtonLinear, ScreenComponent, Text, TextInputOutlined} from '@component';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {KJUR} from 'jsrsasign';
import React, {memo, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {Alert, View, Image} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {connect, useDispatch, useSelector} from 'react-redux';
import styles from './DoiMatKhauStyles';
import {updateSwitchNavigator} from '@app/redux/slices/SwitchNavigatorSlice';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import {cloneObject} from '@app/utils/DataProvider';
import R from '@R';
const DoiMatKhauScreenComponent = ({route}) => {
  console.log('DoiMatKhauScreenComponent');
  const userInfo = useSelector(selectUser);
  const dispatch = useDispatch();
  console.log('userInfo', userInfo);
  const [dialogLoading, setDialogLoading] = useState(false);

  const getDefaultFormValue = () => {
    return {
      mkCu: '',
      mkMoi: '',
      xacNhanMkMoi: '',
    };
  };

  const {
    control,
    handleSubmit,
    getValues,
    formState: {errors},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const getErrMessage = (inputName, errType) => {
    if (errType === 'required') return 'Thông tin bắt buộc';
    else if (inputName === 'mkMoi' && errType === 'minLength') return 'Mật khẩu ít nhất 8 ký tự';
    else if (inputName === 'mkMoi' && errType === 'pattern') return 'Mật khẩu phải có chữ hoa, chữ thường, ký tự đặc biệt';
    else if (inputName === 'xacNhanMkMoi' && errType === 'validate') return 'Mật khẩu xác nhận không trùng với mật khẩu mới';
    return '';
  };
  const onPressDoiMatKhau = async (data) => {
    try {
      let md = new KJUR.crypto.MessageDigest({alg: 'sha256', prov: 'cryptojs'});
      md.updateString(data.mkCu);
      let passCuSHA256 = md.digest();
      let md2 = new KJUR.crypto.MessageDigest({alg: 'sha256', prov: 'cryptojs'});
      md2.updateString(data.mkMoi);
      let passMoiSHA256 = md2.digest();
      setDialogLoading(true);
      let params = {
        pas_cu: passCuSHA256,
        pas_moi: passMoiSHA256,
      };
      console.log('params', params);
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.CHANGE_PASSWORD, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        setDialogLoading(false);
        return;
      }
      let newUserInfo = cloneObject(userInfo);
      newUserInfo.nguoi_dung.pas = passMoiSHA256;
      dispatch(updateUserInfo(newUserInfo)); //cập nhật lại password để ngắt kết nối firebase
      await ESmartClaimEndpoint.disconnectFirebase(); //ngắt kết nối firebase
      setDialogLoading(false);
      resetValue(); //reset value để ra màn LOGIN
      FlashMessageHelper.showFlashMessage('Thông báo', 'Đổi mật khẩu thành công. Vui lòng đăng nhập lại', 'success');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };
  const resetValue = () => {
    AsyncStorage.setItem(ASYNC_STORAGE_KEY.USER_INFO, '');
    AsyncStorage.setItem(ASYNC_STORAGE_KEY.PUSH_TOKEN_REGISTERED, '');
    dispatch(updateUserInfo(''));
    dispatch(updateSwitchNavigator(SCREEN_ROUTER.AUTH));
  };
  // RENDER
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Đổi mật khẩu"
      renderView={
        <KeyboardAwareScrollView style={styles.container}>
          <View style={{alignItems: 'center'}}>
            <Image source={R.images.img_change_password} resizeMode="contain" style={styles.imgSendEmail} />
          </View>
          {/* MK CŨ */}
          <Controller
            control={control}
            name="mkCu"
            rules={{
              required: true,
              // minLength: 8,
              // pattern: REGUlAR_EXPRESSION.REG_PASSWORD,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                title="Mật khẩu cũ"
                value={value}
                onChangeText={onChange}
                placeholder="Nhập mật khẩu cũ"
                isRequired={true}
                isPassword
                error={errors.mkCu && getErrMessage('mkCu', errors.mkCu.type)}
              />
            )}
          />
          {/* MK MỚI */}
          <Controller
            control={control}
            name="mkMoi"
            rules={{
              required: true,
              minLength: 8,
              pattern: REGUlAR_EXPRESSION.REG_PASSWORD,
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                title="Mật khẩu mới"
                value={value}
                onChangeText={onChange}
                placeholder="Nhập mật khẩu mới"
                isRequired={true}
                isPassword
                error={errors.mkMoi && getErrMessage('mkMoi', errors.mkMoi.type)}
              />
            )}
          />

          {/* XÁC NHẬN MK MỚI */}
          <Controller
            control={control}
            name="xacNhanMkMoi"
            rules={{
              required: true,
              validate: (value) => {
                if (value !== getValues('mkMoi')) return 'Mật khẩu xác nhận không trùng với mật khẩu mới';
              },
            }}
            render={({field: {onChange, value}}) => (
              <TextInputOutlined
                title="Xác nhận mật khẩu mới"
                value={value}
                onChangeText={onChange}
                placeholder="Xác nhận mật khẩu mới"
                isRequired={true}
                isPassword
                error={errors.xacNhanMkMoi && getErrMessage('xacNhanMkMoi', errors.xacNhanMkMoi.type)}
              />
            )}
          />
          <View>
            <Text children="Mật khẩu có ít nhất 8 ký tự bao gồm cả chữ hoa, chữ thường, ký tự đặc biệt" style={styles.txtHintPassword} />
          </View>
          <ButtonLinear title="Xác nhận" onPress={handleSubmit(onPressDoiMatKhau)} linearStyle={{marginTop: spacing.small}} />
        </KeyboardAwareScrollView>
      }
    />
  );
};
export const DoiMatKhauScreen = memo(DoiMatKhauScreenComponent, isEqual);
