import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {ScreenComponent, Text} from '@component';
import {Picker} from '@react-native-picker/picker';
import {useNavigation} from '@react-navigation/native';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Dimensions, FlatList, Platform, View} from 'react-native';
import styles from './AssessmentLocationStyles';
const {width, height} = Dimensions.get('screen');

const AssessmentLocationScreenComponent = (props) => {
  console.log('AssessmentLocationModalScreen');
  const navigation = useNavigation();
  const [cities, setCities] = useState([]); //tỉnh thành,
  const [districes, setDistrices] = useState([]); //tỉnh thành,

  const [citySelected, setCitySelected] = useState(1); //city được chọn
  const [districtSelected, setDistrictSelected] = useState([]);
  const [citiesView, setCityView] = useState([]); //view của tưng city item

  useEffect(() => {
    getDiaBanPhuTrach();
  }, []);

  const getDiaBanPhuTrach = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.INDEMNIFY_LOCATION, {});
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      let tinhThanh = response.data_info.tinh_thanh;
      //lấy dữ liệu của tất cả các quận
      let quanHuyen = response.data_info.quan_huyen;
      setDistrices(quanHuyen);
      //lấy dữ liệu quận của thàn phố Hà nội
      let tmpDistrict = [];
      tmpDistrict = quanHuyen.filter((item) => item.ma_ct == '01');
      setDistrictSelected(tmpDistrict);
      //setup data city view
      let tmpCityView = [];
      for (let i = 0; i < tinhThanh.length; i++) {
        tmpCityView.push(<Picker.Item label={tinhThanh[i].ten_tinh} value={+tinhThanh[i].tinh_thanh} />);
      }
      setCityView(tmpCityView);
      setCities(tinhThanh);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onCitySelectedChange = (ma_quan_huyen, index) => {
    setCitySelected(ma_quan_huyen);
    let tmp = districes.filter((item) => item.ma_ct == ma_quan_huyen);
    setDistrictSelected(tmp);
  };

  const renderDistricItem = (data) => {
    return (
      <View style={styles.districtItem}>
        <Text>&bull; {data.item.ten_quan}</Text>
      </View>
    );
  };
  const onPressClose = () => {
    navigation.goBack();
  };
  return (
    <ScreenComponent
      headerBack
      headerTitle={'Địa bàn phụ trách'}
      renderView={
        <View style={styles.container}>
          {/* <View style={styles.headerView}>
            <Text style={styles.txtHeader}>Địa bàn phụ trách</Text>
            <TouchableOpacity onPress={onPressClose}>
              <AntDesign name="close" size={20} style={styles.iconClose} />
            </TouchableOpacity>
          </View> */}
          <View styles={styles.content}>
            {/* <Text style={styles.txtTitle}>Xem địa bàn giám định</Text> */}
            <>
              <Text style={styles.txtLocationTitle}>Tỉnh thành</Text>
              {cities.length > 0 ? (
                <Picker
                  selectedValue={citySelected}
                  style={{
                    height: Platform.OS == 'android' ? 50 : null,
                    width: width - 20,
                  }}
                  onValueChange={onCitySelectedChange}>
                  {citiesView}
                </Picker>
              ) : (
                <Text>Chưa có dữ liệu Tỉnh thành giám định</Text>
              )}
            </>
          </View>
          <>
            <Text style={styles.txtLocationTitle}>Quận huyện</Text>
            {districtSelected.length > 0 ? (
              <FlatList data={districtSelected} keyExtractor={(item) => item.quan_huyen} renderItem={renderDistricItem} style={styles.listDistrict} />
            ) : (
              <Text>Chưa có dữ liệu Quận huyện giám định</Text>
            )}
          </>
        </View>
      }
    />
  );
};

export const AssessmentLocationScreen = memo(AssessmentLocationScreenComponent, isEqual);
