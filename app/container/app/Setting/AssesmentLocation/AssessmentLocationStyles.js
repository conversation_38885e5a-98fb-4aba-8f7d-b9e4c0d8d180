import {StyleSheet} from 'react-native';
import {getStatusBarHeight} from 'react-native-iphone-x-helper';
import {colors} from '../../../../commons/Theme';
export default StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: Platform.OS == 'ios' ? getStatusBarHeight() : 0,
    // height: height,
    // borderWidth: 1,
  },
  content: {},
  headerView: {
    borderBottomWidth: 1,
    borderBottomColor: colors.WHITE6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
  },
  txtHeader: {
    fontSize: 17,
    fontWeight: '900',
  },
  iconClose: {
    color: colors.BLACK,
    opacity: 0.5,
  },
  txtTitle: {
    fontSize: 25,
    marginVertical: 8,
    fontWeight: 'bold',
    marginTop: 20,
  },
  listDistrict: {
    marginTop: 15,
    marginBottom: 20,
  },
  districtItem: {
    marginTop: 10,
    borderBottomWidth: 1,
    paddingBottom: 10,
    borderBottomColor: colors.GRAY,
  },
  txtLocationTitle: {
    fontWeight: 'bold',
    fontSize: 20,
    marginVertical: 10,
  },
});
