import {colors} from '@app/commons/Theme';
import {Icon, TextInputOutlined, Text} from '@app/components';
import {dimensions, spacing} from '@app/theme';
import moment from 'moment';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Controller, useForm} from 'react-hook-form';
import {SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Modal from 'react-native-modal';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

const ModalAddEventComponent = forwardRef(({onBackPress, onPressLuu, value, title}, ref) => {
  const [isVisible, setIsVisible] = useState(false);
  const [openGioBatDau, setOpenGioBatDau] = useState(false);
  const [openGioKetThuc, setOpenGioKetThuc] = useState(false);

  const [text, setText] = useState('');

  const getDefaultFormValue = () => {
    return {
      gio_bat_dau: new Date(),
      gio_ket_thuc: new Date(),
    };
  };

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = useForm({
    defaultValues: getDefaultFormValue(),
    mode: 'onChange',
  });

  const gioBatDau = watch('gio_bat_dau');
  const gioKetThuc = watch('gio_ket_thuc');

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const initModalData = () => {
    if (value && value !== null) setText(value);
    else setText('');
  };

  const _onPressLuu = (val) => {
    onBackPress && onBackPress();
    onPressLuu && onPressLuu(val, title);
  };

  const onPressDateConfirm = (date, setToggleDateTime, setDate) => {
    setToggleDateTime(false);
    setDate(date);
  };

  /* RENDER */

  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate, type) => (
    <DateTimePickerModal
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        onPressDateConfirm(dateSelected, setToggleDateTime, setDateTime, type);
      }}
      display="spinner"
      onCancel={() => setToggleDateTime(false)}
      date={date}
      cancelTextIOS="Để sau"
      confirmTextIOS="Chọn"
      locale={'vi_VN'}
      maximumDate={maxDate}
      minimumDate={minDate}
      themeVariant="light"
      isDarkModeEnabled={false}
      //   is24Hour={true}
    />
  );

  const renderHeader = () => {
    return (
      <View style={styles.modalTitleView}>
        <Text style={styles.modalTitle} children="Thêm lịch" />
        <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
          <Icon.Ionicons name="close" size={22} color={'gray'} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      propagateSwipe={true}
      swipeDirection={['down', 'right']}
      onModalShow={initModalData}
      onSwipeComplete={onBackPress}
      onBackdropPress={onBackPress}
      style={styles.modal}
      avoidKeyboard={true}>
      <SafeAreaView style={styles.modalContent}>
        {renderHeader()}
        <KeyboardAwareScrollView flex={1} marginHorizontal={10} marginTop={10}>
          <Controller
            control={control}
            name="gio_bat_dau"
            rules={{
              required: true,
            }}
            render={({field: {value}}) => <TextInputOutlined title="Tiêu đề" value={value} isRequired={true} />}
          />
          <Controller
            control={control}
            name="gio_bat_dau"
            rules={{
              required: true,
            }}
            render={({field: {value}}) => <TextInputOutlined title="Đối tượng" value={value} isRequired={true} />}
          />
          <Controller
            control={control}
            name="gio_bat_dau"
            rules={{
              required: true,
            }}
            render={({field: {value}}) => <TextInputOutlined title="Địa điểm" value={value} isRequired={true} />}
          />
          <View flex={1} style={{flexDirection: 'row'}}>
            <Controller
              control={control}
              name="gio_bat_dau"
              rules={{
                required: true,
              }}
              render={({field: {value}}) => (
                <TextInputOutlined
                  title="Giờ bắt đầu"
                  isTouchableOpacity={true}
                  onPress={() => setOpenGioBatDau(true)}
                  value={moment(value).format('HH:mm')}
                  editable={false}
                  containerStyle={{flex: 1, marginRight: spacing.small}}
                  isRequired={true}
                  isDateTimeField
                />
              )}
            />
            {renderDateTimeComp(openGioBatDau, setOpenGioBatDau, (value) => setValue('gio_bat_dau', value), gioBatDau, 'time')}
            <Controller
              control={control}
              name="gio_ket_thuc"
              rules={{
                required: true,
              }}
              render={({field: {value}}) => (
                <TextInputOutlined
                  title="Giờ kết thúc"
                  isTouchableOpacity={true}
                  onPress={() => setOpenGioKetThuc(true)}
                  value={moment(value).format('HH:mm')}
                  editable={false}
                  containerStyle={{flex: 1}}
                  isRequired={true}
                  isDateTimeField
                />
              )}
            />
            {renderDateTimeComp(openGioKetThuc, setOpenGioKetThuc, (value) => setValue('gio_ket_thuc', value), gioKetThuc, 'time')}
          </View>
        </KeyboardAwareScrollView>
        {/* <View style={styles.btnView}>
          <ButtonLinear
            title="Đóng"
            linearStyle={styles.btnLuu}
            onPress={() => {
              setIsVisible(false);
            }}
            linearColors={[colors.GRAY, colors.GRAY]}
            textStyle={{color: colors.BLACK_03}}
          />
          <ButtonLinear title={'Lưu'} linearStyle={[styles.btnLuu, {marginLeft: spacing.smaller}]} onPress={() => _onPressLuu(text)} />
        </View> */}
      </SafeAreaView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    margin: 0,
  },
  btnView: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 30,
    left: 10,
    right: 10,
  },
  modalContent: {
    height: dimensions.height * 0.6,
    backgroundColor: '#FFF',
    width: dimensions.width,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  modalHeader: {
    height: 20,
    // borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    width: dimensions.width / 5,
    height: 7,
    // borderWidth: 1,
    backgroundColor: colors.GRAY,
    borderRadius: 20,
  },
  btnLuu: {
    // marginTop: spacing.smaller,
  },
  inputContainer: {
    // flex: 1,
  },
  blockTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.PRIMARY,
    paddingTop: spacing.tiny,
  },
  blockSubForm: {
    backgroundColor: '#FFF',
    paddingBottom: spacing.tiny,
    marginBottom: spacing.smaller,
    // paddingBottom :
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.RED1,
    marginVertical: 10,
  },
  errText: {
    color: colors.RED1,
    marginBottom: 5,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  inputStyle: {
    height: 80,
    textAlignVertical: 'top',
  },
});
export const ModalAddEvent = memo(ModalAddEventComponent, isEqual);
