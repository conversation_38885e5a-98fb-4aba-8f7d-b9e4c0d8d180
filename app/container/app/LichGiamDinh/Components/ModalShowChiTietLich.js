import {isIOS} from '@app/commons/Constant';
import {colors} from '@app/commons/Theme';
import HeaderModal from '@app/components/HeaderModal';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {requestCalenderPermission} from '@app/utils/PermisstionProvider';
import {Icon, Text} from '@component';
import moment from 'moment';
import React, {forwardRef, memo, useEffect, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, Linking, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import ReactNativeCalendarEvents from 'react-native-calendar-events';
import Modal from 'react-native-modal';
import {Switch} from 'react-native-switch';

let timer;

const ModalShowChiTietLichComponent = forwardRef((props, ref) => {
  const {onBackPress, data} = props;

  const [isVisible, setIsVisible] = useState(false);
  // const [check10, setCheck10] = useState(false);
  // const [check30, setCheck30] = useState(false);
  const [remind, setRemind] = useState(false);
  const [eventId, setEventId] = useState('');

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const onChangeValueRemind = async (val) => {
    const calenderPermission = await requestCalenderPermission();
    let calendars = await ReactNativeCalendarEvents.findCalendars();
    if (calenderPermission && calendars.length > 0) {
      if (val) {
        saveEvent(data);
      } else {
        removeEvent();
      }
    }
  };

  const layChiTietLich = async () => {
    try {
      let params = {
        so_id_lich: data.so_id_lich,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.XEM_CHI_TIET_LICH, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      const lich = response.data_info.lich;
      setEventId(lich.id_chuong);
      if (lich.bao_lich === 'C') {
        setRemind(true);
      } else {
        setRemind(false);
      }
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  useEffect(() => {
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const luuChuongBaoNhacNho = async (data, baoLich, result) => {
    try {
      let params = {
        so_id_lich: data.so_id_lich,
        bao_lich: baoLich,
        id_chuong: result,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LUU_CHUONG_BAO_NHAC_NHO, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      layChiTietLich();
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const saveEvent = async () => {
    let DATE_FORMAT = 'HH:mm DD/MM/YYYY';
    let eventDetails = {
      startDate: moment(data?.ngay_bd_hien, DATE_FORMAT).toISOString(),
      endDate: moment(data?.ngay_bd_hien, DATE_FORMAT).toISOString(),
      alarms: [
        {
          date: isIOS ? -15 : 15,
        },
      ],
    };

    ReactNativeCalendarEvents.saveEvent(data.tieu_de, eventDetails).then(
      (result) => {
        luuChuongBaoNhacNho(data, 'C', result);
      },
      (result) => {
        console.error(result);
      },
    );
  };

  const removeEvent = async () => {
    ReactNativeCalendarEvents.removeEvent(eventId).then(
      (result) => {
        luuChuongBaoNhacNho(data, 'K', '');
      },
      (result) => {
        console.error(result);
      },
    );
  };

  return (
    <Modal
      onModalWillShow={layChiTietLich}
      isVisible={isVisible}
      animationIn="fadeInRight"
      animationOut="fadeOutRight"
      onSwipeComplete={onBackPress}
      onBackButtonPress={onBackPress}
      swipeDirection={['down', 'right']}
      // onBackdropPress={onBackPress}
      style={styles.modal}>
      <SafeAreaView flex={1}>
        <HeaderModal title="Chi tiết lịch" onBackPress={onBackPress} />
        <View showsVerticalScrollIndicator={false} paddingHorizontal={spacing.medium}>
          <View justifyContent="space-between" style={[styles.modalRowContent]} marginTop={spacing.medium}>
            <Text style={styles.modalLabel}>Trạng thái: </Text>
            {data.trang_thai === 'K' ? <Text style={[styles.modalValue, {color: colors.GREEN2}]}>Đã kết thúc</Text> : <Text style={[styles.modalValue, {color: colors.RED1}]}>Chưa kết thúc</Text>}
          </View>
          <View style={styles.switchRow}>
            <Text style={[styles.modalLabel, {color: colors.PRIMARY}]}>Nhắc nhở</Text>
            <Switch
              value={remind}
              onValueChange={() => onChangeValueRemind(!remind)}
              disabled={false}
              activeText={'Tắt'}
              inActiveText={'Bật'}
              circleSize={20}
              barHeight={26}
              circleBorderWidth={0}
              // màu khi active
              circleActiveColor={colors.WHITE}
              backgroundActive={colors.PRIMARY}
              //màu khi InActive
              circleInActiveColor={colors.WHITE}
              backgroundInactive={colors.GRAY}
              // renderInsideCircle={() => <CustomComponent />} // custom component to render inside the Switch circle (Text, Image, etc.)
              changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
              innerCircleStyle={{alignItems: 'center', justifyContent: 'center'}} // style for inner animated circle for what you (may) be rendering inside the circle
              outerCircleStyle={{}} // style for outer animated circle
              renderActiveText={true}
              renderInActiveText={true}
              switchLeftPx={5} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
              switchRightPx={5} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
              switchWidthMultiplier={3} // multipled by the `circleSize` prop to calculate total width of the Switch
              switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
            />
          </View>
          {/* {remind && (
            <View>
              <TouchableOpacity style={styles.suggestTimeRow} onPress={() => setCheck10(!check10)}>
                <Text style={[styles.txtSuggestTime, {color: check10 ? colors.BLACK_06 : colors.GRAY10}]}>Trước 10 phút</Text>
                <Icon.AntDesign name="checkcircle" size={20} color={check10 ? colors.GREEN : colors.GRAY2} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.suggestTimeRow} onPress={() => setCheck30(!check30)}>
                <Text style={[styles.txtSuggestTime, {color: check30 ? colors.BLACK_06 : colors.GRAY10}]}>Trước 30 phút</Text>
                <Icon.AntDesign name="checkcircle" size={20} color={check30 ? colors.GREEN : colors.GRAY2} />
              </TouchableOpacity>
            </View>
          )} */}
          <View style={styles.divider} />
          <View style={styles.modalRowContent}>
            <Text style={[styles.modalLabel, {color: colors.PRIMARY}]}>{data.tieu_de} </Text>
          </View>
          <View style={styles.modalRowContent}>
            <Text style={styles.modalLabel}>
              Người liên hệ: <Text style={styles.modalValue}>{data.nguoi_lhe}</Text>
            </Text>
          </View>
          <TouchableOpacity style={styles.modalRowContent} onPress={() => Linking.openURL(`tel:${data.dthoai_lhe}`)}>
            <View flexDirection="row" alignItems="center">
              <Icon.Entypo name="old-phone" size={20} color={colors.PRIMARY} style={{marginRight: 10}} />
              {/* <Text style={styles.modalLabel}>Điện thoại: </Text> */}
              <Text style={styles.modalValue}>{data.dthoai_lhe}</Text>
            </View>
          </TouchableOpacity>
          <View style={styles.divider} />
          <View style={styles.modalRowContent}>
            <Text style={styles.modalLabel}>Thời gian bắt đầu: </Text>
            <Text style={styles.modalValue}>{data.ngay_bd_hien}</Text>
          </View>
          <View style={styles.modalRowContent}>
            <Text style={styles.modalLabel}>Thời gian KT dự kiến: </Text>
            <Text style={styles.modalValue}>{data.ngay_kt_hien}</Text>
          </View>
          <View style={styles.modalRowContent}>
            <Text style={styles.modalLabel}>
              Địa điểm: <Text style={styles.modalValue}>{data.dia_diem}</Text>
            </Text>
          </View>
          <View style={styles.divider} />
          <Text style={styles.modalLabel}>
            Ghi chú: <Text style={styles.modalValue}>{data.noi_dung}</Text>
          </Text>
        </View>
      </SafeAreaView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
    // paddingHorizontal: 10,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginVertical: spacing.medium,
  },
  btnClose: {
    zIndex: 2,
    top: spacing.small,
    position: 'absolute',
    right: spacing.small,
  },
  modalRowContent: {
    flexDirection: 'row',
    marginVertical: spacing.tiny,
    // paddingHorizontal: spacing.small,
  },
  modalLabel: {
    fontSize: 15,
    lineHeight: 20,
    color: colors.BLACK_03,
  },
  modalValue: {
    fontSize: 15,
    lineHeight: 20,
    color: colors.BLACK_03,
  },
  divider: {
    height: 1,
    marginVertical: spacing.smaller,
    backgroundColor: colors.WHITE1,
  },
  txtGoiNgay: {
    fontSize: 16,
    color: colors.WHITE,
    textTransform: 'uppercase',
  },
  btnGoiNgay: {
    borderWidth: 1,
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: colors.PRIMARY,
    backgroundColor: colors.WHITE1,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  suggestTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: spacing.smaller,
  },
  txtSuggestTime: {
    color: colors.GRAY10,
  },
});

export const ModalShowChiTietLich = memo(ModalShowChiTietLichComponent, isEqual);
