import {colors} from '@app/commons/Theme';
import {spacing} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  titleRow: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  content: {
    flexDirection: 'row',
    marginBottom: spacing.tiny,
    marginLeft: spacing.medium,
  },
  btnAddEvent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: spacing.small,
  },
  txtBtnAddEvent: {
    // flex: 1,
    textAlign: 'center',
    marginHorizontal: spacing.mediumPlush,
  },
  viewBtn: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtLabel: {
    lineHeight: 20,
    color: colors.GRAY6,
  },
  detail: {
    flex: 1,
    lineHeight: 20,
    color: colors.BLACK_03,
  },
  listHeaderTxtTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.PRIMARY,
    textTransform: 'uppercase',
    marginHorizontal: spacing.tiny,
  },
  txtEmpty: {
    textAlign: 'center',
    color: colors.BLACK_03,
    marginTop: spacing.small,
  },
  listHeaderTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: spacing.small,
    marginLeft: spacing.smaller,
    marginBottom: spacing.smaller,
  },
  statusDot: {
    width: 10,
    height: 10,
    marginTop: 2,
    borderRadius: 25,
    marginHorizontal: spacing.smaller,
  },
});
