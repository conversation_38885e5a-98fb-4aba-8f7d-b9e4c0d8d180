import {colors} from '@app/commons/Theme';
import {selectUser} from '@app/redux/slices/UserSlice';
import axiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {spacing} from '@app/theme';
import {requestCalenderPermission} from '@app/utils/PermisstionProvider';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@component';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, RefreshControl, SafeAreaView, TouchableOpacity, View} from 'react-native';
import RNCalendarEvents from 'react-native-calendar-events';
import {Calendar, LocaleConfig} from 'react-native-calendars';
import {useSelector} from 'react-redux';
import {ModalAddEvent, ModalShowChiTietLich} from './Components';
import styles from './LichGiamDinhStyle';

LocaleConfig.locales['vi'] = {
  monthNames: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'],
  monthNamesShort: ['Jan.', 'Feb.', 'Mar', 'Apr', 'May', 'Jun', 'Lul', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'],
  dayNames: ['Chủ Nhật', 'Thứ Hai', 'Thứ Ba', 'Thứ Tư', 'Thứ Năm', 'Thứ Sáu', 'Thứ Bảy'],
  dayNamesShort: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
  today: 'Hôm nay',
};
LocaleConfig.defaultLocale = 'vi';

const LichGiamDinhScreenComponent = (props) => {
  const INITIAL_DATE = moment().format('YYYY-MM-DD');
  const DATE_FORMAT = 'YYYY-MM-DD';
  const userInfo = useSelector(selectUser);

  const [dateSelected, setDateSelected] = useState(null);
  const [eventsByDay, setEventsByDay] = useState([]);
  const [dataLichGiamDinhTheoThang, setDataLichGiamDinhTheoThang] = useState([]);
  const [chiTietLich, setLichChiTiet] = useState({});
  const [loading, setLoading] = useState(false);

  let refModalAddEvent = useRef(null);
  let refModalShowDetailEvent = useRef(null);

  useEffect(() => {
    // RNCalendarEvents.fetchAllEvents('2022-12-19T19:26:00.000Z', '2022-12-20T19:26:00.000Z').then(
    //   (result) => {
    //     console.log('Auth requested', result);
    //   },
    //   (result) => {
    //     console.error(result);
    //   },
    // );
    layDsLichTheoThang(new Date());
    requestCalenderPermission();
  }, []);

  const layDanhSachLichTheoNgay = async (day) => {
    setLoading(true);
    try {
      let formatDate = +moment(day).format('YYYYMMDD');
      let params = {
        nsd_lich: userInfo.nguoi_dung.nsd,
        ngay_bd: formatDate,
        trang: 1,
        so_dong: 30,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_LICH_GIAM_DINH_THEO_NGAY, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setEventsByDay(response.data_info.data);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  const layDsLichTheoThang = async (month) => {
    setLoading(true);
    try {
      let formatDate = +moment(month).format('YYYYMM');
      let params = {
        nsd_lich: userInfo.nguoi_dung.nsd,
        thang: formatDate,
        trang: 1,
        so_dong: 30,
      };
      let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_DS_LICH_GIAM_DINH_THEO_THANG, params);
      setLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setDataLichGiamDinhTheoThang(response.data_info);
    } catch (error) {
      setLoading(false);
      Alert.alert('Thông báo', error.message);
    }
    return;
  };

  // const onCreateTriggerNotification = async (data) => {
  //   const date = new Date(Date.now());
  //   date.setHours(14);
  //   date.setMinutes(50);

  //   const trigger: TimestampTrigger = {
  //     type: TriggerType.TIMESTAMP,
  //     timestamp: date.getTime(),
  //     repeatFrequency: RepeatFrequency.HOURLY,
  //   };

  //   await notifee.createTriggerNotification(
  //     {
  //       id: data.so_id_lich,
  //       title: data.tieu_de,
  //       body: 'Today at 11:20am',
  //       android: {
  //         channelId: 'your-channel-id',
  //       },
  //     },
  //     trigger,
  //   );
  // };

  const getDates = (data) => {
    let markedDate = {};
    if (data.length > 0) {
      data.map((e) => {
        markedDate[moment(e.ngay, 'YYYYMMDD').format(DATE_FORMAT)] = {
          marked: true,
          dotColor: e.trang_thai === 'K' ? colors.GREEN2 : colors.RED1,
        };
        markedDate[moment(dateSelected).format(DATE_FORMAT)] = {
          selected: true,
          disableTouchEvent: false,
          selectedColor: '#81D4FA',
          selectedTextColor: colors.BLACK_03,
        };
      });
    } else
      markedDate[moment(dateSelected).format(DATE_FORMAT)] = {
        selected: true,
        disableTouchEvent: false,
        selectedColor: '#81D4FA',
        selectedTextColor: colors.BLACK_03,
      };
    return markedDate;
  };

  const onPressItem = (val) => {
    setLichChiTiet(val);
    refModalShowDetailEvent.current.show();
  };

  const onPressBtnAddEvent = () => {
    RNCalendarEvents.checkPermissions().then(
      (result) => {
        refModalAddEvent.current.show();
      },
      (result) => {
        Alert.alert(result);
      },
    );
  };

  const onDayPress = (day) => {
    setDateSelected(day);
    layDanhSachLichTheoNgay(day);
  };

  const onMonthChange = (val) => {
    setEventsByDay(null);
    layDsLichTheoThang(val);
  };

  const renderHeaderListEvents = () => {
    return (
      <View style={styles.listHeaderTitleView}>
        <Icon.AntDesign name="calendar" size={18} color={colors.PRIMARY} />
        <Text style={styles.listHeaderTxtTitle}>Danh sách lịch</Text>
      </View>
    );
  };

  const renderFooterBtn = () => {
    return (
      <View style={styles.viewBtn}>
        <ButtonLinear onPress={onPressBtnAddEvent} linearStyle={styles.btnAddEvent} textStyle={styles.txtBtnAddEvent} title="Thêm lịch" />
      </View>
    );
  };

  return (
    <ScreenComponent
      headerBack
      headerTitle="Lịch giám định"
      renderView={
        <SafeAreaView style={styles.container}>
          <Calendar
            // style={styles.calendar}
            current={INITIAL_DATE}
            // minDate={getDate(-5)}
            // displayLoadingIndicator={false}
            // markingType={'period'}
            // showWeekNumbers
            // enableSwipeMonths
            onMonthChange={(val) => onMonthChange(val.dateString)}
            onDayPress={(day) => onDayPress(day.dateString)}
            theme={{
              // backgroundColor: 'red',
              calendarBackground: colors.WHITE1,
              textSectionTitleColor: colors.BLUE2,
              textSectionTitleDisabledColor: '#d9e1e8',
              selectedDayBackgroundColor: colors.PRIMARY,
              selectedDayTextColor: '#ffffff',
              todayTextColor: '#00adf5',
              dayTextColor: '#2d4150',
              textDisabledColor: colors.GRAY,
              dotColor: '#00adf5',
              selectedDotColor: colors.PRIMARY,
              arrowColor: colors.PRIMARY,
              disabledArrowColor: '#d9e1e8',
              monthTextColor: colors.PRIMARY,
              indicatorColor: 'blue',
              // textDayFontFamily: 'monospace',
              // textMonthFontFamily: 'monospace',
              // textDayHeaderFontFamily: 'monospace',
              textDayFontWeight: '300',
              textMonthFontWeight: '500',
              textDayHeaderFontWeight: '300',
              textDayFontSize: 16,
              textMonthFontSize: 18,
              textDayHeaderFontSize: 14,
            }}
            markedDates={getDates(dataLichGiamDinhTheoThang)}
          />
          {renderHeaderListEvents()}
          <FlatList
            data={eventsByDay}
            keyExtractor={(e, i) => i.toString()}
            refreshControl={<RefreshControl refreshing={loading} />}
            renderItem={(item) => <RenderEvent data={item} onPress={(value) => onPressItem(value)} />}
            ListEmptyComponent={<Text style={styles.txtEmpty}>Chưa có lịch!</Text>}
          />
          {/* <TouchableOpacity onPress={onCreateTriggerNotification}>
            <Text>Luuw lịch</Text>
          </TouchableOpacity> */}
          <ModalAddEvent ref={refModalAddEvent} onBackPress={() => refModalAddEvent.current.hide()} />
          <ModalShowChiTietLich data={chiTietLich} ref={refModalShowDetailEvent} onBackPress={() => refModalShowDetailEvent.current.hide()} />
        </SafeAreaView>
      }
    />
  );
};

export const LichGiamDinhScreen = memo(LichGiamDinhScreenComponent, isEqual);

const RenderEvent = ({data, onPress}) => {
  const item = data.item;
  const index = data.index;
  return (
    <TouchableOpacity key={index} style={{marginTop: spacing.tiny}} onPress={() => onPress(item)}>
      <View style={styles.titleRow}>
        <View style={[styles.statusDot, {backgroundColor: item.trang_thai === 'K' ? colors.GREEN2 : colors.RED3}]} />
        {/* <Icon.Entypo name="dot-single" size={40} color={item.trang_thai === 'K' ? colors.GREEN2 : colors.RED3} /> */}
        <Text style={[styles.txtLabel, {color: colors.PRIMARY, fontSize: 16}]}>{item.tieu_de}</Text>
      </View>
      <View paddingHorizontal={spacing.medium}>
        {/* <View style={styles.content}>
          <Text style={styles.txtLabel}>Người LH: </Text>
          <Text style={styles.detail}>{item.nguoi_lhe}</Text>
        </View> */}
        {/* <TouchableOpacity onPress={() => Linking.openURL(`tel:${item.dthoai_lhe}`)}>
          <View style={styles.content} flexDirection="row" alignItems="center">
            <Text style={styles.txtLabel}>Điện thoại: </Text>
            <Icon.Entypo name="old-phone" size={20} color={colors.PRIMARY} style={{marginRight: 10}} />
            <Text style={styles.modalValue}>{item.dthoai_lhe}</Text>
          </View>
        </TouchableOpacity> */}
        <View style={styles.content}>
          <Text style={styles.txtLabel}>Thời gian bắt đầu: </Text>
          <Text style={styles.detail}>{item.ngay_bd_hien}</Text>
        </View>
        <View style={styles.content}>
          <Text style={styles.txtLabel}>Thời gian KT dự kiến: </Text>
          <Text style={styles.detail}>{item.ngay_kt_hien}</Text>
        </View>
        {/* <View style={styles.content}>
          <Text style={styles.txtLabel}>
            Địa điểm: <Text style={styles.detail}>{item.dia_diem}</Text>
          </Text>
        </View> */}
      </View>
    </TouchableOpacity>
  );
};
