import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {Icon, PickerModalView, ScreenComponent, SearchBar, Text} from '@component';
import React, {memo, useCallback, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, TouchableOpacity, View} from 'react-native';
import {DVYTItem} from './Components';
import styles from './DichVuYTeStyles';

const DichVuYTeScreenComponent = (props) => {
  console.log('DichVuYTeScreen');
  const [dialogLoading, setDialogLoading] = useState(false);
  //DROPDOWN danh mục cha
  const [dvytChaSelected, setDvytChaSelected] = useState('');
  const [listDVYTCha, setListDVYTCha] = useState([{Name: 'Tất cả', Value: ''}]);
  //Tìm kiếm
  const [searchInput, setSearchInput] = useState('');
  const [listDMYTCon, setListDMYTCon] = useState([]);
  const [listDMYTConRoot, setListDMYTConRoot] = useState([]);

  useEffect(() => {
    setDialogLoading(true);
    layDMYTCha();
  }, []);

  const layDMYTCha = useCallback(async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DICH_VU_SUC_KHOE_CHA, {});
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      response.data_info.map((item) => {
        item.Name = item.ten_dich_vu;
        item.Value = item.ma_dich_vu;
        item.Id = item.ma_dich_vu;
        return item;
      });
      setListDVYTCha(listDVYTCha.concat(response.data_info));
      setSearchInput('');
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  }, []);

  const layDMYTConTheoCha = useCallback(
    async (maDanhMucCha = '') => {
      let params = {ma_dich_vu_ct: maDanhMucCha, trang: 1, so_dong: 100, tim: searchInput};
      try {
        let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_DICH_VU_SUC_KHOE_THEO_MA_CHA, params);
        setDialogLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        setListDMYTCon([...response.data_info]);
        setListDMYTConRoot([...response.data_info]);
      } catch (error) {
        Alert.alert('Thông báo', error.message);
      }
    },
    [searchInput],
  );

  const onChangeDMYTCha = useCallback((itemSelected) => itemSelected !== null && layDMYTConTheoCha(itemSelected.Value), []);

  const onPressSearch = useCallback(() => {
    if (dvytChaSelected === '') layDMYTConTheoCha();
    else {
      let result = [];
      let arrTextSearch = searchInput.trim().split(' ');
      arrTextSearch = arrTextSearch.filter((item) => item != '');
      for (let i = 0; i < listDMYTConRoot.length; i++) {
        let arrTenHangMuc = listDMYTConRoot[i].ten_dich_vu.split(' ');
        let tonTai = 0; //nếu tonTai == (arrTextSearch.length - 1) => tất cả các từ trong arrTextSearch có trong categoryFixFilter[i]
        for (let j = 0; j < arrTextSearch.length; j++) {
          for (let k = 0; k < arrTenHangMuc.length; k++) {
            /*
              j + 1 != tonTai : để loại trường hợp chuỗi tên hạng mục có 2 từ giống nhau
              ví dụ : 
              tên hạng mục : tôi là tôi 
              từ cần tìm : tôi là 
              -> khi duyệt từ 'tôi' ở từ cần tìm -> 'tôi' sẽ được tính 2 lần ->  dẫn đến sai kết quả của biến tonTai
              //có cách khác là remove duplicate ở cả 2 mảng arrTenHangMuc và arrTextSearch r tìm -> sẽ không bị dính trường hợp trên =))
              */
            if (arrTenHangMuc[k].toUpperCase() == arrTextSearch[j].toUpperCase() && j + 1 != tonTai) {
              tonTai = tonTai + 1;
              break;
            }
          }
        }
        if (tonTai == arrTextSearch.length) result.push(listDMYTConRoot[i]);

        //tìm theo
        if (arrTextSearch.length === 1 && listDMYTConRoot[i].ma_dich_vu.toUpperCase().indexOf(arrTextSearch[0].toUpperCase()) > -1) result.push(listDMYTConRoot[i]);
      }
      setListDMYTCon([...result]);
    }
  }, [dvytChaSelected, searchInput, listDMYTCon]);

  const onSearchInputChange = useCallback(
    (value) => {
      setSearchInput(value);
      if (value === '') layDMYTConTheoCha(dvytChaSelected.Value);
    },
    [dvytChaSelected],
  );
  /*RENDER */
  const renderDVYTITem = useCallback(({item}) => <DVYTItem data={item} />, []);
  return (
    <ScreenComponent
      dialogLoading={dialogLoading}
      headerBack
      headerTitle="Tra cứu dịch vụ y tế"
      renderView={
        <View style={styles.container}>
          <View style={styles.filterView}>
            <View style={styles.headerView}>
              <Text style={[styles.dropDownTitle]} children="Tìm kiếm" />
            </View>
            <PickerModalView
              items={listDVYTCha}
              selectedItem={dvytChaSelected}
              containerStyle={styles.picker}
              renderSelectView={(disabled, selected, showModal) => (
                <TouchableOpacity onPress={showModal} style={styles.pickerModalView}>
                  <View style={styles.pickerModalTitle}>{dvytChaSelected.Name ? <Text>{dvytChaSelected.Name}</Text> : <Text>Tất cả</Text>}</View>
                  <Icon.SimpleLineIcons name="arrow-down" style={{marginRight: 20}} />
                </TouchableOpacity>
              )}
              onSelected={(data) => {
                if (data.Name) {
                  onChangeDMYTCha(data);
                  setDvytChaSelected(data);
                }
              }}
            />
            <SearchBar searchViewStyle={styles.searchBar} placeholder="Tên dịch vụ / Mã dịch vụ y tế" onTextChange={onSearchInputChange} onPressSearch={onPressSearch} />
          </View>
          <FlatList data={listDMYTCon} keyExtractor={(item) => item.ma_dich_vu} renderItem={renderDVYTITem} showsVerticalScrollIndicator={false} />
        </View>
      }
    />
  );
};

export const DichVuYTeScreen = memo(DichVuYTeScreenComponent, isEqual);
