import {colors} from '@app/commons/Theme';
import {Text} from '@component';
import React, {memo} from 'react';
import isEqual from 'react-fast-compare';
import {StyleSheet, View} from 'react-native';
import {NumericFormat} from 'react-number-format';

const DVYTItemComponent = (props) => {
  const {data} = props;
  return (
    <View style={styles.container}>
      {data.ten_dich_vu && (
        <View style={styles.nameView}>
          <Text children={data.ten_dich_vu} style={styles.txtName} />
        </View>
      )}
      <View style={styles.detailView}>
        <View style={styles.maView}>
          <Text style={styles.title}>Mã dịch vụ :</Text>
          <Text style={styles.value}>{data.ma_dich_vu || ''}</Text>
        </View>
        {data.ma_dich_vu_byt && (
          <View style={styles.maView}>
            <Text style={styles.title}>Mã theo BYT :</Text>
            <Text style={styles.value}>{data.ma_dich_vu_byt || ''}</Text>
          </View>
        )}
        {data.gia_vien_phi !== null && (
          <View style={styles.maView}>
            <Text style={styles.title}>Giá viện phí :</Text>
            <NumericFormat value={data.gia_vien_phi} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.value}>{value}</Text>} />
          </View>
        )}
        {data.gia_dich_vu_byt !== null && (
          <View style={styles.maView}>
            <Text style={styles.title}>Giá bảo hiểm y tế :</Text>
            <NumericFormat value={data.gia_dich_vu_byt} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.value}>{value}</Text>} />
          </View>
        )}
        {data.gia_dich_vu !== null && (
          <View style={styles.maView}>
            <Text style={styles.title}>Giá dịch vụ :</Text>
            <NumericFormat value={data.gia_dich_vu} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text style={styles.value}>{value}</Text>} />
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // borderWidth: 1,
    marginVertical: 5,
    // paddingLeft: 10,
    // paddingRight: 5,
    // paddingVertical: 10,
    backgroundColor: colors.WHITE,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
    marginHorizontal: 10,
    borderRadius: 10,
    // borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 5,
    elevation: 3,
  },
  detailView: {
    paddingLeft: 10,
    paddingVertical: 5,
    paddingRight: 5,
  },
  maView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtName: {
    fontWeight: 'bold',
    fontSize: 16,
    paddingLeft: 10,
    paddingTop: 10,
  },
  nameView: {
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
    paddingBottom: 5,
    marginBottom: 5,
    backgroundColor: colors.WHITE2,
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
  },
  title: {
    paddingVertical: 2,
    fontWeight: '300',
  },
  value: {
    color: colors.PRIMARY,
  },
});
export const DVYTItem = memo(DVYTItemComponent, isEqual);
