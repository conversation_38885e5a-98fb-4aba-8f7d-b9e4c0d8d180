import {colors} from '@app/commons/Theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    // marginHorizontal: 10,
    flex: 1,
    // zIndex: 1000,
  },
  dropDownTitle: {
    marginTop: 10,
    fontWeight: 'bold',
  },
  pickerModalView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    borderWidth: 0.5,
    backgroundColor: '#FFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 5,
    elevation: 3,
  },
  pickerModalTitle: {
    // textAlign: 'center',
    paddingVertical: 14,
    flex: 1,
    flexDirection: 'row',
    // justifyContent: 'center',
    // alignItems: 'center',
    marginLeft: 10,
  },
  searchBar: {
    marginHorizontal: 0,
    borderRadius: 10,
    borderWidth: 0.5,
    borderColor: '#000',
    // zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 5,
    elevation: 3,
  },
  picker: {
    marginBottom: 0,
  },
  filterView: {
    paddingHorizontal: 10,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
