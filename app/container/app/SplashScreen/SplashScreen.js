import R from '@R';
import {colors} from '@app/commons/Theme';
import {updateCategoryCommon} from '@app/redux/slices/CategoryCommonSlice';
import {updateCategoryImage} from '@app/redux/slices/CategoryImageSlice';
import {updateCategoryImageXeMay} from '@app/redux/slices/CategoryImageXeMaySlice';
import {updateCities} from '@app/redux/slices/CitiesSlice';
import {updateGara} from '@app/redux/slices/GaraSlice';
import {updateSwitchNavigator} from '@app/redux/slices/SwitchNavigatorSlice';
import {updateUserInfo} from '@app/redux/slices/UserSlice';
import {dimensions} from '@app/theme';
import {
  connectFirebase,
  getAllHangMucTonThat,
  getCategoryImage,
  getChiNhanhBaoHiem,
  getCitiesData,
  getDanhMucSanPhamConNguoi,
  getDanhMucSanPhamXe,
  getDsNguyenTe,
  getLevelLostCategory,
  getMqhcxVaNdktgdVaKngqVaCnbh,
  getNhieuDanhMucBoiThuongConNguoi,
  sendCurrentLocation,
} from '@app/utils/DataProvider';
import {requestCurrentLocation} from '@app/utils/LocationProvider';
import {Text} from '@component';
import {APP_NAME, ASYNC_STORAGE_KEY, LOCATION_TYPE, SCREEN_ROUTER, isIOS} from '@constant';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';
import React, {Component} from 'react';
import {Alert, Dimensions, Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import codePush from 'react-native-code-push';
import DeviceInfo from 'react-native-device-info';
import {openSettings} from 'react-native-permissions';
import * as Progress from 'react-native-progress';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';

const {width, height} = Dimensions.get('screen');
class SplashScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      update: false,
      progress: {
        receivedBytes: 0,
        totalBytes: 1,
      },
      isNeedUpdate: false,
      codePushCurrentData: null,
      countClearCodePushData: 0,
    };
  }

  async componentDidMount() {
    if (__DEV__) {
      setTimeout(() => {
        this._bootstrapAsync();
      }, 500);
    } else
      setTimeout(() => {
        // this._checkUpdate();
        this._bootstrapAsync();
      }, 500);
    // this.getCodepushInfo();
  }
  getCodepushInfo = async () => {
    try {
      let response = await codePush.getUpdateMetadata(codePush.UpdateState.RUNNING);
      this.setState({codePushCurrentData: response});
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };
  _bootstrapAsync = async () => {
    const userInfo = await AsyncStorage.getItem(ASYNC_STORAGE_KEY.USER_INFO);

    if (userInfo) {
      this.props.updateUserInfo(JSON.parse(userInfo));
      this.props.updateSwitchNavigator(SCREEN_ROUTER.MAIN);

      let responseMucDo = await getLevelLostCategory(); //DỮ LIỆU DANH MỤC MỨC ĐỘ TỔN THẤT
      if (!responseMucDo) return; //nếu có lỗi về xác thực hay gì đấy, thì k gọi các API tiếp theo
      let responseDanhMucAnh = await getCategoryImage(); //DỮ LIỆU DANH MỤC ẢNH ĐƯỢC CHỌN KHI CHỤP ẢNH
      if (!responseDanhMucAnh) return;
      await connectFirebase();
      await getAllHangMucTonThat(); //DỮ LIỆU 3000 HẠNG MỤC
      await getMqhcxVaNdktgdVaKngqVaCnbh(); //DỮ LIỆU MỐI QUAN HỆ CHỦ XE / NỘI DUNG KẾT THÚC GIÁM ĐỊNH / KIẾN NGHỊ GIẢI QUYẾT / CHI NHÁNH BẢO HIỂM
      await getDanhMucSanPhamXe(); //DỮ LIỆU DANH MỤC SẢN PHẨM XE
      await getDanhMucSanPhamConNguoi(); //DỮ LIỆU DANH MỤC SẢN PHẨM CON NGƯỜI
      await getCitiesData(); //DỮ LIỆU TỈNH THÀNH - QUẬN HUYỆN - PHƯỜNG XÃ
      await getDsNguyenTe(); //DỮ LIỆU NGUYÊN TỆ
      await getNhieuDanhMucBoiThuongConNguoi(); //DỮ LIỆU NGUYÊN TỆ
      await getChiNhanhBaoHiem(); //CHI NHÁNH BH MỚI
    } else {
      this.props.updateSwitchNavigator(SCREEN_ROUTER.AUTH);
    }
  };

  getCurrentLocation = () => {
    requestCurrentLocation(
      (position) => sendCurrentLocation(position.coords),
      (err) => {},
      LOCATION_TYPE.FINE_LOCATION,
    );
  };

  async _checkUpdate() {
    this.setState(
      {
        ...this.state,
        update: true,
      },
      async () => {
        codePush
          .checkForUpdate()
          .then((update) => {
            // console.log('update', update);
            this.forceNav = false;
            this.setState({
              ...this.state,
              update: false,
            });
            //nếu k cần update
            if (!update) {
              this.forceNav = false;
              this._bootstrapAsync();
            }
            //nếu cần update
            else {
              codePush.notifyAppReady();
              codePush.sync(
                {
                  updateDialog: null,
                  installMode: codePush.InstallMode.IMMEDIATE,
                },
                (status) => {
                  // reactotron.log(status);
                  if (
                    status == codePush.SyncStatus.DOWNLOADING_PACKAGE ||
                    status == codePush.SyncStatus.CHECKING_FOR_UPDATE ||
                    status == codePush.SyncStatus.SYNC_IN_PROGRESS ||
                    status == codePush.SyncStatus.INSTALLING_UPDATE
                  ) {
                    this.setState({
                      ...this.state,
                      update: true,
                    });
                  } else {
                    this.setState({
                      ...this.state,
                      update: false,
                    });
                  }
                  if (status == codePush.SyncStatus.UPDATE_INSTALLED) {
                    codePush.allowRestart();
                  }
                },
                (progress) => {
                  this.setState({
                    progress,
                    isNeedUpdate: true,
                    update: false,
                  });
                },
              );
            }
          })
          .catch((err) => {
            this.forceNav = false;
            // console.log(err.toString());
            codePush.allowRestart();
            this._bootstrapAsync();
          });
      },
    );
    codePush.notifyAppReady();
  }
  onPressOpenSettingApp = () => openSettings();
  onPressClearCodepushData = () => {
    if (this.state.countClearCodePushData === 4) {
      this.setState({countClearCodePushData: 0});
      Alert.alert('Thông báo', 'Bạn có muốn xoá dữ liệu cập nhật ứng dụng', [
        {
          text: 'Đồng ý',
          onPress: () => {
            codePush.clearUpdates();
            codePush.restartApp();
          },
        },
        {
          text: 'TỪ CHỐI',
          style: 'destructive',
        },
      ]);
    } else this.setState({countClearCodePushData: this.state.countClearCodePushData + 1});
  };
  /* RENDER */
  renderUpdate = () => {
    const {progress} = this.state;
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Progress.Bar progress={progress.receivedBytes / progress.totalBytes} height={height * 0.018} width={width * 0.8} color={colors.PRIMARY_LIGHT} style={styles.processBar} />
        <Text
          style={{
            textAlign: 'center',
            marginVertical: 10,
            color: colors.BLACK,
          }}
          children={`Đang tải dữ liệu ${Math.round((progress.receivedBytes / progress.totalBytes) * 100)}%`}
        />
      </View>
    );
  };
  renderVersion = () => {
    return (
      <Text style={styles.versionTxt}>
        Phiên bản
        {` ${DeviceInfo.getVersion()} - ${DeviceInfo.getBuildNumber()} - ${this.state.codePushCurrentData ? this.state.codePushCurrentData.label : ''}`}
      </Text>
    );
  };

  render() {
    const {isNeedUpdate, progress} = this.state;
    return (
      // nếu k cần update thì quay lại màn SplashScreen
      <SafeAreaView style={{flex: 1, backgroundColor: '#FFF'}}>
        <View style={styles.container}>
          <TouchableOpacity onLongPress={this._bootstrapAsync} onPress={this.onPressClearCodepushData}>
            <Image source={R.images.img_logo_xanh} style={{width: dimensions.width / 2, height: dimensions.width / 2}} resizeMode="contain" />
          </TouchableOpacity>
          <TouchableOpacity onPress={this.onPressOpenSettingApp}>
            <Text style={styles.txtTitle}>{APP_NAME}</Text>
          </TouchableOpacity>
          <Text style={styles.txtDetail}>E-Smart Claim Solution</Text>
          {this.renderVersion()}
          {isNeedUpdate && (
            <View style={styles.codepushView}>
              <Progress.Bar progress={progress.receivedBytes / progress.totalBytes} height={height * 0.018} width={width * 0.8} color={colors.PRIMARY_LIGHT} style={styles.processBar} />
              <Text
                style={styles.txtUpdate}
                children={`Hệ thống đang tải dữ liệu.\n Vui lòng để ứng dụng mở trong quá trình tải ${Math.round((progress.receivedBytes / progress.totalBytes) * 100)}%`}
              />
            </View>
          )}
          {!isIOS && (
            <View style={styles.copyrightView}>
              <Text children={`${!isIOS ? '©' : ''} ESCS ${!isIOS ? moment().year() : ''}`} style={[styles.versionTxt, {fontSize: 14, fontWeight: 'bold'}]} />
            </View>
          )}
        </View>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  codepushView: {
    position: 'absolute',
    bottom: 20,
  },
  copyrightView: {
    position: 'absolute',
    bottom: 0,
  },
  txtTitle: {
    fontSize: 25,
    fontWeight: 'bold',
    marginTop: 20,
    // lineHeight: 18,
  },
  txtUpdate: {textAlign: 'center', marginVertical: 10, color: colors.BLACK},
  txtDetail: {
    fontSize: 20,
    letterSpacing: 2,
    fontWeight: '400',
    opacity: 0.6,
    flexShrink: 1,
  },
  versionTxt: {
    textAlign: 'center',
    justifyContent: 'center',
    fontSize: 12,
    marginTop: 20,
    letterSpacing: 2,
    fontWeight: '400',
    opacity: 0.6,
  },
  processBar: {
    borderWidth: 1,
    borderColor: colors.PRIMARY,
    backgroundColor: colors.GRAY8,
    borderRadius: 10,
  },
  imgCanhDao1: {
    width: dimensions.width / 2,
    height: dimensions.width / 3,
  },
  imgCanhDaoView1: {
    position: 'absolute',
    right: 0,
    top: 0,
  },
});
const mapStateToProps = (state) => ({
  switchNavigator: state.switchNavigator.value,
});

const mapDispatchToProps = {
  updateSwitchNavigator,
  updateCities,
  updateUserInfo,
  updateCategoryCommon,
  updateCategoryImage,
  updateCategoryImageXeMay,
  updateGara,
};

export default connect(mapStateToProps, mapDispatchToProps)(SplashScreen);
