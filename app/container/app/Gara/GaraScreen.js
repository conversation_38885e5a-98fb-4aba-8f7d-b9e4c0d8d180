import {colors} from '@app/commons/Theme';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import {cloneObject} from '@app/utils/DataProvider';
import {Icon, ScreenComponent, SearchBar, Text} from '@component';
import R from '@R';
import {getDistance} from 'geolib';
import React, {createRef, memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, Image, Linking, RefreshControl, ScrollView, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import RadioGroup from 'react-native-radio-buttons-group';
import {connect} from 'react-redux';
import {GARA_CHINH_HANG_RADIO} from './Constants';
import styles from './GaraStyle';

const GaraScreenComponent = props => {
  console.log('GaraScreen');
  const [listGaraRoot, setListGaraRoot] = useState([]);
  const [listGara, setListGara] = useState([]);

  const [searchInput, setSearchInput] = useState('');
  const [currentPosition, setCurrentPosition] = useState({});
  const [refreshing, setRefreshing] = useState(false);
  const [btnViewSelected, setBtnViewSelected] = useState(0);

  const [toCoordinates, setToCoordinates] = useState(null);
  const [garaChinhHangRadio, setGaraChinhHangRadio] = useState(cloneObject(GARA_CHINH_HANG_RADIO));
  let mapRef = createRef();

  useEffect(() => {
    getGaraData();
  }, []);

  useEffect(() => {
    onChangeGaraChinhHangRadio();
  }, [garaChinhHangRadio]);

  const onChangeGaraChinhHangRadio = () => {
    setSearchInput('');
    if (garaChinhHangRadio[0].selected) setListGara([...listGaraRoot]);
    else if (garaChinhHangRadio[1].selected) {
      let listGaraFilter = listGaraRoot.filter(item => item.hop_tac === 'C');
      setListGara([...listGaraFilter]);
    } else if (garaChinhHangRadio[2].selected) {
      let listGaraFilter = listGaraRoot.filter(item => item.hop_tac === 'K');
      setListGara([...listGaraFilter]);
    }
  };

  //lấy dữ liệu gara
  const getGaraData = async () => {
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LIST_GARA, {ten: null});
      setRefreshing(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListGara(__DEV__ ? response.data_info.slice(0, 10) : response.data_info.slice(0, 150));
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    getGaraData();
  };

  const sortGaraByKmUserToGara = (listGara, setListGara) => {
    listGara.map(gara => {
      gara.userToGara = gara.toa_do ? distanceBetweenTwoCoordinate(gara.toa_do) : 999999;
    });
    let tmp = listGara.sort((a, b) => {
      return a.userToGara - b.userToGara;
    });
    setListGara([...tmp]);
  };

  const onPressSearch = async () => {
    let hopTac = garaChinhHangRadio[0].value;
    if (garaChinhHangRadio[1].selected) hopTac = garaChinhHangRadio[1].value;
    else if (garaChinhHangRadio[2].selected) hopTac = garaChinhHangRadio[2].value;
    let params = {ten: searchInput, hop_tac: hopTac};
    try {
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LIST_GARA, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setListGara([...response.data_info]);
      if (currentPosition.coords) sortGaraByKmUserToGara([...response.data_info], setListGara);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    }
  };

  const distanceBetweenTwoCoordinate = toaDo => {
    return (
      getDistance(
        //toạ độ hiện tại
        {
          latitude: currentPosition.coords.latitude,
          longitude: currentPosition.coords.longitude,
        },
        //toạ độ của gara
        {
          latitude: toaDo.split(',')[0],
          longitude: toaDo.split(',')[1],
        },
      ) / 1000
    );
  };

  const formatKM = km => {
    return km.toFixed(1) + ' km';
  };
  // Mở GOOGLE MAP CHỈ ĐƯƠNG ĐẾN GARA
  const onPressDirection = garaData => {
    // if (garaData.userToGara == 999999) return;
    // NavigationUtil.push(SCREEN_ROUTER_APP.MAP, {garaData: garaData});
    if (!garaData.toa_do) return;
    //nếu k thuộc top 5 thằng gần nhất -> lưu data để hiển thị trên MAP
    // setToAddressData(garaData);
    // if (data.index > 4) setToAddressData(itemData);
    // else setToAddressData(null);
    // console.log('onPressDirection', garaData);
    setToCoordinates({
      latitude: +garaData.toa_do.split(', ')[0],
      longitude: +garaData.toa_do.split(', ')[1],
    });
    setBtnViewSelected(1);
  };

  /**RENDER  */
  const renderGaraItem = data => {
    let item = data.item;
    return (
      <LinearGradient colors={[colors.WHITE1, colors.WHITE]} start={{x: 0, y: 0}} end={{x: 0, y: 1}} style={styles.profileItemView}>
        <View style={styles.profileItemCenterView}>
          <Text style={styles.profileTxtHoSo}>{item.ten}</Text>
          <Text style={styles.profileTxtHoSo}>{item.dia_chi}</Text>
          <View style={styles.profileItemDetail}>
            {item.dien_thoai && (
              <TouchableOpacity style={{flexDirection: 'row', alignItems: 'center'}} onPress={() => Linking.openURL(`tel:${item.dien_thoai}`)}>
                <Icon.Entypo name="phone" size={20} style={{marginRight: 5}} />
                <Text style={styles.profileTxtThoiGian}>{item.dien_thoai}</Text>
              </TouchableOpacity>
            )}

            {item.userToGara != undefined && (
              <TouchableOpacity style={{flexDirection: 'row', alignItems: 'center'}} onPress={() => onPressDirection(item)}>
                <Text style={styles.profileTxtThoiGian}>{item.userToGara == 999999 ? 'Không xác định' : formatKM(item.userToGara)}</Text>
                <Icon.Entypo name="direction" size={20} style={{marginLeft: 5}} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </LinearGradient>
    );
  };
  // RENDER HỒ SƠ CHƯA TIẾP NHẬN
  const renderGara = () => {
    return (
      <View>
        <FlatList
          data={listGara}
          renderItem={renderGaraItem}
          keyExtractor={item => item.so_id}
          ListEmptyComponent={() => (
            <View style={styles.noDataView}>
              <Image source={R.images.img_no_data} resizeMode={'contain'} style={styles.imageNoData} />
              <Text>Chưa có dữ liệu</Text>
            </View>
          )}
        />
      </View>
    );
  };

  const renderRadioInput = (title, radioButtons, onPressRadioButton) => {
    return (
      <View style={{marginLeft: 10, marginRight: 20, marginBottom: 10}}>
        <Text style={styles.dropDownTitle}>{title}</Text>
        <RadioGroup radioButtons={radioButtons} onPress={onPressRadioButton} layout={'row'} />
      </View>
    );
  };

  return (
    <ScreenComponent
      header
      screenTitle="Tra cứu gara"
      hightNavbar
      renderView={
        <View style={styles.container}>
          <View style={{marginTop: -40}}>
            <SearchBar
              value={searchInput}
              onPressSearch={onPressSearch}
              onTextChange={value => {
                if (!value.trim()) onChangeGaraChinhHangRadio();
                setSearchInput(value);
              }}
              onSubmitEditing={onPressSearch}
            />
            {renderRadioInput('', garaChinhHangRadio, data => setGaraChinhHangRadio(cloneObject(data)))}
          </View>
          <ScrollView refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />} showsVerticalScrollIndicator={false}>
            <View style={styles.contentView}>{renderGara()}</View>
          </ScrollView>
        </View>
      }
    />
  );
};

const mapStateToProps = state => ({});
const mapDispatchToProps = {};
const GaraScreenConnect = connect(mapStateToProps, mapDispatchToProps)(GaraScreenComponent);
export const GaraScreen = memo(GaraScreenConnect, isEqual);
