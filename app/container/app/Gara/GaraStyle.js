import {StyleSheet} from 'react-native';
import { colors } from '../../../commons/Theme';

export default StyleSheet.create({
  container: {flex: 1},
  contentView: {
    marginTop: 10,
  },
  profileItemView: {
    flexDirection: 'row',
    marginVertical: 5,
    paddingLeft: 20,
    paddingRight: 10,
    borderRadius: 35,
    borderColor: colors.GRAY,
    borderWidth: 0.4,
    marginHorizontal: 10,
  },
  profileItemCenterView: {
    flex: 1,
    borderBottomColor: colors.GRAY4,
    paddingVertical: 5,
    paddingRight: 5,
  },
  profileItemRightView: {
    borderBottomColor: colors.GRAY4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileItemDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  profileTimeView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileTxtHoSo: {
    marginBottom: 4,
    color: colors.BLACK,
    fontSize: 14,
  },
  profileTxtThoiGian: {
    fontSize: 12,
    color: colors.GRAY5,
  },
  profileImgClock: {
    marginRight: 5,
    width: 14,
    height: 14,
    opacity: 0.8,
  },
  selectedView: {
    position: 'absolute',

    borderRadius: 10,
    // borderWidth: 0.5,
    backgroundColor: colors.GRAY,
    // borderRadius: 5,
    // padding: 5,
  },
  btnSelectedView: {
    padding: 5,
    // borderRadius: 10,
  },
  mapView: {
    // ...StyleSheet.absoluteFillObject,
    flex: 1,
    // marginBottom: 0,
    // paddingTop: 20,
  },
  markerLabelView: {
    width: 100,
    // height: 100,
  },
  trafficView: {
    position: 'absolute',

    backgroundColor: colors.WHITE,
    borderRadius: 10,
  },
  noDataView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNoData: {
    width: width / 3,
    height: width / 3,
  },
});
