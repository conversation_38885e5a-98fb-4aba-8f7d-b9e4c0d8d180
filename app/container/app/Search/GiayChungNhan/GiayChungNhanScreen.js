import {colors, dimension} from '@app/commons/Theme';
import {default as AxiosConfig, default as axiosConfig} from '@app/services/axiosConfig';
import {ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, ScrollView, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import styles from './GiayChungNhanStyle';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';

const profileHeaderTitle = ['Thông tin giấy chứng nhận', 'Tình trạng thanh toán phí', '<PERSON><PERSON><PERSON> hình nghiệp vụ tham gia'];
const iconHeader = ['list-ol', 'money', 'eercast', 'list-ol', 'file-text-o', 'user-circle-o', 'list-ol', 'legal', 'car', 'empire', 'random', 'tasks', 'money'];

const GiayChungNhanScreenComponent = ({route}) => {
  console.log('GiayChungNhanScreenComponent');
  const {giayChungNhan, loaiHinhNghiepVu, prevScreen, prevScreenIsHoSoDiaBan, profileData} = route.params;
  const [gcnChiTiet, setGcnChiTiet] = useState(null);
  const [tinhTrangThanhToan, setTinhTrangThanhToan] = useState([]);
  const [dialogLoading, setDialogLoading] = useState(false);

  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    await layChiTietGCNXeDoiTac();
  };
  //API xem tình trạng thanh toán phí
  const getDataThanhToanPhi = async (outValue) => {
    const {so_id_hd} = outValue;
    try {
      let params = {
        ma_chi_nhanh_ql: giayChungNhan.ma_chi_nhanh || '',
        so_id_hd,
        nv: giayChungNhan.nv || giayChungNhan.nghiep_vu,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XEM_TINH_TRANG_THANH_TOAN_PHI, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setTinhTrangThanhToan(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const layChiTietGCNXeDoiTac = async () => {
    setDialogLoading(true);
    try {
      let params = {
        ma_doi_tac: axiosConfig.CONFIG_SERVER.MA_DOI_TAC,
        ma_chi_nhanh: giayChungNhan.ma_chi_nhanh,
        nv: giayChungNhan.nv,
        so_id_hd: giayChungNhan.so_id_hdong,
        so_id_dt: giayChungNhan.so_id_gcn,
        so_gcn: giayChungNhan.so_gcn,
      };
      console.log('so_gcn: ho_so.gcn, ', params);

      let response = await PartnerEndpoint.xemGCNXeDoiTac(AxiosConfig.ACTION_CODE.XEM_GCN_XE_DOI_TAC, params);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setGcnChiTiet(response.data_info);
      if (response?.out_value?.so_id_hd) getDataThanhToanPhi(response.out_value);
    } catch (error) {
      Alert.alert('Thông báo', error.message);
    } finally {
      setDialogLoading(false);
    }
  };

  const onPressTichHopLaiGCN = async () => {
    setDialogLoading(true);
    try {
      let params = {
        ma_doi_tac: axiosConfig.CONFIG_SERVER.MA_DOI_TAC,
        ma_chi_nhanh: giayChungNhan.ma_chi_nhanh,
        nv: giayChungNhan.nv,
        so_id_hd: giayChungNhan.so_id_hdong,
        so_id_dt: giayChungNhan.so_id_gcn,
        tich_hop_lai: 'C',
      };
      let response = await PartnerEndpoint.xemGCNXeDoiTac(AxiosConfig.ACTION_CODE.XEM_GCN_XE_DOI_TAC, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setGcnChiTiet(response.data_info);
      FlashMessageHelper.showFlashMessage('Thành công', 'Tích hợp lại giấy chứng nhận thành công', 'success');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  /** RENDER */
  const renderThongTinGCN = () => {
    if (!gcnChiTiet || !gcnChiTiet.ho_so) return;

    const renderLabel = (title, value) => {
      return (
        <View style={styles.inforView}>
          <Text style={styles.txtTitle}>{title}</Text>
          <Text style={styles.txtDetail}>{value}</Text>
        </View>
      );
    };
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View flex={1}>
          {renderProfileInformationHeader(profileHeaderTitle[0])}
          <View flexDirection="row">
            {renderLabel('Tên khách hàng', gcnChiTiet.ho_so.ten_kh)}
            {renderLabel('Biển xe', gcnChiTiet.ho_so.bien_xe)}
          </View>
          <View flexDirection="row">
            {renderLabel('Chủ xe', gcnChiTiet.ho_so.ten_chu_xe)}
            {renderLabel('Hãng xe - Hiệu xe', gcnChiTiet.ho_so.hang_xe_ten + ' - ' + gcnChiTiet.ho_so.hieu_xe)}
          </View>
          <View flexDirection="row">
            {renderLabel('Số khung', gcnChiTiet.ho_so.so_khung)}
            {renderLabel('Số máy', gcnChiTiet.ho_so.so_may)}
          </View>
          <View flexDirection="row">
            {renderLabel('Năm sản xuất', gcnChiTiet.ho_so.nam_sx)}
            {renderLabel('Giá trị xe', gcnChiTiet.ho_so.gia_tri)}
          </View>
          <View flexDirection="row">
            {renderLabel('Loại xe', gcnChiTiet.ho_so.ten_loai_xe)}
            {renderLabel('Mục đích sử dụng', gcnChiTiet.ho_so.muc_dich_hthi)}
          </View>
          <View flexDirection="row">
            {renderLabel('Số chỗ - Trọng tải', gcnChiTiet.ho_so.so_cho + ' - ' + gcnChiTiet.ho_so.trong_tai_hthi)}
            {renderLabel('Số bảng kê', gcnChiTiet.ho_so.so_don)}
          </View>
          <View flexDirection="row">
            {renderLabel('Số giấy chứng nhận', gcnChiTiet.ho_so.gcn)}
            {renderLabel('Hiệu lực bảo hiểm', gcnChiTiet.ho_so.hieu_luc)}
          </View>
          <View flexDirection="row">
            {renderLabel('Số hợp đồng', gcnChiTiet.ho_so.so_hd)}
            {renderLabel('Hiệu lực hợp đồng', gcnChiTiet.ho_so.hieu_luc_hd)}
          </View>

          <View flexDirection="row">
            {renderLabel('Tổng phí', gcnChiTiet.ho_so.tong_phi_hthi)}
            {renderLabel('Loại hình bảo hiểm', gcnChiTiet.ho_so.loai_bh)}
          </View>

          <View flexDirection="row">
            {renderLabel('Mã chương trình BH', gcnChiTiet.ho_so.ctrinh_bh || '')}
            {renderLabel('Tên chương trình BH', gcnChiTiet.ho_so.ctrinh_bh_ten || '')}
          </View>
          <View flexDirection="row">
            {renderLabel('Tên đơn vị gốc', gcnChiTiet.ho_so.ten_dvi_goc || '')}
            {renderLabel('MST đơn vị gốc', gcnChiTiet.ho_so.mst_dvi_goc || '')}
          </View>
          {renderLabel('Địa chỉ đơn vị gốc', gcnChiTiet.ho_so.dchi_dvi_goc || '')}
        </View>
      </View>
    );
  };
  const renderProfileInformationHeader = (title, data) => {
    let indexIcon = profileHeaderTitle.findIndex((item) => item == title);
    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <View style={[styles.inforHeaderView]}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View flexDirection="row">
              <Icon.FontAwesome name={iconHeader[indexIcon]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>{title + ' ' + dataLength}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderTinhTrangThanhToanPhi = () => {
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View flex={1}>
          {renderProfileInformationHeader(profileHeaderTitle[1])}
          {tinhTrangThanhToan.map((itemThanhToanPhi, index) => (
            <View key={index}>
              <View style={[styles.inforView, styles.rowThanhToan]}>
                <Text style={styles.txtDetail}>Số tiền</Text>
                <NumericFormat value={itemThanhToanPhi?.so_tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
              </View>
              <View style={[styles.inforView, styles.rowThanhToan]}>
                <Text style={styles.txtDetail}>Số tiền đã thanh toán</Text>
                <NumericFormat value={itemThanhToanPhi?.so_tien_da_tt || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
              </View>
              <View style={[styles.inforView, styles.rowThanhToan]}>
                <Text style={styles.txtDetail}>Ghi chú</Text>
                <Text
                  style={[styles.txtDetail, {maxWidth: dimensions.width / 2, textAlign: 'right', color: itemThanhToanPhi?.style === 'text-success' ? colors.GREEN : colors.RED1, fontWeight: '700'}]}>
                  {itemThanhToanPhi?.ghi_chu || ''}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };
  const renderLoaiHinhNVThamGia = () => {
    // if (tinhTrangThanhToan.length == 0) return;
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View flex={1}>
          {renderProfileInformationHeader(profileHeaderTitle[2], loaiHinhNghiepVu)}
          {renderLoaiHinhNghiepVu()}
          {renderTongTien()}
        </View>
      </View>
    );
  };
  const renderLoaiHinhNghiepVuItem = (data, extraData) => {
    let item = data.item;
    return (
      <View style={[styles.lhnvItemView]}>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Sản phẩm" style={styles.txtDetail} />
          <Text children={item.ten} style={styles.txtDetail} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Tiền bảo hiểm" style={styles.txtDetail} />
          <NumericFormat value={item.tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Khấu trừ" style={styles.txtDetail} />
          <Text children={item.ktru == 'C' ? 'Có' : 'Không'} style={styles.txtDetail} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Mức khấu trừ" style={styles.txtDetail} />
          <NumericFormat value={item.mien_thuong || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Điều khoản bổ sung" style={styles.txtDetail} />
          <Text children={item.dkbs?.split(',').join(', ') || ''} style={[styles.txtDetail, {maxWidth: dimension.width / 2, textAlign: 'right'}]} />
        </View>
      </View>
    );
  };
  const renderLoaiHinhNghiepVu = () => (
    <FlatList data={loaiHinhNghiepVu} keyExtractor={(_, index) => index.toString()} renderItem={(item) => renderLoaiHinhNghiepVuItem(item, {loaiHinhNghiepVu: loaiHinhNghiepVu})} />
  );
  const renderTongTien = () => {
    let tongTienBaoHiem = 0,
      tongMienPhuong = 0;
    loaiHinhNghiepVu.map((item) => {
      tongTienBaoHiem += item.tien;
      tongMienPhuong += item.mien_thuong;
    });
    return (
      <View style={[styles.lhnvItemView]}>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Tổng tiền bảo hiểm" style={[styles.lhnvTongTienTitle, {paddingHorizontal: spacing.small}]} />
          <NumericFormat value={tongTienBaoHiem} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvTongTienValue} />} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Tổng tiền khấu trừ/vụ" style={[styles.lhnvTongTienTitle, {paddingHorizontal: spacing.small}]} />
          <NumericFormat value={tongMienPhuong} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvTongTienValue} />} />
        </View>
      </View>
    );
  };
  const showNutTichHopLaiGCN = !prevScreenIsHoSoDiaBan && prevScreen === SCREEN_ROUTER_APP.PROFILE_ASSESSMENT && profileData?.ho_so.so_hd && profileData?.ho_so.so_hd !== 'HD_KHONG_XAC_DINH';
  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle={'Giấy chứng nhận'}
      renderView={
        <View style={styles.container}>
          <ScrollView showsVerticalScrollIndicator={false}>
            {renderThongTinGCN()}
            {renderTinhTrangThanhToanPhi()}
            {loaiHinhNghiepVu && renderLoaiHinhNVThamGia()}
          </ScrollView>
        </View>
      }
      footer={showNutTichHopLaiGCN && <ButtonLinear title="Tích hợp lại GCN" linearStyle={{marginHorizontal: spacing.default, marginTop: spacing.default}} onPress={onPressTichHopLaiGCN} />}
    />
  );
};

export const GiayChungNhanScreen = memo(GiayChungNhanScreenComponent, isEqual);
