import { colors } from '@app/commons/Theme';
import { scale, spacing, vScale } from '@app/theme';
import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
  },
  centerView: {
    marginTop: vScale(spacing.small),
    flex: 1,
  },
  pickerModalView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    borderWidth: 0.5,
  },
  pickerModalTitle: {
    paddingVertical: vScale(spacing.small),
    flex: 1,
    flexDirection: 'row',
    marginLeft: scale(spacing.small),
  },

  searchView: {
    marginVertical: vScale(spacing.small),
    marginHorizontal: scale(spacing.small),
  },

  btnLoginView: {
    flexDirection: 'row',
    backgroundColor: colors.PRIMARY,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing.medium,
  },
  txtBtnLogin: {
    // paddingRight: scale(spacing.tiny),
    // paddingVertical: vScale(spacing.small),
    textTransform: 'uppercase',
    color: colors.WHITE,
    flex: 1
  },
  txtEmptyData: {
    marginTop: vScale(spacing.small),
    marginHorizontal: scale(spacing.medium),
    backgroundColor: colors.WHITE8,
    borderRadius: 10,
    paddingVertical: vScale(spacing.small),
    paddingHorizontal: scale(spacing.small),
  },
  declaredProfileItemView: {
    flexDirection: 'row',
    paddingVertical: vScale(spacing.small),
    borderBottomWidth: 1,
    borderColor: colors.GRAY,
    marginLeft: scale(spacing.small),
  },
  profileNumber: {},
  txtBienKiemSoatDetail: {
    color: colors.BLUE5,
    fontWeight: '600',
  },
  txtBienKiemSoat: {
    color: colors.GRAY6,
    paddingVertical: vScale(spacing.tiny),
    flex: 1,
    // borderWidth: 1,
  },
  tabBarView: {
    marginVertical: vScale(spacing.smaller),
  },
});
