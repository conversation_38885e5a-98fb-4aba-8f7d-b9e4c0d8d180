import {colors} from '@app/commons/Theme';
import {FontSize, dimensions, spacing, vScale} from '@app/theme';
import {StyleSheet} from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
    marginBottom: spacing.small,
  },
  inforView: {
    flex: 1,
    paddingHorizontal: spacing.small,
    paddingVertical: vScale(spacing.smaller),
    borderColor: colors.GRAY4,
    borderBottomWidth: 0.5,
    backgroundColor: colors.WHITE,
  },
  inforHeaderView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.WHITE5,
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    width: dimensions.width - 20,
  },
  txtTitle: {
    marginBottom: spacing.tiny,
    fontSize: FontSize.size12,
  },
  txtDetail: {
    color: colors.GRAY6,
  },
  headerCollap: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: spacing.small,
    backgroundColor: colors.WHITE5,
    justifyContent: 'center',
  },
  iconBtnTopLeftView: {
    marginRight: spacing.small,
    // borderWidth: 1,
    alignSelf: 'center',
  },
  iconBtnTopRightView: {
    marginHorizontal: spacing.small,
    alignSelf: 'center',
  },
  centerView: {
    marginTop: spacing.tiny,
    // marginBottom: 50,
    flex: 1,
  },
  titleBangView: {
    flexDirection: 'row',
    borderColor: colors.GRAY1,
  },
  titleBang: {
    flex: 1,
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 14,
    paddingVertical: spacing.small,
  },
  itemThanhToanView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: spacing.small,
    borderRadius: 20,
  },
  txtThanhToan: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.tiny,
  },
  txtTongCong: {
    fontWeight: 'bold',
    fontSize: 14,
    flex: 2,
    textAlign: 'center',
    paddingVertical: spacing.small,
  },
  valueTongCong: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: spacing.small,
  },
  tongCongView: {
    flexDirection: 'row',

    borderColor: colors.GRAY1,
    paddingRight: spacing.small,
  },
  rowThanhToan: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: spacing.small,
  },
  lhvnRow: {
    flexDirection: 'row',
  },
  lhnvValue: {
    flex: 1,
    fontWeight: 'bold',
    textAlign: 'right',
  },
  lhnvTitle: {
    flex: 1,
  },
  lhnvItemView: {},
  lhnvTongTienValue: {
    fontSize: 16,
    flex: 1,
    textAlign: 'right',
    color: colors.PRIMARY,
  },
  lhnvTongTienTitle: {
    fontSize: 16,
    flex: 1,
    color: colors.PRIMARY,
  },
  lhnvStyles: {
    paddingRight: 0,
  },
});
