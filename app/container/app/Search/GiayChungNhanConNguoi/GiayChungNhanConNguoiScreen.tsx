import {colors, dimension} from '@app/commons/Theme';
import {default as AxiosConfig, default as axiosConfig} from '@app/services/axiosConfig';
import {ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {dimensions, spacing} from '@app/theme';
import {ButtonLinear, Icon, ScreenComponent, Text} from '@component';
import React, {memo, useEffect, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, ScrollView, View} from 'react-native';
import {NumericFormat} from 'react-number-format';
import styles from './GiayChungNhanConNguoiStyle';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';
import {FlashMessageHelper} from '@app/utils/FlashMessageHelper';
import moment from 'moment';

const profileHeaderTitle = ['Thông tin giấy chứng nhận', 'Tình trạng thanh toán phí', '<PERSON>ại hình nghiệp vụ tham gia'];
const iconHeader = ['list-ol', 'money', 'eercast', 'list-ol', 'file-text-o', 'user-circle-o', 'list-ol', 'legal', 'car', 'empire', 'random', 'tasks', 'money'];

const GiayChungNhanConNguoiScreenComponent = ({route}) => {
  console.log('GiayChungNhanConNguoiScreenComponent');
  const {giayChungNhan, loaiHinhNghiepVu, prevScreen, prevScreenIsHoSoDiaBan, profileData} = route.params;
  const [gcnChiTiet, setGcnChiTiet] = useState(null);
  const [tinhTrangThanhToan, setTinhTrangThanhToan] = useState([]);
  const [dialogLoading, setDialogLoading] = useState(false);

  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    await layChiTietGCNConNguoi();
    getDataThanhToanPhi();
  };
  //API xem tình trạng thanh toán phí
  const getDataThanhToanPhi = async () => {
    try {
      let params = {
        ma_chi_nhanh_ql: giayChungNhan.ma_chi_nhanh || '',
        so_id_hd: giayChungNhan.so_id_hdong || '',
        nv: giayChungNhan.nv,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.XEM_TINH_TRANG_THANH_TOAN_PHI, params);
      console.log('response', response);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setTinhTrangThanhToan(response.data_info);
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const layChiTietGCNConNguoi = async () => {
    try {
      setDialogLoading(true);
      let params = {
        so_id_hd: giayChungNhan.so_id_hd,
        so_id_gcn: giayChungNhan.so_id_dt,
        ma_doi_tac_ql: giayChungNhan.ma_doi_tac_ql,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.LAY_CHI_TIET_GCN_CON_NGUOI, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setGcnChiTiet({ho_so: response.data_info.ho_so});
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const onPressTichHopLaiGCN = async () => {
    setDialogLoading(true);
    try {
      let params = {
        ma_doi_tac: axiosConfig.CONFIG_SERVER.MA_DOI_TAC,
        ma_chi_nhanh: giayChungNhan.ma_chi_nhanh,
        nv: giayChungNhan.nv,
        so_id_hd: giayChungNhan.so_id_hdong,
        so_id_dt: giayChungNhan.so_id_gcn,
        tich_hop_lai: 'C',
      };
      let response = await PartnerEndpoint.xemGCNXeDoiTac(AxiosConfig.ACTION_CODE.XEM_GCN_XE_DOI_TAC, params);
      setDialogLoading(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setGcnChiTiet(response.data_info);
      FlashMessageHelper.showFlashMessage('Thành công', 'Tích hợp lại giấy chứng nhận thành công', 'success');
    } catch (error) {
      setDialogLoading(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  /** RENDER */
  const renderThongTinGCN = () => {
    if (!gcnChiTiet || !gcnChiTiet.ho_so) return;

    const renderLabel = (title, value) => {
      return (
        <View style={styles.inforView}>
          <Text style={styles.txtTitle}>{title}</Text>
          <Text style={styles.txtDetail}>{value}</Text>
        </View>
      );
    };
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View style={{flex: 1}}>
          {renderProfileInformationHeader(profileHeaderTitle[0])}
          <View style={{flexDirection: 'row'}}>
            {renderLabel('Tên khách hàng', gcnChiTiet.ho_so.ten_kh)}
            {renderLabel('Ngày sinh', gcnChiTiet.ho_so.ngay_sinh)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Điện thoại', gcnChiTiet.ho_so.d_thoai)}
            {renderLabel('Email', gcnChiTiet.ho_so.email)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Số GCN', gcnChiTiet.ho_so.gcn)}
            {renderLabel('Gói bảo hiểm', gcnChiTiet.ho_so.goi_bh)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Chi nhánh công tác', gcnChiTiet.ho_so.cnhanh_ctac)}
            {renderLabel('Chức vụ', gcnChiTiet.ho_so.cvu_ctac)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Đơn vị môi giới', gcnChiTiet.ho_so.ten_doi_tac_ql)}
            {renderLabel('Đơn vị xử lý bồi thường', gcnChiTiet.ho_so.ten_doi_tac)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Cán bộ cấp đơn', gcnChiTiet.ho_so.ten_doi_tac_ql)}
            {renderLabel(
              'Hiệu lực bảo hiểm',
              (moment(gcnChiTiet.ho_so.ngay_hl, 'YYYYMMDD').isValid() ? moment(gcnChiTiet.ho_so.ngay_hl, 'YYYYMMDD').format('DD/MM/YYYY') : '') +
                ' - ' +
                (moment(gcnChiTiet.ho_so.ngay_kt, 'YYYYMMDD').isValid() ? moment(gcnChiTiet.ho_so.ngay_kt, 'YYYYMMDD').format('DD/MM/YYYY') : ''),
            )}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Tên người tham gia', gcnChiTiet.ho_so.ten_nguoi_tham_gia)}
            {renderLabel('Giới tính', gcnChiTiet.ho_so.gioi_tinh)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Số chứng minh thư', gcnChiTiet.ho_so.so_cmt)}
            {renderLabel('Địa chỉ', gcnChiTiet.ho_so.dchi)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Sản phẩm', gcnChiTiet.ho_so.lh_nv)}
            {renderLabel('Đơn vị công tác', gcnChiTiet.ho_so.dvi_ctac)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Mã nhân viên', gcnChiTiet.ho_so.manv_ctac)}
            {renderLabel('Phòng ban', gcnChiTiet.ho_so.pban_ctac)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('Mã nhân viên', gcnChiTiet.ho_so.manv_ctac)}
            {renderLabel('Phòng ban', gcnChiTiet.ho_so.pban_ctac)}
          </View>

          <View style={{flexDirection: 'row'}}>
            {renderLabel('SĐT đ.vi môi giới', gcnChiTiet.ho_so.d_thoai_ql || '')}
            {renderLabel('SĐT đ.vị xử lý bồi thường', gcnChiTiet.ho_so.d_thoai_xl || '')}
          </View>
          {renderLabel('SĐT cán bộ cấp đơn', gcnChiTiet.ho_so.d_thoai_nsd || '')}
        </View>
      </View>
    );
  };
  const renderProfileInformationHeader = (title, data?) => {
    let indexIcon = profileHeaderTitle.findIndex((item) => item == title);
    let dataLength = data ? '(' + data.length + ')' : '';
    return (
      <View>
        <View style={[styles.inforHeaderView]}>
          <View activeOpacity={1} style={[styles.headerCollap]}>
            <View style={{flexDirection: 'row'}}>
              <Icon.FontAwesome name={iconHeader[indexIcon]} size={15} style={styles.iconBtnTopLeftView} />
              <Text>{title + ' ' + dataLength}</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const renderTinhTrangThanhToanPhi = () => {
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View style={{flex: 1}}>
          {renderProfileInformationHeader(profileHeaderTitle[1])}
          {tinhTrangThanhToan.map((itemThanhToanPhi, index) => (
            <View key={index}>
              <View style={[styles.inforView, styles.rowThanhToan]}>
                <Text style={styles.txtDetail}>Số tiền</Text>
                <NumericFormat value={itemThanhToanPhi?.so_tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
              </View>
              <View style={[styles.inforView, styles.rowThanhToan]}>
                <Text style={styles.txtDetail}>Số tiền đã thanh toán</Text>
                <NumericFormat value={itemThanhToanPhi?.so_tien_da_tt || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
              </View>
              <View style={[styles.inforView, styles.rowThanhToan]}>
                <Text style={styles.txtDetail}>Ghi chú</Text>
                <Text
                  style={[styles.txtDetail, {maxWidth: dimensions.width / 2, textAlign: 'right', color: itemThanhToanPhi?.style === 'text-success' ? colors.GREEN : colors.RED1, fontWeight: '700'}]}>
                  {itemThanhToanPhi?.ghi_chu || ''}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };
  const renderLoaiHinhNVThamGia = () => {
    // if (tinhTrangThanhToan.length == 0) return;
    return (
      <View style={[styles.centerView]}>
        {/* Thông tin chung */}
        <View flex={1}>
          {renderProfileInformationHeader(profileHeaderTitle[2], loaiHinhNghiepVu)}
          {renderLoaiHinhNghiepVu()}
          {renderTongTien()}
        </View>
      </View>
    );
  };
  const renderLoaiHinhNghiepVuItem = (data, extraData) => {
    let item = data.item;
    return (
      <View style={[styles.lhnvItemView]}>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Sản phẩm" style={styles.txtDetail} />
          <Text children={item.ten} style={styles.txtDetail} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Tiền bảo hiểm" style={styles.txtDetail} />
          <NumericFormat value={item.tien || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Khấu trừ" style={styles.txtDetail} />
          <Text children={item.ktru == 'C' ? 'Có' : 'Không'} style={styles.txtDetail} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Mức khấu trừ" style={styles.txtDetail} />
          <NumericFormat value={item.mien_thuong || 0} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.txtDetail} />} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Điều khoản bổ sung" style={styles.txtDetail} />
          <Text children={item.dkbs?.split(',').join(', ') || ''} style={[styles.txtDetail, {maxWidth: dimension.width / 2, textAlign: 'right'}]} />
        </View>
      </View>
    );
  };
  const renderLoaiHinhNghiepVu = () => (
    <FlatList data={loaiHinhNghiepVu} keyExtractor={(_, index) => index.toString()} renderItem={(item) => renderLoaiHinhNghiepVuItem(item, {loaiHinhNghiepVu: loaiHinhNghiepVu})} />
  );
  const renderTongTien = () => {
    let tongTienBaoHiem = 0,
      tongMienPhuong = 0;
    loaiHinhNghiepVu.map((item) => {
      tongTienBaoHiem += item.tien;
      tongMienPhuong += item.mien_thuong;
    });
    return (
      <View style={[styles.lhnvItemView]}>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Tổng tiền bảo hiểm" style={[styles.lhnvTongTienTitle, {paddingHorizontal: spacing.small}]} />
          <NumericFormat value={tongTienBaoHiem} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvTongTienValue} />} />
        </View>
        <View style={[styles.inforView, styles.rowThanhToan]}>
          <Text children="Tổng tiền khấu trừ/vụ" style={[styles.lhnvTongTienTitle, {paddingHorizontal: spacing.small}]} />
          <NumericFormat value={tongMienPhuong} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value} style={styles.lhnvTongTienValue} />} />
        </View>
      </View>
    );
  };
  // const showNutTichHopLaiGCN = !prevScreenIsHoSoDiaBan && prevScreen === SCREEN_ROUTER_APP.PROFILE_ASSESSMENT && profileData?.ho_so.so_hd && profileData?.ho_so.so_hd !== 'HD_KHONG_XAC_DINH';
  return (
    <ScreenComponent
      headerBack
      dialogLoading={dialogLoading}
      headerTitle={'Giấy chứng nhận con người'}
      renderView={
        <View style={styles.container}>
          <ScrollView showsVerticalScrollIndicator={false}>
            {renderThongTinGCN()}
            {renderTinhTrangThanhToanPhi()}
            {/* {loaiHinhNghiepVu && renderLoaiHinhNVThamGia()} */}
          </ScrollView>
        </View>
      }
      // footer={showNutTichHopLaiGCN && <ButtonLinear title="Tích hợp lại GCN" linearStyle={{marginHorizontal: spacing.default, marginTop: spacing.default}} onPress={onPressTichHopLaiGCN} />}
    />
  );
};

export const GiayChungNhanConNguoiScreen = memo(GiayChungNhanConNguoiScreenComponent, isEqual);
