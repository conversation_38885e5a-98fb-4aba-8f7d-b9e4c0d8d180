import {colors} from '@app/commons/Theme';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {selectCategoryCommon, selectChiNhanhBaoHiemDangCay} from '@app/redux/slices/CategoryCommonSlice';
import {selectUser} from '@app/redux/slices/UserSlice';
import AxiosConfig from '@app/services/axiosConfig';
import {ESmartClaimEndpoint, PartnerEndpoint} from '@app/services/endPoints';
import {scale, spacing} from '@app/theme';
import {ButtonLinear, CustomTabBar, DateTimePickerComponent, Icon, ModalChiNhanhTheoDangCay, ModalChiNhanhTheoDangPhang, ScreenComponent, Text, TextInputOutlined} from '@component';
import {SCREEN_ROUTER_APP} from '@constant';
import moment from 'moment';
import React, {memo, useEffect, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ActivityIndicator, Alert, FlatList, Keyboard, TouchableOpacity, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import RadioGroup from 'react-native-radio-buttons-group';
import {useSelector} from 'react-redux';
import {ModalListGCNConNguoi} from './components';
import styles from './SearchStyle';
const MA_CHI_NHANH_KHONG_AUTO_FILL = ['TCT', 'KV01', 'KV02'];

const SearchScreenComponent = (props) => {
  console.log('SearchScreenComponent');
  const chiNhanhBaoHiemXe = useSelector(selectChiNhanhBaoHiemDangCay);
  const chiNhanhBaoHiemDangCay = useSelector(selectChiNhanhBaoHiemDangCay);
  const userInfo = useSelector(selectUser);
  const scrollViewRef = useRef(null);
  const tabViewRef = useRef(null);
  const [btnTabActive, setBtnTabActive] = useState(0);
  /* input con người*/
  const [searchPeopleRadio, setSearchPeopleRadio] = useState([
    {
      id: '1', // acts as primary key, should be unique and non-empty string
      label: 'Họ tên',
      value: 1,
      containerStyle: {flex: 1},
      selected: true,
      // color: 'rgba(0,	40,	127,0.7)',
      // borderColor: 'rgba(0,	40,	127,0.7)',
      size: 20,
    },
    {
      id: '2',
      label: 'Giấy chứng nhận',
      value: 2,
      containerStyle: {flex: 1},
      selected: false,
      // color: 'rgba(0,	40,	127,0.7)',
      // borderColor: 'rgba(0,	40,	127,0.7)',
      size: 20,
    },
  ]);
  const [inputHoTenOrGCN, setInputHoTenOrGCN] = useState('');
  const [inputCMT, setInputCMT] = useState('');
  const [emptyDataConNguoi, setEmptyDataConNguoi] = useState(false);
  const [inputErrConNguoi, setInputErrConNguoi] = useState(['', '', '']);
  const [productSelected, setProductSelected] = useState({});
  const [listGcnConNguoi, setListGcnConNguoi] = useState([]);
  const [listProduct, setListProduct] = useState([]); //danh mục sản phẩm con người

  /**input xe */
  const [searchXeRadio, setSearchXeRadio] = useState([
    {
      id: '1', // acts as primary key, should be unique and non-empty string
      label: 'Biển xe',
      value: 1,
      containerStyle: {flex: 1},
      selected: true,
      // color: 'rgba(0,	40,	127,0.7)',
      // borderColor: 'rgba(0,	40,	127,0.7)',
      size: 20,
    },
    {
      id: '2',
      label: 'Giấy chứng nhận',
      value: 2,
      containerStyle: {flex: 1},
      selected: false,
      // color: 'rgba(0,	40,	127,0.7)',
      // borderColor: 'rgba(0,	40,	127,0.7)',
      size: 20,
    },
  ]);
  const [inputBienXeOrGCN, setInputBienXeOrGCN] = useState('');
  const [inputTenOrSDT, setInputTenOrSDT] = useState('');
  const [inputErrXe, setInputErrXe] = useState(['', '', '', '', '', '', '', '']);
  const [listGcnXe, setListGCNXe] = useState([]);
  const [emptyDataXe, setEmptyDataXe] = useState(false);
  // const [toggleNgayTonThat, setToggleNgayTonThat] = useState(false);
  // const [ngayTonThat, setNgayTonThat] = useState(new Date());
  const [soHopDong, setSoHopDong] = useState('');
  const [soGCN, setSoGCN] = useState('');
  const [bienSoXe, setBienSoXe] = useState('');
  const [soKhung, setSoKhung] = useState('');
  const [soMay, setSoMay] = useState('');
  const [loadingGcnXe, setLoadingGcnXe] = useState(false);
  const [donViCapDon, setDonViCapDon] = useState('');

  //con người
  const [donViCapDonNg, setDonViCapDonNg] = useState('');
  const [dienThoai, setDienThoai] = useState('');
  const [soHopDongConNguoi, setSoHopDongConNguoi] = useState('');
  const [tenNguoiDuocBaoHiem, setTenNguoiDuocBaoHiem] = useState('');
  const [soGCNConNguoi, setSoGCNConNguoi] = useState('');
  const [ngaySinh, setNgaySinh] = useState('');
  const [toggleNgaySinh, setToggleNgaySinh] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);
  const [listMaDonViXuLySelected, setListMaDonViXuLySelected] = useState([]);
  const [chiNhanhBaoHiem, setChiNhanhBaoHiem] = useState([]);
  const [total, setTotal] = useState(20);
  const [current, setCurrent] = useState(1);

  let refModalDonViXuLy = useRef(null);
  let refModalListGCNConNguoi = useRef(null);
  // let refModalChonSanPham = useRef(null);
  let refModalChiNhanh = useRef(null);
  const categoryCommon = useSelector(selectCategoryCommon);

  useEffect(() => {
    initSanPhamConNguoi();
    initDonViXuLy();
  }, []);

  useEffect(() => {
    refModalDonViXuLy?.current?.setData(chiNhanhBaoHiem);
  }, [chiNhanhBaoHiem]);

  // const initDonViXuLy = () => {
  //   refModalDonViXuLy?.current?.setData(chiNhanhBaoHiem);
  // };

  const initDonViXuLy = () => {
    try {
      // let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay.filter((item) => item.quan_ly === 1); //list chi nhánh mà nó quản lý
      let chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemDangCay;
      chiNhanhBaoHiemQuanLy = chiNhanhBaoHiemQuanLy.map((item) => {
        return {
          ...item,
          listCon: [],
          isExpand: true,
          isCheck: false, //bỏ check or check
          hasChildCheck: false, //list chi nhánh cha, có child checked
          isShow: true,
        };
      });
      let chiNhanhBaoHiemCha = chiNhanhBaoHiemQuanLy.filter((item) => !item.ma_cap_tren); //lấy ra thằng cha to nhất
      for (let i = 0; i < chiNhanhBaoHiemCha.length; i++) {
        let listChiNhanhBHConLai = chiNhanhBaoHiemQuanLy.filter((item) => item.ma_cap_tren);
        chiNhanhBaoHiemCha[i].listCon = deQuyLayChiNhanhCon(chiNhanhBaoHiemCha[i], listChiNhanhBHConLai);
      }
      setChiNhanhBaoHiem(chiNhanhBaoHiemCha);
    } catch (error) {
      console.log(error.message);
    }
  };
  const deQuyLayChiNhanhCon = (chiNhanhCha, listTimKiem) => {
    try {
      let listConFilter = listTimKiem.filter((item) => item.ma_cap_tren === chiNhanhCha.ma_chi_nhanh);
      let listConLai = listTimKiem.filter((item) => item.ma_cap_tren !== chiNhanhCha.ma_chi_nhanh);
      if (listConFilter.length > 0 && listConLai.length === 0) return listConFilter;
      if (listConLai.length === 0) return [];
      else {
        for (let i = 0; i < listConFilter.length; i++) listConFilter[i].listCon = deQuyLayChiNhanhCon(listConFilter[i], listConLai);
        return listConFilter;
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (userInfo?.nguoi_dung) {
      if (!MA_CHI_NHANH_KHONG_AUTO_FILL.includes(userInfo?.nguoi_dung?.ma_chi_nhanh)) setDonViCapDon(userInfo.nguoi_dung.ma_chi_nhanh);
    }
  }, [userInfo]);

  const initSanPhamConNguoi = () => {
    let sanPhamConNguoiTmp = JSON.parse(JSON.stringify(categoryCommon.danhMucSanPhamConNguoi));
    sanPhamConNguoiTmp.map((item) => {
      item.Value = item.ma;
      item.Code = item.ma;
      item.Name = item.ten;
    });
    setListProduct(sanPhamConNguoiTmp);
  };

  const onChangeHoTenOrGCNInput = (value) => {
    let inputErrTmp = inputErrConNguoi;
    inputErrTmp[1] = '';
    if (!value.trim()) {
      inputErrTmp[1] = 'Vui lòng nhập ' + (searchPeopleRadio[0].selected ? 'Họ tên' : 'Số giấy chứng nhận');
    } else if (value.length < 7) {
      inputErrTmp[1] = (searchPeopleRadio[0].selected ? 'Họ tên' : 'Số giấy chứng nhận') + ' phải ít nhất 7 ký tự';
    }
    setInputErrConNguoi([...inputErrTmp]);
    setInputHoTenOrGCN(value);
  };
  const onChangeCMTInput = (value) => {
    let inputErrTmp = inputErrConNguoi;
    inputErrTmp[2] = '';
    if (!value.trim()) {
      inputErrTmp[2] = 'Vui lòng nhập CMND / CCCD / Số Giấy chứng nhận / Ngày sinh';
    } else if (value.length < 8) {
      inputErrTmp[2] = 'CMND / CCCD / Ngày sinh phải ít nhất 8 ký tự ';
    } else if (value && (value.includes('/') || value.includes('.'))) {
      if (!moment(value, 'DD/MM/YYYY', true).isValid()) inputErrTmp[2] = 'Vui lòng nhập ngày sinh theo dạng DD/MM/YYYY';
    }

    setInputErrConNguoi([...inputErrTmp]);
    setInputCMT(value);
  };

  const onChangeBienXeOrGcn = (value) => {
    let inputErrTmp = inputErrXe;
    inputErrTmp[0] = '';
    if (!value.trim()) {
      inputErrTmp[0] = 'Vui lòng nhập ' + (searchXeRadio[0].selected ? 'Biển xe' : 'Giấy chứng nhận');
    } else if (value.length < 5) {
      inputErrTmp[0] = (searchXeRadio[0].selected ? 'Biển xe' : 'Số giấy chứng nhận') + ' phải ít nhất 5 ký tự';
    }
    setInputErrXe([...inputErrTmp]);
    setInputBienXeOrGCN(value);
  };

  const onChangeTenOrSdt = (value) => {
    let inputErrTmp = inputErrXe;
    inputErrTmp[1] = '';
    if (!value.trim()) {
      inputErrTmp[1] = 'Vui lòng nhập Tên chủ xe / Số điện thoại';
    } else if (value.length < 5) {
      inputErrTmp[1] = 'Tên chủ xe / Số điện thoại phải ít nhất 5 ký tự ';
    }

    setInputErrXe([...inputErrTmp]);
    setInputTenOrSDT(value);
  };

  const getTenHienThi = (maChiNhanhSelected, data) => {
    let name = '';
    data.map((e) => {
      if (e.ma_chi_nhanh === maChiNhanhSelected) name = e.ten_chi_nhanh;
    });
    return name;
  };

  //Tìm kiếm GCN con người
  const getDataGCNConNguoi = async (oldData, so_trang = 1, so_dong = 20) => {
    // let inputErrTmp = inputErrConNguoi;
    // let haveErr = false;
    // if (!productSelected.ma) {
    //   inputErrTmp[0] = 'Vui lòng chọn Sản phẩm';
    //   haveErr = true;
    // }
    // if (!inputHoTenOrGCN.trim()) {
    //   inputErrTmp[1] = searchPeopleRadio[0].selected ? 'Vui lòng nhập Họ tên' : 'Vui lòng nhập Giấy chứng nhận';
    //   haveErr = true;
    // }
    // if (!inputCMT.trim()) {
    //   inputErrTmp[2] = 'Vui lòng nhập CMTND / CCCD / Ngày sinh';
    //   haveErr = true;
    // }
    // setInputErrConNguoi([...inputErrTmp]);
    // if (haveErr) return;

    try {
      setIsSubmiting(true);
      let params = {
        ma_chi_nhanh_ql: donViCapDonNg,
        so_gcn: soGCNConNguoi,
        so_hd: soHopDongConNguoi,
        ten_ndbh: tenNguoiDuocBaoHiem,
        so_cmt: inputCMT,
        ngay_sinh: ngaySinh !== '' ? moment(ngaySinh).format('YYYYMMDD') : '',
        sdt: dienThoai,
        trang: so_trang,
        so_dong: so_dong,
      };
      let response = await ESmartClaimEndpoint.execute(AxiosConfig.ACTION_CODE.TIM_KIEM_GCN_CON_NGUOI, params);
      console.log('response', response);
      setIsSubmiting(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') return;
      setTotal(response.out_value.tong_so_dong);
      let listData = [...oldData, ...response.data_info];
      if (response.data_info.length === 0) {
        // setEmptyDataConNguoi(true);
        Alert.alert('Thông báo', 'Không có dữ liệu mà bạn cần tìm. Vui lòng kiểm tra lại!');
      } else {
        refModalListGCNConNguoi.current.show();
        // setEmptyDataConNguoi(false);
      }
      setListGcnConNguoi(listData);
    } catch (error) {
      setIsSubmiting(false);
      Alert.alert('Thông báo', error.message);
    }
  };

  const handleLoadMore = async () => {
    if (current * 20 <= total && !isSubmiting) {
      setCurrent(current + 1);
      getDataGCNConNguoi(listGcnConNguoi, current + 1);
    }
  };

  const onPressSearchConNguoi = () => {
    setCurrent(1);
    getDataGCNConNguoi([], 1);
  };

  const onSelectDonViXuLy = (listMaChiNhanh) => {
    try {
      setListMaDonViXuLySelected(listMaChiNhanh);
      if (listMaChiNhanh) setDonViCapDonNg(listMaChiNhanh.toString().replaceAll(',', ';'));
    } catch (error) {
      console.log(error.message);
    }
  };

  const onPressClear = () => {
    setNgaySinh('');
    setDonViCapDonNg('');
    setSoHopDongConNguoi('');
    setSoGCNConNguoi('');
    setTenNguoiDuocBaoHiem('');
    setInputCMT('');
    setDienThoai('');
    setListMaDonViXuLySelected([]);
  };

  //Tìm kiếm GCN xe
  const onPressSearchXe2 = async () => {
    let inputErrTmp = inputErrXe;
    let haveErr = false;
    if (!soHopDong.trim() && !soGCN.trim() && !bienSoXe.trim() && !soKhung.trim() && !soMay.trim()) {
      inputErrTmp[6] = 'Vui lòng nhập ít nhất 1 trường thông tin';
      haveErr = true;
    }
    if (soHopDong.trim() && soHopDong.trim().length < 5) {
      inputErrTmp[1] = 'Số hợp đồng lớn hơn 5 ký tự';
      haveErr = true;
    }
    if (soGCN.trim() && soGCN.trim().length < 5) {
      inputErrTmp[2] = 'Số GCN lớn hơn 5 ký tự';
      haveErr = true;
    }
    if (bienSoXe.trim() && bienSoXe.trim().length < 5) {
      inputErrTmp[3] = 'Số GCN lớn hơn 5 ký tự';
      haveErr = true;
    }
    if (soKhung.trim() && soKhung.trim().length < 5) {
      inputErrTmp[4] = 'Số khung lớn hơn 5 ký tự';
      haveErr = true;
    }
    if (soKhung.trim() && soKhung.trim().length < 5) {
      inputErrTmp[5] = 'Số máy lớn hơn 5 ký tự';
      haveErr = true;
    }

    if (haveErr) {
      setInputErrXe([...inputErrTmp]);
      return;
    }

    let params = {
      so_hdong: soHopDong,
      so_gcn: soGCN,
      bien_so_xe: bienSoXe,
      so_khung: soKhung,
      so_may: soMay,
      // ngay_xr: moment(ngayTonThat).format('DD/MM/YYYY'),
      nv: '',
      ma_chi_nhanh: donViCapDon,
      nguon: 'CCCT',
    };
    setLoadingGcnXe(true);
    try {
      let response = await PartnerEndpoint.searchGCN2(AxiosConfig.ACTION_CODE.TIM_KIEM_GCN_XE_DOI_TAC, params);
      setLoadingGcnXe(false);
      if (!response || !response.state_info || response.state_info.status !== 'OK') {
        setListGCNXe([]);
        return;
      }
      if (response.data_info.hd?.length === 0 || !response.data_info.hd) setEmptyDataXe(true);
      else setEmptyDataXe(false);
      setListGCNXe(response.data_info.hd || []);
    } catch (error) {
      setLoadingGcnXe(false);
      setListGCNXe([]);
      Alert.alert('Thông báo', error.message);
    }
  };
  const onChangeInput = (value, index) => {
    let errTitle = '';
    if (index == 1) {
      errTitle = 'Số hợp đồng';
      setSoHopDong(value);
    } else if (index == 2) {
      errTitle = 'Số GCN bảo hiểm';
      setSoGCN(value);
    } else if (index == 3) {
      errTitle = 'Biển số xe';
      setBienSoXe(value);
    } else if (index == 4) {
      errTitle = 'Số khung';
      setSoKhung(value);
    } else if (index == 5) {
      errTitle = 'Số máy';
      setSoMay(value);
    }
    let inputErrTmp = inputErrXe;
    inputErrTmp[index] = '';
    inputErrTmp[6] = '';
    if (!value.trim()) setInputErrXe([...inputErrTmp]);
    else if (value.trim().length < 5) inputErrTmp[index] = errTitle + ' lớn hơn 5 ký tự';
    setInputErrXe([...inputErrTmp]);
  };

  /**RENDER  */
  //rende radiobutton trong phần search
  const renderRadioInput = (radioButtons, onPressRadioButton, type) => {
    return (
      <View style={{marginVertical: 5}}>
        <RadioGroup
          radioButtons={radioButtons}
          onPress={(value) => {
            //nếu là XE
            if (type == 0) {
              setInputErrXe(['', '', '']);
              setInputTenOrSDT('');
              setInputBienXeOrGCN('');
            }
            //nếu là con người
            else {
              setInputErrConNguoi(['', '']);
              setInputCMT('');
              setInputHoTenOrGCN('');
            }
            onPressRadioButton(value);
          }}
          layout={'row'}
        />
      </View>
    );
  };

  const renderFormSearchConNguoi = () => {
    return (
      <View style={styles.searchView}>
        {/* {renderRadioInput(searchPeopleRadio, setSearchPeopleRadio, 1)} */}

        <TextInputOutlined
          isTouchableOpacity
          editable={false}
          isDropdown
          title="Đơn vị cấp đơn"
          value={
            listMaDonViXuLySelected.length === 0
              ? 'Chọn đơn vị cấp đơn'
              : listMaDonViXuLySelected.length === 1
              ? getTenHienThi(listMaDonViXuLySelected[0], chiNhanhBaoHiemDangCay)
              : `Có ${listMaDonViXuLySelected.length} đơn vị được chọn`
          }
          placeholder="Đơn vị cấp đơn"
          // onFocus={closeDropdown}
          onPress={() => refModalDonViXuLy.current.show()}
          inputStyle={{color: colors.BLACK}}
          containerStyle={{marginTop: 0}}
        />
        <TextInputOutlined title="Số hợp đồng" value={soHopDongConNguoi} onChangeText={setSoHopDongConNguoi} placeholder="Số hợp đồng" />
        <TextInputOutlined title="Số GCN bảo hiểm" value={soGCNConNguoi} onChangeText={setSoGCNConNguoi} placeholder="Số GCN" />
        <TextInputOutlined title="Tên người được bảo hiểm" value={tenNguoiDuocBaoHiem} onChangeText={setTenNguoiDuocBaoHiem} placeholder="Tên người được bảo hiểm" />
        <TextInputOutlined title="Số CMT/CCCD" value={inputCMT} onChangeText={setInputCMT} placeholder="CMT/CCCD" />
        <View style={{flex: 1}}>
          <TextInputOutlined
            title="Ngày sinh"
            isTouchableOpacity={true}
            onPress={() => {
              Keyboard.dismiss();
              setToggleNgaySinh(true);
            }}
            value={ngaySinh ? moment(ngaySinh).format('DD/MM/YYYY') : ''}
            editable={false}
            inputStyle={{color: colors.BLACK, backgroundColor: colors.WHITE}}
            isDateTimeField
            placeholder="Chọn ngày sinh"
          />
          {renderDateTimeComp(toggleNgaySinh, setToggleNgaySinh, setNgaySinh, ngaySinh !== '' ? ngaySinh : new Date(), 'date')}
        </View>
        <TextInputOutlined keyboardType="phone-pad" title="Điện thoại" value={dienThoai} onChangeText={setDienThoai} placeholder="Nhập số điện thoại" />
        {/* <TextInputOutlined
          cleared={nameSanPham !== ''}
          isDropdown
          editable={false}
          isTouchableOpacity
          title="Sản phẩm"
          value={nameSanPham}
          placeholder="Chọn sản phẩm"
          containerStyle={{ marginTop: 0 }}
          onPress={() => {
            Keyboard.dismiss();
            refModalChonSanPham.current.show();
          }}
          inputStyle={{ color: colors.BLACK }}
          onPressClear={() => {
            setNameSanPham('')
            setMaSanPham('')
          }}
        /> */}

        <View style={{flexDirection: 'row'}}>
          {/* <View style={{ flex: 1, marginLeft: 10 }}>
            <TextInputOutlined
              title="Ngày xảy ra"
              isTouchableOpacity={true}
              onPress={() => {
                Keyboard.dismiss();
                setToggleNgayVaoVien(true);
              }}
              value={moment(ngayVaoVien).format('DD/MM/YYYY')}
              editable={false}
              inputStyle={{ color: colors.BLACK, backgroundColor: colors.WHITE }}
              // containerStyle={{ flex: 1, marginTop: 0 }}
              isRequired={true}
              isDateTimeField
            />
            {renderDateTimeComp(toggleNgayVaoVien, setToggleNgayVaoVien, setNgayVaoVien, ngayVaoVien, 'date')}
          </View> */}
        </View>

        {/* <CommonOutlinedTextFieldWithIcon
          value={inputHoTenOrGCN}
          onChangeText={(value) => onChangeHoTenOrGCNInput(value)}
          placeholder={searchPeopleRadio[0].selected ? 'Họ tên (*)' : 'Giấy chứng nhận (*)'}
          error={inputErrConNguoi[1]}
        /> */}
        {/* <CommonOutlinedTextFieldWithIcon value={inputCMT} onChangeText={(value) => onChangeCMTInput(value)} placeholder="CMND / CCCD / Ngày sinh (*)" error={inputErrConNguoi[2]} /> */}
        {/* <TouchableOpacity disabled={isSubmiting} style={styles.btnLoginView} activeOpacity={0.5} onPress={onPressSearchConNguoi}>
          <Text style={styles.txtBtnLogin}>Tìm kiếm</Text>
        </TouchableOpacity> */}
        <View style={{flexDirection: 'row'}}>
          <ButtonLinear
            disabled={isSubmiting}
            title="Nhập lại"
            textStyle={[styles.txtBtnLogin, {color: 'black'}]}
            linearStyle={{marginTop: 20, marginRight: 10}}
            linearColors={[colors.GRAY2, colors.GRAY2]}
            onPress={onPressClear}
          />
          <ButtonLinear disabled={isSubmiting} loading={isSubmiting} title="Tìm kiếm" textStyle={styles.txtBtnLogin} linearStyle={{marginTop: 20}} onPress={onPressSearchConNguoi} />
        </View>
      </View>
    );
  };

  const renderFormSearchXe = () => {
    return (
      <View style={styles.searchView}>
        {/* {renderRadioInput(searchXeRadio, setSearchXeRadio, 0)}
          <CommonOutlinedTextFieldWithIcon
            value={inputBienXeOrGCN}
            onChangeText={(value) => onChangeBienXeOrGcn(value)}
            placeholder={searchXeRadio[0].selected ? 'Nhập biển số xe (*)' : 'Nhập số giấy chứng nhận (*)'}
            error={inputErrXe[0]}
          />
          <CommonOutlinedTextFieldWithIcon value={inputTenOrSDT} onChangeText={(value) => onChangeTenOrSdt(value)} placeholder="Tên chủ xe / Số điên thoại (*)" error={inputErrXe[1]} /> */}

        {/* <View style={{flex: 1}}>
          <TextInputOutlined
            title="Ngày xảy ra tổn thất"
            isTouchableOpacity={true}
            onPress={() => {
              Keyboard.dismiss();
              setToggleNgayTonThat(true);
            }}
            value={moment(ngayTonThat).format('DD/MM/YYYY')}
            editable={false}
            inputStyle={{color: colors.BLACK, backgroundColor: colors.WHITE}}
            containerStyle={{flex: 1, marginTop: 0}}
            isRequired={true}
            isDateTimeField
          />
          {renderDateTimeComp(toggleNgayTonThat, setToggleNgayTonThat, setNgayTonThat, ngayTonThat, 'date')}
        </View> */}

        <TextInputOutlined
          cleared={donViCapDon !== ''}
          isDropdown
          editable={false}
          isTouchableOpacity
          title="Đơn vị cấp đơn"
          value={getTenHienThi(donViCapDon, chiNhanhBaoHiemXe)}
          // onChangeText={(value) => onChangeInput(value, 1)}
          placeholder="Chọn đơn vị cấp đơn"
          containerStyle={{marginTop: 0}}
          onPress={() => {
            Keyboard.dismiss();
            refModalChiNhanh.current.show();
          }}
          inputStyle={{color: colors.BLACK}}
          onPressClear={() => setDonViCapDon('')}
        />

        <TextInputOutlined title="Số hợp đồng" value={soHopDong} error={inputErrXe[1]} onChangeText={(value) => onChangeInput(value, 1)} placeholder="Số hợp đồng" containerStyle={{marginTop: 0}} />
        <TextInputOutlined title="Số GCN bảo hiểm" value={soGCN} error={inputErrXe[2]} onChangeText={(value) => onChangeInput(value, 2)} placeholder="Số GCN" containerStyle={{marginTop: 0}} />
        <TextInputOutlined title="Biển số xe" value={bienSoXe} error={inputErrXe[3]} onChangeText={(value) => onChangeInput(value, 3)} placeholder="Biển số xe" containerStyle={{marginTop: 0}} />
        <View style={{flexDirection: 'row'}}>
          <View style={{flex: 1}}>
            <TextInputOutlined
              title="Số khung"
              value={soKhung}
              error={inputErrXe[4]}
              onChangeText={(value) => onChangeInput(value, 4)}
              placeholder="Số khung"
              containerStyle={{marginRight: 10, marginTop: 0}}
            />
          </View>
          <View style={{flex: 1}}>
            <TextInputOutlined title="Số máy" value={soMay} error={inputErrXe[5]} onChangeText={(value) => onChangeInput(value, 5)} placeholder="Số máy" containerStyle={{marginTop: 0}} />
          </View>
        </View>
        {inputErrXe[6] != '' && <Text children={inputErrXe[6]} style={{color: colors.RED1, marginBottom: 10}} />}
        {/* <TouchableOpacity style={styles.btnLoginView} activeOpacity={0.5} onPress={onPressSearchXe2}>
          <Text font="regular16" style={styles.txtBtnLogin}>
            Tìm kiếm
          </Text>
        </TouchableOpacity> */}
        <ButtonLinear title="Tìm kiếm" onPress={onPressSearchXe2} linearStyle={{marginTop: 20}} />
      </View>
    );
  };
  const renderDateTimeComp = (toggleDateTime, setToggleDateTime, setDateTime, date, mode, minDate, maxDate = new Date(), type) => (
    <DateTimePickerComponent
      isVisible={toggleDateTime}
      mode={mode}
      onConfirm={(dateSelected) => {
        setToggleDateTime(false);
        setDateTime(dateSelected);
      }}
      onCancel={() => setToggleDateTime(false)}
      date={date}
      maximumDate={maxDate}
      minimumDate={minDate}
    />
  );

  const renderGCNXeItem = (data) => {
    let itemData = data.item;
    return (
      <TouchableOpacity
        onPress={() => {
          NavigationUtil.push(SCREEN_ROUTER_APP.GIAY_CHUNG_NHAN, {
            giayChungNhan: itemData,
          });
        }}
        style={styles.declaredProfileItemView}>
        <Icon.FontAwesome name="circle" size={15} color={colors.BLUE3} />
        <View style={{flexDirection: 'column', flex: 1, marginLeft: scale(spacing.smaller)}}>
          <Text style={styles.profileNumber} font="regular16">
            Số hợp đồng : {itemData.so_hdong || ''}
          </Text>
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={styles.txtBienKiemSoat}>
              Tên chủ xe:
              <Text style={styles.txtBienKiemSoatDetail}>{' ' + (itemData.ten_chu_xe || '')}</Text>
            </Text>
            <Text style={styles.txtBienKiemSoat}>
              Biển xe:
              <Text style={styles.txtBienKiemSoatDetail}>{' ' + (itemData.bien_so_xe || '')}</Text>
            </Text>
          </View>

          <Text style={styles.txtBienKiemSoat}>
            Tên gói bảo hiểm:
            <Text style={styles.txtBienKiemSoatDetail}>{' ' + (itemData.ctrinh_bh_ten || '...')}</Text>
          </Text>

          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={styles.txtBienKiemSoat}>
              Số GCN:
              <Text style={styles.txtBienKiemSoatDetail}>{' ' + (itemData.so_gcn || '')}</Text>
            </Text>
            <Text style={styles.txtBienKiemSoat}>
              Nghiệp vụ:
              <Text style={styles.txtBienKiemSoatDetail}>{' ' + (itemData.ten_loai_gcn || '')}</Text>
            </Text>
          </View>
          <Text style={styles.txtBienKiemSoat}>
            Đơn vị cấp đơn:
            <Text style={styles.txtBienKiemSoatDetail}>{' ' + (itemData.ten_chi_nhanh || '')}</Text>
          </Text>
          <Text style={styles.txtBienKiemSoat}>
            Hiệu lực bảo hiểm:
            <Text style={styles.txtBienKiemSoatDetail}>{' ' + (itemData.ngay_hl_bh || '') + ' - '}</Text>
            <Text style={styles.txtBienKiemSoatDetail}>{itemData.ngay_kt_bh || ''}</Text>
          </Text>
        </View>
        <View style={{alignSelf: 'center'}}>
          <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{opacity: 0.6, alignSelf: 'center'}} color={colors.BLUE1} />
        </View>
      </TouchableOpacity>
    );
  };

  const renderGCNXe = () => {
    if (loadingGcnXe) return <ActivityIndicator size="large" color={colors.PRIMARY} />;
    if (emptyDataXe)
      return (
        <View style={styles.txtEmptyData}>
          <Text>Không tìm thấy dữ liệu xe trên hệ thống, Quý khách vui lòng thử lại.</Text>
        </View>
      );
    return <FlatList data={listGcnXe} keyExtractor={(item) => item.id} renderItem={renderGCNXeItem} />;
  };

  // const renderGCNConNguoiItem = (data) => {
  //   let itemData = data.item;

  //   const renderLabel = (title, value, textStyle) => {
  //     return <View>
  //       <Text style={styles.txtBienKiemSoat} font="regular14">
  //         {title}:
  //         <Text style={styles.txtBienKiemSoatDetail}>{' ' + value}</Text>
  //       </Text>
  //     </View>
  //   }
  //   return (
  //     <TouchableOpacity
  //       onPress={() => {
  //         // NavigationUtil.navigate(SCREEN_ROUTER_APP.PEOPLE_CERTIFICATE, {
  //         //   certificate: itemData,
  //         //   listCertificate: certificates,
  //         // });
  //       }}>
  //       <View style={styles.declaredProfileItemView}>
  //         <Icon.FontAwesome name="circle" size={10} color={colors.BLUE3} style={{ marginTop: 8 }} />
  //         <View style={{ flexDirection: 'column', flex: 1, marginLeft: scale(spacing.smaller) }}>
  //           {renderLabel('Số hợp đồng', itemData.so_hd)}
  //           {renderLabel('Số GCN', itemData.gcn)}
  //           {renderLabel('ĐV cấp đơn', itemData.ten_chi_nhanh)}
  //           {renderLabel('Tên NĐBH', itemData.ten_ndbh)}
  //           {renderLabel('Số CCCD', itemData.so_cmt)}
  //           {renderLabel('Hiệu lực', itemData.hieu_luc)}
  //         </View>
  //         <View style={{ alignSelf: 'center' }}>
  //           <Icon.MaterialIcons name="keyboard-arrow-right" size={25} style={{ opacity: 0.6, alignSelf: 'center' }} color={colors.BLUE1} />
  //         </View>
  //       </View>
  //     </TouchableOpacity>
  //   );
  // };

  // const renderGCNConNguoi = () => {
  //   if (emptyDataConNguoi)
  //     return (
  //       <View style={styles.txtEmptyData}>
  //         <Text>Không tìm thấy dữ liệu con người trên hệ thống, Quý khách vui lòng thử lại.</Text>
  //       </View>
  //     );
  //   return <FlatList data={listGcnConNguoi} keyExtractor={(_, index) => index.toString()} renderItem={renderGCNConNguoiItem} />;
  // };

  return (
    <ScreenComponent
      headerBack
      // onPressBack={() => NavigationUtil.reset(1, SCREEN_ROUTER_APP.HOME)}
      headerTitle="Tra cứu giấy chứng nhận"
      renderView={
        <View style={styles.container}>
          <CustomTabBar activeTab={btnTabActive} goToPage={setBtnTabActive} tabs={['Xe', 'Sức khoẻ']} />
          <KeyboardAwareScrollView scrollEnabled={true} showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
            {btnTabActive === 0 && (
              <>
                {renderFormSearchXe()}
                {renderGCNXe()}
              </>
            )}
            {btnTabActive === 1 && (
              <>
                {renderFormSearchConNguoi()}
                {/* {renderGCNConNguoi()} */}
              </>
            )}
          </KeyboardAwareScrollView>
          <ModalChiNhanhTheoDangPhang
            baseData={chiNhanhBaoHiemXe}
            value={donViCapDon}
            ref={refModalChiNhanh}
            setValue={(val) => setDonViCapDon(val.ma_chi_nhanh)}
            onBackPress={() => refModalChiNhanh.current?.hide()}
          />
          <ModalChiNhanhTheoDangCay ref={refModalDonViXuLy} showCheckCha={true} multiple setListMaDonViXuLySelected={(value) => onSelectDonViXuLy(value)} />
          <ModalListGCNConNguoi
            ref={refModalListGCNConNguoi}
            baseData={listGcnConNguoi}
            onBackPress={() => refModalListGCNConNguoi.current.hide()}
            onLoadMore={handleLoadMore}
            isLoadMore={isSubmiting}
          />
        </View>
      }
    />
  );
};

export const SearchScreen = memo(SearchScreenComponent, isEqual);
