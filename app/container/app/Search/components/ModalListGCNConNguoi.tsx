import {colors} from '@app/commons/Theme';
import {dimensions, scale, spacing, vScale} from '@app/theme';
import {DialogLoading, Empty, HeaderModal, Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useRef, useState} from 'react';
import isEqual from 'react-fast-compare';
import {Alert, FlatList, SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import {DotIndicator} from 'react-native-indicators';
import Modal from 'react-native-modal';
import {ModalShowQlBaoHiem} from './ModalShowQlBaoHiem';
import {ModalShowLsBoiThuong} from './ModalShowLsBoiThuong';
import {ESmartClaimEndpoint} from '@app/services/endPoints';
import axiosConfig from '@app/services/axiosConfig';
import NavigationUtil from '@app/navigation/NavigationUtil';
import {SCREEN_ROUTER_APP} from '@app/commons/Constant';

interface Props {
  onBackPress?: () => void;
  baseData?: any[];
  setValue?: (item: any) => void;
  value?: any;
  isLoadMore?: boolean;
}
let timer;

const ModalListGCNConNguoiComponent = forwardRef<any, Props>((props, ref) => {
  const [isVisible, setIsVisible] = useState(false);
  const {onBackPress, baseData = [], isLoadMore} = props;
  const refContainer = useRef();
  let refModalShowQlBaoHiem = useRef(null);
  let refModalShowLsBoiThuong = useRef(null);

  const [loading, setLoading] = useState(false);
  const [dataQlBaoHiem, setDataQlBaoHiem] = useState([]);
  const [dataLsBoiThuong, setDataLsBoiThuong] = useState([]);
  const [tongTienNoiTru, setTongTienNoiTru] = useState(0);
  const [tongTienNgoaiTru, setTongTienNgoaiTru] = useState(0);

  const [dialogLoading, setDialogLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
  }));

  const xemQlBaoHiem = async (i) => {
    setLoading(true);
    refModalShowQlBaoHiem.current?.show();
    timer = setTimeout(async () => {
      const params = {
        so_id_hd: i.so_id_hd,
        ma_doi_tac_ql: i.ma_doi_tac_ql,
        so_id_dt: i.so_id_dt,
      };

      try {
        let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_TT_QL_GOC, params);
        setLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        setDataQlBaoHiem(response.data_info);
      } catch (error) {
        setLoading(false);
        Alert.alert('Thông báo', error.message);
      }
      return;
    }, 500);
  };

  const xemLsBoiThuong = async (i) => {
    setLoading(true);
    refModalShowLsBoiThuong.current.show();
    timer = setTimeout(async () => {
      const params = {
        so_id_hd: i.so_id_hd,
        so_id_dt: i.so_id_dt,
        ma_doi_tac: i.ma_doi_tac,
        so_id: '',
      };
      try {
        let response = await ESmartClaimEndpoint.execute(axiosConfig.ACTION_CODE.LAY_LS_BOI_THUONG, params);
        setLoading(false);
        if (!response || !response.state_info || response.state_info.status !== 'OK') return;
        let tienNoiTru = 0;
        let tienNgoaiTru = 0;
        let dataNoitru = response.data_info.ho_so.filter((item) => item.hinh_thuc_ten === 'Nội trú');
        dataNoitru.filter((item) => {
          let sum = +item.so_tien_duyet;
          tienNoiTru += sum;
        });
        let dataNgoaitru = response.data_info.ho_so.filter((item) => item.hinh_thuc_ten === 'Ngoại trú');
        dataNgoaitru.filter((item) => {
          let sum = +item.so_tien_duyet;
          tienNgoaiTru += sum;
        });
        setTongTienNoiTru(tienNoiTru);
        setTongTienNgoaiTru(tienNgoaiTru);
        setDataLsBoiThuong(response.data_info.ho_so);
      } catch (error) {
        setLoading(false);
        Alert.alert('Thông báo', error.message);
      }

      return;
    }, 500);
  };

  const renderLabel = (title: string, value: string) => (
    <Text style={styles.txtBienKiemSoat} font="regular14">
      <Text style={styles.subLabel}>{title}: </Text>
      <Text style={styles.content}>{value}</Text>{' '}
    </Text>
  );

  const renderGCNConNguoiItem = ({item}: {item: any}) => (
    <TouchableOpacity
      onPress={() => {
        setIsVisible(false);
        NavigationUtil.push(SCREEN_ROUTER_APP.GIAY_CHUNG_NHAN_CON_NGUOI, {giayChungNhan: item});
      }}>
      <View style={styles.profileItemView}>
        <View style={styles.itemContent}>
          {renderLabel('Số hợp đồng', item.so_hd)}
          {renderLabel('Số GCN', item.gcn)}
          {renderLabel('ĐV cấp đơn', item.ten_chi_nhanh)}
          {renderLabel('Tên NĐBH', item.ten_ndbh)}
          {renderLabel('Số CMT/CCCD', item.so_cmt)}
          {renderLabel('Hiệu lực', item.hieu_luc)}
          {renderLabel('Tên gói bảo hiểm', item.ten_goi_bh)}
        </View>
        <View style={styles.divider} />
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <TouchableOpacity style={styles.bottomBtn} onPress={() => xemQlBaoHiem(item)}>
            <Icon.FontAwesome name="list-alt" size={15} color={colors.PRIMARY} />
            <Text style={[styles.content, {fontWeight: '600', marginLeft: 8}]}>Quyền lợi bảo hiểm</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.bottomBtn} onPress={() => xemLsBoiThuong(item)}>
            <Icon.FontAwesome name="history" size={15} color={colors.PRIMARY} />
            <Text style={[styles.content, {fontWeight: '600', marginLeft: 8}]}>Lịch sử bồi thường</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderFooter = () => {
    return (
      <View marginTop={12}>
        {isLoadMore ? <DotIndicator color={colors.PRIMARY} count={3} size={8} /> : <Text style={{padding: 16, color: colors.PRIMARY, textAlign: 'center'}}>Không có thêm dữ liệu</Text>}
      </View>
    );
  };

  return (
    <Modal animationIn="fadeIn" animationOut="fadeOut" isVisible={isVisible} style={styles.modal}>
      <SafeAreaView style={styles.container}>
        <HeaderModal title="Tra cứu GCN" onBackPress={onBackPress} />
        <FlatList
          data={baseData}
          ref={refContainer}
          scrollEnabled
          style={styles.flatList}
          onEndReachedThreshold={0.3}
          onEndReached={props.onLoadMore}
          renderItem={renderGCNConNguoiItem}
          keyExtractor={(_, index) => index.toString()}
          ListEmptyComponent={<Empty imageStyle={styles.emptyImage} description="Không có kết quả phù hợp" />}
          ListFooterComponent={renderFooter}
        />
        <ModalShowQlBaoHiem refreshing={loading} ref={refModalShowQlBaoHiem} data={dataQlBaoHiem} onBackPress={() => refModalShowQlBaoHiem.current.hide()} />
        <ModalShowLsBoiThuong
          refreshing={loading}
          data={dataLsBoiThuong}
          ref={refModalShowLsBoiThuong}
          tongTienNoiTru={tongTienNoiTru}
          tongTienNgoaiTru={tongTienNgoaiTru}
          onBackPress={() => refModalShowLsBoiThuong.current.hide()}
        />
      </SafeAreaView>
      {/* <DialogLoading /> */}
    </Modal>
  );
});

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },
  container: {
    flex: 1,
  },
  flatList: {
    flex: 1,
  },
  circleIcon: {
    marginTop: 8,
  },
  arrowIcon: {
    opacity: 0.6,
    alignSelf: 'center',
  },
  itemContent: {
    flexDirection: 'column',
    flex: 1,
    marginLeft: scale(spacing.smaller),
  },
  declaredProfileItemView: {
    padding: 8,
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: 5,
    marginHorizontal: 10,
    borderColor: colors.GRAY,
  },
  emptyImage: {
    width: dimensions.width / 4,
    height: dimensions.width / 4,
  },
  txtBienKiemSoatDetail: {
    color: colors.BLUE5,
    fontWeight: '600',
  },
  txtBienKiemSoat: {
    marginVertical: 2,
    flexDirection: 'row',
  },
  subLabel: {
    color: colors.GRAY6,
  },
  content: {
    color: colors.PRIMARY,
  },
  divider: {
    height: 0.5,
    marginVertical: 10,
    backgroundColor: colors.GRAY,
  },
  bottomBtn: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileItemView: {
    backgroundColor: colors.WHITE1,
    padding: 8,
    borderWidth: 1,
    borderRadius: 10,
    marginVertical: 5,
    marginHorizontal: 10,
    borderColor: colors.GRAY,
  },
});

export const ModalListGCNConNguoi = memo(ModalListGCNConNguoiComponent, isEqual);
