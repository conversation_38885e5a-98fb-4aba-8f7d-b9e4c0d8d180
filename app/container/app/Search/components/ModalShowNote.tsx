import {colors} from '@app/commons/Theme';
import {dimensions} from '@app/theme';
import {Icon, Text} from '@component';
import React, {forwardRef, memo, useImperativeHandle, useState} from 'react';
import isEqual from 'react-fast-compare';
import {ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
import Modal from 'react-native-modal';

const ModalShowNoteComponent = forwardRef((props, ref) => {
  const {detail, onBackPress, title} = props;
  const [isVisible, setIsVisible] = useState(false);

  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );
  /* RENDER */
  const renderHeader = () => {
    return (
      <View style={styles.headerView}>
        <View style={styles.modalTitleView}>
          <Text style={styles.modalTitle} children={title ? title : '<PERSON>hi chú nội bộ'} />
          <TouchableOpacity style={styles.closeView} onPress={onBackPress}>
            <Icon.Ionicons name="close" size={22} color={'gray'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderContent = () => {
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={{color: colors.BLACK_03}}>{detail}</Text>
      </ScrollView>
    );
  };
  return (
    <Modal swipeDirection={['down']} style={styles.modal} isVisible={isVisible} propagateSwipe={true} onSwipeComplete={onBackPress} onBackdropPress={onBackPress} onBackButtonPress={onBackPress}>
      <View style={styles.modalView}>
        {renderHeader()}
        {renderContent()}
      </View>
    </Modal>
  );
});
const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalView: {
    width: dimensions.width,
    height: dimensions.height / 2,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: colors.WHITE,
  },
  modalCameraContent: {
    flex: 1,
    backgroundColor: colors.WHITE,
    // borderWidth: 1,
  },
  searchInput: {
    borderWidth: 1,
    paddingLeft: 16,
    borderColor: colors.GRAY,
    // flex: 1,
    height: 40,
    margin: 16,
    borderRadius: 25,
  },

  backBtn: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBack: {
    paddingHorizontal: 15,
  },
  itemHangMucView: {
    padding: 5,
    flexDirection: 'row',
  },
  modalTitleView: {
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderColor: colors.GRAY,
    backgroundColor: colors.WHITE5,
  },
  modalTitle: {
    flex: 1,
    fontSize: 16,
    marginLeft: 30,
    fontWeight: 'bold',
    marginVertical: 15,
    textAlign: 'center',
    color: colors.BLACK_03,
  },
  closeView: {
    marginRight: 15,
    borderRadius: 25,
    paddingVertical: 1,
    paddingHorizontal: 2,
    backgroundColor: colors.GRAY2,
  },
  checkbox: {
    marginRight: 5,
  },
  content: {
    margin: 10,
    paddingBottom: 20,
  },
});

export const ModalShowNote = memo(ModalShowNoteComponent, isEqual);
