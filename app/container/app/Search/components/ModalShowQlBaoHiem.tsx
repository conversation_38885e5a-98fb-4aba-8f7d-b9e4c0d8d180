import {colors} from '@app/commons/Theme';
import {HeaderModal, Text} from '@component';
import {FontSize, spacing} from '@app/theme';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {FlatList, Platform, RefreshControl, SafeAreaView, StyleSheet, View} from 'react-native';
import Modal from 'react-native-modal';
import {NumericFormat} from 'react-number-format';

const ModalShowQlBaoHiemComponent = forwardRef((props, ref) => {
  useImperativeHandle(
    ref,
    () => ({
      show: () => setIsVisible(true),
      hide: () => setIsVisible(false),
    }),
    [],
  );

  const {onBackPress, soId, data, refreshing} = props;
  const [isVisible, setIsVisible] = useState(false);

  const onPressBack = () => {
    onBackPress && onBackPress();
  };
  /* RENDER */
  const renderItem = ({item, index}) => {
    return (
      <View key={index} style={styles.itemView}>
        <View flexDirection="row" justifyContent="space-between" style={{flex: 1}}>
          <View style={{flex: 1}}>
            <Text style={item.lh_nv_ct === null || '' ? styles.txtParent : styles.txtChildren}>{item.ten_hien_thi}</Text>
          </View>
          <Text style={styles.txtParent}>
            Nguyên tệ: <Text style={styles.currency}>USD</Text>
          </Text>
        </View>
        <Text style={styles.subTitle}>Quyền lợi bảo hiểm gốc</Text>
        <View style={styles.contentRow}>
          <Text style={styles.itemLabel}>- Số lần(ngày)/năm: </Text>
          <Text style={styles.txtItemDetail} children={item.so_lan_ngay} />
        </View>
        <View style={styles.contentRow}>
          <Text style={styles.itemLabel}>- Tỷ lệ đồng: </Text>
          <Text style={styles.txtItemDetail} children={item.dong_bh + '%'} />
        </View>
        <View style={styles.contentRow}>
          <Text style={styles.itemLabel}>- Số tiền giới hạn(ngày)/năm: </Text>
          <NumericFormat value={item.tien_lan_ngay} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value + ' đ'} style={styles.txtItemDetail} />} />
        </View>
        <View style={styles.contentRow}>
          <Text style={styles.itemLabel}>- Quyền lợi/năm: </Text>
          <NumericFormat value={item.tien_nam} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value + ' đ'} style={styles.txtItemDetail} />} />
        </View>
        <View style={styles.contentRow}>
          <Text style={styles.itemLabel}>- Số ngày chờ: </Text>
          <Text style={styles.txtItemDetail} children={item.so_ngay_cho} />
        </View>
        <View flexDirection="row" justifyContent="space-between">
          <View>
            <Text style={styles.subTitle}>Quyền lợi đã sử dụng</Text>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Số lần(ngày): </Text>
              <Text style={styles.txtItemDetail} children={item.so_lan_ngay_duyet} />
            </View>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Ql/năm: </Text>
              <NumericFormat value={item.tien_nam_duyet} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value + ' đ'} style={styles.txtItemDetail} />} />
            </View>
          </View>
          <View>
            <Text style={styles.subTitle}>Quyền lợi còn lại</Text>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Số lần(ngày): </Text>
              <Text style={styles.txtItemDetail} children={item.so_lan_ngay_con} />
            </View>
            <View style={styles.contentRow}>
              <Text style={styles.itemLabel}>- Ql/năm: </Text>
              <NumericFormat value={item.tien_nam_con} displayType={'text'} thousandSeparator={true} renderText={(value) => <Text children={value + ' đ'} style={styles.txtItemDetail} />} />
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderContent = () => {
    return (
      <FlatList
        data={data.qloi}
        extraData={data.qloi}
        renderItem={renderItem}
        onEndReachedThreshold={0.3}
        style={styles.flStyles}
        keyExtractor={(item, index) => item + index.toString()}
        showsVerticalScrollIndicator={Platform.OS === 'android' ? true : false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={() => {}} />}
        ListEmptyComponent={<Text style={styles.txtEmpty}>Chưa có dữ liệu</Text>}
      />
    );
  };
  return (
    <Modal style={styles.modal} isVisible={isVisible} animationIn="fadeInRight" animationOut="fadeOutRight" onBackButtonPress={onBackPress}>
      <SafeAreaView style={styles.container}>
        <HeaderModal onBackPress={onPressBack} title="Quyền lợi bảo hiểm" />
        {renderContent()}
      </SafeAreaView>
    </Modal>
  );
});
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.WHITE,
  },
  modal: {
    flex: 1,
    margin: 0,
    paddingVertical: 10,
    justifyContent: 'flex-end',
    backgroundColor: colors.WHITE,
  },

  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 10,
  },
  backBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBack: {
    // paddingRight: 16,
  },
  itemHangMucView: {
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  txtView: {
    flex: 1,
    marginTop: Platform.OS == 'android' ? 4 : 2,
    flexDirection: 'row',
  },
  title: {
    fontSize: 18,
    marginLeft: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  txtParent: {
    color: colors.RED1,
    fontWeight: '500',
    fontSize: FontSize.size14,
  },
  txtChildren: {
    flex: 1,
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  itemView: {
    paddingVertical: 4,
    borderBottomWidth: 0.5,
    borderColor: colors.GRAY,
  },
  txtItemDetail: {
    fontWeight: '500',
    color: colors.PRIMARY,
    fontSize: FontSize.size14,
  },
  itemLabel: {
    fontWeight: '500',
    color: colors.GRAY6,
    fontSize: FontSize.size13,
  },
  subTitle: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.BLACK,
    marginTop: spacing.tiny,
  },
  contentRow: {
    flexDirection: 'row',
    marginTop: spacing.tiny,
  },
  flStyles: {
    paddingHorizontal: 10,
  },
  currency: {
    fontWeight: '500',
    color: colors.PRIMARY,
  },
});

export const ModalShowQlBaoHiem = ModalShowQlBaoHiemComponent;
